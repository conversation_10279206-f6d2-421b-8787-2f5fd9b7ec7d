const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const fileModel = require('../../../models/v1/mapping/fileModel');
const { getUserInfoFromRequest } = require('../../../utils/others');


/**
 * 上传文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadFile = async (req, res) => {
  try {
    if (!req.file && !req.files) {
      return res.status(400).json({
        code: 400,
        message: '未检测到上传的文件'
      });
    }
    
    // 验证必要的参数
    if (!req.body.id) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：id'
      });
    }

    // 获取存储路径名称，优先使用class，如果没有则尝试使用pathName，否则使用默认general
    const storagePath = req.body.class || 'general';

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    
    if (!userInfo) {
      return res.status(401).json({
        code: 401,
        message: '未授权，请先登录'
      });
    }

    // 处理单个文件
    if (req.file) {
      const file = req.file;
      
      // 检查文件是否被存储在错误的目录中
      const currentPath = file.path;
      const expectedDir = `uploads/${storagePath}/`;
      
      // 如果路径不包含期望的目录名（例如：uploads/research_project/）
      if (!currentPath.includes(`/${storagePath}/`)) {
        // 确保目标目录存在
        if (!fs.existsSync(expectedDir)) {
          fs.mkdirSync(expectedDir, { recursive: true });
          console.log(`创建目录: ${expectedDir}`);
        }
        
        // 构造新文件路径
        const newFilename = file.filename;
        const newPath = path.join(expectedDir, newFilename);
        
        // 移动文件
        try {
          fs.renameSync(currentPath, newPath);
          console.log(`文件已移动: ${currentPath} -> ${newPath}`);
          // 更新file对象的路径
          file.path = newPath;
        } catch (moveError) {
          console.error('移动文件失败:', moveError);
          // 如果移动失败，尝试复制文件
          try {
            fs.copyFileSync(currentPath, newPath);
            fs.unlinkSync(currentPath);
            file.path = newPath;
            console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
          } catch (copyError) {
            console.error('复制文件失败:', copyError);
            // 如果复制也失败了，继续使用原始路径
          }
        }
      }
      
      // 构建文件对象
      const fileObj = {
        projectId: req.body.id, // 使用传入的项目ID
        fileName: file.filename,
        originalName: Buffer.from(file.originalname, 'binary').toString('utf8'),
        filePath: file.path,
        fileSize: file.size,
        mimeType: file.mimetype,
        extension: path.extname(file.originalname).toLowerCase(),
        uploaderId: userInfo.id,
        relatedId: req.body.relatedId || null,
        relatedType: req.body.class || null, // 使用class参数代替relatedType
        storageLocation: 'local',
        description: req.body.description || null
      };
      
      // 保存到数据库
      const savedFile = await fileModel.create(fileObj);
      
      return res.status(201).json({
        code: 200,
        message: '文件上传成功',
        data: [{
          id: savedFile.id,
          fileName: savedFile.fileName,
          originalName: savedFile.originalName,
          filePath: savedFile.filePath,
          fileSize: savedFile.fileSize,
          mimeType: savedFile.mimeType,
          extension: savedFile.extension,
          url: `/uploads/${storagePath}/${savedFile.fileName}`
        }]
      });
    }
    
    // 处理多个文件
    if (req.files && req.files.length > 0) {
      const filesData = [];
      const expectedDir = `uploads/${storagePath}/`;
      
      // 确保目标目录存在
      if (!fs.existsSync(expectedDir)) {
        fs.mkdirSync(expectedDir, { recursive: true });
        console.log(`创建目录: ${expectedDir}`);
      }
      
      for (const file of req.files) {
        // 检查文件是否被存储在错误的目录中
        const currentPath = file.path;
        
        // 如果路径不包含期望的目录名（例如：uploads/research_project/）
        if (!currentPath.includes(`/${storagePath}/`)) {
          // 构造新文件路径
          const newFilename = file.filename;
          const newPath = path.join(expectedDir, newFilename);
          
          // 移动文件
          try {
            fs.renameSync(currentPath, newPath);
            console.log(`文件已移动: ${currentPath} -> ${newPath}`);
            // 更新file对象的路径
            file.path = newPath;
          } catch (moveError) {
            console.error('移动文件失败:', moveError);
            // 如果移动失败，尝试复制文件
            try {
              fs.copyFileSync(currentPath, newPath);
              fs.unlinkSync(currentPath);
              file.path = newPath;
              console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
            } catch (copyError) {
              console.error('复制文件失败:', copyError);
              // 如果复制也失败了，继续使用原始路径
            }
          }
        }
        
        // 构建文件对象
        const fileObj = {
          projectId: req.body.id, // 使用传入的项目ID
          fileName: file.filename,
          originalName: Buffer.from(file.originalname, 'binary').toString('utf8'),
          filePath: file.path,
          fileSize: file.size,
          mimeType: file.mimetype,
          extension: path.extname(file.originalname).toLowerCase(),
          uploaderId: userInfo.id,
          relatedId: req.body.relatedId || null,
          relatedType: req.body.class || null, // 使用class参数代替relatedType
          storageLocation: 'local',
          description: req.body.description || null
        };
        
        // 保存到数据库
        const savedFile = await fileModel.create(fileObj);
        
        filesData.push({
          id: savedFile.id,
          fileName: savedFile.fileName,
          originalName: savedFile.originalName,
          filePath: savedFile.filePath,
          fileSize: savedFile.fileSize,
          mimeType: savedFile.mimeType,
          extension: savedFile.extension,
          url: `/uploads/${storagePath}/${savedFile.fileName}`
        });
      }
      
      return res.status(201).json({
        code: 200,
        message: '文件上传成功',
        data: filesData
      });
    }
    
    return res.status(400).json({
      code: 400,
      message: '未发现有效文件'
    });
  } catch (error) {
    console.error('文件上传失败:', error);
    
    // 清理临时文件
    try {
      if (req.file) {
        fs.unlinkSync(req.file.path);
      }
      if (req.files && req.files.length > 0) {
        for (const file of req.files) {
          fs.unlinkSync(file.path);
        }
      }
    } catch (cleanupError) {
      console.error('清理临时文件失败:', cleanupError);
    }
    
    return res.status(500).json({
      code: 500,
      message: '文件上传失败: ' + error.message
    });
  }
};

/**
 * 为科研项目上传附件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadProjectAttachments = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '未检测到上传的文件'
      });
    }

    // 验证项目ID
    if (!req.body.projectId) {
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID'
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    if (!userInfo) {
      return res.status(401).json({
        code: 401,
        message: '未授权，请先登录'
      });
    }

    const filesData = [];
    for (const file of req.files) {
      // 构建文件对象
      const fileObj = {
        projectId: req.body.projectId, // 使用项目ID
        fileName: file.filename,
        originalName: Buffer.from(file.originalname, 'binary').toString('utf8'),
        filePath: file.path,
        fileSize: file.size,
        mimeType: file.mimetype,
        extension: path.extname(file.originalname).toLowerCase(),
        uploaderId: userInfo.id,
        relatedId: req.body.projectId,
        relatedType: req.body.class || 'research_project', // 使用class参数代替relatedType
        storageLocation: 'local',
        description: req.body.description || null
      };
      
      // 保存到数据库
      const savedFile = await fileModel.create(fileObj);
      
      filesData.push({
        id: savedFile.id,
        fileName: savedFile.fileName,
        originalName: savedFile.originalName,
        filePath: savedFile.filePath,
        fileSize: savedFile.fileSize,
        mimeType: savedFile.mimeType,
        extension: savedFile.extension,
        url: `/uploads/research_projects/${savedFile.fileName}`
      });
    }
    
    return res.status(201).json({
      code: 200,
      message: '项目附件上传成功',
      data: filesData
    });
  } catch (error) {
    console.error('项目附件上传失败:', error);
    
    // 清理临时文件
    try {
      if (req.files && req.files.length > 0) {
        for (const file of req.files) {
          fs.unlinkSync(file.path);
        }
      }
    } catch (cleanupError) {
      console.error('清理临时文件失败:', cleanupError);
    }
    
    return res.status(500).json({
      code: 500,
      message: '项目附件上传失败: ' + error.message
    });
  }
};

/**
 * 获取相关联的文件列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRelatedFiles = async (req, res) => {
  try {
    const { relatedId, relatedType } = req.body;
    
    if (!relatedId || !relatedType) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要的关联信息',
        data: null
      });
    }
    
    // 查询关联的文件
    const files = await fileModel.findAll({
      where: { 
        relatedId,
        relatedType,
        isDeleted: 0
      },
      order: [['createdAt', 'DESC']]
    });
    
    // 格式化文件信息
    const formattedFiles = files.map(file => ({
      id: file.id,
      fileName: file.fileName,
      originalName: file.originalName,
      fileSize: file.fileSize,
      mimeType: file.mimeType,
      extension: file.extension,
      url: `/api/v1/sys/file/download/${file.id}`,
      description: file.description,
      createdAt: file.createdAt
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取文件列表成功',
      data: formattedFiles
    });
  } catch (error) {
    console.error('获取关联文件列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取关联文件列表失败',
      error: error.message
    });
  }
};

/**
 * 根据ID下载文件
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.downloadFile = async (req, res) => {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({
          code: 400,
          message: '缺少文件ID',
          data: null
        });
      }
      
      // 查询文件记录
      const fileRecord = await fileModel.findOne({
        where: { id, isDeleted: 0 }
      });
      
      if (!fileRecord) {
        return res.status(404).json({
          code: 404,
          message: '文件不存在或已删除',
          data: null
        });
      }
      
      let filePath = fileRecord.filePath;
      let fileExists = fs.existsSync(filePath);
      
      // 如果文件不存在原路径，尝试查找备选位置
      if (!fileExists) {
        // 尝试备选路径1: 如果路径中包含单数形式，尝试查找复数形式
        if (fileRecord.relatedType === 'research_project' && filePath.includes('/research_project/')) {
          const altPath = filePath.replace('/research_project/', '/research_projects/');
          if (fs.existsSync(altPath)) {
            filePath = altPath;
            fileExists = true;
            console.log(`使用备选路径1下载文件: ${altPath}`);
          }
        }
        // 尝试备选路径2: 如果路径中包含复数形式，尝试查找单数形式
        else if (fileRecord.relatedType === 'research_project' && filePath.includes('/research_projects/')) {
          const altPath = filePath.replace('/research_projects/', '/research_project/');
          if (fs.existsSync(altPath)) {
            filePath = altPath;
            fileExists = true;
            console.log(`使用备选路径2下载文件: ${altPath}`);
          }
        }
      }
      
      // 如果文件不存在（所有尝试都失败）
      if (!fileExists) {
        // 标记文件为已删除
        await fileRecord.update({ isDeleted: 1 });
        
        return res.status(404).json({
          code: 404,
          message: '文件已被物理删除',
          data: null
        });
      }
      
      // 设置响应头并发送文件
      res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(fileRecord.originalName)}`);
      res.setHeader('Content-Type', fileRecord.mimeType);
      
      // 使用stream发送文件
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
      
    } catch (error) {
      console.error('文件下载失败:', error);
      return res.status(500).json({
        code: 500,
        message: '文件下载失败',
        error: error.message
      });
    }
  };
  
/**
 * 根据ID预览文件（在线查看）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.previewFile = async (req, res) => {
  console.log("previewFile1231231");
  
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少文件ID',
        data: null
      });
    }
    
    // 查询文件记录
    const fileRecord = await fileModel.findOne({
      where: { id, isDeleted: 0 }
    });
    
    if (!fileRecord) {
      return res.status(404).json({
        code: 404,
        message: '文件不存在或已删除',
        data: null
      });
    }
    
    let filePath = fileRecord.filePath;    
    
    // 设置响应头 - 关键区别：使用inline而不是attachment
    res.setHeader('Content-Disposition', `inline; filename=${encodeURIComponent(fileRecord.originalName)}`);
    res.setHeader('Content-Type', fileRecord.mimeType);
    
    // 使用stream发送文件
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
  } catch (error) {
    console.error('文件预览失败:', error);
    return res.status(500).json({
      code: 500,
      message: '文件预览失败',
      error: error.message
    });
  }
};


  /**
   * 删除文件
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  exports.deleteFile = async (req, res) => {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({
          code: 400,
          message: '缺少文件ID',
          data: null
        });
      }
      
      // 获取当前用户
      const userInfo = await getUserInfoFromRequest(req);
      
      // 查询文件记录
      const fileRecord = await fileModel.findOne({
        where: { id, isDeleted: 0 }
      });
      
      if (!fileRecord) {
        return res.status(404).json({
          code: 404,
          message: '文件不存在或已删除',
          data: null
        });
      }
      
      // 检查权限 - 只有上传者或管理员可以删除
      if (fileRecord.uploaderId !== userInfo.id && !['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
        return res.status(403).json({
          code: 403,
          message: '您没有权限删除该文件',
          data: null
        });
      }
      
      // 从数据库中永久删除记录（硬删除）
      await fileRecord.destroy();
      
      // 尝试物理删除文件
      try {
        if (fs.existsSync(fileRecord.filePath)) {
          fs.unlinkSync(fileRecord.filePath);
          console.log(`成功删除文件: ${fileRecord.filePath}`);
        } else {
          // 尝试查找备选位置
          // 如果文件路径中包含单数形式，尝试转换为复数形式
          let altPath = fileRecord.filePath;
          if (fileRecord.relatedType === 'research_project' && altPath.includes('/research_project/')) {
            altPath = altPath.replace('/research_project/', '/research_projects/');
            if (fs.existsSync(altPath)) {
              fs.unlinkSync(altPath);
              console.log(`成功删除文件(备选路径): ${altPath}`);
            }
          }
          // 如果文件路径中包含复数形式，尝试转换为单数形式
          else if (fileRecord.relatedType === 'research_project' && altPath.includes('/research_projects/')) {
            altPath = altPath.replace('/research_projects/', '/research_project/');
            if (fs.existsSync(altPath)) {
              fs.unlinkSync(altPath);
              console.log(`成功删除文件(备选路径): ${altPath}`);
            }
          } else {
            console.warn(`文件不存在，无法物理删除: ${fileRecord.filePath}`);
          }
        }
      } catch (deleteError) {
        console.warn('文件物理删除失败:', deleteError);
        // 继续执行，不因物理删除失败而中断操作
      }
      
      return res.status(200).json({
        code: 200,
        message: '文件删除成功',
        data: null
      });
    } catch (error) {
      console.error('文件删除失败:', error);
      return res.status(500).json({
        code: 500,
        message: '文件删除失败',
        error: error.message
      });
    }
  };

/**
 * 移动文件到项目文件夹
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.moveFileToProjectFolder = async (req, res) => {
  try {
    const { projectId, fileIds, class: classType } = req.body;
    
    if (!projectId || !fileIds) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：projectId和fileIds'
      });
    }
    
    // 确定存储路径
    const storagePath = classType || 'research_project';
    
    // 目标目录
    const targetDir = `uploads/${storagePath}/${projectId}/`;
    
    // 确保目标目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log(`创建项目文件目录: ${targetDir}`);
    }
    
    // 解析文件ID数组
    let fileIdArray = [];
    if (typeof fileIds === 'string') {
      try {
        fileIdArray = JSON.parse(fileIds);
      } catch (error) {
        fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
      }
    } else if (Array.isArray(fileIds)) {
      fileIdArray = fileIds;
    } else {
      return res.status(400).json({
        code: 400,
        message: 'fileIds格式不正确'
      });
    }
    
    // 处理每个文件
    const results = [];
    for (const fileId of fileIdArray) {
      // 查找文件记录
      const fileRecord = await fileModel.findOne({
        where: { id: fileId, isDeleted: 0 }
      });
      
      if (!fileRecord) {
        results.push({
          fileId,
          success: false,
          message: '文件不存在或已删除'
        });
        continue;
      }
      
      // 获取文件当前路径和文件名
      const currentPath = fileRecord.filePath;
      const fileName = path.basename(currentPath);
      const newPath = path.join(targetDir, fileName);
      
      try {
        // 移动文件
        if (fs.existsSync(currentPath)) {
          // 尝试直接移动
          try {
            fs.renameSync(currentPath, newPath);
            console.log(`文件已移动: ${currentPath} -> ${newPath}`);
          } catch (moveError) {
            console.error('移动文件失败:', moveError);
            // 如果移动失败，尝试复制后删除
            fs.copyFileSync(currentPath, newPath);
            fs.unlinkSync(currentPath);
            console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
          }
          
          // 更新数据库记录
          await fileRecord.update({
            filePath: newPath,
            projectId: projectId,
            relatedId: projectId,
            relatedType: storagePath
          });
          
          results.push({
            fileId,
            success: true,
            originalPath: currentPath,
            newPath: newPath,
            url: `/uploads/${storagePath}/${projectId}/${fileName}`
          });
        } else {
          // 如果文件不存在，检查其他可能的路径
          let found = false;
          
          // 尝试查找可能的路径
          const possiblePaths = [
            currentPath,
            currentPath.replace(`/general/`, `/${storagePath}/`),
            currentPath.replace(`/${storagePath}/`, `/general/`),
            // 处理单复数形式
            currentPath.replace(`/research_project/`, `/research_projects/`),
            currentPath.replace(`/research_projects/`, `/research_project/`)
          ];
          
          for (const possPath of possiblePaths) {
            if (fs.existsSync(possPath)) {
              try {
                fs.renameSync(possPath, newPath);
                console.log(`文件已移动(从备用路径): ${possPath} -> ${newPath}`);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(possPath, newPath);
                fs.unlinkSync(possPath);
                console.log(`文件已复制并删除源文件: ${possPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: projectId,
                relatedId: projectId,
                relatedType: storagePath
              });
              
              results.push({
                fileId,
                success: true,
                originalPath: possPath,
                newPath: newPath,
                url: `/uploads/${storagePath}/${projectId}/${fileName}`
              });
              
              found = true;
              break;
            }
          }
          
          if (!found) {
            results.push({
              fileId,
              success: false,
              message: '找不到源文件'
            });
          }
        }
      } catch (error) {
        console.error(`处理文件 ${fileId} 时出错:`, error);
        results.push({
          fileId,
          success: false,
          message: error.message
        });
      }
    }
    
    return res.status(200).json({
      code: 200,
      message: '文件移动处理完成',
      data: results
    });
  } catch (error) {
    console.error('移动文件到项目文件夹失败:', error);
    return res.status(500).json({
      code: 500,
      message: '移动文件失败: ' + error.message
    });
  }
};

/**
 * 删除目录及其内容
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteDirectory = async (req, res) => {
  try {
    const { path } = req.body;
    
    if (!path) {
      return res.status(400).json({
        code: 400,
        message: '缺少目录路径',
        data: null
      });
    }
    
    // 安全检查：确保路径以/uploads/开头，防止恶意删除其他目录
    if (!path.startsWith('/uploads/') && !path.startsWith('uploads/')) {
      return res.status(403).json({
        code: 403,
        message: '无效的目录路径，只允许删除uploads目录下的内容',
        data: null
      });
    }
    
    // 规范化路径，确保正确处理
    const normalizedPath = path.replace(/^\//, ''); // 移除开头的斜杠
    const absolutePath = path.resolve(normalizedPath);
    
    // 检查目录是否存在
    if (!fs.existsSync(absolutePath)) {
      return res.status(404).json({
        code: 404,
        message: '目录不存在',
        data: null
      });
    }
    
    // 递归删除目录及其内容
    const deleteRecursive = (dirPath) => {
      if (fs.existsSync(dirPath)) {
        fs.readdirSync(dirPath).forEach((file) => {
          const curPath = path.join(dirPath, file);
          if (fs.lstatSync(curPath).isDirectory()) {
            // 递归删除子目录
            deleteRecursive(curPath);
          } else {
            // 删除文件
            fs.unlinkSync(curPath);
          }
        });
        // 删除目录本身
        fs.rmdirSync(dirPath);
      }
    };
    
    // 执行删除操作
    deleteRecursive(absolutePath);
    
    return res.status(200).json({
      code: 200,
      message: '目录删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除目录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除目录失败',
      error: error.message
    });
  }
};

/**
 * 删除指定路径的目录（供内部使用的工具函数）
 * @param {string} directoryPath - 要删除的目录路径
 * @returns {Promise<boolean>} - 操作是否成功
 */
exports.deleteDirectoryUtil = async (directoryPath) => {
  console.log('删除目录:', directoryPath);
  try {
    // 安全检查：确保路径以/uploads/或uploads/开头
    if (!directoryPath.startsWith('/uploads/') && !directoryPath.startsWith('uploads/')) {
      console.error('无效的目录路径，只允许删除uploads目录下的内容:', directoryPath);
      return false;
    }
    
    // 规范化路径
    const normalizedPath = directoryPath.replace(/^\//, ''); // 移除开头的斜杠
    const absolutePath = path.resolve(normalizedPath);
    
    // 检查目录是否存在
    if (!fs.existsSync(absolutePath)) {
      console.warn('要删除的目录不存在:', absolutePath);
      return true; // 不存在也视为删除成功
    }
    
    // 递归删除目录及其内容
    const deleteRecursive = (dirPath) => {
      if (fs.existsSync(dirPath)) {
        fs.readdirSync(dirPath).forEach((file) => {
          const curPath = path.join(dirPath, file);
          if (fs.lstatSync(curPath).isDirectory()) {
            // 递归删除子目录
            deleteRecursive(curPath);
          } else {
            // 删除文件
            fs.unlinkSync(curPath);
          }
        });
        // 删除目录本身
        fs.rmdirSync(dirPath);
      }
    };
    
    // 执行删除操作
    deleteRecursive(absolutePath);
    console.log('目录删除成功:', absolutePath);
    return true;
    
  } catch (error) {
    console.error('删除目录失败:', directoryPath, error);
    return false;
  }
};