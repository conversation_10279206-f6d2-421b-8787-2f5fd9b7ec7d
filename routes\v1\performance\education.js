const express = require('express');
const educationController = require('../../../controllers/v1/education.controller');

const router = express.Router();

// 获取教育教学列表
router.post('/list', educationController.getEducations);

// 获取教育教学详情
router.post('/detail', educationController.getEducationDetail);

// 创建教育教学记录
router.post('/create', educationController.createEducation);

// 更新教育教学记录
router.post('/update', educationController.updateEducation);

// 删除教育教学记录
router.post('/delete', educationController.deleteEducation);

// 导入教育教学记录
router.post('/import', educationController.importEducations);

// 导出教育教学记录
router.post('/export', educationController.exportEducations);

module.exports = router; 