/**
 * 自定义API错误类
 */
class ApiError extends Error {
  constructor(message, code = 500, statusCode = 500, data = null) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.statusCode = statusCode;
    this.data = data;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 验证错误
 */
class ValidationError extends ApiError {
  constructor(message, data = null) {
    super(message, 400, 400, data);
  }
}

/**
 * 未授权错误
 */
class UnauthorizedError extends ApiError {
  constructor(message = '未授权访问') {
    super(message, 401, 401);
  }
}

/**
 * 禁止访问错误
 */
class ForbiddenError extends ApiError {
  constructor(message = '禁止访问') {
    super(message, 403, 403);
  }
}

/**
 * 不存在错误
 */
class NotFoundError extends ApiError {
  constructor(message = '资源不存在') {
    super(message, 404, 404);
  }
}

/**
 * 请求过多错误
 */
class TooManyRequestsError extends ApiError {
  constructor(message = '请求过于频繁') {
    super(message, 429, 429);
  }
}

/**
 * 请求体过大错误
 */
class PayloadTooLargeError extends ApiError {
  constructor(message = '请求体过大') {
    super(message, 413, 413);
  }
}

/**
 * 服务器内部错误
 */
class InternalServerError extends ApiError {
  constructor(message = '服务器内部错误') {
    super(message, 500, 500);
  }
}

module.exports = {
  ApiError,
  ValidationError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  TooManyRequestsError,
  PayloadTooLargeError,
  InternalServerError
}; 