const express = require('express');
const router = express.Router();
const socialServicesRulesController = require('../../../controllers/v1/sys/socialServicesRulesController');

/**
 * 获取社会服务规则列表
 * @route GET /v1/sys/social-services-rules/list
 * @group 社会服务规则管理 - 社会服务规则相关接口
 * @param {string} contribution_name.query - 贡献类型（模糊搜索）
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 0, page: 1, pageSize: 10, list: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list',  socialServicesRulesController.getSocialServicesRules);

/**
 * 获取社会服务规则详情
 * @route GET /v1/sys/social-services-rules/detail
 * @group 社会服务规则管理 - 社会服务规则相关接口
 * @param {string} id.query - 规则ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {规则详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail',  socialServicesRulesController.getSocialServicesRuleDetail);

/**
 * 创建社会服务规则
 * @route POST /v1/sys/social-services-rules/create
 * @group 社会服务规则管理 - 社会服务规则相关接口
 * @param {string} contribution_name.body.required - 贡献类型
 * @param {string} secondary_option.body.required - 二级选择
 * @param {number} score.body.required - 分数
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {创建的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create',  socialServicesRulesController.createSocialServicesRule);

/**
 * 更新社会服务规则
 * @route PUT /v1/sys/social-services-rules/update
 * @group 社会服务规则管理 - 社会服务规则相关接口
 * @param {string} id.body.required - 规则ID
 * @param {string} contribution_name.body - 贡献类型
 * @param {string} secondary_option.body - 二级选择
 * @param {number} score.body - 分数
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {更新后的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/update',  socialServicesRulesController.updateSocialServicesRule);

/**
 * 删除社会服务规则
 * @route DELETE /v1/sys/social-services-rules/delete
 * @group 社会服务规则管理 - 社会服务规则相关接口
 * @param {string} id.body.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete',  socialServicesRulesController.deleteSocialServicesRule);

/**
 * 批量删除社会服务规则
 * @route DELETE /v1/sys/social-services-rules/batch-delete
 * @group 社会服务规则管理 - 社会服务规则相关接口
 * @param {Array} ids.body.required - 规则ID列表
 * @returns {object} 200 - {code: 200, message: "批量删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/batch-delete',  socialServicesRulesController.batchDeleteSocialServicesRules);

module.exports = router; 