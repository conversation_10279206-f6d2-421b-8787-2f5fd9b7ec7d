<template>
  <div class="zy-logo">
    <img :width="size" :src="setting.websiteInfo.logo || url || defaultLogo">
    <span v-if="showTitle" class="title">{{setting.websiteInfo.name || '暨南大学基础医学与公共卫生学院教师绩效评定与管理平台'}}@{{ setting.websiteInfo.version || '1.0.0' }}</span>
  </div>
</template>

<script setup>
import {ref} from 'vue'
// 使用在线默认logo替代本地不存在的图片
import setting from '@/setting.js';

const defaultLogo = 'https://jnumed.jnu.edu.cn/_upload/tpl/02/6a/618/template618/images/logo.png';

const props = defineProps(
    {
      url: {
        type: String,
        default: ''
      },
      showTitle: {
        type: Boolean,
        default: true
      },
      size: {
        type: [String,Number],
        default: '56'
      },
    }
)
// 定义可触发的事件
const emit = defineEmits(['logo-click',])
const clickLogo = () => {
  emit('logo-click')
}
const webSiteTitle = ref(import.meta.env.VITE_APP_WEB_TITLE)
const webSiteTitleV = ref(import.meta.env.VITE_APP_WEB_VERSION)
</script>

<style lang="scss" scoped>
.zy-logo {
  display: flex;
  justify-content: left;
  align-items: center;
  .title {
    font-weight: bold;
    margin-left: 12px;
    font-size: 1.3rem;
    font-family: "Baskerville Old Face";
  }
}
</style>
