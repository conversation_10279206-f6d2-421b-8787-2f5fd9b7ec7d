const { Op } = require('sequelize');
const researchFundsRulesModel = require('../../../models/v1/mapping/researchFundsRulesModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取科研经费规则列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getResearchFundsRules = async (req, res) => {
  try {
    console.log('🔍 获取科研经费规则列表 - 请求参数:', req.query);
    
    // 获取查询参数
    const { page = 1, pageSize = 10, minAmount, maxAmount } = req.query;
    
    // 确保 page 和 pageSize 是有效的数字，否则使用默认值
    const pageNum = page ? parseInt(page) : 1;
    const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

    // 如果 page 或 pageSize 是无效数字，返回默认值
    const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
    const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;
    
    console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

    // 构建查询条件
    const where = {};
    if (minAmount) {
      where.minAmount = { [Op.gte]: parseFloat(minAmount) };
    }
    if (maxAmount) {
      where.maxAmount = { [Op.lte]: parseFloat(maxAmount) };
    }
    
    console.log('🔍 最终查询条件:', JSON.stringify(where));

    // 分页查询
    const offset = (validPage - 1) * validPageSize;
    try {
      console.log('📚 执行数据库查询...');
      const { count, rows } = await researchFundsRulesModel.findAndCountAll({
        where,
        offset,
        limit: validPageSize,
        order: [['minAmount', 'ASC']] // 按最小金额升序
      });
      
      console.log(`✅ 查询成功: 共${count}条记录`);
      
      const totalPages = Math.ceil(count / validPageSize);
      
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          total: count,
          page: validPage,
          pageSize: validPageSize,
          totalPages,
          list: rows
        }
      });
    } catch (dbError) {
      console.error('❌ 数据库查询失败:', dbError);
      return res.status(500).json({
        code: 500,
        message: `数据库查询失败: ${dbError.message}`,
        data: null
      });
    }
  } catch (error) {
    console.error('❌ 获取科研经费规则列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取科研经费规则列表失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取科研经费规则详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getResearchFundsRuleDetail = async (req, res) => {
  try {
    const { id } = req.query;
    console.log('🔍 获取科研经费规则详情 - ID:', id);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    const rule = await researchFundsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }
    
    console.log(`✅ 查询成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 获取科研经费规则详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取科研经费规则详情失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 创建科研经费规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createResearchFundsRule = async (req, res) => {
  try {
    const { minAmount, maxAmount, score } = req.body;
    console.log('📝 创建科研经费规则 - 请求数据:', { minAmount, maxAmount, score });
    
    // 校验必填参数
    if (minAmount === undefined || score === undefined) {
      return res.status(400).json({
        code: 400,
        message: '最小金额和基础分数不能为空',
        data: null
      });
    }
    
    // 检查金额范围是否与其他规则重叠
    const existingRule = await researchFundsRulesModel.findOne({
      where: {
        [Op.or]: [
          {
            minAmount: { [Op.lte]: minAmount },
            maxAmount: { [Op.gte]: minAmount }
          },
          {
            minAmount: { [Op.lte]: maxAmount || Number.MAX_SAFE_INTEGER },
            maxAmount: { [Op.gte]: maxAmount || Number.MAX_SAFE_INTEGER }
          }
        ]
      }
    });

    if (existingRule) {
      return res.status(400).json({
        code: 400,
        message: '金额范围与其他规则重叠',
        data: null
      });
    }
    
    // 创建规则
    const rule = await researchFundsRulesModel.create({
      id: uuidv4(),
      minAmount,
      maxAmount,
      score,
      createdBy: req.user.id
    });
    
    console.log(`✅ 创建成功: 规则ID ${rule.id}`);
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 创建科研经费规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `创建科研经费规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 更新科研经费规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateResearchFundsRule = async (req, res) => {
  try {
    const { id, minAmount, maxAmount, score } = req.body;
    console.log('📝 更新科研经费规则 - 请求数据:', { id, minAmount, maxAmount, score });

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    // 查找规则
    const rule = await researchFundsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }

    // 如果更新了金额范围，检查是否与其他规则重叠
    if ((minAmount !== undefined && minAmount !== rule.minAmount) || 
        (maxAmount !== undefined && maxAmount !== rule.maxAmount)) {
      const existingRule = await researchFundsRulesModel.findOne({
        where: {
          id: { [Op.ne]: id },
          [Op.or]: [
            {
              minAmount: { [Op.lte]: minAmount || rule.minAmount },
              maxAmount: { [Op.gte]: minAmount || rule.minAmount }
            },
            {
              minAmount: { [Op.lte]: maxAmount || rule.maxAmount || Number.MAX_SAFE_INTEGER },
              maxAmount: { [Op.gte]: maxAmount || rule.maxAmount || Number.MAX_SAFE_INTEGER }
            }
          ]
        }
      });

      if (existingRule) {
        return res.status(400).json({
          code: 400,
          message: '金额范围与其他规则重叠',
          data: null
        });
      }
    }

    // 更新规则
    await rule.update({
      minAmount: minAmount !== undefined ? minAmount : rule.minAmount,
      maxAmount: maxAmount !== undefined ? maxAmount : rule.maxAmount,
      score: score !== undefined ? score : rule.score
    });
    
    console.log(`✅ 更新成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 更新科研经费规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `更新科研经费规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 删除科研经费规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteResearchFundsRule = async (req, res) => {
  try {
    const { id } = req.query;
    console.log('🗑️ 删除科研经费规则 - ID:', id);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    const rule = await researchFundsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }

    await rule.destroy();
    
    console.log(`✅ 删除成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('❌ 删除科研经费规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `删除科研经费规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 批量删除科研经费规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteResearchFundsRules = async (req, res) => {
  try {
    const { ids } = req.body;
    console.log('🗑️ 批量删除科研经费规则 - IDs:', ids);

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: 'ID列表不能为空',
        data: null
      });
    }

    await researchFundsRulesModel.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });
    
    console.log(`✅ 批量删除成功: 共${ids.length}条记录`);
    
    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: null
    });
  } catch (error) {
    console.error('❌ 批量删除科研经费规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `批量删除科研经费规则失败: ${error.message}`,
      data: null
    });
  }
}; 