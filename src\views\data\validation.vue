<template>
  <div class="data-validation">
    <a-card title="数据校验" :bordered="false">
      <a-alert
        message="数据校验说明"
        description="本页面用于验证收集到的数据的准确性和完整性。您可以查看数据校验结果、设置校验规则、导出校验报告等。"
        type="info"
        show-icon
        class="mb-4"
      />

      <a-row :gutter="16" class="mb-4">
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <FileSearchOutlined />
                待校验数据
              </span>
            </template>
            <div class="card-content">
              <span class="number">256</span>
              <span class="unit">条</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <CheckCircleOutlined />
                校验通过
              </span>
            </template>
            <div class="card-content">
              <span class="number">180</span>
              <span class="unit">条</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <WarningOutlined />
                校验异常
              </span>
            </template>
            <div class="card-content">
              <span class="number">76</span>
              <span class="unit">条</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <PercentageOutlined />
                通过率
              </span>
            </template>
            <div class="card-content">
              <span class="number">70.3</span>
              <span class="unit">%</span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="results" tab="校验结果">
          <a-form layout="inline" :model="searchForm" class="search-form">
            <a-form-item label="数据类型">
              <a-select v-model:value="searchForm.dataType" placeholder="请选择数据类型" style="width: 200px">
                <a-select-option value="research">科研项目</a-select-option>
                <a-select-option value="teaching">教学获奖</a-select-option>
                <a-select-option value="international">国际交流</a-select-option>
                <a-select-option value="social">社会服务</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="校验状态">
              <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 200px">
                <a-select-option value="pending">待校验</a-select-option>
                <a-select-option value="passed">校验通过</a-select-option>
                <a-select-option value="failed">校验异常</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>

          <a-table :columns="resultColumns" :data-source="resultData" :pagination="pagination" @change="handleTableChange">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a @click="viewDetail(record)">查看详情</a>
                  <a-divider type="vertical" />
                  <a @click="revalidate(record)">重新校验</a>
                  <a-divider type="vertical" />
                  <a @click="exportReport(record)">导出报告</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="rules" tab="校验规则">
          <div class="rules-header">
            <a-button type="primary" @click="showRuleModal">
              <template #icon><PlusOutlined /></template>
              添加规则
            </a-button>
          </div>
          <a-table :columns="ruleColumns" :data-source="ruleData" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-switch
                  v-model:checked="record.status"
                  :checkedChildren="'启用'"
                  :unCheckedChildren="'禁用'"
                  @change="(checked) => toggleRuleStatus(record, checked)"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a @click="editRule(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm
                    title="确定要删除该规则吗？"
                    @confirm="deleteRule(record)"
                  >
                    <a class="text-danger">删除</a>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="batch" tab="批量校验">
          <a-form :model="batchForm" layout="vertical">
            <a-form-item label="选择数据类型">
              <a-checkbox-group v-model:value="batchForm.dataTypes">
                <a-checkbox value="research">科研项目</a-checkbox>
                <a-checkbox value="teaching">教学获奖</a-checkbox>
                <a-checkbox value="international">国际交流</a-checkbox>
                <a-checkbox value="social">社会服务</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            <a-form-item label="时间范围">
              <a-range-picker v-model:value="batchForm.dateRange" />
            </a-form-item>
            <a-form-item label="校验规则">
              <a-select
                v-model:value="batchForm.rules"
                mode="multiple"
                placeholder="请选择校验规则"
                style="width: 100%"
              >
                <a-select-option value="rule1">必填字段校验</a-select-option>
                <a-select-option value="rule2">数值范围校验</a-select-option>
                <a-select-option value="rule3">日期格式校验</a-select-option>
                <a-select-option value="rule4">数据一致性校验</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="handleBatchValidate">
                <template #icon><CheckCircleOutlined /></template>
                开始校验
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <a-modal
      v-model:visible="detailModalVisible"
      title="校验详情"
      @ok="handleDetailModalOk"
      @cancel="handleDetailModalCancel"
      width="800px"
    >
      <template v-if="currentRecord">
        <div class="detail-info">
          <h3>{{ currentRecord.dataName }}</h3>
          <p class="detail-meta">
            <span>提交人：{{ currentRecord.submitter }}</span>
            <span>提交时间：{{ currentRecord.submitTime }}</span>
            <span>校验时间：{{ currentRecord.validateTime }}</span>
            <span>状态：{{ getStatusText(currentRecord.status) }}</span>
          </p>
          <a-divider />
          <div class="detail-fields">
            <div v-for="(field, index) in currentRecord.fields" :key="index" class="field-item">
              <label>{{ field.label }}：</label>
              <div class="field-value">
                <template v-if="field.type === 'file'">
                  <a :href="field.value" target="_blank">查看文件</a>
                </template>
                <template v-else>
                  {{ field.value }}
                </template>
              </div>
            </div>
          </div>
          <template v-if="currentRecord.issues && currentRecord.issues.length > 0">
            <a-divider />
            <div class="detail-issues">
              <h4>校验问题</h4>
              <a-list :data-source="currentRecord.issues">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-tag color="red">{{ item.type }}</a-tag>
                    {{ item.message }}
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </template>
        </div>
      </template>
    </a-modal>

    <a-modal
      v-model:visible="ruleModalVisible"
      :title="isEdit ? '编辑规则' : '添加规则'"
      @ok="handleRuleModalOk"
      @cancel="handleRuleModalCancel"
      width="600px"
    >
      <a-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" layout="vertical">
        <a-form-item label="规则名称" name="name">
          <a-input v-model:value="ruleForm.name" placeholder="请输入规则名称" />
        </a-form-item>
        <a-form-item label="规则类型" name="type">
          <a-select v-model:value="ruleForm.type" placeholder="请选择规则类型">
            <a-select-option value="required">必填字段校验</a-select-option>
            <a-select-option value="range">数值范围校验</a-select-option>
            <a-select-option value="date">日期格式校验</a-select-option>
            <a-select-option value="consistency">数据一致性校验</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="适用数据类型" name="dataTypes">
          <a-select
            v-model:value="ruleForm.dataTypes"
            mode="multiple"
            placeholder="请选择适用数据类型"
            style="width: 100%"
          >
            <a-select-option value="research">科研项目</a-select-option>
            <a-select-option value="teaching">教学获奖</a-select-option>
            <a-select-option value="international">国际交流</a-select-option>
            <a-select-option value="social">社会服务</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="规则参数" name="params">
          <a-textarea v-model:value="ruleForm.params" :rows="4" placeholder="请输入规则参数（JSON格式）" />
        </a-form-item>
        <a-form-item label="规则描述" name="description">
          <a-textarea v-model:value="ruleForm.description" :rows="4" placeholder="请输入规则描述" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import {
  FileSearchOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  PercentageOutlined,
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 当前激活的标签页
const activeTab = ref('results')

// 搜索表单
const searchForm = reactive({
  dataType: undefined,
  status: undefined
})

// 校验结果表格列定义
const resultColumns = [
  {
    title: '数据名称',
    dataIndex: 'dataName',
    key: 'dataName',
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    key: 'dataType',
  },
  {
    title: '提交人',
    dataIndex: 'submitter',
    key: 'submitter',
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    sorter: true,
  },
  {
    title: '校验时间',
    dataIndex: 'validateTime',
    key: 'validateTime',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 校验结果数据
const resultData = ref([
  {
    key: '1',
    dataName: '基于人工智能的教学评价研究',
    dataType: '科研项目',
    submitter: '张三',
    submitTime: '2023-12-01 10:00:00',
    validateTime: '2023-12-01 10:30:00',
    status: 'passed',
    fields: [
      { label: '项目名称', type: 'text', value: '基于人工智能的教学评价研究' },
      { label: '项目级别', type: 'select', value: '国家级' },
      { label: '项目金额', type: 'number', value: '500000' },
      { label: '项目成果', type: 'text', value: '发表论文3篇，申请专利1项' }
    ]
  },
  {
    key: '2',
    dataName: '优秀教学成果奖',
    dataType: '教学获奖',
    submitter: '李四',
    submitTime: '2023-12-05 14:30:00',
    validateTime: '2023-12-05 15:00:00',
    status: 'failed',
    fields: [
      { label: '获奖名称', type: 'text', value: '优秀教学成果奖' },
      { label: '获奖级别', type: 'select', value: '省级' },
      { label: '获奖时间', type: 'date', value: '2023-11-15' },
      { label: '获奖证书', type: 'file', value: '/path/to/certificate.pdf' }
    ],
    issues: [
      { type: '必填字段', message: '获奖证书文件未上传' }
    ]
  },
  {
    key: '3',
    dataName: '国际学术会议',
    dataType: '国际交流',
    submitter: '王五',
    submitTime: '2023-12-10 09:15:00',
    validateTime: '2023-12-10 09:45:00',
    status: 'pending',
    fields: [
      { label: '交流名称', type: 'text', value: '国际学术会议' },
      { label: '交流国家', type: 'text', value: '美国' },
      { label: '交流时间', type: 'date', value: '2023-11-20' },
      { label: '交流成果', type: 'text', value: '发表会议论文1篇' }
    ]
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 3,
  showSizeChanger: true,
  showQuickJumper: true,
})

// 规则表格列定义
const ruleColumns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '规则类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '适用数据类型',
    dataIndex: 'dataTypes',
    key: 'dataTypes',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 规则数据
const ruleData = ref([
  {
    key: '1',
    name: '必填字段校验',
    type: '必填字段校验',
    dataTypes: ['科研项目', '教学获奖', '国际交流', '社会服务'],
    status: true,
  },
  {
    key: '2',
    name: '数值范围校验',
    type: '数值范围校验',
    dataTypes: ['科研项目', '教学获奖'],
    status: true,
  },
])

// 批量校验表单
const batchForm = reactive({
  dataTypes: [],
  dateRange: [],
  rules: []
})

// 详情相关
const detailModalVisible = ref(false)
const currentRecord = ref(null)

// 规则表单
const ruleModalVisible = ref(false)
const isEdit = ref(false)
const ruleFormRef = ref(null)
const ruleForm = reactive({
  name: '',
  type: undefined,
  dataTypes: [],
  params: '',
  description: ''
})

// 规则表单验证规则
const ruleRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在2-50个字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  dataTypes: [
    { required: true, message: '请选择适用数据类型', trigger: 'change' }
  ],
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    pending: 'orange',
    passed: 'green',
    failed: 'red'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    pending: '待校验',
    passed: '校验通过',
    failed: '校验异常'
  }
  return textMap[status] || status
}

// 查看详情
const viewDetail = (record) => {
  currentRecord.value = record
  detailModalVisible.value = true
}

// 重新校验
const revalidate = (record) => {
  message.success(`已开始重新校验：${record.dataName}`)
}

// 导出报告
const exportReport = (record) => {
  message.success(`已导出校验报告：${record.dataName}`)
}

// 搜索
const handleSearch = () => {
  console.log('Search form:', searchForm)
  // 这里添加搜索逻辑
}

// 重置搜索
const handleReset = () => {
  searchForm.dataType = undefined
  searchForm.status = undefined
}

// 批量校验
const handleBatchValidate = () => {
  if (batchForm.dataTypes.length === 0) {
    message.warning('请至少选择一种数据类型')
    return
  }
  if (!batchForm.dateRange || batchForm.dateRange.length === 0) {
    message.warning('请选择时间范围')
    return
  }
  if (batchForm.rules.length === 0) {
    message.warning('请至少选择一条校验规则')
    return
  }
  message.success('批量校验任务已启动')
}

// 显示规则模态框
const showRuleModal = () => {
  isEdit.value = false
  ruleForm.name = ''
  ruleForm.type = undefined
  ruleForm.dataTypes = []
  ruleForm.params = ''
  ruleForm.description = ''
  ruleModalVisible.value = true
}

// 编辑规则
const editRule = (record) => {
  isEdit.value = true
  Object.assign(ruleForm, record)
  ruleModalVisible.value = true
}

// 切换规则状态
const toggleRuleStatus = (record, checked) => {
  record.status = checked
  message.success(`规则已${checked ? '启用' : '禁用'}`)
}

// 删除规则
const deleteRule = (record) => {
  ruleData.value = ruleData.value.filter(item => item.key !== record.key)
  message.success('规则已删除')
}

// 处理详情模态框确认
const handleDetailModalOk = () => {
  detailModalVisible.value = false
}

// 处理详情模态框取消
const handleDetailModalCancel = () => {
  detailModalVisible.value = false
}

// 处理规则模态框确认
const handleRuleModalOk = () => {
  ruleFormRef.value.validate().then(() => {
    // 这里添加保存规则的逻辑
    message.success(`${isEdit.value ? '编辑' : '创建'}成功！`)
    ruleModalVisible.value = false
  }).catch(error => {
    console.log('Validation failed:', error)
  })
}

// 处理规则模态框取消
const handleRuleModalCancel = () => {
  ruleModalVisible.value = false
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
  console.log('Table changed:', pag, filters, sorter)
}
</script>

<style scoped>
.data-validation {
  padding: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-content {
  text-align: center;
}

.card-content .number {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.card-content .unit {
  margin-left: 4px;
  color: #666;
}

.search-form {
  margin-bottom: 16px;
}

.rules-header {
  margin-bottom: 16px;
}

.detail-info {
  padding: 0 24px;
}

.detail-meta {
  color: #666;
  margin: 10px 0;
}

.detail-meta span {
  margin-right: 20px;
}

.detail-fields {
  margin: 20px 0;
}

.field-item {
  margin-bottom: 16px;
  display: flex;
}

.field-item label {
  width: 120px;
  font-weight: 500;
}

.field-value {
  flex: 1;
}

.detail-issues {
  background-color: #fff2f0;
  padding: 16px;
  border-radius: 4px;
}

.text-danger {
  color: #ff4d4f;
}
</style> 