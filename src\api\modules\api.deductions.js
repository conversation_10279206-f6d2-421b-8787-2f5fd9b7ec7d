import request from '@/utils/request'
import axios from 'axios'
import { useUserId } from '@/composables/useUserId'

// 扣分相关接口
const api = {
  list: '/sys/deduction/list',
  detail: '/sys/deduction',
  create: '/sys/deduction',
  update: '/sys/deduction',
  delete: '/sys/deduction',
  import: '/sys/deduction/import',
  export: '/sys/deduction/export',
  typeDistribution: '/sys/deduction/stats/type-distribution',
  timeDistribution: '/sys/deduction/stats/time-distribution',
  teacherRanking: '/sys/deduction/stats/teacher-ranking',
  reasonAnalysis: '/sys/deduction/stats/reason-analysis',
  personalList: '/sys/deduction/personal/list',
  personalStats: '/sys/deduction/personal/stats'
}

// 获取全部扣分列表
export function getDeductions(params) {
  return request.get(api.list, params)
}

// 获取扣分详情
export function getDeductionDetail(id) {
  return request.get(`${api.detail}/${id}`)
}



// 导入扣分数据
export function importDeductions(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return axios.post(api.import, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出扣分数据
export function exportDeductions(params) {
  return request.get(api.export, params, { responseType: 'blob' })
}

// 获取扣分类型分布
export function getDeductionTypeDistribution(params) {
  return request.get(api.typeDistribution, params)
}

// 获取扣分时间分布
export function getDeductionTimeDistribution() {
  return request.get(api.timeDistribution)
}

// 获取教师扣分排名
export function getTeacherDeductionRanking(params) {
  return request.get(api.teacherRanking, params)
}

// 获取扣分原因分析
export function getDeductionReasonAnalysis(params) {
  return request.get(api.reasonAnalysis, params)
}

// 获取个人扣分列表
export function getPersonalDeductions(params) {
  return request.get(api.personalList, params)
}

// 获取个人扣分统计
export async function getPersonalDeductionStats() {
  try {
    const { getUserId } = useUserId();
    const userId = await getUserId(true);
    let params = {};
    
    // 只有在userId有值且不为空字符串时才添加
    if (userId && userId !== '') {
      params.userId = userId;
    }
    
    return request.get(api.personalStats, params)
  } catch (error) {
    console.error('获取个人扣分统计错误:', error);
    throw error;
  }
}

// 添加扣分
export function addDeduction(data) {
  return request.post(api.create, data);
}

// 更新扣分
export function updateDeduction(id, data) {
  return request.put(`${api.update}/${id}`, data);
}

// 删除扣分
export function deleteDeduction(id, userId) {
  // 确保userId是一个有效值
  const userIdParam = userId || '';
  return request.deleteWithQuery(`${api.delete}/${id}?userId=${userIdParam}`);
}