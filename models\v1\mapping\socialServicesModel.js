const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

/**
 * 社会服务模型
 * @module models/v1/mapping/socialServiceModel
 */
const SocialService = sequelize.define('social_services', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: 'ID'
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '服务名称'
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '服务类型'
  },
  target: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '服务对象'
  },
  startDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '开始时间'
  },
  endDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '结束时间'
  },
  userIdList: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '参与用户ID'
  },
  usernameList: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '参与用户名'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '服务内容'
  },
  result: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '服务成果'
  },
  score: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '得分'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    defaultValue: 1,
    comment: '状态'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'social_services',
  timestamps: true,
  indexes: [
    {
      name: 'idx_social_service_type',
      fields: ['type']
    },
    {
      name: 'idx_social_service_target',
      fields: ['target']
    },
    {
      name: 'idx_social_service_status',
      fields: ['status']
    }
  ]
});

module.exports = SocialService; 