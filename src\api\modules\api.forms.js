import service from '../server'

// 获取表单列表
export const getFormsList = (data) => {
  return service.post('/api/v1/form/list', data)
}

// 获取表单详情
export const getFormDetail = (id) => {
  return service.post('/api/v1/form/detail', { id })
}

// 创建表单
export const createForm = (data) => {
  return service.post('/api/v1/form/create', data)
}

// 更新表单
export const updateForm = (id, data) => {
  return service.post('/api/v1/form/update', { id, ...data })
}

// 删除表单
export const deleteForm = (id) => {
  return service.post('/api/v1/form/delete', { id })
} 