// 首页专用样式
// 用于首页绩效管理系统的样式定义

// 暨南大学头部样式
.jnu-header {
  display: flex;
  align-items: center;
  padding: 15px 0;
  max-width: 1400px;
  margin: 0 auto;
  
  .jnu-logo {
    margin-right: 15px;
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.08));
    max-height: 50px;
    width: auto;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .jnu-title {
    color: #7fc7a0;
    font-size: 24px;
    font-weight: bold;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

.jnu-divider {
  border: none;
  height: 2px;
  background: linear-gradient(to right, #7fc7a0, #e6bf7c);
  margin: 0 0 25px 0;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

// 页面容器样式
.zy-container {
  min-height: 100vh;
  background: #fff;
  padding: 20px 0;
}

// 主要内容区域
.one {
  max-width: 1400px;
  margin: 0 auto 2rem auto;
  padding: 0 16px;
  
  .major {
    text-align: center;
    padding: 2rem 0;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    
    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 1rem;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .sub-title {
      font-size: 1.1rem;
      color: #7f8c8d;
      margin-bottom: 1.5rem;
      font-weight: 500;

      a {
        color: #3498db;
        text-decoration: none;
        font-weight: 600;

        &:hover {
          color: #2980b9;
          text-decoration: underline;
        }
      }
    }

    p {
      font-size: 1.1rem;
      line-height: 1.7;
      color: #34495e;
      max-width: 900px;
      margin: 0 auto 2rem auto;
      text-align: justify;
    }

    .system-features {
      margin-top: 2rem;

      .feature-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 1.5rem;
        max-width: 700px;
        margin: 0 auto;

        .feature-item {
          display: flex;
          align-items: center;
          padding: 1rem;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
          }

          .feature-icon {
            font-size: 2rem;
            margin-right: 1rem;
            flex-shrink: 0;
          }

          .feature-text {
            flex: 1;

            strong {
              display: block;
              font-size: 0.95rem;
              color: #2c3e50;
              margin-bottom: 0.25rem;
            }

            br + text {
              font-size: 0.85rem;
              color: #7f8c8d;
              line-height: 1.3;
            }
          }
        }
      }
    }
  }
}

// 系统概览样式
.overview, .performance-overview, .score-calculation, .my-score, .performance-analysis {
  max-width: 1400px;
  margin: 2rem auto;
  padding: 0 16px;
}

.overview {
  margin-bottom: 32px;
  
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
}

// 卡片内数字统计样式
.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  font-weight: 500;
}

.ant-statistic-content {
  font-size: 20px;
  margin-top: 4px;
  font-weight: 600;
}

// 绩效统计卡片样式
.performance-stats-card {
  background: #fafafa;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  border-radius: 8px;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }
}

.view-more-container {
  text-align: right;
  margin-top: 16px;
}

.view-more-btn {
  color: #1890ff;
  font-weight: bold;
  transition: all 0.3s ease;
  
  &:hover {
    color: #40a9ff;
  }
}

// 自定义分页样式
.custom-pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  
  .ant-pagination {
    .ant-pagination-item {
      border-radius: 4px;
      
      &.ant-pagination-item-active {
        background: #1890ff;
        border-color: #1890ff;

        a {
          color: #fff;
        }
      }
    }
    
    .ant-pagination-prev,
    .ant-pagination-next {
      border-radius: 4px;
      
      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }
}

// 图表区域样式
.charts {
  max-width: 1400px;
  margin: 2rem auto;
  padding: 0 16px;
}

// StatsCard样式优化
:deep(.stats-card) {
  width: 100%;
  height: 100%;
  
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
    
    .ant-card-head {
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
      }
    }
  }
}

.dimension-count {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  font-weight: 500;
}

// 筛选容器样式优化
.filter-container {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  
  .ant-space {
    width: 100%;
    justify-content: space-between;
    
    .ant-select {
      border-radius: 6px;
      
      &:hover {
        border-color: #40a9ff;
      }
      
      &.ant-select-focused {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
    
    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: #1890ff;
        border: 1px solid #1890ff;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .overview, .performance-overview, .score-calculation, .my-score, .performance-analysis, .charts {
    padding: 0 12px;
  }
}

@media (max-width: 992px) {
  .one {
    .major {
      .system-features {
        .feature-grid {
          grid-template-columns: 1fr;
          grid-template-rows: auto;
          max-width: 500px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .zy-container {
    padding: 10px 0;
  }
  
  .jnu-header {
    padding: 10px 16px;
    
    .jnu-logo {
      max-height: 40px;
      margin-right: 10px;
    }
    
    .jnu-title {
      font-size: 18px;
    }
  }
  
  .one {
    padding: 0 8px;
    
    .major {
      padding: 1.5rem 1rem;
      
      h1 {
        font-size: 1.8rem;
      }
      
      .sub-title {
        font-size: 1rem;
      }
      
      p {
        font-size: 0.9rem;
      }
    }
  }
  
  .overview, .performance-overview, .score-calculation, .my-score, .performance-analysis, .charts {
    margin: 1rem auto;
    padding: 0 8px;
  }
  
  .filter-container {
    padding: 8px 12px;
    
    .ant-space {
      flex-direction: column;
      gap: 8px;
      
      .ant-select,
      .ant-btn {
        width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .jnu-header {
    flex-direction: column;
    text-align: center;
    
    .jnu-logo {
      margin-right: 0;
      margin-bottom: 8px;
    }
    
    .jnu-title {
      font-size: 16px;
    }
  }
  
  .one {
    .major {
      h1 {
        font-size: 1.5rem;
      }

      .sub-title {
        font-size: 0.9rem;
      }

      p {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
      }

      .system-features {
        .feature-grid {
          grid-template-columns: 1fr;
          grid-template-rows: auto;
          gap: 1rem;

          .feature-item {
            padding: 0.75rem;

            .feature-icon {
              font-size: 1.5rem;
              margin-right: 0.75rem;
            }

            .feature-text {
              strong {
                font-size: 0.9rem;
              }
            }
          }
        }
      }
    }
  }
}
