const express = require('express');
const exchangeController = require('../../../controllers/v1/sys/internationalExchangeController');
const multer = require('multer');
const path = require('path');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../../uploads/temp'));
  },
  filename: function (req, file, cb) {
    cb(null, 'exchange-import-' + Date.now() + path.extname(file.originalname));
  }
});

// 创建上传中间件
const upload = multer({ 
  storage: storage,
  fileFilter: function (req, file, cb) {
    // 只接受xlsx格式的文件
    if (
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel'
    ) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传Excel文件！'));
    }
  }
});

const router = express.Router();

// 获取国际交流列表
router.post('/list', exchangeController.getExchanges);
// 获取指定国际交流
router.post('/detail', exchangeController.getExchangeById);
// 创建国际交流
router.post('/create', exchangeController.createExchange);
// 更新国际交流
router.post('/update', exchangeController.updateExchange);
// 删除国际交流
router.post('/delete', exchangeController.deleteExchange);
// 导入国际交流 - 添加文件上传中间件
router.post('/import', upload.single('file'), exchangeController.importExchanges);
// 导出国际交流
router.post('/export', exchangeController.exportExchanges);

module.exports = router; 