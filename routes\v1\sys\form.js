const express = require('express');
const formController = require('../../../controllers/v1/sys/formController');

const router = express.Router();

/**
 * 获取表单列表
 * @route POST /v1/sys/form/list
 * @group 表单管理 - 系统表单相关接口
 * @param {number} page.body - 页码，默认1
 * @param {number} limit.body - 每页数量，默认10
 * @param {string} name.body - 表单名称（模糊搜索）
 * @param {string} type.body - 表单类型
 * @param {number} status.body - 状态（0-停用，1-启用）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', formController.getForms);

/**
 * 获取指定表单
 * @route POST /v1/sys/form/detail
 * @group 表单管理 - 系统表单相关接口
 * @param {string} id.body.required - 表单ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {表单详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/detail', formController.getFormById);

/**
 * 创建表单
 * @route POST /v1/sys/form/create
 * @group 表单管理 - 系统表单相关接口
 * @param {string} name.body.required - 表单名称
 * @param {string} type.body.required - 表单类型
 * @param {string} description.body - 表单描述
 * @param {array} fields.body.required - 表单字段
 * @param {number} status.body - 状态（0-停用，1-启用），默认1
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "表单ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', formController.createForm);

/**
 * 更新表单
 * @route POST /v1/sys/form/update
 * @group 表单管理 - 系统表单相关接口
 * @param {string} id.body.required - 表单ID
 * @param {string} name.body - 表单名称
 * @param {string} type.body - 表单类型
 * @param {string} description.body - 表单描述
 * @param {array} fields.body - 表单字段
 * @param {number} status.body - 状态（0-停用，1-启用）
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update', formController.updateForm);

/**
 * 删除表单
 * @route POST /v1/sys/form/delete
 * @group 表单管理 - 系统表单相关接口
 * @param {string} id.body.required - 表单ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', formController.deleteForm);

/**
 * 提交表单数据
 * @route POST /v1/sys/form/submit
 * @group 表单管理 - 系统表单相关接口
 * @param {string} formId.body.required - 表单ID
 * @param {object} data.body.required - 表单数据
 * @returns {object} 200 - {code: 200, message: "提交成功", data: {id: "数据ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/submit', formController.submitFormData);

/**
 * 获取表单数据
 * @route POST /v1/sys/form/data
 * @group 表单管理 - 系统表单相关接口
 * @param {string} formId.body.required - 表单ID
 * @param {number} page.body - 页码，默认1
 * @param {number} limit.body - 每页数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/data', formController.getFormData);

module.exports = router; 