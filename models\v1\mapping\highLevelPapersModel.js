const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const userModel = require('./userModel');
const highLevelPapersRulesModel = require('./highLevelPapersRulesModel');

// 定义高水平论文模型
const HighLevelPaper = sequelize.define('high_level_papers', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    defaultValue: DataTypes.UUIDV4,
    comment: '论文ID'
  },
  submitterId: {
    type: DataTypes.CHAR(36),
    allowNull: true,
    comment: '提交人ID'
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '论文题目'
  },
  journal: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '期刊名称'
  },
  publicationTime: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '出版时间（弃用）'
  },
  paperLevelId: {
    type: DataTypes.CHAR(36),
    allowNull: false,
    comment: '论文级别ID'
  },
  allocationProportionBase: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '分配比例基数'
  },
  totalAllocationProportion: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '总分配比例'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  },
  attachmentUrl: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '附件URL'
  },
  doi: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'DOI标识'
  },
  reviewComment: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核意见'
  },
  reviewerId: {
    type: DataTypes.CHAR(36),
    allowNull: true,
    comment: '审核人ID'
  },
  ifReviewer: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    comment: '审核状态（0，拒审核 1，审核，null未审核）'
  },
  impactFactor: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '影响因子'
  },
  publishDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '发表时间/出版时间'
  },
  firstAuthorType: {
    type: DataTypes.TINYINT(1),
    allowNull: false,
    defaultValue: 1,
    comment: '第一作者类型：1-我院研究生，0-非我院研究生'
  },
  isFirstAffiliationOurs: {
    type: DataTypes.TINYINT(1),
    allowNull: false,
    defaultValue: 1,
    comment: '第一单位是否为我们大学(1:是,0:否)'
  },
  citations: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '引用次数'
  },
  collegeCorrespondentAuthorNumber: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '本学院通讯作者人数'
  },
  correspondentAuthorNumber: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '通讯作者总人数'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: false,
    defaultValue: 1,
    comment: '状态(1:启用,0:禁用)'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'high_level_papers',
  timestamps: true,
  indexes: [
    {
      name: 'idx_high_level_paper_status',
      fields: ['status']
    },
    {
      name: 'idx_high_level_paper_publish_date',
      fields: ['publishDate']
    },
    {
      name: 'idx_high_level_paper_first_author_type',
      fields: ['firstAuthorType']
    },
    {
      name: 'idx_high_level_paper_level_id',
      fields: ['paperLevelId']
    },
    {
      name: 'idx_high_level_paper_first_affiliation',
      fields: ['isFirstAffiliationOurs']
    }
  ]
});

// 添加关联关系
HighLevelPaper.belongsTo(userModel, {
  foreignKey: 'submitterId',
  as: 'submitter'
});

HighLevelPaper.belongsTo(userModel, {
  foreignKey: 'reviewerId',
  as: 'reviewer'
});

HighLevelPaper.belongsTo(highLevelPapersRulesModel, {
  foreignKey: 'paperLevelId',
  as: 'paperLevel'
});

module.exports = HighLevelPaper; 