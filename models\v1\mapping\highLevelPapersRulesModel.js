const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const userModel = require('./userModel');

// 定义高水平论文核算规则模型
const HighLevelPapersRules = sequelize.define('high_level_papers_rules', // 数据库表名为high_level_papers_rules
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: '主键，使用 UUID 唯一标识每条记录',
        },
        paperLevel: {
            type: DataTypes.STRING(255),
            notNull: true,
            allowNull: false,
            unique: true,
            comment: '论文级别（如 A1 Ⅰ、A1 Ⅱ 等）',
        },
        score: {
            type: DataTypes.DECIMAL(10, 2),
            notNull: true,
            allowNull: false,
            comment: '基础核算分数',
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: '论文级别的详细描述（如中科院 JCR 分区等）',
        },
        nonDepartmentAuthorCoefficient: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true,
            defaultValue: 0.9,
            comment: '非本院研究生第一作者的系数',
        },
        coFirstAuthorRankCoefficient: {
            type: DataTypes.STRING(255),
            allowNull: true,
            comment: '共同第一作者的排名系数（如 100%, 1/2, 1/3 等）',
        },
        maxPapersPerMentor: {
            type: DataTypes.INTEGER,
            allowNull: true,
            defaultValue: 5,
            comment: '每个导师最多填写的代表性论文数量',
        },
        nonDepartmentAuthorLimit: {
            type: DataTypes.INTEGER,
            allowNull: true,
            defaultValue: 1,
            comment: '非本院研究生第一作者的文章数量限制',
        },
        createdBy: {
            type: DataTypes.UUID,
            notNull: true,
            allowNull: false,
            comment: '创建者 ID（userId）',
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录创建时间',
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录最后修改时间',
        },
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
        indexes: [
            {
                unique: true,
                fields: ['paperLevel'],
                name: 'uk_paper_level'
            }
        ]
    }); 

// 添加关联关系
HighLevelPapersRules.belongsTo(userModel, {
    foreignKey: 'createdBy',
    as: 'creator'
});

module.exports = HighLevelPapersRules; 