/**
 * 定时任务调度器
 * 用于管理系统的定时任务，包括数据库备份等
 */

const cron = require('node-cron');
const { runBackupProcess } = require('./databaseBackup');

/**
 * 数据库备份任务
 * 每月1号凌晨2点执行
 */
const scheduleBackupTask = () => {
    // Cron表达式: 秒 分 时 日 月 周
    // '0 0 2 1 * *' 表示每月1号凌晨2点执行
    const backupTask = cron.schedule('0 0 2 1 * *', async () => {
        console.log('=== 定时备份任务开始执行 ===');
        console.log(`执行时间: ${new Date().toLocaleString('zh-CN')}`);
        
        try {
            const result = await runBackupProcess();

            if (result.success) {
                if (result.skipped) {
                    console.log('定时备份任务执行完成（已跳过）');
                    console.log(`跳过原因: ${result.message}`);
                    console.log(`当前备份文件总数: ${result.totalBackups}`);
                } else {
                    console.log('定时备份任务执行成功');
                    console.log(`备份文件: ${result.backupFilePath}`);
                    console.log(`当前备份文件总数: ${result.totalBackups}`);
                }
            } else {
                console.error('定时备份任务执行失败:', result.error);
            }
        } catch (error) {
            console.error('定时备份任务异常:', error);
        }
        
        console.log('=== 定时备份任务执行完成 ===');
    }, {
        scheduled: false, // 初始不启动，需要手动启动
        timezone: "Asia/Shanghai" // 设置时区为中国标准时间
    });
    
    return backupTask;
};

/**
 * 测试备份任务
 * 每分钟执行一次，用于测试
 */
const scheduleTestBackupTask = () => {
    const testTask = cron.schedule('0 * * * * *', async () => {
        console.log('=== 测试备份任务开始执行 ===');
        console.log(`执行时间: ${new Date().toLocaleString('zh-CN')}`);
        
        try {
            const result = await runBackupProcess();

            if (result.success) {
                if (result.skipped) {
                    console.log('测试备份任务执行完成（已跳过）');
                    console.log(`跳过原因: ${result.message}`);
                } else {
                    console.log('测试备份任务执行成功');
                    console.log(`备份文件: ${result.backupFilePath}`);
                }
            } else {
                console.error('测试备份任务执行失败:', result.error);
            }
        } catch (error) {
            console.error('测试备份任务异常:', error);
        }
        
        console.log('=== 测试备份任务执行完成 ===');
    }, {
        scheduled: false,
        timezone: "Asia/Shanghai"
    });
    
    return testTask;
};

/**
 * 任务管理器
 */
class TaskScheduler {
    constructor() {
        this.tasks = new Map();
        this.isInitialized = false;
    }
    
    /**
     * 初始化调度器
     */
    init() {
        if (this.isInitialized) {
            console.log('任务调度器已经初始化');
            return;
        }
        
        console.log('初始化任务调度器...');
        
        // 创建备份任务
        const backupTask = scheduleBackupTask();
        this.tasks.set('backup', backupTask);
        
        // 创建测试备份任务（开发环境使用）
        if (process.env.NODE_ENV === 'development') {
            const testBackupTask = scheduleTestBackupTask();
            this.tasks.set('testBackup', testBackupTask);
        }
        
        this.isInitialized = true;
        console.log('任务调度器初始化完成');
        console.log(`已注册 ${this.tasks.size} 个定时任务`);
    }
    
    /**
     * 启动指定任务
     * @param {string} taskName 任务名称
     */
    startTask(taskName) {
        const task = this.tasks.get(taskName);
        if (task) {
            task.start();
            console.log(`任务 "${taskName}" 已启动`);
        } else {
            console.error(`任务 "${taskName}" 不存在`);
        }
    }
    
    /**
     * 停止指定任务
     * @param {string} taskName 任务名称
     */
    stopTask(taskName) {
        const task = this.tasks.get(taskName);
        if (task) {
            task.stop();
            console.log(`任务 "${taskName}" 已停止`);
        } else {
            console.error(`任务 "${taskName}" 不存在`);
        }
    }
    
    /**
     * 启动所有任务
     */
    startAllTasks() {
        console.log('启动所有定时任务...');
        this.tasks.forEach((task, name) => {
            task.start();
            console.log(`任务 "${name}" 已启动`);
        });
        console.log('所有定时任务已启动');
    }
    
    /**
     * 停止所有任务
     */
    stopAllTasks() {
        console.log('停止所有定时任务...');
        this.tasks.forEach((task, name) => {
            task.stop();
            console.log(`任务 "${name}" 已停止`);
        });
        console.log('所有定时任务已停止');
    }
    
    /**
     * 获取任务状态
     * @param {string} taskName 任务名称
     * @returns {boolean} 任务是否正在运行
     */
    getTaskStatus(taskName) {
        const task = this.tasks.get(taskName);
        return task ? task.getStatus() === 'scheduled' : false;
    }
    
    /**
     * 获取所有任务状态
     * @returns {Object} 所有任务的状态
     */
    getAllTasksStatus() {
        const status = {};
        this.tasks.forEach((task, name) => {
            status[name] = {
                running: task.getStatus() === 'scheduled',
                status: task.getStatus()
            };
        });
        return status;
    }
    
    /**
     * 手动执行备份任务
     * @param {boolean} forceBackup - 是否强制备份（跳过频率检查）
     */
    async executeBackupNow(forceBackup = false) {
        console.log(`手动执行备份任务... (强制模式: ${forceBackup ? '是' : '否'})`);
        try {
            const result = await runBackupProcess(forceBackup);

            if (result.success) {
                if (result.skipped) {
                    console.log('手动备份任务执行完成（已跳过）');
                    console.log(`跳过原因: ${result.message}`);
                } else {
                    console.log('手动备份任务执行成功');
                    console.log(`备份文件: ${result.backupFilePath}`);
                }
            }

            return result;
        } catch (error) {
            console.error('手动备份任务执行失败:', error);
            throw error;
        }
    }
    
    /**
     * 销毁调度器
     */
    destroy() {
        console.log('销毁任务调度器...');
        this.stopAllTasks();
        this.tasks.clear();
        this.isInitialized = false;
        console.log('任务调度器已销毁');
    }
}

// 创建全局调度器实例
const scheduler = new TaskScheduler();

/**
 * 初始化定时任务系统
 */
const initScheduler = () => {
    try {
        scheduler.init();
        
        // 在生产环境自动启动备份任务
        if (process.env.NODE_ENV === 'production') {
            scheduler.startTask('backup');
            console.log('生产环境：自动启动数据库备份任务');
        } else {
            console.log('开发环境：备份任务已创建但未启动，可手动启动');
        }
        
        return scheduler;
    } catch (error) {
        console.error('初始化定时任务系统失败:', error);
        throw error;
    }
};

module.exports = {
    scheduler,
    initScheduler,
    TaskScheduler
};
