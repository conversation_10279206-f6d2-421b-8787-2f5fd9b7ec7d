const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const fileModel = sequelize.define(
  'file',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '文件ID'
    },
    projectId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '绩效项目ID'
    },
    fileName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '存储文件名'
    },
    originalName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '原始文件名'
    },
    filePath: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '文件存储路径'
    },
    fileSize: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '文件大小（字节）'
    },
    mimeType: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '文件MIME类型'
    },
    extension: {
      type: DataTypes.STRING(10),
      allowNull: false,
      comment: '文件扩展名'
    },
    uploaderId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '上传人ID（关联user.id）'
    },
    // 添加关联字段 - 表示文件属于哪个模块
    relatedId: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: '关联ID（如项目ID等）'
    },
    relatedType: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '关联类型（如research_project等）'
    },
    storageLocation: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'local',
      comment: '存储位置（如local）'
    },
    isDeleted: {
      type: DataTypes.TINYINT.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
      comment: '软删除标记（0=正常，1=删除）'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '文件描述'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '更新时间'
    }
  },
  {
    sequelize,
    modelName: 'file',
    tableName: 'file',
    timestamps: true
  }
);

// 在文件加载后添加关联，避免循环依赖问题
const setupAssociations = () => {
  const userModel = require('./userModel');

  // 与用户表关联
  fileModel.belongsTo(userModel, {
    foreignKey: 'uploaderId',
    as: 'uploader'
  });
};

// 确保关联设置被调用
setTimeout(setupAssociations, 0);

module.exports = fileModel;