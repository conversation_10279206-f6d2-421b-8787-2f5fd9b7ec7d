/**
 * 教学科技奖励级别管理模块权限配置
 * 简化版 - 角色验证，级别管理主要面向管理员
 */
module.exports = {
  // 基本权限配置
  list: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 所有用户都可以查看级别列表，用于下拉选择等
  },
  
  listWithCount: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 所有用户都可以查看级别统计信息
  },
  
  detail: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 所有用户都可以查看级别详情
  },
  
  create: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以创建级别
  },
  
  update: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以更新级别
  },
  
  delete: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以删除级别
  },
  
  // 统计相关权限
  distribution: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  awards: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  }
};
