const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

/**
 * 用户职称记录模型
 * 表名使用下划线命名法: user_level_records
 * 字段名使用小驼峰命名法: userId, levelId, obtainedAt 等
 */
const UserLevelRecords = sequelize.define('user_level_records', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '记录ID，使用UUID'
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '用户ID，关联user表'
    },
    levelId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '对应的级别ID，关联user_levels表'
    },
    obtainedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '获得该职称的时间'
    },
    remarks: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注，如通过评审、调动等'
    },
    createdBy: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '记录创建人ID'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '记录创建时间'
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '记录更新时间'
    }
}, {
    // 明确指定表名
    tableName: 'user_level_records',
    // 不使用自动复数形式
    freezeTableName: true,
    // 启用时间戳
    timestamps: true,
    // 添加表注释
    comment: '用户获得职称记录表',
    // 添加索引
    indexes: [
        {
            name: 'idx_user_id',
            fields: ['userId']
        },
        {
            name: 'idx_level_id',
            fields: ['levelId']
        },
        {
            name: 'idx_obtained_at',
            fields: ['obtainedAt']
        }
    ]
});

// 设置模型关联
const setupAssociations = () => {
    const userModel = require('./userModel');
    const userLevelsModel = require('./userLevelsModel');

    // 职称记录属于一个用户
    UserLevelRecords.belongsTo(userModel, {
        foreignKey: 'userId',
        as: 'user'
    });

    // 职称记录属于一个职称级别
    UserLevelRecords.belongsTo(userLevelsModel, {
        foreignKey: 'levelId',
        as: 'level'
    });

    // 职称记录由某个用户创建
    UserLevelRecords.belongsTo(userModel, {
        foreignKey: 'createdBy',
        as: 'creator'
    });

    // 用户有多个职称记录
    userModel.hasMany(UserLevelRecords, {
        foreignKey: 'userId',
        as: 'levelRecords'
    });

    // 职称级别有多个记录
    userLevelsModel.hasMany(UserLevelRecords, {
        foreignKey: 'levelId',
        as: 'records'
    });
};

// 延迟设置关联以避免循环依赖
setTimeout(setupAssociations, 0);

module.exports = UserLevelRecords;
