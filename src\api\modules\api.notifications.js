import service from '../server'

// ============================================================================
// 通知管理 API
// ============================================================================

/**
 * 获取通知列表
 * @param {Object} params - 查询参数
 * @param {string} params.type - 通知类型
 * @param {string|number} params.is_read - 是否已读（0-未读，1-已读）
 * @param {string} params.user_id - 用户ID
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页条数
 * @returns {Promise} 通知列表
 */
export const getNotificationsList = (params) => {
  return service.get('/sys/notifications', { params }).then(response => {
    // 后端已经返回正确格式，直接适配分页数据结构
    if (response.code === 200 && response.data) {
      return {
        code: response.code,
        message: response.message,
        data: {
          list: response.data.list || [],
          total: response.data.pagination?.total || 0,
          page: response.data.pagination?.page || 1,
          pageSize: response.data.pagination?.pageSize || 10
        }
      }
    }
    // 如果格式不匹配，直接返回
    return response
  })
}

/**
 * 获取通知详情
 * @param {string|number} id - 通知ID
 * @returns {Promise} 通知详情
 */
export const getNotificationDetail = (id) => {
  return service.get(`/sys/notifications/${id}`)
}

/**
 * 创建通知（仅管理员可用，支持三种发送方式）
 * @param {Object} data - 通知数据
 * @param {string} data.type - 通知类型
 * @param {string} data.title - 通知标题
 * @param {string} data.content - 通知内容
 * @param {string} data.abstract - 通知摘要
 * @param {string} data.target_type - 发送目标类型：all/department/user_level
 * @param {string} data.department - 部门名称（按部门发送时使用）
 * @param {string} data.user_level - 用户级别（按用户级别发送时使用）
 * @param {string} data.initiator_id - 发起人ID
 * @returns {Promise} 创建结果
 */
export const createNotification = (data) => {
  return service.post('/sys/notifications', data).then(response => {
    // 适配后端返回格式
    return response
  })
}

/**
 * 发送通知（兼容旧版本API）
 * @param {Object} data - 通知数据
 * @returns {Promise} 发送结果
 */
export const sendNotification = (data) => {
  return createNotification(data)
}

/**
 * 更新通知
 * @param {string|number} id - 通知ID
 * @param {Object} data - 更新数据
 * @param {boolean} data.is_read - 是否已读（布尔值）
 * @returns {Promise} 更新结果
 */
export const updateNotification = (id, data) => {
  // 确保 is_read 是布尔值
  const updateData = { ...data }
  if (updateData.is_read !== undefined) {
    updateData.is_read = Boolean(updateData.is_read)
  }
  return service.put(`/sys/notifications/${id}`, updateData)
}

/**
 * 删除通知
 * @param {string|number} id - 通知ID
 * @returns {Promise} 删除结果
 */
export const deleteNotification = (id) => {
  return service.delete(`/sys/notifications/${id}`)
}

// ============================================================================
// 用户和部门管理 API
// ============================================================================

/**
 * 获取部门列表
 * @returns {Promise} 部门列表
 */
export const getDepartmentsList = () => {
  return service.get('/sys/notifications/departments').then(response => {
    // 适配后端返回格式
    return response
  })
}

/**
 * 获取用户级别列表
 * @returns {Promise} 用户级别列表
 */
export const getUserLevelsList = () => {
  return service.get('/sys/notifications/user-levels').then(response => {
    // 适配后端返回格式
    return response
  })
}

/**
 * 获取用户个人通知列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页条数
 * @param {string} params.type - 通知类型
 * @param {boolean} params.isRead - 是否已读
 * @returns {Promise} 用户通知列表
 */
export const getUserNotifications = (params = {}) => {
  // 构建查询字符串
  const queryParams = new URLSearchParams();
  if (params.page) queryParams.append('page', params.page);
  if (params.pageSize) queryParams.append('pageSize', params.pageSize);
  if (params.type) queryParams.append('type', params.type);
  if (params.isRead !== undefined) queryParams.append('isRead', params.isRead);

  const queryString = queryParams.toString();
  const url = `/common/notifications/user-notifications${queryString ? '?' + queryString : ''}`;

  console.log('通知API请求URL:', url);
  console.log('请求参数:', params);

  return service.get(url).then(response => {
    console.log('通知API原始响应:', response);

    // 适配后端返回格式
    if (response.status === 1 && response.data) {
      return {
        status: response.status,
        code: 200, // 兼容旧代码
        message: response.message,
        data: {
          list: response.data.list || [],
          total: response.data.pagination?.total || 0,
          page: response.data.pagination?.page || 1,
          pageSize: response.data.pagination?.pageSize || 10
        }
      }
    }
    return response
  })
}

/**
 * 标记通知为已读
 * @param {string} notificationId - 通知ID
 * @returns {Promise} 操作结果
 */
export const markNotificationAsRead = (notificationId) => {
  console.log('标记通知已读，ID:', notificationId);
  return service.post('/common/notifications/mark-read', { notificationId }).then(response => {
    console.log('标记已读API响应:', response);
    return response;
  })
}

/**
 * 获取未读通知数量
 * @returns {Promise} 未读通知数量
 */
export const getUnreadNotificationCount = () => {
  return service.get('/common/notifications/unread-count').then(response => {
    console.log('未读数量API响应:', response);
    return response;
  })
}

// ============================================================================
// 批量操作 API
// ============================================================================

/**
 * 批量标记通知为已读
 * @param {Array} notificationIds - 通知ID数组
 * @returns {Promise} 操作结果
 */
export const batchMarkAsRead = (notificationIds) => {
  return service.put('/sys/notifications/batch/read', {
    notification_ids: notificationIds
  })
}

// ============================================================================
// 便捷方法
// ============================================================================



/**
 * 发送部门通知
 * @param {Object} data - 通知数据
 * @param {string} data.title - 标题
 * @param {string} data.content - 内容
 * @param {string} data.department - 部门名称
 * @param {string} data.type - 通知类型，默认为'department'
 * @returns {Promise} 发送结果
 */
export const sendDepartmentNotification = (data) => {
  return createNotification({
    type: data.type || 'department',
    title: data.title,
    content: data.content,
    abstract: data.abstract || '',
    target_type: 'department',
    department: data.department,
    initiator_id: data.initiatorId
  })
}

/**
 * 发送用户级别通知
 * @param {Object} data - 通知数据
 * @param {string} data.title - 标题
 * @param {string} data.content - 内容
 * @param {string} data.userLevel - 用户级别
 * @param {string} data.type - 通知类型，默认为'level'
 * @returns {Promise} 发送结果
 */
export const sendUserLevelNotification = (data) => {
  return createNotification({
    type: data.type || 'level',
    title: data.title,
    content: data.content,
    abstract: data.abstract || '',
    target_type: 'user_level',
    user_level: data.userLevel,
    initiator_id: data.initiatorId
  })
}

/**
 * 发送全员通知
 * @param {Object} data - 通知数据
 * @param {string} data.title - 标题
 * @param {string} data.content - 内容
 * @param {string} data.type - 通知类型，默认为'announcement'
 * @returns {Promise} 发送结果
 */
export const sendAllUsersNotification = (data) => {
  return createNotification({
    type: data.type || 'announcement',
    title: data.title,
    content: data.content,
    abstract: data.abstract || '',
    target_type: 'all',
    initiator_id: data.initiatorId
  })
}

// ============================================================================
// 通知设置 API
// ============================================================================

/**
 * 获取用户通知设置
 * @param {string} userId - 用户ID
 * @returns {Promise} 用户通知设置
 */
export const getUserNotificationSettings = (userId) => {
  return service.get(`/sys/notifications/settings/${userId}`)
}

/**
 * 更新用户通知设置
 * @param {string} userId - 用户ID
 * @param {Object} settings - 通知设置数据
 * @returns {Promise} 更新结果
 */
export const updateUserNotificationSettings = (userId, settings) => {
  return service.put(`/sys/notifications/settings/${userId}`, settings)
}

// ============================================================================
// 通知模板 API
// ============================================================================

/**
 * 获取通知模板列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页条数
 * @param {string} params.type - 模板类型
 * @returns {Promise} 模板列表
 */
export const getNotificationTemplates = (params) => {
  return service.get('/sys/notifications/templates', { params }).then(response => {
    // 适配后端返回格式
    return response
  })
}

/**
 * 获取通知模板详情
 * @param {string|number} id - 模板ID
 * @returns {Promise} 模板详情
 */
export const getNotificationTemplate = (id) => {
  return service.get(`/sys/notifications/templates/${id}`).then(response => {
    return response
  })
}

/**
 * 创建通知模板
 * @param {Object} data - 模板数据
 * @param {string} data.name - 模板名称
 * @param {string} data.type - 通知类型
 * @param {string} data.title - 模板标题
 * @param {string} data.content - 模板内容
 * @param {string} data.description - 使用说明
 * @returns {Promise} 创建结果
 */
export const createNotificationTemplate = (data) => {
  return service.post('/sys/notifications/templates', data).then(response => {
    return response
  })
}

/**
 * 更新通知模板
 * @param {string|number} id - 模板ID
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
export const updateNotificationTemplate = (id, data) => {
  return service.put(`/sys/notifications/templates/${id}`, data).then(response => {
    return response
  })
}

/**
 * 删除通知模板
 * @param {string|number} id - 模板ID
 * @returns {Promise} 删除结果
 */
export const deleteNotificationTemplate = (id) => {
  return service.delete(`/sys/notifications/templates/${id}`).then(response => {
    return response
  })
}