<template>
  <div class="performance-container academic-appointments-container">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />

    <a-card title="学术任职管理" :bordered="false" class="performance-card">
      <template #extra>
        <a-space>
          <a-upload
            :customRequest="handleImport"
            :show-upload-list="false"
            :before-upload="beforeFileUpload"
          >
            <a-button type="primary" v-permission="'score:academicAppointments:admin:update'">
              <template #icon><UploadOutlined /></template>
              导入数据
            </a-button>
          </a-upload>
          <a-upload
            :customRequest="handleExcelToJsonConvert"
            :show-upload-list="false"
            :before-upload="beforeExcelUpload"
          >
            <a-button type="primary" v-permission="'score:academicAppointments:admin:update'">
              <template #icon><UploadOutlined /></template>
              Excel数据导入
            </a-button>
          </a-upload>
          <a-button type="primary" @click="showAddModal" v-permission="'score:academicAppointments:self:create'">
            <template #icon><PlusOutlined /></template>
            添加学术任职
          </a-button>
          <a-button :type="showPersonalAppointments ? 'default' : 'primary'" @click="togglePersonalAppointments" v-permission="'score:academicAppointments:admin'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalAppointments ? '查看全部学术任职' : '查看我的学术任职' }}
          </a-button>
        </a-space>
      </template>
        <!-- 学术任职填写说明区域 -->
        <a-card title="学术任职填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
          <a-alert
            class="mb-16"
            message="学术任职统计时间范围"
            :description="`统计时间：${timeRangeText || '加载中...'}`"
            type="info"
            show-icon
          />
          <div class="rule-content">
            <p><strong>填写说明：</strong></p>
            <ol class="detail-list">
              <li>学术协会仅按照国家级和省自治区的医学会和医学各专业的学会计算</li>
              <li>理事长和副理事长相对应的主任委员和副主任委员、会长和副会长按相同标准对待</li>
              <li>学会任职以最高级别仅计算一项</li>
              <li>按任职年度计算</li>
              <li>需提供带有聘任日期和聘任期限的聘书复印件供审核</li>
              <li>任职年限区间为统计时间范围</li>
            </ol>
          </div>
        </a-card>

      <!-- 统计概览卡片 -->
      <!-- <a-row :gutter="16" style="margin-bottom: 16px;">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="当前有效任职"
              :value="statisticsData.activeAppointments"
              :loading="statisticsLoading"
              style="text-align: center;"
            >
              <template #suffix>
                <span>个</span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="总任职数量"
              :value="statisticsData.totalAppointments"
              :loading="statisticsLoading"
              style="text-align: center;"
            >
              <template #suffix>
                <span>个</span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="平均得分"
              :value="statisticsData.averageScore"
              :precision="2"
              :loading="statisticsLoading"
              style="text-align: center;"
            >
              <template #suffix>
                <span>分</span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="审核完成率"
              :value="statisticsData.reviewCompletionRate"
              :precision="2"
              :loading="statisticsLoading"
              style="text-align: center;"
              suffix="%"
              :value-style="{ color: statisticsData.reviewCompletionRate > 80 ? '#3f8600' : '#cf1322' }"
            >
              <template #prefix>
                <a-progress :percent="statisticsData.reviewCompletionRate" size="small" :show-info="false" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row> -->

        <!-- 图表区域 -->
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="审核状态概览" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="reviewStatusChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('reviewStatus', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="reviewStatusChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="任职级别分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="levelChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('level', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="levelChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>
      
      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="24">
          <a-card title="年度新增任职趋势" :bordered="false">
            <template #extra>
              <a-space>
                <a-select
                  v-model:value="appointmentTrendChartRange"
                  style="width: 150px;"
                  @change="(value) => changeChartRange('appointmentTrend', value)"
                >
                  <a-select-option value="in">统计范围内</a-select-option>
                  <a-select-option value="out">统计范围外</a-select-option>
                  <a-select-option value="all">全部任职</a-select-option>
                </a-select>
              </a-space>
            </template>
            <div ref="appointmentTrendChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 学术任职排行 -->
      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="24">
          <a-card title="学术任职排行" :bordered="false">
            <template #extra>
              <a-space>
                <a-input-search
                  v-model:value="authorRankingPagination.nickname"
                  v-permission="'score:textbooks:admin:list'"
                  placeholder="用户名称"
                  style="width: 150px;"
                  @search="loadUserRanking"
                  @pressEnter="loadUserRanking"
                />
                <a-select
                  v-model:value="userRankChartRange"
                  style="width: 150px;"
                  @change="(value) => changeChartRange('userRank', value)"
                >
                  <a-select-option value="in">统计范围内</a-select-option>
                  <a-select-option value="out">统计范围外</a-select-option>
                  <a-select-option value="all">全部任职</a-select-option>
                </a-select>
                <a-button type="primary" @click="exportUserRanking" :loading="userRankExportLoading" 
                v-if="hasPerms('score:academicAppointments:admin:list')"
                >
                  <template #icon><DownloadOutlined /></template>
                  导出
                </a-button>
              </a-space>
            </template>
            <a-table
              :columns="userRankColumns"
              :data-source="userRankData"
              :pagination="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' ? userRankPagination : false"
              :loading="userRankLoading"
              rowKey="userId"
              @change="handleUserRankTableChange"
              :scroll="{ x: 800 }"
              :bordered="true"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'rank'">
                  <a-tag :color="getRankColor(index + 1)">{{ index + 1 }}</a-tag>
                </template>
                <template v-else-if="column.key === 'activeAppointments'">
                  <a-badge :count="record.activeAppointments" :number-style="{ backgroundColor: '#52c41a' }" />
                </template>
                <template v-else-if="column.key === 'details'">
                  <a-button type="link" @click="showUserAppointmentsDetails(record)"
                  v-if="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId"
                  >
                    查看详情
                  </a-button>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>

        <!-- 搜索表单 -->
        <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
          <a-form :model="searchForm" @finish="handleSearch" layout="vertical" class="performance-form">
            <a-row :gutter="[12, 8]">
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="协会/期刊名称" name="associationName">
                  <a-input
                    v-model:value="searchForm.associationName"
                    placeholder="请输入协会/期刊名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="职务级别" name="levelId">
                  <a-select
                    v-model:value="searchForm.levelId"
                    placeholder="请选择职务级别"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="level in levelOptions" :key="level.id" :value="level.id">
                      {{ level.levelName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="起始年份" name="startYear">
                  <a-input-number
                    v-model:value="searchForm.startYear"
                    placeholder="起始年份"
                    style="width: 100%"
                    :min="1900"
                    :max="2100"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="结束年份" name="endYear">
                  <a-input-number
                    v-model:value="searchForm.endYear"
                    placeholder="结束年份"
                    style="width: 100%"
                    :min="1900"
                    :max="2100"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="审核状态" name="reviewStatus">
                  <a-select
                    v-model:value="searchForm.reviewStatus"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部状态</a-select-option>
                    <a-select-option value="rejected">已拒绝</a-select-option>
                    <a-select-option value="pending">待审核</a-select-option>
                    <a-select-option value="reviewed">已审核</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="筛选范围" name="range">
                  <a-select
                    v-model:value="searchForm.range"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部范围</a-select-option>
                    <a-select-option value="in">统计范围内</a-select-option>
                    <a-select-option value="out">统计范围外</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
                <a-form-item label=" " style="margin-bottom: 0;">
                  <div class="search-actions-inline">
                    <a-button type="primary" html-type="submit" size="default">
                      <template #icon><SearchOutlined /></template>
                      搜索
                    </a-button>
                    <a-button @click="resetSearch" size="default">
                      <template #icon><ReloadOutlined /></template>
                      重置
                    </a-button>
                    <a-button type="default" @click="handleExport" :loading="exportLoading" size="default">
                      <template #icon><DownloadOutlined /></template>
                      导出
                    </a-button>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 数据表格 -->
        <div class="performance-table">
          <a-table
            :columns="columns"
            :data-source="dataSource"
            :pagination="pagination"
            :loading="isLoading"
            rowKey="id"
            @change="handleTableChange"
            :scroll="{ x: 1200 }"
            :bordered="true"
          >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'levelName'">
            <a-tag color="blue">{{ record.level?.levelName || '-' }}</a-tag>
          </template>
          <template v-else-if="column.key === 'score'">
            <span style="font-weight: bold; color: #1890ff;">
              {{ record.level?.score || 0 }}分
            </span>
          </template>
          <template v-else-if="column.key === 'yearRange'">
            {{ record.startYear }} {{ record.endYear ? `- ${record.endYear}` : "至今" }}
          </template>
          <template v-else-if="column.key === 'ifReviewer'">
            <a-tag :color="record.ifReviewer == true ? 'success' : (record.ifReviewer == false ? 'error' : 'warning')">
              {{ record.ifReviewer == 1 ? '已审核' : (record.ifReviewer == 0 ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'reviewComment'">
            <span style="font-weight: bold; color: #1890ff;">
              {{ record.reviewComment || '-' }}
            </span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown placement="bottomRight" :trigger="['click']">
              <template #overlay>
                <a-menu class="action-dropdown-menu">
                  <!-- 编辑选项 -->
                  <a-menu-item
                    key="edit"
                    v-if="record.ifReviewer != 1 && hasPerms(showPersonalAppointments ? 'score:academicAppointments:self:update' : 'score:academicAppointments:admin:update')"
                  >
                    <a @click="handleEdit(record)" class="action-menu-item">
                      <EditOutlined />
                      <span>编辑</span>
                    </a>
                  </a-menu-item>

                  <!-- 重新提交审核选项 -->
                  <a-menu-item
                    key="resubmit"
                    v-if="record.ifReviewer === false && hasPerms('score:academicAppointments:self:reapply')"
                  >
                    <a @click="handleResubmit(record)" class="action-menu-item">
                      <ReloadOutlined />
                      <span>重新提交审核</span>
                    </a>
                  </a-menu-item>

                  <!-- 审核选项 - 仅管理员视图显示 -->
                  <a-menu-item
                    key="review"
                    v-if="!showPersonalAppointments && !record.ifReviewer && hasPerms('score:academicAppointments:admin:review')"
                  >
                    <a @click="handleReview(record)" class="action-menu-item">
                      <AuditOutlined />
                      <span>审核</span>
                    </a>
                  </a-menu-item>

                  <a-menu-divider v-if="record.ifReviewer != 1 || (!showPersonalAppointments && !record.ifReviewer)" />

                  <!-- 删除选项 -->
                  <a-menu-item
                    key="delete"
                    v-if="hasPerms(showPersonalAppointments ? 'score:academicAppointments:self:delete' : 'score:academicAppointments:admin:delete')"
                  >
                    <a @click="confirmDelete(record)" class="action-menu-item text-danger">
                      <DeleteOutlined />
                      <span>删除</span>
                    </a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small" class="action-trigger-btn">
                操作
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
          </a-table>
        </div>

        <!-- 表格底部统计信息 -->
        <div class="table-footer">
          <div class="total-score">
            <span>总得分：{{ typeof totalScore === 'number' ? totalScore.toFixed(2) : '0.00' }}分</span>
          </div>
        </div>
      </a-card>

    <!-- 教师任职详情弹窗 -->
    <a-modal
      v-model:visible="userDetailsVisible"
      :title="`${'教师'}的学术任职详情`"
      width="900px"
      :footer="null"
      :autofocus="false"
      :focusTriggerAfterClose="false"
    >
      <a-table
        :columns="userAppointmentDetailColumns"
        :data-source="userAppointmentDetails"
        :pagination="userDetailsPagination"
        :loading="userDetailsLoading"
        rowKey="id"
        :scroll="{ x: 800 }"
        :bordered="true"
        @change="handleUserDetailsPaginationChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'levelName'">
            <a-tag color="blue">{{ record.level?.levelName || '-' }}</a-tag>
          </template>
          <template v-else-if="column.key === 'score'">
            <span style="font-weight: bold; color: #1890ff;">
              {{ record.level?.score || 0 }}分
            </span>
          </template>
          <template v-else-if="column.key === 'yearRange'">
            {{ record.startYear }} {{ record.endYear ? `- ${record.endYear}` : "至今" }}
          </template>
          <template v-else-if="column.key === 'ifReviewer'">
            <a-tag :color="record.ifReviewer === true ? 'success' : (record.ifReviewer === false ? 'error' : 'warning')">
              {{ record.ifReviewer === true ? '已审核' : (record.ifReviewer === false ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'associationName'">
            <a-tooltip :title="record.associationName" placement="topLeft">
              <span>{{ record.associationName }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'position'">
            <a-tooltip :title="record.position" placement="topLeft">
              <span>{{ record.position }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'remark'">
            <a-tooltip :title="record.remark || '-'" placement="topLeft">
              <span>{{ record.remark || '-' }}</span>
            </a-tooltip>
          </template>
        </template>
      </a-table>
      <div style="margin-top: 16px; text-align: right; font-weight: bold;">
        总得分: {{ typeof userDetailsTotalScore === 'number' ? userDetailsTotalScore.toFixed(2) : '0.00' }}分
      </div>
    </a-modal>

    <!-- 添加学术任职模态框 -->
    <ZyModal
      :show="modalVisible"
      :title="isEdit ? '编辑学术任职' : '添加学术任职'"
      :min-width="600"
      :min-height="400"
      @close="handleModalCancel"
    >
      <div class="appointment-form">
        <a-form 
          ref="formRef" 
          :model="formState" 
          :rules="rules" 
          :label-col="{ span: 6 }" 
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item name="userId" label="教师">
            <a-select
              v-model:value="formState.userId"
              placeholder="请选择教师"
              style="width: 100%;"
              :filter-option="false"
              show-search
              allow-clear
              :loading="userSearchLoading"
              @search="handleUserSearch"
              :not-found-content="userSearchLoading ? undefined : '未找到匹配结果'"
            >
              <a-select-option v-for="user in userOptions" :key="user.id" :value="user.id">
                {{ user.nickname || user.username }} ({{ user.studentNumber || '无工号' }})
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item name="associationName" label="协会/期刊名称">
            <a-input v-model:value="formState.associationName" placeholder="请输入协会/期刊名称" />
          </a-form-item>
          
          <a-form-item name="position" label="职务名称">
            <a-input v-model:value="formState.position" placeholder="请输入职务名称" />
          </a-form-item>
          
          <a-form-item name="levelId" label="级别">
            <a-select
              v-model:value="formState.levelId"
              placeholder="请选择职务级别"
              style="width: 100%;"
            >
              <a-select-option v-for="level in levelOptions" :key="level.id" :value="level.id">
                {{ level.levelName }} ({{ level.score }}分)
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item name="startYear" label="起始年份">
            <a-input-number 
              v-model:value="formState.startYear" 
              style="width: 100%;" 
              :min="1900"
              :max="2100"
              placeholder="请输入起始年份"
            />
          </a-form-item>

          <a-form-item name="endYear" label="结束年份">
            <a-input-number 
              v-model:value="formState.endYear" 
              style="width: 100%;" 
              :min="formState.startYear || 1900"
              :max="2100"
              placeholder="请输入结束年份（不填为至今）"
            />
          </a-form-item>

          <a-form-item name="remark" label="备注">
            <a-textarea 
              v-model:value="formState.remark" 
              placeholder="请输入备注信息" 
              :rows="3" 
            />
          </a-form-item>

          <!-- 修改文件上传表单项，使用更好的样式 -->
          <a-form-item label="附件" name="fileList">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
              <a-upload
                v-model:file-list="fileList"
                :customRequest="handleFileUpload"
                :before-upload="beforeUpload"
                multiple
                :show-upload-list="false"
              >
                <a-button type="primary">
                  <template #icon><UploadOutlined /></template>
                  选择文件
                </a-button>
              </a-upload>
              <span style="margin-left: 16px; color: #666; font-size: 12px;">
                支持上传文档、图片或压缩文件，单个文件不超过10MB
              </span>
            </div>
            
            <!-- 已上传的文件列表 -->
            <a-table
              :columns="fileColumns"
              :data-source="fileList"
              :pagination="false"
              size="small"
              style="margin-top: 8px;"
              rowKey="uid"
              v-if="fileList.length > 0"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fileName'">
                  <span :title="record.name">{{ record.name }}</span>
                </template>
                <template v-if="column.key === 'fileSize'">
                  {{ formatFileSize(record.size) }}
                </template>
                <template v-if="column.key === 'status'">
                  <div v-if="record.status === 'uploading'">
                    <a-progress :percent="record.percent || 0" size="small" />
                  </div>
                  <a-tag v-else :color="record.status === 'done' ? 'success' : (record.status === 'error' ? 'error' : 'processing')">
                    {{ record.status === 'done' ? '已上传' : (record.status === 'error' ? '上传失败' : '上传中') }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="previewFile(record)" v-if="record.status === 'done'">
                      <template #icon><EyeOutlined /></template>
                      预览
                    </a-button>
                    <a-button type="link" size="small" @click="downloadFile(record)" v-if="record.status === 'done'">
                      <template #icon><DownloadOutlined /></template>
                      下载
                    </a-button>
                    <a-popconfirm
                      title="确定要删除该文件吗？"
                      @confirm="confirmDeleteFile(record)"
                      okText="确认"
                      cancelText="取消"
                    >
                      <a-button type="link" danger size="small">
                        <template #icon><DeleteOutlined /></template>
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
            
            <!-- 显示已有的文件 -->
            <a-divider v-if="fileList.length > 0 && existingFileList.length > 0" style="margin: 12px 0" />
            
            <a-table
              :columns="fileColumns"
              :data-source="existingFileList"
              :pagination="false"
              size="small"
              style="margin-top: 8px;"
              rowKey="id"
              v-if="existingFileList.length > 0"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fileName'">
                  <span :title="record.originalName">{{ record.originalName || record.fileName }}</span>
                </template>
                <template v-if="column.key === 'fileSize'">
                  {{ formatFileSize(record.size) }}
                </template>
                <template v-if="column.key === 'status'">
                  <a-tag color="success">已上传</a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="previewFile(record)">
                      <template #icon><EyeOutlined /></template>
                      预览
                    </a-button>
                    <a-button type="link" size="small" @click="downloadFile(record)">
                      <template #icon><DownloadOutlined /></template>
                      下载
                    </a-button>
                    <a-popconfirm
                      title="确定要删除该文件吗？"
                      @confirm="() => removeExistingFile(record.id)"
                      okText="确认"
                      cancelText="取消"
                    >
                      <a-button type="link" danger size="small">
                        <template #icon><DeleteOutlined /></template>
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-form-item>
          <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
            <a-space>
              <a-button type="primary" @click="handleModalOk" :loading="confirmLoading">
                提交
              </a-button>
              <a-button @click="handleModalCancel">
                取消
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </ZyModal>

    <!-- 文件预览弹窗 -->
    <a-modal
      :visible="previewVisible"
      :title="previewTitle"
      :footer="null"
      @cancel="previewVisible = false"
    >
      <img alt="预览图片" style="width: 100%" :src="previewImage" />
    </a-modal>

    <!-- 添加ProjectReviewModal组件 -->
    <ProjectReviewModal
      :visible="reviewModalVisible"
      :record="currentReviewRecord"
      :attachments="reviewAttachments"
      @cancel="reviewModalVisible = false"
      @submit="handleReviewSubmit"
    />

    <!-- 添加Excel导入预览模态框 -->
    <a-modal
      v-model:visible="importPreviewVisible"
      title="Excel导入预览"
      width="1000px"
      :footer="null"
      :maskClosable="false"
    >
      <div v-if="importInProgress">
        <a-spin tip="导入中...">
          <div>
            <p>已成功导入: {{ importResults.success }} 条</p>
            <p>导入失败: {{ importResults.failed }} 条</p>
          </div>
        </a-spin>
      </div>
      <div v-else-if="convertingExcel">
        <a-spin tip="正在解析Excel文件..."></a-spin>
      </div>
      <div v-else>
        <div class="import-header">
          <div>
            <a-alert
              v-if="importPreviewData.length > 0"
              message="请确认数据无误后进行导入"
              type="info"
              show-icon
              style="margin-bottom: 16px"
            />
            <a-alert
              v-if="userIdCheckError"
              message="存在无法匹配的用户ID"
              description="部分记录的人事编号无法在系统中找到对应用户，导入时将跳过这些记录"
              type="warning"
              show-icon
              style="margin-bottom: 16px"
            />
          </div>
          <div class="action-buttons">
            <a-button 
              type="primary" 
              @click="handleStartImport" 
              :disabled="importPreviewData.length === 0"
              :loading="importInProgress"
            >
              开始导入
            </a-button>
            <a-button @click="importPreviewVisible = false" style="margin-left: 8px">
              取消
            </a-button>
            <a-button 
              type="link" 
              @click="handleDownloadJson" 
              :disabled="importPreviewData.length === 0"
              style="margin-left: 8px"
            >
              下载JSON
            </a-button>
          </div>
        </div>
        
        <a-table
          v-if="importPreviewData.length > 0"
          :columns="importPreviewColumns"
          :dataSource="importPreviewData"
          :scroll="{ x: 1200, y: 400 }"
          :pagination="{ pageSize: 10 }"
          size="small"
          bordered
          rowKey="index"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.key === 'userIdCheck'">
              <a-tag :color="record.userIdCheckStatus ? 'success' : 'error'">
                {{ record.userIdCheckStatus ? '已匹配' : '未匹配' }}
              </a-tag>
            </template>
          </template>
        </a-table>
        <div v-else class="empty-data">
          <a-empty description="暂无预览数据" />
        </div>
      </div>
    </a-modal>

    <!-- 导入结果模态框 -->
    <a-modal
      v-model:visible="importResultVisible"
      title="导入结果"
      :closable="true"
      :footer="null"
    >
      <a-result
        :status="importResults.success > 0 ? 'success' : 'warning'"
        :title="`导入完成: 成功 ${importResults.success} 条, 失败 ${importResults.failed} 条`"
        :sub-title="importResults.failedRecords.length > 0 ? '点击按钮导出失败记录' : ''"
      />
      <div style="text-align: center">
        <a-button @click="importResultVisible = false">关闭</a-button>
        <a-button 
          type="primary" 
          @click="exportFailedRecords"
          :disabled="importResults.failedRecords.length === 0"
          style="margin-left: 8px"
        >
          导出失败记录
        </a-button>
      </div>
    </a-modal>
  </div>
</template> 

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch, h } from 'vue'
import { UploadOutlined, DownloadOutlined, PlusOutlined, UserOutlined, SearchOutlined, ReloadOutlined, EyeOutlined, QuestionCircleOutlined, DeleteOutlined, EditOutlined, AuditOutlined, DownOutlined } from '@ant-design/icons-vue'
import { message, Modal, Radio, Input } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import ZyModal from '@/components/common/ZyModal.vue'
import useUserId from '@/composables/useUserId'
import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole } = useUserRole();
import { debounce } from 'lodash'
import {
  getAppointments,
  getAppointmentDetail,
  addAppointment,
  updateAppointment,
  deleteAppointment,
  importAppointments,
  reviewAppointment,
  getAppointmentLevelDistribution,
  getAppointmentYearlyTrend,
  getReviewStatusOverview,
  getTeacherAppointmentRanking,
  getAppointmentStatistics,
  reapplyReview
} from '@/api/modules/api.academicAppointments'
import { getAssociationLevels } from '@/api/modules/api.associationLevels'
import { usersSearch } from '@/api/modules/api.users'
import ProjectReviewModal from '@/components/review/ProjectReviewModal.vue'
import { uploadFile as utilUploadFile, previewFileById, downloadFileById, deleteFileById } from '@/utils/others'
import { uploadFiles } from '@/api/modules/api.file'
import * as XLSX from 'xlsx'
import { excelToAcademicAppointmentsJson } from '@/utils/fileUtils'
import { downloadJson } from '@/utils/fileUtils'
import { hasPerms } from '@/libs/util.common';
import { getScoreTimeRange } from '@/api/modules/api.home';
// 图表引用
const levelChartRef = ref(null)
const reviewStatusChartRef = ref(null)
const appointmentTrendChartRef = ref(null)

// 图表实例
const levelChart = ref(null)
const reviewStatusChart = ref(null)
const appointmentTrendChart = ref(null)

// 添加时间范围文本
const timeRangeText = ref('');

// 表格列定义
const columns = [
  {
    title: '协会/期刊名称',
    dataIndex: 'associationName',
    key: 'associationName',
    width: '18%',
    ellipsis: true,
  },
  {
    title: '职务名称',
    dataIndex: 'position',
    key: 'position',
    width: '13%',
    ellipsis: true,
  },
  {
    title: '级别',
    dataIndex: 'levelName',
    key: 'levelName',
    width: '15%',
  },
  {
    title: '年份',
    key: 'yearRange',
    width: '10%',
  },
  {
    title: '教师',
    dataIndex: ['user', 'nickname'],
    key: 'user',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '分数',
    key: 'score',
    width: '10%',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: '15%',
    ellipsis: true,
  },
  {
    title: '审核状态',
    key: 'ifReviewer',
    width: '10%',
  },
  {
    title: '审核建议',
    dataIndex: 'reviewComment',
    key: 'reviewComment',
    width: '15%',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: '13%',
    fixed: 'right',
    align: 'center'
  },
]

// 数据源
const dataSource = ref([])
const isLoading = ref(false)
const levelOptions = ref([])

// 统计数据
const statisticsData = reactive({
  activeAppointments: 0,
  totalAppointments: 0,
  averageScore: 0,
  reviewCompletionRate: 0
})
const statisticsLoading = ref(false)

// 教师排名列
const userRankColumns = [
  {
    title: '排名',
    key: 'rank',
    width: '80px',
    align: 'center',
  },
  {
    title: '教师',
    dataIndex: 'userName',
    key: 'userName',
    width: '150px',
  },
  {
    title: '工号',
    dataIndex: 'studentNumber',
    key: 'studentNumber',
    width: '120px',
  },
  {
    title: '总任职数',
    dataIndex: 'totalAppointments',
    key: 'totalAppointments',
    width: '120px',
    align: 'center',
    sorter: true,
  },
  {
    title: '总得分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: '120px',
    align: 'center',
    sorter: true,
  },
  {
    title: '操作',
    key: 'details',
    width: '100px',
    align: 'center',
    fixed: 'right',
  },
]

// 教师任职详情列
const userAppointmentDetailColumns = [
  {
    title: '协会/期刊名称',
    dataIndex: 'associationName',
    key: 'associationName',
    width: '22%',
    ellipsis: true,
  },
  {
    title: '职务名称',
    dataIndex: 'position',
    key: 'position',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '级别',
    key: 'levelName',
    width: '18%',
  },
  {
    title: '年份',
    key: 'yearRange',
    width: '12%',
  },
  {
    title: '分数',
    key: 'score',
    width: '8%',
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: '10%',
    ellipsis: true,
  },
]

// 教师任职数据
const userRankData = ref([])
const userRankLoading = ref(false)
const userRankPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条`
})
const userAppointmentDetails = ref([])
const userDetailsVisible = ref(false)
const userDetailsLoading = ref(false)
const selectedUserDetailName = ref('')
const userDetailsTotalScore = ref(0)

// 搜索表单
const searchForm = reactive({
  associationName: '',
  levelId: undefined,
  startYear: undefined,
  endYear: undefined,
  reviewStatus: 'reviewed',
  range: 'in'
})

// 重置搜索条件
const resetSearch = () => {
  searchForm.associationName = ''
  searchForm.levelId = undefined
  searchForm.startYear = undefined
  searchForm.endYear = undefined
  searchForm.reviewStatus = 'reviewed'
  searchForm.range = 'in'
  handleSearch()
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1 // 重置为第一页
  fetchData()
}

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)

// 表单引用
const formRef = ref(null)

// 表单数据
const formState = reactive({
  userId: '',
  associationName: '',
  position: '',
  levelId: '',
  startYear: undefined,
  endYear: undefined,
  remark: '',
  fileList: []
})

// 文件相关状态
const fileList = ref([])
const previewVisible = ref(false)
const previewImage = ref('')
const previewTitle = ref('')

// 文件上传前检查
const beforeFileUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  
  // 防止重复上传相同文件
  const isDuplicate = fileList.value.some(item => item.name === file.name)
  if (isDuplicate) {
    message.error('已存在同名文件!')
    return false
  }
  
  return true
}


// 添加在其他状态变量附近
const currentRole = ref('');
const currentUserId = ref('');

// 是否显示个人学术任职
const showPersonalAppointments = ref(false)

// 使用useUserId composable
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()

// 添加错误状态
const errorMessage = ref('')

// 用户统计相关变量
const userScoreData = ref([])
const userScoreLoading = ref(false)
const userScoreColumns = [
  {
    title: '排名',
    dataIndex: 'rank',
    key: 'rank',
    width: 80,
    align: 'center'
  },
  {
    title: '用户昵称',
    dataIndex: 'nickname',
    key: 'nickname',
    width: 120
  },
  {
    title: '总得分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    key: 'details',
    width: 100,
    align: 'center'
  }
]
const userScorePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
})
const userScoreSearchParams = reactive({
  nickname: '',
  range: 'in',
  page: 1,
  pageSize: 10
})
const userScoreChartRange = ref('in')
const exporting = ref(false)

// 用户统计相关函数
const fetchAllUsersTotalScore = async () => {
  try {
    userScoreLoading.value = true
    // 这里应该调用相应的API，暂时使用空数组
    userScoreData.value = []
    userScorePagination.total = 0
    console.log('获取用户统计数据功能待实现')
  } catch (error) {
    console.error('获取用户统计数据失败:', error)
    message.error('获取用户统计数据失败')
  } finally {
    userScoreLoading.value = false
  }
}

const handleUserScoreRangeChange = (value) => {
  userScoreChartRange.value = value
  userScoreSearchParams.range = value
  fetchAllUsersTotalScore()
}

const resetUserScoreSearch = () => {
  userScoreSearchParams.nickname = ''
  userScoreSearchParams.range = 'in'
  userScoreSearchParams.page = 1
  fetchAllUsersTotalScore()
}

const exportUserScoreData = async () => {
  try {
    exporting.value = true
    message.info('导出功能待实现')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const handleUserScoreTableChange = (pagination) => {
  // 更新搜索参数
  userScoreSearchParams.page = pagination.current
  userScoreSearchParams.pageSize = pagination.pageSize
  // 更新分页配置，保持其他配置不变
  userScorePagination.current = pagination.current
  userScorePagination.pageSize = pagination.pageSize
  userScorePagination.showSizeChanger = true
  userScorePagination.pageSizeOptions = ['10', '20', '50']
  userScorePagination.showTotal = total => `共 ${total} 条`
  fetchAllUsersTotalScore()
}

const showUserScoreDetails = (record) => {
  console.log('查看用户详情:', record)
  message.info('查看详情功能待实现')
}


// 添加请求锁和追踪系统，在chartsInitialized之后添加
const chartsInitialized = ref(false)

// 全局请求追踪对象
const requestTracker = reactive({
  appointments: false,
  statistics: false,
  levelDistribution: false,
  reviewStatusOverview: false,
  yearlyTrend: false,
  userRanking: false,
  // 请求时间戳用于判断是否重复请求
  timestamps: {},
  // 最小请求间隔(毫秒)
  minInterval: 500
})

// 帮助函数：检查是否允许发起请求
const canRequest = (key) => {
  // 如果请求已锁定，不允许再次请求
  if (requestTracker[key]) return false
  
  // 检查是否在最小间隔内
  const now = Date.now()
  const lastTime = requestTracker.timestamps[key] || 0
  if (now - lastTime < requestTracker.minInterval) return false
  
  // 锁定请求，记录时间戳
  requestTracker[key] = true
  requestTracker.timestamps[key] = now
  return true
}

// 帮助函数：释放请求锁
const releaseRequest = (key) => {
  requestTracker[key] = false
}

// 总分计算
const totalScore = computed(() => {
  // 计算表格中所有记录的总分
  const sum = dataSource.value.reduce((acc, item) => {
    return acc + (parseFloat(item.level?.score) || 0)
  }, 0)
  return sum
})

// 审核状态筛选
// const reviewStatus = ref('reviewed')

// 处理审核状态筛选
// const handleReviewStatusChange = (status) => {
//   reviewStatus.value = status
//   fetchData()
// }

// 表格数据范围筛选
// const tableDataRange = ref('in')

// 处理表格数据范围筛选
// const handleTableRangeChange = (range) => {
//   tableDataRange.value = range
//   pagination.current = 1
//   fetchData()
// }

// 监听showPersonalAppointments变化，更新图表
watch(showPersonalAppointments, () => {
  fetchStatistics()
  nextTick(() => {
    initCharts()
  })
})

// 统计图表范围筛选
const levelChartRange = ref('in')
const reviewStatusChartRange = ref('in')
const appointmentTrendChartRange = ref('in')
const userRankChartRange = ref('in')

// 图表审核状态筛选
const levelChartReviewStatus = ref('reviewed')
const appointmentTrendChartReviewStatus = ref('reviewed')
const userRankChartReviewStatus = ref('reviewed')

// 处理统计图表范围筛选
const changeChartRange = async (type, range) => {
  try {
    // 更新相应图表的范围状态
    if (type === 'level') {
      levelChartRange.value = range
      chartsInitialized.value = false // 重置图表初始化状态
      await loadLevelDistribution()
    } else if (type === 'reviewStatus') {
      reviewStatusChartRange.value = range
      chartsInitialized.value = false // 重置图表初始化状态
      await loadReviewStatusOverview()
    } else if (type === 'appointmentTrend') {
      appointmentTrendChartRange.value = range
      chartsInitialized.value = false // 重置图表初始化状态
      await loadYearlyTrend()
    } else if (type === 'userRank') {
      userRankChartRange.value = range
      chartsInitialized.value = false // 重置图表初始化状态
      await loadUserRanking()
    }
  } catch (error) {
    console.error(`更新${type}图表出错:`, error)
    message.error('更新图表数据失败')
  }
}

// 添加用于跟踪定时器的集合
const timers = ref([]);

// 修改onMounted钩子，进行更严格的控制
onMounted(async () => {
  console.log('组件初始化开始')

  try {
    // 获取当前用户角色
    try {
      currentRole.value = await getUserRole();
      console.log("当前用户角色:", currentRole.value);
      console.log("roleAuth===", currentRole.value ? currentRole.value.roleAuth : 'undefined');
    } catch (error) {
      console.error("获取用户角色失败:", error);
      message.error("获取用户角色信息失败，某些功能可能受限");
    }

    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true);
      console.log("当前用户ID:", currentUserId.value);
    } catch (error) {
      console.error("获取用户ID失败:", error);
    }

    // 如果是教师角色，默认显示个人学术任职
    if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
      showPersonalAppointments.value = true;
      console.log('用户为教师角色，默认显示个人学术任职');
    }

    // 第二步：获取学术任职列表，这是最主要的数据
    await fetchData(true) // 传true表示这是初始化加载

    // 第三步：获取级别数据（这个通常很小，不会有性能问题）
    await fetchLevels()

    // 其他数据将在fetchData成功后通过回调加载
  } catch (error) {
    console.error('组件初始化错误:', error)
    message.error('初始化失败，请刷新页面重试')
  }
  
  // 监听窗口大小变化，调整图表大小
  const resizeHandler = debounce(() => {
    try {
      if (levelChart.value) levelChart.value.resize()
      if (reviewStatusChart.value) reviewStatusChart.value.resize()
      if (appointmentTrendChart.value) appointmentTrendChart.value.resize()
    } catch (error) {
      console.error('调整图表大小出错:', error)
    }
  }, 200);
  
  window.addEventListener('resize', resizeHandler);
  
  // 存储事件监听器引用，以便在组件卸载时移除
  window._academicAppointmentsResizeHandler = resizeHandler;

  // 在mounted钩子中调用获取时间范围
  getTimeRange();
})

// 添加组件卸载前钩子函数
onBeforeUnmount(() => {
  console.log('组件准备卸载，清理资源');
  
  // 清理echarts实例
  try {
    if (levelChart.value) {
      levelChart.value.dispose();
      levelChart.value = null;
    }

    if (reviewStatusChart.value) {
      reviewStatusChart.value.dispose();
      reviewStatusChart.value = null;
    }

    if (appointmentTrendChart.value) {
      appointmentTrendChart.value.dispose();
      appointmentTrendChart.value = null;
    }
  } catch (error) {
    console.error('清理echarts实例出错:', error);
  }
  
  // 移除窗口事件监听器
  if (window._academicAppointmentsResizeHandler) {
    window.removeEventListener('resize', window._academicAppointmentsResizeHandler);
    window._academicAppointmentsResizeHandler = null;
  }
  
  // 清除所有定时器
  if (userSearchTimeout.value) {
    clearTimeout(userSearchTimeout.value);
    userSearchTimeout.value = null;
  }
  
  // 重置请求状态
  Object.keys(requestTracker).forEach(key => {
    if (key !== 'timestamps' && key !== 'minInterval') {
      requestTracker[key] = false;
    }
  });
  
  // 清理可能存在的其他定时器
  if (timers.value.length > 0) {
    timers.value.forEach(timer => {
      if (timer) clearTimeout(timer);
    });
    timers.value = [];
  }
  
  console.log('组件资源清理完成');
});

// 修改setTimeout的调用，保存定时器ID以便清理
const safeSetTimeout = (callback, delay) => {
  const timer = setTimeout(() => {
    const index = timers.value.indexOf(timer);
    if (index > -1) {
      timers.value.splice(index, 1);
    }
    callback();
  }, delay);
  
  timers.value.push(timer);
  return timer;
};

// 修改togglePersonalAppointments函数，避免触发多次加载
const togglePersonalAppointments = () => {
  showPersonalAppointments.value = !showPersonalAppointments.value
  chartsInitialized.value = false
  
  // 重置所有请求锁
  Object.keys(requestTracker).forEach(key => {
    if (key !== 'timestamps' && key !== 'minInterval') {
      requestTracker[key] = false
    }
  })
  
  // 使用安全的setTimeout
  safeSetTimeout(() => {
    // 只调用fetchData，让它控制其他数据的加载
    fetchData(true)
  }, 50)
}

// 修改loadStatisticsData函数，使用安全的setTimeout
const loadStatisticsData = async () => {
  // 如果请求已锁定，跳过
  if (!canRequest('statistics')) {
    console.log('跳过重复的statistics请求')
    return
  }
  
  statisticsLoading.value = true
  
  try {
    // 构建参数
    const params = {
      _t: Date.now() // 添加时间戳避免缓存
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalAppointments.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    // 调用API
    const response = await getAppointmentStatistics(params)
    
    if (response?.code === 200) {
      statisticsData.activeAppointments = response.data?.activeAppointments || 0
      statisticsData.totalAppointments = response.data?.totalAppointments || 0
      statisticsData.averageScore = response.data?.averageScore || 0
      // 审核完成率将由reviewStatusChart更新
    } else {
      console.error('获取统计数据失败:', response)
    }
  } catch (error) {
    console.error('获取统计数据出错:', error)
  } finally {
    statisticsLoading.value = false
    releaseRequest('statistics')
  }
}

// 修改sequentialLoadCharts函数
const sequentialLoadCharts = async () => {
  try {
    // 确保DOM元素存在
    await nextTick()
    
    // 顺序加载三个图表
    if (levelChartRef.value) {
      await loadLevelDistribution() 
      await new Promise(resolve => safeSetTimeout(resolve, 200)) // 使用安全的setTimeout
    }
    
    if (reviewStatusChartRef.value) {
      await loadReviewStatusOverview()
      await new Promise(resolve => safeSetTimeout(resolve, 200)) // 使用安全的setTimeout
    }
    
    if (appointmentTrendChartRef.value) {
      await loadYearlyTrend()
    }
    
    // 标记图表已初始化
    chartsInitialized.value = true
  } catch (error) {
    console.error('顺序加载图表出错:', error)
  }
}

// 新增：加载级别分布
const loadLevelDistribution = async () => {
  // 如果请求已锁定，跳过
  if (!canRequest('levelDistribution')) {
    console.log('跳过重复的levelDistribution请求')
    return
  }
  
  try {
    // 如果图表实例已存在，则销毁
    if (levelChart.value) {
      levelChart.value.dispose()
    }

    // 初始化echarts实例
    levelChart.value = echarts.init(levelChartRef.value)
    levelChart.value.showLoading()
    
    // 构建请求参数
    const params = {
      range: levelChartRange.value,
      reviewStatus: levelChartReviewStatus.value !== 'all' ? levelChartReviewStatus.value : undefined,
      _t: Date.now() // 添加时间戳避免缓存
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalAppointments.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    // 调用API获取数据
    const response = await getAppointmentLevelDistribution(params)
    
    levelChart.value.hideLoading()

    if (response?.code === 200) {
      const data = response.data

      // 设置图表配置
      levelChart.value.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '职务级别',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      })
    } else {
      console.error('获取任职级别分布数据失败:', response)
    }
  } catch (error) {
    console.error('初始化任职级别分布图表失败:', error)
  } finally {
    releaseRequest('levelDistribution')
  }
}

// 新增：加载审核状态概览
const loadReviewStatusOverview = async () => {
  // 如果请求已锁定，跳过
  if (!canRequest('reviewStatusOverview')) {
    console.log('跳过重复的reviewStatusOverview请求')
    return
  }
  
  try {
    // 如果图表实例已存在，则销毁
    if (reviewStatusChart.value) {
      reviewStatusChart.value.dispose()
    }

    // 初始化echarts实例
    reviewStatusChart.value = echarts.init(reviewStatusChartRef.value)
    reviewStatusChart.value.showLoading()
    
    // 构建请求参数
    const params = {
      range: reviewStatusChartRange.value,
      _t: Date.now() // 添加时间戳避免缓存
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalAppointments.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    // 调用API获取数据
    const response = await getReviewStatusOverview(params)
    
    reviewStatusChart.value.hideLoading()

    if (response?.code === 200) {
      const data = response.data

      // 设置图表配置 - 修改为使用approved, pending, rejected
      reviewStatusChart.value.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          data: ['已审核', '待审核', '已拒绝'],
          orient: 'vertical',
          left: 10,
          bottom: 10
        },
        series: [
          {
            name: '审核状态',
            type: 'pie',
            radius: '50%',
            data: [
              { value: data.reviewed, name: '已审核', itemStyle: { color: '#52c41a' } },
              { value: data.pending, name: '待审核', itemStyle: { color: '#faad14' } },
              { value: data.rejected, name: '已拒绝', itemStyle: { color: '#f5222d' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      })
      
      // 更新状态概览数据
      statisticsData.reviewCompletionRate = data.reviewedRate || 0
    } else {
      console.error('获取审核状态概览数据失败:', response)
    }
  } catch (error) {
    console.error('初始化审核状态概览图表失败:', error)
  } finally {
    releaseRequest('reviewStatusOverview')
  }
}

const authorRankingPagination = reactive({
  nickname: ''
});

// 新增：加载年度趋势
const loadYearlyTrend = async () => {
  // 如果请求已锁定，跳过
  if (!canRequest('yearlyTrend')) {
    console.log('跳过重复的yearlyTrend请求')
    return
  }
  
  try {
    // 如果图表实例已存在，则销毁
    if (appointmentTrendChart.value) {
      appointmentTrendChart.value.dispose()
    }

    // 初始化echarts实例
    appointmentTrendChart.value = echarts.init(appointmentTrendChartRef.value)
    appointmentTrendChart.value.showLoading()
    
    // 构建请求参数
    const params = {
      range: appointmentTrendChartRange.value,
      reviewStatus: appointmentTrendChartReviewStatus.value !== 'all' ? appointmentTrendChartReviewStatus.value : undefined,
      _t: Date.now() // 添加时间戳避免缓存
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalAppointments.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    // 调用API获取数据
    const response = await getAppointmentYearlyTrend(params)
    
    appointmentTrendChart.value.hideLoading()

    if (response?.code === 200) {
      const data = response.data

      // 设置图表配置
      appointmentTrendChart.value.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['新增任职数', '累计总数'],
          top: 'top'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.years,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '新增任职数',
            position: 'left'
          },
          {
            type: 'value',
            name: '累计总数',
            position: 'right'
          }
        ],
        series: [
          {
            name: '新增任职数',
            type: 'bar',
            data: data.newAppointments,
            itemStyle: {
              color: '#1890ff'
            }
          },
          {
            name: '累计总数',
            type: 'line',
            yAxisIndex: 1,
            data: data.cumulativeAppointments,
            itemStyle: {
              color: '#52c41a'
            }
          }
        ]
      })
    } else {
      console.error('获取年度任职趋势数据失败:', response)
    }
  } catch (error) {
    console.error('初始化年度任职趋势图表失败:', error)
  } finally {
    releaseRequest('yearlyTrend')
  }
}

// 修改：加载用户排名
const loadUserRanking = async () => {
  // 如果请求已锁定或在个人视图，跳过
  if (!canRequest('userRanking') || showPersonalAppointments.value) {
    console.log('跳过重复的userRanking请求')
    return
  }
  
  userRankLoading.value = true
  
  try {
    // 构建请求参数
    const params = {
      limit: userRankPagination.pageSize,
      page: userRankPagination.current,
      pageSize: userRankPagination.pageSize,
      range: userRankChartRange.value,
      nickname: authorRankingPagination.nickname,
      reviewStatus: userRankChartReviewStatus.value !== 'all' ? userRankChartReviewStatus.value : undefined,
      _t: Date.now() // 添加时间戳避免缓存
    }

    const response = await getTeacherAppointmentRanking(params)

    if (response?.code === 200) {
      userRankData.value = response.data.list || []
      // 更新分页信息
      userRankPagination.current = response.data.pagination?.page || userRankPagination.current
      userRankPagination.pageSize = response.data.pagination?.pageSize || userRankPagination.pageSize
      userRankPagination.total = response.data.pagination?.total || 0
    } else {
      console.error('获取教师排名数据失败:', response)
    }
  } catch (error) {
    console.error('获取教师排名数据失败:', error)
  } finally {
    userRankLoading.value = false
    releaseRequest('userRanking')
  }
}

// 获取学术任职级别
const fetchLevels = async () => {
  try {
    const response = await getAssociationLevels()
    
    if (response && response.code === 200) {
      levelOptions.value = response.data || []
    } else {
      message.error(response?.message || '获取学术任职级别失败')
    }
  } catch (error) {
    console.error('获取学术任职级别失败:', error)
    message.error('获取学术任职级别失败: ' + (error.message || '未知错误'))
  }
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
  // 只更新必要的分页属性，保持其他配置不变
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  // 确保其他配置属性保持不变
  pagination.showSizeChanger = true
  pagination.pageSizeOptions = ['10', '20', '50']
  pagination.showTotal = total => `共 ${total} 条`

  // 处理排序
  if (sorter && sorter.field) {
    // 这里可以添加排序逻辑
  }
  
  fetchData()
}

// 处理学术任职模态框确认
const handleModalOk = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    confirmLoading.value = true
    
    // 构建提交数据
    const submitData = {
      userId: formState.userId,
      associationName: formState.associationName,
      position: formState.position,
      levelId: formState.levelId,
      startYear: formState.startYear,
      endYear: formState.endYear,
      remark: formState.remark
    }
    
    // 处理文件ID
    if (formState.fileIds && formState.fileIds.length > 0) {
      submitData.fileIds = JSON.stringify(formState.fileIds)
    }
    
    // 如果有附件URL
    if (formState.attachmentUrl && formState.attachmentUrl.length > 0) {
      submitData.attachmentUrl = JSON.stringify(formState.attachmentUrl)
    }
    
    let response
    
    if (isEdit.value && currentRecord.value) {
      // 更新学术任职
      response = await updateAppointment(currentRecord.value.id, submitData)
    } else {
      // 添加学术任职
      response = await addAppointment(submitData)
    }
    
    if (response && response.code === 200) {
      message.success(isEdit.value ? '更新成功' : '添加成功')
      modalVisible.value = false
      
      // 重置请求锁
      Object.keys(requestTracker).forEach(key => {
        if (key !== 'timestamps' && key !== 'minInterval') {
          requestTracker[key] = false
        }
      })
      
      // 重新加载数据，但使用延迟确保不会同时触发多个请求
      setTimeout(() => fetchData(true), 100)
    } else {
      message.error(response?.message || (isEdit.value ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    console.error(isEdit.value ? '更新学术任职失败:' : '添加学术任职失败:', error)
    message.error((isEdit.value ? '更新学术任职失败: ' : '添加学术任职失败: ') + (error.message || '未知错误'))
  } finally {
    confirmLoading.value = false
  }
}

// 取消弹窗
const handleModalCancel = () => {
  modalVisible.value = false
}

// 显示添加学术任职弹窗
const showAddModal = () => {
  isEdit.value = false
  currentRecord.value = null
  
  // 重置表单
  formState.userId = ''
  formState.associationName = ''
  formState.position = ''
  formState.levelId = ''
  formState.startYear = undefined
  formState.endYear = undefined
  formState.remark = ''
  
  // 显示弹窗
  modalVisible.value = true
  
  // 如果表单ref存在，重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 处理编辑
const handleEdit = async (record) => {
  isEdit.value = true
  currentRecord.value = record
  
  try {
    const response = await getAppointmentDetail(record.id)
    
    if (response && response.code === 200) {
      const detail = response.data
      
      // 设置基本信息
      formState.userId = detail.userId
      formState.associationName = detail.associationName
      formState.position = detail.position
      formState.levelId = detail.levelId
      formState.startYear = detail.startYear
      formState.endYear = detail.endYear
      formState.remark = detail.remark || ''
      
      // 显示弹窗
      modalVisible.value = true
    } else {
      message.error(response?.message || '获取学术任职详情失败')
    }
  } catch (error) {
    console.error('获取学术任职详情失败:', error)
    message.error('获取学术任职详情失败: ' + (error.message || '未知错误'))
  }
}

// 确认删除
const confirmDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDelete(record)
  })
}

// 删除学术任职
const handleDelete = async (record) => {
  try {
    console.log("record", record);

    const response = await deleteAppointment(record)

    if (response && response.code === 200) {
      message.success('删除成功')

      // 刷新数据和图表
      fetchData()
      fetchStatistics()
      initCharts()
      fetchUserRanking()
    } else {
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('删除学术任职失败:', error)
    message.error('删除学术任职失败: ' + (error.message || '未知错误'))
  }
}

// 处理学术任职审核
const handleReview = async (record) => {
  try {
    // 显示加载中
    message.loading('正在加载详情...', 0.5)
    currentReviewRecord.value = record
    
    // 获取任职详情，包括附件
    try {
      const response = await getAppointmentDetail(record.id)
      if (response && response.code === 200 && response.data) {
        // 处理附件数据
        reviewAttachments.value = (response.data.attachments || []).map((file, index) => ({
          uid: `-${index}`,
          name: file.name || file.originalName || `附件${index + 1}`,
          status: 'done',
          url: file.url,
          response: { file: { id: file.id } },
          data: file
        }))
        console.log('任职附件:', reviewAttachments.value)
      }
    } catch (error) {
      console.error('获取任职详情失败:', error)
      message.error('获取任职详情失败，但将继续审核流程')
    }
    
    // 显示审核模态框
    reviewModalVisible.value = true
    
  } catch (error) {
    console.error('打开审核对话框失败:', error)
    message.error('操作失败: ' + (error.message || '未知错误'))
  }
}

// 添加审核提交处理函数
const handleReviewSubmit = async (formData) => {
  try {
    console.log('接收到的表单数据:', formData)
    
    // 获取当前用户ID作为审核人
    await getUserId(true)
    
    if (!userId.value) {
      message.error('无法获取用户ID，请重新登录')
      reviewModalVisible.value = false
      return
    }
    
    // 调用审核API
    const submitData = {
      id: formData.id,
      reviewer: userId.value,
      reviewStatus: formData.reviewStatus,
      reviewComment: formData.reviewComment
    }
    
    console.log('发送到API的数据:', submitData)
    
    const response = await reviewAppointment(submitData)
    
    if (response && response.code === 200) {
      message.success('审核成功')
      reviewModalVisible.value = false
      fetchData()
      updateCharts()
    } else {
      message.error(response?.message || '审核失败')
    }
  } catch (error) {
    console.error('审核失败:', error)
    message.error('审核失败: ' + (error.message || '未知错误'))
  }
}

// 修改导出处理函数
const handleExport = async () => {
  try {
    exportLoading.value = true
    
    // 显示加载中提示
    const hide = message.loading('正在导出数据...', 0)
    
    // 构造导出参数 - 使用当前的筛选条件
    const params = {
      // 使用当前搜索表单的条件
      associationName: searchForm.associationName,
      levelId: searchForm.levelId,
      startYear: searchForm.startYear,
      endYear: searchForm.endYear,
      range: searchForm.range,
      reviewStatus: searchForm.reviewStatus,
      isExport: true // 标记为导出，返回所有数据
    }
    
    // 如果是个人视图，添加用户ID
    if (showPersonalAppointments.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      } else {
        message.error('无法获取用户ID，请重新登录')
        exportLoading.value = false
        hide()
        return
      }
    }
    
    // 调用API获取数据
    const response = await getAppointments(params)
    
    hide() // 关闭加载提示
    
    if (response && response.code === 200 && response.data.list) {
      const data = response.data.list
      
      // 导出数据处理
      const exportData = data.map(item => {
        return {
          '协会/期刊名称': item.associationName || '',
          '职务名称': item.position || '',
          '级别': item.level?.levelName || '',
          '年份': `${item.startYear}${item.endYear ? ' - ' + item.endYear : ' 至今'}`,
          '教师': item.user?.nickname || item.user?.username || '',
          '分数': item.level?.score || 0,
          '备注': item.remark || '',
          '审核状态': item.ifReviewer === true ? '已审核' : (item.ifReviewer === false ? '已拒绝' : '待审核'),
          '审核建议': item.reviewComment || ''
        }
      })
      
      // 使用xlsx导出
      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '学术任职列表')
      
      // 生成文件名
      const fileName = `学术任职列表_${dayjs().format('YYYY-MM-DD')}.xlsx`
      
      // 导出文件
      XLSX.writeFile(workbook, fileName)
      
      message.success('导出成功')
    } else {
      message.error(response?.message || '导出失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败: ' + (error.message || '未知错误'))
  } finally {
    exportLoading.value = false
  }
}


// 更新charts函数
const updateCharts = () => {
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    // 只有当组件仍然存在才尝试更新图表
    if (levelChartRef.value) loadLevelDistribution();
    if (reviewStatusChartRef.value) loadReviewStatusOverview();
    if (appointmentTrendChartRef.value) loadYearlyTrend();
    if (!showPersonalAppointments.value) loadUserRanking();
  });
}

// 添加导出加载状态
const exportLoading = ref(false)

// 添加文件相关变量
const existingFileList = ref([])
const fileColumns = ref([
  {
    title: '文件名',
    dataIndex: 'fileName',
    key: 'fileName', 
    ellipsis: true
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'ifReviewer',
    key: 'ifReviewer',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 240
  }
])

// 添加审核相关变量
const reviewModalVisible = ref(false)
const currentReviewRecord = ref({})
const reviewAttachments = ref([])
const userSearchTimeout = ref(null)
const userOptions = ref([])
const userSearchLoading = ref(false)

// 教师搜索
const handleUserSearch = (value) => {
  // 清除之前的定时器
  if (userSearchTimeout.value) {
    clearTimeout(userSearchTimeout.value);
    userSearchTimeout.value = null;
  }
  
  // 如果输入为空，清空选项
  if (!value || value.trim() === '') {
    userOptions.value = []
    return
  }
  
  // 设置500ms延迟，避免频繁请求
  userSearchTimeout.value = safeSetTimeout(async () => {
    userSearchLoading.value = true
    try {
      // 调用搜索接口
      const response = await usersSearch({ keyword: value })
      
      if (response && response.code === 200) {
        // 检查返回的数据结构
        if (Array.isArray(response.data)) {
          userOptions.value = response.data
        } else if (response.data && Array.isArray(response.data.list)) {
          // 如果返回的是包含list属性的对象
          userOptions.value = response.data.list
        } else {
          // 如果数据结构不是预期的格式
          console.error('搜索教师返回的数据结构异常:', response.data)
          userOptions.value = []
        }
      } else {
        userOptions.value = []
        console.error('搜索教师失败:', response?.message || '未知错误')
      }
    } catch (error) {
      console.error('搜索教师出错:', error)
      userOptions.value = []
    } finally {
      userSearchLoading.value = false
    }
  }, 500);
}

// 自定义导入处理
const handleImport = async ({ file, onSuccess, onError }) => {
  try {
    message.loading('正在导入数据...')
    const res = await importAppointments(file)
    
    if (res && res.code === 200) {
      message.success('导入成功')
      onSuccess()
      fetchData() // 重新获取数据
      // 刷新图表和统计数据
      fetchStatistics()
      initCharts()
      fetchUserRanking()
    } else {
      message.error(res?.message || '导入失败')
      onError()
    }
  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败: ' + (error.message || '未知错误'))
    onError()
  }
}

// 监听图表范围变化
watch(
  [levelChartRange, reviewStatusChartRange, appointmentTrendChartRange, userRankChartRange],
  () => {
    chartsInitialized.value = false
  }
)

// 监听审核状态变化
watch(
  [levelChartReviewStatus, appointmentTrendChartReviewStatus, userRankChartReviewStatus],
  () => {
    chartsInitialized.value = false
  }
)

// 初始化所有图表 - 修改为仅做记录，不执行实际初始化，避免重复请求
const initCharts = () => {
  console.log('已废弃，请使用sequentialLoadCharts')
  // 此函数保留用于兼容现有代码，但不应直接使用
}

// 使用防抖函数包装图表更新 - 修改为更安全的实现
const debouncedUpdateCharts = debounce(() => {
  // 只在图表已初始化的情况下考虑更新
  if (chartsInitialized.value) {
    console.log('图表更新被防抖处理')
    // 不再直接调用initCharts，避免重复请求
  }
}, 300)

// 获取排名标签颜色
const getRankColor = (rank) => {
  if (rank <= 3) {
    return ['#f5222d', '#fa8c16', '#faad14'][rank - 1]
  }
  return ''
}

// 添加监听数据源变化，但使用更安全的实现
watch(
  dataSource, 
  () => {
    // 只记录变化，不触发额外的图表更新
    console.log('数据源发生变化，图表将在需要时更新')
  }, 
  { deep: true }
)

// 文件大小格式化
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 处理文件上传函数
const handleFileUpload = ({ file, onSuccess, onError, onProgress }) => {
  utilUploadFile({
    file,
    uploadApi: uploadFiles,
    id: formState.id || 'temp_' + Date.now(),
    relatedId: formState.id,
    class: 'academicAppointments',
    onProgress,
    onSuccess: (res) => {
      if (res && res.code === 200 && res.data) {
        // 将文件ID添加到formState.fileIds中
        if (!formState.fileIds) formState.fileIds = []
        formState.fileIds.push(res.data.id)
        
        // 如果有文件路径，添加到attachmentUrl
        if (res.data.filePath) {
          if (!formState.attachmentUrl) formState.attachmentUrl = []
          formState.attachmentUrl.push(res.data.filePath)
        }
      }
      onSuccess(res)
    },
    onError,
    fileList: fileList.value,
    formState: formState
  })
}

// 文件上传前检查
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
    return false;
  }
  return true;
};

// 预览文件
const previewFile = (file) => {
  const fileId = file.response?.file?.uid || file.id
  if (fileId) {
    previewFileById(fileId)
  } else {
    message.warning('无法获取文件ID，预览失败')
  }
}

// 下载文件
const downloadFile = (file) => {
  const fileId = file.response?.file?.uid || file.id
  const fileName = file.name || file.originalName || '下载文件'
  if (fileId) {
    downloadFileById(fileId, fileName)
  }  else {
    message.warning('无法获取文件ID，下载失败')
  }
}

// 删除文件
const confirmDeleteFile = (record) => {
  const fileId = record.response?.file?.uid
  if (fileId) {
    deleteFileById(fileId, {
      onSuccess: () => {
        // 从文件列表中移除
        const index = fileList.value.findIndex(item => 
          item.uid === record.uid || 
          item.id === fileId
        )
        
        if (index !== -1) {
          fileList.value.splice(index, 1)
        }
        
        // 从fileIds中移除
        const idIndex = formState.fileIds.indexOf(fileId)
        if (idIndex !== -1) {
          formState.fileIds.splice(idIndex, 1)
        }
        
        message.success('文件已删除')
      },
      onError: (errorMsg) => {
        message.error(`删除失败: ${errorMsg}`)
      }
    })
    } else {
    // 如果没有文件ID，只是从列表中移除
    const index = fileList.value.findIndex(item => item.uid === record.uid)
    if (index !== -1) {
      fileList.value.splice(index, 1)
      message.success('文件已从列表中移除')
    }
  }
}
// 删除已存在的文件
const removeExistingFile = (fileId) => {
  if (fileId) {
    deleteFileById(fileId, {
      onSuccess: () => {
        // 从已存在文件列表中移除
        const index = existingFileList.value.findIndex(item => item.id === fileId)
        if (index !== -1) {
          existingFileList.value.splice(index, 1)
        }
        
        // 从fileIds中移除
        if (formState.fileIds) {
          const idIndex = formState.fileIds.indexOf(fileId)
          if (idIndex !== -1) {
            formState.fileIds.splice(idIndex, 1)
          }
        }
        
        message.success('文件已删除')
      },
      onError: (errorMsg) => {
        message.error(`删除失败: ${errorMsg}`)
      }
    })
  }
}

// 修改 hasPermissionToReview 函数
// 使用hasPermissionToReview函数替代isAdmin/isCurrentUserFirstResponsible函数
const hasPermissionToReview = (record) => {
  // 未审核的才需要显示审核按钮
  if (record.ifReviewer !== null) return false;
  
  // 默认允许显示审核按钮，后端会做权限验证
  return true;
};

const userRankExportLoading = ref(false)

// 导出教师排名数据
const exportUserRanking = async () => {
  try {
    userRankExportLoading.value = true
    // 显示加载中提示
    const hide = message.loading('正在导出排行榜数据...', 0)
    
    const response = await getTeacherAppointmentRanking({
      range: userRankChartRange.value,
      reviewStatus: userRankChartReviewStatus.value !== 'all' ? userRankChartReviewStatus.value : undefined,
      isExport: true
    })
    
    hide() // 关闭加载提示
    
    if (response && response.code === 200 && response.data.list) {
      const data = response.data.list
      
      // 导出数据处理
      const exportData = data.map((item, index) => {
        return {
          '排名': index + 1,
          '教师': item.userName,
          '工号': item.employeeNumber || item.studentNumber,
          '任职总数': item.totalAppointments,
          '总得分': item.totalScore.toFixed(2)
        }
      })
      
      // 使用xlsx导出
      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '教师学术任职排行榜')
      
      // 生成文件名
      const fileName = `教师学术任职排行榜_${dayjs().format('YYYY-MM-DD')}.xlsx`
      
      // 导出文件
      XLSX.writeFile(workbook, fileName)
      
      message.success('导出成功')
    } else {
      message.error(response?.message || '导出失败')
    }
  } catch (error) {
    console.error('导出教师排名失败:', error)
    message.error('导出失败: ' + (error.message || '未知错误'))
  } finally {
    userRankExportLoading.value = false
  }
}

// 添加教师任职详情弹窗函数
const showUserAppointmentsDetails = async (record) => {
  try {
    userDetailsVisible.value = true
    selectedUserDetailName.value = record.userName
    
    // 保存当前用户信息供后续使用
    currentUserDetail.value = record
    
    // 重置分页到第一页
    userDetailsPagination.current = 1
    userDetailsPagination.pageSize = 10
    
    // 加载第一页数据
    await fetchUserAppointmentsDetails(1, 10)
  } catch (error) {
    console.error('获取教师任职详情失败:', error)
    message.error('获取教师任职详情失败: ' + (error.message || '未知错误'))
  }
}

// 获取教师任职详情数据
const fetchUserAppointmentsDetails = async (page = 1, pageSize = 10) => {
  try {
    userDetailsLoading.value = true

    // 构建请求参数
    const params = {
      userId: currentUserDetail.value?.userId,
      page: page,
      pageSize: pageSize,
      range: userRankChartRange.value,
      reviewStatus: userRankChartReviewStatus.value !== 'all' ? userRankChartReviewStatus.value : undefined
    }

    // 调用API获取教师任职详情
    const response = await getAppointments(params)

    if (response && response.code === 200) {
      userAppointmentDetails.value = response.data.list || []
      
      // 更新分页总条数
      userDetailsPagination.total = response.data.total || 0
      
      // 计算总分
      userDetailsTotalScore.value = userAppointmentDetails.value.reduce((total, item) => {
        return total + (parseFloat(item.level?.score) || 0)
      }, 0)
    } else {
      message.error(response?.message || '获取教师任职详情失败')
    }
  } catch (error) {
    console.error('获取教师任职详情失败:', error)
    message.error('获取教师任职详情失败: ' + (error.message || '未知错误'))
  } finally {
    userDetailsLoading.value = false
  }
}

// 添加一个存储当前查看用户的变量
const currentUserDetail = ref(null);

// 处理表格分页变化
const handleUserDetailsPaginationChange = (pagination) => {
  // 只更新必要的分页属性，保持其他配置不变
  userDetailsPagination.current = pagination.current
  userDetailsPagination.pageSize = pagination.pageSize
  // 确保其他配置属性保持不变
  userDetailsPagination.showSizeChanger = true
  userDetailsPagination.pageSizeOptions = ['10', '20', '50']
  userDetailsPagination.showTotal = total => `共 ${total} 条`

  // 加载对应页数据
  fetchUserAppointmentsDetails(pagination.current, pagination.pageSize)
}

// 添加表单验证规则
const rules = {
  userId: [
    { required: true, message: '请选择教师', trigger: 'change' }
  ],
  associationName: [
    { required: true, message: '请输入协会/期刊名称', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请输入职务名称', trigger: 'blur' }
  ],
  levelId: [
    { required: true, message: '请选择职务级别', trigger: 'change' }
  ],
  startYear: [
    { required: true, message: '请输入起始年份', trigger: 'change' }
  ]
}

// 在适当的位置（例如loadStatisticsData函数后面）添加fetchStatistics函数
// 新增：添加fetchStatistics函数，调用loadStatisticsData
const fetchStatistics = () => {
  loadStatisticsData();
}

// 新增：添加fetchUserRanking函数，调用loadUserRanking
const fetchUserRanking = () => {
  loadUserRanking();
}

// 处理用户排名表格变化
const handleUserRankTableChange = (pagination) => {
  // 只更新必要的分页属性，保持其他配置不变
  userRankPagination.current = pagination.current
  userRankPagination.pageSize = pagination.pageSize
  // 确保其他配置属性保持不变
  userRankPagination.showSizeChanger = true
  userRankPagination.pageSizeOptions = ['10', '20', '50']
  userRankPagination.showTotal = (total) => `共 ${total} 条`
  loadUserRanking()
}

// 分页配置
const userDetailsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
})

// Excel转JSON相关状态
const importPreviewVisible = ref(false) // Excel预览模态框可见性
const importResultVisible = ref(false) // 导入结果模态框可见性
const importPreviewData = ref([]) // Excel预览数据
const importInProgress = ref(false) // 导入中状态
const convertingExcel = ref(false) // Excel转换中状态
const userIdCheckError = ref(false) // 用户ID检查错误标志

// 导入结果
const importResults = reactive({
  success: 0,
  failed: 0,
  failedRecords: []
})

// 预览表格列定义
const importPreviewColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100,
  },
  {
    title: '协会/期刊名称',
    dataIndex: 'associationName',
    key: 'associationName',
    width: 200,
  },
  {
    title: '职务',
    dataIndex: 'position',
    key: 'position',
    width: 120,
  },
  {
    title: '级别',
    dataIndex: 'levelName',
    key: 'levelName',
    width: 100,
  },
  {
    title: '起始年份',
    dataIndex: 'startYear',
    key: 'startYear',
    width: 100,
  },
  {
    title: '结束年份',
    dataIndex: 'endYear',
    key: 'endYear',
    width: 100,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 150,
  },
  {
    title: '用户ID匹配',
    key: 'userIdCheck',
    width: 100,
    customRender: ({ record }) => {
      if (record.userIdCheckStatus) {
        return h('a-tag', { color: 'success' }, () => '已匹配');
      } else {
        return h('a-tag', { color: 'error' }, () => '未匹配');
      }
    }
  }
]

// 检查Excel文件格式
const beforeExcelUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel' ||
                  /\.xlsx?$/.test(file.name);
  if (!isExcel) {
    message.error('请上传Excel文件!');
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
    return false;
  }
  return true;
}

// Excel转JSON处理
const handleExcelToJsonConvert = ({ file }) => {
  convertingExcel.value = true;
  importPreviewData.value = [];
  userIdCheckError.value = false;
  importPreviewVisible.value = true;
  
  // 使用工具函数转换Excel
  excelToAcademicAppointmentsJson(file).then(async (data) => {
    console.log('解析Excel结果:', data);
    
    const processedData = data.map((item, index) => ({
      ...item,
      index: index + 1,
      userIdCheckStatus: false, // 默认未匹配
      userId: null // 待查询后填充
    }));
    
    importPreviewData.value = processedData;
    
    // 检查用户ID
    await checkAuthorUserId();
    
    convertingExcel.value = false;
  }).catch((error) => {
    console.error('Excel转换出错:', error);
    message.error('Excel文件解析失败: ' + error.message);
    convertingExcel.value = false;
    importPreviewVisible.value = false;
  });
}

// 检查作者用户ID
const checkAuthorUserId = async () => {
  const appointmentsData = importPreviewData.value;
  let foundCount = 0;
  let notFoundCount = 0;
  
  userIdCheckError.value = false;
  
  for (let i = 0; i < appointmentsData.length; i++) {
    const appointment = appointmentsData[i];
    let found = false;
    
    // 如果有人事编号或姓名，则进行搜索
    if (appointment.personnelId || appointment.name) {
      try {
        // 优先使用人事编号搜索
        const searchKeyword = appointment.personnelId || appointment.name;
        // 搜索用户ID
        const response = await usersSearch({ keyword: searchKeyword });
        
        if (response && response.code === 200 && response.data && response.data.length > 0) {
          // 找到完全匹配的用户（人事编号精确匹配）
          if (appointment.personnelId) {
            const exactMatch = response.data.find(u => u.studentNumber === appointment.personnelId);
            if (exactMatch) {
              appointment.userId = exactMatch.id;
              found = true;
              foundCount++;
            } else {
              // 如果没有完全匹配，使用第一个结果
              appointment.userId = response.data[0].id;
              found = true;
              foundCount++;
            }
          } else {
            // 使用姓名搜索时，使用第一个结果
            appointment.userId = response.data[0].id;
            found = true;
            foundCount++;
          }
        } else {
          // 如果用人事编号没找到，尝试使用姓名搜索
          if (appointment.personnelId && appointment.name) {
            const nameResponse = await usersSearch({ keyword: appointment.name });
            if (nameResponse && nameResponse.code === 200 && nameResponse.data && nameResponse.data.length > 0) {
              appointment.userId = nameResponse.data[0].id;
              found = true;
              foundCount++;
            } else {
              appointment.userId = null;
              notFoundCount++;
              userIdCheckError.value = true;
            }
          } else {
            appointment.userId = null;
            notFoundCount++;
            userIdCheckError.value = true;
          }
        }
        
        // 添加一个小延时避免API请求太快
        if (i < appointmentsData.length - 1) {
          await new Promise(resolve => safeSetTimeout(resolve, 50));
        }
      } catch (error) {
        console.error(`查找用户"${appointment.name}"失败:`, error);
        appointment.userId = null;
        notFoundCount++;
        userIdCheckError.value = true;
      }
    } else {
      // 如果既没有人事编号也没有姓名，则标记为未找到
      appointment.userId = null;
      notFoundCount++;
      userIdCheckError.value = true;
    }
    
    // 更新检查状态
    appointment.userIdCheckStatus = found;
  }
  
  console.log(`用户ID检查完成: 找到${foundCount}条, 未找到${notFoundCount}条`);
  
  return {
    total: foundCount + notFoundCount,
    found: foundCount,
    notFound: notFoundCount
  };
}

// 添加getList函数的定义，避免引用错误
const getList = () => {
  fetchData(true);
};

// 处理开始导入
const handleStartImport = async () => {
  if (importPreviewData.value.length === 0) {
    message.warning('没有可导入的数据');
    return;
  }
  
  importInProgress.value = true;
  importResults.success = 0;
  importResults.failed = 0;
  importResults.failedRecords = [];
  
  try {
    // 查询级别ID映射
    let levelMap = {};
    try {
      const levelRes = await getAssociationLevels();
      if (levelRes.code === 200 && levelRes.data && levelRes.data.length > 0) {
        levelRes.data.forEach(level => {
          levelMap[level.levelName] = level.id;
        });
      }
    } catch (error) {
      console.error('获取级别数据失败:', error);
    }
    
    // 创建一个更安全的异步处理函数
    const processItems = async () => {
      const data = [...importPreviewData.value]; // 复制数组，避免直接操作原数组
      
      for (let i = 0; i < data.length; i++) {
        // 检查组件是否还在运行
        if (!importInProgress.value) {
          console.log('导入已被中断');
          break;
        }
        
        const item = data[i];
        try {
          // 如果用户ID未匹配成功但有姓名，尝试再次查询
          if (!item.userId && item.name) {
            try {
              const nameResponse = await usersSearch({ keyword: item.name });
              if (nameResponse && nameResponse.code === 200 && nameResponse.data && nameResponse.data.length > 0) {
                item.userId = nameResponse.data[0].id;
                item.userIdCheckStatus = true;
                console.log(`在导入时成功匹配到用户ID: ${item.name} => ${item.userId}`);
              }
            } catch (error) {
              console.error(`导入时查找用户名"${item.name}"失败:`, error);
            }
          }

          // 如果用户ID匹配失败并且无法在导入时解决，则记录失败
          if (!item.userId) {
            importResults.failed++;
            importResults.failedRecords.push({
              ...item,
              error: '用户ID匹配失败'
            });
            continue;
          }
          
          // 查找级别ID
          const levelId = item.levelName ? levelMap[item.levelName] : null;
          if (!levelId) {
            importResults.failed++;
            importResults.failedRecords.push({
              ...item,
              error: '级别匹配失败'
            });
            continue;
          }
          
          // 构建请求数据
          const requestData = {
            userId: item.userId,
            associationName: item.associationName,
            position: item.position,
            levelId: levelId,
            startYear: item.startYear,
            endYear: item.endYear || null,
            remark: item.remark
          };
          
          // 发送请求创建任职
          const res = await addAppointment(requestData);
          
          if (res && res.code === 200) {
            importResults.success++;
          } else {
            importResults.failed++;
            importResults.failedRecords.push({
              ...item,
              error: res?.message || '创建失败'
            });
          }
          
          // 小延时，避免UI阻塞
          await new Promise(resolve => safeSetTimeout(resolve, 100));
          
        } catch (error) {
          console.error('导入单条记录失败:', error);
          importResults.failed++;
          importResults.failedRecords.push({
            ...item,
            error: error.message || '处理异常'
          });
        }
      }
    };
    
    // 开始处理
    await processItems();
    
    // 刷新数据
    getList();
    fetchStatistics();
    updateCharts(); // 使用updateCharts代替initCharts，更安全
    
  } catch (error) {
    console.error('导入过程中出错:', error);
    message.error('导入过程中出错: ' + error.message);
  } finally {
    importInProgress.value = false;
    importPreviewVisible.value = false;
    importResultVisible.value = true;
  }
}

// 导出失败记录
const exportFailedRecords = () => {
  if (importResults.failedRecords.length === 0) {
    message.info('没有失败记录可导出');
    return;
  }
  
  const failedData = importResults.failedRecords.map(item => ({
    姓名: item.name,
    学术协会期刊名称: item.associationName,
    职务: item.position,
    级别: item.levelName,
    起始年份: item.startYear,
    结束年份: item.endYear || '',
    备注: item.remark || '',
    失败原因: item.error || ''
  }));
  
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(failedData);
  XLSX.utils.book_append_sheet(workbook, worksheet, '导入失败记录');
  
  const excelBuffer = XLSX.write(workbook, { type: 'array', bookType: 'xlsx' });
  const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
  
  const filename = `学术任职导入失败记录_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`;
  
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    window.navigator.msSaveOrOpenBlob(blob, filename);
  } else {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
}

// 下载预览数据为JSON
const handleDownloadJson = () => {
  if (importPreviewData.value.length === 0) {
    message.warning('没有可下载的数据');
    return;
  }
  
  const jsonData = importPreviewData.value.map(item => ({
    name: item.name,
    personnelId: item.personnelId,
    associationName: item.associationName,
    position: item.position,
    levelName: item.levelName,
    startYear: item.startYear,
    endYear: item.endYear,
    remark: item.remark,
    userIdCheckStatus: item.userIdCheckStatus,
    userId: item.userId
  }));
  
  downloadJson(jsonData, `学术任职数据_${dayjs().format('YYYYMMDD_HHmmss')}`)
    .then(() => message.success('下载成功'))
    .catch(error => {
      console.error('下载JSON失败:', error);
      message.error('下载失败: ' + error.message);
    });
}

// 处理重新提交审核
const handleResubmit = (record) => {
  Modal.confirm({
    title: '确认重新提交审核',
    content: '是否确认将该任职记录重新提交审核？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await resubmitForReview(record.id);
    }
  });
};

// 重新提交审核接口调用
const resubmitForReview = async (id) => {
  try {
    isLoading.value = true;
    const response = await reapplyReview({ id });
    
    if (response && response.code === 200) {
      message.success('重新提交审核成功');
      fetchData(); // 刷新数据
    } else {
      message.error(response?.message || '重新提交审核失败');
    }
  } catch (error) {
    console.error('重新提交审核失败:', error);
    message.error('重新提交审核失败: ' + (error.message || error));
  } finally {
    isLoading.value = false;
  }
};

// 修改fetchData函数，使其控制所有其他数据的加载
const fetchData = async (isInitialLoad = false) => {
  // 如果请求已锁定，跳过
  if (!canRequest('appointments')) {
    console.log('跳过重复的appointments请求')
    return
  }
  
  isLoading.value = true
  errorMessage.value = ''
  
  try {
    // 构建请求参数
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      reviewStatus: searchForm.reviewStatus,
      range: searchForm.range,
      _t: Date.now() // 添加时间戳避免缓存
    }
    
    // 添加协会/期刊名称搜索
    if (searchForm.associationName) {
      params.associationName = searchForm.associationName
    }

    // 添加职务级别搜索
    if (searchForm.levelId) {
      params.levelId = searchForm.levelId
    }

    // 添加年份搜索
    if (searchForm.startYear) {
      params.startYear = searchForm.startYear
    }

    if (searchForm.endYear) {
      params.endYear = searchForm.endYear
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalAppointments.value) {
      try {
        const currentUserId = await getUserId(true)
        
        if (!currentUserId) {
          message.error('未获取到教师信息，请重新登录')
          return
        }
        
        params.userId = currentUserId
      } catch (userError) {
        console.error('获取教师信息过程中发生错误:', userError)
        message.error('获取教师信息失败，请重新登录')
        return
      }
    }
    
    // 统一调用getAppointments API
    const response = await getAppointments(params)
    
    if (response && response.code === 200) {
      dataSource.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0
      
      // 如果是初始加载或图表未初始化，按顺序加载其他数据
      if (isInitialLoad || !chartsInitialized.value) {
        // 先获取统计数据，这不需要等待图表初始化
        safeSetTimeout(() => loadStatisticsData(), 100)
        
        // 然后按顺序初始化图表
        safeSetTimeout(() => sequentialLoadCharts(), 300)
        
        // 如果不是个人视图，加载用户排名
        if (!showPersonalAppointments.value) {
          safeSetTimeout(() => loadUserRanking(), 500)
        }
      }
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = '获取学术任职列表失败：' + (response?.message || '未知错误')
    }
  } catch (error) {
    console.error('获取学术任职列表失败:', error)
    message.error('获取学术任职列表失败: ' + (error.message || '未知错误'))
    errorMessage.value = '获取学术任职列表失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
    releaseRequest('appointments')
  }
}

// 获取时间范围
const getTimeRange = () => {
  // 调用API获取时间范围
  getScoreTimeRange('academicAppointments').then(res => {
    if (res.code === 200 && res.data) {
      timeRangeText.value = res.data.timeRange || '';
    } else {
      timeRangeText.value = '暂无时间范围数据';
    }
  }).catch(error => {
    console.error('获取时间范围失败:', error);
    timeRangeText.value = '获取时间范围失败';
  });
};
</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';
</style>