import request from '../server'

// API 路径配置
const api = {
  list: '/highLevelPapersRules',
  detail: '/highLevelPapersRules/detail',
  create: '/highLevelPapersRules',
  update: '/highLevelPapersRules',
  delete: '/highLevelPapersRules',
  batchDelete: '/highLevelPapersRules/batch',
  import: '/highLevelPapersRules/import',
  export: '/highLevelPapersRules/export'
}

/**
 * 获取高水平论文规则列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getHighLevelPapersRules(params) {
  const { page = 1, pageSize = 10, paperLevel } = params || {};
  
  // 构造参数对象
  const queryParams = {
    page,
    pageSize,
    paperLevel
  };
  
  return request.get(api.list, queryParams);
}

/**
 * 获取所有高水平论文规则（不分页）
 * @returns {Promise} - 返回Promise对象
 */
export function getAllHighLevelPapersRules() {
  return request.get(api.list, { pageSize: 999 }); // 使用大的pageSize获取所有数据
}

/**
 * 获取高水平论文规则详情
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function getHighLevelPapersRuleDetail(id) {
  return request.get(api.detail, { id });
}

/**
 * 创建高水平论文规则
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function addHighLevelPapersRule(data) {
  return request.post(api.create, data);
}

/**
 * 更新高水平论文规则
 * @param {string} id - 规则ID
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateHighLevelPapersRule(id, data) {
  const updateData = { ...data };
  delete updateData.id; // 从请求体中删除id
  return request.put(`${api.update}/${id}`, updateData);
}

/**
 * 删除高水平论文规则
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteHighLevelPapersRule(id) {
  return request.delete(`${api.delete}/${id}`);
}

/**
 * 批量删除高水平论文规则
 * @param {Array} ids - 规则ID数组
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteHighLevelPapersRules(ids) {
  return request.delete(api.batchDelete, { ids });
}

/**
 * 导入高水平论文规则数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回Promise对象
 */
export function importHighLevelPapersRules(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request.post(api.import, formData, null, 'multipart/form-data');
}

/**
 * 导出高水平论文规则数据
 * @param {Object} params - 过滤参数
 * @returns {Promise} - 返回Promise对象
 */
export function exportHighLevelPapersRules(params) {
  return request.get(api.export, params, { responseType: 'blob' });
} 
