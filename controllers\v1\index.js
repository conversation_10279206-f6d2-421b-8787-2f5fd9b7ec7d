/**
 * 控制器入口文件
 * 整合所有系统控制器
 */

// 系统控制器
const authController = require('./sys/authController');
const userController = require('./sys/userController');
const roleController = require('./sys/roleController');
const permissionsController = require('./sys/permissionsController');
const resourcesController = require('./sys/resourcesController');
const userOptLogController = require('./sys/userOptLogController');

// 业务控制器
const homeController = require('./sys/homeController');
const teacherController = require('./sys/teacherController');
const researchProjectController = require('./sys/researchProjectController');
const notificationController = require('./sys/notificationController');
const formController = require('./sys/formController');
const dictionaryController = require('./sys/dictionaryController');
const serviceController = require('./sys/serviceController');
// 新增国际交流控制器
const internationalExchangeController = require('./sys/internationalExchangeController');
const highLevelPapersRulesController = require('./sys/highLevelPapersRulesController');

module.exports = {
  // 系统控制器
  sys: {
    authController,
    userController,
    roleController,
    permissionsController,
    resourcesController,
    userOptLogController,
    
    // 业务控制器
    homeController,
    teacherController,
    researchProjectController,
    notificationController,
    formController,
    dictionaryController,
    serviceController,
    // 添加国际交流控制器导出
    internationalExchangeController,
    highLevelPapersRulesController
  }
}; 