import {defineStore} from 'pinia';
import {useRouter} from 'vue-router';
import {useDbStore} from './db.js'
import dbUtils from '../libs/util.strotage.js'
import {useTabsStore} from './tabs';
import {authLogin} from "../api/modules/api.auth";
import {ZyNotification} from "../libs/util.toast";
import {rolesFindOne} from "../api/modules/api.roles";
import {usersFindOne} from "../api/modules/api.users";
import { useUserRole } from '../../composables/useUserRole'; // 导入角色管理composable


export const useAuthStore = defineStore('auth', () => {
    let router = useRouter()
    const db = useDbStore();
    // 重置tabs
    let tabStore = useTabsStore()
    // 用户角色管理
    const userRoleManager = useUserRole();

    async function logout() {
        // 执行退出登录逻辑，例如清除用户凭证和重置用户状态等
        // ...
        // 重置tabs
        tabStore.resetTabs()
        // 清除所有缓存
        db.removeAllInfo()
        // 清除角色信息
        userRoleManager.clearRole();

        // 导航到登录页或其他适当的页面
        await router.replace('/login');

    }

    async function login(value) {
        // 执行登录逻辑
        return new Promise((resolve, reject) => {
            console.log('开始登录流程，参数:', {...value, password: '******'})
            authLogin(value).then(res => {
                console.log('===========================================')
                console.log('登录API调用成功，响应数据:', {
                    id: res.data.id,
                    username: res.data.username,
                    nickname: res.data.nickname,
                    roleId: res.data.roleId,
                    status: res.data.status,
                    token: res.data.token ? res.data.token.substring(0, 20) + '...' : '无token'
                })
                
                // 首先检查token是否有效
                if (!res.data.token) {
                    console.error('登录响应中无token!')
                    ZyNotification.error('登录失败：服务器未返回有效的授权令牌')
                    reject(new Error('登录响应中无token'))
                    return
                }
                
                // 清除旧缓存
                console.log('清除旧缓存数据')
                dbUtils.clear()
                
                // 单独处理token存储，确保成功
                try {
                    console.log('存储token到localStorage')
                    // 确保token有Bearer前缀
                    let tokenToStore = res.data.token
                    if (!tokenToStore.startsWith('Bearer ')) {
                        console.log('添加Bearer前缀到token')
                        tokenToStore = 'Bearer ' + tokenToStore
                    }
                    
                    // 存储到localStorage
                    localStorage.setItem(`zyadmin-1.0.0-token`, tokenToStore)
                    console.log('Token存储成功，长度:', tokenToStore.length)
                    
                    // 再次验证token存储是否成功
                    const storedToken = localStorage.getItem(`zyadmin-1.0.0-token`)
                    console.log('验证Token存储:', storedToken ? '成功' : '失败', 
                                storedToken ? `长度:${storedToken.length}` : '')
                    
                    // 同时存储到dbUtils，确保两处保持一致
                    dbUtils.set('token', tokenToStore)
                    console.log('token已同步保存到dbUtils和localStorage')
                } catch (e) {
                    console.error('Token存储失败:', e)
                }
                
                // 先保存基本用户信息，确保页面能正常显示
                console.log('保存基本用户信息')
                const baseUserInfo = {
                    id: res.data.id,
                    username: res.data.username,
                    nickname: res.data.nickname || '用户' + res.data.id, // 确保有默认昵称
                    roleId: res.data.roleId,
                    status: res.data.status,
                    avatar: res.data.avatar || '/assets/default-avatar.png', // 确保有默认头像
                    loginTime: new Date().toISOString()
                }
                dbUtils.set('userInfo', baseUserInfo)
                console.log('基本用户信息存储完成:', baseUserInfo)
                
                // 单独存储用户ID，便于其他组件使用
                try {
                    // 将用户ID存储到localStorage
                    localStorage.setItem('zyadmin-1.0.0-userId', res.data.id);
                    console.log('用户ID单独存储成功:', res.data.id);
                    
                    // 同时存储到sessionStorage，增加可靠性
                    sessionStorage.setItem('zyadmin-1.0.0-userId', res.data.id);
                    console.log('用户ID同时存储到sessionStorage');
                    
                    // 设置cookie，过期时间为1天
                    const expires = new Date();
                    expires.setDate(expires.getDate() + 1);
                    document.cookie = `userId=${res.data.id}; expires=${expires.toUTCString()}; path=/`;
                    console.log('用户ID存储到cookie，过期时间1天');
                } catch (e) {
                    console.error('单独存储用户ID失败:', e);
                }
                
                // 添加延迟确保token已存储并可用于后续请求
                console.log('延迟200ms后获取完整用户信息')
                setTimeout(() => {
                    console.log('===========================================')
                    console.log('开始获取用户详细信息和角色权限')
                    
                    // 检查token是否仍然有效
                    const checkToken = dbUtils.get('token')
                    console.log('获取用户信息前检查token:', checkToken ? '存在' : '不存在',
                               checkToken ? `长度:${checkToken.length}` : '')
                    
                    if (!checkToken) {
                        console.error('TOKEN已丢失，无法获取用户详细信息!')
                        // 重新存储token
                        console.log('尝试重新存储token')
                        dbUtils.set('token', res.data.token)
                    }
                    
                    // 并行获取角色信息和用户详细信息
                    console.log('并行发起角色和用户信息请求')
                    
                    // 获取角色信息
                    rolesFindOne({id: res.data.roleId})
                        .then(role => {
                            console.log('角色信息获取成功:', {
                                name: role.data.name,
                                permissionsCount: role.data.permissions ? role.data.permissions.length : 0
                            })
                            
                            // 存储权限信息
                            setPerm(Array.isArray(role.data.permissions) ? role.data.permissions : [])
                            console.log('权限信息存储成功')
                        })
                        .catch(err => {
                            console.error('获取角色信息失败:', err)
                            ZyNotification.warning('获取权限信息失败，部分功能可能受限')
                            // 设置空权限数组，避免undefined
                            setPerm([])
                        })
                        .finally(() => {
                            console.log('角色信息处理完成')
                        })
                    
                    // 获取用户详细信息
                    usersFindOne({id: res.data.id})
                        .then(user => {
                            console.log('用户详细信息获取成功:', {
                                nickname: user.data.nickname,
                                email: user.data.email,
                                avatar: user.data.avatar ? '有头像' : '无头像'
                            })
                            
                            // 更新用户详细信息，合并基本信息和详细信息
                            const currentUserInfo = dbUtils.get('userInfo') || {}
                            const updatedUserInfo = {
                                ...currentUserInfo,
                                ...user.data,
                                lastUpdated: new Date().toISOString()
                            }
                            dbUtils.set('userInfo', updatedUserInfo)
                            console.log('用户详细信息更新成功')
                        })
                        .catch(err => {
                            console.error('获取用户详细信息失败:', err)
                            ZyNotification.warning('获取用户详情失败，部分功能可能受限')
                        })
                        .finally(() => {
                            console.log('用户信息处理完成')
                        })
                    
                    // 获取用户角色信息
                    console.log('开始获取用户角色信息')
                    userRoleManager.getUserRole(res.data.id)
                        .then(roleInfo => {
                            console.log('用户角色获取成功:', roleInfo)
                            // 角色信息已经在composable中保存到localStorage
                        })
                        .catch(err => {
                            console.error('获取用户角色失败:', err)
                            ZyNotification.warning('获取角色信息失败，部分功能可能受限')
                        })
                        .finally(() => {
                            console.log('角色信息处理完成')
                        })
                    
                    // 所有信息处理完成，完成登录流程
                    console.log('===========================================')
                    console.log('完成登录流程，重定向到首页')
                    resolve({...res.data})
                    ZyNotification.success(`欢迎: ${res.data.nickname || '用户'+res.data.id}`)
                    router.replace('/');
                    
                }, 200) // 增加延迟到200ms确保token存储完成
            }).catch(err => {
                console.error('登录失败:', err)
                ZyNotification.error('登录失败: ' + (err.message || '服务器错误'))
                reject(err)
            })
        })
    }

    // 获取用户的角色权限列表数据 并且存储本地
    async function setPerm(value) {
        try {
            // 确保权限列表是数组
            let permissions = Array.isArray(value) ? value : [];
            
            // 如果权限列表为空，添加默认权限
            if (permissions.length === 0) {
                console.warn('权限列表为空，使用默认权限');
                // 添加默认权限
                permissions = ['index']; // 确保用户至少可以访问首页
            }
            
            console.log('保存用户权限:', permissions);
            
            // 存储到本地
            dbUtils.set('perms', permissions);
            
            // 再次验证存储是否成功
            const storedPerms = dbUtils.get('perms');
            if (!storedPerms || !Array.isArray(storedPerms) || storedPerms.length === 0) {
                console.error('权限存储验证失败，重新尝试');
                // 再次尝试存储
                localStorage.setItem(`zyadmin-1.0.0-perms`, JSON.stringify(permissions));
                dbUtils.set('perms', permissions);
            }
        } catch (error) {
            console.error('保存权限出错:', error);
            // 出错时设置默认权限
            const defaultPerms = ['index'];
            dbUtils.set('perms', defaultPerms);
        }
    }

    return {
        setPerm,
        logout,
        login,
    };

})
