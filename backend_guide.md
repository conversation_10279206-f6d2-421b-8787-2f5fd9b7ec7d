# 后端部署与维护说明

本项目后端基于 **Node.js + PM2**，支持一键拉取代码、安装依赖并重启服务。

## 目录结构
```

/root/jxpd/
├── jn-jxpd-server       # 后端代码目录
│    ├── app.js          # 入口文件
│    ├── package.json    # Node 依赖清单
│    ├── requirements.txt# Python 依赖清单（可选）
│    └── ...
└── ...

````

## 一键部署脚本

部署脚本：`deploy_backend.sh`（需放在服务器上并赋权）

```bash
#!/usr/bin/env bash
set -euo pipefail

APP_DIR="/root/jxpd/jn-jxpd-server"
PM2_NAME="jxpd-server"
BRANCH="${BRANCH:-master}"
ENTRY="${ENTRY:-app.js}"
NODE_ENV="${NODE_ENV:-production}"
PORT="${PORT:-3089}"

cd "$APP_DIR"
echo "==> 拉取最新代码（$BRANCH）"
git fetch --all --prune
git checkout -B "$BRANCH" "origin/$BRANCH"
git pull --ff-only

if [[ -f package.json ]]; then
  echo "==> 安装 Node 依赖"
  if [[ -f package-lock.json ]]; then
    npm ci
  else
    npm install --production
  fi
  if npm run | grep -qE ' build'; then
    npm run build || true
  fi
fi

if [[ -f requirements.txt ]]; then
  echo "==> 安装 Python 依赖"
  python3 -m pip install --upgrade pip
  pip3 install -r requirements.txt
fi

echo "==> 重启 PM2"
export NODE_ENV="$NODE_ENV" PORT="$PORT"
if pm2 list | grep -q "$PM2_NAME"; then
  pm2 restart "$PM2_NAME" --update-env
else
  if [[ -f ecosystem.config.js ]]; then
    pm2 start ecosystem.config.js --only "$PM2_NAME"
  else
    pm2 start "$ENTRY" --name "$PM2_NAME" --cwd "$APP_DIR" --update-env
  fi
fi
pm2 save
echo "✅ 部署完成"
````

## 使用方法

1. **赋权**

```bash
chmod +x deploy_backend.sh
```

2. **发布默认分支（master）**

```bash
./deploy_backend.sh
```

3. **发布指定分支**

```bash
BRANCH=feature-old-base ./deploy_backend.sh
```

4. **指定端口 / 入口文件**

```bash
PORT=3089 ENTRY=app.js ./deploy_backend.sh
```

---

## 快速自检

1. **检查 PM2 状态**

```bash
pm2 list
```

`status` 应为 `online`。

2. **查看运行日志**

```bash
pm2 logs jxpd-server --lines 100
```

3. **验证 API 是否正常**

```bash
curl -I http://127.0.0.1:3089/v1/auth/login
```

---

## 常见问题

| 问题                    | 可能原因            | 解决方案                                                                      |
| --------------------- | --------------- | ------------------------------------------------------------------------- |
| `MODULE_NOT_FOUND` 错误 | 依赖未安装或缺少新包      | 确认 `package.json` / `requirements.txt` 中已声明依赖，重新执行脚本                      |
| `EADDRINUSE` 端口被占用    | 3089 端口已被占用     | 修改 PM2 启动环境变量 `PORT`，或释放该端口                                               |
| 脚本提示 `权限不足`           | 未用 root 或 sudo  | 使用 `sudo ./deploy_backend.sh`                                             |
| 拉取代码冲突                | 本地改动未提交         | 先提交或 `git reset --hard` 后重试                                               |
| 服务无法访问                | Nginx 未配置反代或未重载 | 检查 `/etc/nginx/sites-enabled/jxpd.conf` 并执行 `sudo systemctl reload nginx` |

---

## 日常维护

* **查看版本**

```bash
cd /root/jxpd/jn-jxpd-server && git log --oneline -n 1
```

* **实时监控日志**

```bash
pm2 logs jxpd-server
```

* **停止服务**

```bash
pm2 stop jxpd-server
```

* **重启服务**

```bash
pm2 restart jxpd-server
```

````

---

我建议你这个 `backend_guide.md` 跟 `deploy_backend.sh` 放在 **后端项目根目录**，这样以后只需要：  
```bash
./deploy_backend.sh
````

就可以完成 **拉取 + 安装依赖 + 构建 + 重启** 了。
