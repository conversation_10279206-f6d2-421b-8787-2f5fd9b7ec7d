import request from '../server'

// API 路径配置
const api = {
  list: '/researchFundsRules/list',
  detail: '/researchFundsRules/detail',
  create: '/researchFundsRules/create',
  update: '/researchFundsRules/update',
  delete: '/researchFundsRules/delete',
  batchDelete: '/researchFundsRules/batch-delete',
  import: '/researchFundsRules/import',
  export: '/researchFundsRules/export'
}

/**
 * 获取科研经费规则列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getResearchFundsRules(params) {
  const { page = 1, pageSize = 10, fundLevel } = params || {};
  
  // 构造参数对象
  const queryParams = {
    page,
    pageSize,
    fundLevel
  };
  
  return request.get(api.list, queryParams);
}

/**
 * 获取科研经费规则详情
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function getResearchFundsRuleDetail(id) {
  return request.get(api.detail, { id });
}

/**
 * 创建科研经费规则
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function addResearchFundsRule(data) {
  return request.post(api.create, data);
}

/**
 * 更新科研经费规则
 * @param {string} id - 规则ID
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateResearchFundsRule(id, data) {
  return request.put(api.update, { id, ...data });
}

/**
 * 删除科研经费规则
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteResearchFundsRule(id) {
  return request.delete(api.delete, { id });
}

/**
 * 批量删除科研经费规则
 * @param {Array} ids - 规则ID数组
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteResearchFundsRules(ids) {
  return request.delete(api.batchDelete, { ids });
}

/**
 * 导入科研经费规则数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回Promise对象
 */
export function importResearchFundsRules(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request.post(api.import, formData, null, 'multipart/form-data');
}

/**
 * 导出科研经费规则数据
 * @param {Object} params - 过滤参数
 * @returns {Promise} - 返回Promise对象
 */
export function exportResearchFundsRules(params) {
  return request.get(api.export, params, { responseType: 'blob' });
} 