const rateLimit = require('express-rate-limit');
const { TooManyRequestsError } = require('../utils/errors');

/**
 * 创建限流中间件
 * @param {Object} options - 限流配置选项
 * @returns {Function} 限流中间件
 */
const createRateLimiter = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15分钟
    max = 100, // 限制每个IP在windowMs内最多100个请求
    message = '请求过于频繁，请稍后再试',
    statusCode = 429
  } = options;

  return rateLimit({
    windowMs,
    max,
    handler: (req, res) => {
      throw new TooManyRequestsError(message);
    },
    standardHeaders: true,
    legacyHeaders: false
  });
};

module.exports = {
  createRateLimiter
}; 