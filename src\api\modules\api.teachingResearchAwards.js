import service from '../server'

// 教学科技奖励相关接口
const api = {
  list: '/teaching-research-awards/list',
  detail: '/teaching-research-awards/detail',
  create: '/teaching-research-awards/create',
  update: '/teaching-research-awards/update',
  delete: '/teaching-research-awards/delete',
  review: '/teaching-research-awards/review',
  reapply: '/teaching-research-awards/reapply',
  export: '/teaching-research-awards/export',
  import: '/teaching-research-awards/import',
  // 级别相关
  levelsList: '/teaching-research-award-levels/list',
  levelsAll: '/teaching-research-award-levels/levels',
  levelsCreate: '/teaching-research-award-levels/create',
  levelsUpdate: '/teaching-research-award-levels/update',
  levelsDelete: '/teaching-research-award-levels/delete',
  // 排行榜相关
  teacherRanking: '/teaching-research-awards/teacher-ranking',
  // 统计分析相关
  totalScoreStats: '/teaching-research-awards/stats/total-score',
  userAwardsDetail: '/teaching-research-awards/user-awards-detail',
  // 图表统计相关
  reviewStatusStats: '/teaching-research-awards/stats/review-status',
  levelStats: '/teaching-research-awards/stats/level',
  yearStats: '/teaching-research-awards/stats/year',
  departmentStats: '/teaching-research-awards/stats/department'
}

// 获取教学科技奖励列表
export const getTeachingResearchAwardsList = (data) => {
  return service.post(api.list, data)
}

// 获取教学科技奖励详情
export const getTeachingResearchAwardDetail = (data) => {
  return service.post(api.detail, data)
}

// 创建教学科技奖励
export const createTeachingResearchAward = (data) => {
  return service.post(api.create, data)
}

// 更新教学科技奖励
export const updateTeachingResearchAward = (data) => {
  const { id, ...updateData } = data
  return service.post(`${api.update}/${id}`, updateData)
}

// 删除教学科技奖励
export const deleteTeachingResearchAward = (id) => {
  return service.delete(`${api.delete}/${id}`)
}

// 审核教学科技奖励
export const reviewTeachingResearchAward = (data) => {
  return service.post(api.review, data)
}

// 重新提交审核
export const reapplyTeachingResearchAward = (id) => {
  return service.post(`${api.reapply}/${id}`)
}

// 导出教学科技奖励
export const exportTeachingResearchAwards = (data) => {
  return service.post(api.export, data)
}

// 导入教学科技奖励
export const importTeachingResearchAwards = (data) => {
  return service.post(api.import, data)
}

// 获取奖励级别列表（分页）
export const getAwardLevelsList = (data) => {
  return service.post(api.levelsList, data)
}

// 获取所有启用的奖励级别
export const getAwardLevels = () => {
  return service.get(api.levelsAll)
}

// 创建奖励级别
export const createAwardLevel = (data) => {
  return service.post(api.levelsCreate, data)
}

// 更新奖励级别
export const updateAwardLevel = (data) => {
  return service.post(api.levelsUpdate, data)
}

// 删除奖励级别
export const deleteAwardLevel = (data) => {
  return service.post(api.levelsDelete, data)
}

// ==================== 排行榜相关接口 ====================

// 获取教师教学科研奖励排名
export const getTeacherAwardRanking = (data) => {
  return service.post(api.teacherRanking, data)
}

// ==================== 统计分析相关接口 ====================

// 获取教学科研奖励总分统计
export const getTeachingResearchAwardsTotalScore = (data) => {
  return service.post(api.totalScoreStats, data)
}

// 获取用户教学科研奖励详情列表及得分
export const getUserAwardsDetail = (data) => {
  return service.post(api.userAwardsDetail, data)
}

/**
 * 获取用户奖励详情列表
 * @param {Object} params - 查询参数
 * @param {string} params.userId - 用户ID
 * @param {string} params.range - 范围：in/out/all
 * @param {string} params.reviewStatus - 审核状态：reviewed/pending/rejected/all
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} - 返回Promise对象
 */
export function getUserAwardDetails(params) {
  return service.post(api.userAwardsDetail, params)
}

// ==================== 图表统计相关接口 ====================

/**
 * 获取审核状态分布统计
 * @param {Object} data - 查询参数
 * @param {string} data.range - 范围：in/out/all
 * @returns {Promise} - 返回Promise对象
 */
export function getReviewStatusDistribution(data) {
  return service.post(api.reviewStatusStats, data)
}

/**
 * 获取奖励级别分布统计
 * @param {Object} data - 查询参数
 * @param {string} data.range - 范围：in/out/all
 * @returns {Promise} - 返回Promise对象
 */
export function getAwardLevelDistribution(data) {
  return service.post(api.levelStats, data)
}

/**
 * 获取年度奖励分布统计
 * @param {Object} data - 查询参数
 * @param {string} data.range - 范围：in/out/all
 * @returns {Promise} - 返回Promise对象
 */
export function getYearDistribution(data) {
  return service.post(api.yearStats, data)
}

/**
 * 获取系/教研室分布统计
 * @param {Object} data - 查询参数
 * @param {string} data.range - 范围：in/out/all
 * @returns {Promise} - 返回Promise对象
 */
export function getDepartmentDistribution(data) {
  return service.post(api.departmentStats, data)
}
