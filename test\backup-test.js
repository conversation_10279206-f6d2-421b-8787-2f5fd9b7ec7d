/**
 * 备份功能测试脚本
 */

const { runBackupProcess, getBackupList, checkMysqldumpAvailable } = require('../utils/databaseBackup');
const { scheduler } = require('../utils/scheduler');

async function testBackupFunctionality() {
    console.log('=== 开始测试备份功能 ===');
    console.log(`操作系统: ${process.platform}`);
    console.log(`Node.js版本: ${process.version}`);
    
    try {
        // 0. 检查环境
        console.log('\n0. 检查备份环境...');
        try {
            const mysqldumpAvailable = await checkMysqldumpAvailable();
            console.log(`mysqldump可用性: ${mysqldumpAvailable ? '✅ 可用' : '❌ 不可用，将使用Node.js备份'}`);
        } catch (error) {
            console.log('环境检查失败:', error.message);
        }
        
        // 1. 测试获取备份列表
        console.log('\n1. 测试获取备份列表...');
        const backupList = getBackupList();
        console.log(`当前备份文件数量: ${backupList.length}`);
        if (backupList.length > 0) {
            console.log('最新备份文件:', backupList[0].fileName);
            console.log('文件大小:', backupList[0].sizeFormatted);
        }
        
        // 2. 测试手动备份
        console.log('\n2. 测试手动备份...');
        const backupResult = await runBackupProcess();
        
        if (backupResult.success) {
            console.log('✅ 备份成功!');
            console.log('备份文件路径:', backupResult.backupFilePath);
            console.log('当前备份文件总数:', backupResult.backupList.length);
        } else {
            console.log('❌ 备份失败:', backupResult.error);
            console.log('错误详情:', backupResult.message);
        }
        
        // 3. 测试定时任务状态
        console.log('\n3. 测试定时任务状态...');
        try {
            const taskStatus = scheduler.getAllTasksStatus();
            console.log('定时任务状态:', JSON.stringify(taskStatus, null, 2));
        } catch (error) {
            console.log('获取定时任务状态失败:', error.message);
        }
        
        // 4. 测试手动执行备份任务
        console.log('\n4. 测试手动执行备份任务...');
        try {
            const manualResult = await scheduler.executeBackupNow();
            
            if (manualResult.success) {
                console.log('✅ 手动备份任务执行成功!');
            } else {
                console.log('❌ 手动备份任务执行失败:', manualResult.error);
            }
        } catch (error) {
            console.log('手动备份任务执行异常:', error.message);
        }
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        console.error('错误堆栈:', error.stack);
    }
    
    console.log('\n=== 备份功能测试完成 ===');
}

// 如果直接运行此脚本
if (require.main === module) {
    testBackupFunctionality();
}

module.exports = { testBackupFunctionality };
