const express = require('express');
const notificationsController = require('../../../controllers/v1/common/notificationsController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

const router = express.Router();

// 创建消息通知权限中间件函数
const notificationsPermission = (action) => createModulePermission('notifications', action);

/**
 * 获取通知列表
 * @route GET /v1/sys/notifications
 * @group 通知管理 - 通知相关接口
 * @param {string} type.query - 通知类型
 * @param {boolean} is_read.query - 是否已读
 * @param {string} user_id.query - 用户ID
 * @returns {object} 200 - 成功返回通知列表
 * @security JWT
 */
router.get('/',
  authMiddleware,
  notificationsPermission('list'),
  notificationsController.getNotifications
);

/**
 * 获取部门列表
 * @route GET /v1/sys/notifications/departments
 * @group 通知管理 - 通知相关接口
 * @returns {object} 200 - 成功返回部门列表
 * @security JWT
 */
router.get('/departments',
  authMiddleware,
  notificationsPermission('getDepartments'),
  notificationsController.getDepartments
);

/**
 * 获取用户级别列表
 * @route GET /v1/sys/notifications/user-levels
 * @group 通知管理 - 通知相关接口
 * @returns {object} 200 - 成功返回用户级别列表
 * @security JWT
 */
router.get('/user-levels',
  authMiddleware,
  notificationsPermission('getUserLevels'),
  notificationsController.getUserLevels
);

/**
 * 批量标记为已读
 * @route PUT /v1/sys/notifications/batch/read
 * @group 通知管理 - 通知相关接口
 * @param {array} notification_ids.body - 通知ID数组
 * @returns {object} 200 - 成功批量标记为已读
 * @security JWT
 */
router.put('/batch/read',
  authMiddleware,
  notificationsPermission('batchMarkAsRead'),
  notificationsController.batchMarkAsRead
);

/**
 * 获取通知详情
 * @route GET /v1/sys/notifications/:id
 * @group 通知管理 - 通知相关接口
 * @param {integer} id.path - 通知ID
 * @returns {object} 200 - 成功返回通知详情
 * @security JWT
 */
router.get('/:id',
  authMiddleware,
  notificationsPermission('detail'),
  notificationsController.getNotificationById
);

/**
 * 创建通知（仅管理员可用）
 * @route POST /v1/sys/notifications
 * @group 通知管理 - 通知相关接口
 * @param {string} type.body - 通知类型
 * @param {string} title.body - 通知标题
 * @param {string} content.body - 通知内容
 * @param {string} abstract.body - 通知摘要
 * @param {string} target_type.body - 发送目标类型（all/department/user_level）
 * @param {string} department.body - 部门名称（按部门发送时使用）
 * @param {string} user_level.body - 用户级别ID（按用户级别发送时使用）
 * @param {string} initiator_id.body - 发起人ID
 * @returns {object} 200 - 成功创建通知
 * @security JWT
 */
router.post('/',
  authMiddleware,
  notificationsPermission('send'),
  notificationsController.createNotification
);

/**
 * 更新通知
 * @route PUT /v1/sys/notifications/:id
 * @group 通知管理 - 通知相关接口
 * @param {integer} id.path - 通知ID
 * @param {boolean} is_read.body - 是否已读
 * @returns {object} 200 - 成功更新通知
 * @security JWT
 */
router.put('/:id',
  authMiddleware,
  notificationsPermission('update'),
  notificationsController.updateNotification
);

/**
 * 删除通知
 * @route DELETE /v1/sys/notifications/:id
 * @group 通知管理 - 通知相关接口
 * @param {integer} id.path - 通知ID
 * @returns {object} 200 - 成功删除通知
 * @security JWT
 */
router.delete('/:id',
  authMiddleware,
  notificationsPermission('delete'),
  notificationsController.deleteNotification
);

module.exports = router;