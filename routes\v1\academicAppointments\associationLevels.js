const express = require('express');
const router = express.Router();
const associationLevelController = require('../../../controllers/v1/academicAppointments/associationLevelsController');

/**
 * 获取学术任职级别列表
 * @route GET /v1/sys/association-levels/levels
 * @group 学术任职级别管理 - 学术任职级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels', associationLevelController.getAssociationLevels);

/**
 * 获取所有级别及其学术任职数量
 * @route GET /v1/sys/association-levels/levels-with-count
 * @group 学术任职级别管理 - 学术任职级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id, levelName, score, appointmentCount},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels-with-count', associationLevelController.getLevelsWithCount);

/**
 * 获取学术任职级别详情
 * @route GET /v1/sys/association-levels/level/:id
 * @group 学术任职级别管理 - 学术任职级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/:id', associationLevelController.getAssociationLevelDetail);

/**
 * 创建学术任职级别
 * @route POST /v1/sys/association-levels/level/create
 * @group 学术任职级别管理 - 学术任职级别相关接口
 * @param {string} levelName.body.required - 级别名称
 * @param {string} score.body.required - 分数
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/create', associationLevelController.createAssociationLevel);

/**
 * 更新学术任职级别
 * @route POST /v1/sys/association-levels/level/update
 * @group 学术任职级别管理 - 学术任职级别相关接口
 * @param {string} id.body.required - 级别ID
 * @param {string} levelName.body - 级别名称
 * @param {string} score.body - 分数
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/update', async (req, res) => {
  const { id, ...updateData } = req.body;
  req.params = { id };
  req.body = updateData;
  await associationLevelController.updateAssociationLevel(req, res);
});

/**
 * 删除学术任职级别
 * @route POST /v1/sys/association-levels/level/delete
 * @group 学术任职级别管理 - 学术任职级别相关接口
 * @param {string} id.body.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/delete', async (req, res) => {
  const { id } = req.body;
  req.params = { id };
  await associationLevelController.deleteAssociationLevel(req, res);
});

module.exports = router; 