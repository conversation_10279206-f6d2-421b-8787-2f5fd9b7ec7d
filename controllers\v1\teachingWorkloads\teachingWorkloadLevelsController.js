const { v4: uuidv4 } = require('uuid');
const teachingWorkloadLevelsModel = require('../../../models/v1/mapping/teachingWorkloadLevelsModel');
const { Op } = require('sequelize');
const { getUserInfoFromRequest } = require('../../../utils/others');

/**
 * 获取教学工作量级别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getWorkloadLevels = async (req, res) => {
    try {
        console.log('🔍 获取教学工作量级别列表 - 请求参数:', req.query);

        // 获取查询参数
        const { page = 1, pageSize = 10, categoryName } = req.query;

        // 确保 page 和 pageSize 是有效的数字，否则使用默认值
        const pageNum = page ? parseInt(page) : 1;
        const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

        // 如果 page 或 pageSize 是无效数字，返回默认值
        const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
        const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;

        console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

        // 构建查询条件
        const where = {};
        if (categoryName) {
            where.categoryName = { [Op.like]: `%${categoryName}%` };
        }

        console.log('🔍 查询条件:', where);

        // 分页参数
        const offset = (validPage - 1) * validPageSize;
        const limit = validPageSize;

        // 查询级别列表
        const { count, rows } = await teachingWorkloadLevelsModel.findAndCountAll({
            where,
            offset,
            limit,
            order: [['createdAt', 'DESC']]
        });

        console.log(`📋 查询结果: 总数 ${count}, 当前页数据 ${rows.length} 条`);

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: {
                list: rows,
                pagination: {
                    total: count,
                    page: validPage,
                    pageSize: validPageSize,
                    totalPages: Math.ceil(count / validPageSize)
                }
            }
        });
    } catch (error) {
        console.error('❌ 获取教学工作量级别列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取教学工作量级别列表失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取所有教学工作量级别（不分页）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllWorkloadLevels = async (req, res) => {
    try {
        console.log('🔍 获取所有教学工作量级别');

        // 查询所有启用的级别
        const levels = await teachingWorkloadLevelsModel.findAll({
            where: {
                status: 1 // 只查询启用的级别
            },
            order: [['score', 'DESC'], ['createdAt', 'DESC']]
        });

        console.log(`📋 查询结果: ${levels.length} 条级别数据`);

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: levels
        });
    } catch (error) {
        console.error('❌ 获取所有教学工作量级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取所有教学工作量级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取教学工作量级别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getWorkloadLevelDetail = async (req, res) => {
    try {
        const { id } = req.params;
        console.log(`🔍 获取教学工作量级别详情 - ID: ${id}`);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: '缺少级别ID',
                data: null
            });
        }

        // 查询级别详情
        const level = await teachingWorkloadLevelsModel.findByPk(id);

        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '级别不存在',
                data: null
            });
        }

        console.log(`✅ 查询成功: 级别 ${level.categoryName}`);

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: level
        });
    } catch (error) {
        console.error('❌ 获取教学工作量级别详情失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取教学工作量级别详情失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 创建教学工作量级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createWorkloadLevel = async (req, res) => {
    try {
        const { categoryName, score, description, status } = req.body;
        console.log('🔧 创建教学工作量级别:', { categoryName, score, description, status });

        // 验证必填字段
        if (!categoryName) {
            return res.status(400).json({
                code: 400,
                message: '类别名称不能为空',
                data: null
            });
        }

        // 检查类别名称是否已存在
        const existingLevel = await teachingWorkloadLevelsModel.findOne({
            where: { categoryName }
        });

        if (existingLevel) {
            return res.status(400).json({
                code: 400,
                message: '类别名称已存在',
                data: null
            });
        }

        const userInfo = await getUserInfoFromRequest(req);
        const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');

        if (!isAdmin) {
            return res.status(403).json({
              code: 403,
              message: '您没有权限创建教学工作量级别',
              data: null
            });
          }

        // 创建级别
        const level = await teachingWorkloadLevelsModel.create({
            id: uuidv4(),
            categoryName,
            score: score || 0,
            description,
            status: status !== undefined ? status : 1
        });

        console.log(`✅ 创建成功: 级别ID ${level.id}`);

        return res.status(201).json({
            code: 200,
            message: '创建成功',
            data: level
        });
    } catch (error) {
        console.error('❌ 创建教学工作量级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `创建教学工作量级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 更新教学工作量级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateWorkloadLevel = async (req, res) => {
    try {
        const { id } = req.params;
        const { categoryName, score, description, status } = req.body;
        console.log(`🔧 更新教学工作量级别 - ID: ${id}`, { categoryName, score, description, status });

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: '缺少级别ID',
                data: null
            });
        }

        // 查询级别是否存在
        const level = await teachingWorkloadLevelsModel.findByPk(id);

        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '级别不存在',
                data: null
            });
        }

        // 如果更新类别名称，检查是否与其他级别重复
        if (categoryName && categoryName !== level.categoryName) {
            const existingLevel = await teachingWorkloadLevelsModel.findOne({
                where: {
                    categoryName,
                    id: { [Op.ne]: id }
                }
            });

            if (existingLevel) {
                return res.status(400).json({
                    code: 400,
                    message: '类别名称已存在',
                    data: null
                });
            }
        }

        const userInfo = await getUserInfoFromRequest(req);
        const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');

        if (!isAdmin) {
            return res.status(403).json({
              code: 403,
              message: '您没有权限更新教学工作量级别',
              data: null
            });
          }

        // 更新级别
        await level.update({
            categoryName: categoryName || level.categoryName,
            score: score !== undefined ? score : level.score,
            description: description !== undefined ? description : level.description,
            status: status !== undefined ? status : level.status
        });

        console.log(`✅ 更新成功: 级别ID ${id}`);

        return res.status(200).json({
            code: 200,
            message: '更新成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 更新教学工作量级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `更新教学工作量级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 删除教学工作量级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteWorkloadLevel = async (req, res) => {
    try {
        const { id } = req.params;
        console.log(`🗑️ 删除教学工作量级别 - ID: ${id}`);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: '缺少级别ID',
                data: null
            });
        }

        // 查询级别是否存在
        const level = await teachingWorkloadLevelsModel.findByPk(id);

        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '级别不存在',
                data: null
            });
        }

        const userInfo = await getUserInfoFromRequest(req);
        const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');

        if (!isAdmin) {
            return res.status(403).json({
              code: 403,
              message: '您没有权限删除教学工作量级别',
              data: null
            });
          }

        // 删除级别
        await level.destroy();

        console.log(`✅ 删除成功: 级别ID ${id}`);

        return res.status(200).json({
            code: 200,
            message: '删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 删除教学工作量级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `删除教学工作量级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 批量删除教学工作量级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteWorkloadLevels = async (req, res) => {
    try {
        const { ids } = req.body;
        console.log('🗑️ 批量删除教学工作量级别:', ids);

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                code: 400,
                message: '请提供要删除的级别ID列表',
                data: null
            });
        }

        const userInfo = await getUserInfoFromRequest(req);
        const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');

        if (!isAdmin) {
            return res.status(403).json({
              code: 403,
              message: '您没有权限批量删除教学工作量级别',
              data: null
            });
          }

        // 批量删除级别
        const deletedCount = await teachingWorkloadLevelsModel.destroy({
            where: {
                id: { [Op.in]: ids }
            }
        });

        console.log(`✅ 批量删除成功: 删除了 ${deletedCount} 个级别`);

        return res.status(200).json({
            code: 200,
            message: `批量删除成功，共删除 ${deletedCount} 个级别`,
            data: { deletedCount }
        });
    } catch (error) {
        console.error('❌ 批量删除教学工作量级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `批量删除教学工作量级别失败: ${error.message}`,
            data: null
        });
    }
};
