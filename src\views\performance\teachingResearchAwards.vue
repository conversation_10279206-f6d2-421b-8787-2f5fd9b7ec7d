<template>
  <div class="performance-container teaching-research-awards-container">
    <a-card title="教学科研奖励管理" :bordered="false" class="performance-card">
      <template #extra>
        <a-space>
          <!-- Excel 数据导入按钮 -->
          <a-upload
            :customRequest="handleExcelToJsonConvert"
            :show-upload-list="false"
            :before-upload="beforeExcelUpload"
            accept=".xlsx,.xls,.csv"
            v-permission="'score:teachingResearchAwards:admin:update'"
          >
            <a-button type="primary" ghost>
              <template #icon><FileExcelOutlined /></template>
              Excel数据导入
            </a-button>
          </a-upload>

          <a-button type="primary" @click="handleCreate" v-permission="'score:teachingResearchAwards:create'">
            <template #icon><PlusOutlined /></template>
            新增奖励
          </a-button>
          <a-button :type="showPersonalAwards ? 'default' : 'primary'" @click="togglePersonalAwards" v-permission="'score:teachingResearchAwards:admin'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalAwards ? '查看全部奖励' : '查看我的奖励' }}
          </a-button>
        </a-space>
      </template>
      
      <!-- 教学和科技奖励填写说明区域 -->
      <a-card title="教学和科技奖励填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
        <a-alert
          class="mb-16"
          message="教学科研奖励统计时间范围"
          :description="`统计时间：${timeRangeText || '加载中...'}`"
          type="info"
          show-icon
        />
        <div class="rule-content">
          <p><strong>填写说明：</strong></p>
          <ol class="detail-list">
            <li>教学和科技奖励获奖时间范围在统计时间内</li>
            <li>此表由第一负责人为我院的教职工填写，各获奖者比例由第一负责人分配，第一负责人非我院教职工不填写</li>
            <li>此表中出现的姓名全部为我院教职工</li>
            <li>分配比例请填写小数，不要填写百分数</li>
            <li>"奖励名称/等级"已设置下拉菜单，选择即可</li>
            <li>"对应分数"、"总分配比例"已内置公式，请勿更改</li>
            <li>总分配比例应为1</li>
            <li>需提供写有获奖时间的获奖证书备查</li>
          </ol>
        </div>
      </a-card>

      <!-- 个人统计卡片 -->
      <a-row :gutter="16" style="margin-bottom: 24px" v-if="showPersonalAwards">
        <a-col :span="24">
          <a-card :bordered="false">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic
                  title="我的总得分"
                  :value="personalStats.totalScore || 0"
                  :precision="2"
                  :value-style="{ fontSize: '24px', color: '#3f8600' }"
                >
                  <template #prefix>
                    <TrophyOutlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="奖励总数"
                  :value="personalStats.awardCount || 0"
                  :value-style="{ fontSize: '24px', color: '#1890ff' }"
                >
                  <template #prefix>
                    <CrownOutlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="院内排名"
                  :value="personalStats.rank || '--'"
                  :value-style="{ fontSize: '24px', color: '#cf1322' }"
                >
                  <template #suffix>
                    <span>/ {{ personalStats.totalUsers || '--' }}</span>
                  </template>
                  <template #prefix>
                    <RiseOutlined />
                  </template>
                </a-statistic>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <!-- 图表区域 -->
      <a-row :gutter="16" style="margin-bottom: 24px">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-card :bordered="false" title="审核状态分布" size="small" class="performance-card chart-container">
            <template #extra>
              <a-select
                v-model:value="reviewStatusChartRange"
                size="small"
                style="width: 100px;"
                @change="(value) => changeChartRange('reviewStatus', value)"
              >
                <a-select-option value="in">范围内</a-select-option>
                <a-select-option value="out">范围外</a-select-option>
                <a-select-option value="all">全部</a-select-option>
              </a-select>
            </template>
            <div ref="reviewStatusChartRef" id="reviewStatusChartContainer" class="chart-wrapper"></div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-card :bordered="false" title="奖励级别分布" size="small" class="performance-card chart-container">
            <template #extra>
              <a-select
                v-model:value="levelChartRange"
                size="small"
                style="width: 100px;"
                @change="(value) => changeChartRange('level', value)"
              >
                <a-select-option value="in">范围内</a-select-option>
                <a-select-option value="out">范围外</a-select-option>
                <a-select-option value="all">全部</a-select-option>
              </a-select>
            </template>
            <div ref="levelChartRef" id="levelChartContainer" class="chart-wrapper"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-bottom: 24px">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-card :bordered="false" title="年度奖励分布" size="small" class="performance-card chart-container">
            <template #extra>
              <a-select
                v-model:value="yearChartRange"
                size="small"
                style="width: 100px;"
                @change="(value) => changeChartRange('year', value)"
              >
                <a-select-option value="in">范围内</a-select-option>
                <a-select-option value="out">范围外</a-select-option>
                <a-select-option value="all">全部</a-select-option>
              </a-select>
            </template>
            <div ref="yearChartRef" id="yearChartContainer" class="chart-wrapper"></div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-card :bordered="false" title="系/教研室分布" size="small" class="performance-card chart-container">
            <template #extra>
              <a-select
                v-model:value="departmentChartRange"
                size="small"
                style="width: 100px;"
                @change="(value) => changeChartRange('department', value)"
              >
                <a-select-option value="in">范围内</a-select-option>
                <a-select-option value="out">范围外</a-select-option>
                <a-select-option value="all">全部</a-select-option>
              </a-select>
            </template>
            <div ref="departmentChartRef" id="departmentChartContainer" class="chart-wrapper"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 用户奖励得分统计表格 -->
      <a-card title="用户奖励得分统计" :bordered="false" style="margin-bottom: 24px;">
        <template #extra>
          <a-space>
            <a-input-search
              v-model:value="userScoreSearchParams.nickname"
              v-permission="'score:teachingResearchAwards:admin:list'"
              placeholder="用户昵称"
              style="width: 150px;"
              @search="fetchAllUsersTotalScore"
              @pressEnter="fetchAllUsersTotalScore"
            />
            <a-select
              v-model:value="userScoreChartRange"
              style="width: 150px;"
              @change="handleUserScoreRangeChange"
            >
              <a-select-option value="in">统计范围内</a-select-option>
              <a-select-option value="out">统计范围外</a-select-option>
              <a-select-option value="all">全部奖励</a-select-option>
            </a-select>
            <a-button type="primary" @click="exportUserScoreData" :loading="exportingTable" v-permission="'score:teachingResearchAwards:admin:list'">
              <template #icon><DownloadOutlined /></template>
              导出
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="userScoreColumns"
          :data-source="userScoreData"
          :pagination="userScorePagination"
          :loading="userScoreLoading"
          rowKey="userId"
          @change="handleUserScoreTableChange"
          :scroll="{ x: 800 }"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'rank'">
              <a-tag :color="getRankColor(record.rank)">{{ record.rank }}</a-tag>
            </template>
            <template v-else-if="column.key === 'totalScore'">
              <span style="font-weight: bold; color: #1890ff;">{{ record.totalScore ? parseFloat(record.totalScore).toFixed(2) : '0.00' }}分</span>
            </template>
            <template v-else-if="column.key === 'details'">
              <a-button 
              v-if="currentRole?.roleAuth === 'SUPER' || currentRole?.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId"
              type="link" @click="showUserScoreDetails(record)">
                查看详情
              </a-button>
            </template>
          </template>
        </a-table>
      </a-card>

      <!-- 搜索表单 -->
      <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
        <a-form :model="searchForm" @finish="handleSearch" layout="vertical" class="performance-form">
          <a-row :gutter="[12, 8]">
            <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
              <a-form-item label="获奖名称" name="awardName">
                <a-input
                  v-model:value="searchForm.awardName"
                  placeholder="请输入获奖名称"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
              <a-form-item label="奖励级别" name="awardLevelId">
                <a-select
                  v-model:value="searchForm.awardLevelId"
                  placeholder="请选择奖励级别"
                  style="width: 100%"
                  allow-clear
                  :loading="levelLoading"
                >
                  <a-select-option
                    v-for="level in awardLevels"
                    :key="level.id"
                    :value="level.id"
                  >
                    {{ level.levelName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4" v-permission="'score:teachingResearchAwards:admin:list'">
              <a-form-item label="负责人" name="leader">
                <a-select
                  v-model:value="searchForm.leader"
                  show-search
                  placeholder="请输入负责人姓名或工号"
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="leaderSearchLoading ? undefined : null"
                  :options="leaderOptions"
                  @search="handleLeaderSearch"
                  @change="handleLeaderChange"
                  allow-clear
                  style="width: 100%"
                >
                  <template #notFoundContent>
                    <a-spin v-if="leaderSearchLoading" size="small" />
                    <div v-else>未找到匹配的用户</div>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
              <a-form-item label="获奖时间" name="dateRange">
                <a-range-picker
                  v-model:value="searchForm.dateRange"
                  :format="'YYYY-MM-DD'"
                  style="width: 100%"
                  :placeholder="['开始日期', '结束日期']"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4" v-permission="'score:teachingResearchAwards:admin:list'">
              <a-form-item label="系/教研室" name="department">
                <a-input
                  v-model:value="searchForm.department"
                  placeholder="请输入系/教研室"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
              <a-form-item label="审核状态" name="ifReviewer">
                <a-select
                  v-model:value="searchForm.ifReviewer"
                  style="width: 100%"
                  allow-clear
                >
                  <a-select-option value="all">全部奖励</a-select-option>
                  <a-select-option value="reviewed">已审核</a-select-option>
                  <a-select-option value="rejected">已拒绝</a-select-option>
                  <a-select-option value="pending">待审核</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
              <a-form-item label="统计范围" name="range">
                <a-select
                  v-model:value="searchForm.range"
                  style="width: 100%"
                  allow-clear
                >
                  <a-select-option value="in">统计范围内</a-select-option>
                  <a-select-option value="out">统计范围外</a-select-option>
                  <a-select-option value="all">全部奖励</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
              <a-form-item label=" " style="margin-bottom: 0;">
                <div class="search-actions-inline">
                  <a-button type="primary" html-type="submit" size="default">
                    <template #icon><SearchOutlined /></template>
                    搜索
                  </a-button>
                  <a-button @click="handleReset" size="default">
                    <template #icon><ReloadOutlined /></template>
                    重置
                  </a-button>
                  <a-button type="default" @click="handleExport" :loading="exportingTable" size="default" v-permission="'score:teachingResearchAwards:admin:list'">
                    <template #icon><DownloadOutlined /></template>
                    导出
                  </a-button>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 数据表格 -->
      <div class="performance-table">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
          :scroll="{ x: 1200 }"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'score'">
              <span v-if="isAwardInScoreRange(record)" style="font-weight: bold; color: #1890ff;">{{ record.awardLevel?.score ? parseFloat(record.awardLevel.score).toFixed(2) : '0.00' }}分</span>
              <span v-else style="color: #999999;">不计分</span>
            </template>
            <template v-else-if="column.key === 'participants'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ formatParticipantsWithAllocation(record.participants, record) }}</span>
            </template>
            <template v-else-if="column.key === 'awardName'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ record.awardName }}</span>
            </template>
            <template v-else-if="column.key === 'awardLevel'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ formatAwardLevel(record.awardLevel, record) }}</span>
            </template>
            <template v-else-if="column.key === 'awardTime'">
              {{ formatDate(record.awardTime) }}
            </template>
            <template v-else-if="column.key === 'reviewStatus'">
              <a-tag :color="getReviewStatusColor(record.ifReviewer)">
                {{ getReviewStatusText(record.ifReviewer) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'reviewComment'">
              <a-tooltip v-if="record.reviewComment" :title="record.reviewComment">
                <span class="ellipsis-text">{{ record.reviewComment || '-' }}</span>
              </a-tooltip>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'submitter'">
              <span>{{ record.submitter ? (record.submitter.nickname || record.submitter.username || '未知') : '未设置' }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-dropdown placement="bottomRight" :trigger="['click']">
                <a-button type="link" size="small" class="action-trigger-btn">
                  操作
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu class="action-dropdown-menu">
                    <!-- 编辑选项 -->
                    <a-menu-item
                      key="edit"
                      v-if="record.ifReviewer != 1 && hasPerms('score:teachingResearchAwards:self:update')"
                    >
                      <a @click="handleEdit(record)" class="action-menu-item">
                        <EditOutlined />
                        <span>编辑</span>
                      </a>
                    </a-menu-item>

                    <!-- 重新提交审核选项 -->
                    <a-menu-item
                      key="resubmit"
                      v-if="record.ifReviewer == 0 && hasPerms('score:teachingResearchAwards:self:reapply')"
                    >
                      <a @click="handleResubmit(record)" class="action-menu-item">
                        <ReloadOutlined />
                        <span>重新提交审核</span>
                      </a>
                    </a-menu-item>

                    <!-- 审核选项 - 仅管理员视图显示 -->
                    <a-menu-item
                      key="review"
                      v-if="record.ifReviewer != 1 && hasPerms('score:teachingResearchAwards:admin:review')"
                    >
                      <a @click="handleReview(record)" class="action-menu-item">
                        <AuditOutlined />
                        <span>审核</span>
                      </a>
                    </a-menu-item>

                    <a-menu-divider v-if="record.ifReviewer != 1 || record.ifReviewer == 0 || (!showPersonalAwards && record.ifReviewer === null)" />

                    <!-- 删除选项 -->
                    <a-menu-item
                      key="delete"
                      v-if="hasPerms('score:teachingResearchAwards:self:delete')"
                    >
                      <a @click="handleDelete(record)" class="action-menu-item text-danger">
                        <DeleteOutlined />
                        <span>删除</span>
                      </a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </template>
        </a-table>
      </div>

      <div class="table-footer">
        <div class="total-score">
          <span>总分：{{ typeof totalScore === 'number' ? totalScore.toFixed(2) : '0.00' }}分</span>
        </div>
      </div>

    <!-- 新增/编辑弹窗 -->
    <TeachingResearchAwardForm
      v-model:visible="formVisible"
      :form-data="currentRecord"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 详情弹窗 -->
    <TeachingResearchAwardDetail
      v-model:visible="detailVisible"
      :award-id="currentRecord?.id"
    />

    <!-- 审核弹窗 -->
    <TeachingResearchAwardReview
      v-model:visible="reviewVisible"
      :award-id="currentRecord?.id"
      @success="handleReviewSuccess"
    />

    <!-- Excel 导入预览模态框 -->
    <a-modal
      v-model:visible="importPreviewVisible"
      title="Excel 导入预览"
      width="90%"
      :maskClosable="false"
      :footer="null"
      @cancel="handleCancelImportPreview"
    >
      <template v-if="importPreviewLoading">
        <div style="text-align: center; padding: 40px;">
          <a-spin size="large" />
          <p style="margin-top: 20px;">正在解析数据，请稍候...</p>
        </div>
      </template>
      <template v-else>
        <div style="margin-bottom: 16px;">
          <a-alert
            :type="userIdCheckResults.notFound > 0 ? 'warning' : 'success'"
            :message="userIdCheckResults.notFound > 0 ?
              `存在${userIdCheckResults.notFound}个用户ID未找到，这些记录可能导入失败` :
              '所有用户ID均已找到'"
            show-icon
          />
          <div style="margin-top: 8px;">
            <a-space>
              <span>共找到 <b>{{ importPreviewData.length }}</b> 条记录</span>
              <a-button type="primary" @click="handleStartImport" :loading="importInProgress">
                开始导入
              </a-button>
              <a-button @click="handleCancelImportPreview">取消</a-button>
            </a-space>
          </div>
        </div>

        <!-- 预览数据表格 -->
        <a-table
          :columns="importPreviewColumns"
          :data-source="importPreviewData"
          :pagination="{ pageSize: 10 }"
          :scroll="{ x: 1200 }"
          row-key="index"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'userIdStatus'">
              <a-tag :color="record.userIdCheckStatus === 'found' ? 'green' : 'red'">
                {{ record.userIdCheckStatus === 'found' ? '已找到' : '未找到' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'participants'">
              <div v-for="p in record.participants" :key="p.name">
                {{ p.name }}({{ (p.allocationRatio * 100).toFixed(1) }}%)
                <a-tag v-if="p.isLeader" color="blue">负责人</a-tag>
              </div>
            </template>
          </template>
        </a-table>
      </template>
    </a-modal>

    <!-- 用户详情模态框 -->
    <a-modal
      v-model:visible="userDetailsVisible"
      title="用户奖励详情"
      width="80%"
      :footer="null"
      @cancel="userDetailsVisible = false"
    >
      <template #title>
        <div>
          <span>{{ selectedUserDetailName }}</span>
          <span style="margin-left: 16px; font-weight: normal; font-size: 14px; color: #1890ff;">
            总得分: {{ userDetailsTotalScore.toFixed(2) }}分
          </span>
        </div>
      </template>
      <a-spin :spinning="userDetailsLoading">
        <a-table
          :columns="userDetailsColumns"
          :data-source="userAwardDetails"
          :pagination="userDetailsPagination"
          :loading="userDetailsLoading"
          rowKey="id"
          @change="handleUserDetailsTableChange"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'awardName'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ record.awardName }}</span>
            </template>
            <template v-else-if="column.key === 'awardLevel'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ formatAwardLevel(record.awardLevel, record) }}</span>
            </template>
            <template v-else-if="column.key === 'awardTime'">
              {{ formatDate(record.awardTime) }}
            </template>
            <template v-else-if="column.key === 'allocationRatio'">
              {{ (record.allocationRatio * 100).toFixed(2) }}%
            </template>
            <template v-else-if="column.key === 'score'">
              <span v-if="isAwardInScoreRange(record)" style="font-weight: bold; color: #1890ff;">
                {{ record.userScore || (record.awardLevel?.score ? parseFloat(record.awardLevel.score).toFixed(2) : '0.00') }}分
              </span>
              <span v-else style="color: #999999;">不计分</span>
            </template>
            <template v-else-if="column.key === 'reviewStatus'">
              <a-tag :color="getReviewStatusColor(record.ifReviewer)">
                {{ getReviewStatusText(record.ifReviewer) }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-spin>
    </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExportOutlined,
  EyeOutlined,
  EditOutlined,
  CheckOutlined,
  DeleteOutlined,
  DownOutlined,
  UserOutlined,
  TrophyOutlined,
  CrownOutlined,
  RiseOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  AuditOutlined
} from '@ant-design/icons-vue'
// 静态导入xlsx库
import * as XLSX from 'xlsx'
import { usePermission } from '@/hooks/usePermission'
import {
  getTeachingResearchAwardsList,
  getTeachingResearchAwardDetail,
  deleteTeachingResearchAward,
  reapplyTeachingResearchAward,
  getAwardLevels,
  importTeachingResearchAwards,
  getTeacherAwardRanking,
  getUserAwardDetails,
  getReviewStatusDistribution,
  getAwardLevelDistribution,
  getYearDistribution,
  getDepartmentDistribution
} from '@/api/modules/api.teachingResearchAwards'
import { usersSearch } from '@/api/modules/api.users'
import TeachingResearchAwardForm from './components/TeachingResearchAwardForm.vue'
import TeachingResearchAwardDetail from './components/TeachingResearchAwardDetail.vue'
import TeachingResearchAwardReview from './components/TeachingResearchAwardReview.vue'
import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole } = useUserRole()
import useUserId from '@/composables/useUserId'
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()
import { hasPerms } from '@/libs/util.common';
import { getScoreTimeRange } from '@/api/modules/api.home';

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const awardLevels = ref([])
const formVisible = ref(false)
const detailVisible = ref(false)
const reviewVisible = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)
const showPersonalAwards = ref(false)
const exportingTable = ref(false)
const levelLoading = ref(false)
const leaderSearchLoading = ref(false)
const leaderOptions = ref([])
const timeRangeText = ref(''); // 添加到顶层

const currentRole = ref(null)
const currentUserId = ref(null)

// Excel 导入相关
const convertingExcel = ref(false)
const importPreviewVisible = ref(false)
const importPreviewLoading = ref(false)
const importInProgress = ref(false)
const importPreviewData = ref([])
const userIdCheckResults = reactive({
  total: 0,
  found: 0,
  notFound: 0
})

// 导入预览表格列
const importPreviewColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
    align: 'center'
  },
  {
    title: '获奖名称',
    dataIndex: 'awardName',
    key: 'awardName',
    width: 200,
    ellipsis: true
  },
  {
    title: '奖励级别',
    dataIndex: 'awardLevelName',
    key: 'awardLevelName',
    width: 150
  },
  {
    title: '获奖时间',
    dataIndex: 'awardTime',
    key: 'awardTime',
    width: 100
  },
  {
    title: '负责人',
    dataIndex: 'responsiblePerson',
    key: 'responsiblePerson',
    width: 100
  },
  {
    title: '参与者',
    key: 'participants',
    width: 200
  },
  {
    title: '用户ID状态',
    key: 'userIdStatus',
    width: 100,
    align: 'center'
  }
]

// 统计数据
const overallStats = ref({
  awardCount: 0,
  totalScore: 0,
  levelDistribution: {}
})

const personalStats = ref({
  awardCount: 0,
  totalScore: 0,
  rank: '--',
  totalUsers: '--'
})

// 图表相关
const reviewStatusChartRef = ref(null)
const levelChartRef = ref(null)
const yearChartRef = ref(null)
const departmentChartRef = ref(null)

const reviewStatusChartRange = ref('in')
const levelChartRange = ref('in')
const yearChartRange = ref('in')
const departmentChartRange = ref('in')

// 统计年份
const overallStatsYear = ref(new Date().getFullYear())
const availableYears = ref([
  new Date().getFullYear(),
  new Date().getFullYear() - 1,
  new Date().getFullYear() - 2,
  new Date().getFullYear() - 3,
  new Date().getFullYear() - 4
])

// 计算属性
const averageScore = computed(() => {
  if (!overallStats.value.awardCount || overallStats.value.awardCount === 0) {
    return 0
  }
  return (overallStats.value.totalScore / overallStats.value.awardCount)
})

const totalScore = computed(() => {
  if (!tableData.value || tableData.value.length === 0) {
    return 0
  }
  return tableData.value.reduce((sum, item) => {
    if (isAwardInScoreRange(item)) {
      return sum + (parseFloat(item.awardLevel?.score) || 0)
    }
    return sum
  }, 0)
})

// 用户得分相关
const userScoreData = ref([])
const userScoreLoading = ref(false)
const userScoreSearchParams = reactive({
  nickname: ''
})
const userScorePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})
const userScoreChartRange = ref('in')

// 用户详情相关
const userDetailsVisible = ref(false)
const userDetailsLoading = ref(false)
const userAwardDetails = ref([])
const selectedUserId = ref(null)
const selectedUserDetailName = ref('')
const userDetailsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
})
const userDetailsTotalScore = ref(0)

// 用户得分表格列
const userScoreColumns = [
  {
    title: '排名',
    key: 'rank',
    dataIndex: 'rank',
    width: 80,
    align: 'center'
  },
  {
    title: '用户名',
    dataIndex: 'nickname',
    key: 'nickname',
    width: 150
  },
  {
    title: '工号',
    dataIndex: 'studentNumber',
    key: 'studentNumber',
    width: 120
  },
  {
    title: '奖励数量',
    dataIndex: 'awardCount',
    key: 'awardCount',
    width: 100,
    align: 'center'
  },
  {
    title: '总得分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: 120,
    align: 'center',
    sorter: true
  },
  {
    title: '操作',
    key: 'details',
    width: 100,
    align: 'center'
  }
]

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  awardName: '',
  awardLevelId: undefined,
  department: '',
  dateRange: [],
  ifReviewer: "all",
  range: 'in'
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '获奖名称',
    dataIndex: 'awardName',
    key: 'awardName',
    width: 200,
    ellipsis: false
  },
  {
    title: '奖励级别',
    dataIndex: 'awardLevel',
    key: 'awardLevel',
    width: 150,
    ellipsis: false
  },
  {
    title: '参与人员',
    dataIndex: 'participants',
    key: 'participants',
    width: 200,
    ellipsis: true
  },
  {
    title: '获奖时间',
    dataIndex: 'awardTime',
    key: 'awardTime',
    width: 100,
    ellipsis: true
  },
  {
    title: '系/教研室',
    dataIndex: 'department',
    key: 'department',
    width: 120,
    ellipsis: true
  },
  {
    title: '计算分数',
    dataIndex: 'score',
    key: 'score',
    width: 80,
    align: 'center'
  },
  {
    title: '审核状态',
    dataIndex: 'ifReviewer',
    key: 'reviewStatus',
    width: 100,
    align: 'center'
  },
  {
    title: '审核建议',
    dataIndex: 'reviewComment',
    key: 'reviewComment',
    width: 150,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right',
    align: 'center'
  }
]

// 用户详情相关列
const userDetailsColumns = [
  {
    title: '获奖名称',
    dataIndex: 'awardName',
    key: 'awardName',
    width: 200,
    ellipsis: false
  },
  {
    title: '奖励级别',
    dataIndex: 'levelName',
    key: 'levelName',
    width: 150,
    ellipsis: false
  },
  {
    title: '获奖时间',
    dataIndex: 'awardTime',
    key: 'awardTime',
    width: 100,
    ellipsis: true
  },
  {
    title: '系/教研室',
    dataIndex: 'department',
    key: 'department',
    width: 60,
    ellipsis: true
  },
  {
    title: '分配比例',
    dataIndex: 'allocationRatio',
    key: 'allocationRatio',
    width: 80,
    align: 'center'
  },
  {
    title: '得分',
    dataIndex: 'score',
    key: 'score',
    width: 80,
    align: 'center'
  },
]

// 工具函数
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const getReviewStatusColor = (status) => {
  if (status === true || status === 1) return 'success'
  if (status === false || status === 0) return 'error'
  return 'warning' // 待审核或null
}

const getReviewStatusText = (status) => {
  if (status === true || status === 1) return '已审核'
  if (status === false || status === 0) return '已拒绝'
  return '待审核' // 待审核或null
}

const getRankColor = (rank) => {
  if (rank <= 3) return 'red'
  if (rank <= 10) return 'orange'
  if (rank <= 20) return 'blue'
  return 'default'
}

// 格式化奖励级别（不显示分数）
const formatAwardLevel = (awardLevel, record) => {
  if (!awardLevel) {
    // 如果awardLevel为空，尝试从awardLevelId和全局奖励级别列表中获取
    if (record && record.awardLevelId) {
      const foundLevel = awardLevels.value.find(level => level.id === record.awardLevelId);
      return foundLevel ? foundLevel.levelName : '未知级别';
    }
    return '-';
  }
  
  if (typeof awardLevel === 'object') {
    if (awardLevel.levelName) {
      return awardLevel.levelName;
    }
    // 对象但没有levelName属性
    return '未知级别';
  }
  
  return awardLevel;
}

// 格式化参与者信息（参考research-projects.vue）
const formatParticipantsWithAllocation = (participants, record) => {
  if (!participants || !Array.isArray(participants) || participants.length === 0) {
    return '-'
  }

  return participants.map(p => {
    // 正确获取用户对象，可能是p.participant或p.user
    const user = p.participant || p.user

    // 获取用户名称
    let name = '未知用户'
    if (user) {
      name = user.nickname || user.username || user.name
      console.log('name', name) 
    }

    // 分配比例显示为百分比，保留整数
    const allocation = p.allocationRatio ? `(占比${(parseFloat(p.allocationRatio) * 100).toFixed(0)}%)` : ''

    // 负责人标识，使用中文
    const leader = p.isLeader === 1 || p.isLeader === true ? '[负责人]' : ''

    return `${name}${leader}${allocation}`
  }).join('、 ')
}

// 切换个人/全部视图
const togglePersonalAwards = () => {
  showPersonalAwards.value = !showPersonalAwards.value
  loadData()
  if (showPersonalAwards.value) {
    loadPersonalStats()
  } else {
    loadOverallStats()
  }
}

// 加载统计数据
const loadOverallStats = async () => {
  try {
    // TODO: 调用真实的统计API
    // const response = await getTeachingResearchAwardsStats({ year: overallStatsYear.value })
    // if (response.code === 200) {
    //   overallStats.value = response.data
    // }

    // 暂时清空数据，等待后端API实现
    overallStats.value = {
      awardCount: 0,
      totalScore: 0,
      levelDistribution: {}
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    overallStats.value = {
      awardCount: 0,
      totalScore: 0,
      levelDistribution: {}
    }
  }
}

const loadPersonalStats = async () => {
  try {
    // TODO: 调用真实的个人统计API
    // const response = await getPersonalTeachingResearchAwardsStats()
    // if (response.code === 200) {
    //   personalStats.value = response.data
    // }

    // 暂时清空数据，等待后端API实现
    personalStats.value = {
      awardCount: 0,
      totalScore: 0,
      rank: '--',
      totalUsers: '--'
    }
  } catch (error) {
    console.error('加载个人统计数据失败:', error)
    personalStats.value = {
      awardCount: 0,
      totalScore: 0,
      rank: '--',
      totalUsers: '--'
    }
  }
}

// 图表范围变化
const changeChartRange = async (chartType, value) => {
  console.log(`${chartType} 图表范围变更为: ${value}`)
  
  try {
    // 动态导入echarts
    const echarts = await import('echarts');
    
    // 根据图表类型重新加载对应的数据
    switch (chartType) {
      case 'reviewStatus':
        if (reviewStatusChartRef.value) {
          const reviewStatusChart = echarts.init(reviewStatusChartRef.value);
          let reviewStatusData = [
            { value: 0, name: '已审核' },
            { value: 0, name: '已拒绝' },
            { value: 0, name: '待审核' }
          ];
          
          try {
            // 调用API获取真实数据
            const response = await getReviewStatusDistribution({ range: value });
            if (response.code === 200 && response.data) {
              // 转换API返回的数据格式以适应图表
              reviewStatusData = [
                { value: response.data.reviewed || 0, name: '已审核' },
                { value: response.data.rejected || 0, name: '已拒绝' },
                { value: response.data.pending || 0, name: '待审核' }
              ];
            }
            
            // 更新图表
            reviewStatusChart.setOption({
              series: [{
                data: reviewStatusData
              }]
            });
          } catch (error) {
            console.error('获取审核状态分布数据失败:', error);
            message.error('获取审核状态分布数据失败');
          }
        }
        break;
        
      case 'level':
        if (levelChartRef.value) {
          const levelChart = echarts.init(levelChartRef.value);
          let levelData = [
            { value: 0, name: '国家级' },
            { value: 0, name: '省部级' },
            { value: 0, name: '市级' },
            { value: 0, name: '校级' }
          ];
          
          try {
            // 调用API获取真实数据
            const response = await getAwardLevelDistribution({ range: value });
            if (response.code === 200 && response.data && Array.isArray(response.data)) {
              levelData = response.data.map(item => ({
                value: item.count || 0,
                name: item.levelName || '未知级别'
              }));
              
              // 更新图表
              levelChart.setOption({
                legend: {
                  data: levelData.map(item => item.name)
                },
                series: [{
                  data: levelData
                }]
              });
            }
          } catch (error) {
            console.error('获取奖励级别分布数据失败:', error);
            message.error('获取奖励级别分布数据失败');
          }
        }
        break;
        
      case 'year':
        if (yearChartRef.value) {
          const yearChart = echarts.init(yearChartRef.value);
          let yearData = [
            { value: 0, name: '2023年' },
            { value: 0, name: '2022年' },
            { value: 0, name: '2021年' },
            { value: 0, name: '2020年' }
          ];
          
          try {
            // 调用API获取真实数据
            const response = await getYearDistribution({ range: value });
            if (response.code === 200 && response.data && Array.isArray(response.data)) {
              yearData = response.data.map(item => ({
                value: item.count || 0,
                name: item.year ? `${item.year}年` : '未知年份'
              }));
              
              // 更新图表
              yearChart.setOption({
                legend: {
                  data: yearData.map(item => item.name)
                },
                series: [{
                  data: yearData
                }]
              });
            }
          } catch (error) {
            console.error('获取年度奖励分布数据失败:', error);
            message.error('获取年度奖励分布数据失败');
          }
        }
        break;
        
      case 'department':
        if (departmentChartRef.value) {
          const departmentChart = echarts.init(departmentChartRef.value);
          let departmentData = [
            { value: 0, name: '计算机系' },
            { value: 0, name: '数学系' },
            { value: 0, name: '物理系' },
            { value: 0, name: '化学系' }
          ];
          
          try {
            // 调用API获取真实数据
            const response = await getDepartmentDistribution({ range: value });
            if (response.code === 200 && response.data && Array.isArray(response.data)) {
              departmentData = response.data.map(item => ({
                value: item.count || 0,
                name: item.department || '未知部门'
              }));
              
              // 更新图表
              departmentChart.setOption({
                legend: {
                  data: departmentData.map(item => item.name)
                },
                series: [{
                  data: departmentData
                }]
              });
            }
          } catch (error) {
            console.error('获取系/教研室分布数据失败:', error);
            message.error('获取系/教研室分布数据失败');
          }
        }
        break;
        
      default:
        console.warn(`未知的图表类型: ${chartType}`);
    }
  } catch (error) {
    console.error('更新图表失败:', error);
    message.error('更新图表失败');
  }
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  pagination.current = 1
  loadData()
}

const handleCreate = () => {
  currentRecord.value = null
  isEdit.value = false
  formVisible.value = true
}

const handleExport = async () => {
  try {
    exportingTable.value = true
    // TODO: 调用真实的导出API
    // const params = {
    //   awardName: searchForm.awardName || undefined,
    //   awardLevelId: searchForm.awardLevelId || undefined,
    //   department: searchForm.department || undefined,
    //   startDate: searchForm.dateRange?.[0] || undefined,
    //   endDate: searchForm.dateRange?.[1] || undefined,
    //   ifReviewer: searchForm.ifReviewer,
    //   showPersonal: showPersonalAwards.value
    // }
    // const response = await exportTeachingResearchAwards(params)
    // if (response.code === 200) {
    //   message.success('导出成功')
    // }

    message.warning('导出功能暂未实现，请联系管理员')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exportingTable.value = false
  }
}

const handleDetail = (record) => {
  currentRecord.value = record
  detailVisible.value = true
}

const handleEdit = async (record) => {
  try {
    // 设置编辑模式
    isEdit.value = true
    currentRecord.value = record

    // 显示loading状态
    loading.value = true

    // 通过API获取奖励详情
    console.log('正在获取教学科研奖励详情:', record.id)
    const response = await getTeachingResearchAwardDetail({ id: record.id })

    if (!response || response.code !== 200 || !response.data) {
      message.error('获取奖励详情失败')
      loading.value = false
      return
    }

    const awardData = response.data
    console.log('获取奖励详情成功:', awardData)

    // 设置当前记录为详细数据
    currentRecord.value = awardData

    // 显示表单模态框
    formVisible.value = true

  } catch (error) {
    console.error('获取奖励详情失败:', error)
    message.error('获取奖励详情失败')
  } finally {
    loading.value = false
  }
}

const handleReview = (record) => {
  currentRecord.value = record
  reviewVisible.value = true
}

// 审核成功回调
const handleReviewSuccess = () => {
  reviewVisible.value = false
  loadData() // 刷新数据
}

// 判断奖励是否在统计范围内
const isAwardInScoreRange = (record) => {
  // 这里可以根据实际业务逻辑判断
  // 暂时返回true，表示都在统计范围内
  return true
}

// 重新提交审核
const handleResubmit = (record) => {
  Modal.confirm({
    title: '确认重新提交审核',
    content: `确定要重新提交"${record.awardName}"的审核申请吗？`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        console.log('重新提交审核，奖励ID:', record.id)

        // 调用重新提交审核的API
        const response = await reapplyTeachingResearchAward(record.id)
        console.log('重新提交审核API响应:', response)

        if (response && response.code === 200) {
          message.success('重新提交审核成功')
          loadData() // 刷新数据
        } else {
          message.error(response?.message || '重新提交审核失败')
        }
      } catch (error) {
        console.error('重新提交审核失败:', error)
        message.error('重新提交审核失败: ' + (error.message || '未知错误'))
      }
    }
  })
}

// 负责人搜索
const handleLeaderSearch = (value) => {
  // 实现负责人搜索逻辑
  console.log('搜索负责人:', value)
}

// 负责人选择变化
const handleLeaderChange = (value) => {
  // 实现负责人选择变化逻辑
  console.log('选择负责人:', value)
}

// 本地Excel转换函数，专门用于教学科研奖励
const convertExcelToTeachingResearchAwards = async (file, options = {}) => {
  console.log('开始本地Excel转换，文件信息:', {
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type
  })

  // 检查文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件')
  }

  const extension = file.name.split('.').pop().toLowerCase()
  if (!['xlsx', 'xls', 'csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)')
  }

  const headerRow = options.headerRow ?? 2 // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null
  const fieldMapping = options.fieldMapping || {}

  try {
    // 使用静态导入的XLSX库，不再需要动态导入
    // const XLSX = await import('xlsx/dist/xlsx.full.min.js')

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          console.log('开始解析Excel文件...')
          const ab = e.target.result
          const wb = XLSX.read(ab, { type: 'array' })

          console.log('Excel工作簿信息:', {
            sheetNames: wb.SheetNames,
            totalSheets: wb.SheetNames.length
          })

          const wsname = sheetName || wb.SheetNames[0]
          console.log(`选择工作表: ${wsname}`)

          const ws = wb.Sheets[wsname]

          // 获取工作表范围信息
          const range = XLSX.utils.decode_range(ws['!ref'])
          console.log('工作表范围:', {
            startRow: range.s.r,
            endRow: range.e.r,
            startCol: range.s.c,
            endCol: range.e.c,
            totalRows: range.e.r - range.s.r + 1,
            totalCols: range.e.c - range.s.c + 1
          })

          // 先读取所有数据查看原始内容
          const allData = XLSX.utils.sheet_to_json(ws, {
            raw: false,
            defval: null,
            header: 1 // 使用数组格式，便于查看原始数据
          })

          console.log('原始数据预览（前5行）:', allData.slice(0, 5))
          console.log(`总数据行数: ${allData.length}`)

          // 从指定行开始读取数据（参考research-projects.vue的配置）
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          })

          console.log(`从第${headerRow + 1}行开始读取，解析出${jsonData.length}条记录`)
          console.log('解析后的数据结构（前3条）:', jsonData.slice(0, 3))

          // 检查表头信息
          if (jsonData.length > 0) {
            const headers = Object.keys(jsonData[0])
            console.log('检测到的表头字段:', headers)
            console.log('表头字段数量:', headers.length)
          }

          resolve(jsonData)
        } catch (error) {
          console.error('解析Excel文件时出错:', error)
          reject(new Error(`解析Excel文件失败: ${error.message}`))
        }
      }
      reader.onerror = () => {
        console.error('FileReader读取文件失败')
        reject(new Error('读取文件失败'))
      }
      reader.readAsArrayBuffer(file)
    })

    // 字段映射函数
    const findFieldValue = (row, fieldKey) => {
      const possibleNames = fieldMapping[fieldKey] || []
      console.log(`查找字段 ${fieldKey}，可能的名称:`, possibleNames)

      for (const name of possibleNames) {
        if (row.hasOwnProperty(name) && row[name] !== null && row[name] !== undefined) {
          console.log(`找到字段 ${fieldKey} -> ${name}: "${row[name]}"`)
          return row[name]
        }
      }

      console.log(`未找到字段 ${fieldKey}`)
      return null
    }

    // 处理数据，转换为教学科研奖励数据格式
    const awards = []
    console.log(`开始处理${data.length}条原始数据...`)

    for (let i = 0; i < data.length; i++) {
      const row = data[i]
      console.log(`处理第${i + 1}行数据:`, row)

      // 使用字段映射查找获奖名称
      const awardName = findFieldValue(row, 'awardName')
      console.log(`第${i + 1}行 - 获奖名称: "${awardName}", 类型: ${typeof awardName}`)

      // 跳过空行
      if (!awardName || String(awardName).trim() === '') {
        console.log(`第${i + 1}行 - 跳过空行（获奖名称为空）`)
        continue
      }

      // 创建奖励对象
      const award = {
        awardName: String(awardName).trim(),
        awardLevelName: findFieldValue(row, 'awardLevelName') ? String(findFieldValue(row, 'awardLevelName')).trim() : '',
        awardTime: findFieldValue(row, 'awardTime') || '',
        department: findFieldValue(row, 'department') ? String(findFieldValue(row, 'department')).trim() : '',
        responsiblePerson: findFieldValue(row, 'responsiblePerson') ? String(findFieldValue(row, 'responsiblePerson')).trim() : '',
        participants: []
      }

      console.log(`第${i + 1}行 - 创建奖励对象:`, award)

      // 处理获奖者信息
      const awardees = []
      console.log(`第${i + 1}行 - 开始处理获奖者信息...`)

      // 处理获奖者1（负责人）
      const awardee1Name = findFieldValue(row, 'awardee1')
      console.log(`第${i + 1}行 - 获奖者1: "${awardee1Name}"`)

      if (awardee1Name) {
        const personnelId = findFieldValue(row, 'personnelId')
        const allocationRatioRaw = findFieldValue(row, 'allocationRatio')

        const awardee1 = {
          name: String(awardee1Name).trim(),
          personnelId: personnelId ? String(personnelId).trim() : '',
          allocationRatio: parseFloat(String(allocationRatioRaw || '100').replace('%', '')) / 100,
          isLeader: true // 获奖者1为负责人
        }

        console.log(`第${i + 1}行 - 获奖者1处理结果:`, awardee1)
        awardees.push(awardee1)
      } else {
        console.log(`第${i + 1}行 - 未找到获奖者1信息`)
      }

      // 如果没有找到获奖者信息，但有负责人信息，使用负责人作为获奖者
      if (awardees.length === 0 && award.responsiblePerson) {
        console.log(`第${i + 1}行 - 未找到获奖者信息，使用负责人作为获奖者: ${award.responsiblePerson}`)
        awardees.push({
          name: award.responsiblePerson,
          personnelId: '',
          allocationRatio: 1.0,
          isLeader: true
        })
      }

      award.participants = awardees

      // 处理审核人信息
      const reviewer = findFieldValue(row, 'reviewer')
      if (reviewer) {
        award.reviewer = String(reviewer).trim()
        console.log(`第${i + 1}行 - 审核人: ${award.reviewer}`)
      }

      console.log(`第${i + 1}行 - 最终奖励对象:`, award)
      awards.push(award)
    }

    console.log(`数据处理完成，共解析出${awards.length}条有效奖励记录`)
    console.log('所有奖励记录:', awards)

    const result = {
      data: awards,
      total: data.length,
      success: awards.length,
      failed: data.length - awards.length
    }

    console.log('最终转换结果:', result)
    console.log(`转换统计: 总行数=${result.total}, 成功=${result.success}, 失败=${result.failed}`)

    return result

  } catch (error) {
    console.error('转换Excel到教学科研奖励JSON出错:', error)
    console.error('错误堆栈:', error.stack)
    throw error
  }
}

// Excel 导入相关方法
// Excel文件上传前检查
const beforeExcelUpload = (file) => {
  // 检查文件类型
  const isExcel =
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel' ||
    file.name.endsWith('.csv')

  if (!isExcel) {
    message.error('只能上传Excel文件 (.xlsx, .xls, .csv)!')
    return false
  }

  // 文件大小限制：20MB
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    message.error('文件大小不能超过20MB!')
    return false
  }

  return true
}

// 处理Excel到JSON转换
const handleExcelToJsonConvert = async ({ file }) => {
  const messageKey = `excel_convert_${Date.now()}`

  try {
    convertingExcel.value = true
    console.log('开始处理Excel文件:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    })

    // 显示处理提示
    message.loading({
      content: '正在解析Excel文件，请稍候...',
      key: messageKey,
      duration: 0
    })

    // 配置选项：表头在第3行（参考research-projects.vue）
    const options = {
      headerRow: 2, // 第3行作为表头（索引从0开始，所以是2）
      sheetName: null // 使用第一个工作表
    }

    console.log('Excel解析配置:', options)

    // 添加字段映射配置，用于处理不同的表头名称
    const fieldMapping = {
      // 标准字段名 -> 可能的Excel表头名称数组
      'awardName': ['获奖名称', '奖项名称', '项目名称', '成果名称'],
      'awardLevelName': ['奖励名称/等级', '奖励等级', '等级', '奖项等级', '奖励名称等级'],
      'awardTime': ['获奖时间（写到月）', '获奖时间', '时间', '获奖日期'],
      'department': ['系/教研室', '系教研室', '部门', '单位', '所属部门'],
      'responsiblePerson': ['负责人', '项目负责人', '第一负责人'],
      'awardee1': ['获奖者1', '获奖者一', '第一获奖者', '获奖人1'],
      'personnelId': ['人事编号', '工号', '员工编号', '编号'],
      'allocationRatio': ['分配比例', '比例', '分配率'],
      'reviewer': ['审核人', '审核者', '审核员']
    }
    console.log('字段映射配置:', fieldMapping)

    // 使用本地的Excel转换函数
    console.log('开始调用本地转换函数...')
    const result = await convertExcelToTeachingResearchAwards(file, { ...options, fieldMapping })
    console.log('转换函数执行完成，结果:', result)

    const awardsData = result.data || []
    console.log(`提取到${awardsData.length}条奖励数据`)

    if (awardsData.length === 0) {
      console.warn('未提取到任何有效数据，原始结果:', result)
      message.warning('未从Excel文件中提取到任何有效数据，请检查Excel格式是否正确')
      return
    }

    // 数据清理和格式化
    console.log('开始数据清理和格式化...')
    const optimizedData = awardsData.map((award, index) => {
      const cleanAward = {}
      Object.entries(award).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          cleanAward[key] = value
        }
      })
      console.log(`第${index + 1}条数据清理结果:`, cleanAward)
      return cleanAward
    })

    console.log('数据清理完成，准备显示预览...')

    // 显示导入预览
    await processJsonData(optimizedData)

  } catch (error) {
    console.error('Excel文件处理失败:', error)
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      fileName: file?.name,
      fileSize: file?.size
    })
    message.error(`Excel文件处理失败: ${error.message}`)
  } finally {
    convertingExcel.value = false
    message.destroy(messageKey)
  }
}

// 处理JSON数据的公共方法
const processJsonData = async (parsedData) => {
  importPreviewLoading.value = true
  message.loading('正在解析JSON数据并检查用户ID，请稍候...', 0)

  try {
    console.log('开始处理JSON数据:', parsedData)

    // 确保数据是数组形式
    const dataArray = Array.isArray(parsedData) ? parsedData : [parsedData]
    console.log(`数据数组长度: ${dataArray.length}`)

    // 初始化用户ID检查结果
    userIdCheckResults.total = dataArray.length
    userIdCheckResults.found = 0
    userIdCheckResults.notFound = 0

    // 准备预览数据
    const previewItems = dataArray.map((item, index) => {
      console.log(`处理第${index + 1}项数据:`, item)

      const previewItem = {
        index: index + 1,
        awardName: item.awardName || '',
        awardLevelName: item.awardLevelName || '',
        awardTime: item.awardTime || '',
        responsiblePerson: item.responsiblePerson || '',
        department: item.department || '',
        participants: item.participants || [],
        userIdCheckStatus: 'checking',
        rawData: item
      }

      console.log(`第${index + 1}项预览数据:`, previewItem)
      return previewItem
    })

    console.log('设置预览数据:', previewItems)
    importPreviewData.value = previewItems
    importPreviewVisible.value = true

    // 开始检查用户ID
    console.log('开始检查用户ID...')
    for (let i = 0; i < previewItems.length; i++) {
      const item = previewItems[i]
      console.log(`检查第${i + 1}项的用户ID:`, item)

      // 检查参与者信息
      if (item.participants && item.participants.length > 0) {
        console.log(`第${i + 1}项有${item.participants.length}个参与者`)

        for (const participant of item.participants) {
          if (participant.name) {
            console.log(`查找用户: ${participant.name}`)
            try {
              // 使用姓名查找用户ID
              const userSearchResult = await usersSearch({ keyword: participant.name })
              console.log(`用户搜索结果:`, userSearchResult)

              if (userSearchResult.code === 200 && userSearchResult.data.length > 0) {
                const foundUser = userSearchResult.data[0]
                participant.userId = foundUser.id
                participant.userFound = true
                userIdCheckResults.found++
                console.log(`找到用户: ${participant.name} -> ${foundUser.id}`)
              } else {
                participant.userFound = false
                userIdCheckResults.notFound++
                console.log(`未找到用户: ${participant.name}`)
              }
            } catch (error) {
              console.error(`查找用户 ${participant.name} 失败:`, error)
              participant.userFound = false
              userIdCheckResults.notFound++
            }
          } else {
            console.log('参与者姓名为空，跳过')
          }
        }
      } else {
        console.log(`第${i + 1}项没有参与者信息`)
      }

      // 更新检查状态
      const allFound = item.participants.every(p => p.userFound)
      item.userIdCheckStatus = allFound ? 'found' : 'notfound'
      console.log(`第${i + 1}项用户ID检查状态: ${item.userIdCheckStatus}`)
    }

    console.log('用户ID检查完成:', userIdCheckResults)

  } catch (error) {
    console.error('处理JSON数据失败:', error)
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      parsedData: parsedData
    })
    message.error(`处理数据失败: ${error.message}`)
  } finally {
    importPreviewLoading.value = false
    message.destroy()
    console.log('JSON数据处理完成')
  }
}

// 开始导入
const handleStartImport = async () => {
  if (importPreviewData.value.length === 0) {
    message.warning('没有可导入的数据')
    return
  }

  try {
    importInProgress.value = true

    // 准备导入数据
    const importData = importPreviewData.value.map(item => {
      const data = { ...item.rawData }

      // 确保参与者有正确的userId
      if (data.participants) {
        data.participants = data.participants.map(p => ({
          ...p,
          userId: p.userId || null
        }))
      }

      return data
    })

    // 调用导入API
    const response = await importTeachingResearchAwards(importData)

    if (response.code === 200) {
      message.success(`导入成功！共导入 ${response.data.success} 条记录`)
      importPreviewVisible.value = false
      loadData() // 刷新列表
    } else {
      message.error(response.message || '导入失败')
    }

  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败: ' + error.message)
  } finally {
    importInProgress.value = false
  }
}

// 取消导入预览
const handleCancelImportPreview = () => {
  importPreviewVisible.value = false
  importPreviewData.value = []
}

const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除"${record.awardName}"吗？此操作不可逆。`,
    okText: '确认',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        const response = await deleteTeachingResearchAward(record.id)
        if (response && response.code === 200) {
          message.success('删除成功')
          loadData()
        } else {
          message.error(response?.message || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败: ' + (error.message || '未知错误'))
      }
    }
  })
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

// 表单成功回调
const handleFormSuccess = () => {
  loadData()
  loadAwardLevels()
}

// 用户得分相关函数
const fetchAllUsersTotalScore = async () => {
  userScoreLoading.value = true
  try {
    const params = {
      page: userScorePagination.current,
      pageSize: userScorePagination.pageSize,
      range: userScoreChartRange.value,
      reviewStatus: 'reviewed', // 只显示已审核的数据
      ...userScoreSearchParams
    }

    const response = await getTeacherAwardRanking(params)

    if (response && response.code === 200) {
      // 转换数据格式以匹配表格列
      userScoreData.value = response.data.list.map(item => ({
        key: item.userId,
        rank: item.rank,
        nickname: item.userName,
        studentNumber: item.studentNumber,
        awardCount: item.totalAwards,
        totalScore: item.totalScore,
        userId: item.userId
      }))
      
      // 更新分页信息
      if (response.data.pagination) {
        userScorePagination.current = response.data.pagination.page
        userScorePagination.pageSize = response.data.pagination.pageSize
        userScorePagination.total = response.data.pagination.total
      } else {
        userScorePagination.total = response.data.total || 0
      }
    } else {
      message.error(response?.message || '获取用户得分统计失败')
      userScoreData.value = []
      userScorePagination.total = 0
    }
  } catch (error) {
    console.error('获取用户得分统计失败:', error)
    message.error('获取用户得分统计失败')
    userScoreData.value = []
    userScorePagination.total = 0
  } finally {
    userScoreLoading.value = false
  }
}

const resetUserScoreSearch = () => {
  userScoreSearchParams.nickname = ''
  fetchAllUsersTotalScore()
}

const handleUserScoreRangeChange = (value) => {
  userScoreChartRange.value = value
  fetchAllUsersTotalScore()
}

const handleUserScoreTableChange = (pag, filters, sorter) => {
  userScorePagination.current = pag.current
  userScorePagination.pageSize = pag.pageSize
  fetchAllUsersTotalScore()
}

const showUserScoreDetails = async (record) => {
  try {
    userDetailsLoading.value = true;
    selectedUserId.value = record.userId;
    selectedUserDetailName.value = record.nickname || '未知用户';
    
    // 重置分页
    userDetailsPagination.current = 1;
    
    // 调用API获取用户奖励详情
    const response = await getUserAwardDetails({
      userId: record.userId,
      range: userScoreChartRange.value,
      reviewStatus: 'reviewed', // 只显示已审核的数据
      page: userDetailsPagination.current,
      pageSize: userDetailsPagination.pageSize
    });
    
    if (response && response.code === 200) {
      // 处理返回的数据
      userAwardDetails.value = response.data.list.map(item => ({
        ...item,
        userScore: parseFloat(item.userScore || 0).toFixed(2),
        totalScore: parseFloat(item.totalScore || 0).toFixed(2)
      }));
      
      // 更新分页信息
      if (response.data.pagination) {
        userDetailsPagination.current = response.data.pagination.page;
        userDetailsPagination.pageSize = response.data.pagination.pageSize;
        userDetailsPagination.total = response.data.pagination.total || 0;
      } else {
        userDetailsPagination.total = response.data.pagination?.total || 0;
      }
      
      userDetailsTotalScore.value = parseFloat(response.data.totalScore || 0);
      
      // 显示模态框
      userDetailsVisible.value = true;
    } else {
      message.error(response?.message || '获取用户奖励详情失败');
    }
  } catch (error) {
    console.error('获取用户奖励详情失败:', error);
    message.error('获取用户奖励详情失败: ' + (error.message || '未知错误'));
  } finally {
    userDetailsLoading.value = false;
  }
}

const handleUserDetailsTableChange = async (pagination) => {
  userDetailsLoading.value = true;
  userDetailsPagination.current = pagination.current;
  userDetailsPagination.pageSize = pagination.pageSize;
  
  try {
    const response = await getUserAwardDetails({
      userId: selectedUserId.value,
      range: userScoreChartRange.value,
      reviewStatus: 'reviewed',
      page: userDetailsPagination.current,
      pageSize: userDetailsPagination.pageSize
    });
    
    if (response && response.code === 200) {
      userAwardDetails.value = response.data.list.map(item => ({
        ...item,
        userScore: parseFloat(item.userScore || 0).toFixed(2),
        totalScore: parseFloat(item.totalScore || 0).toFixed(2)
      }));
      
      // 更新分页信息
      if (response.data.pagination) {
        userDetailsPagination.current = response.data.pagination.page;
        userDetailsPagination.pageSize = response.data.pagination.pageSize;
        userDetailsPagination.total = response.data.pagination.total || 0;
      } else {
        userDetailsPagination.total = response.data.pagination?.total || 0;
      }
      
      userDetailsTotalScore.value = parseFloat(response.data.totalScore || 0);
    } else {
      message.error(response?.message || '获取用户奖励详情失败');
    }
  } catch (error) {
    console.error('获取用户奖励详情分页数据失败:', error);
    message.error('获取用户奖励详情分页数据失败');
  } finally {
    userDetailsLoading.value = false;
  }
}

const exportUserScoreData = () => {
  exportingTable.value = true
  setTimeout(() => {
    message.success('导出成功')
    exportingTable.value = false
  }, 1000)
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    // 准备查询参数，参考research-projects.vue的参数格式
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      awardName: searchForm.awardName || undefined,
      awardLevelId: searchForm.awardLevelId || undefined,
      department: searchForm.department || undefined,
      startDate: searchForm.dateRange?.[0] || undefined,
      endDate: searchForm.dateRange?.[1] || undefined,
      ifReviewer: searchForm.ifReviewer,
      range: searchForm.range
    }

    // 调用API获取数据
    const response = await getTeachingResearchAwardsList(params)

    if (response.code === 200) {
      tableData.value = response.data?.list || []
      // 更新分页信息
      if (response.data?.pagination) {
        pagination.current = response.data.pagination.page
        pagination.pageSize = response.data.pagination.pageSize
        pagination.total = response.data.pagination.total
      } else {
        pagination.total = response.data?.total || 0
      }
    } else {
      message.error(response.message || '加载数据失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 加载奖励级别
const loadAwardLevels = async () => {
  try {
    const response = await getAwardLevels()
    if (response.code === 200) {
      awardLevels.value = response.data || []
    } else {
      console.error('加载奖励级别失败:', response.message)
      awardLevels.value = []
    }
  } catch (error) {
    console.error('加载奖励级别失败:', error)
    awardLevels.value = []
  }
}

// 生命周期
onMounted(async () => {
  currentRole.value = await getUserRole()
    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true)
      console.log("当前用户ID:", currentUserId.value)
    } catch (error) {
      console.error("获取用户ID失败:", error)
    }

  loadData()
  loadAwardLevels()
  loadOverallStats()
  fetchAllUsersTotalScore()
  
  // 初始化图表
  initCharts()

  // 获取时间范围
  const getTimeRange = () => {
    // 调用API获取时间范围
    getScoreTimeRange('teachingResearchAwards').then(res => {
      if (res.code === 200 && res.data) {
        timeRangeText.value = res.data.timeRange || '';
      } else {
        timeRangeText.value = '暂无时间范围数据';
      }
    }).catch(error => {
      console.error('获取时间范围失败:', error);
      timeRangeText.value = '获取时间范围失败';
    });
  };

  // 在onMounted中调用
  getTimeRange();
})

// 添加图表初始化函数
const initCharts = async () => {
  try {
    // 动态导入echarts
    const echarts = await import('echarts');
    
    // 初始化审核状态分布图表
    if (reviewStatusChartRef.value) {
      const reviewStatusChart = echarts.init(reviewStatusChartRef.value);
      let reviewStatusData = [
        { value: 0, name: '已审核' },
        { value: 0, name: '已拒绝' },
        { value: 0, name: '待审核' }
      ];
      
      try {
        // 调用API获取真实数据
        const response = await getReviewStatusDistribution({ range: reviewStatusChartRange.value });
        if (response.code === 200 && response.data) {
          // 转换API返回的数据格式以适应图表
          reviewStatusData = [
            { value: response.data.reviewed || 0, name: '已审核' },
            { value: response.data.rejected || 0, name: '已拒绝' },
            { value: response.data.pending || 0, name: '待审核' }
          ];
        }
      } catch (error) {
        console.error('获取审核状态分布数据失败:', error);
      }
      
      reviewStatusChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: reviewStatusData.map(item => item.name)
        },
        series: [
          {
            name: '审核状态',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: reviewStatusData
          }
        ]
      });
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        reviewStatusChart.resize();
      });
    }
    
    // 初始化奖励级别分布图表
    if (levelChartRef.value) {
      const levelChart = echarts.init(levelChartRef.value);
      let levelData = [
        { value: 0, name: '国家级' },
        { value: 0, name: '省部级' },
        { value: 0, name: '市级' },
        { value: 0, name: '校级' }
      ];
      
      try {
        // 调用API获取真实数据
        const response = await getAwardLevelDistribution({ range: levelChartRange.value });
        if (response.code === 200 && response.data && Array.isArray(response.data)) {
          levelData = response.data.map(item => ({
            value: item.count || 0,
            name: item.levelName || '未知级别'
          }));
        }
      } catch (error) {
        console.error('获取奖励级别分布数据失败:', error);
      }
      
      levelChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: levelData.map(item => item.name)
        },
        series: [
          {
            name: '奖励级别',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: levelData
          }
        ]
      });
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        levelChart.resize();
      });
    }
    
    // 初始化年度奖励分布图表
    if (yearChartRef.value) {
      const yearChart = echarts.init(yearChartRef.value);
      let yearData = [
        { value: 0, name: '2023年' },
        { value: 0, name: '2022年' },
        { value: 0, name: '2021年' },
        { value: 0, name: '2020年' }
      ];
      
      try {
        // 调用API获取真实数据
        const response = await getYearDistribution({ range: yearChartRange.value });
        if (response.code === 200 && response.data && Array.isArray(response.data)) {
          yearData = response.data.map(item => ({
            value: item.count || 0,
            name: item.year ? `${item.year}年` : '未知年份'
          }));
        }
      } catch (error) {
        console.error('获取年度奖励分布数据失败:', error);
      }
      
      yearChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: yearData.map(item => item.name)
        },
        series: [
          {
            name: '年度分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: yearData
          }
        ]
      });
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        yearChart.resize();
      });
    }
    
    // 初始化系/教研室分布图表
    if (departmentChartRef.value) {
      const departmentChart = echarts.init(departmentChartRef.value);
      let departmentData = [
        { value: 0, name: '计算机系' },
        { value: 0, name: '数学系' },
        { value: 0, name: '物理系' },
        { value: 0, name: '化学系' }
      ];
      
      try {
        // 调用API获取真实数据
        const response = await getDepartmentDistribution({ range: departmentChartRange.value });
        if (response.code === 200 && response.data && Array.isArray(response.data)) {
          departmentData = response.data.map(item => ({
            value: item.count || 0,
            name: item.department || '未知部门'
          }));
        }
      } catch (error) {
        console.error('获取系/教研室分布数据失败:', error);
      }
      
      departmentChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: departmentData.map(item => item.name)
        },
        series: [
          {
            name: '部门分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: departmentData
          }
        ]
      });
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        departmentChart.resize();
      });
    }
  } catch (error) {
    console.error('初始化图表失败:', error);
    message.error('初始化图表失败');
  }
}
</script>

<style scoped>
.performance-container {
  padding: 16px;
}

.performance-card {
  margin-bottom: 16px;
}

.chart-container {
  height: 300px;
}

.chart-wrapper {
  height: 250px;
  width: 100%;
}

.search-form {
  margin-bottom: 0;
}

.search-actions-inline {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.action-left,
.action-right {
  display: flex;
  gap: 8px;
}

.award-name-cell .award-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.award-name-cell .award-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.award-score {
  color: #1890ff;
  font-weight: 500;
}

.responsible-cell .user-name {
  font-weight: 500;
}

.responsible-cell .user-number {
  color: #666;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container {
    height: 250px;
  }

  .chart-wrapper {
    height: 200px;
  }

  .search-actions-inline {
    justify-content: flex-start;
  }
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}

/* 表格样式优化 */
.ant-table-tbody > tr > td {
  padding: 12px 8px;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 操作下拉菜单样式 */
.action-dropdown-menu {
  min-width: 120px;
}

.action-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: inherit;
  text-decoration: none;
}

.action-menu-item:hover {
  color: #1890ff;
}

.action-menu-item.text-danger {
  color: #ff4d4f;
}

.action-menu-item.text-danger:hover {
  color: #ff7875;
}

.action-trigger-btn {
  padding: 0 8px;
}

/* 表格底部总分样式 */
.table-footer {
  margin-top: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  text-align: right;
}

.total-score {
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
}

/* 性能表格样式 */
.performance-table {
  margin-bottom: 16px;
}

.performance-form {
  margin-bottom: 0;
}

.performance-form .ant-form-item {
  margin-bottom: 16px;
}

.performance-form .ant-form-item-label {
  font-weight: 500;
}

/* 搜索表单样式 */
.search-form .ant-card-body {
  padding: 16px;
}

/* 图表容器样式 */
.chart-container .ant-card-body {
  padding: 16px;
}

/* 统计卡片样式增强 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-content-value {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .search-actions-inline {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-actions-inline .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

@media (max-width: 992px) {
  .performance-container {
    padding: 8px;
  }

  .performance-card {
    margin-bottom: 12px;
  }
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  font-size: 16px;
}

/* 表格行样式 */
.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 按钮组样式 */
.ant-space {
  flex-wrap: wrap;
}

/* 输入框样式 */
.ant-input, .ant-select-selector {
  border-radius: 4px;
}

/* 分页样式 */
.ant-pagination {
  text-align: right;
  margin-top: 16px;
}

/* 用户详情模态框样式 */
.user-details {
  padding: 16px;
}

.user-info {
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 8px;
}

.label {
  font-weight: 500;
}

.value {
  margin-left: 8px;
}

.award-list {
  margin-top: 16px;
}

.award-list h3 {
  margin-bottom: 8px;
}
</style>
