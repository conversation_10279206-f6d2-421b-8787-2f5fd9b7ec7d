const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

/**
 * 用户级别模型
 * 表名使用下划线命名法: user_levels
 * 字段名使用小驼峰命名法: levelName, createdAt 等
 */
const UserLevels = sequelize.define('user_levels', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '级别ID，使用UUID'
    },
    levelName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        comment: '级别名称（如教授、副教授、讲师等）'
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '级别描述'
    },
    sort: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '排序字段，数字越小排序越靠前'
    },
    weight: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 1.00,
        comment: '职称权重系数，默认1.00，范围0.01-99.99'
    },
    status: {
        type: DataTypes.TINYINT(1),
        allowNull: true,
        defaultValue: 1,
        comment: '状态：1-启用，0-禁用'
    },
    createdBy: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '创建者ID'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间'
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间'
    }
}, {
    // 明确指定表名
    tableName: 'user_levels',
    // 不使用自动复数形式
    freezeTableName: true,
    // 启用时间戳
    timestamps: true,
    // 添加表注释
    comment: '用户级别表',
    // 添加索引
    indexes: [
        {
            name: 'uk_level_name',
            unique: true,
            fields: ['levelName']
        },
        {
            name: 'idx_status',
            fields: ['status']
        },
        {
            name: 'idx_sort',
            fields: ['sort']
        }
    ]
});

module.exports = UserLevels;
