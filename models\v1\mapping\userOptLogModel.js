const {DataTypes} = require('sequelize');
const sequelize = require('@config/dbConfig');
// https://www.sequelize.cn/core-concepts/model-instances

/**
 * 用户操作日志模型
 * 表名使用下划线命名法: user_opt_log
 * 字段名使用小驼峰命名法: operatorId, operatorIP 等
 */
const UserOptLog = sequelize.define('user_opt_log', // 明确指定表名为下划线形式
    {
        id:{
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4
        },
        operator: {
            type: DataTypes.STRING,
            comment: '操作人'
        },
        operatorId: {
            type: DataTypes.STRING,
            comment: '操作人ID'
        },
        module: {
            type: DataTypes.STRING,
            comment: '操作模块'
        },
        platform: {
            type: DataTypes.STRING,
            comment: '操作平台'
        },
        operatorIP: {
            type: DataTypes.STRING,
            comment: '设备IP'
        },
        latitude: {
            type: DataTypes.DOUBLE,
            comment: '纬度'
        },
        longitude: {
            type: DataTypes.DOUBLE,
            comment: '经度'
        },
        address: {
            type: DataTypes.STRING,
            comment: '设备位置'
        },
        content: {
            type: DataTypes.TEXT,
            comment: '操作内容'
        },
        createdAt: {
            type: DataTypes.DATE,
            comment: '创建时间'
        },
        updatedAt: {
            type: DataTypes.DATE,
            comment: '更新时间'
        },  
    },
    {
        // 不使用 freezeTableName，因为我们已经明确指定了表名
        freezeTableName: true,
        // 定义表名为小写复数形式
        tableName: 'user_opt_log',
        // 添加注释
        comment: '用户操作日志表'
    });

// 导入用户模型
const User = require('./userModel');

// 定义与用户模型的关联关系
UserOptLog.belongsTo(User, {
    foreignKey: 'operatorId',
    as: 'user'
});

module.exports = UserOptLog;
