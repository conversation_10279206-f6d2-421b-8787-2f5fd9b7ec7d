import request from './server'

// API 路径配置
const api = {
  list: '/timeInterval/list',
  detail: '/timeInterval/detail',
  create: '/timeInterval/create',
  update: '/timeInterval/update',
  delete: '/timeInterval/delete',
  batchDelete: '/timeInterval/batch-delete'
}

/**
 * 获取时间区间列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getTimeIntervals(params) {
  return request.get(api.list, params)
}

/**
 * 获取时间区间详情
 * @param {string} id - 记录ID
 * @returns {Promise} - 返回Promise对象
 */
export function getTimeIntervalDetail(id) {
  return request.get(api.detail, { id })
}

/**
 * 创建时间区间
 * @param {Object} data - 记录数据
 * @returns {Promise} - 返回Promise对象
 */
export function createTimeInterval(data) {
  return request.post(api.create, data)
}

/**
 * 更新时间区间
 * @param {Object} data - 记录数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateTimeInterval(data) {
  return request.put(api.update, data)
}

/**
 * 删除时间区间
 * @param {string} id - 记录ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteTimeInterval(id) {
  return request.delete(api.delete, { id })
}

/**
 * 批量删除时间区间
 * @param {Array} ids - 记录ID列表
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteTimeIntervals(ids) {
  return request.delete(api.batchDelete, { ids })
} 