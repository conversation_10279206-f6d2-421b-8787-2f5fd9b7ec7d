const log4js = require('log4js');

log4js.configure({
    appenders: {
        console: { type: 'console' },
        file: { 
            type: 'file', 
            filename: 'logs/app.log',
            maxLogSize: 10485760,
            backups: 3,
            compress: true 
        }
    },
    categories: {
        default: { 
            appenders: ['console', 'file'], 
            level: process.env.NODE_ENV === 'development' ? 'debug' : 'info'
        }
    }
});

const logger = log4js.getLogger();

module.exports = logger; 