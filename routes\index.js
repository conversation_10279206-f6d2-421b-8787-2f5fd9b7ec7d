/**
 * 路由入口文件 - 模块化重构版本
 * 整合所有API路由，按业务模块分类
 */

const express = require('express');
const router = express.Router();

// API版本前缀
const API_PREFIX = '/v1';

// ========== 绩效管理模块 ==========

// 高水平论文模块
router.use(`${API_PREFIX}/highLevelPapers`, require('./v1/highLevelPapers/highLevelPapers'));
router.use(`${API_PREFIX}/highLevelPaperParticipants`, require('./v1/highLevelPapers/highLevelPaperParticipants'));
router.use(`${API_PREFIX}/highLevelPapersRules`, require('./v1/highLevelPapers/highLevelPapersRules'));

// 科研项目模块
router.use(`${API_PREFIX}/researchProjects`, require('./v1/researchProjects/researchProjects'));
router.use(`${API_PREFIX}/researchProjectParticipants`, require('./v1/researchProjects/researchProjectParticipants'));
router.use(`${API_PREFIX}/researchProjectsLevels`, require('./v1/researchProjects/researchProjectsLevels'));

// 教学改革项目模块
router.use(`${API_PREFIX}/teachingReformProjects`, require('./v1/teachingReformProjects/teachingReformProjects'));
router.use(`${API_PREFIX}/teachingReformParticipants`, require('./v1/teachingReformProjects/teachingReformParticipants'));
router.use(`${API_PREFIX}/teachingReformProjectLevels`, require('./v1/teachingReformProjects/teachingReformProjectLevels'));

// 专利模块
router.use(`${API_PREFIX}/patents`, require('./v1/patents/patents'));
router.use(`${API_PREFIX}/patentCategories`, require('./v1/patents/patentCategories'));
router.use(`${API_PREFIX}/patentParticipants`, require('./v1/patents/patentParticipants'));

// 会议模块
router.use(`${API_PREFIX}/conferences`, require('./v1/conferences/conferences'));
router.use(`${API_PREFIX}/conferencesLevels`, require('./v1/conferences/conferencesLevels'));
router.use(`${API_PREFIX}/conferencesParticipants`, require('./v1/conferences/conferencesParticipants'));

// 教材模块
router.use(`${API_PREFIX}/textbooks`, require('./v1/textbooks/textbooks'));
router.use(`${API_PREFIX}/textbookCategories`, require('./v1/textbooks/textbookCategories'));

// 学术任职模块
router.use(`${API_PREFIX}/academicAppointments`, require('./v1/academicAppointments/academicAppointments'));
router.use(`${API_PREFIX}/associationLevels`, require('./v1/academicAppointments/associationLevels'));

// 指导学生获奖模块
router.use(`${API_PREFIX}/studentAwardGuidanceAwards`, require('./v1/studentAwardGuidanceAwards/studentAwardGuidanceAwards'));
router.use(`${API_PREFIX}/studentAwardGuidanceAwardLevels`, require('./v1/studentAwardGuidanceAwards/studentAwardGuidanceAwardLevels'));
router.use(`${API_PREFIX}/studentAwardGuidanceParticipants`, require('./v1/studentAwardGuidanceAwards/studentAwardGuidanceParticipants'));

// 指导学生立项模块
router.use(`${API_PREFIX}/studentProjectGuidanceProjects`, require('./v1/studentProjectGuidanceProjects/studentProjectGuidanceProjects'));
router.use(`${API_PREFIX}/studentProjectGuidanceProjectLevels`, require('./v1/studentProjectGuidanceProjects/studentProjectGuidanceProjectLevels'));
router.use(`${API_PREFIX}/studentProjectGuidanceParticipants`, require('./v1/studentProjectGuidanceProjects/studentProjectGuidanceParticipants'));

// 教学科技奖励模块
router.use(`${API_PREFIX}/teaching-research-awards`, require('./v1/teachingResearchAwards/teachingResearchAwards'));
router.use(`${API_PREFIX}/teaching-research-award-levels`, require('./v1/teachingResearchAwards/teachingResearchAwardLevels'));

// 教学工作量模块
router.use(`${API_PREFIX}/sys/teaching-workloads`, require('./v1/teachingWorkloads/teachingWorkloads'));
router.use(`${API_PREFIX}/sys/teaching-workloads`, require('./v1/teachingWorkloads/teachingWorkloadParticipants'));
router.use(`${API_PREFIX}/sys/teaching-workloads`, require('./v1/teachingWorkloads/teachingWorkloadLevels'));

// ========== 系统管理模块 ==========

// 认证相关
router.use(`${API_PREFIX}/auth`, require('./v1/sys/auth'));
router.use(`${API_PREFIX}/user`, require('./v1/sys/user'));

// 权限管理
router.use(`${API_PREFIX}/role`, require('./v1/sys/role'));
router.use(`${API_PREFIX}/permissions`, require('./v1/sys/permissions'));
router.use(`${API_PREFIX}/resources`, require('./v1/sys/resources'));

// 系统功能
router.use(`${API_PREFIX}/sys/file`, require('./v1/sys/file'));
router.use(`${API_PREFIX}/home`, require('./v1/sys/home'));
router.use(`${API_PREFIX}/optLog`, require('./v1/sys/optLog'));
router.use(`${API_PREFIX}/statistics`, require('./v1/sys/statistics'));
router.use(`${API_PREFIX}/teacher`, require('./v1/sys/teacher'));

// 通知系统
router.use(`${API_PREFIX}/sys/notifications`, require('./v1/sys/notifications'));

// 用户个人通知
router.use(`${API_PREFIX}/common/notifications`, require('./v1/common/userNotifications'));

// 通知设置管理
router.use(`${API_PREFIX}/sys/notifications/settings`, require('./v1/sys/notificationSettings'));

// 通知模板管理
router.use(`${API_PREFIX}/sys/notifications/templates`, require('./v1/sys/notificationTemplates'));

// 用户级别管理
router.use(`${API_PREFIX}/sys/user-levels`, require('./v1/sys/userLevels'));

// 用户职称记录管理
router.use(`${API_PREFIX}/sys/user-level-records`, require('./v1/sys/userLevelRecords'));

// 部门管理
router.use(`${API_PREFIX}/sys/departments`, require('./v1/sys/departments'));

// 用户排名和绩效分析
router.use(`${API_PREFIX}/ranking`, require('./v1/sys/userRanking'));

// 数据库备份管理
router.use(`${API_PREFIX}/admin/backup`, require('./v1/admin/backup'));

// ========== 规则配置模块 ==========

router.use(`${API_PREFIX}/scoreWeights`, require('./v1/sys/scoreWeights'));
router.use(`${API_PREFIX}/awards`, require('./v1/sys/awards'));
router.use(`${API_PREFIX}/awardsRules`, require('./v1/sys/awardsRulesRoute'));
router.use(`${API_PREFIX}/internationalExchangesRules`, require('./v1/sys/internationalExchangesRulesRoute'));
router.use(`${API_PREFIX}/socialServicesRules`, require('./v1/sys/socialServicesRulesRoute'));
router.use(`${API_PREFIX}/deductionsRules`, require('./v1/sys/deductionsRulesRoute'));
router.use(`${API_PREFIX}/timeInterval`, require('./v1/sys/timeIntervalRoute'));

// ========== 其他模块 ==========

router.use(`${API_PREFIX}/researchFunds`, require('./v1/sys/researchFunds'));
router.use(`${API_PREFIX}/researchFundsRules`, require('./v1/sys/researchFundsRules'));
router.use(`${API_PREFIX}/international`, require('./v1/sys/international'));
router.use(`${API_PREFIX}/socialService`, require('./v1/sys/socialService'));
router.use(`${API_PREFIX}/employment`, require('./v1/sys/employment'));
router.use(`${API_PREFIX}/deduction`, require('./v1/sys/deduction'));

// SQL查询接口
router.use(`${API_PREFIX}/sql`, require('./v1/sql/sql'));



// 健康检查路由
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'API服务运行正常',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
