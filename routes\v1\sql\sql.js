const express = require('express');
const router = express.Router();
const sqlController = require('../../../controllers/v1/sql/sqlController');

/**
 * 创建并更新排名缓存表
 * @route POST /v1/sys/sql/update-ranking-tables
 * @group 排名管理 - 排名数据管理相关接口
 * @description 创建三个排名缓存表（范围内已审核、范围外已审核、全部已审核）并计算填充最新数据
 * @returns {object} 200 - {code: 200, message: "排名缓存表更新成功", data: {tables: [], updateTime: "日期时间"}}
 * @returns {object} 403 - {code: 403, message: "您没有权限更新排名缓存表", data: null}
 * @returns {object} 500 - {code: 500, message: "更新排名缓存表失败: 错误信息", data: null}
 * @security JWT
 */
router.post('/update-ranking-tables', sqlController.updateRankingTables);


/**
 * 获取用户排名数据
 * @route GET /v1/sys/sql/get-user-rankings
 * @group 排名管理 - 排名数据管理相关接口
 * @description 获取用户排名数据，支持分页和筛选
 * @param {string} range - 统计范围筛选，可选值：'in'(范围内), 'out'(范围外), 'all'(全部)，默认'all'
 * @param {number} page - 页码，默认1
 * @param {number} limit - 每页条数，默认10
 * @param {string} userId - 用户ID（可选，如果提供则只获取特定用户的排名数据）
 * @returns {object} 200 - {code: 200, message: "获取排名数据成功", result: {records: [], total: 总数量, size: 每页大小, current: 当前页码, pages: 总页数}}
 * @returns {object} 400 - {code: 400, message: "参数错误：错误详情", data: null}
 * @returns {object} 500 - {code: 500, message: "获取排名数据失败: 错误信息", data: null}
 * @security JWT
 */
router.get('/get-user-rankings', sqlController.getUserRankings);

module.exports = router;
