import axios from 'axios';
import config from '@/config';
import dbUtils from '@/libs/util.strotage.js';
import { message } from 'ant-design-vue';

// 强制使用相对路径
const apiBaseUrl = '/v1';
console.log('创建axios实例，强制使用相对路径baseURL:', apiBaseUrl);
console.log('withCredentials设置: true');

// 创建axios实例
const instance = axios.create({
  baseURL: apiBaseUrl, // 强制使用相对路径
  timeout: config.requestTimeout || 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  },
  withCredentials: true // 启用跨域请求时携带cookie，解决会话问题
});

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 在发送请求之前，添加token
    const token = dbUtils.get('token');
    if (token) {
      config.headers.Authorization = token;
    }
    
    console.log('========== API请求开始 ==========');
    console.log('请求方法:', config.method.toUpperCase());
    console.log('请求URL:', config.baseURL + config.url);
    console.log('请求参数:', config.params || config.data || {});
    console.log('请求头:', config.headers);
    
    // 检查是否有token
    if (token) {
      console.log('API请求携带token:', token.substring(0, 15) + '...');
    } else {
      console.log('API请求无token');
      // 对于登录和验证码请求，不需要token
      if (config.url.includes('/auth/login') || config.url.includes('/auth/captcha')) {
        console.log('登录或验证码请求，无需token');
      }
    }
    
    return config;
  },
  error => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    // 对响应数据进行处理
    const res = response.data;
    
    console.log('========== API响应成功 ==========');
    console.log('响应URL:', response.config.url);
    console.log('响应状态:', response.status);
    console.log('响应数据:', res);
    
    // 如果是二进制数据（如文件下载），直接返回
    if (response.config.responseType === 'blob') {
      return response.data;
    }

    // 正常情况返回数据
    if (res.code === 200 || res.status === 'success') {
      console.log('========== API响应成功结束 ==========');
      return res;
    } 
    
    // 处理其他状态码
    console.log('========== API响应错误 ==========');
    console.log('错误码:', res.code);
    console.log('错误信息:', res.message);
    message.error(res.message || '操作失败');
    console.log('========== API响应错误结束 ==========');
    return Promise.reject(res);
  },
  error => {
    console.log('========== API响应错误 ==========');
    console.error('错误信息:', error.message);
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    } else if (error.request) {
      console.error('请求未收到响应:', error.request);
    }
    
    // 处理错误情况
    if (error.response) {
      // 服务器返回了错误状态码
      const status = error.response.status;
      
      switch (status) {
        case 401:
          message.error('未授权，请重新登录');
          // 清除登录信息
          dbUtils.clear();
          // 刷新页面，重定向到登录页
          setTimeout(() => {
            window.location.reload();
          }, 1500);
          break;
        case 403:
          message.error('没有权限访问');
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 500:
          message.error('服务器错误，请稍后再试');
          break;
        default:
          message.error(`请求错误: ${error.response.data?.message || error.message}`);
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      message.error('网络错误，无法连接到服务器');
    } else {
      // 请求配置有误
      message.error(`请求错误: ${error.message}`);
    }
    
    console.log('========== API响应错误结束 ==========');
    return Promise.reject(error);
  }
);

// 封装请求方法
export const request = {
  get(url, params) {
    return instance({
      method: 'get',
      url,
      params
    });
  },
  
  post(url, data) {
    return instance({
      method: 'post',
      url,
      data
    });
  },
  
  put(url, data) {
    return instance({
      method: 'put',
      url,
      data
    });
  },
  
  delete(url, data) {
    return instance({
      method: 'delete',
      url,
      data
    });
  },
  
  // 支持将数据作为查询参数的删除请求
  deleteWithQuery(url) {
    return instance({
      method: 'delete',
      url
    });
  },
  
  // 支持URL参数的POST请求
  postWithQuery(url, data) {
    return instance({
      method: 'post',
      url,
      data
    });
  },
  
  // 支持URL参数的PUT请求
  putWithQuery(url, data) {
    return instance({
      method: 'put',
      url,
      data
    });
  }
};

export default request;
