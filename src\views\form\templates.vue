<template>
  <div class="form-templates">
    <a-card title="表单模板" :bordered="false">
      <template #extra>
        <a-button type="primary" @click="showCreateModal">
          <template #icon><PlusOutlined /></template>
          创建模板
        </a-button>
      </template>

      <a-table :columns="columns" :data-source="data" :pagination="pagination" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === '启用' ? 'green' : 'red'">
              {{ record.status }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="editTemplate(record)">编辑</a>
              <a-divider type="vertical" />
              <a @click="previewTemplate(record)">预览</a>
              <a-divider type="vertical" />
              <a-popconfirm
                :title="record.status === '启用' ? '确定要禁用该模板吗？' : '确定要启用该模板吗？'"
                @confirm="toggleTemplateStatus(record)"
              >
                <a>{{ record.status === '启用' ? '禁用' : '启用' }}</a>
              </a-popconfirm>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除该模板吗？"
                @confirm="deleteTemplate(record)"
              >
                <a class="text-danger">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="createModalVisible"
      :title="isEdit ? '编辑模板' : '创建模板'"
      @ok="handleCreateModalOk"
      @cancel="handleCreateModalCancel"
      width="800px"
    >
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="模板名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入模板名称" />
        </a-form-item>

        <a-form-item label="模板类型" name="type">
          <a-select v-model:value="formState.type" placeholder="请选择模板类型">
            <a-select-option value="research">科研项目</a-select-option>
            <a-select-option value="teaching">教学获奖</a-select-option>
            <a-select-option value="international">国际交流</a-select-option>
            <a-select-option value="social">社会服务</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="表单字段" name="fields">
          <div v-for="(field, index) in formState.fields" :key="index" class="field-item">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-input v-model:value="field.label" placeholder="字段名称" />
              </a-col>
              <a-col :span="6">
                <a-select v-model:value="field.type" placeholder="字段类型">
                  <a-select-option value="text">文本</a-select-option>
                  <a-select-option value="number">数字</a-select-option>
                  <a-select-option value="date">日期</a-select-option>
                  <a-select-option value="select">选择</a-select-option>
                  <a-select-option value="file">文件</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-switch v-model:checked="field.required" checkedChildren="必填" unCheckedChildren="选填" />
              </a-col>
              <a-col :span="6">
                <a-button type="link" danger @click="removeField(index)">删除</a-button>
              </a-col>
            </a-row>
          </div>
          <a-button type="dashed" block @click="addField">
            <template #icon><PlusOutlined /></template>
            添加字段
          </a-button>
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" :rows="4" placeholder="请输入备注信息" />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal
      v-model:visible="previewModalVisible"
      title="模板预览"
      @ok="handlePreviewModalOk"
      @cancel="handlePreviewModalCancel"
      width="800px"
    >
      <div class="preview-form">
        <h3>{{ currentTemplate?.name }}</h3>
        <p class="template-meta">
          <span>类型：{{ getTemplateTypeName(currentTemplate?.type) }}</span>
          <span>创建时间：{{ currentTemplate?.createTime }}</span>
        </p>
        <a-divider />
        <div v-for="(field, index) in currentTemplate?.fields" :key="index" class="preview-field">
          <label>{{ field.label }}</label>
          <div class="preview-input">
            <template v-if="field.type === 'text'">
              <a-input placeholder="请输入" />
            </template>
            <template v-else-if="field.type === 'number'">
              <a-input-number style="width: 100%" placeholder="请输入" />
            </template>
            <template v-else-if="field.type === 'date'">
              <a-date-picker style="width: 100%" />
            </template>
            <template v-else-if="field.type === 'select'">
              <a-select placeholder="请选择">
                <a-select-option value="option1">选项1</a-select-option>
                <a-select-option value="option2">选项2</a-select-option>
              </a-select>
            </template>
            <template v-else-if="field.type === 'file'">
              <a-upload>
                <a-button>
                  <template #icon><UploadOutlined /></template>
                  选择文件
                </a-button>
              </a-upload>
            </template>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { PlusOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 表格列定义
const columns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '模板类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 模拟数据
const data = ref([
  {
    key: '1',
    name: '科研项目评分表',
    type: 'research',
    createTime: '2023-12-01 10:00:00',
    status: '启用',
    fields: [
      { label: '项目名称', type: 'text', required: true },
      { label: '项目级别', type: 'select', required: true },
      { label: '项目金额', type: 'number', required: true },
      { label: '项目成果', type: 'text', required: false }
    ]
  },
  {
    key: '2',
    name: '教学获奖评分表',
    type: 'teaching',
    createTime: '2023-12-05 14:30:00',
    status: '启用',
    fields: [
      { label: '获奖名称', type: 'text', required: true },
      { label: '获奖级别', type: 'select', required: true },
      { label: '获奖时间', type: 'date', required: true },
      { label: '获奖证书', type: 'file', required: true }
    ]
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 2,
  showSizeChanger: true,
  showQuickJumper: true,
})

// 创建/编辑模板相关
const createModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref(null)
const formState = reactive({
  name: '',
  type: undefined,
  fields: [],
  remark: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在2-50个字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择模板类型', trigger: 'change' }
  ],
  fields: [
    { required: true, message: '请至少添加一个字段', trigger: 'change' }
  ]
}

// 预览相关
const previewModalVisible = ref(false)
const currentTemplate = ref(null)

// 获取模板类型名称
const getTemplateTypeName = (type) => {
  const typeMap = {
    research: '科研项目',
    teaching: '教学获奖',
    international: '国际交流',
    social: '社会服务'
  }
  return typeMap[type] || type
}

// 添加字段
const addField = () => {
  formState.fields.push({
    label: '',
    type: 'text',
    required: false
  })
}

// 删除字段
const removeField = (index) => {
  formState.fields.splice(index, 1)
}

// 创建模板
const showCreateModal = () => {
  isEdit.value = false
  formState.name = ''
  formState.type = undefined
  formState.fields = []
  formState.remark = ''
  createModalVisible.value = true
}

// 编辑模板
const editTemplate = (record) => {
  isEdit.value = true
  Object.assign(formState, record)
  createModalVisible.value = true
}

// 预览模板
const previewTemplate = (record) => {
  currentTemplate.value = record
  previewModalVisible.value = true
}

// 切换模板状态
const toggleTemplateStatus = (record) => {
  record.status = record.status === '启用' ? '禁用' : '启用'
  message.success(`模板已${record.status}`)
}

// 删除模板
const deleteTemplate = (record) => {
  data.value = data.value.filter(item => item.key !== record.key)
  message.success('模板已删除')
}

// 处理创建/编辑模态框确认
const handleCreateModalOk = () => {
  formRef.value.validate().then(() => {
    // 这里添加保存模板的逻辑
    message.success(`${isEdit.value ? '编辑' : '创建'}成功！`)
    createModalVisible.value = false
  }).catch(error => {
    console.log('Validation failed:', error)
  })
}

// 处理创建/编辑模态框取消
const handleCreateModalCancel = () => {
  createModalVisible.value = false
}

// 处理预览模态框确认
const handlePreviewModalOk = () => {
  previewModalVisible.value = false
}

// 处理预览模态框取消
const handlePreviewModalCancel = () => {
  previewModalVisible.value = false
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
  console.log('Table changed:', pag, filters, sorter)
}
</script>

<style scoped>
.form-templates {
  padding: 24px;
}

.field-item {
  margin-bottom: 16px;
}

.template-meta {
  color: #666;
  margin: 10px 0;
}

.template-meta span {
  margin-right: 20px;
}

.preview-field {
  margin-bottom: 16px;
}

.preview-field label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.preview-input {
  width: 100%;
}

.text-danger {
  color: #ff4d4f;
}
</style> 