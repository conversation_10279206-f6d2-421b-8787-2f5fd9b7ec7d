<template>
  <div class="notification-list">
    <a-card title="通知列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-select
            v-model:value="filterType"
            style="width: 120px"
            placeholder="通知类型"
            @change="handleFilterChange"
          >
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option value="system">系统通知</a-select-option>
            <a-select-option value="important">重要通知</a-select-option>
            <a-select-option value="reminder">提醒通知</a-select-option>
            <a-select-option value="normal">普通通知</a-select-option>
            <a-select-option value="result">结果通知</a-select-option>
          </a-select>
          <a-button type="primary" @click="showSendModal" v-permission="'notification:create'">
            <template #icon><SendOutlined /></template>
            发送通知
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="filteredData"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">

          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeName(record.type) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="viewNotification(record)">查看</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这条通知吗？"
                @confirm="deleteNotification(record)"
                v-permission="'notification:create'"
              >
                <a class="text-danger" v-permission="'notification:create'">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="viewModalVisible"
      title="通知详情"
      @ok="handleViewModalOk"
      @cancel="handleViewModalCancel"
    >
      <template v-if="currentNotification">
        <h3>{{ currentNotification.title }}</h3>
        <p class="notification-meta">
          <span>发送时间：{{ formatTime(currentNotification.createdAt) }}</span>
          <span>发送人：{{ currentNotification.initiator?.nickname || '系统' }}</span>
          <span>类型：{{ getTypeName(currentNotification.type) }}</span>
        </p>
        <div class="notification-content">
          {{ currentNotification.content }}
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { SendOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  getNotificationsList,
  deleteNotification as deleteNotificationAPI
  // updateNotification // 暂时不使用，后续实现用户读取状态表时启用
} from '@/api/modules/api.notifications'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表格列定义
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '发送人',
    dataIndex: ['initiator', 'nickname'],
    key: 'initiator',
    width: 120,
    customRender: ({ record }) => {
      return record.initiator?.nickname || '系统'
    }
  },
  {
    title: '发送方式',
    dataIndex: 'sendMode',
    key: 'sendMode',
    width: 100,
    customRender: ({ record }) => {
      const modeMap = {
        'all': '全员',
        'department': '部门',
        'user_level': '职称级别',
        'single': '个人'
      }
      return modeMap[record.sendMode] || record.sendMode
    }
  },
  {
    title: '发送目标',
    dataIndex: 'target',
    key: 'target',
    width: 150,
    ellipsis: true,
    customRender: ({ record }) => {
      if (record.sendMode === 'all') {
        return '全体用户'
      } else if (record.sendMode === 'department' && record.targetDepartment) {
        return record.targetDepartment.departmentName
      } else if (record.sendMode === 'user_level' && record.targetUserLevel) {
        return record.targetUserLevel.levelName
      } else if (record.sendMode === 'single' && record.targetUser) {
        return record.targetUser.nickname || record.targetUser.username
      }
      return '-'
    }
  },
  {
    title: '发送时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180,
    sorter: true,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleString('zh-CN') : ''
    }
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 通知过滤
const filterType = ref('')
const loading = ref(false)
const notifications = ref([])

// 过滤后的数据
const filteredData = computed(() => {
  if (!filterType.value) {
    return notifications.value
  }
  return notifications.value.filter(item => item.type === filterType.value)
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadNotifications()
  }
})

// 加载通知列表
const loadNotifications = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    if (filterType.value) {
      params.type = filterType.value
    }

    const response = await getNotificationsList(params)

    if (response.code === 200) {
      notifications.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载通知列表失败:', error)
    message.error('加载通知列表失败')
  } finally {
    loading.value = false
  }
}

// 查看通知相关
const viewModalVisible = ref(false)
const currentNotification = ref(null)

const viewNotification = async (record) => {
  currentNotification.value = record
  viewModalVisible.value = true

  // try {
  //   const response = await updateNotification(record.id, { is_read: true })
  //   if (response.code === 200) {
  //     // 更新本地状态
  //   }
  // } catch (error) {
  //   console.error('标记已读失败:', error)
  // }
}

const handleViewModalOk = () => {
  viewModalVisible.value = false
}

const handleViewModalCancel = () => {
  viewModalVisible.value = false
}

// 删除通知
const deleteNotification = async (record) => {
  try {
    const response = await deleteNotificationAPI(record.id)
    if (response.code === 200) {
      message.success('通知已删除')
      loadNotifications() // 重新加载列表
    } else {
      message.error(response.message || '删除通知失败')
    }
  } catch (error) {
    console.error('删除通知失败:', error)
    message.error('删除通知失败')
  }
}

// 发送通知
const showSendModal = () => {
  router.push('/notification/send')
}

// 表格变化处理
const handleTableChange = (pag, _filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize

  // 处理排序
  if (sorter.field && sorter.order) {
    // 实际项目中可以调用API进行服务器端排序
    console.log('排序字段:', sorter.field)
    console.log('排序方式:', sorter.order)
  }

  loadNotifications()
}

// 过滤变化处理
const handleFilterChange = (value) => {
  filterType.value = value
  pagination.current = 1 // 重置到第一页
  loadNotifications()
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  return new Date(timeString).toLocaleString('zh-CN')
}

// 获取通知类型名称
const getTypeName = (type) => {
  const typeMap = {
    'system': '系统通知',
    'important': '重要通知',
    'reminder': '提醒通知',
    'normal': '普通通知',
    'result': '结果通知'
  }
  return typeMap[type] || '未知类型'
}

// 获取通知类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    'system': 'cyan',
    'important': 'red',
    'reminder': 'orange',
    'normal': 'blue',
    'result': 'green'
  }
  return colorMap[type] || 'default'
}

// 初始化数据
onMounted(() => {
  loadNotifications()
})
</script>

<style scoped>
.notification-list {
  padding: 24px;
}

.notification-meta {
  color: #666;
  margin: 10px 0;
}

.notification-meta span {
  margin-right: 20px;
}

.notification-content {
  margin-top: 20px;
  line-height: 1.6;
}

.text-danger {
  color: #ff4d4f;
}
</style> 