<template>
  <div class="international-exchange-container">
    <a-card title="E国际交流评分管理">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal" v-permission="showPersonalExchanges ? 'score:E:self:create' : 'score:E:admin:create'">
            <template #icon><plus-outlined /></template>
            新增交流
          </a-button>
          <a-button type="primary" @click="handleExport" v-permission="'score:E:admin:list'">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button :type="showPersonalExchanges ? 'default' : 'primary'" @click="togglePersonalExchanges">
            <template #icon><user-outlined /></template>
            {{ showPersonalExchanges ? '查看全部交流' : '查看我的交流' }}
          </a-button>
        </a-space>
      </template>
      
      <a-row :gutter="16">
        <a-col :span="24">
          <a-alert
            :message="showPersonalExchanges ? '我的国际交流评分统计' : '国际交流评分管理'"
            :description="showPersonalExchanges ? 
              '查看我的国际交流评分情况，包括交流类型、交流国家/地区、交流时间、交流成果等评分标准。' : 
              '管理用户国际交流评分，包括交流类型、交流国家/地区、交流时间、交流成果等评分标准。'"
            type="info"
            show-icon
          />
        </a-col>
      </a-row>
      <a-divider />
      
      <!-- 个人统计卡片 - 仅在个人视图时显示 -->
      <template v-if="showPersonalExchanges">
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :span="8">
            <a-card>
              <a-statistic
                title="我的总得分"
                :value="personalStats.totalScore || 0"
                :precision="2"
                style="margin-right: 50px"
              >
                <template #prefix>
                  <trophy-outlined style="color: gold" />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card>
              <a-statistic
                title="交流次数"
                :value="personalStats.exchangeCount || 0"
                style="margin-right: 50px"
              >
                <template #prefix>
                  <team-outlined style="color: #1890ff" />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card>
              <a-statistic
                title="累计交流天数"
                :value="personalStats.totalDays || 0"
                style="margin-right: 50px"
              >
                <template #prefix>
                  <calendar-outlined style="color: #52c41a" />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </template>
      
      <!-- 个人视图时的图表和最近记录 -->
      <template v-if="showPersonalExchanges">
        <a-tabs default-active-key="1" style="margin-bottom: 24px">
          <a-tab-pane key="1" tab="交流图表">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card title="我的交流类型分布">
                  <div ref="personalTypeChartRef" style="height: 300px"></div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="我的交流国家/地区分布">
                  <div ref="personalCountryChartRef" style="height: 300px"></div>
                </a-card>
              </a-col>
            </a-row>
            <a-row :gutter="16" style="margin-top: 16px">
              <a-col :span="12">
                <a-card title="我的交流时间分布">
                  <div ref="personalTimeChartRef" style="height: 300px"></div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card title="我的评分趋势">
                  <div ref="personalScoreChartRef" style="height: 300px"></div>
                </a-card>
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane key="2" tab="最近交流记录">
            <a-table
              :columns="columns"
              :data-source="personalStats.recentExchanges || []"
              :pagination="false"
              :loading="personalStatsLoading"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a @click="showEditModal(record)" v-permission="showPersonalExchanges ? 'score:E:self:update' : 'score:E:admin:update'">编辑</a>
                    <a-divider type="vertical" />
                    <a @click="confirmDeleteRecord(record)" v-permission="showPersonalExchanges ? 'score:E:self:delete' : 'score:E:admin:delete'">删除</a>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </template>
      
      <!-- 全局统计图表 - 仅在全局视图显示 -->
      <template v-else>
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :span="12">
            <a-card title="交流类型分布">
              <div ref="exchangeTypeChartRef" style="height: 300px"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="交流国家/地区分布">
              <div ref="exchangeCountryChartRef" style="height: 300px"></div>
            </a-card>
          </a-col>
        </a-row>
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :span="12">
            <a-card title="交流时间分布">
              <div ref="exchangeTimeChartRef" style="height: 300px"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="用户评分排名">
              <div ref="userScoreChartRef" style="height: 300px"></div>
            </a-card>
          </a-col>
        </a-row>
      </template>
      
      <!-- 搜索表单 -->
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <!-- 全局视图下显示用户搜索 -->
        <a-form-item v-if="!showPersonalExchanges" label="参与用户" name="usernameList">
          <a-select
            v-model:value="searchForm.usernameList"
            mode="tags"
            placeholder="请选择参与用户"
            style="width: 200px"
            :options="userOptions.map(user => ({ value: user.username || user.nickname, label: user.username || user.nickname }))"
            :filter-option="filterUserOption"
          ></a-select>
        </a-form-item>
        <a-form-item label="交流类型" name="type">
          <a-select v-model:value="searchForm.type" placeholder="请选择交流类型" style="width: 200px" allowClear>
            <a-select-option value="学术访问">学术访问</a-select-option>
            <a-select-option value="国际会议">国际会议</a-select-option>
            <a-select-option value="合作研究">合作研究</a-select-option>
            <a-select-option value="短期培训">短期培训</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="交流国家/地区" name="country">
          <a-input v-model:value="searchForm.country" placeholder="请输入交流国家/地区" />
        </a-form-item>
        <a-form-item label="交流机构" name="institution">
          <a-input v-model:value="searchForm.institution" placeholder="请输入交流机构" />
        </a-form-item>
        <a-form-item label="交流时间" name="dateRange">
          <a-range-picker v-model:value="searchForm.dateRange" style="width: 230px" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">搜索</a-button>
          <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
        </a-form-item>
      </a-form>
      
      <a-divider />
      
      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="showEditModal(record)" v-permission="showPersonalExchanges ? 'score:E:self:update' : 'score:E:admin:update'">编辑</a>
              <a-divider type="vertical" />
              <a @click="confirmDeleteRecord(record)" v-permission="showPersonalExchanges ? 'score:E:self:delete' : 'score:E:admin:delete'">删除</a>
            </a-space>
          </template>
        </template>
      </a-table>
      
      <!-- 新增/编辑模态框 -->
      <a-modal
        :title="modalTitle"
        :visible="modalVisible"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="confirmLoading"
      >
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="参与用户" name="usernameList">
            <a-select
              v-model:value="formState.usernameList"
              mode="tags"
              placeholder="请输入或选择参与用户"
              style="width: 100%"
              :tokenSeparators="[',']"
              @change="handleUserSelectionChange"
            >
              <a-select-option v-for="user in userOptions" :key="user.id" :value="user.username || user.nickname">
                {{ user.username || user.nickname }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="参与用户ID" name="userIdList" style="display: none">
            <a-select
              v-model:value="formState.userIdList"
              mode="tags"
              placeholder="用户ID"
              style="width: 100%"
            >
            </a-select>
          </a-form-item>
          <a-form-item label="交流名称" name="name">
            <a-input v-model:value="formState.name" placeholder="请输入交流名称（可自动生成）" />
          </a-form-item>
          <a-form-item label="交流类型" name="type">
            <a-select v-model:value="formState.type" placeholder="请选择交流类型">
              <a-select-option value="学术访问">学术访问</a-select-option>
              <a-select-option value="国际会议">国际会议</a-select-option>
              <a-select-option value="合作研究">合作研究</a-select-option>
              <a-select-option value="短期培训">短期培训</a-select-option>
              <a-select-option value="其他">其他</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="交流国家/地区" name="country">
            <a-input v-model:value="formState.country" placeholder="请输入交流国家/地区" />
          </a-form-item>
          <a-form-item label="交流机构" name="institution">
            <a-input v-model:value="formState.institution" placeholder="请输入交流机构" />
          </a-form-item>
          <a-form-item label="交流时间" name="dateRange">
            <a-range-picker
              v-model:value="formState.dateRange"
              style="width: 100%"
              placeholder="请选择交流时间范围"
            />
          </a-form-item>
          <a-form-item label="交流内容" name="content">
            <a-textarea
              v-model:value="formState.content"
              :rows="4"
              placeholder="请输入交流内容"
            />
          </a-form-item>
          <a-form-item label="交流成果" name="result">
            <a-textarea
              v-model:value="formState.result"
              :rows="4"
              placeholder="请输入交流成果"
            />
          </a-form-item>
          <a-form-item label="评分" name="score">
            <a-input-number
              v-model:value="formState.score"
              :min="0"
              :max="100"
              style="width: 100%"
              placeholder="请输入评分"
            />
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="formState.remark"
              :rows="4"
              placeholder="请输入备注"
            />
          </a-form-item>
          
          <!-- 添加文件上传组件 -->
          <a-form-item label="附件" name="fileList">
            <a-upload
              :file-list="fileList"
              :before-upload="beforeFileUpload"
              :customRequest="customFileUpload"
              :remove="handleRemove"
              @preview="handleFilePreview"
            >
              <div>
                <a-button>
                  <upload-outlined />
                  <span>上传文件</span>
                </a-button>
                <a-tooltip title="支持常见文档、图片格式，单个文件不超过5MB">
                  <question-circle-outlined style="margin-left: 8px" />
                </a-tooltip>
              </div>
            </a-upload>
          </a-form-item>
        </a-form>
      </a-modal>
      
      <!-- 文件预览弹窗 -->
      <a-modal
        :visible="previewVisible"
        :title="previewTitle"
        :footer="null"
        @cancel="previewVisible = false"
      >
        <img alt="预览图片" style="width: 100%" :src="previewImage" />
      </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, onUnmounted, nextTick } from 'vue';
import { 
  PlusOutlined, 
  ExportOutlined, 
  UserOutlined, 
  TeamOutlined,
  CalendarOutlined,
  TrophyOutlined,
  UploadOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue';
import { message, Modal, Radio, Input } from 'ant-design-vue';
import * as echarts from 'echarts';
import { 
  getInternationalExchanges, 
  addInternationalExchange, 
  updateInternationalExchange, 
  deleteInternationalExchange,
  exportInternationalExchanges,
  getPersonalExchangeStats,
  getUserList,
  //getExchangeDetail,
  //reviewExchange
} from '@/api/modules/api.international-exchange';
import { useUserId } from '@/composables/useUserId';
import axios from 'axios';
import dayjs from 'dayjs';

// 表格列定义
const columns = [
  {
    title: '参与用户',
    dataIndex: 'usernameList',
    key: 'usernameList',
    render: (text) => text || '-'
  },
  {
    title: '交流名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '交流类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '交流国家/地区',
    dataIndex: 'country',
    key: 'country',
  },
  {
    title: '交流机构',
    dataIndex: 'institution',
    key: 'institution',
  },
  {
    title: '交流时间',
    dataIndex: 'exchangeDate',
    key: 'exchangeDate',
    render: (_, record) => `${record.startDate} 至 ${record.endDate}`
  },
  {
    title: '交流天数',
    dataIndex: 'exchangeDays',
    key: 'exchangeDays',
    sorter: true,
  },
  {
    title: '评分',
    dataIndex: 'score',
    key: 'score',
    sorter: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
    align: 'center'
  },
];

// 搜索表单
const searchForm = reactive({
  usernameList: [],
  type: undefined,
  country: '',
  institution: '',
  dateRange: null,
});

// 表单状态
const formState = reactive({
  id: '',
  usernameList: [],
  userIdList: [],
  name: '',
  type: undefined,
  country: '',
  institution: '',
  dateRange: null,
  content: '',
  result: '',
  score: undefined,
  remark: '',
  fileList: [] // 添加文件列表字段
});

// 教师选项
const teacherOptions = ref([
]);

// 表单验证规则
const rules = {
  usernameList: [{ required: true, message: '请至少选择一名用户', trigger: 'change', type: 'array' }],
  type: [{ required: true, message: '请选择交流类型', trigger: 'change' }],
  country: [{ required: true, message: '请输入交流国家/地区', trigger: 'blur' }],
  institution: [{ required: true, message: '请输入交流机构', trigger: 'blur' }],
  dateRange: [{ required: true, message: '请选择交流时间', trigger: 'change' }],
  content: [{ required: true, message: '请输入交流内容', trigger: 'blur' }],
  score: [{ required: true, message: '请输入评分', trigger: 'blur' }],
};

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 模态框状态
const modalVisible = ref(false);
const modalTitle = ref('新增交流');
const confirmLoading = ref(false);
const formRef = ref(null);

// 图表引用
const exchangeTypeChartRef = ref(null);
const exchangeCountryChartRef = ref(null);
const exchangeTimeChartRef = ref(null);
const userScoreChartRef = ref(null);

// 个人视图图表引用
const personalTypeChartRef = ref(null);
const personalCountryChartRef = ref(null);
const personalTimeChartRef = ref(null);
const personalScoreChartRef = ref(null);

// 图表实例
let exchangeTypeChart = null;
let exchangeCountryChart = null;
let exchangeTimeChart = null;
let userScoreChart = null;

// 个人视图图表实例
let personalTypeChart = null;
let personalCountryChart = null;
let personalTimeChart = null;
let personalScoreChart = null;

// 个人视图状态
const showPersonalExchanges = ref(false);
const personalStatsLoading = ref(false);
const personalStats = reactive({
  exchangeCount: 0,
  totalDays: 0,
  totalScore: 0,
  typeDistribution: {},
  countryDistribution: {},
  timeDistribution: {},
  scoreTrend: {},
  recentExchanges: []
});

// 获取当前用户ID
const { getUserId } = useUserId();

// 用户选项
const userOptions = ref([]);

// 获取用户列表
const fetchUserList = async () => {
  try {
    const response = await getUserList();
    if (response.code === 200 && response.data) {
      userOptions.value = response.data;
    } else {
      message.error('获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  }
};

// 用户选择过滤
const filterUserOption = (input, option) => {
  return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
};

// 初始化图表
const initCharts = () => {
  // 先销毁可能已存在的图表实例
  if (exchangeTypeChart) {
    exchangeTypeChart.dispose();
    exchangeTypeChart = null;
  }
  if (exchangeCountryChart) {
    exchangeCountryChart.dispose();
    exchangeCountryChart = null;
  }
  if (exchangeTimeChart) {
    exchangeTimeChart.dispose();
    exchangeTimeChart = null;
  }
  if (userScoreChart) {
    userScoreChart.dispose();
    userScoreChart = null;
  }

  // 确保DOM元素已经渲染
  if (!exchangeTypeChartRef.value || !exchangeCountryChartRef.value || 
      !exchangeTimeChartRef.value || !userScoreChartRef.value) {
    console.log('图表DOM元素未准备好，延迟初始化');
    setTimeout(initCharts, 100);
    return;
  }

  // 交流类型分布图
  exchangeTypeChart = echarts.init(exchangeTypeChartRef.value);
  const exchangeTypeOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: ['学术访问', '国际会议', '合作研究', '短期培训', '其他']
    },
    series: [
      {
        name: '交流类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 0, name: '学术访问' },
          { value: 0, name: '国际会议' },
          { value: 0, name: '合作研究' },
          { value: 0, name: '短期培训' },
          { value: 0, name: '其他' }
        ]
      }
    ]
  };
  exchangeTypeChart.setOption(exchangeTypeOption);

  // 交流国家/地区分布图
  exchangeCountryChart = echarts.init(exchangeCountryChartRef.value);
  const exchangeCountryOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: []
    },
    series: [
      {
        name: '交流国家/地区',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: []
      }
    ]
  };
  exchangeCountryChart.setOption(exchangeCountryOption);

  // 交流时间分布图
  exchangeTimeChart = echarts.init(exchangeTimeChartRef.value);
  const exchangeTimeOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '交流数量',
        type: 'bar',
        data: new Array(12).fill(0)
      }
    ]
  };
  exchangeTimeChart.setOption(exchangeTimeOption);

  // 教师评分排名图
  userScoreChart = echarts.init(userScoreChartRef.value);
  const userScoreOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: []
    },
    series: [
      {
        name: '评分',
        type: 'bar',
        data: []
      }
    ]
  };
  userScoreChart.setOption(userScoreOption);
};

// 更新图表
const updateCharts = () => {
  // 确保DOM元素已经存在
  if (!exchangeTypeChartRef.value || !exchangeCountryChartRef.value || 
      !exchangeTimeChartRef.value || !userScoreChartRef.value) {
    console.warn('图表DOM元素未准备好，延迟更新图表');
    return;
  }

  // 先销毁原有图表实例
  if (exchangeTypeChart) {
    exchangeTypeChart.dispose();
  }
  if (exchangeCountryChart) {
    exchangeCountryChart.dispose();
  }
  if (exchangeTimeChart) {
    exchangeTimeChart.dispose();
  }
  if (userScoreChart) {
    userScoreChart.dispose();
  }
  
  // 重新初始化图表
  exchangeTypeChart = echarts.init(exchangeTypeChartRef.value);
  exchangeCountryChart = echarts.init(exchangeCountryChartRef.value);
  exchangeTimeChart = echarts.init(exchangeTimeChartRef.value);
  userScoreChart = echarts.init(userScoreChartRef.value);

  // 更新交流类型分布
  const exchangeTypeCount = {
    '学术访问': 0,
    '国际会议': 0,
    '合作研究': 0,
    '短期培训': 0,
    '其他': 0
  };
  dataSource.value.forEach(item => {
    exchangeTypeCount[item.type] = (exchangeTypeCount[item.type] || 0) + 1;
  });
  exchangeTypeChart.setOption({
    title: {
      text: '交流类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: Object.keys(exchangeTypeCount)
    },
    series: [{
      name: '交流类型',
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '20',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: Object.entries(exchangeTypeCount).map(([name, value]) => ({ name, value }))
    }]
  });

  // 更新交流国家/地区分布
  const exchangeCountryCount = {};
  dataSource.value.forEach(item => {
    exchangeCountryCount[item.country] = (exchangeCountryCount[item.country] || 0) + 1;
  });
  const countryData = Object.entries(exchangeCountryCount)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 10);
  exchangeCountryChart.setOption({
    title: {
      text: '交流国家/地区分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: countryData.map(item => item.name)
    },
    series: [{
      name: '交流国家/地区',
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '20',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: countryData
    }]
  });

  // 更新交流时间分布
  const exchangeTimeCount = new Array(12).fill(0);
  dataSource.value.forEach(item => {
    if (item.startDate && item.endDate) {
      const startDate = new Date(item.startDate);
      const endDate = new Date(item.endDate);
      
      // 如果日期有效
      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        for (let date = new Date(startDate); date <= endDate; date.setMonth(date.getMonth() + 1)) {
          exchangeTimeCount[date.getMonth()]++;
        }
      }
    }
  });
  exchangeTimeChart.setOption({
    title: {
      text: '交流时间分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '交流数量',
      type: 'bar',
      data: exchangeTimeCount,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#83bff6' },
          { offset: 0.5, color: '#188df0' },
          { offset: 1, color: '#188df0' }
        ])
      },
    }]
  });

  // 更新用户评分排名
  const userScores = {};
  dataSource.value.forEach(item => {
    // 处理用户名称，可能是逗号分隔的字符串
    const usernameList = typeof item.usernameList === 'string' 
      ? item.usernameList.split(',') 
      : Array.isArray(item.usernameList) ? item.usernameList : [item.usernameList];
      
    usernameList.forEach(username => {
      if (!username) return;
      
      if (!userScores[username]) {
        userScores[username] = {
          total: 0,
          count: 0
        };
      }
      userScores[username].total += Number(item.score) || 0;
      userScores[username].count++;
    });
  });
  
  const userScoreData = Object.entries(userScores)
    .map(([name, data]) => ({
      name,
      score: data.count > 0 ? data.total / data.count : 0
    }))
    .sort((a, b) => b.score - a.score)
    .slice(0, 10);
  
  userScoreChart.setOption({
    title: {
      text: '用户评分排名',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: userScoreData.map(item => item.name)
    },
    series: [{
      name: '评分',
      type: 'bar',
      data: userScoreData.map(item => item.score),
      itemStyle: {
        color: function(params) {
          // 为前三名设置不同颜色
          const colorList = ['#f5222d', '#fa8c16', '#faad14'];
          if (params.dataIndex < 3) {
            return colorList[params.dataIndex];
          }
          return '#1890ff';
        }
      }
    }]
  });
};

// 监听数据变化
watch(() => dataSource.value, () => {
  updateCharts();
}, { deep: true });

// 监听窗口大小变化的处理函数
const handleResize = () => {
  // 全局图表
  if (exchangeTypeChart) exchangeTypeChart.resize();
  if (exchangeCountryChart) exchangeCountryChart.resize();
  if (exchangeTimeChart) exchangeTimeChart.resize();
  if (userScoreChart) userScoreChart.resize();
  
  // 个人视图图表
  if (personalTypeChart) personalTypeChart.resize();
  if (personalCountryChart) personalCountryChart.resize();
  if (personalTimeChart) personalTimeChart.resize();
  if (personalScoreChart) personalScoreChart.resize();
};

// 添加窗口大小变化监听
window.addEventListener('resize', handleResize);

// 初始化数据
onMounted(async () => {
  // 初始化全局图表
  initCharts();
  
  // 获取用户列表
  await fetchUserList();
  
  // 检查是否直接进入个人视图模式
  const urlParams = new URLSearchParams(window.location.search);
  const viewMode = urlParams.get('view');
  if (viewMode === 'personal') {
    showPersonalExchanges.value = true;
    await fetchPersonalStats();
  }
  
  // 获取数据
  fetchData();
});

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    
    // 构建查询参数
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize
    };
    
    // 添加搜索条件
    if (searchForm.usernameList && searchForm.usernameList.length > 0) {
      params.usernameList = searchForm.usernameList.join(',');
    }
    
    if (searchForm.type) {
      params.type = searchForm.type;
    }
    
    if (searchForm.country) {
      params.country = searchForm.country;
    }
    
    if (searchForm.institution) {
      params.institution = searchForm.institution;
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0].format('YYYY-MM-DD');
      params.endDate = searchForm.dateRange[1].format('YYYY-MM-DD');
    }

    // 在个人视图模式下，添加userOnly标识，仅查询个人数据
    if (showPersonalExchanges.value) {
      // 获取当前用户ID
      const userId = await getUserId();
      if (!userId) {
        message.error('获取用户信息失败，请重新登录');
        return;
      }
      console.log('个人视图模式，查询用户ID:', userId);
      params.userOnly = true;
      params.userId = userId;
    }
    
    console.log('开始获取国际交流数据，发送查询参数:', params);
    
    // 调用API获取数据
    try {
      const response = await getInternationalExchanges(params);
      console.log('获取国际交流数据响应:', response);
      
      if (response && response.code === 200) {
        // 处理后端返回的数据，映射字段
        console.log('处理响应数据, 列表长度:', response.data?.list?.length || 0);
        
        if (!response.data || !response.data.list) {
          console.error('响应数据缺少list字段:', response.data);
          message.error('响应数据格式错误');
          return;
        }
        
        dataSource.value = response.data.list.map(item => {
          // 计算交流天数
          let exchangeDays = 0;
          try {
            exchangeDays = Math.ceil((new Date(item.endDate) - new Date(item.startDate)) / (1000 * 60 * 60 * 24));
            if (isNaN(exchangeDays) || exchangeDays < 0) {
              console.warn('计算交流天数出错，使用默认值0:', item.startDate, item.endDate);
              exchangeDays = 0;
            }
          } catch (err) {
            console.error('计算交流天数异常:', err);
          }
          
          // 打印各字段类型
          console.log('处理记录:', {
            id: item.id,
            name: item.name,
            usernameList: item.usernameList,
            userIdList: item.userIdList,
            type: item.type,
            startDate: item.startDate,
            endDate: item.endDate
          });
          
          return {
            id: item.id,
            name: item.name,
            usernameList: item.usernameList,
            userIdList: item.userIdList,
            type: item.type,
            country: item.country,
            institution: item.institution,
            startDate: item.startDate,
            endDate: item.endDate,
            exchangeDate: [item.startDate, item.endDate],
            exchangeDays,
            content: item.content,
            result: item.result,
            score: item.score,
            remark: item.remark || ''
          };
        });
        pagination.total = response.data.pagination.total;
        console.log('数据处理完成，记录数:', dataSource.value.length);
        
        // 确保在非个人视图模式下更新全局图表
        if (!showPersonalExchanges.value) {
          // 使用nextTick确保DOM更新后再更新图表
          nextTick(() => {
            console.log('更新全局图表...');
            // 若图表未初始化，则先初始化
            if (!exchangeTypeChart || !exchangeCountryChart || !exchangeTimeChart || !userScoreChart) {
              console.log('图表未初始化，先初始化图表');
              initCharts();
            }
            // 更新图表数据
            updateCharts();
          });
        }
      } else {
        console.error('获取数据失败:', response?.message || '未知错误');
        message.error(response?.message || '获取数据失败');
      }
    } catch (error) {
      console.error('API调用过程中出错:', error);
      message.error('API调用出错');
    }
  } catch (error) {
    console.error('获取国际交流数据失败:', error);
    message.error('获取数据失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.usernameList = [];
  searchForm.type = undefined;
  searchForm.country = '';
  searchForm.institution = '';
  searchForm.dateRange = null;
  handleSearch();
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 显示新增模态框
const showAddModal = () => {
  modalTitle.value = '新增交流';
  Object.keys(formState).forEach(key => {
    if (key === 'type') {
      formState[key] = undefined;
    } else if (key === 'usernameList' || key === 'userIdList') {
      formState[key] = [];
    } else {
      formState[key] = '';
    }
  });
  modalVisible.value = true;
};

// 显示编辑模态框
const showEditModal = (record) => {
  modalTitle.value = '编辑交流';
  
  // 处理参与用户信息
  let usernames = record.usernameList;
  let userIds = record.userIdList || [];
  
  // 处理字符串形式的参与用户
  if (typeof record.usernameList === 'string') {
    usernames = record.usernameList.split(',');
  } else if (!Array.isArray(record.usernameList)) {
    usernames = [record.usernameList].filter(Boolean);
  }
  
  // 处理用户ID
  if (typeof record.userIdList === 'string') {
    userIds = record.userIdList.split(',');
  } else if (Array.isArray(record.userIdList)) {
    userIds = [...record.userIdList];
  } else if (record.userIdList) {
    userIds = [record.userIdList];
  } else {
    userIds = [];
  }
  
  // 如果没有用户ID，则使用默认ID
  if (userIds.length === 0 && usernames.length > 0) {
    userIds = usernames.map((name, index) => {
      const found = userOptions.value.find(user => user.username === name || user.nickname === name);
      return found ? found.id : `temp-${index}`;
    });
  }
  
  // 更新表单状态
  formState.id = record.id;
  formState.name = record.name;
  formState.type = record.type;
  formState.country = record.country;
  formState.institution = record.institution;
  formState.content = record.content;
  formState.result = record.result;
  formState.score = record.score;
  formState.remark = record.remark;
  formState.usernameList = usernames;
  formState.userIdList = userIds;
  
  // 处理日期范围
  if (record.startDate && record.endDate) {
    formState.dateRange = [dayjs(record.startDate), dayjs(record.endDate)];
  }
  
  modalVisible.value = true;
};

// 处理用户选择变更，自动关联ID
const handleUserSelectionChange = (values) => {
  // 新的用户ID数组
  let userIds = [...formState.userIdList];
  
  // 检查当前选中的用户名是否有新增的
  for (const username of values) {
    // 查找该用户是否存在于已有选项中
    const foundUser = userOptions.value.find(user => user.username === username || user.nickname === username);
    
    if (foundUser) {
      // 如果找到对应用户，确保其ID在userIds中
      if (!userIds.includes(foundUser.id)) {
        userIds.push(foundUser.id);
      }
    } else {
      // 如果是新增的用户，生成临时ID
      const tempId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;
      userIds.push(tempId);
    }
  }
  
  // 更新教师IDs
  formState.userIdList = userIds;
};

// 处理模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate();
    
    confirmLoading.value = true;
    
    // 处理用户ID和名称的关联
    handleUserSelectionChange(formState.usernameList);
    
    // 构建请求数据
    const formData = {
      usernameList: formState.usernameList,
      userIdList: formState.userIdList,
      name: formState.name,
      type: formState.type,
      country: formState.country,
      institution: formState.institution,
      startDate: formState.dateRange ? formState.dateRange[0].format('YYYY-MM-DD') : null,
      endDate: formState.dateRange ? formState.dateRange[1].format('YYYY-MM-DD') : null,
      content: formState.content,
      result: formState.result,
      score: formState.score,
      remark: formState.remark
    };
    
    // 如果没有提供名称，则自动生成
    if (!formData.name) {
      formData.name = `${formData.type} - ${formData.country} - ${formData.institution}`;
    }
    
    // 根据是否有ID来判断是新增还是编辑
    let response;
    if (formState.id) {
      response = await updateInternationalExchange(formState.id, formData);
    } else {
      response = await addInternationalExchange(formData);
    }
    
    if (response.code === 200) {
      message.success(formState.id ? '编辑成功' : '添加成功');
      modalVisible.value = false;
      fetchData(); // 刷新数据
    } else {
      message.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('表单提交错误', error);
    message.error('表单验证失败，请检查填写内容');
  } finally {
    confirmLoading.value = false;
  }
};

// 表单数据处理函数 - 编辑现有记录
const handleEdit = (record) => {
  modalTitle.value = '编辑交流';
  // 重置表单
  Object.keys(formState).forEach(key => {
    formState[key] = null;
  });
  
  // 填充数据
  formState.id = record.id;
  formState.name = record.name;
  formState.type = record.type;
  formState.country = record.country;
  formState.institution = record.institution;
  
  // 处理日期范围
  if (record.startDate && record.endDate) {
    formState.dateRange = [dayjs(record.startDate), dayjs(record.endDate)];
  }
  
  formState.content = record.content;
  formState.result = record.result;
  formState.score = record.score;
  formState.remark = record.remark;
  
  // 处理用户列表
  if (record.userIdList && Array.isArray(record.userIdList)) {
    formState.userIdList = record.userIdList;
    
    // 尝试获取用户名称
    const usernames = record.usernameList || [];
    formState.usernameList = usernames;
  }
  
  modalVisible.value = true;
};

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 确认删除
const confirmDeleteRecord = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDelete(record)
  })
}

// 删除记录
const handleDelete = (record) => {
  deleteInternationalExchange(record.id)
    .then(response => {
      if (response.code === 200) {
        message.success(response.message || '删除成功');
        fetchData(); // 重新加载数据
      } else {
        message.error(response.message || '删除失败');
      }
    })
    .catch(error => {
      console.error('删除国际交流数据失败:', error);
      message.error('删除失败');
    });
};

// 导出数据
const handleExport = () => {
  exportInternationalExchanges({
    usernameList: searchForm.usernameList,
    type: searchForm.type,
    country: searchForm.country,
    institution: searchForm.institution
  })
    .then(response => {
      // 处理文件下载，使用Blob方式处理
      const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `国际交流数据_${new Date().getTime()}.xlsx`;
      link.click();
      URL.revokeObjectURL(link.href);
      message.success('导出成功');
    })
    .catch(error => {
      console.error('导出国际交流数据失败:', error);
      message.error('导出失败');
    });
};

// 切换个人/全局视图
const togglePersonalExchanges = () => {
  showPersonalExchanges.value = !showPersonalExchanges.value;
  
  // 如果切换到个人视图，获取个人数据
  if (showPersonalExchanges.value) {
    fetchPersonalStats();
  } else {
    // 如果切换到全局视图，确保重新加载全局数据
    nextTick(() => {
      if (!exchangeTypeChart || !exchangeCountryChart || !exchangeTimeChart) {
        initCharts();
      }
      updateCharts();
    });
  }
};

const fetchPersonalStats = async () => {
  personalStatsLoading.value = true;
  try {
    // 获取当前用户ID
    const userId = await getUserId();
    if (!userId) {
      message.error('获取用户信息失败，请重新登录');
      return;
    }
    
    console.log('开始获取个人统计数据，用户ID:', userId);
    
    // 调用API获取个人统计数据，传递userId参数
    try {
      const response = await getPersonalExchangeStats(userId);
      
      console.log('获取个人统计数据响应:', response);
      
      if (response && response.code === 200) {
        console.log('获取个人统计数据成功，数据:', response.data);
        
        // 处理最近交流数据，确保字段与表格一致
        if (response.data && response.data.recentExchanges) {
          console.log('处理最近交流数据，条数:', response.data.recentExchanges.length);
          
          response.data.recentExchanges = response.data.recentExchanges.map(exchange => {
            // 计算交流天数
            let exchangeDays = 0;
            try {
              const startDate = new Date(exchange.startDate);
              const endDate = new Date(exchange.endDate);
              exchangeDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
              if (isNaN(exchangeDays) || exchangeDays < 0) {
                console.warn('计算个人交流天数出错，使用默认值0:', exchange.startDate, exchange.endDate);
                exchangeDays = 0;
              }
            } catch (err) {
              console.error('计算个人交流天数异常:', err);
            }
            
            console.log('处理个人交流记录:', {
              id: exchange.id,
              name: exchange.name,
              usernameList: exchange.teacherName || exchange.usernameList
            });
            
            return {
              id: exchange.id,
              name: exchange.name,
              usernameList: exchange.teacherName || exchange.usernameList,  // 处理可能的字段差异
              type: exchange.type,
              country: exchange.country,
              institution: exchange.institution || '',
              startDate: exchange.startDate,
              endDate: exchange.endDate,
              exchangeDate: [exchange.startDate, exchange.endDate],
              exchangeDays,
              content: exchange.content,
              result: exchange.result,
              score: exchange.score,
              remark: exchange.remark || ''
            };
          });
        }
        
        // 更新个人统计数据
        Object.assign(personalStats, response.data);
        
        // 使用nextTick确保DOM更新后再初始化图表
        nextTick(() => {
          console.log('更新个人视图图表...');
          // 初始化个人视图图表
          initPersonalCharts();
          // 然后更新图表数据
          updatePersonalCharts();
        });
      } else {
        console.error('获取个人统计数据失败:', response?.message || '未知错误');
        message.error(response?.message || '获取个人统计数据失败');
      }
    } catch (error) {
      console.error('API调用过程中出错:', error);
      message.error('个人统计API调用出错');
      setDefaultPersonalStats();
    }
  } catch (error) {
    console.error('获取个人统计出错:', error);
    message.error('获取个人统计失败: ' + (error.message || '未知错误'));
    setDefaultPersonalStats();
  } finally {
    personalStatsLoading.value = false;
  }
};

const initPersonalCharts = () => {
  // 先销毁可能已存在的图表实例
  if (personalTypeChart) {
    personalTypeChart.dispose();
    personalTypeChart = null;
  }
  if (personalCountryChart) {
    personalCountryChart.dispose();
    personalCountryChart = null;
  }
  if (personalTimeChart) {
    personalTimeChart.dispose();
    personalTimeChart = null;
  }
  if (personalScoreChart) {
    personalScoreChart.dispose();
    personalScoreChart = null;
  }

  // 确保DOM元素已经渲染
  if (!personalTypeChartRef.value || !personalCountryChartRef.value || 
      !personalTimeChartRef.value || !personalScoreChartRef.value) {
    console.log('个人图表DOM元素未准备好，延迟初始化');
    setTimeout(initPersonalCharts, 100);
    return;
  }

  // 个人交流类型分布图
  personalTypeChart = echarts.init(personalTypeChartRef.value);
  const personalTypeOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: []
    },
    series: [
      {
        name: '交流类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: []
      }
    ]
  };
  personalTypeChart.setOption(personalTypeOption);

  // 个人交流国家/地区分布图
  personalCountryChart = echarts.init(personalCountryChartRef.value);
  const personalCountryOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: []
    },
    series: [
      {
        name: '交流国家/地区',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: []
      }
    ]
  };
  personalCountryChart.setOption(personalCountryOption);

  // 个人交流时间分布图
  personalTimeChart = echarts.init(personalTimeChartRef.value);
  const personalTimeOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '交流数量',
        type: 'bar',
        data: []
      }
    ]
  };
  personalTimeChart.setOption(personalTimeOption);

  // 个人评分趋势图
  personalScoreChart = echarts.init(personalScoreChartRef.value);
  const personalScoreOption = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '评分',
        type: 'line',
        data: []
      }
    ]
  };
  personalScoreChart.setOption(personalScoreOption);
};

// 更新个人视图图表数据
const updatePersonalCharts = () => {
  // 确保DOM元素已经存在
  if (!personalTypeChartRef.value || !personalCountryChartRef.value || 
      !personalTimeChartRef.value || !personalScoreChartRef.value) {
    console.warn('个人视图图表DOM元素未准备好，延迟更新图表');
    return;
  }

  // 先销毁原有图表实例
  if (personalTypeChart) {
    personalTypeChart.dispose();
  }
  if (personalCountryChart) {
    personalCountryChart.dispose();
  }
  if (personalTimeChart) {
    personalTimeChart.dispose();
  }
  if (personalScoreChart) {
    personalScoreChart.dispose();
  }
  
  // 重新初始化图表
  personalTypeChart = echarts.init(personalTypeChartRef.value);
  personalCountryChart = echarts.init(personalCountryChartRef.value);
  personalTimeChart = echarts.init(personalTimeChartRef.value);
  personalScoreChart = echarts.init(personalScoreChartRef.value);

  // 更新个人交流类型分布
  if (personalStats.typeDistribution) {
    const typeData = Object.entries(personalStats.typeDistribution)
      .map(([name, value]) => ({ name, value }))
      .filter(item => item.name && item.value);
      
    if (typeData.length > 0) {
      personalTypeChart.setOption({
        title: {
          text: '我的交流类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: typeData.map(item => item.name)
        },
        series: [{
          name: '交流类型',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: typeData
        }]
      });
    }
  }

  // 更新个人交流国家/地区分布
  if (personalStats.countryDistribution) {
    const countryData = Object.entries(personalStats.countryDistribution)
      .map(([name, value]) => ({ name, value }))
      .filter(item => item.name && item.value)
      .sort((a, b) => b.value - a.value)
      .slice(0, 10);
      
    if (countryData.length > 0) {
      personalCountryChart.setOption({
        title: {
          text: '我的交流国家/地区分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: countryData.map(item => item.name)
        },
        series: [{
          name: '交流国家/地区',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: countryData
        }]
      });
    }
  }

  // 更新个人交流时间分布
  if (personalStats.timeDistribution) {
    const years = Object.keys(personalStats.timeDistribution).sort();
    const timeData = years.map(year => ({
      name: year,
      value: personalStats.timeDistribution[year] || 0
    }));
    
    personalTimeChart.setOption({
      title: {
        text: '我的交流时间分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: timeData.map(item => item.name)
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: '交流数量',
        type: 'bar',
        data: timeData.map(item => item.value),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    });
  }

  // 更新个人评分趋势
  if (personalStats.scoreTrend) {
    const years = Object.keys(personalStats.scoreTrend).sort();
    const scoreData = years.map(year => ({
      year,
      score: Number(personalStats.scoreTrend[year]) || 0
    }));
    
    personalScoreChart.setOption({
      title: {
        text: '我的评分趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: scoreData.map(item => item.year)
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: '评分',
        type: 'line',
        data: scoreData.map(item => item.score),
        smooth: true,
        symbolSize: 6,
        itemStyle: {
          color: '#2378f7'
        },
        lineStyle: {
          width: 3
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(35, 120, 247, 0.8)' },
            { offset: 1, color: 'rgba(35, 120, 247, 0.1)' }
          ])
        }
      }]
    });
  }
};

// 设置个人统计默认值
const setDefaultPersonalStats = () => {
  console.log('设置个人统计默认值');
  Object.assign(personalStats, {
    exchangeCount: 0,
    totalScore: 0,
    totalDays: 0,
    typeDistribution: {},
    countryDistribution: {},
    timeDistribution: {},
    scoreTrend: {},
    recentExchanges: []
  });

  // 确保图表能够正确显示默认空状态
  nextTick(() => {
    console.log('初始化默认空状态的个人图表');
    initPersonalCharts();
    updatePersonalCharts();
  });
};

// 清理工作
const cleanupCharts = () => {
  // 销毁全局图表实例
  if (exchangeTypeChart) {
    exchangeTypeChart.dispose();
    exchangeTypeChart = null;
  }
  if (exchangeCountryChart) {
    exchangeCountryChart.dispose();
    exchangeCountryChart = null;
  }
  if (exchangeTimeChart) {
    exchangeTimeChart.dispose();
    exchangeTimeChart = null;
  }
  if (userScoreChart) {
    userScoreChart.dispose();
    userScoreChart = null;
  }
  
  // 销毁个人视图图表实例
  if (personalTypeChart) {
    personalTypeChart.dispose();
    personalTypeChart = null;
  }
  if (personalCountryChart) {
    personalCountryChart.dispose();
    personalCountryChart = null;
  }
  if (personalTimeChart) {
    personalTimeChart.dispose();
    personalTimeChart = null;
  }
  if (personalScoreChart) {
    personalScoreChart.dispose();
    personalScoreChart = null;
  }
};

// 在组件卸载时清理图表和事件监听
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
  
  // 清理图表实例
  cleanupCharts();
});

// 文件相关状态
const fileList = ref([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 文件上传前检查
const beforeFileUpload = (file) => {
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('文件大小不能超过5MB!');
    return false;
  }
  
  // 防止重复上传相同文件
  const isDuplicate = fileList.value.some(item => item.name === file.name);
  if (isDuplicate) {
    message.error('已存在同名文件!');
    return false;
  }
  
  return true;
};

// 自定义文件上传逻辑
const customFileUpload = ({ file, onSuccess, onError }) => {
  try {
    // 构建文件对象
    const fileObj = {
      uid: file.uid,
      name: file.name,
      status: 'done',
      url: URL.createObjectURL(file),
      originFileObj: file,
      response: { file: file },
    };
    
    // 添加到文件列表
    fileList.value = [...fileList.value, fileObj];
    
    // 调用成功回调
    onSuccess(fileObj);
  } catch (error) {
    console.error('文件上传出错:', error);
    onError(error);
  }
};

// 删除文件
const handleRemove = (file) => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
  
  // 释放blob url
  if (file.url && file.url.startsWith('blob:')) {
    URL.revokeObjectURL(file.url);
  }
  
  return true;
};

// 预览文件
const handleFilePreview = async (file) => {
  if (file && (file.url || file.preview)) {
    previewImage.value = file.url || file.preview;
    previewVisible.value = true;
    previewTitle.value = file.name || file.fileName || '文件预览';
  } else {
    message.warning('无法预览该文件');
  }
};

// 下载文件
const handleFileDownload = (file) => {
  try {
    if (file && (file.url || file.preview)) {
      const link = document.createElement('a');
      link.href = file.url || file.preview;
      link.download = file.name || file.fileName || '下载文件';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else if (file && file.response && file.response.fileUrl) {
      const link = document.createElement('a');
      link.href = file.response.fileUrl;
      link.download = file.name || file.fileName || '下载文件';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.warning('无法下载该文件');
    }
  } catch (error) {
    console.error('文件下载出错:', error);
    message.error('文件下载失败');
  }
};

// 处理审核
const handleReview = async (record) => {
  try {
    // 创建一个ref存储审核信息
    const reviewInfo = ref({
      reviewStatus: 1, // 默认通过
      reviewComment: '', // 审核意见
      attachments: [] // 存储附件
    });
    
    // 显示加载中
    message.loading('正在加载国际交流详情...', 0.5);
    
    // // 获取项目详情，包括附件
    // try {
    //   // 获取项目详情
    //   const response = await getExchangeDetail(record.id);
    //   if (response && response.code === 200 && response.data) {
    //     // 设置附件
    //     reviewInfo.value.attachments = (response.data.attachments || []).map((file, index) => ({
    //       uid: `-${index}`,
    //       name: file.name || file.originalName || `附件${index + 1}`,
    //       status: 'done',
    //       url: file.url,
    //       response: { file: { id: file.id } },
    //       data: file
    //     }));
    //   }
    // } catch (error) {
    //   console.error('获取国际交流详情失败:', error);
    //   // 不中断流程，继续显示审核对话框
    // }
    
    // 确认审核操作 - 使用h函数创建内容而不是JSX
    Modal.confirm({
      title: '审核国际交流',
      icon: null,
      width: 600,
      content: h('div', {}, [
        h('p', { style: { marginBottom: '10px' } }, `您正在审核"${record.name || '国际交流'}"`),
        h('div', { style: { marginBottom: '10px' } }, [
          h('span', { style: { display: 'inline-block', width: '80px' } }, '审核结果：'),
          h('a-radio-group', {
            value: reviewInfo.value.reviewStatus,
            onChange: (e) => { reviewInfo.value.reviewStatus = e.target.value }
          }, [
            h('a-radio', { value: 1 }, '通过'),
            h('a-radio', { value: 0 }, '拒绝')
          ])
        ]),
        h('div', { style: { marginBottom: '15px' } }, [
          h('span', { style: { display: 'inline-block', width: '80px', verticalAlign: 'top' } }, '审核意见：'),
          h('a-textarea', {
            value: reviewInfo.value.reviewComment,
            onChange: (e) => { reviewInfo.value.reviewComment = e.target.value },
            rows: 3,
            placeholder: '请输入审核意见'
          })
        ]),
        reviewInfo.value.attachments && reviewInfo.value.attachments.length > 0 
          ? h('div', {}, [
              h('div', { style: { fontWeight: 'bold', marginBottom: '10px' } }, '相关附件:'),
              h('div', { style: { maxHeight: '200px', overflow: 'auto' } }, [
                h('table', { style: { width: '100%', borderCollapse: 'collapse' } }, [
                  h('thead', {}, [
                    h('tr', { style: { backgroundColor: '#f5f5f5' } }, [
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8' } }, '文件名'),
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8', width: '120px' } }, '大小'),
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8', width: '150px' } }, '操作')
                    ])
                  ]),
                  h('tbody', {}, reviewInfo.value.attachments.map(file => 
                    h('tr', { key: file.uid, style: { borderBottom: '1px solid #e8e8e8' } }, [
                      h('td', { style: { padding: '8px', textAlign: 'left' } }, file.name),
                      h('td', { style: { padding: '8px', textAlign: 'left' } }, formatFileSize(file.data?.size || 0)),
                      h('td', { style: { padding: '8px', textAlign: 'left' } }, 
                        h('div', { style: { display: 'flex', gap: '8px' } }, [
                          h('a', { 
                            style: { color: '#1890ff', cursor: 'pointer' }, 
                            onClick: () => handleFilePreview(file) 
                          }, '预览'),
                          h('a', { 
                            style: { color: '#1890ff', cursor: 'pointer' }, 
                            onClick: () => handleFileDownload(file) 
                          }, '下载')
                        ])
                      )
                    ])
                  ))
                ])
              ])
            ])
          : null
      ]),
      okText: '确认',
      cancelText: '取消',
      async onOk() {
        try {
          // 获取当前用户ID作为审核人
          await getUserId(true);
          
          if (!userId.value) {
            message.error('无法获取用户ID，请重新登录');
            return;
          }
          
          // 调用审核API
          const submitData = {
            id: record.id,
            reviewer: userId.value,
            reviewStatus: reviewInfo.value.reviewStatus,
            reviewComment: reviewInfo.value.reviewComment
          };
          
          // const response = await reviewExchange(record.id, submitData);
          const response = {
            code: 200,
            message: '审核成功'
          };
          if (response && response.code === 200) {
            message.success('审核成功');
            fetchData(); // 刷新数据
          } else {
            message.error(response?.message || '审核失败');
          }
        } catch (error) {
          console.error('审核国际交流失败:', error);
          message.error('审核国际交流失败: ' + (error.message || '未知错误'));
        }
      }
    });
  } catch (error) {
    console.error('打开审核对话框失败:', error);
    message.error('操作失败: ' + (error.message || '未知错误'));
  }
};

// 文件大小格式化
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';
.international-exchange-container {
  padding: 24px;
}
</style> 