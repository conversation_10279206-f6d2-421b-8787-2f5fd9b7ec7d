/**
 * 权限配置索引文件
 * 统一导出所有模块的权限配置
 */
module.exports = {
  // 科研项目模块权限配置
  researchProjects: require('./researchProjects'),
  patents: require('./patents'),
  highLevelPapers: require('./highLevelPapers'),
  textbooks: require('./textbooks'),
  teachingReformProjects: require('./teachingReformProjects'),
  teachingResearchAwards: require('./teachingResearchAwards'),
  teachingResearchAwardLevels: require('./teachingResearchAwardLevels'),
  teachingWorkloads: require('./teachingWorkloads'),
  home: require('./home'),
  studentProjectGuidanceProjects: require('./studentProjectGuidanceProjects'),
  studentAwardGuidanceAwards: require('./studentAwardGuidanceAwards'),
  conferences: require('./conferences'),
  academicAppointments: require('./academicAppointments'),

  // 用户排名和绩效分析模块权限配置
  userRanking: require('./userRanking'),

  // 消息通知模块权限配置
  notifications: require('./notifications'),

  // 通知设置模块权限配置
  notificationSettings: require('./notificationSettings'),

  // 通知模板模块权限配置
  notificationTemplates: require('./notificationTemplates'),

  // 用户级别模块权限配置
  userLevels: require('./userLevels'),

  // 用户职称记录模块权限配置
  userLevelRecords: require('./userLevelRecords'),

  // 部门管理模块权限配置
  departments: require('./departments')
};