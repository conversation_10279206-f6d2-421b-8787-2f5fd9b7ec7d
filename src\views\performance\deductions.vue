<template>
  <div class="deductions-container">
    <a-card title="H-扣分管理" :bordered="false">
      <template #extra>
        <div>
          <a-upload
            :customRequest="handleImport"
            :show-upload-list="false"
            :before-upload="beforeUpload"
            style="display: inline-block; margin-right: 8px;"
          >
            <a-button type="primary" style="margin-right: 8px;" v-permission="'score:H:admin:update'">
              <template #icon><upload-outlined /></template>
              导入数据
            </a-button>
          </a-upload>
          <a-button type="primary" @click="handleExport" style="margin-right: 8px;" v-permission="'score:H:admin:list'">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button type="primary" @click="showAddModal" style="margin-right: 8px;" v-permission="viewMode === 'personal' ? 'score:H:self:create' : 'score:H:admin:create'">
            <template #icon><plus-outlined /></template>
            添加扣分记录
          </a-button>
          <a-button :type="viewMode === 'personal' ? 'default' : 'primary'" @click="toggleViewMode" class="toggle-button">
            <template #icon><user-outlined /></template>
            {{ viewMode === 'personal' ? '查看全部扣分' : '查看我的扣分' }}
          </a-button>
        </div>
      </template>
      
      <a-alert
        message="扣分管理"
        description="负责管理研究生导师培养力扣分记录，根据《暨南大学基础医学院、公共卫生学院研究生导师培养力量化考核办法》执行。"
        type="info"
        show-icon
        style="margin-bottom: 16px; background-color: #f0f9f4; border-color: #ccebd7; color: #52937b;"
      />
      
      <!-- 添加扣分规则说明 -->
      <a-collapse expandIconPosition="right" :bordered="false" style="margin-bottom: 16px">
        <a-collapse-panel key="1" header="研究生导师培养力扣分规则详情" :headerStyle="{ color: '#1890ff', fontWeight: '500' }">
          <p><strong>第十三条</strong> 在最高培养年限规定（硕士5年、博士7年）内未获得学位或未就业的内招研究生情况，扣减分数如下：</p>
          <a-table :columns="[
            { title: '未按时获得学位的研究生数', dataIndex: 'count', key: 'count' },
            { title: '扣减分数', dataIndex: 'score', key: 'score' }
          ]" :data-source="[
            { key: '1', count: '1', score: '5' },
            { key: '2', count: '2', score: '10' }
          ]" :pagination="false" size="small" style="margin-bottom: 10px" />
          
          <a-table :columns="[
            { title: '研究生就业情况', dataIndex: 'situation', key: 'situation' },
            { title: '扣减分数', dataIndex: 'score', key: 'score' }
          ]" :data-source="[
            { key: '1', situation: '研究生在毕业当年8月31日前未就业', score: '10' },
            { key: '2', situation: '研究生在毕业当年12月31日前未就业', score: '20' }
          ]" :pagination="false" size="small" style="margin-bottom: 10px" />
          
          <p>导师连续两年指导的研究生未按期就业，则停招1年。</p>
          
          <p><strong>第十四条</strong> 导师具有以下情况之一者，则不具备研究招生指标分配资格：</p>
          <ol>
            <li>师德师风考核不合格的；</li>
            <li>师生关系紧张或具有不良记录的；</li>
            <li>拒绝承担研究生招生、培养及学位点建设相关工作任务的；</li>
            <li>导师本人或所指导研究生违反学术道德的；</li>
            <li>所指导研究生学位论文抽检有问题或不合格的；</li>
            <li>基础医学博士生导师当年未主持在研国家级项目；基础医学或预防医学硕士生导师当年未主持在研省级及以上课题的（横向课题单项到账经费大于等于100万视为省级课题）；</li>
            <li>博士生导师科研经费不足20万；硕士生导师科研经费不足10万；</li>
            <li>在最高培养年限规定内未获得学位的内招学生数≥3人的；</li>
            <li>填报导师培养力材料弄虚作假的；</li>
            <li>其他的学校规定不具备研究生招生资格的情况。</li>
          </ol>
        </a-collapse-panel>
      </a-collapse>
      
      <!-- 个人扣分统计卡片 -->
      <div v-if="viewMode === 'personal'">
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :span="24">
            <a-card class="personal-info-card" :bordered="false">
              <a-row :gutter="16">
                <a-col :xs="24" :sm="8">
                  <a-statistic title="总扣分次数" :value="personalStats.totalCount || 0" :valueStyle="{ color: '#FF7F50', fontSize: '24px' }">
                    <template #prefix>
                      <warning-outlined />
                    </template>
                    <template #suffix>
                      <span>次</span>
                    </template>
                  </a-statistic>
                </a-col>
                <a-col :xs="24" :sm="8">
                  <a-statistic title="总扣分值" :value="personalStats.totalScore || 0" :precision="2" :valueStyle="{ color: '#cf1322', fontSize: '24px' }">
                    <template #prefix>
                      <fall-outlined />
                    </template>
                    <template #suffix>
                      <span>分</span>
                    </template>
                  </a-statistic>
                </a-col>
                <a-col :xs="24" :sm="8">
                  <a-statistic 
                    title="平均每次扣分" 
                    :value="(personalStats.totalCount && personalStats.totalScore) ? (personalStats.totalScore / personalStats.totalCount).toFixed(2) : 0" 
                    :precision="2" 
                    :valueStyle="{ color: '#3f8600', fontSize: '24px' }"
                  >
                    <template #prefix>
                      <calculator-outlined />
                    </template>
                    <template #suffix>
                      <span>分</span>
                    </template>
                  </a-statistic>
                </a-col>
              </a-row>
            </a-card>
          </a-col>
        </a-row>
      </div>
      
      <!-- 统计图表 -->
      <a-row :gutter="16" style="margin-bottom: 24px">
        <a-col :xs="24" :md="12">
          <a-card title="扣分类型分布" class="chart-card" :headStyle="{ fontSize: '15px' }" :bordered="false">
            <div ref="deductionTypeChartRef" style="height: 280px"></div>
          </a-card>
        </a-col>
        <a-col :xs="24" :md="12">
          <a-card title="扣分时间分布" class="chart-card" :headStyle="{ fontSize: '15px' }" :bordered="false">
            <div ref="deductionTimeChartRef" style="height: 280px"></div>
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="16" style="margin-bottom: 24px">
        <a-col :xs="24" :md="12">
          <a-card title="教师扣分排名" class="chart-card" :headStyle="{ fontSize: '15px' }" :bordered="false">
            <div ref="teacherDeductionChartRef" style="height: 280px"></div>
          </a-card>
        </a-col>
        <a-col :xs="24" :md="12">
          <a-card title="扣分原因分析" class="chart-card" :headStyle="{ fontSize: '15px' }" :bordered="false">
            <div class="chart-hint">鼠标悬停查看完整扣分原因</div>
            <div ref="deductionReasonChartRef" style="height: 280px"></div>
          </a-card>
        </a-col>
      </a-row>
      
      <!-- 搜索表单 -->
      <a-form layout="inline" :model="searchForm" @finish="handleSearch" style="margin-bottom: 16px;">
        <a-form-item name="userId" label="用户ID">
          <a-input v-model:value="searchForm.userId" placeholder="请输入用户ID" style="width: 200px;" />
        </a-form-item>
        <a-form-item name="deductionType" label="扣分类型">
          <a-select v-model:value="searchForm.deductionType" placeholder="请选择扣分类型" style="width: 200px;" allowClear>
            <a-select-option value="未获得学位">未获得学位</a-select-option>
            <a-select-option value="未就业">未就业</a-select-option>
            <a-select-option value="师德师风">师德师风</a-select-option>
            <a-select-option value="学术道德">学术道德</a-select-option>
            <a-select-option value="科研项目">科研项目</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="deductionDate" label="扣分时间">
          <a-range-picker
            v-model:value="searchForm.deductionDate"
            style="width: 230px"
            placeholder="请选择扣分时间范围"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">
            <template #icon><search-outlined /></template>
            搜索
          </a-button>
          <a-button style="margin-left: 8px" @click="resetSearch">
            <template #icon><reload-outlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
      
      <!-- 数据表格 -->
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
          :scroll="{ x: 1200 }"
          :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped-row' : '')"
        >
          <!-- 自定义单元格内容 -->
          <template #bodyCell="{ column, record }">
            <!-- 扣分原因显示 -->
            <template v-if="column.key === 'deductionReason' && record && record.deductionReason">
              <a-tooltip :title="record.deductionReason">
                <span>{{ record.deductionReason.length > 25 ? record.deductionReason.substring(0, 25) + '...' : record.deductionReason }}</span>
              </a-tooltip>
            </template>
            
            <!-- 处理结果显示 -->
            <template v-if="column.key === 'handleResult' && record && record.handleResult">
              <a-tooltip :title="record.handleResult">
                <span>{{ record.handleResult.length > 15 ? record.handleResult.substring(0, 15) + '...' : record.handleResult }}</span>
              </a-tooltip>
            </template>
            
            <!-- 扣分分值显示 -->
            <template v-if="column.key === 'deductionScore' && record">
              <a-tag :color="getScoreColor(record.deductionScore)">{{ record.deductionScore }}</a-tag>
            </template>
            
            <!-- 扣分类型显示 -->
            <template v-if="column.key === 'deductionType' && record">
              <a-tag :color="getTypeColor(record.deductionType)">{{ record.deductionType }}</a-tag>
            </template>
            
            <!-- 操作按钮 -->
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="showEditModal(record)" v-permission="viewMode === 'personal' ? 'score:H:self:update' : 'score:H:admin:update'">
                  <EditOutlined /> 编辑
                </a>
                <a-divider type="vertical" />
                <a @click="confirmDelete(record)" v-permission="viewMode === 'personal' ? 'score:H:self:delete' : 'score:H:admin:delete'">
                  <DeleteOutlined /> 删除
                </a>
              </a-space>
            </template>
          </template>
          
          <!-- 空数据状态 -->
          <template #emptyText>
            <a-empty 
              :description="viewMode === 'personal' ? '暂无个人扣分数据' : '暂无扣分数据'" 
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
            />
          </template>
        </a-table>
      </a-card>
      
      <!-- 新增/编辑模态框 -->
      <a-modal
        :title="modalTitle"
        v-model:visible="modalVisible"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="confirmLoading"
        width="650px"
      >
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="用户名" name="username">
            <a-input v-model:value="formState.username" placeholder="请输入用户名" />
          </a-form-item>
          <a-form-item label="扣分类型" name="deductionType">
            <a-select v-model:value="formState.deductionType" placeholder="请选择扣分类型">
              <a-select-option value="未获得学位">未获得学位</a-select-option>
              <a-select-option value="未就业">未就业</a-select-option>
              <a-select-option value="师德师风">师德师风</a-select-option>
              <a-select-option value="学术道德">学术道德</a-select-option>
              <a-select-option value="科研项目">科研项目</a-select-option>
              <a-select-option value="其他">其他</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="扣分时间" name="deductionDate">
            <a-date-picker
              v-model:value="formState.deductionDate"
              style="width: 100%"
              placeholder="请选择扣分时间"
            />
          </a-form-item>
          <a-form-item label="扣分分值" name="deductionScore">
            <a-input-number
              v-model:value="formState.deductionScore"
              :min="0"
              :max="100"
              style="width: 100%"
              placeholder="请输入扣分分值"
            />
          </a-form-item>
          <a-form-item label="扣分原因" name="deductionReason">
            <a-textarea
              v-model:value="formState.deductionReason"
              :rows="4"
              placeholder="请输入扣分原因"
            />
          </a-form-item>
          <a-form-item label="处理结果" name="handleResult">
            <a-textarea
              v-model:value="formState.handleResult"
              :rows="3"
              placeholder="请输入处理结果"
            />
          </a-form-item>
          <a-form-item label="关联用户" name="userId">
            <a-select
              v-model:value="formState.userId"
              placeholder="请选择关联用户"
              style="width: 100%"
              @change="handleUserChange"
              show-search
              :options="usersOptions"
              :filter-option="(input, option) => 
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            >
            </a-select>
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="formState.remark"
              :rows="2"
              placeholder="请输入备注"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script setup>
console.log('deductions.vue 文件开始加载...');

// 导入所有需要的模块
import { ref, reactive, onMounted, watch, computed } from 'vue';
import dayjs from 'dayjs';
console.log('Vue 相关模块导入成功');

import { 
  PlusOutlined, 
  ExportOutlined, 
  EditOutlined, 
  DeleteOutlined,
  UserOutlined,
  TeamOutlined,
  WarningOutlined,
  FallOutlined,
  CalculatorOutlined,
  SearchOutlined,
  ReloadOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';
console.log('Ant Design 图标导入成功');

import { message, Modal } from 'ant-design-vue';
console.log('message 组件导入成功');

import { Empty } from 'ant-design-vue';
console.log('Empty 组件导入成功');

import * as echarts from 'echarts';
console.log('echarts 导入成功');

import { getPersonalDeductionStats, getPersonalDeductions, getDeductions, addDeduction, updateDeduction, deleteDeduction, exportDeductions, importDeductions } from '@/api/modules/api.deductions';
import { usersList } from '@/api/modules/api.users';
import request from '@/utils/request';
import axios from 'axios';
console.log('API 模块导入成功');

import { useAuthStore } from '@/stores/auth.js';
console.log('Auth Store 模块导入成功');

import { useUserId } from '@/composables/useUserId';
console.log('useUserId 模块导入成功');

console.log('所有模块导入成功，开始执行组件代码');

// 使用userId钩子
const { userId, getUserId } = useUserId();

// 搜索表单 - 移到try-catch外面确保一定被定义
const searchForm = reactive({
  userId: '',
  deductionType: undefined,
  deductionDate: null,
});

// 表单状态 - 移到try-catch外面确保一定被定义
const formState = reactive({
  id: '',
  username: '',
  deductionType: undefined,
  deductionDate: null,
  deductionScore: undefined,
  deductionReason: '',
  handleResult: '',
  remark: '',
  userId: '',
});

// 表格数据 - 移到try-catch外面确保一定被定义
const dataSource = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 视图模式状态
const viewMode = ref('admin');  // 默认为管理员视图
const authStore = useAuthStore();

// 个人扣分统计数据
const personalStats = ref({
  totalCount: 0,
  totalScore: 0,
  typeDistribution: [],
  timeDistribution: [],
  recentDeductions: []
});

// 用户列表数据
const usersOptions = ref([]);

// 表格列定义
const columns = [
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 100,
  },
  {
    title: '扣分类型',
    dataIndex: 'deductionType',
    key: 'deductionType',
    width: 100,
  },
  {
    title: '扣分时间',
    dataIndex: 'deductionDate',
    key: 'deductionDate',
    width: 110,
  },
  {
    title: '扣分分值',
    dataIndex: 'deductionScore',
    key: 'deductionScore',
    width: 90,
    sorter: true,
  },
  {
    title: '扣分原因',
    dataIndex: 'deductionReason',
    key: 'deductionReason',
    ellipsis: true,
    width: 280,
  },
  {
    title: '处理结果',
    dataIndex: 'handleResult',
    key: 'handleResult',
    ellipsis: true,
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
    align: 'center'
  },
];

// 表单验证规则
const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  deductionType: [{ required: true, message: '请选择扣分类型', trigger: 'change' }],
  deductionDate: [{ required: true, message: '请选择扣分时间', trigger: 'change' }],
  deductionScore: [{ required: true, message: '请输入扣分分值', trigger: 'blur' }],
  deductionReason: [{ required: true, message: '请输入扣分原因', trigger: 'blur' }],
  handleResult: [{ required: true, message: '请输入处理结果', trigger: 'blur' }],
  userId: [{ required: true, message: '请选择关联用户', trigger: 'change' }],
};

// 模态框状态
const modalVisible = ref(false);
const modalTitle = ref('新增扣分');
const confirmLoading = ref(false);
const formRef = ref(null);

// 图表引用
const deductionTypeChartRef = ref(null);
const deductionTimeChartRef = ref(null);
const teacherDeductionChartRef = ref(null);
const deductionReasonChartRef = ref(null);

// 图表实例
let deductionTypeChart = null;
let deductionTimeChart = null;
let teacherDeductionChart = null;
let deductionReasonChart = null;

// 存储排序后的扣分原因数据，用于tooltip访问
let sortedReasonsList = [];

// 根据扣分分值获取颜色
const getScoreColor = (score) => {
  if (score >= 100) return '#f5222d';  // 红色
  if (score >= 20) return '#fa8c16';   // 橙色
  if (score >= 10) return '#faad14';   // 黄色
  return '#52c41a';                    // 绿色
};

// 根据扣分类型获取颜色
const getTypeColor = (type) => {
  const typeColorMap = {
    '未获得学位': '#1890ff',
    '未就业': '#722ed1',
    '师德师风': '#f5222d',
    '学术道德': '#fa8c16',
    '科研项目': '#13c2c2',
    '其他': '#8c8c8c'
  };
  return typeColorMap[type] || '#8c8c8c';
};

// 是否有管理权限
const hasAdminPermission = computed(() => {
  // 从认证存储中获取用户角色信息
  const user = authStore.user;
  // 检查用户是否有管理员或督导角色权限
  if (!user) return false;
  
  // 检查用户的权限列表
  const perms = authStore.perms || [];
  // 管理员具有所有权限
  if (perms.includes('*')) return true;
  // 拥有绩效管理权限的用户可以查看全部扣分
  return perms.includes('performance') || perms.includes('performance:deductions');
});

// 是否为管理视图
const isAdminView = computed(() => {
  return viewMode.value === 'admin';
});

// 初始化图表
const initCharts = () => {
  console.log('初始化全局图表...');
  // 扣分类型分布图
  deductionTypeChart = echarts.init(deductionTypeChartRef.value);
  const deductionTypeOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: ['未获得学位', '未就业', '师德师风', '学术道德', '科研项目', '其他']
    },
    series: [
      {
        name: '扣分类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 0, name: '未获得学位' },
          { value: 0, name: '未就业' },
          { value: 0, name: '师德师风' },
          { value: 0, name: '学术道德' },
          { value: 0, name: '科研项目' },
          { value: 0, name: '其他' }
        ]
      }
    ]
  };
  deductionTypeChart.setOption(deductionTypeOption);

  // 扣分时间分布图
  deductionTimeChart = echarts.init(deductionTimeChartRef.value);
  const deductionTimeOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value',
      name: '扣分次数'
    },
    series: [
      {
        name: '扣分次数',
        type: 'bar',
        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        itemStyle: {
          color: '#ff7f50'
        }
      }
    ]
  };
  deductionTimeChart.setOption(deductionTimeOption);

  // 教师扣分排名图
  teacherDeductionChart = echarts.init(teacherDeductionChartRef.value);
  const teacherDeductionOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '扣分总分'
    },
    yAxis: {
      type: 'category',
      data: []
    },
    series: [
      {
        name: '扣分总分',
        type: 'bar',
        data: [],
        itemStyle: {
          color: function(params) {
            // 根据扣分设置不同颜色
            const score = params.value;
            if (score >= 20) return '#f5222d'; // 严重
            if (score >= 10) return '#fa8c16'; // 中等
            if (score >= 5) return '#faad14'; // 轻微
            return '#52c41a'; // 轻微
          }
        }
      }
    ]
  };
  teacherDeductionChart.setOption(teacherDeductionOption);

  // 扣分原因分析图
  deductionReasonChart = echarts.init(deductionReasonChartRef.value);
  const deductionReasonOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        // 初始化时没有数据，返回默认提示
        return `<div style="font-weight:bold;font-size:14px;margin-bottom:6px;">扣分原因详情</div>
                <div style="font-size:12px;color:#666;">请先加载数据...</div>`;
      },
      textStyle: {
        fontSize: 13,
        lineHeight: 18
      },
      extraCssText: 'max-width:330px; white-space:pre-wrap; word-break:break-word; padding:10px; border-radius:4px;',
    },
    legend: {
      data: ['出现次数', '平均扣分'],
      right: 10,
      top: 5,
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '40px',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: [],
      inverse: true,  // 从上到下显示
      axisLabel: {
        formatter: function(value) {
          // 将长文本简化为最多25个字符
          if (value.length > 25) {
            return value.substring(0, 23) + '...';
          }
          return value;
        },
        width: 150,
        overflow: 'truncate',
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      }
    },
    series: [
      {
        name: '出现次数',
        type: 'bar',
        data: [],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#ff9a9e' },
            { offset: 1, color: '#ff7f50' }
          ])
        },
        barWidth: 12,
        barGap: '30%',
      },
      {
        name: '平均扣分',
        type: 'bar',
        data: [],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#ffd700' },
            { offset: 1, color: '#faad14' }
          ])
        },
        barWidth: 12,
        label: {
          show: true,
          position: 'right',
          formatter: '{c}分',
          color: '#333'
        }
      }
    ]
  };
  deductionReasonChart.setOption(deductionReasonOption);
};

// 更新图表数据
const updateCharts = () => {
  // 更新扣分类型分布
  const typeCount = {
    '未获得学位': 0,
    '未就业': 0,
    '师德师风': 0,
    '学术道德': 0,
    '科研项目': 0,
    '其他': 0
  };
  dataSource.value.forEach(item => {
    typeCount[item.deductionType] = (typeCount[item.deductionType] || 0) + 1;
  });
  deductionTypeChart.setOption({
    series: [{
      data: Object.entries(typeCount).map(([name, value]) => ({ name, value }))
    }]
  });

  // 更新扣分时间分布
  const timeCount = {
    '1月': 0, '2月': 0, '3月': 0, '4月': 0, '5月': 0, '6月': 0,
    '7月': 0, '8月': 0, '9月': 0, '10月': 0, '11月': 0, '12月': 0
  };
  dataSource.value.forEach(item => {
    if (item.deductionDate) {
      try {
        const date = dayjs(item.deductionDate);
        if (date.isValid()) {
          const month = date.month() + 1;
          timeCount[`${month}月`] = (timeCount[`${month}月`] || 0) + 1;
        }
      } catch (error) {
        console.error('日期解析错误:', error);
      }
    }
  });
  deductionTimeChart.setOption({
    series: [{
      data: Object.values(timeCount)
    }]
  });

  // 更新教师扣分排名
  const teacherDeductionMap = {};
  dataSource.value.forEach(item => {
    if (!teacherDeductionMap[item.username]) {
      teacherDeductionMap[item.username] = 0;
    }
    teacherDeductionMap[item.username] += item.deductionScore;
  });
  
  const teacherDeductionData = Object.entries(teacherDeductionMap)
    .map(([name, score]) => ({ name, score }))
    .sort((a, b) => b.score - a.score)
    .slice(0, 10);
  
  teacherDeductionChart.setOption({
    yAxis: {
      data: teacherDeductionData.map(item => item.name)
    },
    series: [{
      data: teacherDeductionData.map(item => item.score)
    }]
  });

  // 更新扣分原因分析
  updateDeductionReasonChart();
};

// 更新扣分原因分析
const updateDeductionReasonChart = () => {
  // 简化扣分原因文本，提取主要信息
  const simplifiedReasonData = {};
  
  dataSource.value.forEach(item => {
    // 提取扣分原因的主要部分，按照不同类型处理
    let simplifiedReason = '';
    
    if (item.deductionType === '未获得学位') {
      simplifiedReason = '未按期获得学位';
    } else if (item.deductionType === '未就业') {
      if (item.deductionReason.includes('8月31日前未就业')) {
        simplifiedReason = '毕业当年8月底前未就业';
      } else if (item.deductionReason.includes('12月31日前未就业')) {
        simplifiedReason = '毕业当年12月底前未就业';
      } else {
        simplifiedReason = '研究生未按期就业';
      }
    } else if (item.deductionType === '师德师风') {
      simplifiedReason = '师德师风考核不合格';
    } else if (item.deductionType === '学术道德') {
      if (item.deductionReason.includes('论文抽检不合格')) {
        simplifiedReason = '学位论文抽检不合格';
      } else {
        simplifiedReason = '违反学术道德';
      }
    } else if (item.deductionType === '科研项目') {
      if (item.deductionReason.includes('未主持在研国家级项目')) {
        simplifiedReason = '缺少国家级项目';
      } else if (item.deductionReason.includes('科研经费不足')) {
        simplifiedReason = '科研经费不足';
      } else {
        simplifiedReason = '科研项目问题';
      }
    } else {
      // 使用正则表达式匹配主要内容，改进匹配模式
      const mainReasonMatch = item.deductionReason.match(/^(.+?)(?:，根据|，取消)/);
      if (mainReasonMatch && mainReasonMatch[1]) {
        simplifiedReason = mainReasonMatch[1];
      } else {
        // 如果没有匹配到，尝试截取前25个字符
        simplifiedReason = item.deductionReason.substring(0, 25);
        if (item.deductionReason.length > 25) {
          simplifiedReason += '...';
        }
      }
    }
    
    if (!simplifiedReasonData[simplifiedReason]) {
      simplifiedReasonData[simplifiedReason] = {
        count: 0,
        totalScore: 0,
        fullReason: item.deductionReason, // 保存完整原因用于tooltip
        type: item.deductionType // 保存扣分类型
      };
    }
    
    simplifiedReasonData[simplifiedReason].count++;
    simplifiedReasonData[simplifiedReason].totalScore += item.deductionScore;
  });

  // 按平均扣分排序，取前6个原因
  const sortedReasons = Object.entries(simplifiedReasonData)
    .map(([reason, data]) => ({
      reason,
      count: data.count,
      avgScore: data.totalScore / data.count,
      totalScore: data.totalScore,
      type: data.type,
      fullReason: data.fullReason // 保存完整原因用于tooltip
    }))
    .sort((a, b) => b.avgScore - a.avgScore)
    .slice(0, 6);
  
  const reasons = sortedReasons.map(item => item.reason);
  const reasonCounts = sortedReasons.map(item => item.count);
  const reasonAverages = sortedReasons.map(item => item.avgScore.toFixed(1));
  
  // 创建扣分详情数据，用于tooltip显示
  const detailData = {};
  reasons.forEach((reason, index) => {
    detailData[reason] = {
      fullReason: sortedReasons[index].fullReason,
      count: reasonCounts[index],
      avgScore: reasonAverages[index]
    };
  });
  
  // 更新图表配置
  deductionReasonChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function(params) {
        const reason = params[0].name; // 获取当前鼠标悬停的原因名称
        const detail = detailData[reason]; // 获取对应的详细信息
        
        if (!detail) return '';
        
        return `<div style="font-weight:bold;margin-bottom:5px;font-size:18px;">扣分原因详情</div>
                <div style="max-width:250px;word-break:break-word;white-space:normal;line-height:1.4;padding:3px 0;font-size:16px;">${detail.fullReason}</div>
                <div style="margin-top:6px;color:#fff;font-size:16px;">
                  <span style="display:inline-block;width:8px;height:8px;background-color:${params[0].color};margin-right:4px;border-radius:50%;"></span>
                  出现次数：<b>${detail.count}</b><br/>
                  <span style="display:inline-block;width:8px;height:8px;background-color:${params[1].color};margin-right:4px;border-radius:50%;"></span>
                  平均扣分：<b>${detail.avgScore}</b>
                </div>`;
      },
      textStyle: {
        fontSize: 13,
        lineHeight: 18
      },
      extraCssText: 'max-width:280px; white-space:pre-wrap; word-break:break-word; padding:8px;',
    },
    yAxis: {
      data: reasons
    },
    series: [
      {
        name: '出现次数',
        data: reasonCounts,
        itemStyle: {
          color: function(params) {
            // 根据扣分类型设置颜色
            const type = sortedReasons[params.dataIndex].type;
            return new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#ff9a9e' },
              { offset: 1, color: getTypeColor(type) || '#ff7f50' }
            ]);
          }
        }
      },
      {
        name: '平均扣分',
        data: reasonAverages
      }
    ]
  });

  // 存储排序后的扣分原因数据，用于tooltip访问
  sortedReasonsList = sortedReasons;
};

// 监听数据变化
watch(() => dataSource.value, () => {
  updateCharts();
}, { deep: true });

// 监听窗口大小变化
window.addEventListener('resize', () => {
  deductionTypeChart?.resize();
  deductionTimeChart?.resize();
  teacherDeductionChart?.resize();
  deductionReasonChart?.resize();
});

// 初始化数据
onMounted(() => {
  console.log('组件挂载完成，开始初始化...');
  try {
    // 确保图表DOM元素已加载
    console.log('初始化全局图表...');
    initCharts();
    // 获取用户列表
    fetchUsersList();
    console.log('开始获取数据...');
    fetchData();
    console.log('组件初始化完成');
  } catch (error) {
    console.error('组件初始化失败:', error);
  }
});

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    
    // 获取当前用户ID
    const currentUserId = await getUserId(true);
    
    // 准备请求参数
    const params = {
      userId: searchForm.userId, // 搜索框中的userId
      deductionType: searchForm.deductionType,
      startDate: searchForm.deductionDate && searchForm.deductionDate[0] ? dayjs(searchForm.deductionDate[0]).format('YYYY-MM-DD') : undefined,
      endDate: searchForm.deductionDate && searchForm.deductionDate[1] ? dayjs(searchForm.deductionDate[1]).format('YYYY-MM-DD') : undefined,
      page: pagination.current,
      pageSize: pagination.pageSize
    };
    
    // 如果userId为空字符串，删除这个参数
    if (params.userId === '') {
      delete params.userId;
    }
    
    console.log('请求参数:', params);
    
    let response;
    
    if (viewMode.value === 'personal') {
      // 个人视图 - 确保传入userId参数
      if (!params.userId && currentUserId) {
        params.userId = currentUserId;
      }
      
      console.log('获取个人扣分列表，参数:', params);
      // 个人视图 - 使用个人扣分列表接口
      response = await getPersonalDeductions(params);
      
      // 同时获取个人统计数据
      try {
        // 确保传入用户ID
        const statsParams = { userId: currentUserId };
        console.log('获取个人统计数据，参数:', statsParams);
        
        const statsResponse = await getPersonalDeductionStats(statsParams);
        if (statsResponse.code === 200 && statsResponse.data) {
          personalStats.value = statsResponse.data;
        }
      } catch (statsError) {
        console.error('获取个人统计数据失败:', statsError);
      }
    } else {
      // 管理员视图 - 使用全部扣分列表接口
      response = await getDeductions(params);
    }
    
    console.log('API响应:', response);
    
    if (response && response.code === 200 && response.data) {
      // 处理日期格式
      const formattedList = (response.data.list || []).map(item => {
        if (item.deductionDate) {
          // 确保日期格式一致
          return {
            ...item,
            deductionDate: item.deductionDate // 保持原始格式，在显示时处理
          };
        }
        return item;
      });
      
      dataSource.value = formattedList;
      pagination.total = response.data.total || 0;
      console.log('数据获取成功，数量:', dataSource.value.length);
      
      // 更新图表
      updateCharts();
    } else {
      console.error('获取数据失败:', response);
      message.error('获取数据失败');
    }
  } catch (error) {
    console.error('获取数据异常:', error);
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索提交
const handleSearch = () => {
  pagination.current = 1; // 重置到第一页
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  if (searchForm) {
    searchForm.userId = '';
    searchForm.deductionType = undefined;
    searchForm.deductionDate = null;
  }
  handleSearch();
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 显示新增模态框
const showAddModal = () => {
  modalTitle.value = '新增扣分';
  // 清空表单
  formState.id = '';
  formState.username = '';
  formState.deductionType = undefined;
  formState.deductionDate = null;
  formState.deductionScore = undefined;
  formState.deductionReason = '';
  formState.handleResult = '';
  formState.remark = '';
  formState.userId = '';
  
  // 打印清空后的表单状态
  console.log('新增模态框表单初始化状态:', formState);
  
  // 显示模态框
  console.log('打开新增模态框');
  modalVisible.value = true;
};

// 显示编辑模态框
const showEditModal = (record) => {
  console.log('显示编辑模态框', record);
  modalTitle.value = '编辑扣分';
  
  // 先复制基本非日期字段
  Object.keys(formState).forEach(key => {
    if (key !== 'deductionDate' && key in record) {
      formState[key] = record[key];
    }
  });
  
  // 单独处理日期字段
  if (record.deductionDate) {
    console.log('处理日期:', record.deductionDate);
    try {
      // 转换为 dayjs 对象
      formState.deductionDate = dayjs(record.deductionDate);
    } catch (error) {
      console.error('日期转换错误:', error);
      formState.deductionDate = null;
    }
  } else {
    formState.deductionDate = null;
  }
  
  // 确保用户信息正确设置
  if (record.userId && !formState.userId) {
    formState.userId = record.userId;
  }
  if (record.username && !formState.username) {
    formState.username = record.username;
  }
  
  modalVisible.value = true;
};

// 处理模态框确定
const handleModalOk = () => {
  console.log('处理模态框确定，当前表单状态:', formState);
  
  formRef.value
    .validate()
    .then(async () => {
      try {
        confirmLoading.value = true;
        console.log('表单验证通过，准备提交数据:', formState);
        
        // 通过 useUserId 获取用户ID
        const currentUserId = await getUserId(true);
        if (!currentUserId) {
          throw new Error('无法获取用户ID');
        }
        
        // 转换日期格式，确保日期是正确的格式
        const submitData = {
          ...formState,
          deductionDate: formState.deductionDate ? dayjs(formState.deductionDate).format('YYYY-MM-DD') : '',
        };
        
        // 不需要在请求体中包含userId，而是在URL中添加
        delete submitData.userId;
        
        console.log('提交的数据:', submitData);
        
        // 获取基础URL
        const baseUrl = request.defaults?.baseURL || '';
        let response;
        
        if (formState.id) {
          // 更新扣分 - 确保在URL上添加userId参数
          const url = `${baseUrl}/sys/deduction/${formState.id}?userId=${currentUserId}`;
          console.log('发送更新请求到:', url);
          
          response = await axios.put(url, submitData, {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': localStorage.getItem('zyadmin-1.0.0-token') || ''
            }
          });
        } else {
          // 添加扣分 - 确保在URL上添加userId参数
          const url = `${baseUrl}/sys/deduction?userId=${currentUserId}`;
          console.log('发送创建请求到:', url);
          
          response = await axios.post(url, submitData, {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': localStorage.getItem('zyadmin-1.0.0-token') || ''
            }
          });
        }
        
        console.log('提交数据响应:', response);
        
        if (response.data && response.data.code === 200) {
          message.success(formState.id ? '更新成功' : '添加成功');
          modalVisible.value = false;
          fetchData(); // 刷新数据
        } else {
          message.error((response.data && response.data.message) || (formState.id ? '更新失败' : '添加失败'));
        }
      } catch (error) {
        console.error('提交数据异常:', error);
        message.error(formState.id ? '更新失败' : '添加失败');
      } finally {
        confirmLoading.value = false;
      }
    })
    .catch(error => {
      console.log('表单验证失败:', error);
    });
};

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 确认删除
const confirmDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDelete(record)
  })
}

// 删除记录
const handleDelete = async (record) => {
  loading.value = true;
  try {
    // 获取用户ID
    const { getUserId } = useUserId();
    const userId = await getUserId(true);

    // 直接传递userId参数
    const response = await deleteDeduction(record.id, userId);

    if (response.code === 200) {
      message.success('删除成功');
      fetchData();
    } else {
      message.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  } finally {
    loading.value = false;
  }
};

// 导出数据
const handleExport = () => {
  try {
    // 准备导出参数
    const params = {
      deductionType: searchForm.deductionType,
      startDate: searchForm.deductionDate && searchForm.deductionDate[0] ? searchForm.deductionDate[0].format('YYYY-MM-DD') : undefined,
      endDate: searchForm.deductionDate && searchForm.deductionDate[1] ? searchForm.deductionDate[1].format('YYYY-MM-DD') : undefined
    };
    
    // 只有在userId有值时才添加参数
    if (searchForm.userId) {
      params.userId = searchForm.userId;
    }
    
    exportDeductions(params)
      .then(response => {
        // 处理导出的文件
        const blob = new Blob([response.data], { type: response.headers['content-type'] });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `扣分数据_${new Date().getTime()}.xlsx`;
        link.click();
        URL.revokeObjectURL(link.href);
        message.success('导出成功');
      })
      .catch(error => {
        console.error('导出失败:', error);
        message.error('导出失败');
      });
  } catch (error) {
    console.error('导出操作异常:', error);
    message.error('导出失败');
  }
};

// 获取个人扣分统计数据
const fetchPersonalStats = async () => {
  console.log('获取个人扣分统计数据');
  
  try {
    // 获取当前用户ID
    const currentUserId = await getUserId(true);
    if (!currentUserId) {
      console.error('无法获取用户ID');
      message.error('无法获取用户ID');
      return;
    }
    
    // 确保传入用户ID参数
    const params = { userId: currentUserId };
    console.log('获取个人统计数据，参数:', params);
    
    getPersonalDeductionStats(params)
      .then(response => {
        if (response.code === 200) {
          personalStats.value = response.data;
          console.log('个人扣分统计获取成功:', response.data);
        } else {
          console.error('获取个人扣分统计失败:', response.message);
          message.error(response.message || '获取个人统计失败');
        }
      })
      .catch(error => {
        console.error('获取个人扣分统计异常:', error);
        message.error('获取个人统计失败');
      });
  } catch (error) {
    console.error('获取个人统计前准备失败:', error);
    message.error('获取个人统计失败');
  }
};

// 切换视图模式
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'personal' ? 'admin' : 'personal';
  pagination.current = 1; // 重置分页
  fetchData(); // 重新获取数据

  // 如果是切换到个人模式，获取个人统计数据
  if (viewMode.value === 'personal') {
    fetchPersonalStats();
  }
};

// 文件上传前检查
const beforeUpload = (file) => {
  // 检查文件类型
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传Excel文件!');
    return false;
  }
  
  // 检查文件大小（限制10MB）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件必须小于10MB!');
    return false;
  }
  
  return true;
};

// 处理文件导入
const handleImport = (options) => {
  const { file } = options;
  loading.value = true;
  
  // 调用导入API
  importDeductions(file)
    .then(response => {
      if (response.code === 200) {
        message.success('导入成功');
        // 刷新数据
        fetchData();
      } else {
        message.error(response.message || '导入失败');
      }
    })
    .catch(error => {
      console.error('导入失败:', error);
      message.error('导入失败');
    })
    .finally(() => {
      loading.value = false;
    });
};

// 监听模式切换
watch(() => viewMode.value, (newMode) => {
  console.log('视图模式切换:', newMode);
  pagination.current = 1;
  fetchData();
  
  // 如果是切换到个人模式，则获取个人统计
  if (newMode === 'personal') {
    fetchPersonalStats();
  }
}, { immediate: true });

// 处理用户变化
const handleUserChange = (value) => {
  // 找到选中的用户
  const selectedUser = usersOptions.value.find(user => user.value === value);
  if (selectedUser) {
    // 更新表单中的用户名
    formState.username = selectedUser.label;
  }
};

// 获取用户列表
const fetchUsersList = async () => {
  try {
    const res = await usersList({
      pagination: { current: 1, pageSize: 100 },
      params: {}
    });
    
    console.log('用户列表API响应数据:', res);
    
    // 处理不同的API返回格式
    let userData = [];
    
    // 情况1: {status: 1, message: "Success.", data: {result: [...]}}
    if (res && res.status === 1 && res.data && Array.isArray(res.data.result)) {
      userData = res.data.result;
    }
    // 情况2: {data: {status: 1, message: "Success.", data: {result: [...]}}}
    else if (res && res.data && res.data.status === 1 && res.data.data && Array.isArray(res.data.data.result)) {
      userData = res.data.data.result;
    }
    // 情况3: {code: 200, message: "获取成功", data: {list: [...]}}
    else if (res && res.code === 200 && res.data && Array.isArray(res.data.list)) {
      userData = res.data.list;
    }
    // 情况4: {data: {code: 200, message: "获取成功", data: {list: [...]}}}
    else if (res && res.data && res.data.code === 200 && res.data.data && Array.isArray(res.data.data.list)) {
      userData = res.data.data.list;
    }
    
    if (userData.length > 0) {
      usersOptions.value = userData.map(user => ({
        label: user.nickname || user.username,
        value: user.id
      }));
      console.log('获取用户列表成功，数量:', usersOptions.value.length);
    } else {
      console.error('获取用户列表失败，未找到有效数据:', res);
      message.error('获取用户列表失败，请刷新页面重试');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  }
};

// 初始化教师扣分排名图表
const initTeacherDeductionChart = () => {
  if (!teacherDeductionChartRef.value) return;
  
  try {
    const teacherChart = echarts.init(teacherDeductionChartRef.value);
    
    // 模拟数据，基于当前dataSource构建
    const teacherDeductionMap = {};
    dataSource.value.forEach(item => {
      if (!teacherDeductionMap[item.username]) {
        teacherDeductionMap[item.username] = 0;
      }
      teacherDeductionMap[item.username] += item.deductionScore;
    });
    
    // 转换为数组并排序
    const sortedData = Object.entries(teacherDeductionMap)
      .map(([username, score]) => ({ username, score }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 10); // 取前10名
    
    const usernames = sortedData.map(item => item.username);
    const scores = sortedData.map(item => item.score);
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01]
      },
      yAxis: {
        type: 'category',
        data: usernames,
        axisLabel: {
          interval: 0,
          rotate: 0,
          formatter: function(value) {
            if (value.length > 5) {
              return value.substring(0, 5) + '...';
            }
            return value;
          }
        }
      },
      series: [
        {
          name: '扣分总值',
          type: 'bar',
          data: scores,
          itemStyle: {
            color: '#FF6B6B'
          },
          label: {
            show: true,
            position: 'right',
            formatter: '{c}'
          }
        }
      ]
    };
    
    teacherChart.setOption(option);
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      teacherChart.resize();
    });
    
    // 返回图表实例
    return teacherChart;
  } catch (error) {
    console.error('初始化教师扣分排名图表失败:', error);
    return null;
  }
};

// 处理日期格式
const formatDate = (date) => {
  if (!date) return '';
  
  try {
    // 统一使用 dayjs 处理各种日期格式
    return dayjs(date).format('YYYY-MM-DD');
  } catch (error) {
    console.error('日期格式化错误:', error);
    // 如果出错，尝试返回原始值
    if (typeof date === 'string') {
      return date;
    }
    return '';
  }
};

// 处理日期显示
const parseDate = (dateStr) => {
  if (!dateStr) return null;
  // 使用 dayjs 处理字符串日期
  try {
    return dayjs(dateStr);
  } catch (error) {
    console.error('日期解析错误:', error);
    return null;
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';
.deductions-container {
  padding: 24px;
}

.chart-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.chart-card:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.09);
  transform: translateY(-3px);
}

.filter-card {
  margin-bottom: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.table-striped-row {
  background-color: #f9f9f9;
}

/* 自定义折叠面板样式 */
:deep(.ant-collapse-header) {
  font-size: 15px;
  transition: all 0.3s;
}

:deep(.ant-collapse-header:hover) {
  color: #40a9ff !important;
}

:deep(.ant-collapse-content-box) {
  padding: 16px 24px !important;
  background-color: #fafafa;
  border-radius: 0 0 6px 6px;
}

/* 自定义表格样式 */
:deep(.ant-table-thead > tr > th) {
  background-color: #f0f5ff;
  color: #1890ff;
  font-weight: 500;
}

:deep(.ant-table-row:hover > td) {
  background-color: #e6f7ff !important;
}

/* 自定义标签样式 */
:deep(.ant-tag) {
  border: none;
  padding: 2px 8px;
  border-radius: 4px;
}

/* 自定义按钮样式 */
:deep(.ant-btn) {
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon) {
  margin-right: 4px;
}

/* 自定义卡片样式 */
:deep(.ant-card) {
  border-radius: 6px;
}

.chart-hint {
  text-align: center;
  color: #909399;
  font-size: 12px;
  margin-bottom: 10px;
}

/* 个人统计卡片样式 */
.personal-info-card {
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 576px) {
  .deductions-container {
    padding: 16px 12px;
  }
  
  :deep(.ant-statistic-title) {
    font-size: 14px;
  }
  
  :deep(.ant-statistic-content) {
    font-size: 20px;
  }
}
</style> 