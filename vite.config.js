import {fileURLToPath, URL} from 'node:url'

import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    // 加载环境变量
    const env = loadEnv(mode, process.cwd());
    
    // 从环境变量读取配置
    let apiBaseUrl;
    if (mode === 'production') {
        // 生产环境使用相对路径
        apiBaseUrl = '/v1';
    } else {
        // 开发环境使用环境变量或默认值
        apiBaseUrl = env.VITE_API_BASE_URL || 'http://localhost:3089/v1';
    }
    
    const serverPort = parseInt(env.VITE_PORT || '3090');
    const serverHost = env.VITE_HOST || '0.0.0.0';
    
    console.log(`[vite配置] 环境: ${mode}, API地址: ${apiBaseUrl}, 当前环境变量: `, env);
    
    return {
        plugins: [
            vue(), 
            vueJsx(),
        ],
        server: {
            //本地服务器主机名 配置后可以使用本地网络访问
            host: serverHost,
            //指定启动端口号
            port: serverPort,
            //设为 true 时若端口已被占用则会直接退出，而不是尝试下一个可用端口
            strictPort: false,
            //服务器启动时自动在浏览器中打开应用程序,当此值为字符串时，会被用作 URL 的路径名
            open: true,
            proxy: {
                '/api': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, '')
                },
                '/v1': {
                    target: apiBaseUrl,
                    changeOrigin: true
                },
                '/auth': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                // 新的绩效管理模块代理
                '/highLevelPapers': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/researchProjects': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/teachingReformProjects': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/patents': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/conferences': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/textbooks': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/academicAppointments': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/studentAwardGuidanceAwards': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/studentProjectGuidanceProjects': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                // 级别和分类模块代理
                '/patentCategories': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/textbookCategories': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/teachingReformProjectLevels': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/studentProjectGuidanceProjectLevels': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/studentAwardGuidanceAwardLevels': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/conferencesLevels': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/associationLevels': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/researchProjectsLevels': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                // 规则模块代理
                '/scoreWeights': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/highLevelPapersRules': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/deductionsRules': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/internationalExchangesRules': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/researchFundsRules': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/socialServicesRules': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                // 其他模块代理
                '/timeInterval': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/researchFunds': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/international': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/file': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/teachingReformParticipants': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/research-project': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/sys': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                    rewrite: (path) => path
                },
                '/teacher': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/home': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/notifications': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/user-levels': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                },
                '/userRanking': {
                    target: apiBaseUrl,
                    changeOrigin: true,
                }
            }
        },
        resolve: {
            // 别名
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url)),
                "comps": fileURLToPath(new URL('src/components', import.meta.url)),
                "views": fileURLToPath(new URL('src/views', import.meta.url)),
                "store": fileURLToPath(new URL('src/store', import.meta.url)),
                "utils": fileURLToPath(new URL('src/utils', import.meta.url)),
                "libs": fileURLToPath(new URL('src/libs', import.meta.url)),
                "api": fileURLToPath(new URL('src/api', import.meta.url)),
                "styles": fileURLToPath(new URL('src/styles', import.meta.url)),
                "config": fileURLToPath(new URL('src/config', import.meta.url)),
            }
        },
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData: '@import \'@/assets/styles/abstracts/_variables.scss\';'
                },
                less: {
                    modifyVars: {
                        // 在这里添加你想要修改的 Ant Design Vue 主题变量
                        // 例如，修改主色为蓝色
                        '@primary-color': "#d2b48c",
                        '@success-color': "#80b178",
                        '@warning-color': "#d8c49a",
                        '@error-color': "#d1786b",
                        '@heading-color': "#333333",
                        '@text-color': "#666666",
                        '@text-color-secondary': "#999999",
                    },
                    javascriptEnabled: true
                }

            },

        },
    }
})
