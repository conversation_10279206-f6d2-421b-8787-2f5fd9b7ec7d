// routes/userRoutes.js
const express = require('express');
const userController = require('../../../controllers/v1/auth/userController');

const router = express.Router();

/**
 * 获取所有用户
 * @route POST /v1/sys/user/list
 * @group 用户管理 - 系统用户相关接口
 * @param {object} query.body - 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'createdAt', order: 'ascend'}}
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {current: 1, pageSize: 15, total: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', ...userController.getAllUsers);

/**
 * 创建用户
 * @route POST /v1/sys/user/create
 * @group 用户管理 - 系统用户相关接口
 * @param {string} username.body.required - 用户名
 * @param {string} password.body.required - 密码
 * @param {string} nickname.body - 昵称
 * @param {string} email.body - 邮箱
 * @param {string} phone.body - 手机号
 * @param {number} roleId.body.required - 角色ID
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "用户ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', ...userController.createUser);

/**
 * 获取指定用户
 * @route POST /v1/sys/user/findOne
 * @group 用户管理 - 系统用户相关接口
 * @param {number} id.body.required - 用户ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {用户详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/findOne', ...userController.findOneUser);

/**
 * 更新用户
 * @route POST /v1/sys/user/update
 * @group 用户管理 - 系统用户相关接口
 * @param {number} id.body.required - 用户ID
 * @param {string} username.body - 用户名
 * @param {string} nickname.body - 昵称
 * @param {string} email.body - 邮箱
 * @param {string} phone.body - 手机号
 * @param {number} roleId.body - 角色ID
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update', ...userController.updateUser);

/**
 * 删除用户
 * @route POST /v1/sys/user/delete
 * @group 用户管理 - 系统用户相关接口
 * @param {number} id.body.required - 用户ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', ...userController.deleteUser);

/**
 * 搜索用户
 * @route POST /v1/sys/user/search
 * @group 用户管理 - 系统用户相关接口
 * @param {string} keyword.body.required - 搜索关键词(用户名或学号/工号)
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [用户列表]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/search', ...userController.searchUsers);

/**
 * 获取用户级别列表
 * @route GET /v1/sys/user/user-levels
 * @group 用户管理 - 系统用户相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [用户级别列表]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/user-levels', ...userController.getUserLevels);

/**
 * 获取部门列表
 * @route GET /v1/sys/user/departments
 * @group 用户管理 - 系统用户相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [部门列表]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/departments', ...userController.getDepartments);

/**
 * 获取用户职称记录
 * @route POST /v1/sys/user/level-records
 * @group 用户管理 - 系统用户相关接口
 * @param {string} userId.body.required - 用户ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [职称记录列表]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level-records', ...userController.getUserLevelRecords);

/**
 * 保存用户职称记录
 * @route POST /v1/sys/user/save-level-records
 * @group 用户管理 - 系统用户相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {array} records.body.required - 职称记录数组
 * @returns {object} 200 - {code: 200, message: "保存成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/save-level-records', ...userController.saveUserLevelRecords);

module.exports = router;
