import request from '../server'
import { saveAs } from 'file-saver'

/**
 * 获取会议列表
 * @param {Object} params - 请求参数
 * @param {string} [params.conferenceName] - 会议名称（模糊搜索）
 * @param {string} [params.levelId] - 会议级别ID
 * @param {string} [params.holdTimeStart] - 举办开始日期
 * @param {string} [params.holdTimeEnd] - 举办结束日期
 * @param {string} [params.userId] - 用户ID（可选，如果提供则获取特定用户的参与会议）
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @param {string} [params.range='all'] - 统计范围筛选：'in'(范围内), 'out'(范围外), 'all'(全部)
 * @param {string} [params.reviewStatus='all'] - 审核状态筛选：'all'(全部), 'reviewed'(已审核), 'pending'(待审核), 'rejected'(已拒绝)
 * @returns {Promise} - 请求结果
 */
export function getConferences(params) {
  return request.post('/conferences/list', params || {})
}

/**
 * 获取会议详情
 * @param {String} id - 会议ID
 * @returns {Promise} 响应结果
 */
export function getConferenceDetail(id) {
  return request.post('/conferences/project/detail', { id })
}

/**
 * 创建会议
 * @param {Object} data - 会议数据
 * @returns {Promise} 响应结果
 */
export function addConference(data) {
  return request.post('/conferences/project/create', data || {})
}

/**
 * 更新会议
 * @param {String} id - 会议ID
 * @param {Object} data - 会议数据
 * @returns {Promise} 响应结果
 */
export function updateConference(id, data) {
  return request.post('/conferences/project/update', { id, ...data })
}

/**
 * 删除会议
 * @param {String} id - 会议ID
 * @returns {Promise} 响应结果
 */
export function deleteConference(id) {
  return request.delete(`/conferences/project/delete/${id}`)
}

/**
 * 审核会议
 * @param {Object} data - 额外数据，包含审核人信息
 * @returns {Promise} 响应结果
 */
export function reviewConference(data) {
  return request.post('/conferences/project/review', data)
}

/**
 * 导入会议数据
 * @param {File} file - 上传的Excel文件
 * @returns {Promise} 响应结果
 */
export function importConferences(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/conferences/projects/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出会议数据
 * @param {Object} params - 查询参数
 * @returns {Promise} 响应结果
 */
export function exportConferences(params) {
  return request.post('/conferences/projects/export', params, { responseType: 'blob' })
    .then(response => {
      // 处理文件下载
      const fileName = params.fileName || '会议数据.xlsx'
      saveAs(new Blob([response]), fileName)
      return { code: 200, message: '导出成功' }
    })
}

/**
 * 获取会议时间分布
 * @param {Object} data - 请求参数
 * @returns {Promise} 响应结果
 */
export function getConferenceTimeDistribution(data) {
  return request.post('/conferences/statistics/time-distribution', data || {})
}

/**
 * 获取会议级别分布数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getLevelDistribution(params = {}) {
  return request.post('/conferences/statistics/level-distribution', params)
}

/**
 * 获取教师会议排名数据
 * @param {Object} params - 查询参数，包括range、reviewStatus和limit
 * @returns {Promise} - 请求结果
 */
export function getTeacherConferenceRanking(params = {}) {
  return request.post('/conferences/statistics/teacher-ranking', params)
}

/**
 * 获取教师会议详情
 * @param {Object} data
 * @returns Promise
 */
export function getTeacherConferenceDetails(data) {
  return request.post('/conferences/statistics/teacher-project-details', data)
}

/**
 * 获取会议统计概览数据
 * @param {Object} params - 查询参数，包括userId
 * @returns {Promise} - 请求结果
 */
export function getConferenceStatistics(params = {}) {
  return request.post('/conferences/statistics/overview', params)
}

/**
 * 获取会议总分统计
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */

export function getConferencesTotalScore(params) {
  return request.post('/conferences/statistics/conferences-total-score', params);
}

/**
 * 获取用户会议详情
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */

export function getUserConferencesDetail(params) {
  return request.post('/conferences/user/details', params);
}

/**重新提交审核 */
export function reapplyReview(params) {
  return request.post('/conferences/reapply', params)
}

/**
 * 获取审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getReviewStatusOverview(params) {
  return request.post('/conferences/statistics/review-status-overview', params)
}

