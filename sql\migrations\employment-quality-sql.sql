-- 就业质量测试数据SQL

-- 创建就业质量表
CREATE TABLE IF NOT EXISTS `employment_quality` (
  `id` VARCHAR(36) NOT NULL,
  `major` VARCHAR(50) NOT NULL COMMENT '专业',
  `year` INT NOT NULL COMMENT '年份',
  `employmentRate` DECIMAL(5,2) NOT NULL COMMENT '就业率',
  `employmentIndustry` VARCHAR(50) NOT NULL COMMENT '就业行业',
  `employmentRegion` VARCHAR(50) NOT NULL COMMENT '就业地区',
  `averageSalary` DECIMAL(10,2) NOT NULL COMMENT '平均薪资',
  `majorMatchRate` DECIMAL(5,2) NOT NULL COMMENT '专业匹配率',
  `employmentSatisfaction` DECIMAL(5,2) NOT NULL COMMENT '就业满意度',
  `score` DECIMAL(5,2) NOT NULL COMMENT '综合评分',
  `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `userId` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `createdAt` DATETIME NOT NULL,
  `updatedAt` DATETIME NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_major` (`major`),
  INDEX `idx_year` (`year`),
  INDEX `idx_userId` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='就业质量表';

-- 清空已有数据
DELETE FROM `employment_quality`;

-- 插入测试数据到就业质量表
INSERT INTO `employment_quality` 
(`id`, `major`, `year`, `employmentRate`, `employmentIndustry`, `employmentRegion`, 
`averageSalary`, `majorMatchRate`, `employmentSatisfaction`, `score`, `status`, `userId`, `username`, `createdAt`, `updatedAt`) 
VALUES
('b7a8d1c3-e6f2-4a5b-9c8d-7e6f5a4b3c2d', '计算机科学与技术', 2023, 95.50, 'IT', '一线城市', 12000.00, 85.50, 90.50, 92.00, 1, '001', '张三', NOW(), NOW()),
('c8b9e2d3-f7g4-5h6i-0j1k-8l9m6n5o4p3', '金融学', 2023, 92.30, '金融', '一线城市', 11000.00, 80.50, 88.50, 90.00, 1, '002', '李四', NOW(), NOW()),
('d9c0f3e4-g8h5-6i7j-1k2l-9m0n7o6p5q4', '教育学', 2023, 88.70, '教育', '二线城市', 8000.00, 90.50, 85.50, 88.00, 1, '003', '王五', NOW(), NOW()),
('e0d1g4f5-h9i6-7j8k-2l3m-0n1o8p7q6r5', '机械工程', 2023, 90.20, '制造业', '二线城市', 9000.00, 75.50, 82.50, 85.00, 1, '004', '赵六', NOW(), NOW()),
('f1e2h5g6-i0j7-8k9l-3m4n-1o2p9q8r7s6', '工商管理', 2023, 87.50, '服务业', '三线城市', 7500.00, 70.50, 80.50, 82.00, 1, '005', '钱七', NOW(), NOW()),
('g2f3i6h7-j1k8-9l0m-4n5o-2p3q0r9s8t7', '计算机科学与技术', 2022, 93.20, 'IT', '一线城市', 11000.00, 83.20, 88.60, 90.00, 1, '001', '张三', NOW(), NOW()),
('h3g4j7i8-k2l9-0m1n-5o6p-3q4r1s0t9u8', '金融学', 2022, 90.10, '金融', '一线城市', 10000.00, 78.30, 86.70, 88.00, 1, '002', '李四', NOW(), NOW()),
('i4h5k8j9-l3m0-1n2o-6p7q-4r5s2t1u0v9', '教育学', 2022, 86.50, '教育', '二线城市', 7500.00, 88.20, 83.70, 86.00, 1, '003', '王五', NOW(), NOW()),
('j5i6l9k0-m4n1-2o3p-7q8r-5s6t3u2v1w0', '机械工程', 2022, 88.60, '制造业', '二线城市', 8500.00, 73.20, 80.40, 83.00, 1, '004', '赵六', NOW(), NOW()),
('k6j7m0l1-n5o2-3p4q-8r9s-6t7u4v3w2x1', '工商管理', 2022, 85.30, '服务业', '三线城市', 7000.00, 68.30, 78.60, 80.00, 1, '005', '钱七', NOW(), NOW()),
('l7k8n1m2-o6p3-4q5r-9s0t-7u8v5w4x3y2', '软件工程', 2023, 96.80, 'IT', '一线城市', 13500.00, 92.30, 93.50, 94.00, 1, '001', '张三', NOW(), NOW()),
('m8l9o2n3-p7q4-5r6s-0t1u-8v9w6x5y4z3', '数据科学', 2023, 94.50, 'IT', '一线城市', 13000.00, 89.70, 91.20, 93.00, 1, '002', '李四', NOW(), NOW()),
('n9m0p3o4-q8r5-6s7t-1u2v-9w0x7y6z5a4', '人工智能', 2023, 95.80, 'IT', '一线城市', 14000.00, 91.40, 92.80, 95.00, 1, '006', '孙八', NOW(), NOW()),
('o0n1q4p5-r9s6-7t8u-2v3w-0x1y8z7a6b5', '物联网工程', 2023, 93.70, 'IT', '一线城市', 12500.00, 88.90, 90.60, 91.00, 1, '007', '周九', NOW(), NOW()),
('p1o2r5q6-s0t7-8u9v-3w4x-1y2z9a8b7c6', '会计学', 2023, 89.40, '金融', '一线城市', 9500.00, 82.60, 86.30, 87.00, 1, '008', '吴十', NOW(), NOW()),
('q2p3s6r7-t1u8-9v0w-4x5y-2z3a0b9c8d7', '市场营销', 2023, 88.20, '服务业', '二线城市', 9000.00, 79.80, 84.50, 85.00, 1, '009', '郑十一', NOW(), NOW()),
('r3q4t7s8-u2v9-0w1x-5y6z-3a4b1c0d9e8', '国际贸易', 2023, 87.90, '服务业', '一线城市', 9200.00, 81.20, 85.70, 86.00, 1, '010', '王十二', NOW(), NOW()),
('s4r5u8t9-v3w0-1x2y-6z7a-4b5c2d1e0f9', '英语', 2023, 85.60, '教育', '二线城市', 8200.00, 87.30, 84.90, 85.00, 1, '011', '李十三', NOW(), NOW()),
('t5s6v9u0-w4x1-2y3z-7a8b-5c6d3e2f1g0', '法学', 2023, 86.80, '服务业', '一线城市', 9800.00, 85.40, 87.20, 88.00, 1, '012', '张十四', NOW(), NOW()),
('u6t7w0v1-x5y2-3z4a-8b9c-6d7e4f3g2h1', '医学', 2023, 94.30, '医疗', '一线城市', 11200.00, 93.60, 91.80, 93.00, 1, '013', '刘十五', NOW(), NOW()); 