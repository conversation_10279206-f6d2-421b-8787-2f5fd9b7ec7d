const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const multer = require('multer');
const jwt = require('jsonwebtoken');
const { JWT_SECRET } = require('../../../config');
const researchProjectModel = require('../../../models/v1/mapping/researchProjectModel');
const researchProjectParticipantsModel = require('../../../models/v1/mapping/researchProjectParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const roleModel = require('../../../models/v1/mapping/roleModel');
const fileModel = require('../../../models/v1/mapping/fileModel');
const researchProjectsLevelsModel = require('../../../models/v1/mapping/researchProjectsLevelsModel');
const { Op, QueryTypes, Sequelize } = require('sequelize');
const fileController = require('../common/fileController');
const { getTimeIntervalByName, getUserInfoFromRequest, getSequelizeInstance } = require('../../../utils/others');
const upload = multer({ dest: 'uploads/' });
const { updateUserRankings, RANKING_TYPE_MAPPINGS } = require('../../../utils/rankingUtils');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');



// 添加与参与者的关联
researchProjectModel.hasMany(researchProjectParticipantsModel, {
  foreignKey: 'projectId',
  as: 'participants'
});

/**
 * 将存储过程结果转换为数组格式
 * @param {Array} result - 存储过程返回的结果
 * @param {boolean} [removeMetadata=true] - 是否移除元数据字段
 * @param {boolean} [convertNumericStrings=true] - 是否将数字字符串转换为数字
 * @returns {Array} 处理后的数组
 */
const convertStoredProcResultToArray = (result, removeMetadata = true, convertNumericStrings = true) => {
  if (!Array.isArray(result)) {
    console.warn('存储过程结果不是数组:', result);
    return [];
  }
  
  // 处理MySQL存储过程返回的特殊格式
  let dataArray = [];
  
  // 如果结果是嵌套数组，取第一个元素
  if (Array.isArray(result[0])) {
    dataArray = result[0];
  } else if (typeof result[0] === 'object') {
    dataArray = result;
  }
  
  // 移除可能存在的元数据字段
  if (removeMetadata) {
    dataArray = dataArray.filter(item => {
      return !item.hasOwnProperty('meta') && 
             !item.hasOwnProperty('_meta') && 
             !item.hasOwnProperty('fieldCount');
    });
  }
  
  // 将数字字符串转换为数字
  if (convertNumericStrings) {
    dataArray = dataArray.map(item => {
      const newItem = {...item};
      
      for (const [key, value] of Object.entries(newItem)) {
        if (typeof value === 'string' && !isNaN(value) && value.trim() !== '') {
          if (value.includes('.')) {
            newItem[key] = parseFloat(value);
          } else {
            newItem[key] = parseInt(value, 10);
          }
        }
      }
      
      return newItem;
    });
  }
  
  return dataArray;
};

/**
 * 获取科研项目列表（统一接口）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjects = async (req, res) => {
  try {
    const { 
      name, 
      level, // 前端传递的是level而不是levelId
      levelId, // 保留levelId以兼容其他代码
      type, 
      leader, // leader参数现在用于submitterId查询
      submitterId, // 直接支持submitterId参数
      startDate, 
      endDate, 
      approvalStartDate,
      approvalEndDate,
      userId, 
      page = 1, 
      pageSize = 10,
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'all'(全部), 'reviewed'(已审核), 'rejected'(拒绝), 'pending'(待审核)
      isExport = false // 是否是导出操作
    } = req.body;
    console.log("userId===",userId);
    
    // 构建查询条件
    const where = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      where.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null;
    }
    
    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }
    
    // 使用level或levelId参数，优先使用level（与前端匹配）
    if (level) {
      where.levelId = level;
    } else if (levelId) {
      where.levelId = levelId;
    }
    
    if (type) {
      where.type = type;
    }
    
    // 初始化include配置
    const includeConfig = [
      {
        model: userModel,
        as: 'submitter',
        attributes: ['id', 'nickname', 'username', 'studentNumber'],
        required: false,
      },
      {
        model: researchProjectParticipantsModel,
        as: 'participants',
        include: [
          {
            model: userModel,
            as: 'user',
            attributes: ['id', 'nickname', 'username', 'studentNumber'],
            required: false,
          }
        ],
        required: false,
      }
    ];
    
    // 处理负责人查询 - 两种情况：ID查询或名称模糊查询
    if (submitterId) {
      // 如果提供了确切的submitterId，直接精确查询
      where.submitterId = submitterId;
    } else if (leader) {
      // 检查leader是否是UUID格式
      if (leader.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        // 是UUID，直接用于精确查询
        where.submitterId = leader;
      } else {
        // 不是UUID，进行名称模糊查询
        // 修改submitter的include设置，添加名称过滤条件
        includeConfig[0] = {
          model: userModel,
          as: 'submitter',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: true, // 重要：需要inner join才能正确过滤
          where: {
            [Op.or]: [
              { nickname: { [Op.like]: `%${leader}%` } },
              { username: { [Op.like]: `%${leader}%` } },
              { studentNumber: { [Op.like]: `%${leader}%` } }
            ]
          }
        };
      }
    }
    
    if (startDate && endDate) {
      where.startDate = { 
        [Op.gte]: startDate,
        [Op.lte]: endDate
      };
    } else if (startDate) {
      where.startDate = { [Op.gte]: startDate };
    } else if (endDate) {
      where.startDate = { [Op.lte]: endDate };
    }
    
    if (approvalStartDate && approvalEndDate) {
      where.approvalDate = { 
        [Op.gte]: approvalStartDate,
        [Op.lte]: approvalEndDate
      };
    } else if (approvalStartDate) {
      where.approvalDate = { [Op.gte]: approvalStartDate };
    } else if (approvalEndDate) {
      where.approvalDate = { [Op.lte]: approvalEndDate };
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("researchProjects");
    
    // 将range过滤条件添加到数据库查询中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        where.approvalDate = {
          ...where.approvalDate, // 保留原有的approvalDate条件
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        where[Op.or] = [
          { approvalDate: { [Op.lt]: intervalStartTime } },
          { approvalDate: { [Op.gt]: intervalEndTime } }
        ];
      }
    }
    
    // 如果提供了userId，添加用户参与关系的查询条件
    if (userId) {
      // 修复查询方式，使用两个独立的查询然后合并结果
      const participantProjects = await researchProjectParticipantsModel.findAll({
        where: { userId },
        attributes: ['projectId'],
        raw: true
      });
      
      const projectIds = participantProjects.map(p => p.projectId);
      
      // 修改查询条件为：是提交者或项目ID在参与者列表中
      const userCondition = [
        { submitterId: userId },
        { id: { [Op.in]: projectIds } }
      ];
      
      // 如果之前已经设置了Or条件（针对range=out的情况），则需要合并条件
      if (where[Op.or]) {
        // 需要使用AND将range条件和userId条件组合
        where[Op.and] = [
          { [Op.or]: where[Op.or] },  // 之前的条件
          { [Op.or]: userCondition }   // userId条件
        ];
        delete where[Op.or];  // 删除原来的条件，因为已经移到AND中了
      } else {
        where[Op.or] = userCondition;
      }
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    console.log('查询条件:', where);
    console.log('分页参数:', { page: pageNum, pageSize: pageSizeNum, offset, limit, isExport });
    console.log('负责人查询:', leader ? (typeof leader === 'string' ? `模糊搜索: ${leader}` : `ID搜索: ${leader}`) : '无');
    
    // 设置查询选项，根据是否导出决定是否应用分页
    const queryOptions = {
      where,
      order: [['approvalDate', 'DESC']], // 按批准日期降序排序
      include: includeConfig,
      distinct: true, // 使用distinct确保正确计数（特别是有多个关联时）
    };
    
    // 只有在不是导出操作时才应用分页限制
    if (!isExport) {
      queryOptions.offset = offset;
      queryOptions.limit = limit;
      queryOptions.order = [['createdAt', 'DESC']];
    }
    
    // 查询数据
    let { count, rows } = await researchProjectModel.findAndCountAll(queryOptions);
    
    // 处理每个项目的成员信息
    const processedProjects = [];
    
    for (const project of rows) {
      const projectJson = project.toJSON();
      
      // 添加是否在统计时间范围内的标记（用于前端展示，但不再用于筛选）
      let isInTimeRange = false;
      if (timeInterval && projectJson.approvalDate) {
        const projectStartDate = new Date(projectJson.approvalDate);
        const intervalStartTime = new Date(timeInterval.startTime);
        const intervalEndTime = new Date(timeInterval.endTime);
        
        isInTimeRange = (projectStartDate >= intervalStartTime && projectStartDate <= intervalEndTime);
      }
      
      projectJson.isInTimeRange = isInTimeRange;
      
      // 处理项目参与者信息
      if (projectJson.participants && projectJson.participants.length > 0) {
        // 找出项目负责人
        const leader = projectJson.participants.find(p => p.isLeader);
        if (leader) {
          projectJson.leader = leader.user;
          projectJson.leaderId = leader.userId;
        }
        
        // 处理所有参与者信息
        projectJson.memberDetails = projectJson.participants.map(p => ({
          id: p.userId,
          name: p.user ? (p.user.nickname || p.user.username) : '未知用户',
          allocationRatio: p.allocationRatio,
          participantRank: p.participantRank,
          isLeader: p.isLeader
        }));
      }
      
      // 不再根据range参数过滤，因为已经在数据库查询中处理了
      processedProjects.push(projectJson);
    }
    
    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: processedProjects,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count, // 使用数据库返回的总记录数
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取科研项目列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取科研项目列表失败',
      error: error.message
    });
  }
};

/**
 * 获取项目详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    // 查询项目详情
    const project = await researchProjectModel.findByPk(id, {
      include: [
        {
          model: researchProjectParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'username', 'nickname', 'studentNumber'],
              required: false,
            }
          ],
          required: false,
        },
        {
          model: userModel,
          as: 'submitter',
          attributes: ['id', 'username', 'nickname', 'studentNumber'],
          required: false,
        },
        {
          model: researchProjectsLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        }
      ]
    });
    
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '未找到科研项目',
        data: null
      });
    }
    
    // 将项目数据转换为JSON对象，方便后续处理
    const projectData = project.toJSON();
    
    // 处理项目参与者信息
    if (projectData.participants && projectData.participants.length > 0) {
      // 找出项目负责人
      const leader = projectData.participants.find(p => p.isLeader);
      if (leader) {
        projectData.leader = leader.user;
      }
      
      // 处理所有参与者信息
      projectData.memberDetails = projectData.participants.map(p => ({
        id: p.id,
        userId: p.userId,
        user: p.user,
        allocationRatio: p.allocationRatio,
        participantRank: p.participantRank,
        isLeader: p.isLeader
      }));
      
      // 创建ID到用户信息的映射，方便前端使用
      const memberMap = {};
      projectData.participants.forEach(p => {
        if (p.user) {
          memberMap[p.userId] = p.user;
        }
      });
      projectData.memberMap = memberMap;
    }
    
    // 添加可读的审核状态描述
    projectData.reviewStatusDesc = getReviewStatusDesc(projectData.ifReviewer);
    
    // 查询项目关联的文件列表
    const files = await fileModel.findAll({
      where: {
        projectId: id,
        relatedId: id
      },
      attributes: [
        'id', 
        'fileName', 
        'originalName', 
        'filePath', 
        'fileSize', 
        'mimeType', 
        'extension',
        'uploaderId', 
        'relatedId', 
        'relatedType', 
        'createdAt', 
        'updatedAt'
      ],
      order: [['createdAt', 'DESC']]
    });
    
    // 处理文件信息，添加URL和其他前端需要的信息
    projectData.attachments = files.map(file => {
      const fileData = file.toJSON();
      // 构造文件URL
      const filePath = fileData.filePath;
      const url = filePath.startsWith('/') ? filePath : `/${filePath}`;
      
      return {
        id: fileData.id,
        name: fileData.originalName,
        fileName: fileData.fileName,
        size: fileData.fileSize,
        type: fileData.mimeType,
        extension: fileData.extension,
        url: url,
        filePath: fileData.filePath,
        uploadTime: fileData.createdAt
      };
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: projectData
    });
  } catch (error) {
    console.error('获取科研项目详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取科研项目详情失败',
      error: error.message
    });
  }
};

// 辅助函数：获取可读的审核状态描述
function getReviewStatusDesc(status) {
  if (status === 1) {
    return '已通过';
  } else if (status === 0) {
    return '已拒绝';
  } else {
    return '待审核';
  }
}

/**
 * 创建科研项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createProject = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(researchProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const {
      projectId,
      name,
      levelId,
      type,
      projectIssuingDepartment,
      fundingAmount,
      isUniversityFirstUnit,
      isCollegeFirstUnit,
      submitterId,
      startDate,
      approvalDate,
      endDate,
      description,
      remark,
      status,
      userId,
      userAllocationProportion,
      members,
      fileIds, // 接收前端传递的文件ID数组
      attachmentUrl // 接收前端传递的文件路径
    } = req.body;
    console.log('创建科研项目请求参数:', req.query); 
    console.log('创建科研项目请求参数body:', req.body); 
    console.log(name, levelId, type, projectIssuingDepartment, fundingAmount, isUniversityFirstUnit, isCollegeFirstUnit, submitterId, startDate, approvalDate, endDate, description, remark, status, userId, userAllocationProportion, members);

    // 验证必要字段
    if (!name || !levelId || !type || !projectIssuingDepartment || !fundingAmount || 
        !userId || !userAllocationProportion || !submitterId) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        msg: '缺少必要字段'
      });
    }
    
    // 验证分配比例
    let totalProportion = parseFloat(userAllocationProportion) || 0;
    let membersList = [];
    
    if (members) {
      try {
        if (typeof members === 'string') {
          membersList = JSON.parse(members);
        } else {
          membersList = members;
        }
        
        if (Array.isArray(membersList)) {
          membersList.forEach(member => {
            if (member.allocationProportion) {
              totalProportion += parseFloat(member.allocationProportion) || 0;
            }
          });
        }
      } catch (error) {
        console.error('解析成员列表出错', error);
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          msg: '成员数据格式不正确'
        });
      }
    }
    
    // 验证总比例是否约等于1
    if (Math.abs(totalProportion - 1) > 0.01) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        msg: `参与人员分配比例总和必须为1(当前: ${totalProportion})`
      });
    }
    
    // 获取项目级别信息，用于计算得分
    const projectLevel = await researchProjectsLevelsModel.findByPk(levelId, { transaction });
    if (!projectLevel) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        msg: '未找到指定的项目级别'
      });
    }
    
    // 创建科研项目
    const newProject = await researchProjectModel.create({
      projectId,
      name,
      levelId,
      submitterId,
      type,
      projectIssuingDepartment,
      fundingAmount,
      isUniversityFirstUnit: isUniversityFirstUnit === '1' || isUniversityFirstUnit === 1 || isUniversityFirstUnit === '是' || isUniversityFirstUnit === true ? true : false,
      isCollegeFirstUnit: isCollegeFirstUnit === '1' || isCollegeFirstUnit === 1 || isCollegeFirstUnit === '是' || isCollegeFirstUnit === true ? true : false,
      startDate,
      approvalDate,
      endDate,
      description,
      remark, // 添加备注字段
      status: status ? parseInt(status) : 1, // 添加状态字段，确保是数字类型
      score: projectLevel.score || 0,
      // 初始设置为null，稍后使用项目ID更新
      attachmentUrl: null
    }, { transaction });
    
    // 添加成员关联 - 先添加负责人
    await researchProjectParticipantsModel.create({
      projectId: newProject.id,
      userId: userId,
      allocationRatio: userAllocationProportion, // 使用allocationRatio字段名
      isLeader: true, // 设置为负责人
      participantRank: 1 // 第一作者排名为1
    }, { transaction });
    
    // 添加其他成员
    if (membersList && membersList.length > 0) {
      const projectMembers = membersList.map((member, index) => ({
        projectId: newProject.id,
        userId: member.id,
        allocationRatio: member.allocationProportion, // 使用allocationRatio字段名
        isLeader: false, // 非负责人
        participantRank: member.participantRank || index + 2 // 使用传入的排名或按顺序分配，从2开始
      }));
      
      await researchProjectParticipantsModel.bulkCreate(projectMembers, { transaction });
    }
    
    // 处理关联文件 - 使用前端传递的文件ID而非重新创建文件记录
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组，可能以JSON字符串形式传递
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl，可能以JSON字符串形式传递
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl === 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
    
      
      // 如果有文件ID，关联到项目
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            projectId: newProject.id, // 设置文件的projectId为新创建的项目ID
            relatedId: newProject.id,
            relatedType: 'research_projects' // 保持relatedType字段的兼容性
          };
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath; // 使用filePath字段，这是文件模型中的正确字段名
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );

          processedFileIds.push(fileId);
        }

      }
    } else {
      console.log('未提供文件ID，跳过文件关联处理');
    }
    
    // 提交事务
    await transaction.commit();

    // 事务提交成功后，异步移动文件到指定的项目目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'research_projects'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${newProject.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 新增变量跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: newProject.id,
                relatedId: newProject.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
              
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新项目的attachmentUrl为项目文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的项目文件夹路径，确保不包含文件名
            const projectFolderPath = `uploads\\research_projects\\${newProject.id}\\`;
            
            await newProject.update({
              attachmentUrl: projectFolderPath
            });
            console.log(`已更新项目 ${newProject.id} 的attachmentUrl为: ${projectFolderPath}`);
            
          } catch (updateError) {
            console.error('更新项目attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响项目创建的返回结果，仅记录错误
        console.error('移动文件到项目目录失败:', moveError);
      }
    }
    
    return res.status(201).json({
      code: 200,
      msg: '科研项目创建成功',
      data: newProject
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('创建科研项目时发生错误:', error);
    return res.status(500).json({
      code: 500,
      msg: '创建科研项目失败: ' + error.message
    });
  }
};

/**
 * 更新科研项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateProject = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(researchProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    console.log('更新科研项目请求参数:', req.params); 
    const userInfo = await getUserInfoFromRequest(req);

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    const { 
      projectId,
      name, 
      levelId, 
      type, 
      projectIssuingDepartment,
      startDate, 
      approvalDate,
      endDate, 
      fundingAmount,
      isUniversityFirstUnit,
      isCollegeFirstUnit,
      userId, // 负责人ID
      userAllocationProportion, // 负责人分配比例
      members, // 其他成员JSON字符串
      description, 
      remark,
      attachmentUrl,
      status,
      submitterId,
      fileIds // 添加文件ID参数
    } = req.body;
    console.log('更新科研项目请求参数:', req.body); 
    console.log('更新科研项目请求参数:', req.query); 
    
    // 查询项目是否存在
    const project = await researchProjectModel.findByPk(id, {
      include: [
        {
          model: researchProjectParticipantsModel,
          as: 'participants',
          required: false
        },
        {
          model: researchProjectsLevelsModel,
          as: 'level',
          required: false
        }
      ],
      transaction
    });
    console.log("project===============",project);
    
    if (!project) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到科研项目',
        data: null
      });
    }
    
    // 查找当前用户是否是项目负责人或提交者
    const isProjectOwner = await researchProjectParticipantsModel.findOne({
      where: {
        projectId: id,
        userId: userInfo.id,
        isLeader: true
      },
      transaction
    }) || project.submitterId === userInfo.id;
    
    // 权限检查：只有管理员或项目负责人/提交者可以编辑项目
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth) && !isProjectOwner) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限编辑该项目，只能编辑自己负责的项目',
        data: null
      });
    }
    
    // 如果项目已审核，需要更新用户排名
    if (project.ifReviewer == 1) {
      try {
        console.log('开始更新用户排名数据，项目ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("researchProjects");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断项目变更前后是否在时间区间内（使用approvalDate判断）
        const oldApprovalDate = project.approvalDate;
        const newApprovalDate = approvalDate || project.approvalDate;
        console.log('项目审批日期 - 原始:', oldApprovalDate, '新:', newApprovalDate);
        
        const wasInTimeRange = timeInterval ? 
          isProjectInTimeRange(oldApprovalDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
          
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(newApprovalDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('项目时间范围状态 - 原始:', wasInTimeRange ? '在范围内' : '不在范围内', 
                   '变更后:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表（针对减分操作）
        let oldRankingTables = [];
        if (wasInTimeRange) {
          oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        // 确定需要更新的排名表（针对加分操作）
        let newRankingTables = [];
        if (isInTimeRange) {
          newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('排名表 - 原始:', oldRankingTables, '新:', newRankingTables);
        
        // 获取旧的级别分数
        const oldBaseScore = project.level ? project.level.score : 0;
        console.log('原始级别分数:', oldBaseScore);
        
        // 获取新的级别分数
        let newBaseScore = oldBaseScore;
        if (levelId && levelId !== project.levelId) {
          const newLevel = await researchProjectsLevelsModel.findByPk(levelId, { transaction });
          if (newLevel) {
            newBaseScore = newLevel.score || 0;
          }
        }
        console.log('新级别分数:', newBaseScore);
        
        // 获取旧的参与者名单
        const oldParticipants = project.participants || [];
        console.log('原始参与者数量:', oldParticipants.length);
        
        // 准备新的参与者列表
        let newParticipants = [];
        
        // 如果提供了新的负责人和成员信息，解析它们
        if (userId && userAllocationProportion) {
          // 添加负责人
          newParticipants.push({
            userId: userId,
            allocationRatio: parseFloat(userAllocationProportion) || 0,
            isLeader: true
          });
          
          // 添加其他成员
          if (members) {
            let membersList = [];
            try {
              if (typeof members === 'string') {
                membersList = JSON.parse(members);
              } else {
                membersList = members;
              }
              
              if (Array.isArray(membersList) && membersList.length > 0) {
                for (const member of membersList) {
                  newParticipants.push({
                    userId: member.id,
                    allocationRatio: parseFloat(member.allocationProportion) || 0,
                    isLeader: false
                  });
                }
              }
            } catch (error) {
              console.error('解析成员列表出错', error);
            }
          }
        } 
        // 使用提供的participants数组
        else if (req.body.participants && Array.isArray(req.body.participants)) {
          newParticipants = req.body.participants.map(p => ({
            userId: p.userId,
            allocationRatio: parseFloat(p.allocationRatio) || 0,
            isLeader: p.isLeader || false
          }));
        }
        console.log('新参与者数量:', newParticipants.length);
        
        // 从原始参与者中找出要删除的参与者 - 他们在旧列表中但不在新列表中
        const oldUserIds = oldParticipants.map(p => p.userId);
        const newUserIds = newParticipants.map(p => p.userId);
        
        // 找出要删除的参与者
        const deletedUserIds = oldUserIds.filter(id => !newUserIds.includes(id));
        console.log('要删除的参与者:', deletedUserIds);
        
        // 找出保留的参与者
        const keptUserIds = oldUserIds.filter(id => newUserIds.includes(id));
        console.log('保留的参与者:', keptUserIds);
        
        // 找出新增的参与者
        const addedUserIds = newUserIds.filter(id => !oldUserIds.includes(id));
        console.log('新增的参与者:', addedUserIds);
        
        // 1. 处理要删除的参与者 - 减少项目数量和分数
        if (deletedUserIds.length > 0) {
          const deletedParticipants = oldParticipants.filter(p => deletedUserIds.includes(p.userId));
          console.log('被删除的参与者完整数据:', JSON.stringify(deletedParticipants));
          
          const deletedIds = [];
          const deletedRatios = [];
          const deletedScores = [];
          const countDeltas = []; // 固定值为1的数组表示每人减少1个项目
          
          for (const participant of deletedParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            deletedIds.push(userId);
            deletedRatios.push(ratio);
            deletedScores.push(participantScore);
            countDeltas.push(1); // 每个被删除的参与者项目数-1
          }
          
          console.log('被删除参与者的排名更新数据:', {
            userIds: deletedIds,
            countDeltas: countDeltas,
            scores: deletedScores
          });
          
          // 对每个排名表执行减分操作
          for (const table of oldRankingTables) {
            console.log(`为被删除的参与者更新排名表 ${table}`);
            await updateUserRankings(
              deletedIds,
              table,
              'researchProjects',
              countDeltas, // 使用固定值1表示项目计数-1
              deletedScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去被删除参与者的分数和项目数');
        }
        
        // 2. 处理保留但分配比例变化的参与者 - 先减去原有分数，后面再加上新分数
        if (keptUserIds.length > 0) {
          const keptOldParticipants = oldParticipants.filter(p => keptUserIds.includes(p.userId));
          console.log('保留的参与者原始数据:', JSON.stringify(keptOldParticipants));
          
          const keptIds = [];
          const keptRatios = [];
          const keptScores = [];
          
          for (const participant of keptOldParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            keptIds.push(userId);
            keptRatios.push(ratio);
            keptScores.push(participantScore);
          }
          
          console.log('保留参与者的减分数据:', {
            userIds: keptIds,
            scores: keptScores
          });
          
          // 减去原有分数（但不减少项目计数）
          for (const table of oldRankingTables) {
            console.log(`为保留的参与者减去原有分数：${table}`);
            // 传递0表示不改变项目计数，只改变分数
            const zeroCounts = Array(keptIds.length).fill(0);
            await updateUserRankings(
              keptIds,
              table,
              'researchProjects',
              zeroCounts, // 项目计数不变
              keptScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去保留参与者的原有分数');
        }
        
        // 3. 处理所有新参与者名单（包括保留的和新增的）- 增加分数，对新增的也增加项目数
        if (newParticipants.length > 0) {
          console.log('新参与者完整数据:', JSON.stringify(newParticipants));
          
          const allNewIds = [];
          const allNewRatios = [];
          const allNewScores = [];
          const allCountDeltas = []; // 对新增参与者设为1，对保留的参与者设为0
          
          for (const participant of newParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = newBaseScore * ratio;
            
            allNewIds.push(userId);
            allNewRatios.push(ratio);
            allNewScores.push(participantScore);
            
            // 对新增参与者项目数+1，对保留的参与者项目数不变
            if (addedUserIds.includes(userId)) {
              allCountDeltas.push(1);
            } else {
              allCountDeltas.push(0);
            }
          }
          
          console.log('所有新参与者的加分数据:', {
            userIds: allNewIds,
            countDeltas: allCountDeltas,
            scores: allNewScores
          });
          
          // 为所有参与者添加分数，但只为新增参与者增加项目计数
          for (const table of newRankingTables) {
            console.log(`为所有参与者更新排名表：${table}`);
            await updateUserRankings(
              allNewIds,
              table,
              'researchProjects',
              allCountDeltas, // 使用差异化的计数更新：新增的+1，保留的不变
              allNewScores,
              transaction,
              "add" // 加分操作
            );
          }
          console.log('成功更新所有参与者的分数和新增参与者的项目数');
        }
        
        console.log('成功完成科研项目参与者的排名数据更新');
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 获取项目级别信息，用于更新得分
    let score = project.score;
    if (levelId && levelId !== project.levelId) {
      const projectLevel = await researchProjectsLevelsModel.findByPk(levelId, { transaction });
      if (projectLevel) {
        score = projectLevel.score || 0;
      }
    }
    
    // 更新项目基本信息
    const updateData = {};
    if (projectId !== undefined) updateData.projectId = projectId;
    if (name !== undefined) updateData.name = name;
    if (levelId !== undefined) updateData.levelId = levelId;
    if (type !== undefined) updateData.type = type;
    if (projectIssuingDepartment !== undefined) updateData.projectIssuingDepartment = projectIssuingDepartment;
    if (startDate !== undefined) updateData.startDate = startDate;
    if (approvalDate !== undefined) updateData.approvalDate = approvalDate;
    if (endDate !== undefined) updateData.endDate = endDate;
    if (fundingAmount !== undefined) updateData.fundingAmount = fundingAmount;
    if (isUniversityFirstUnit !== undefined) updateData.isUniversityFirstUnit = isUniversityFirstUnit;
    if (isCollegeFirstUnit !== undefined) updateData.isCollegeFirstUnit = isCollegeFirstUnit;
    if (description !== undefined) updateData.description = description;
    if (remark !== undefined) updateData.remark = remark;
    // 设置标准化的附件文件夹路径
    updateData.attachmentUrl = `uploads\\research_projects\\${id}\\`;
    if (status !== undefined) updateData.status = status;
    if (submitterId !== undefined) updateData.submitterId = submitterId;
    if (score !== undefined) updateData.score = score;
    
    await project.update(updateData, { transaction });
    
    // 处理参与者信息
    // 如果提供了新的负责人和成员信息，则重新设置所有参与者
    if (userId && userAllocationProportion) {
      // 首先删除所有现有参与者
      await researchProjectParticipantsModel.destroy({
        where: { projectId: id },
        transaction
      });
      
      // 添加新的负责人
      await researchProjectParticipantsModel.create({
        projectId: id,
        userId: userId,
        allocationRatio: userAllocationProportion, // 使用allocationRatio字段
        isLeader: true, // 设置为负责人
        participantRank: 1 // 第一作者排名为1
      }, { transaction });
      
      // 添加其他成员
      let membersList = [];
      if (members) {
        try {
          if (typeof members === 'string') {
            membersList = JSON.parse(members);
          } else {
            membersList = members;
          }
          
          if (Array.isArray(membersList) && membersList.length > 0) {
            const projectMembers = membersList.map((member, index) => ({
              projectId: id,
              userId: member.id,
              allocationRatio: member.allocationProportion, // 使用allocationRatio字段
              isLeader: false, // 非负责人
              participantRank: member.participantRank || index + 2 // 使用传入的排名或按顺序分配，从2开始
            }));
            
            await researchProjectParticipantsModel.bulkCreate(projectMembers, { transaction });
          }
        } catch (error) {
          console.error('解析成员列表出错', error);
          await transaction.rollback();
          return res.status(400).json({
            code: 400,
            message: '成员数据格式不正确',
            error: error.message
          });
        }
      }
    } else if (req.body.participants && Array.isArray(req.body.participants)) {
      // 使用提供的participants数组更新参与者
      const participants = req.body.participants;
      
      // 获取当前项目的所有参与者
      const currentParticipants = project.participants || [];
      
      // 删除不在新列表中的参与者
      const newParticipantUserIds = participants.map(p => p.userId);
      
      for (const currentParticipant of currentParticipants) {
        if (!newParticipantUserIds.includes(currentParticipant.userId)) {
          await currentParticipant.destroy({ transaction });
        }
      }
      
      // 更新或创建新的参与者
      for (const participant of participants) {
        const { userId, allocationRatio, participantRank, isLeader } = participant;
        
        // 查找是否已存在该参与者
        const existingParticipant = currentParticipants.find(p => p.userId === userId);
        
        if (existingParticipant) {
          // 更新现有参与者
          await existingParticipant.update({
            allocationRatio,
            participantRank: participantRank || null,
            isLeader: isLeader || false
          }, { transaction });
        } else {
          // 创建新参与者
          await researchProjectParticipantsModel.create({
            projectId: id,
            userId,
            allocationRatio,
            participantRank: participantRank || null,
            isLeader: isLeader || false
          }, { transaction });
        }
      }
    }
    
    // 处理文件关联 - 确保考虑fileIds参数
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组，可能以JSON字符串形式传递
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl，可能以JSON字符串形式传递
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl === 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
      
      // 如果有文件ID，更新关联关系
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            projectId: project.id,
            relatedId: project.id,
            relatedType: 'research_projects'
          };
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath;
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );
          
          processedFileIds.push(fileId);
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，异步移动文件到指定的项目目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'research_projects'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${project.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: project.id,
                relatedId: project.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新项目的attachmentUrl为项目文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的项目文件夹路径更新项目的attachmentUrl，确保不包含文件名
            const projectFolderPath = `uploads\\research_projects\\${project.id}\\`;
            
            await project.update({
              attachmentUrl: projectFolderPath
            });
            console.log(`已更新项目 ${project.id} 的attachmentUrl为: ${projectFolderPath}`);
          } catch (updateError) {
            console.error('更新项目attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响项目更新的返回结果，仅记录错误
        console.error('移动文件到项目目录失败:', moveError);
      }
    }
    
    // 重新加载数据返回最新数据
    const updatedProject = await researchProjectModel.findByPk(id, {
      include: [
        {
          model: researchProjectParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'username', 'nickname', 'studentNumber'],
              required: false,
            }
          ],
          required: false,
        },
        {
          model: userModel,
          as: 'submitter',
          attributes: ['id', 'username', 'nickname', 'studentNumber'],
          required: false,
        }
      ]
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: updatedProject
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('更新科研项目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新科研项目失败',
      error: error.message
    });
  }
};

/**
 * 删除科研项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteProject = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(researchProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    const userInfo = await getUserInfoFromRequest(req);

    // 查询项目是否存在
    const project = await researchProjectModel.findByPk(id, { 
      include: [
        {
          model: researchProjectParticipantsModel,
          as: 'participants',
          required: false
        },
        {
          model: researchProjectsLevelsModel,
          as: 'level',
          required: false
        }
      ],
      transaction 
    });
    
    if (!project) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到科研项目',
        data: null
      });
    }
    
    // 查找当前用户是否是项目负责人或提交者
    const isProjectOwner = await researchProjectParticipantsModel.findOne({
      where: {
        projectId: id,
        userId: userInfo.id,
        isLeader: true
      },
      transaction
    }) || project.submitterId === userInfo.id;
    
    // 权限检查：只有管理员或项目负责人/提交者可以删除项目
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth) && !isProjectOwner) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限删除该项目，只能删除自己负责的项目',
        data: null
      });
    }

    // 如果项目已审核，需要更新用户排名
    if (project.ifReviewer == 1) {
      try {
        console.log('开始处理删除项目的排名更新，项目ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("researchProjects");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断项目是否在时间区间内（使用approvalDate判断）
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(project.approvalDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('项目时间范围状态:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('需要更新的排名表:', rankingTables);
        
        // 获取项目的级别分数
        const baseScore = project.level ? project.level.score : 0;
        console.log('项目级别分数:', baseScore);
        
        // 获取项目的所有参与者
        const participants = project.participants || [];
        console.log('项目参与者数量:', participants.length);
        
        // 如果有参与者，批量处理减分操作
        if (participants.length > 0) {
          // 准备批量更新的数据
          const userIds = [];
          const scores = [];
          
          // 收集所有参与者数据
          for (const participant of participants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = baseScore * ratio;
            
            userIds.push(userId);
            scores.push(participantScore);
          }
          
          console.log('删除项目的参与者数据:', {
            userIds: userIds,
            scores: scores
          });
          
          // 创建固定值为1的计数数组，表示每个用户项目数-1
          const countDeltas = Array(userIds.length).fill(1);
          
          // 对每个排名表执行减分操作（一次性批量处理所有参与者）
          for (const table of rankingTables) {
            console.log(`为被删除项目的所有参与者更新排名表 ${table}`);
            await updateUserRankings(
              userIds,
              table,
              'researchProjects',
              countDeltas, // 使用固定值1表示项目计数-1
              scores, // 使用参与者分数数组
              transaction,
              "subtract" // 减分操作
            );
          }
          
          console.log('成功从排名表中减去科研项目参与者的分数和项目数');
        }
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 删除项目相关的参与者记录
    await researchProjectParticipantsModel.destroy({
      where: { projectId: id },
      transaction
    });
    
    // 处理项目附件 - 检查是否有附件模型并进行处理
    try {
      const projectFiles = await fileModel.findAll({
        where: { 
          projectId: id
        },
        transaction
      });
      
      // 收集项目文件夹路径
      let projectFolderPaths = new Set();
      
      if (projectFiles && projectFiles.length > 0) {
        console.log(`找到${projectFiles.length}个与项目关联的文件记录`);
        
        // 从文件路径中提取项目文件夹路径
        for (const file of projectFiles) {
          if (file.filePath) {
            // 从filePath中提取项目文件夹路径
            const folderPath = file.filePath.substring(0, file.filePath.lastIndexOf('/'));
            if (folderPath.includes('research_projects')) {
              projectFolderPaths.add(folderPath);
            }
          }
          
          // 将文件标记为已删除
          await file.destroy({ transaction });
        }
      } else {
        console.log('未找到与项目关联的文件记录');
      }
      
    } catch (attachmentError) {
      console.warn('处理项目文件时出错:', attachmentError);
      // 继续执行，不因附件删除失败而中断整个操作
    }
    // 删除项目
    await project.destroy({ transaction });
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，执行文件系统删除操作（这些操作不能回滚，所以放在事务之外）
    try {
      // 直接使用项目ID构造项目文件夹路径
      const projectFolderPath = `uploads/research_projects/${id}`;
      console.log(`尝试删除项目文件夹: ${projectFolderPath}`);
      await fileController.deleteDirectoryUtil(projectFolderPath);
    } catch (fsError) {
      console.warn('删除文件系统中的文件时出错:', fsError);
      // 数据库事务已提交成功，文件系统操作失败不影响API返回结果
    }
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('删除科研项目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除科研项目失败',
      error: error.message
    });
  }
};

/**
 * 导出科研项目数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportProjects = async (req, res) => {
  try {
    const { name, levelId, type, submitterId, startDate, endDate } = req.query;
    
    // 构建查询条件
    const where = {};
    
    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }
    
    if (levelId) {
      where.levelId = levelId;
    }
    
    if (type) {
      where.type = type;
    }
    
    if (submitterId) {
      where.submitterId = submitterId;
    }
    
    if (startDate) {
      where.startDate = { [Op.gte]: startDate };
    }
    
    if (endDate) {
      where.endDate = { [Op.lte]: endDate };
    }
    
    console.log('导出查询条件:', where);
    
    // 查询数据
    const projects = await researchProjectModel.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });
    
    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('科研项目列表');
    
    // 定义表头
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: '项目名称', key: 'name', width: 30 },
      { header: '项目级别', key: 'levelId', width: 15 },
      { header: '项目类型', key: 'type', width: 15 },
      { header: '开始日期', key: 'startDate', width: 15 },
      { header: '结束日期', key: 'endDate', width: 15 },
      { header: '负责人', key: 'submitterId', width: 15 },
      { header: '成员', key: 'participants', width: 30 },
      { header: '描述', key: 'description', width: 30 },
      { header: '分数', key: 'fundingAmount', width: 10 },
      { header: '状态', key: 'status', width: 10 },
      { header: '创建时间', key: 'createdAt', width: 20 },
      { header: '更新时间', key: 'updatedAt', width: 20 }
    ];
    
    // 格式化项目级别
    const formatLevel = (levelId) => {
      const levelMap = {
        'international': '国际级',
        'national_major': '国家重点',
        'national_general': '国家一般',
        'provincial_major': '省部重点',
        'provincial_general': '省部一般',
        'municipal': '市厅级',
        'school': '校级'
      };
      return levelMap[levelId] || levelId;
    };
    
    // 格式化项目类型
    const formatType = (type) => {
      const typeMap = {
        'national_fund': '国家自然科学基金',
        'social_science': '社会科学基金',
        'enterprise_cooperation': '企业合作项目',
        'international_cooperation': '国际合作项目',
        'university_project': '校级项目',
        'other': '其他类型'
      };
      return typeMap[type] || type;
    };
    
    // 格式化状态
    const formatStatus = (status) => {
      return status === 1 ? '正常' : '已结项';
    };
    
    // 添加数据行
    projects.forEach(project => {
      worksheet.addRow({
        id: project.id,
        name: project.name,
        levelId: formatLevel(project.levelId),
        type: formatType(project.type),
        startDate: project.startDate,
        endDate: project.endDate,
        submitterId: project.submitterId,
        participants: project.participants,
        description: project.description,
        fundingAmount: project.fundingAmount,
        status: formatStatus(project.status),
        createdAt: project.createdAt,
        updatedAt: project.updatedAt
      });
    });
    
    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=research-projects.xlsx');
    
    // 将工作簿写入响应
    await workbook.xlsx.write(res);
    
    // 结束响应
    res.end();
  } catch (error) {
    console.error('导出科研项目数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出科研项目数据失败',
      error: error.message
    });
  }
};

/**
 * 导入科研项目数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importProjects = async (req, res) => {
  try {
    // 检查是否存在文件
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未上传文件',
        data: null
      });
    }
    
    // 读取上传的Excel文件
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(req.file.path);
    const worksheet = workbook.getWorksheet(1);
    
    // 准备结果统计
    const result = {
      total: 0,
      success: 0,
      failed: 0,
      errors: []
    };
    
    // 获取表头映射
    const headers = {};
    worksheet.getRow(1).eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value;
    });
    
    // 处理每一行数据
    const promises = [];
    
    worksheet.eachRow((row, rowNumber) => {
      // 跳过表头
      if (rowNumber === 1) return;
      
      result.total++;
      
      const projectData = {};
      row.eachCell((cell, colNumber) => {
        const fieldName = headers[colNumber];
        if (fieldName) {
          // 根据字段名称设置对应的数据
          switch(fieldName) {
            case '项目名称':
              projectData.name = cell.value;
              break;
            case '项目级别':
              projectData.levelId = mapLevelToKey(cell.value);
              break;
            case '项目类型':
              projectData.type = mapTypeToKey(cell.value);
              break;
            case '开始日期':
              projectData.startDate = cell.value;
              break;
            case '结束日期':
              projectData.endDate = cell.value;
              break;
            case '负责人':
              projectData.submitterId = cell.value;
              break;
            case '成员':
              // 不再直接设置participants字段
              // 将成员信息临时保存，稍后处理
              projectData.membersInfo = cell.value;
              break;
            case '描述':
              projectData.description = cell.value;
              break;
            case '分数':
              projectData.fundingAmount = parseFloat(cell.value) || 0;
              break;
            case '状态':
              projectData.status = cell.value === '正常' ? 1 : 0;
              break;
            default:
              break;
          }
        }
      });
      
      // 验证必填字段
      if (!projectData.name) {
        result.failed++;
        result.errors.push({
          row: rowNumber,
          message: '项目名称不能为空'
        });
        return;
      }
      
      if (!projectData.levelId) {
        result.failed++;
        result.errors.push({
          row: rowNumber,
          message: '项目级别无效'
        });
        return;
      }
      
      if (!projectData.type) {
        result.failed++;
        result.errors.push({
          row: rowNumber,
          message: '项目类型无效'
        });
        return;
      }
      
      if (!projectData.startDate) {
        result.failed++;
        result.errors.push({
          row: rowNumber,
          message: '开始日期不能为空'
        });
        return;
      }
      
      if (!projectData.endDate) {
        result.failed++;
        result.errors.push({
          row: rowNumber,
          message: '结束日期不能为空'
        });
        return;
      }
      
      if (!projectData.submitterId) {
        result.failed++;
        result.errors.push({
          row: rowNumber,
          message: '项目负责人不能为空'
        });
        return;
      }
      
      // 创建项目
      const promise = researchProjectModel.create(projectData)
        .then(async (createdProject) => {
          try {
            // 如果有成员信息，创建项目参与者关系
            if (projectData.membersInfo) {
              const members = projectData.membersInfo.split(',').map(m => m.trim()).filter(m => m);
              
              // 查找成员对应的用户ID
              for (const memberName of members) {
                // 查找用户
                const user = await userModel.findOne({ 
                  where: { 
                    [Op.or]: [
                      { nickname: memberName },
                      { username: memberName }
                    ] 
                  } 
                });
                
                if (user) {
                  // 创建参与者记录，默认分配比例为均分
                  await researchProjectParticipantsModel.create({
                    id: uuidv4(),
                    projectId: createdProject.id,
                    userId: user.id,
                    allocationRatio: 1.0 / (members.length + 1), // 均分比例
                    participantRank: members.indexOf(memberName) + 1,
                    isLeader: false
                  });
                }
              }
              
              // 设置项目负责人
              if (projectData.submitterId) {
                // 查找负责人
                const leader = await userModel.findOne({
                  where: { 
                    [Op.or]: [
                      { nickname: projectData.submitterId },
                      { username: projectData.submitterId }
                    ] 
                  }
                });
                
                if (leader) {
                  // 更新项目的提交者ID
                  await createdProject.update({ submitterId: leader.id });
                  
                  // 创建负责人参与记录
                  await researchProjectParticipantsModel.create({
                    id: uuidv4(),
                    projectId: createdProject.id,
                    userId: leader.id,
                    allocationRatio: 1.0 / (members.length + 1), // 均分比例
                    participantRank: 0,
                    isLeader: true
                  });
                }
              }
            }
            
            result.success++;
          } catch (participantError) {
            console.error('添加项目参与者失败:', participantError);
            result.failures++;
            result.errors.push({
              row: rowNumber,
              message: '创建项目成功，但添加参与者失败: ' + participantError.message
            });
          }
        })
        .catch(error => {
          result.failed++;
          result.errors.push({
            row: rowNumber,
            message: error.message
          });
        });
      
      promises.push(promise);
    });
    
    // 等待所有项目创建完成
    await Promise.all(promises);
    
    // 删除临时文件
    fs.unlinkSync(req.file.path);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: result
    });
  } catch (error) {
    console.error('导入科研项目数据失败:', error);
    
    // 如果文件存在，删除临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    return res.status(500).json({
      code: 500,
      message: '导入科研项目数据失败',
      error: error.message
    });
  }
};

// 辅助函数：将显示值映射为键值
function mapLevelToKey(displayValue) {
  const levelMap = {
    '国际级': 'international',
    '国家重点': 'national_major',
    '国家一般': 'national_general',
    '省部重点': 'provincial_major',
    '省部一般': 'provincial_general',
    '市厅级': 'municipal',
    '校级': 'school'
  };
  return levelMap[displayValue] || displayValue;
}

function mapTypeToKey(displayValue) {
  const typeMap = {
    '国家自然科学基金': 'national_fund',
    '社会科学基金': 'social_science',
    '企业合作项目': 'enterprise_cooperation',
    '国际合作项目': 'international_cooperation',
    '校级项目': 'university_project',
    '其他类型': 'other'
  };
  return typeMap[displayValue] || displayValue;
}

/**
 * 获取项目级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body; // 添加reviewStatus参数
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("researchProjects");
    const userInfo = await getUserInfoFromRequest(req);
    
    // 获取所有项目级别，用于后续将levelId映射到级别名称
    const allLevels = await researchProjectsLevelsModel.findAll();
    const levelIdToNameMap = {};
    allLevels.forEach(level => {
      levelIdToNameMap[level.id] = level.levelName;
    });
    
    // 条件构建
    const whereCondition = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }
    
    // 添加时间范围过滤条件到数据库查询中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        whereCondition.approvalDate = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { approvalDate: { [Op.lt]: intervalStartTime } },
          { approvalDate: { [Op.gt]: intervalEndTime } }
        ];
      }
    }
    
    // 查询所有项目
    let projects = null;
    
    // 如果传入了特定的userId参数，则通过关联表查询该用户参与的项目
    if (userId) {
      // 使用关联查询，通过参与者表查找相关项目
      projects = await researchProjectModel.findAll({
        where: whereCondition, // 添加审核状态筛选
        include: [
          {
            model: researchProjectParticipantsModel,
            as: 'participants',
            where: { userId: userId },
            required: true
          }
        ]
      });
    } else {
      // 如果没有指定userId，则查询所有项目
      projects = await researchProjectModel.findAll({
        where: whereCondition // 添加审核状态筛选
      });
    }
    
    // 初始化级别数据
    const levelData = {};
    
    // 统计项目级别分布
    projects.forEach(project => {
      const projectJson = project.toJSON();
      const levelId = projectJson.levelId;
      // 使用级别名称而不是ID
      const levelName = levelIdToNameMap[levelId] || '未知级别';
      levelData[levelName] = (levelData[levelName] || 0) + 1;
    });
    
    // 转换为前端期望的格式：[{name, value}]
    const result = Object.entries(levelData).map(([name, value]) => ({ name, value }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取项目级别分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目级别分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取项目类型分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTypeDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body; // 添加reviewStatus参数
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("researchProjects");

    const userInfo = await getUserInfoFromRequest(req);
    
    // 条件构建
    const whereCondition = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }
    
    // 添加时间范围过滤条件到数据库查询中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        whereCondition.approvalDate = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { approvalDate: { [Op.lt]: intervalStartTime } },
          { approvalDate: { [Op.gt]: intervalEndTime } }
        ];
      }
    }
    
    // 查询所有项目
    let projects = null;
    
    // 如果传入了特定的userId参数，则通过关联表查询该用户参与的项目
    if (userId) {
      // 使用关联查询，通过参与者表查找相关项目
      projects = await researchProjectModel.findAll({
        where: whereCondition, // 添加审核状态筛选
        include: [
          {
            model: researchProjectParticipantsModel,
            as: 'participants',
            where: { userId: userId },
            required: true
          }
        ]
      });
    } else {
      // 如果没有指定userId，则查询所有项目
      projects = await researchProjectModel.findAll({
        where: whereCondition // 添加审核状态筛选
      });
    }
    
    // 初始化类型数据
    const typeData = {};
    
    // 统计项目类型分布
    projects.forEach(project => {
      const projectJson = project.toJSON();
      const typeName = projectJson.type;
      typeData[typeName] = (typeData[typeName] || 0) + 1;
    });
    
    // 转换为前端期望的格式：[{name, value}]
    const result = Object.entries(typeData).map(([name, value]) => ({ name, value }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取项目类型分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目类型分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取项目时间分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTimeDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body; // 添加reviewStatus参数
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("researchProjects");

    const userInfo = await getUserInfoFromRequest(req);
    
    // 条件构建
    const whereCondition = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }
    
    // 添加时间范围过滤条件到数据库查询中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        whereCondition.approvalDate = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { approvalDate: { [Op.lt]: intervalStartTime } },
          { approvalDate: { [Op.gt]: intervalEndTime } }
        ];
      }
    }
    
    // 查询所有项目
    let projects = null;
    
    // 如果传入了特定的userId参数，则通过关联表查询该用户参与的项目
    if (userId) {
      // 使用关联查询，通过参与者表查找相关项目
      projects = await researchProjectModel.findAll({
        where: whereCondition, // 添加审核状态筛选
        include: [
          {
            model: researchProjectParticipantsModel,
            as: 'participants',
            where: { userId: userId },
            required: true
          }
        ]
      });
    } else {
      // 如果没有指定userId，则查询所有项目
      projects = await researchProjectModel.findAll({
        where: whereCondition // 添加审核状态筛选
      });
    }
    
    // 初始化时间数据
    const timeData = {};
    
    // 统计项目时间分布
    projects.forEach(project => {
      const projectJson = project.toJSON();
      
      if (projectJson.approvalDate) {
        try {
          // 提取年月，格式：YYYY-MM
          const month = projectJson.approvalDate.substring(0, 7);
          
          if (month && month.length >= 7) {
            timeData[month] = (timeData[month] || 0) + 1;
          }
        } catch (error) {
          console.error('项目时间解析错误:', projectJson.approvalDate, error);
        }
      }
    });
    
    // 对时间进行排序
    const sortedMonths = Object.keys(timeData).sort();
    const monthsData = [];
    const dataValues = [];
    
    sortedMonths.forEach(month => {
      monthsData.push(month);
      dataValues.push(timeData[month]);
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        months: monthsData,
        data: dataValues
      }
    });
  } catch (error) {
    console.error('获取项目时间分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目时间分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取项目得分分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getScoreDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body; // 添加reviewStatus参数
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("researchProjects");

    const userInfo = await getUserInfoFromRequest(req);
    
    // 条件构建
    const whereCondition = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }
    
    // 添加时间范围过滤条件到数据库查询中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        whereCondition.approvalDate = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { approvalDate: { [Op.lt]: intervalStartTime } },
          { approvalDate: { [Op.gt]: intervalEndTime } }
        ];
      }
    }
    
    // 查询所有项目
    let projects = null;
    
    // 如果传入了特定的userId参数，则通过关联表查询该用户参与的项目
    if (userId) {
      // 使用关联查询，通过参与者表查找相关项目
      projects = await researchProjectModel.findAll({
        where: whereCondition, // 添加审核状态筛选
        include: [
          {
            model: researchProjectParticipantsModel,
            as: 'participants',
            where: { userId: userId },
            required: true
          }
        ]
      });
    } else {
      // 如果没有指定userId，则查询所有项目
      projects = await researchProjectModel.findAll({
        where: whereCondition // 添加审核状态筛选
      });
    }
    
    // 定义分数范围
    const scoreRanges = [
      { min: 0, max: 50, name: '0-50分' },
      { min: 51, max: 100, name: '51-100分' },
      { min: 101, max: 150, name: '101-150分' },
      { min: 151, max: 200, name: '151-200分' },
      { min: 201, max: 250, name: '201-250分' }
    ];
    
    // 初始化分数分布数据
    const scoreData = scoreRanges.map(range => ({
      name: range.name,
      value: 0
    }));
    
    // 统计项目分数分布
    projects.forEach(project => {
      const projectJson = project.toJSON();
      const score = parseFloat(projectJson.fundingAmount) || 0;
      
      // 找到对应的分数范围
      for (let i = 0; i < scoreRanges.length; i++) {
        if (score > scoreRanges[i].min && score <= scoreRanges[i].max) {
          scoreData[i].value += 1;
          break;
        }
      }
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: scoreData
    });
  } catch (error) {
    console.error('获取项目得分分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目得分分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取项目负责人得分排名数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLeaderRanking = async (req, res) => {
  try {
    const { range = 'all', page = 1, limit = 10, userId, reviewStatus = 'all', is_export_all, nick_name } = req.body;
    
    // 查找数据库连接，通过已有模型获取sequelize实例
    let sequelize;
    if (researchProjectModel.sequelize) {
      sequelize = researchProjectModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }
    
    // 调用存储过程 - 使用与getAllUsersTotalScore相同的存储过程
    const results = await sequelize.query(
      'CALL get_research_project_ranking(?, ?, ?, ?, ?, ?, ?)',
      {
        replacements: [
          range || 'all',
          reviewStatus || 'all', 
          limit, // 设置一个足够大的pageSize
          page,    // 第一页
          is_export_all || 0,    // 导出全部
          '',   // projectId 不筛选
          nick_name || ''    // nickname 不筛选 
        ],
        type: sequelize.QueryTypes.RAW,
        raw: true,
        nest: true
      }
    );
    
    // 处理查询结果
    let rankingData = [];
    if (typeof convertStoredProcResultToArray === 'function') {
      rankingData = convertStoredProcResultToArray(results, true, true);
    } else {
      if (Array.isArray(results) && results.length > 0 && Array.isArray(results[0])) {
        rankingData = results[0];
      } else if (Array.isArray(results) && results.length > 0) {
        rankingData = results;
      }
    }
    
    // 如果有userId参数，过滤只保留该用户
    if (userId) {
      rankingData = rankingData.filter(item => item.userId === userId);
    }
    
    // 转换为原始getLeaderRanking函数的输出格式
    const formattedResults = rankingData.map(item => ({
      id: item.userId,
      name: item.nickName || item.userId,
      score: parseFloat(item.totalScore || 0)
    }));
    
    // 按分数降序排序
    formattedResults.sort((a, b) => b.score - a.score);
    
    // 限制返回数量
    const result = formattedResults.slice(0, limit);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取项目负责人得分排名数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目负责人得分排名数据失败',
      error: error.message
    });
  }
};

/**
 * 判断项目是否在时间范围内
 * @param {string} projectDate - 项目日期
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isProjectInTimeRange(projectDate, startDate, endDate) {
  if (!projectDate || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const projectDateObj = new Date(projectDate);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 项目日期必须在开始日期和结束日期之间
  return projectDateObj >= startDateObj && projectDateObj <= endDateObj;
}

/**
 * 获取用户总得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserTotalScore = async (req, res) => {
  try {
    const { 
      userId, 
      range = 'all',
      page = 1, 
      pageSize = 10 
    } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '请提供用户ID',
        data: null
      });
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    console.log('获取用户总得分，分页参数:', { page: pageNum, pageSize: pageSizeNum, offset, limit });
    
    // 获取时间区间 - 使用自定义时间范围或默认区间
    let startDate, endDate;
    
    if (req.body.timeRange && req.body.timeRange.startDate && req.body.timeRange.endDate) {
      // 使用前端传入的自定义时间范围
      startDate = req.body.timeRange.startDate;
      endDate = req.body.timeRange.endDate;
      console.log('使用自定义时间范围:', startDate, '至', endDate);
    } else {
      // 使用系统默认的时间区间
      const timeInterval = await getTimeIntervalByName("researchProjects");
      startDate = timeInterval ? timeInterval.startTime : null;
      endDate = timeInterval ? timeInterval.endTime : null;
      console.log('使用默认时间范围:', startDate, '至', endDate);
    }
    
    // 构建查询条件
    const whereCondition = {};
    
    // 添加时间范围过滤条件到数据库查询中
    if (startDate && endDate && range !== 'all') {
      const intervalStartTime = new Date(startDate);
      const intervalEndTime = new Date(endDate);
      
      if (range === 'in') {
        // 在时间范围内
        whereCondition.approvalDate = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { approvalDate: { [Op.lt]: intervalStartTime } },
          { approvalDate: { [Op.gt]: intervalEndTime } }
        ];
      }
    }
    
    // 查询用户参与的所有项目
    const projects = await researchProjectModel.findAll({
      where: whereCondition,
      include: [
        {
          model: userModel,
          as: 'submitter',
          attributes: ['id', 'nickname', 'username'],
          required: false,
        },
        {
          model: researchProjectParticipantsModel,
          as: 'participants',
          where: { userId: userId },
          required: false
        }
      ],
    });
    
    // 跟踪用户的总分和项目详情
    let totalScore = 0;
    const projectDetails = [];
    
    // 统计每个项目中用户的得分
    projects.forEach(project => {
      const projectJson = project.toJSON();
      
      // 项目得分
      const projectScore = parseFloat(projectJson.fundingAmount) || 0;
      
      // 项目信息，稍后添加用户得分
      const projectDetail = {
        id: projectJson.id,
        name: projectJson.name,
        levelId: projectJson.levelId, 
        type: projectJson.type,
        startDate: projectJson.startDate,
        endDate: projectJson.endDate,
        totalScore: projectScore,
        userScore: 0, // 用户在该项目中的得分
        role: '', // 用户在项目中的角色
        allocationRatio: 0 // 用户的分配比例
      };
      
      // 检查用户是否为项目负责人
      if (projectJson.submitterId === userId) {
        // 如果用户是负责人
        let userAllocation = 0;
        
        // 尝试获取负责人的分配比例
        if (projectJson.participants && projectJson.participants.length > 0) {
          const leader = projectJson.participants.find(p => p.isLeader);
          if (leader) {
            userAllocation = leader.allocationRatio || 0;
          }
        }
        
        // 计算用户作为负责人的得分
        const scoreForUser = userAllocation > 0 ? 
          projectScore * userAllocation : projectScore;
        
        totalScore += scoreForUser;
        projectDetail.userScore = scoreForUser;
        projectDetail.role = 'leader';
        projectDetail.allocationRatio = userAllocation > 0 ? userAllocation : 1;
        
        projectDetails.push(projectDetail);
      } else {
        // 检查用户是否为项目成员
        if (projectJson.participants && projectJson.participants.length > 0) {
          // 解析成员ID
          const memberIds = projectJson.participants.map(p => p.userId);
          
          // 寻找用户在成员列表中的索引
          const memberIndex = memberIds.indexOf(userId);
          
          if (memberIndex !== -1) {
            // 用户是该项目的成员
            const userAllocation = projectJson.participants[memberIndex].allocationRatio || 0;
            
            // 计算用户作为成员的得分
            const scoreForUser = projectScore * userAllocation;
            
            totalScore += scoreForUser;
            projectDetail.userScore = scoreForUser;
            projectDetail.role = 'member';
            projectDetail.allocationRatio = userAllocation;
            
            projectDetails.push(projectDetail);
          }
        }
      }
    });
    
    // 按用户得分降序排序项目
    projectDetails.sort((a, b) => b.userScore - a.userScore);
    
    // 计算总项目数
    const totalProjects = projectDetails.length;
    
    // 应用分页
    const pagedProjects = projectDetails.slice(offset, offset + limit);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        userId: userId,
        totalScore: totalScore,
        list: pagedProjects,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: totalProjects,
          totalPages: Math.ceil(totalProjects / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户总得分失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户总得分失败',
      error: error.message
    });
  }
};

/**
 * 获取所有用户科研项目总分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllUsersTotalScore = async (req, res) => {
  try {
    const { 
      range = 'all',
      page = 1, 
      pageSize = 10,
      sortField = 'totalScore',
      sortOrder = 'desc',
      projectId = '',
      nickname = '',
      reviewStatus = 'all'
    } = req.body;
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const isExport = req.body.isExport || false;
    
    // 根据range选择对应的排名表模型
    let RankingModel;
    switch(range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }
    
    // 查询条件：按科研项目总分降序排序
    const queryOptions = {
      order: [['researchScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'researchCount',
        'researchScore'
      ]
    };
    
    // 如果提供了昵称，添加筛选条件
    if (nickname) {
      queryOptions.where = {
        nickName: {
          [Op.like]: `%${nickname}%`
        }
      };
    }
    
    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }
    
    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);
    
    // 格式化返回数据
    const formattedResults = rows.map((item, index) => ({
      rank: index + 1 + offset,
      userId: item.userId,
      nickname: item.nickName,
      studentNumber: item.studentNumber,
      projectCount: parseInt(item.researchCount || 0),
      leaderProjectCount: 0, // 数据库中没有这个字段，设为默认值
      totalScore: parseFloat(item.researchScore || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: formattedResults,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取所有用户总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有用户总分统计失败',
      error: error.message
    });
  }
};

/**
 * 审核科研项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewProject = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(researchProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id, reviewStatus, reviewComment, reviewer } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    if (reviewStatus === undefined) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少审核状态',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找项目
    const project = await researchProjectModel.findByPk(id, { transaction });
    
    if (!project) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }
    
    // 权限检查 - 只有管理员可以审核
    const hasPermission = userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER';
    
    if (!hasPermission) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核项目',
        data: null
      });
    }
    
    // 更新审核状态
    // ifReviewer: null/undefined=待审核, 0=拒绝, 1=同意审核
    const updateData = {
      ifReviewer: reviewStatus,
      reviewedAt: new Date(),
      reviewerId: reviewer || userInfo.id
    };
    
    // 添加审核意见（如果有）
    if (reviewComment !== undefined) {
      updateData.reviewComment = reviewComment;
    }
    
    await project.update(updateData, { transaction });
    
    // 项目审核通过后，更新用户排名数据
    if (reviewStatus === 1) {
      try {
        // 获取项目级别对应的分数
        const projectLevel = await researchProjectsLevelsModel.findByPk(project.levelId, { transaction });
        if (!projectLevel) {
          console.error(`未找到项目级别信息，levelId: ${project.levelId}`);
          throw new Error('未找到项目级别信息');
        }
        
        const baseScore = projectLevel.score || 0;
        
        // 获取项目所有参与者及其分配比例
        const participants = await researchProjectParticipantsModel.findAll({
          where: { projectId: id },
          transaction
        });
        
        if (participants && participants.length > 0) {
          // 准备用户ID数组和得分数组
          const participantUserIds = [];
          const participantScores = [];
          
          // 计算每个参与者的得分
          for (const participant of participants) {
            const userId = participant.userId;
            const allocationRatio = parseFloat(participant.allocationRatio) || 0;
            
            // 计算该参与者应得的分数 = 项目基础分 * 分配比例
            const userScore = baseScore * allocationRatio;
            participantUserIds.push(userId);
            participantScores.push(userScore);
          }
          
          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("researchProjects");
          
          // 判断项目是否在时间区间内
          const isInTimeRange = timeInterval && project.approvalDate ? 
            isProjectInTimeRange(project.approvalDate, timeInterval.startTime, timeInterval.endTime) : 
            false;
          
          // 根据项目是否在时间区间内，更新不同的排名表
          let rankingTables = [];
          
          if (isInTimeRange) {
            // 在区间内：更新范围内表和全部表
            rankingTables = [
              'user_ranking_reviewed_in', 
              'user_ranking_reviewed_all'
            ];
          } else {
            // 不在区间内：更新范围外表和全部表
            rankingTables = [
              'user_ranking_reviewed_out', 
              'user_ranking_reviewed_all'
            ];
          }
          
          try {
            for (const table of rankingTables) {
              // 更新所有参与者的排名数据：每人计数+1，分数增加各自的得分
              await updateUserRankings(
                participantUserIds,          // 所有参与者的用户ID数组
                table,                       // 表名
                'researchProjects',           // 类型名
                Array(participantUserIds.length).fill(1), // 每个参与者计数+1
                participantScores,           // 每个参与者的得分数组
                transaction,                 // 传递事务对象
                "add"                        // 操作类型：加分
              );
            }
          } catch (rankingError) {
            console.error('更新排名表失败:', rankingError);
            // 对于数据完整性错误，应当回滚事务
            if (rankingError.name === 'SequelizeDatabaseError') {
              await transaction.rollback();
              throw new Error(`更新排名失败: ${rankingError.message}`);
            } else {
              // 对于其他非致命错误，记录但不中断流程
              console.warn(`排名更新出现非致命错误: ${rankingError.message}`);
            }
          }
        } else {
          console.log(`项目ID ${id} 没有参与者，无需更新排名`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 检查是否已经回滚，如果没有则回滚事务
        if (!error.message || !error.message.includes('更新排名失败')) {
          await transaction.rollback();
          throw error; // 将错误向上传播
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('审核项目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '审核失败',
      error: error.message
    });
  }
};

/**
 * 获取用户科研项目详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserProjectDetails = async (req, res) => {
  try {
    const { 
      userId, 
      range = 'all',
      reviewStatus = 'reviewed',
      page = 1, 
      pageSize = 10 
    } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '请提供用户ID',
        data: null
      });
    }
    
    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    
    // 验证用户权限 - TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1' && userId !== userInfo.id) {
      return res.status(403).json({
        code: 403,
        message: '权限不足，您只能查看自己的项目详情',
        data: null
      });
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    let startDate, endDate;
    
    if (req.body.timeRange && req.body.timeRange.startDate && req.body.timeRange.endDate) {
      // 使用前端传入的自定义时间范围
      startDate = req.body.timeRange.startDate;
      endDate = req.body.timeRange.endDate;
      console.log('使用自定义时间范围:', startDate, '至', endDate);
    } else {
      // 使用系统默认的时间区间
      const timeInterval = await getTimeIntervalByName("researchProjects");
      startDate = timeInterval ? timeInterval.startTime : null;
      endDate = timeInterval ? timeInterval.endTime : null;
      console.log('使用默认时间范围:', startDate, '至', endDate);
    }
    
    // 构建查询条件
    const whereCondition = {};
    
    // 添加时间范围过滤条件到数据库查询中
    if (startDate && endDate && range !== 'all') {
      const intervalStartTime = new Date(startDate);
      const intervalEndTime = new Date(endDate);
      
      if (range === 'in') {
        // 在时间范围内
        whereCondition.approvalDate = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { approvalDate: { [Op.lt]: intervalStartTime } },
          { approvalDate: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    // 审核状态筛选
    if (reviewStatus !== 'all') {
      if (reviewStatus === 'reviewed') {
        whereCondition.ifReviewer = 1;
      } else if (reviewStatus === 'rejected') {
        whereCondition.ifReviewer = 0;
      } else if (reviewStatus === 'pending') {
        whereCondition.ifReviewer = null;
      }
    }
    
    // 首先查询用户参与的项目ID列表
    const participantProjects = await researchProjectParticipantsModel.findAll({
      where: { userId },
      attributes: ['projectId', 'allocationRatio', 'isLeader'],
      raw: true
    });

    const projectIds = participantProjects.map(p => p.projectId);

    if (projectIds.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          userId,
          projects: [],
          pagination: {
            page: pageNum,
            pageSize: pageSizeNum,
            total: 0,
            totalPages: 0
          }
        }
      });
    }

    // 添加项目ID筛选条件，确保只查询用户参与的项目
    whereCondition.id = { [Op.in]: projectIds };

    // 查询用户参与的项目
    const userProjects = await researchProjectModel.findAll({
      where: whereCondition,
      include: [
        {
          model: userModel,
          as: 'submitter',
          attributes: ['id', 'nickname', 'username', 'roleId'],
          required: false,
        },
        {
          model: researchProjectsLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false
        }
      ]
    });

    const projectDetails = [];

    // 处理查询结果
    for (const project of userProjects) {
      const projectJson = project.toJSON();

      // 查找用户在该项目中的参与信息
      const participation = participantProjects.find(p => p.projectId === project.id);

      if (participation) {
        const userRole = participation.isLeader ? 'leader' : 'member';
        const allocationRatio = parseFloat(participation.allocationRatio || 0);

        // 计算项目总分数（使用项目级别的分数）
        const projectScore = parseFloat(projectJson.level?.score || 0);

        // 计算用户在项目中的得分
        const userScore = projectScore * allocationRatio;

        // 获取评审信息 - 直接使用ifReviewer字段
        let reviewStatus = 'pending';
        let reviewOpinion = projectJson.reviewComment || '';

        if (projectJson.ifReviewer == 1) {
          reviewStatus = 'reviewed';
        } else if (projectJson.ifReviewer == 0) {
          reviewStatus = 'rejected';
        }

        // 创建项目详情对象
        const projectDetail = {
          id: projectJson.id,
          name: projectJson.name,
          levelId: projectJson.levelId,
          type: projectJson.type,
          startDate: projectJson.startDate,
          endDate: projectJson.endDate,
          approvalDate: projectJson.approvalDate,
          totalScore: projectScore,
          userRole,
          allocationRatio: allocationRatio.toFixed(2),
          userScore: parseFloat(userScore.toFixed(2)),
          reviewStatus,
          reviewOpinion,
          memberCount: 1 // 简化为1，因为我们已经通过参与者表过滤了
        };

        projectDetails.push(projectDetail);
      }
    }
    
    // 按用户得分降序排序项目
    projectDetails.sort((a, b) => b.userScore - a.userScore);
    
    // 获取总项目数
    const totalProjects = projectDetails.length;
    
    // 应用分页
    const pagedProjects = projectDetails.slice(offset, offset + limit);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        userId,
        projects: pagedProjects,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: totalProjects,
          totalPages: Math.ceil(totalProjects / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户项目详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户项目详情失败',
      error: error.message
    });
  }
};

/**
 * 获取科研项目总分统计（按类型和总体）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectsTotalScore = async (req, res) => {
  try {
    const { 
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
    } = req.body;
    
    console.log("getProjectsTotalScore被调用，参数:", req.body);
    

    // 获取数据库连接实例
    let sequelize;
    if (researchProjectModel.sequelize) {
      sequelize = researchProjectModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }
    
    let userId = null;

    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    const results = await sequelize.query(
      'CALL get_research_projects_total_score(?, ?, ?)',
      {
        replacements: [
          range || 'all',
          reviewStatus || 'all',
          userId || null
        ],
        type: sequelize.QueryTypes.RAW
      }
    );
    
    console.log("存储过程返回结果:", JSON.stringify(results));
    
    // 处理查询结果
    let typeStats = [];
    let totalStats = { totalProjects: 0, totalScore: 0 };
    let timeInterval = null;
    
    // 分析和处理返回的结果结构
    if (Array.isArray(results)) {
      // 如果结果是扁平数组（没有嵌套）且包含type和typeName字段，则这是类型统计
      if (results.length > 0 && results[0] && typeof results[0].type === 'string') {
        typeStats = results.map(item => ({
          ...item,
          count: parseInt(item.count || 0),
          totalScore: parseFloat(item.totalScore || 0)
        }));
      } 
      // 如果结果是嵌套数组，则按照预期的三个结果集处理
      else if (results.length > 0) {
        // 第一个结果集：类型统计
        if (Array.isArray(results[0])) {
          typeStats = results[0].map(item => ({
            ...item,
            count: parseInt(item.count || 0),
            totalScore: parseFloat(item.totalScore || 0)
          }));
        }
        
        // 第二个结果集：总体统计
        if (results.length > 1 && Array.isArray(results[1]) && results[1].length > 0) {
          const totalStatsData = results[1][0];
          if (totalStatsData) {
            totalStats = {
              totalProjects: parseInt(totalStatsData.totalProjects || 0),
              totalScore: parseFloat(totalStatsData.totalScore || 0)
            };
          }
        }
        
        // 第三个结果集：时间区间
        if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
          const timeData = results[2][0];
          if (timeData) {
            timeInterval = {
              startTime: timeData.startTime,
              endTime: timeData.endTime,
              name: timeData.name
            };
          }
        }
      }
    }
    
    // 如果有类型统计但没有总体统计，则计算总体统计
    if (typeStats.length > 0 && (totalStats.totalProjects === 0 || totalStats.totalScore === 0)) {
      totalStats = {
        totalProjects: typeStats.reduce((sum, item) => sum + (item.count || 0), 0),
        totalScore: parseFloat(typeStats.reduce((sum, item) => sum + (item.totalScore || 0), 0).toFixed(2))
      };
    }
    
    // 如果没有从存储过程获得时间区间，则从数据库直接获取
    if (!timeInterval) {
      try {
        const timeIntervalData = await getTimeIntervalByName("researchProjects");
        if (timeIntervalData) {
          timeInterval = {
            startTime: timeIntervalData.startTime,
            endTime: timeIntervalData.endTime,
            name: timeIntervalData.name
          };
        }
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        typeStats: typeStats,
        totalStats: totalStats,
        timeInterval: timeInterval,
        userId: userId // 仍然返回userId，但当前查询结果不会基于userId过滤
      }
    });
  } catch (error) {
    console.error('获取科研项目总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取科研项目总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户科研项目详情列表及得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserProjectsDetail = async (req, res) => {
  try {
    const { 
      userId,                // 用户ID - 必填
      range = 'all',        // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      pageSize = 10,        // 每页记录数
      pageNum = 1           // 当前页码
    } = req.body;
    console.log("getUserProjectsDetail被调用，参数:", JSON.stringify({userId, range, reviewStatus, pageSize, pageNum}));  
    // 验证必填参数
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：userId',
        data: null
      });
    }
    
    // 获取数据库连接实例
    let sequelize;
    if (researchProjectModel.sequelize) {
      sequelize = researchProjectModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }
    
    // 调用存储过程
    const results = await sequelize.query(
      'CALL get_user_research_projects_detail(?, ?, ?, ?, ?)',
      {
        replacements: [
          userId,
          range || 'all',
          reviewStatus || 'all',
          pageSize,
          pageNum
        ],
        type: sequelize.QueryTypes.RAW
      }
    );
    
    console.log("存储过程返回结果结构:", {
      resultsType: typeof results,
      isArray: Array.isArray(results),
      length: results.length,
      firstElementType: results.length > 0 ? typeof results[0] : 'none',
      isFirstElementArray: results.length > 0 ? Array.isArray(results[0]) : false,
      firstElementLength: results.length > 0 && Array.isArray(results[0]) ? results[0].length : 0
    });
    
    // 处理查询结果
    let totalCount = 0;
    let projectsList = [];
    let statsData = { totalProjects: 0, leaderProjectCount: 0, totalScore: 0 };
    let timeInterval = null;
    
    // 分析和处理返回的结果结构
    if (Array.isArray(results) && results.length > 0) {
      // 第一个结果集: 总记录数
      if (results[0] && Array.isArray(results[0]) && results[0].length > 0) {
        totalCount = parseInt(results[0][0].totalCount || 0);
      }
      
      // 第二个结果集: 项目列表
      if (results.length > 1 && Array.isArray(results[1])) {
        projectsList = results[1].map(item => ({
          ...item,
          baseScore: parseFloat(item.baseScore || 0),
          actualScore: parseFloat(item.actualScore || 0),
          allocationRatio: parseFloat(item.allocationRatio || 0),
          fundingAmount: parseFloat(item.fundingAmount || 0)
        }));
      }
      
      // 第三个结果集: 统计数据
      if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
        const stats = results[2][0];
        statsData = {
          totalProjects: parseInt(stats.totalProjects || 0),
          leaderProjectCount: parseInt(stats.leaderProjectCount || 0),
          totalScore: parseFloat(stats.totalScore || 0)
        };
      }
      
      // 第四个结果集: 时间区间
      if (results.length > 3 && Array.isArray(results[3]) && results[3].length > 0) {
        const timeData = results[3][0];
        timeInterval = {
          startTime: timeData.startTime,
          endTime: timeData.endTime,
          name: timeData.name
        };
      }
    }
    
    // 如果没有从存储过程获得时间区间，则从数据库直接获取
    if (!timeInterval) {
      try {
        const timeIntervalData = await getTimeIntervalByName("researchProjects");
        if (timeIntervalData) {
          timeInterval = {
            startTime: timeIntervalData.startTime,
            endTime: timeIntervalData.endTime,
            name: timeIntervalData.name
          };
        }
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalCount,
        pageSize,
        pageNum,
        projects: projectsList,
        stats: statsData,
        timeInterval,
        userId
      }
    });
  } catch (error) {
    console.error('获取用户科研项目详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户科研项目详情失败',
      error: error.message
    });
  }
};

/**
 * 重新提交科研项目审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapplyProject = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(researchProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找项目
    const project = await researchProjectModel.findByPk(id, { transaction });
    
    if (!project) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }
    
    // 检查项目所有权或管理员权限
    const isOwner = project.userId === userInfo.id;
    const isAdmin = userInfo.role.roleAuth === 'TEACHER-LV1' || userInfo.role.roleAuth === 'SUPER' || userInfo.role.roleAuth === 'ADMIN-LV2';
    
    if (!isOwner && !isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限重新提交该项目',
        data: null
      });
    }
    
    // 检查当前审核状态，只有被拒绝的项目可以重新提交
    if (project.ifReviewer != 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '只有被拒绝的项目可以重新提交审核',
        data: null
      });
    }
    
    // 更新项目状态为待审核
    await project.update({
      ifReviewer: null,  // 设置为待审核状态
      reviewComment: null, // 清空之前的审核意见
      reviewerId: null // 清空之前的审核人
    }, { transaction });
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();

    console.error('重新提交审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '重新提交审核失败',
      error: error.message
    });
  }
};

/**
 * 获取审核状态概览
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReviewStatusOverview = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("researchProjects");
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const whereCondition = {};

    // 时间范围筛选
    if (range === 'in' && timeInterval) {
      whereCondition.approvalDate = {
        [Op.between]: [timeInterval.startTime, timeInterval.endTime]
      };
    } else if (range === 'out' && timeInterval) {
      whereCondition.approvalDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }

    // 构建用户筛选条件
    let userCondition = {};
    if (userId) {
      userCondition = {
        include: [{
          model: researchProjectParticipantsModel,
          as: 'participants',
          where: { userId: userId },
          required: true
        }]
      };
    } else if (userInfo.role.roleAuth === 'TEACHER-LV1') {
      userCondition = {
        include: [{
          model: researchProjectParticipantsModel,
          as: 'participants',
          where: { userId: userInfo.id },
          required: true
        }]
      };
    }

    // 查询各状态数量
    const [reviewed, pending, rejected] = await Promise.all([
      researchProjectModel.count({
        where: { ...whereCondition, ifReviewer: 1 },
        ...userCondition
      }),
      researchProjectModel.count({
        where: { ...whereCondition, ifReviewer: null },
        ...userCondition
      }),
      researchProjectModel.count({
        where: { ...whereCondition, ifReviewer: 0 },
        ...userCondition
      })
    ]);

    const total = reviewed + pending + rejected;
    const reviewedRate = total > 0 ? ((reviewed / total) * 100).toFixed(1) : 0;

    return res.status(200).json({
      code: 200,
      message: '获取审核状态数据成功',
      data: {
        reviewed,
        pending,
        rejected,
        total,
        reviewedRate: parseFloat(reviewedRate)
      }
    });
  } catch (error) {
    console.error('获取审核状态概览失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取审核状态概览失败',
      error: error.message
    });
  }
};

