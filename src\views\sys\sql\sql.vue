<template>
  <div class="sql-ranking-container">
    <a-card title="排名管理" :bordered="false">
      <a-tabs v-model:activeKey="activeTab">
        <!-- 更新排名缓存表选项卡 -->
        <a-tab-pane key="updateTables" tab="更新排名缓存表">
          <div class="operation-section">
            <a-alert
              class="mb-4"
              type="info"
              show-icon
              message="更新排名缓存操作将重新计算所有用户的排名数据，可能需要较长时间，请谨慎操作。"
            />
            <a-button 
              type="primary" 
              :loading="updateTablesLoading" 
              @click="handleUpdateRankingTables"
            >
              更新排名缓存表
            </a-button>
          </div>

          <a-divider />

          <div v-if="updateTablesResult" class="result-section">
            <a-alert
              :type="updateTablesResult.success ? 'success' : 'error'"
              show-icon
              :message="updateTablesResult.message"
            />
            <div v-if="updateTablesResult.success" class="result-details mt-4">
              <a-descriptions title="更新结果" bordered>
                <a-descriptions-item label="更新时间">
                  {{ updateTablesResult.data.updateTime }}
                </a-descriptions-item>
                <a-descriptions-item label="更新表" :span="2">
                  <a-tag v-for="table in updateTablesResult.data.tables" :key="table" color="blue">
                    {{ table }}
                  </a-tag>
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>
        </a-tab-pane>

        <!-- 更新指定用户排名选项卡 -->
        <a-tab-pane key="updateUser" tab="更新用户排名">
          <a-form
            ref="updateUserForm"
            :model="updateUserForm"
            :rules="updateUserFormRules"
            layout="vertical"
          >
            <a-form-item label="选择表" name="tableName">
              <a-select v-model:value="updateUserForm.tableName" placeholder="请选择排名表">
                <a-select-option value="user_ranking_reviewed_in">范围内已审核</a-select-option>
                <a-select-option value="user_ranking_reviewed_out">范围外已审核</a-select-option>
                <a-select-option value="user_ranking_reviewed_all">所有已审核</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="用户ID" name="userIds">
              <a-select
                v-model:value="updateUserForm.userIds"
                mode="multiple"
                placeholder="请选择要更新的用户"
                :options="userOptions"
                :loading="usersLoading"
                @search="handleUserSearch"
              ></a-select>
            </a-form-item>

            <a-form-item label="数据类型" name="typeName">
              <a-select v-model:value="updateUserForm.typeName" placeholder="请选择数据类型">
                <a-select-option value="appointment">学术任职</a-select-option>
                <a-select-option value="award">学生获奖指导</a-select-option>
                <a-select-option value="conference">学术会议</a-select-option>
                <a-select-option value="paper">高水平论文</a-select-option>
                <a-select-option value="patent">专利</a-select-option>
                <a-select-option value="researchProject">科研项目</a-select-option>
                <a-select-option value="studentProject">学生项目指导</a-select-option>
                <a-select-option value="teachingProject">教学改革项目</a-select-option>
                <a-select-option value="textbook">教材</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="数量变化" name="totalDelta">
              <a-input-number
                v-model:value="updateUserForm.totalDelta"
                placeholder="正数为增加，负数为减少"
                :precision="0"
              />
            </a-form-item>

            <a-form-item label="分数变化" name="scoreDelta">
              <a-input-number
                v-model:value="updateUserForm.scoreDelta"
                placeholder="正数为增加，负数为减少"
                :precision="2"
              />
            </a-form-item>

            <a-form-item>
              <a-button
                type="primary"
                :loading="updateUserLoading"
                @click="handleUpdateUserRankings"
              >
                更新用户排名
              </a-button>
            </a-form-item>
          </a-form>

          <a-divider />

          <div v-if="updateUserResult" class="result-section">
            <a-alert
              :type="updateUserResult.success ? 'success' : 'error'"
              show-icon
              :message="updateUserResult.message"
            />
            <div v-if="updateUserResult.success" class="result-details mt-4">
              <a-descriptions title="更新结果" bordered>
                <a-descriptions-item label="更新表">
                  {{ updateUserResult.data.tableName }}
                </a-descriptions-item>
                <a-descriptions-item label="更新用户数">
                  {{ updateUserResult.data.updatedUsers }}
                </a-descriptions-item>
                <a-descriptions-item label="更新时间">
                  {{ updateUserResult.data.updateTime }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>
        </a-tab-pane>

        <!-- 查看用户排名选项卡 -->
        <a-tab-pane key="viewRanking" tab="查看排名数据">
          <a-form layout="inline" class="mb-4">
            <a-form-item label="统计范围">
              <a-select
                v-model:value="rankingQueryParams.range"
                style="width: 150px"
              >
                <a-select-option value="in">范围内</a-select-option>
                <a-select-option value="out">范围外</a-select-option>
                <a-select-option value="all">全部</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="用户ID">
              <a-input
                v-model:value="rankingQueryParams.userId"
                placeholder="可选，输入用户ID"
                style="width: 200px"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="loadRankingData">查询</a-button>
              <a-button class="ml-2" @click="resetRankingQuery">重置</a-button>
            </a-form-item>
          </a-form>

          <a-table
            :columns="rankingColumns"
            :data-source="rankingData"
            :loading="rankingLoading"
            :pagination="rankingPagination"
            @change="handleTableChange"
            :scroll="{ x: 1500 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="viewUserDetail(record.userId)">
                  查看详情
                </a-button>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { getUserRankings, searchUsers, updateRankingTables, updateUserRankings } from '@/api/modules/api.sql';

export default defineComponent({
  name: 'SqlRankingManagement',
  
  setup() {
    // 当前激活的选项卡
    const activeTab = ref('updateTables');

    // 更新排名缓存表相关
    const updateTablesLoading = ref(false);
    const updateTablesResult = ref(null);

    // 更新用户排名相关
    const updateUserForm = reactive({
      tableName: '',
      userIds: [],
      typeName: '',
      totalDelta: 0,
      scoreDelta: 0
    });

    const updateUserFormRules = {
      tableName: [{ required: true, message: '请选择排名表', trigger: 'change' }],
      userIds: [{ required: true, type: 'array', message: '请选择至少一个用户', trigger: 'change' }],
      typeName: [{ required: true, message: '请选择数据类型', trigger: 'change' }]
    };

    const updateUserLoading = ref(false);
    const updateUserResult = ref(null);
    const userOptions = ref([]);
    const usersLoading = ref(false);

    // 查看排名数据相关
    const rankingQueryParams = reactive({
      range: 'all',
      page: 1,
      limit: 10,
      userId: ''
    });

    const rankingData = ref([]);
    const rankingLoading = ref(false);
    const rankingTotal = ref(0);

    const rankingColumns = [
      { title: '排名', dataIndex: 'rank', key: 'rank', fixed: 'left', width: 80 },
      { title: '用户ID', dataIndex: 'userId', key: 'userId', width: 120 },
      { title: '姓名', dataIndex: 'nickName', key: 'nickName', width: 120 },
      { title: '学号', dataIndex: 'studentNumber', key: 'studentNumber', width: 120 },
      { title: '项目总数', dataIndex: 'totalItemCount', key: 'totalItemCount', width: 100 },
      { title: '总分', dataIndex: 'totalScore', key: 'totalScore', width: 100 },
      { title: '学术任职数', dataIndex: 'appointmentCount', key: 'appointmentCount', width: 110 },
      { title: '学术任职分', dataIndex: 'appointmentScore', key: 'appointmentScore', width: 110 },
      { title: '获奖指导数', dataIndex: 'awardCount', key: 'awardCount', width: 110 },
      { title: '获奖指导分', dataIndex: 'awardScore', key: 'awardScore', width: 110 },
      { title: '学术会议数', dataIndex: 'conferenceCount', key: 'conferenceCount', width: 110 },
      { title: '学术会议分', dataIndex: 'conferenceScore', key: 'conferenceScore', width: 110 },
      { title: '论文数', dataIndex: 'paperCount', key: 'paperCount', width: 90 },
      { title: '论文分', dataIndex: 'paperScore', key: 'paperScore', width: 90 },
      { title: '专利数', dataIndex: 'patentCount', key: 'patentCount', width: 90 },
      { title: '专利分', dataIndex: 'patentScore', key: 'patentScore', width: 90 },
      { title: '操作', key: 'action', fixed: 'right', width: 100 }
    ];

    const rankingPagination = computed(() => ({
      total: rankingTotal.value,
      current: rankingQueryParams.page,
      pageSize: rankingQueryParams.limit,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条记录`
    }));

    // 更新排名缓存表
    const handleUpdateRankingTables = async () => {
      try {
        updateTablesLoading.value = true;
        const response = await updateRankingTables();
        if (response.code === 200) {
          updateTablesResult.value = {
            success: true,
            message: response.message,
            data: response.data
          };
          message.success('排名缓存表更新成功');
        } else {
          updateTablesResult.value = {
            success: false,
            message: response.message
          };
          message.error(response.message);
        }
      } catch (error) {
        updateTablesResult.value = {
          success: false,
          message: error.message || '更新排名缓存表失败'
        };
        message.error('更新排名缓存表失败');
        console.error('更新排名缓存表出错:', error);
      } finally {
        updateTablesLoading.value = false;
      }
    };

    // 搜索用户
    const handleUserSearch = async (query) => {
      if (!query || query.length < 2) return;

      try {
        usersLoading.value = true;
        const response = await searchUsers(query);

        if (response.code === 200) {
          userOptions.value = response.data.map(user => ({
            value: user.id,
            label: `${user.nickname} (${user.studentNumber})`
          }));
        }
      } catch (error) {
        console.error('搜索用户出错:', error);
        message.error('搜索用户失败');
      } finally {
        usersLoading.value = false;
      }
    };

    // 更新用户排名
    const handleUpdateUserRankings = async () => {
      try {
        updateUserLoading.value = true;
        const response = await updateUserRankings(updateUserForm);

        if (response.code === 200) {
          updateUserResult.value = {
            success: true,
            message: response.message,
            data: response.data
          };
          message.success('用户排名数据已更新');
        } else {
          updateUserResult.value = {
            success: false,
            message: response.message
          };
          message.error(response.message);
        }
      } catch (error) {
        updateUserResult.value = {
          success: false,
          message: error.message || '更新用户排名数据失败'
        };
        message.error('更新用户排名数据失败');
        console.error('更新用户排名数据出错:', error);
      } finally {
        updateUserLoading.value = false;
      }
    };

    // 加载排名数据
    const loadRankingData = async () => {
      try {
        rankingLoading.value = true;
        const response = await getUserRankings(rankingQueryParams);
        
        if (response.code === 200) {
          rankingData.value = response.result.records;
          rankingTotal.value = response.result.total;
        } else {
          message.error(response.message);
        }
      } catch (error) {
        console.error('获取排名数据出错:', error);
        message.error('获取排名数据失败');
      } finally {
        rankingLoading.value = false;
      }
    };

    // 处理表格分页、筛选、排序变化
    const handleTableChange = (pagination) => {
      rankingQueryParams.page = pagination.current;
      rankingQueryParams.limit = pagination.pageSize;
      loadRankingData();
    };

    // 重置排名查询条件
    const resetRankingQuery = () => {
      rankingQueryParams.range = 'all';
      rankingQueryParams.userId = '';
      rankingQueryParams.page = 1;
      loadRankingData();
    };

    // 查看用户详情
    const viewUserDetail = (userId) => {
      // 根据实际需求跳转到用户详情页或弹出详情对话框
      message.info(`查看用户 ${userId} 的详情`);
    };

    onMounted(() => {
      // 初始加载排名数据
      loadRankingData();
    });

    return {
      // 通用
      activeTab,
      
      // 更新排名缓存表
      updateTablesLoading,
      updateTablesResult,
      handleUpdateRankingTables,
      
      // 更新用户排名
      updateUserForm,
      updateUserFormRules,
      updateUserLoading,
      updateUserResult,
      userOptions,
      usersLoading,
      handleUserSearch,
      handleUpdateUserRankings,
      
      // 查看排名数据
      rankingQueryParams,
      rankingData,
      rankingColumns,
      rankingLoading,
      rankingPagination,
      loadRankingData,
      handleTableChange,
      resetRankingQuery,
      viewUserDetail
    };
  }
});
</script>

<style scoped>
.sql-ranking-container {
  padding: 20px;
}

.operation-section {
  margin-bottom: 20px;
}

.result-section {
  margin-top: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.ml-2 {
  margin-left: 8px;
}
</style>
