/*
 Navicat Premium Data Transfer

 Source Server         : 医学院绩效考核
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41-0ubuntu0.20.04.1)
 Source Host           : ************:3306
 Source Schema         : jxpd

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41-0ubuntu0.20.04.1)
 File Encoding         : 65001

 Date: 08/08/2025 17:40:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for academicAppointments
-- ----------------------------
DROP TABLE IF EXISTS `academicAppointments`;
CREATE TABLE `academicAppointments`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '记录ID',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID（外键）',
  `associationName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '协会/期刊名称',
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '职务名称',
  `levelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID（外键）',
  `startYear` year NOT NULL COMMENT '起始年份',
  `endYear` year NULL DEFAULT NULL COMMENT '结束年份（空为至今）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态（1-有效）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user`(`userId` ASC) USING BTREE,
  INDEX `idx_association`(`associationName`(20) ASC) USING BTREE,
  INDEX `idx_level_id`(`levelId` ASC) USING BTREE,
  CONSTRAINT `fk_appointment_level` FOREIGN KEY (`levelId`) REFERENCES `associationLevels` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_appointment_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '学术任职表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for associationLevels
-- ----------------------------
DROP TABLE IF EXISTS `associationLevels`;
CREATE TABLE `associationLevels`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `levelName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '级别名称（唯一）',
  `score` decimal(10, 2) NULL DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level`(`levelName` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '学术任职级别表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for awards
-- ----------------------------
DROP TABLE IF EXISTS `awards`;
CREATE TABLE `awards`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖名称',
  `awardsRuleId` varbinary(255) NULL DEFAULT NULL COMMENT '获奖规则对应id',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖类型',
  `level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖级别',
  `awardDate` date NOT NULL COMMENT '获奖时间',
  `awardUnit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '获奖单位',
  `awardRank` int NULL DEFAULT NULL COMMENT '获奖排名',
  `teachers` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖教师',
  `ifReviewer` tinyint NULL DEFAULT NULL COMMENT '是否审核',
  `reviewer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_award_type`(`type` ASC) USING BTREE,
  INDEX `idx_award_level`(`level` ASC) USING BTREE,
  INDEX `idx_award_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教学与科研获奖评分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for awards_rules
-- ----------------------------
DROP TABLE IF EXISTS `awards_rules`;
CREATE TABLE `awards_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `awardLevel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '奖项级别（如国家级一等奖、省部级二等奖等）',
  `rank` int NOT NULL COMMENT '完成人排序（第几位）',
  `score` decimal(10, 2) NOT NULL COMMENT '核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '奖项的详细描述（如国家级一等奖、省部级二等奖等）',
  `unitCount` int NULL DEFAULT 1 COMMENT '合作单位数目（用于分数除法）',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_award_level_rank`(`awardLevel` ASC, `rank` ASC) USING BTREE COMMENT '确保奖项级别和排序的唯一性'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教学与科研获奖核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for combined_ranking_temp
-- ----------------------------
DROP TABLE IF EXISTS `combined_ranking_temp`;
CREATE TABLE `combined_ranking_temp`  (
  `userId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `nickName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `studentNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `totalItemCount` int NULL DEFAULT 0,
  `totalScore` decimal(10, 2) NULL DEFAULT 0.00,
  `appointmentCount` int NULL DEFAULT 0,
  `appointmentScore` decimal(10, 2) NULL DEFAULT 0.00,
  `awardCount` int NULL DEFAULT 0,
  `awardScore` decimal(10, 2) NULL DEFAULT 0.00,
  `conferenceCount` int NULL DEFAULT 0,
  `conferenceScore` decimal(10, 2) NULL DEFAULT 0.00,
  `paperCount` int NULL DEFAULT 0,
  `paperScore` decimal(10, 2) NULL DEFAULT 0.00,
  `patentCount` int NULL DEFAULT 0,
  `patentScore` decimal(10, 2) NULL DEFAULT 0.00,
  `researchProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `studentProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `teachingProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `textbookScore` decimal(10, 2) NULL DEFAULT 0.00
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for conferences
-- ----------------------------
DROP TABLE IF EXISTS `conferences`;
CREATE TABLE `conferences`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '会议ID',
  `conferenceName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '会议名称',
  `levelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID（外键）',
  `holdTime` date NOT NULL COMMENT '举办时间（格式YYYY-MM-01）',
  `firstResponsibleId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '第一负责人ID（外键，关联user.id）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '备注',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID（外键，关联user.id）',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_conference_name`(`conferenceName` ASC) USING BTREE,
  INDEX `idx_hold_time`(`holdTime` ASC) USING BTREE,
  INDEX `fk_conference_level`(`levelId` ASC) USING BTREE,
  INDEX `fk_conference_first_responsible`(`firstResponsibleId` ASC) USING BTREE,
  INDEX `idx_reviewer`(`reviewerId` ASC) USING BTREE,
  INDEX `idx_if_reviewer_hold_time`(`ifReviewer` ASC, `holdTime` ASC) USING BTREE,
  INDEX `idx_level_id`(`levelId` ASC) USING BTREE,
  CONSTRAINT `fk_conference_first_responsible` FOREIGN KEY (`firstResponsibleId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_conference_level` FOREIGN KEY (`levelId`) REFERENCES `conferences_levels` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_conference_reviewer` FOREIGN KEY (`reviewerId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '会议主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for conferences_levels
-- ----------------------------
DROP TABLE IF EXISTS `conferences_levels`;
CREATE TABLE `conferences_levels`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `levelName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别名称（唯一）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '描述',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态（1-启用，0-禁用）',
  `score` decimal(10, 2) NULL DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level_name`(`levelName` ASC) USING BTREE,
  INDEX `idx_level_name`(`levelName` ASC) USING BTREE,
  INDEX `idx_level_status`(`status` ASC) USING BTREE,
  INDEX `idx_level_score`(`score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '会议级别表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for conferences_participants
-- ----------------------------
DROP TABLE IF EXISTS `conferences_participants`;
CREATE TABLE `conferences_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参与者记录ID',
  `conferenceId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '会议ID（外键）',
  `participantId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参与者ID（外键，关联user.id）',
  `allocationRatio` decimal(5, 4) NOT NULL COMMENT '分配比例（0~1）',
  `isLeader` tinyint(1) NULL DEFAULT 0 COMMENT '是否第一负责人（1-是）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_conference_participant`(`conferenceId` ASC, `participantId` ASC) USING BTREE,
  INDEX `idx_participant`(`participantId` ASC) USING BTREE,
  INDEX `idx_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_conference_participant_conference`(`conferenceId` ASC) USING BTREE,
  INDEX `idx_conference_participant_user`(`participantId` ASC) USING BTREE,
  INDEX `idx_conference_participant_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_participant_allocation`(`participantId` ASC, `allocationRatio` ASC) USING BTREE,
  CONSTRAINT `fk_conference_participant_conference` FOREIGN KEY (`conferenceId`) REFERENCES `conferences` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_conference_participant_user` FOREIGN KEY (`participantId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '会议参与者表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for deductions
-- ----------------------------
DROP TABLE IF EXISTS `deductions`;
CREATE TABLE `deductions`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `username` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户姓名',
  `userId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
  `deductionType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '扣分类型',
  `deductionDate` date NOT NULL COMMENT '扣分时间',
  `deductionScore` decimal(10, 2) NOT NULL COMMENT '扣分分值',
  `deductionReason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '扣分原因',
  `handleResult` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '处理结果',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人ID',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_deduction_type`(`deductionType` ASC) USING BTREE,
  INDEX `idx_deduction_date`(`deductionDate` ASC) USING BTREE,
  INDEX `idx_deduction_status`(`status` ASC) USING BTREE,
  INDEX `idx_deduction_userId`(`userId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '扣分管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for deductions_rules
-- ----------------------------
DROP TABLE IF EXISTS `deductions_rules`;
CREATE TABLE `deductions_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `unTimelyDegreeCount` int NULL DEFAULT NULL COMMENT '未按时获得学位的研究生数',
  `unTimelyDegreeDeduction` int NULL DEFAULT NULL COMMENT '未按时获得学位的扣减分数',
  `unemploymentDate` date NULL DEFAULT NULL COMMENT '未就业的截止日期（如 8 月 31 日、12 月 31 日）',
  `unemploymentDeduction` int NULL DEFAULT NULL COMMENT '未就业的扣减分数',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（与 id 一致的 UUID）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '扣分规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门ID，使用UUID',
  `departmentName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门名称',
  `departmentCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门代码',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '部门描述',
  `parentId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '上级部门ID（支持层级结构）',
  `sort` int NULL DEFAULT 0 COMMENT '排序字段，数字越小排序越靠前',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者ID',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_department_name`(`departmentName` ASC) USING BTREE COMMENT '确保部门名称唯一',
  UNIQUE INDEX `uk_department_code`(`departmentCode` ASC) USING BTREE COMMENT '确保部门代码唯一',
  INDEX `idx_parent_id`(`parentId` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort`(`sort` ASC) USING BTREE,
  CONSTRAINT `fk_department_parent` FOREIGN KEY (`parentId`) REFERENCES `departments` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for employment_quality
-- ----------------------------
DROP TABLE IF EXISTS `employment_quality`;
CREATE TABLE `employment_quality`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `major` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '专业',
  `year` int NOT NULL COMMENT '年份',
  `employmentRate` decimal(5, 2) NOT NULL COMMENT '就业率',
  `employmentIndustry` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '就业行业',
  `employmentRegion` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '就业地区',
  `averageSalary` decimal(10, 2) NOT NULL COMMENT '平均薪资',
  `majorMatchRate` decimal(5, 2) NOT NULL COMMENT '专业匹配率',
  `employmentSatisfaction` decimal(5, 2) NOT NULL COMMENT '就业满意度',
  `score` decimal(5, 2) NOT NULL COMMENT '综合评分',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `userId` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人ID',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_major`(`major` ASC) USING BTREE,
  INDEX `idx_year`(`year` ASC) USING BTREE,
  INDEX `idx_userId`(`userId` ASC) USING BTREE,
  INDEX `idx_employment_quality_major`(`major` ASC) USING BTREE,
  INDEX `idx_employment_quality_year`(`year` ASC) USING BTREE,
  INDEX `idx_employment_quality_status`(`status` ASC) USING BTREE,
  INDEX `idx_employment_quality_user_id`(`userId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '就业质量表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for file
-- ----------------------------
DROP TABLE IF EXISTS `file`;
CREATE TABLE `file`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件ID',
  `projectId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '项目id',
  `fileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '存储文件名',
  `originalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始文件名',
  `filePath` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件存储路径',
  `fileSize` bigint NOT NULL COMMENT '文件大小（字节）',
  `mimeType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件MIME类型',
  `extension` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件扩展名',
  `uploaderId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '上传人ID（关联user.id）',
  `relatedId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联ID（如项目ID等）',
  `relatedType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联类型（如research_project等）',
  `storageLocation` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'local' COMMENT '存储位置（如local）',
  `isDeleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删除标记（0=正常，1=删除）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文件描述',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uploader`(`uploaderId` ASC) USING BTREE COMMENT '上传人索引',
  INDEX `idx_filename`(`fileName` ASC) USING BTREE COMMENT '文件名索引',
  INDEX `idx_related`(`relatedId` ASC, `relatedType` ASC) USING BTREE COMMENT '关联索引',
  INDEX `idx_uploader_id`(`uploaderId` ASC) USING BTREE,
  CONSTRAINT `fk_file_uploader` FOREIGN KEY (`uploaderId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for high_level_paper_participants
-- ----------------------------
DROP TABLE IF EXISTS `high_level_paper_participants`;
CREATE TABLE `high_level_paper_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录ID',
  `paperId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文ID',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
  `allocationRatio` decimal(4, 2) NOT NULL COMMENT '分配比例（0.00-1.00）',
  `authorRank` int NULL DEFAULT NULL COMMENT '作者排名',
  `isFirstAuthor` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否第一作者',
  `isCorrespondingAuthor` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否通讯作者',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_participant`(`paperId` ASC, `userId` ASC) USING BTREE,
  INDEX `idx_paper_participant_paper`(`paperId` ASC) USING BTREE,
  INDEX `idx_paper_participant_user`(`userId` ASC) USING BTREE,
  INDEX `idx_paper_participant_first_author`(`isFirstAuthor` ASC) USING BTREE,
  INDEX `idx_paper_participant_corresponding`(`isCorrespondingAuthor` ASC) USING BTREE,
  INDEX `idx_participant_allocation_author`(`userId` ASC, `allocationRatio` ASC, `isFirstAuthor` ASC, `isCorrespondingAuthor` ASC) USING BTREE,
  CONSTRAINT `fk_paper_participant_paper` FOREIGN KEY (`paperId`) REFERENCES `high_level_papers` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_paper_participant_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '高水平论文参与人员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for high_level_papers
-- ----------------------------
DROP TABLE IF EXISTS `high_level_papers`;
CREATE TABLE `high_level_papers`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文ID',
  `submitterId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提交人ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文题目',
  `journal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '期刊名称',
  `publishDate` date NOT NULL COMMENT '发表时间/出版时间',
  `publicationTime` datetime NULL DEFAULT NULL COMMENT '出版时间（弃用）',
  `paperLevelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文级别ID（收录）',
  `collegeCorrespondentAuthorNumber` int NOT NULL DEFAULT 0 COMMENT '本学院通讯作者人数',
  `correspondentAuthorNumber` int NOT NULL DEFAULT 0 COMMENT '通讯作者总人数',
  `allocationProportionBase` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分配比例基数',
  `totalAllocationProportion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '总分配比例',
  `isFirstAffiliationOurs` tinyint(1) NOT NULL DEFAULT 1 COMMENT '第一单位是否为我们大学(1:是,0:否)',
  `firstAuthorType` tinyint(1) NOT NULL DEFAULT 1 COMMENT '第一作者类型(1:我院研究生,0:非我院研究生)',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '审核状态（0，拒审核 1，审核，null未审核）',
  `impactFactor` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '影响因子',
  `doi` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'DOI标识',
  `citations` int NOT NULL DEFAULT 0 COMMENT '引用次数',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_high_level_paper_status`(`status` ASC) USING BTREE,
  INDEX `idx_high_level_paper_publish_date`(`publishDate` ASC) USING BTREE,
  INDEX `idx_high_level_paper_first_author_type`(`firstAuthorType` ASC) USING BTREE,
  INDEX `fk_paper_submitter`(`submitterId` ASC) USING BTREE,
  INDEX `fk_paper_reviewer`(`reviewerId` ASC) USING BTREE,
  INDEX `idx_high_level_paper_level_id`(`paperLevelId` ASC) USING BTREE,
  INDEX `idx_high_level_paper_first_affiliation`(`isFirstAffiliationOurs` ASC) USING BTREE,
  INDEX `idx_paper_publish_review`(`publishDate` ASC, `ifReviewer` ASC) USING BTREE,
  CONSTRAINT `fk_paper_level` FOREIGN KEY (`paperLevelId`) REFERENCES `high_level_papers_rules` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_paper_reviewer` FOREIGN KEY (`reviewerId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_paper_submitter` FOREIGN KEY (`submitterId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '高水平论文评分表(包含第一作者类型和共同作者排名)' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for high_level_papers_rules
-- ----------------------------
DROP TABLE IF EXISTS `high_level_papers_rules`;
CREATE TABLE `high_level_papers_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `paperLevel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文级别（如 A1 Ⅰ、A1 Ⅱ 等）',
  `score` decimal(10, 2) NOT NULL COMMENT '基础核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '论文级别的详细描述（如中科院 JCR 分区等）',
  `nonDepartmentAuthorCoefficient` decimal(10, 2) NULL DEFAULT 0.90 COMMENT '非本院研究生第一作者的系数',
  `coFirstAuthorRankCoefficient` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '共同第一作者的排名系数（如 100%, 1/2, 1/3 等）',
  `maxPapersPerMentor` int NULL DEFAULT 5 COMMENT '每个导师最多填写的代表性论文数量',
  `nonDepartmentAuthorLimit` int NULL DEFAULT 1 COMMENT '非本院研究生第一作者的文章数量限制',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_paper_level`(`paperLevel` ASC) USING BTREE COMMENT '确保论文级别唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '高水平论文核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for international_exchanges
-- ----------------------------
DROP TABLE IF EXISTS `international_exchanges`;
CREATE TABLE `international_exchanges`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交流名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交流类型',
  `country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交流国家/地区',
  `institution` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '交流机构',
  `startDate` date NOT NULL COMMENT '开始时间',
  `endDate` date NOT NULL COMMENT '结束时间',
  `userIdList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '参与用户ID列表',
  `usernameList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '参与用户列表',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '交流内容',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '交流成果',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人ID',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_exchange_type`(`type` ASC) USING BTREE,
  INDEX `idx_exchange_country`(`country` ASC) USING BTREE,
  INDEX `idx_exchange_institution`(`institution` ASC) USING BTREE,
  INDEX `idx_exchange_dates`(`startDate` ASC, `endDate` ASC) USING BTREE,
  INDEX `idx_exchange_status`(`status` ASC) USING BTREE,
  FULLTEXT INDEX `idx_exchange_user_ids`(`userIdList`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '国际交流表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for international_exchanges_rules
-- ----------------------------
DROP TABLE IF EXISTS `international_exchanges_rules`;
CREATE TABLE `international_exchanges_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `project` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称（如来华学习交流、赴港澳台学习交流等）',
  `score` decimal(10, 2) NOT NULL COMMENT '核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目的详细描述（如来华学习交流超过 90 天的境外研究生）',
  `maxPeople` int NULL DEFAULT NULL COMMENT '最多记录个数',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId，与 id 一致的 UUID）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_project`(`project` ASC) USING BTREE COMMENT '确保项目名称唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '国际交流与合作评价核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '唯一标识每条记录的主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录的类型，例如research_project',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目的标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目的详细内容',
  `createdAt` datetime NOT NULL COMMENT '记录创建的时间戳',
  `abstract` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '与通知相关的摘要信息',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '与记录关联的用户ID，使用UUID格式',
  `initiatorId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通知的发起人ID，使用UUID格式',
  `sendMode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'single' COMMENT '发送模式：all/department/user_level',
  `targetDepartmentId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标部门ID（当send_mode为department时使用）',
  `targetUserLevelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目标用户级别ID（当send_mode为user_level时使用）',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '通知状态：1-正常，0-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_notifications_send_mode`(`sendMode` ASC) USING BTREE,
  INDEX `idx_notifications_target_user_level`(`targetUserLevelId` ASC) USING BTREE,
  INDEX `idx_notifications_status`(`status` ASC) USING BTREE,
  INDEX `idx_notifications_target_department_id`(`targetDepartmentId` ASC) USING BTREE,
  CONSTRAINT `fk_notifications_department` FOREIGN KEY (`targetDepartmentId`) REFERENCES `departments` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_notifications_user_level` FOREIGN KEY (`targetUserLevelId`) REFERENCES `user_levels` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for patent_categories
-- ----------------------------
DROP TABLE IF EXISTS `patent_categories`;
CREATE TABLE `patent_categories`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类ID',
  `categoryName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名称（唯一）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '分类描述',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态（1-启用，0-禁用）',
  `score` decimal(10, 2) NULL DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_name`(`categoryName` ASC) USING BTREE,
  INDEX `idx_patent_category_name`(`categoryName` ASC) USING BTREE,
  INDEX `idx_patent_category_status`(`status` ASC) USING BTREE,
  INDEX `idx_patent_category_score`(`score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '专利分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for patent_participants
-- ----------------------------
DROP TABLE IF EXISTS `patent_participants`;
CREATE TABLE `patent_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参与者ID',
  `patentId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '专利ID（外键）',
  `participantId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参与者Id（外键，关联user表的id）',
  `allocationRatio` decimal(5, 4) NOT NULL COMMENT '分配比例（0~1）',
  `isLeader` tinyint(1) NULL DEFAULT 0 COMMENT '是否第一负责人（1-是）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_patent_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_patent_participant_patent`(`patentId` ASC) USING BTREE,
  INDEX `idx_patent_participant_user`(`participantId` ASC) USING BTREE,
  INDEX `idx_patent_participant_leader`(`isLeader` ASC) USING BTREE,
  CONSTRAINT `patent_participants_ibfk_1` FOREIGN KEY (`patentId`) REFERENCES `patents` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '专利参与者表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for patents
-- ----------------------------
DROP TABLE IF EXISTS `patents`;
CREATE TABLE `patents`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '专利ID',
  `firstResponsibleID` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '第一负责人ID',
  `patentName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '专利名称',
  `categoryId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类ID（外键）',
  `authorizationDate` date NOT NULL COMMENT '授权时间（格式YYYY-MM-01）',
  `conversionDate` date NULL DEFAULT NULL COMMENT '转化时间（未转化则为NULL）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人ID',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_patent_name`(`patentName` ASC) USING BTREE,
  INDEX `idx_authorization_date`(`authorizationDate` ASC) USING BTREE,
  INDEX `idx_reviewer`(`reviewerId` ASC) USING BTREE,
  INDEX `idx_patent_category`(`categoryId` ASC) USING BTREE,
  INDEX `idx_patent_authorization_date`(`authorizationDate` ASC) USING BTREE,
  INDEX `idx_patent_name_auth_date`(`patentName`(50) ASC, `authorizationDate` ASC, `ifReviewer` ASC) USING BTREE,
  INDEX `idx_patent_auth_review`(`authorizationDate` ASC, `ifReviewer` ASC) USING BTREE,
  CONSTRAINT `patents_ibfk_1` FOREIGN KEY (`categoryId`) REFERENCES `patent_categories` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '专利主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限键',
  `parent_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级权限键（可选）',
  `auth` tinyint(1) NULL DEFAULT 0 COMMENT '是否是权限按钮',
  `status` int NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for research_funds
-- ----------------------------
DROP TABLE IF EXISTS `research_funds`;
CREATE TABLE `research_funds`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '经费类型',
  `amount` decimal(65, 2) NOT NULL COMMENT '经费金额(万元)',
  `startDate` date NOT NULL COMMENT '开始时间',
  `endDate` date NOT NULL COMMENT '结束时间',
  `leader` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目负责人',
  `members` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目成员',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目描述',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_research_fund_leader`(`leader` ASC) USING BTREE,
  INDEX `idx_research_fund_type`(`type` ASC) USING BTREE,
  INDEX `idx_research_fund_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '科研经费评分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for research_funds_rules
-- ----------------------------
DROP TABLE IF EXISTS `research_funds_rules`;
CREATE TABLE `research_funds_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `minAmount` decimal(15, 2) NOT NULL COMMENT '经费范围下限（单位：元）',
  `maxAmount` decimal(15, 2) NULL DEFAULT NULL COMMENT '经费范围上限（单位：元），可以为 NULL 表示无上限',
  `score` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定核算分数',
  `scoreFormula` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分数计算公式（当经费范围无上限时使用）',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_amount_range`(`minAmount` ASC, `maxAmount` ASC) USING BTREE COMMENT '确保经费范围唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '科研经费核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for research_project_participants
-- ----------------------------
DROP TABLE IF EXISTS `research_project_participants`;
CREATE TABLE `research_project_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录ID',
  `projectId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目ID',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
  `allocationRatio` decimal(4, 2) NOT NULL COMMENT '分配比例（0.00-1.00）',
  `participantRank` int NULL DEFAULT NULL COMMENT '参与者排名',
  `isLeader` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否项目负责人',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_participant`(`projectId` ASC, `userId` ASC) USING BTREE,
  INDEX `idx_project_participant_project`(`projectId` ASC) USING BTREE,
  INDEX `idx_project_participant_user`(`userId` ASC) USING BTREE,
  INDEX `idx_project_participant_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_researcher_leader_allocation`(`userId` ASC, `isLeader` ASC, `allocationRatio` ASC) USING BTREE,
  CONSTRAINT `fk_project_participant_project` FOREIGN KEY (`projectId`) REFERENCES `research_projects` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_project_participant_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '科研项目参与人员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for research_projects
-- ----------------------------
DROP TABLE IF EXISTS `research_projects`;
CREATE TABLE `research_projects`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目ID',
  `submitterId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '提交人ID',
  `projectId` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目编号',
  `projectIssuingDepartment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目下达部门',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目类别',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `approvalDate` date NOT NULL COMMENT '获批时间\r\n',
  `fundingAmount` decimal(12, 2) NULL DEFAULT NULL COMMENT '经费金额',
  `levelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目级别ID',
  `isUniversityFirstUnit` tinyint(1) NOT NULL DEFAULT 1 COMMENT '第一单位是否为暨南大学',
  `isCollegeFirstUnit` tinyint(1) NOT NULL DEFAULT 1 COMMENT '第一单位是否医学院',
  `startDate` date NOT NULL COMMENT '开始日期',
  `endDate` date NOT NULL COMMENT '结束日期',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目描述',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '审核状态（默认为空，0拒绝未审核 1，同意审核）',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:进行中,0:已结项)',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_research_project_status`(`status` ASC) USING BTREE,
  INDEX `idx_research_project_start_date`(`startDate` ASC) USING BTREE,
  INDEX `idx_research_project_level_id`(`levelId` ASC) USING BTREE,
  INDEX `idx_research_project_type`(`type` ASC) USING BTREE,
  INDEX `fk_project_submitter`(`submitterId` ASC) USING BTREE,
  INDEX `fk_project_reviewer`(`reviewerId` ASC) USING BTREE,
  INDEX `idx_research_projects_type`(`type` ASC) USING BTREE,
  INDEX `idx_project_id_approval_review`(`projectId` ASC, `approvalDate` ASC, `ifReviewer` ASC) USING BTREE,
  CONSTRAINT `fk_project_reviewer` FOREIGN KEY (`reviewerId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_project_submitter` FOREIGN KEY (`submitterId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_research_project_level` FOREIGN KEY (`levelId`) REFERENCES `research_projects_levels_rules` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '科研项目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for research_projects_levels_rules
-- ----------------------------
DROP TABLE IF EXISTS `research_projects_levels_rules`;
CREATE TABLE `research_projects_levels_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `levelName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目级别名称（如国家级、省部级等）',
  `score` decimal(10, 2) NOT NULL COMMENT '基础核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '级别描述',
  `nonDepartmentLeaderCoefficient` decimal(10, 2) NULL DEFAULT 0.90 COMMENT '非本院负责人的系数',
  `maxProjectsPerPerson` int NULL DEFAULT 5 COMMENT '每人最多可填写的代表性项目数量',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level_name`(`levelName` ASC) USING BTREE COMMENT '确保项目级别名称唯一',
  INDEX `idx_research_projects_levels_rules_id`(`id` ASC) USING BTREE,
  INDEX `idx_level_score`(`score` ASC) USING BTREE,
  INDEX `idx_research_level_score`(`score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '科研项目级别核算规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for resources
-- ----------------------------
DROP TABLE IF EXISTS `resources`;
CREATE TABLE `resources`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `srcName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资源名称',
  `srcType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资源类型',
  `previewPath` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '资源预览路径',
  `downloadPath` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '资源预览路径',
  `deletePath` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '资源删除路径',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户ID',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `srcSize` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资源大小',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `userId`(`userId` ASC) USING BTREE,
  CONSTRAINT `resources_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `roleName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `roleAuth` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色标识',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色备注',
  `status` int NULL DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `roleId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
  `permissionsId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限ID',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `roleId`(`roleId` ASC) USING BTREE,
  INDEX `permissionsId`(`permissionsId` ASC) USING BTREE,
  CONSTRAINT `role_permissions_ibfk_13` FOREIGN KEY (`roleId`) REFERENCES `role` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `role_permissions_ibfk_14` FOREIGN KEY (`permissionsId`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for score_records
-- ----------------------------
DROP TABLE IF EXISTS `score_records`;
CREATE TABLE `score_records`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `teacherId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '教师ID',
  `year` int NOT NULL COMMENT '评分年份',
  `researchProjectScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '科研项目得分',
  `researchFundScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '科研经费得分',
  `paperScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '论文得分',
  `awardScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '获奖得分',
  `exchangeScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '国际交流得分',
  `serviceScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '社会服务得分',
  `employmentScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '就业质量得分',
  `deductionScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '扣分得分',
  `totalScore` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_teacher_year`(`teacherId` ASC, `year` ASC) USING BTREE,
  INDEX `idx_score_record_teacher_id`(`teacherId` ASC) USING BTREE,
  INDEX `idx_score_record_year`(`year` ASC) USING BTREE,
  INDEX `idx_score_record_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评分记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for score_weights
-- ----------------------------
DROP TABLE IF EXISTS `score_weights`;
CREATE TABLE `score_weights`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权重ID',
  `categoryName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类别名称',
  `categoryCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类别代码',
  `weight` decimal(5, 2) NOT NULL DEFAULT 1.00 COMMENT '权重系数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_code`(`categoryCode` ASC) USING BTREE COMMENT '确保类别代码唯一',
  INDEX `idx_weight_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评分权重系数表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for social_services
-- ----------------------------
DROP TABLE IF EXISTS `social_services`;
CREATE TABLE `social_services`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务类型',
  `target` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务对象',
  `startDate` date NOT NULL COMMENT '开始时间',
  `endDate` date NOT NULL COMMENT '结束时间',
  `userIdList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参与用户ID',
  `usernameList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参与用户名称\r\n',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '服务内容',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '服务成果',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_social_service_type`(`type` ASC) USING BTREE,
  INDEX `idx_social_service_target`(`target` ASC) USING BTREE,
  INDEX `idx_social_service_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '社会服务评分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for social_services_rules
-- ----------------------------
DROP TABLE IF EXISTS `social_services_rules`;
CREATE TABLE `social_services_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '唯一标识符，随机生成的 UUID',
  `contributionName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '贡献的主类型（如“创办及参与重要学术期刊”）',
  `secondaryOption` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '贡献的二级选择（如“主编”、“副主编”、“编委”等）',
  `score` int NOT NULL COMMENT '该二级选择对应的得分',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（与 id 一致的 UUID）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_contribution_secondary`(`contributionName` ASC, `secondaryOption` ASC) USING BTREE COMMENT '确保贡献类型和二级选择的唯一性'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '社会服务核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for student_award_guidance_awardLevels
-- ----------------------------
DROP TABLE IF EXISTS `student_award_guidance_awardLevels`;
CREATE TABLE `student_award_guidance_awardLevels`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `levelName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '级别名称（唯一）',
  `score` decimal(10, 2) NOT NULL COMMENT '对应分数',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level`(`levelName` ASC) USING BTREE,
  INDEX `idx_level_score`(`score` ASC) USING BTREE,
  INDEX `idx_award_level_score`(`score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '指导学生获奖级别表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for student_award_guidance_awards
-- ----------------------------
DROP TABLE IF EXISTS `student_award_guidance_awards`;
CREATE TABLE `student_award_guidance_awards`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '奖项ID',
  `awardName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖名称/出国研究生姓名',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '系/教研室',
  `awardDate` date NOT NULL COMMENT '获奖时间/出国交流时间',
  `levelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_award_date`(`awardDate` ASC) USING BTREE,
  INDEX `idx_award_level`(`levelId` ASC) USING BTREE,
  INDEX `idx_department`(`department` ASC) USING BTREE,
  INDEX `idx_award_date_review`(`awardDate` ASC, `ifReviewer` ASC) USING BTREE,
  CONSTRAINT `fk_student_award_level` FOREIGN KEY (`levelId`) REFERENCES `student_award_guidance_awardLevels` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '指导学生获奖主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for student_award_guidance_participants
-- ----------------------------
DROP TABLE IF EXISTS `student_award_guidance_participants`;
CREATE TABLE `student_award_guidance_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '记录ID',
  `awardId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '奖项ID',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
  `allocationRatio` decimal(4, 2) NOT NULL COMMENT '分配比例（0.00-1.00）',
  `isLeader` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否主要指导教师',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_participant`(`awardId` ASC, `userId` ASC) USING BTREE,
  INDEX `idx_award_participant_award`(`awardId` ASC) USING BTREE,
  INDEX `idx_award_participant_user`(`userId` ASC) USING BTREE,
  INDEX `idx_award_participant_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_award_participant_allocation`(`userId` ASC, `awardId` ASC, `allocationRatio` ASC) USING BTREE,
  CONSTRAINT `fk_award_participant_award` FOREIGN KEY (`awardId`) REFERENCES `student_award_guidance_awards` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_award_participant_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '指导学生获奖参与表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for student_project_guidance_participants
-- ----------------------------
DROP TABLE IF EXISTS `student_project_guidance_participants`;
CREATE TABLE `student_project_guidance_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '记录ID',
  `projectId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '项目ID',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
  `allocationRatio` decimal(4, 2) NOT NULL COMMENT '分配比例（0.00-1.00）',
  `isLeader` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否主持人',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_participant`(`projectId` ASC, `userId` ASC) USING BTREE,
  INDEX `idx_project_participant_project`(`projectId` ASC) USING BTREE,
  INDEX `idx_project_participant_user`(`userId` ASC) USING BTREE,
  INDEX `idx_project_participant_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_project_participant_allocation`(`userId` ASC, `projectId` ASC, `allocationRatio` ASC) USING BTREE,
  INDEX `idx_student_project_participant_allocation`(`userId` ASC, `projectId` ASC, `allocationRatio` ASC) USING BTREE,
  CONSTRAINT `fk_student_participant_project` FOREIGN KEY (`projectId`) REFERENCES `student_project_guidance_projects` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_student_participant_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '指导学生立项参与表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for student_project_guidance_projectLevels
-- ----------------------------
DROP TABLE IF EXISTS `student_project_guidance_projectLevels`;
CREATE TABLE `student_project_guidance_projectLevels`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `levelName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '级别名称（唯一）',
  `score` decimal(10, 2) NOT NULL COMMENT '对应分数',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level`(`levelName` ASC) USING BTREE,
  INDEX `idx_level_score`(`score` ASC) USING BTREE,
  INDEX `idx_project_level_score`(`id` ASC, `score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '指导学生立项级别表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for student_project_guidance_projects
-- ----------------------------
DROP TABLE IF EXISTS `student_project_guidance_projects`;
CREATE TABLE `student_project_guidance_projects`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '项目ID',
  `projectNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目编号',
  `projectName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `approvalDepartment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '下达部门',
  `approvalDate` date NOT NULL COMMENT '获批日期',
  `approvalFund` decimal(10, 2) NULL DEFAULT NULL COMMENT '批准经费(万元)',
  `levelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `startYear` year NOT NULL COMMENT '执行起始年',
  `endYear` year NOT NULL COMMENT '执行结束年',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_project_number`(`projectNumber`(10) ASC) USING BTREE,
  INDEX `idx_approval_date`(`approvalDate` ASC) USING BTREE,
  INDEX `idx_level`(`levelId` ASC) USING BTREE,
  INDEX `idx_project_approval_review`(`approvalDate` ASC, `ifReviewer` ASC) USING BTREE,
  CONSTRAINT `fk_student_project_level` FOREIGN KEY (`levelId`) REFERENCES `student_project_guidance_projectLevels` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '指导学生立项主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for teacher
-- ----------------------------
DROP TABLE IF EXISTS `teacher`;
CREATE TABLE `teacher`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '教师姓名',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职称',
  `department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属院系',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(1:在职 0:离职)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for teaching_reform_participants
-- ----------------------------
DROP TABLE IF EXISTS `teaching_reform_participants`;
CREATE TABLE `teaching_reform_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '记录ID',
  `projectId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '项目ID',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
  `allocationRatio` decimal(4, 2) NOT NULL COMMENT '分配比例（0.00-1.00）',
  `isLeader` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否主持人',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_participant`(`projectId` ASC, `userId` ASC) USING BTREE,
  INDEX `idx_project_participant_project`(`projectId` ASC) USING BTREE,
  INDEX `idx_project_participant_user`(`userId` ASC) USING BTREE,
  INDEX `idx_project_participant_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_reform_participant_leader_allocation`(`userId` ASC, `isLeader` ASC, `allocationRatio` ASC) USING BTREE,
  CONSTRAINT `fk_participant_project` FOREIGN KEY (`projectId`) REFERENCES `teaching_reform_projects` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_participant_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '项目参与表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for teaching_reform_projectLevels
-- ----------------------------
DROP TABLE IF EXISTS `teaching_reform_projectLevels`;
CREATE TABLE `teaching_reform_projectLevels`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `levelName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '级别名称（唯一）',
  `score` decimal(10, 2) NOT NULL COMMENT '对应分数',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level`(`levelName` ASC) USING BTREE,
  INDEX `idx_level_score`(`score` ASC) USING BTREE,
  INDEX `idx_reform_level_score`(`score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '项目级别表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for teaching_reform_projects
-- ----------------------------
DROP TABLE IF EXISTS `teaching_reform_projects`;
CREATE TABLE `teaching_reform_projects`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '项目ID',
  `projectNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目编号',
  `projectName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `approvalDepartment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '下达部门',
  `approvalDate` date NOT NULL COMMENT '获批日期',
  `approvalFund` decimal(10, 2) NULL DEFAULT NULL COMMENT '批准经费(万元)',
  `levelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `startYear` year NOT NULL COMMENT '执行起始年',
  `endYear` year NOT NULL COMMENT '执行结束年',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_project_number`(`projectNumber`(10) ASC) USING BTREE,
  INDEX `idx_approval_date`(`approvalDate` ASC) USING BTREE,
  INDEX `idx_level`(`levelId` ASC) USING BTREE,
  INDEX `idx_reform_approval_review`(`approvalDate` ASC, `ifReviewer` ASC) USING BTREE,
  CONSTRAINT `fk_project_level` FOREIGN KEY (`levelId`) REFERENCES `teaching_reform_projectLevels` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教学改革项目主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for teaching_research_award_levels
-- ----------------------------
DROP TABLE IF EXISTS `teaching_research_award_levels`;
CREATE TABLE `teaching_research_award_levels`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '级别ID',
  `levelName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '级别名称',
  `score` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '基础分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '级别描述',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态（1-启用，0-禁用）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level_name`(`levelName` ASC) USING BTREE COMMENT '确保级别名称唯一',
  INDEX `idx_level_status`(`status` ASC) USING BTREE,
  INDEX `idx_level_score`(`score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教学科技奖励级别表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for teaching_research_award_participants
-- ----------------------------
DROP TABLE IF EXISTS `teaching_research_award_participants`;
CREATE TABLE `teaching_research_award_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参与者记录ID',
  `awardId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '奖励ID（外键）',
  `participantId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参与者ID（外键，关联user.id）',
  `employeeNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人事编号',
  `allocationRatio` decimal(5, 4) NOT NULL COMMENT '分配比例（0~1）',
  `isLeader` tinyint(1) NULL DEFAULT 0 COMMENT '是否第一负责人（1-是）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_award_participant`(`awardId` ASC, `participantId` ASC) USING BTREE COMMENT '确保同一奖励同一参与者只有一条记录',
  INDEX `idx_award_participant`(`awardId` ASC, `participantId` ASC) USING BTREE,
  INDEX `idx_participant`(`participantId` ASC) USING BTREE,
  INDEX `idx_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_award_participant_award`(`awardId` ASC) USING BTREE,
  INDEX `idx_award_participant_user`(`participantId` ASC) USING BTREE,
  INDEX `idx_award_participant_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_participant_allocation`(`participantId` ASC, `allocationRatio` ASC) USING BTREE,
  CONSTRAINT `fk_teaching_award_participant_award` FOREIGN KEY (`awardId`) REFERENCES `teaching_research_awards` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_teaching_award_participant_user` FOREIGN KEY (`participantId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教学科技奖励参与者表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for teaching_research_awards
-- ----------------------------
DROP TABLE IF EXISTS `teaching_research_awards`;
CREATE TABLE `teaching_research_awards`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '奖励ID',
  `awardName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖名称',
  `awardTime` date NOT NULL COMMENT '获奖时间',
  `awardLevelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '奖励级别ID（外键）',
  `firstResponsibleId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '第一负责人ID（外键，关联user.id）',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系/教研室',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID（外键，关联user.id）',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态（1-有效，0-删除）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_award_name`(`awardName` ASC) USING BTREE,
  INDEX `idx_award_time`(`awardTime` ASC) USING BTREE,
  INDEX `idx_award_level`(`awardLevelId` ASC) USING BTREE,
  INDEX `idx_first_responsible`(`firstResponsibleId` ASC) USING BTREE,
  INDEX `idx_reviewer`(`reviewerId` ASC) USING BTREE,
  INDEX `idx_if_reviewer_award_time`(`ifReviewer` ASC, `awardTime` ASC) USING BTREE,
  INDEX `idx_award_status`(`status` ASC) USING BTREE,
  CONSTRAINT `fk_award_first_responsible` FOREIGN KEY (`firstResponsibleId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_award_level` FOREIGN KEY (`awardLevelId`) REFERENCES `teaching_research_award_levels` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_award_reviewer` FOREIGN KEY (`reviewerId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教学科技奖励主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for teaching_workload_level
-- ----------------------------
DROP TABLE IF EXISTS `teaching_workload_level`;
CREATE TABLE `teaching_workload_level`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类别ID',
  `categoryName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类别名称',
  `score` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '基础分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '类别描述',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态（1-启用，0-禁用）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_name`(`categoryName` ASC) USING BTREE COMMENT '确保类别名称唯一',
  INDEX `idx_category_status`(`status` ASC) USING BTREE,
  INDEX `idx_category_score`(`score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教学工作量类别表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for teaching_workload_participants
-- ----------------------------
DROP TABLE IF EXISTS `teaching_workload_participants`;
CREATE TABLE `teaching_workload_participants`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参与者记录ID',
  `workloadId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工作量ID（外键）',
  `participantId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参与者ID（外键，关联user.id）',
  `employeeNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '人事编号',
  `allocationRatio` decimal(5, 4) NOT NULL COMMENT '分配比例（0~1）',
  `isLeader` tinyint(1) NULL DEFAULT 0 COMMENT '是否主讲教师（1-是）',
  `teachingHours` decimal(8, 2) NULL DEFAULT 0.00 COMMENT '个人授课时数',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_workload_participant`(`workloadId` ASC, `participantId` ASC) USING BTREE COMMENT '确保同一工作量同一参与者只有一条记录',
  INDEX `idx_workload_participant`(`workloadId` ASC, `participantId` ASC) USING BTREE,
  INDEX `idx_participant`(`participantId` ASC) USING BTREE,
  INDEX `idx_leader`(`isLeader` ASC) USING BTREE,
  INDEX `idx_participant_allocation`(`participantId` ASC, `allocationRatio` ASC) USING BTREE,
  CONSTRAINT `fk_workload_participant_user` FOREIGN KEY (`participantId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_workload_participant_workload` FOREIGN KEY (`workloadId`) REFERENCES `teaching_workloads` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教学工作量参与者表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for teaching_workloads
-- ----------------------------
DROP TABLE IF EXISTS `teaching_workloads`;
CREATE TABLE `teaching_workloads`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工作量ID',
  `teacherId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '教师ID（外键，关联user.id）',
  `semester` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '授课学期（如：2023-2024-1）',
  `courseName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课程名称',
  `studentLevel` enum('undergraduate','graduate') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '学生类别（undergraduate-本科生，graduate-研究生）',
  `courseType` enum('theory','experiment') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课程类型（theory-理论课，experiment-实验课）',
  `courseNature` enum('required','elective') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课程性质（required-必修课，elective-选修课）',
  `isNewCourse` tinyint(1) NULL DEFAULT 0 COMMENT '是否新开课（1-是，0-否）',
  `studentCount` int NOT NULL COMMENT '授课人数',
  `teachingHours` decimal(8, 2) NOT NULL COMMENT '教师授课时数',
  `categoryId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '工作量类别ID（外键）',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系/教研室',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人ID（外键，关联user.id）',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '是否审核（1，已审核，0拒绝审核，null未审核）',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态（1-有效，0-删除）',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_teacher`(`teacherId` ASC) USING BTREE,
  INDEX `idx_semester`(`semester` ASC) USING BTREE,
  INDEX `idx_course_name`(`courseName` ASC) USING BTREE,
  INDEX `idx_student_level`(`studentLevel` ASC) USING BTREE,
  INDEX `idx_course_type`(`courseType` ASC) USING BTREE,
  INDEX `idx_course_nature`(`courseNature` ASC) USING BTREE,
  INDEX `idx_new_course`(`isNewCourse` ASC) USING BTREE,
  INDEX `idx_category`(`categoryId` ASC) USING BTREE,
  INDEX `idx_reviewer`(`reviewerId` ASC) USING BTREE,
  INDEX `idx_if_reviewer_semester`(`ifReviewer` ASC, `semester` ASC) USING BTREE,
  INDEX `idx_workload_status`(`status` ASC) USING BTREE,
  INDEX `idx_teacher_semester`(`teacherId` ASC, `semester` ASC) USING BTREE,
  CONSTRAINT `fk_workload_category` FOREIGN KEY (`categoryId`) REFERENCES `teaching_workload_level` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_workload_reviewer` FOREIGN KEY (`reviewerId`) REFERENCES `user` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_workload_teacher` FOREIGN KEY (`teacherId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教学工作量主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for textbook_categories
-- ----------------------------
DROP TABLE IF EXISTS `textbook_categories`;
CREATE TABLE `textbook_categories`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类别 ID',
  `categoryAndPosition` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类别及任职（唯一）',
  `score` decimal(10, 2) NULL DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category`(`categoryAndPosition` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教材与著作类别表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for textbook_publishing_rules
-- ----------------------------
DROP TABLE IF EXISTS `textbook_publishing_rules`;
CREATE TABLE `textbook_publishing_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类别（如国家级规划教材、国家级课程等）',
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色（如主编、副主编、编委等）',
  `score` decimal(10, 2) NOT NULL COMMENT '核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '类别的详细描述（如国家级规划教材、国家级课程等）',
  `additionalInfo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '额外信息（如需提供转让合同或应用证明等）',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者 ID（userId）',
  `createdAt` datetime NOT NULL COMMENT '记录创建时间',
  `updatedAt` datetime NOT NULL COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_role`(`category` ASC, `role` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for textbooks
-- ----------------------------
DROP TABLE IF EXISTS `textbooks`;
CREATE TABLE `textbooks`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '教材与著作 ID',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户 ID（外键）',
  `materialName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '教材与著作名称',
  `publishDate` date NOT NULL COMMENT '出版日期',
  `categoryId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类别 ID',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `reviewComment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '审核意见（拒绝或同意）',
  `ifReviewer` tinyint(1) NULL DEFAULT NULL COMMENT '审核状态（0，拒审核 1，审核，null未审核）',
  `reviewerId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '审核人 ID',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `attachmentUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '附件URL',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user`(`userId` ASC) USING BTREE,
  INDEX `fk_material_category`(`categoryId` ASC) USING BTREE,
  CONSTRAINT `fk_material_category` FOREIGN KEY (`categoryId`) REFERENCES `textbook_categories` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_material_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '教材与著作主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for time_interval
-- ----------------------------
DROP TABLE IF EXISTS `time_interval`;
CREATE TABLE `time_interval`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `nameC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `startTime` date NOT NULL,
  `endTime` date NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `createBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_time_interval_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `roleId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色ID',
  `studentNumber` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工号/学号',
  `avatar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户头像',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'Vchs0bbdk2pr/Ac6DsHruw==' COMMENT '密码',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'John Doe' COMMENT '昵称',
  `userLevelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户级别ID，关联user_levels表',
  `departmentId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门ID，关联departments表',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_nickname`(`nickname` ASC) USING BTREE,
  INDEX `idx_user_student_number`(`studentNumber` ASC) USING BTREE,
  INDEX `idx_user_level_id`(`userLevelId` ASC) USING BTREE,
  INDEX `idx_user_department_id`(`departmentId` ASC) USING BTREE,
  CONSTRAINT `fk_user_department` FOREIGN KEY (`departmentId`) REFERENCES `departments` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_user_level` FOREIGN KEY (`userLevelId`) REFERENCES `user_levels` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_level_records
-- ----------------------------
DROP TABLE IF EXISTS `user_level_records`;
CREATE TABLE `user_level_records`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录ID，使用UUID',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID，关联user表',
  `levelId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对应的级别ID，关联user_levels表',
  `obtainedAt` datetime NOT NULL COMMENT '获得该职称的时间',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注，如通过评审、调动等',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录创建人ID',
  `createdAt` datetime NOT NULL COMMENT '记录创建时间',
  `updatedAt` datetime NOT NULL COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`userId` ASC) USING BTREE,
  INDEX `idx_level_id`(`levelId` ASC) USING BTREE,
  INDEX `idx_obtained_at`(`obtainedAt` ASC) USING BTREE,
  CONSTRAINT `fk_user_level_records_level` FOREIGN KEY (`levelId`) REFERENCES `user_levels` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_user_level_records_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户获得职称记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_levels
-- ----------------------------
DROP TABLE IF EXISTS `user_levels`;
CREATE TABLE `user_levels`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '级别ID，使用UUID',
  `levelName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '级别名称（如教授、副教授、讲师等）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '级别描述',
  `weight` decimal(5, 2) NOT NULL DEFAULT 1.00 COMMENT '职称权重系数，默认1.00',
  `sort` int NULL DEFAULT 0 COMMENT '排序字段，数字越小排序越靠前',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者ID',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level_name`(`levelName` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort`(`sort` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户级别表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_opt_log
-- ----------------------------
DROP TABLE IF EXISTS `user_opt_log`;
CREATE TABLE `user_opt_log`  (
  `id` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `operator` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `operatorId` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作人ID',
  `module` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作模块',
  `platform` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作平台',
  `operatorIP` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '设备IP',
  `latitude` double NULL DEFAULT NULL COMMENT '纬度',
  `longitude` double NULL DEFAULT NULL COMMENT '经度',
  `address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '设备位置',
  `content` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作内容',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_ranking_reviewed_all
-- ----------------------------
DROP TABLE IF EXISTS `user_ranking_reviewed_all`;
CREATE TABLE `user_ranking_reviewed_all`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `updateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `rank` int NULL DEFAULT NULL,
  `userId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `nickName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `studentNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `totalItemCount` int NULL DEFAULT 0,
  `totalScore` decimal(10, 2) NULL DEFAULT 0.00,
  `appointmentCount` int NULL DEFAULT 0,
  `appointmentScore` decimal(10, 2) NULL DEFAULT 0.00,
  `awardCount` int NULL DEFAULT 0,
  `awardScore` decimal(10, 2) NULL DEFAULT 0.00,
  `conferenceCount` int NULL DEFAULT 0,
  `conferenceScore` decimal(10, 2) NULL DEFAULT 0.00,
  `paperCount` int NULL DEFAULT 0,
  `paperScore` decimal(10, 2) NULL DEFAULT 0.00,
  `patentCount` int NULL DEFAULT 0,
  `patentScore` decimal(10, 2) NULL DEFAULT 0.00,
  `researchCount` int NULL DEFAULT 0,
  `researchScore` decimal(10, 2) NULL DEFAULT 0.00,
  `studentProjectCount` int NULL DEFAULT 0,
  `studentProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `teachingProjectCount` int NULL DEFAULT 0,
  `teachingProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `textbookCount` int NULL DEFAULT 0,
  `textbookScore` decimal(10, 2) NULL DEFAULT 0.00,
  `teachingWorkloadCount` int NULL DEFAULT 0 COMMENT '教学工作量数量',
  `teachingWorkloadScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '教学工作量分数',
  `teachingResearchAwardCount` int NULL DEFAULT 0 COMMENT '教学科技奖励数量',
  `teachingResearchAwardScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '教学科技奖励分数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `userId`(`userId` ASC) USING BTREE,
  INDEX `totalScore`(`totalScore` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_ranking_reviewed_in
-- ----------------------------
DROP TABLE IF EXISTS `user_ranking_reviewed_in`;
CREATE TABLE `user_ranking_reviewed_in`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `updateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `rank` int NULL DEFAULT NULL,
  `userId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `nickName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `studentNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `totalItemCount` int NULL DEFAULT 0,
  `totalScore` decimal(10, 2) NULL DEFAULT 0.00,
  `appointmentCount` int NULL DEFAULT 0,
  `appointmentScore` decimal(10, 2) NULL DEFAULT 0.00,
  `awardCount` int NULL DEFAULT 0,
  `awardScore` decimal(10, 2) NULL DEFAULT 0.00,
  `conferenceCount` int NULL DEFAULT 0,
  `conferenceScore` decimal(10, 2) NULL DEFAULT 0.00,
  `paperCount` int NULL DEFAULT 0,
  `paperScore` decimal(10, 2) NULL DEFAULT 0.00,
  `patentCount` int NULL DEFAULT 0,
  `patentScore` decimal(10, 2) NULL DEFAULT 0.00,
  `researchCount` int NULL DEFAULT 0,
  `researchScore` decimal(10, 2) NULL DEFAULT 0.00,
  `studentProjectCount` int NULL DEFAULT 0,
  `studentProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `teachingProjectCount` int NULL DEFAULT 0,
  `teachingProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `textbookCount` int NULL DEFAULT 0,
  `textbookScore` decimal(10, 2) NULL DEFAULT 0.00,
  `teachingWorkloadCount` int NULL DEFAULT 0 COMMENT '教学工作量数量',
  `teachingWorkloadScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '教学工作量分数',
  `teachingResearchAwardCount` int NULL DEFAULT 0 COMMENT '教学科技奖励数量',
  `teachingResearchAwardScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '教学科技奖励分数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `userId`(`userId` ASC) USING BTREE,
  INDEX `totalScore`(`totalScore` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_ranking_reviewed_out
-- ----------------------------
DROP TABLE IF EXISTS `user_ranking_reviewed_out`;
CREATE TABLE `user_ranking_reviewed_out`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `updateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `rank` int NULL DEFAULT NULL,
  `userId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `nickName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `studentNumber` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `totalItemCount` int NULL DEFAULT 0,
  `totalScore` decimal(10, 2) NULL DEFAULT 0.00,
  `appointmentCount` int NULL DEFAULT 0,
  `appointmentScore` decimal(10, 2) NULL DEFAULT 0.00,
  `awardCount` int NULL DEFAULT 0,
  `awardScore` decimal(10, 2) NULL DEFAULT 0.00,
  `conferenceCount` int NULL DEFAULT 0,
  `conferenceScore` decimal(10, 2) NULL DEFAULT 0.00,
  `paperCount` int NULL DEFAULT 0,
  `paperScore` decimal(10, 2) NULL DEFAULT 0.00,
  `patentCount` int NULL DEFAULT 0,
  `patentScore` decimal(10, 2) NULL DEFAULT 0.00,
  `researchCount` int NULL DEFAULT 0,
  `researchScore` decimal(10, 2) NULL DEFAULT 0.00,
  `studentProjectCount` int NULL DEFAULT 0,
  `studentProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `teachingProjectCount` int NULL DEFAULT 0,
  `teachingProjectScore` decimal(10, 2) NULL DEFAULT 0.00,
  `textbookCount` int NULL DEFAULT 0,
  `textbookScore` decimal(10, 2) NULL DEFAULT 0.00,
  `teachingWorkloadCount` int NULL DEFAULT 0 COMMENT '教学工作量数量',
  `teachingWorkloadScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '教学工作量分数',
  `teachingResearchAwardCount` int NULL DEFAULT 0 COMMENT '教学科技奖励数量',
  `teachingResearchAwardScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '教学科技奖励分数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `userId`(`userId` ASC) USING BTREE,
  INDEX `totalScore`(`totalScore` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_record
-- ----------------------------
DROP TABLE IF EXISTS `user_record`;
CREATE TABLE `user_record`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名称',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职称',
  `department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属院系',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(1:已注册的USER 0:临时USER)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Procedure structure for get_academic_appointments_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_academic_appointments_total_score`;
delimiter ;;
CREATE PROCEDURE `get_academic_appointments_total_score`(IN range_param VARCHAR(50),    -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'academicAppointments'
    LIMIT 1;
    
    -- 按级别统计任职数量和总分
    SELECT 
        al.id AS levelId,
        al.levelName,
        COUNT(a.id) AS count,
        SUM(CAST(IFNULL(al.score, 0) AS DECIMAL(10,2))) AS totalScore
    FROM academicAppointments a
    LEFT JOIN associationLevels al ON a.levelId = al.id
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR a.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤（学术任职使用年份，需要特殊处理）
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (
                -- 开始年份在范围内
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                -- 结束年份在范围内
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                -- 跨越整个范围
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            WHEN range_param = 'out' AND NOT (
                -- 开始年份在范围内
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                -- 结束年份在范围内
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                -- 跨越整个范围
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            ELSE FALSE
        END
    GROUP BY al.id, al.levelName
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(a.id) AS totalAppointments,
        CAST(SUM(IFNULL(al.score, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM academicAppointments a
    LEFT JOIN associationLevels al ON a.levelId = al.id
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR a.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            WHEN range_param = 'out' AND NOT (
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'academicAppointments'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_conferences_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_conferences_total_score`;
delimiter ;;
CREATE PROCEDURE `get_conferences_total_score`(IN range_param VARCHAR(50),    -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'conferences'
    LIMIT 1;
    
    -- 按级别统计会议数量和总分
    SELECT 
        cl.id AS levelId,
        cl.levelName,
        COUNT(c.id) AS count,
        SUM(CAST(IFNULL(cl.score * cp.allocationRatio, 0) AS DECIMAL(10,2))) AS totalScore
    FROM conferences c
    LEFT JOIN conferences_levels cl ON c.levelId = cl.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN conferences_participants cp ON c.id = cp.conferenceId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR cp.participantId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND c.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND c.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND c.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (c.holdTime BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (c.holdTime NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    GROUP BY cl.id, cl.levelName
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(c.id) AS totalConferences,
        CAST(SUM(IFNULL(cl.score * cp.allocationRatio, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM conferences c
    LEFT JOIN conferences_levels cl ON c.levelId = cl.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN conferences_participants cp ON c.id = cp.conferenceId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR cp.participantId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND c.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND c.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND c.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (c.holdTime BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (c.holdTime NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'conferences'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_high_level_papers_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_high_level_papers_total_score`;
delimiter ;;
CREATE PROCEDURE `get_high_level_papers_total_score`(IN range_param VARCHAR(50),    -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'highLevelPapers'
    LIMIT 1;
    
    -- 按级别统计论文数量和总分
    SELECT 
        hpr.id AS levelId,
        hpr.paperLevel AS levelName,
        COUNT(hp.id) AS count,
        SUM(CAST(IFNULL(hpr.score * hpp.allocationRatio, 0) AS DECIMAL(10,2))) AS totalScore
    FROM high_level_papers hp
    LEFT JOIN high_level_papers_rules hpr ON hp.paperLevelId = hpr.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN high_level_paper_participants hpp ON hp.id = hpp.paperId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR hpp.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND hp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND hp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND hp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (hp.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (hp.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    GROUP BY hpr.id, hpr.paperLevel
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(hp.id) AS totalPapers,
        CAST(SUM(IFNULL(hpr.score * hpp.allocationRatio, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM high_level_papers hp
    LEFT JOIN high_level_papers_rules hpr ON hp.paperLevelId = hpr.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN high_level_paper_participants hpp ON hp.id = hpp.paperId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR hpp.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND hp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND hp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND hp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (hp.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (hp.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'highLevelPapers'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_high_level_paper_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_high_level_paper_ranking`;
delimiter ;;
CREATE PROCEDURE `get_high_level_paper_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT,
    IN nick_name VARCHAR(100))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'highLevelPapers'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 直接查询而不使用临时表
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            u.id AS userId,
            u.nickname AS nickName,
            u.studentNumber,
            COUNT(pp.paperId) AS paperCount,
            SUM(IF(pp.isFirstAuthor = 1, 1, 0)) AS firstAuthorCount,
            SUM(IF(pp.isCorrespondingAuthor = 1, 1, 0)) AS correspondingAuthorCount,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.publishDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.publishDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM high_level_paper_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN high_level_papers p ON pp.paperId = p.id
        LEFT JOIN high_level_papers_rules r ON p.paperLevelId = r.id
        WHERE 
            (nick_name = '' OR u.nickname LIKE CONCAT('%', nick_name, '%'))
            AND (
                CASE 
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.publishDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            u.id AS userId,
            u.nickname AS nickName,
            u.studentNumber,
            COUNT(pp.paperId) AS paperCount,
            SUM(IF(pp.isFirstAuthor = 1, 1, 0)) AS firstAuthorCount,
            SUM(IF(pp.isCorrespondingAuthor = 1, 1, 0)) AS correspondingAuthorCount,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.publishDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.publishDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM high_level_paper_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN high_level_papers p ON pp.paperId = p.id
        LEFT JOIN high_level_papers_rules r ON p.paperLevelId = r.id
        WHERE 
            (nick_name = '' OR u.nickname LIKE CONCAT('%', nick_name, '%'))
            AND (
                CASE 
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.publishDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_patents_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_patents_total_score`;
delimiter ;;
CREATE PROCEDURE `get_patents_total_score`(IN range_param VARCHAR(50),    -- 'in'(范围内), 'out'(范围外), 'all'(全部)
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'patents'
    LIMIT 1;
    
    -- 按类型统计专利数量和总分
    SELECT 
        pc.id AS categoryId,
        pc.categoryName,
        COUNT(p.id) AS count,
        SUM(CAST(IFNULL(pc.score * pp.allocationRatio, 0) AS DECIMAL(10,2))) AS totalScore
    FROM patents p
    LEFT JOIN patent_categories pc ON p.categoryId = pc.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN patent_participants pp ON p.id = pp.patentId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR pp.participantId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤（检查授权日期或转化日期）
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) THEN TRUE
            WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) THEN TRUE
            ELSE FALSE
        END
    GROUP BY pc.id, pc.categoryName
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(p.id) AS totalPatents,
        CAST(SUM(IFNULL(pc.score * pp.allocationRatio, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM patents p
    LEFT JOIN patent_categories pc ON p.categoryId = pc.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN patent_participants pp ON p.id = pp.patentId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR pp.participantId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤（检查授权日期或转化日期）
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) THEN TRUE
            WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'patents'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_patent_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_patent_ranking`;
delimiter ;;
CREATE PROCEDURE `get_patent_ranking`(IN range_param VARCHAR(50),         -- 'in'(范围内), 'out'(范围外), 'all'(全部)
    IN review_status_param VARCHAR(50), -- 'approved'(已批准), 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 'all'(全部)
    IN limit_param INT,                 -- 每页数量
    IN page_param INT,                  -- 页码
    IN is_export_all TINYINT,           -- 是否导出全部
    IN patent_name VARCHAR(100),        -- 专利名称（用于搜索）
    IN nick_name VARCHAR(100))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'patents'
    LIMIT 1;

    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 直接查询而不使用临时表
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            u.id AS userId,
            u.nickname AS nickName,
            u.studentNumber,
            COUNT(pp.patentId) AS patentCount,
            SUM(IF(pp.isLeader = 1, 1, 0)) AS leaderPatentCount,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) 
                        THEN CAST(IFNULL(pc.score, '0') AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) 
                        THEN CAST(IFNULL(pc.score, '0') AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(pc.score, '0') AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM patent_participants pp
        INNER JOIN user u ON pp.participantId = u.id
        INNER JOIN patents p ON pp.patentId = p.id
        INNER JOIN patent_categories pc ON p.categoryId = pc.id
        WHERE 
            (patent_name = '' OR p.patentName LIKE CONCAT('%', patent_name, '%'))
            AND (nick_name = '' OR u.nickname LIKE CONCAT('%', nick_name, '%'))
            AND (
                CASE 
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) THEN TRUE
                    WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            u.id AS userId,
            u.nickname AS nickName,
            u.studentNumber,
            COUNT(pp.patentId) AS patentCount,
            SUM(IF(pp.isLeader = 1, 1, 0)) AS leaderPatentCount,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) 
                        THEN CAST(IFNULL(pc.score, '0') AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) 
                        THEN CAST(IFNULL(pc.score, '0') AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(pc.score, '0') AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM patent_participants pp
        INNER JOIN user u ON pp.participantId = u.id
        INNER JOIN patents p ON pp.patentId = p.id
        INNER JOIN patent_categories pc ON p.categoryId = pc.id
        WHERE 
            (patent_name = '' OR p.patentName LIKE CONCAT('%', patent_name, '%'))
            AND (nick_name = '' OR u.nickname LIKE CONCAT('%', nick_name, '%'))
            AND (
                CASE 
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) THEN TRUE
                    WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_project_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_project_ranking`;
delimiter ;;
CREATE PROCEDURE `get_project_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentProjectGuidance'
    LIMIT 1;

    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 直接查询而不使用临时表
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `key`,
            u.id AS userId,
            u.nickname AS userName,
            u.studentNumber,
            COUNT(pp.projectId) AS totalProjects,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM student_project_guidance_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN student_project_guidance_projects p ON pp.projectId = p.id
        LEFT JOIN student_project_guidance_projectLevels l ON p.levelId = l.id
        WHERE 
            (
                CASE
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
										WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `key`,
            u.id AS userId,
            u.nickname AS userName,
            u.studentNumber,
            COUNT(pp.projectId) AS totalProjects,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM student_project_guidance_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN student_project_guidance_projects p ON pp.projectId = p.id
        LEFT JOIN student_project_guidance_projectLevels l ON p.levelId = l.id
        WHERE 
            (
                CASE
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
										WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_research_projects_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_research_projects_total_score`;
delimiter ;;
CREATE PROCEDURE `get_research_projects_total_score`(IN range_param VARCHAR(50),    -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    
    -- 获取时间区间（与 get_research_project_ranking 相同的方式）
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'researchProjects'
    LIMIT 1;
    
    -- 按类型统计项目数量和总分
    SELECT 
        rp.type,
        -- 使用与现有代码相同的类型名称映射
        CASE rp.type
            WHEN 'national_fund' THEN '国家自然科学基金'
            WHEN 'social_science' THEN '社会科学基金' 
            WHEN 'enterprise_cooperation' THEN '企业合作项目'
            WHEN 'international_cooperation' THEN '国际合作项目'
            WHEN 'university_project' THEN '校级项目'
            WHEN 'other' THEN '其他类型'
            ELSE rp.type
        END AS typeName,
        COUNT(rp.id) AS count,
        SUM(CAST(IFNULL(rpl.score * rpp.allocationRatio, 0) AS DECIMAL(10,2))) AS totalScore
    FROM research_projects rp
    LEFT JOIN research_projects_levels_rules rpl ON rp.levelId = rpl.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN research_project_participants rpp ON rp.id = rpp.projectId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR rpp.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND rp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND rp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND rp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (rp.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (rp.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    GROUP BY rp.type
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(rp.id) AS totalProjects,
        CAST(SUM(IFNULL(rpl.score * rpp.allocationRatio, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM research_projects rp
    LEFT JOIN research_projects_levels_rules rpl ON rp.levelId = rpl.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN research_project_participants rpp ON rp.id = rpp.projectId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR rpp.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND rp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND rp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND rp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (rp.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (rp.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'researchProjects'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_research_project_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_research_project_ranking`;
delimiter ;;
CREATE PROCEDURE `get_research_project_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT,
    IN project_id VARCHAR(100),
    IN nick_name VARCHAR(100))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'researchProjects'
    LIMIT 1;

    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 直接查询而不使用临时表
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            u.id AS userId,
            u.nickname AS nickName,
            u.studentNumber,
            COUNT(pp.projectId) AS projectCount,
            SUM(IF(pp.isLeader = 1, 1, 0)) AS leaderProjectCount,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM research_project_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN research_projects p ON pp.projectId = p.id
        LEFT JOIN research_projects_levels_rules l ON p.levelId = l.id
        WHERE
            (project_id = '' OR p.projectId LIKE CONCAT('%', project_id, '%'))
            AND (nick_name = '' OR u.nickname LIKE CONCAT('%', nick_name, '%'))
            AND (
                CASE 
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            u.id AS userId,
            u.nickname AS nickName,
            u.studentNumber,
            COUNT(pp.projectId) AS projectCount,
            SUM(IF(pp.isLeader = 1, 1, 0)) AS leaderProjectCount,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM research_project_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN research_projects p ON pp.projectId = p.id
        LEFT JOIN research_projects_levels_rules l ON p.levelId = l.id
        WHERE
            (project_id = '' OR p.projectId LIKE CONCAT('%', project_id, '%'))
            AND (nick_name = '' OR u.nickname LIKE CONCAT('%', nick_name, '%'))
            AND (
                CASE 
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_student_award_guidance_awards_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_student_award_guidance_awards_total_score`;
delimiter ;;
CREATE PROCEDURE `get_student_award_guidance_awards_total_score`(IN range_param VARCHAR(50),    -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentAwardGuidance'
    LIMIT 1;
    
    -- 按级别统计获奖数量和总分
    SELECT 
        al.id AS levelId,
        al.levelName,
        COUNT(a.id) AS count,
        SUM(CAST(IFNULL(al.score * ap.allocationRatio, 0) AS DECIMAL(10,2))) AS totalScore
    FROM student_award_guidance_awards a
    LEFT JOIN student_award_guidance_awardLevels al ON a.levelId = al.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN student_award_guidance_participants ap ON a.id = ap.awardId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR ap.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    GROUP BY al.id, al.levelName
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(a.id) AS totalAwards,
        CAST(SUM(IFNULL(al.score * ap.allocationRatio, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM student_award_guidance_awards a
    LEFT JOIN student_award_guidance_awardLevels al ON a.levelId = al.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN student_award_guidance_participants ap ON a.id = ap.awardId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR ap.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'studentAwardGuidance'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_student_project_guidance_projects_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_student_project_guidance_projects_total_score`;
delimiter ;;
CREATE PROCEDURE `get_student_project_guidance_projects_total_score`(IN range_param VARCHAR(50),    -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE start_year INT;
    DECLARE end_year INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentProjectGuidance'
    LIMIT 1;
    
    -- 计算区间的年份范围
    SET start_year = YEAR(start_date);
    SET end_year = YEAR(end_date);
    
    -- 按级别统计项目数量和总分
    SELECT 
        pl.id AS levelId,
        pl.levelName,
        COUNT(p.id) AS count,
        SUM(CAST(IFNULL(pl.score * pp.allocationRatio, 0) AS DECIMAL(10,2))) AS totalScore
    FROM student_project_guidance_projects p
    LEFT JOIN student_project_guidance_projectLevels pl ON p.levelId = pl.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN student_project_guidance_participants pp ON p.id = pp.projectId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR pp.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤 - 同时检查approvalDate和startYear
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                               AND (p.startYear BETWEEN start_year AND end_year) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date 
                               OR p.startYear NOT BETWEEN start_year AND end_year) THEN TRUE
            ELSE FALSE
        END
    GROUP BY pl.id, pl.levelName
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(p.id) AS totalProjects,
        CAST(SUM(IFNULL(pl.score * pp.allocationRatio, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM student_project_guidance_projects p
    LEFT JOIN student_project_guidance_projectLevels pl ON p.levelId = pl.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN student_project_guidance_participants pp ON p.id = pp.projectId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR pp.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤 - 同时检查approvalDate和startYear
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                               AND (p.startYear BETWEEN start_year AND end_year) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date 
                               OR p.startYear NOT BETWEEN start_year AND end_year) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'studentProjectGuidance'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_teacher_award_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_teacher_award_ranking`;
delimiter ;;
CREATE PROCEDURE `get_teacher_award_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentAwardGuidance'
    LIMIT 1;

    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 直接查询而不使用临时表
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `key`,
            u.id AS userId,
            u.nickname AS userName,
            u.studentNumber,
            COUNT(p.awardId) AS totalAwards,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                    WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM student_award_guidance_participants p
        INNER JOIN user u ON p.userId = u.id
        INNER JOIN student_award_guidance_awards a ON p.awardId = a.id
        LEFT JOIN student_award_guidance_awardLevels l ON a.levelId = l.id
        WHERE 
            (
                CASE
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `key`,
            u.id AS userId,
            u.nickname AS userName,
            u.studentNumber,
            COUNT(p.awardId) AS totalAwards,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                    WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM student_award_guidance_participants p
        INNER JOIN user u ON p.userId = u.id
        INNER JOIN student_award_guidance_awards a ON p.awardId = a.id
        LEFT JOIN student_award_guidance_awardLevels l ON a.levelId = l.id
        WHERE 
            (
                CASE
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_teacher_conference_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_teacher_conference_ranking`;
delimiter ;;
CREATE PROCEDURE `get_teacher_conference_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'conferences'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 根据是否导出所有数据选择查询方式
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `key`,
            userId,
            nickName AS userName,
            studentNumber AS employeeNumber,
            COUNT(conferenceId) AS totalConferences,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (holdTime BETWEEN start_date AND end_date) THEN score * allocationRatio
                    WHEN range_param = 'out' AND (holdTime NOT BETWEEN start_date AND end_date) THEN score * allocationRatio
                    WHEN range_param = 'all' THEN score * allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM (
            SELECT 
                u.id AS userId,
                u.nickname AS nickName,
                u.username,
                u.studentNumber,
                c.id AS conferenceId,
                c.holdTime,
                cp.allocationRatio,
                CAST(IFNULL(cl.score, '0') AS DECIMAL(10,2)) AS score
            FROM conferences_participants cp
            INNER JOIN user u ON cp.participantId = u.id
            INNER JOIN conferences c ON cp.conferenceId = c.id
            LEFT JOIN conferences_levels cl ON c.levelId = cl.id
            WHERE 
                -- 筛选审核状态
                (review_status_param = 'all' OR 
                (review_status_param = 'reviewed' AND c.ifReviewer = 1) OR 
                (review_status_param = 'rejected' AND c.ifReviewer = 0) OR
                (review_status_param = 'pending' AND c.ifReviewer IS NULL))
        ) AS filtered_data
        WHERE 
            (range_param = 'all') OR
            (range_param = 'in' AND (holdTime BETWEEN start_date AND end_date)) OR
            (range_param = 'out' AND (holdTime NOT BETWEEN start_date AND end_date))
        GROUP BY userId, nickName, username, studentNumber
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `key`,
            userId,
            nickName AS userName,
            studentNumber AS employeeNumber,
            COUNT(conferenceId) AS totalConferences,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (holdTime BETWEEN start_date AND end_date) THEN score * allocationRatio
                    WHEN range_param = 'out' AND (holdTime NOT BETWEEN start_date AND end_date) THEN score * allocationRatio
                    WHEN range_param = 'all' THEN score * allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM (
            SELECT 
                u.id AS userId,
                u.nickname AS nickName,
                u.username,
                u.studentNumber,
                c.id AS conferenceId,
                c.holdTime,
                cp.allocationRatio,
                CAST(IFNULL(cl.score, '0') AS DECIMAL(10,2)) AS score
            FROM conferences_participants cp
            INNER JOIN user u ON cp.participantId = u.id
            INNER JOIN conferences c ON cp.conferenceId = c.id
            LEFT JOIN conferences_levels cl ON c.levelId = cl.id
            WHERE 
                -- 筛选审核状态
                (review_status_param = 'all' OR 
                (review_status_param = 'reviewed' AND c.ifReviewer = 1) OR 
                (review_status_param = 'rejected' AND c.ifReviewer = 0) OR
                (review_status_param = 'pending' AND c.ifReviewer IS NULL))
        ) AS filtered_data
        WHERE 
            (range_param = 'all') OR
            (range_param = 'in' AND (holdTime BETWEEN start_date AND end_date)) OR
            (range_param = 'out' AND (holdTime NOT BETWEEN start_date AND end_date))
        GROUP BY userId, nickName, username, studentNumber
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_teacher_project_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_teacher_project_ranking`;
delimiter ;;
CREATE PROCEDURE `get_teacher_project_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentProjectGuidance'
    LIMIT 1;

    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 直接查询而不使用临时表
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `key`,
            u.id AS userId,
            u.nickname AS userName,
            u.studentNumber,
            COUNT(pp.projectId) AS totalProjects,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM student_project_guidance_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN student_project_guidance_projects p ON pp.projectId = p.id
        LEFT JOIN student_project_guidance_projectLevels l ON p.levelId = l.id
        WHERE 
            (
                CASE
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `key`,
            u.id AS userId,
            u.nickname AS userName,
            u.studentNumber,
            COUNT(pp.projectId) AS totalProjects,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM student_project_guidance_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN student_project_guidance_projects p ON pp.projectId = p.id
        LEFT JOIN student_project_guidance_projectLevels l ON p.levelId = l.id
        WHERE 
            (
                CASE
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                    WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_teaching_reform_projects_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_teaching_reform_projects_total_score`;
delimiter ;;
CREATE PROCEDURE `get_teaching_reform_projects_total_score`(IN range_param VARCHAR(50),    -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'teachingReformProjects'
    LIMIT 1;
    
    -- 按级别统计项目数量和总分
    SELECT 
        pl.id AS levelId,
        pl.levelName,
        COUNT(p.id) AS count,
        SUM(CAST(IFNULL(pl.score * tp.allocationRatio, 0) AS DECIMAL(10,2))) AS totalScore
    FROM teaching_reform_projects p
    LEFT JOIN teaching_reform_projectLevels pl ON p.levelId = pl.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN teaching_reform_participants tp ON p.id = tp.projectId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR tp.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    GROUP BY pl.id, pl.levelName
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(p.id) AS totalProjects,
        CAST(SUM(IFNULL(pl.score * tp.allocationRatio, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM teaching_reform_projects p
    LEFT JOIN teaching_reform_projectLevels pl ON p.levelId = pl.id
    -- 根据userId_param是否为NULL决定是否关联参与者表
    LEFT JOIN teaching_reform_participants tp ON p.id = tp.projectId
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR tp.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'teachingReformProjects'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_teaching_reform_project_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_teaching_reform_project_ranking`;
delimiter ;;
CREATE PROCEDURE `get_teaching_reform_project_ranking`(IN range_param VARCHAR(10),
    IN review_status_param VARCHAR(20),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval 
    WHERE name = 'teachingReformProjects' 
    LIMIT 1;

    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 直接查询而不使用临时表
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            u.id AS userId,
            u.nickname AS nickName,
            u.studentNumber,
            COUNT(pp.projectId) AS totalProjects,
            SUM(IF(pp.isLeader = 1, 1, 0)) AS leaderProjectCount,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM teaching_reform_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN teaching_reform_projects p ON pp.projectId = p.id
        LEFT JOIN teaching_reform_projectLevels l ON p.levelId = l.id
        WHERE 
            (
                CASE
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
										WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            u.id AS userId,
            u.nickname AS nickName,
            u.studentNumber,
            COUNT(pp.projectId) AS totalProjects,
            SUM(IF(pp.isLeader = 1, 1, 0)) AS leaderProjectCount,
            CAST(SUM(
                CASE 
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    WHEN range_param = 'all' 
                        THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                    ELSE 0 
                END
            ) AS DECIMAL(10,2)) AS totalScore
        FROM teaching_reform_participants pp
        INNER JOIN user u ON pp.userId = u.id
        INNER JOIN teaching_reform_projects p ON pp.projectId = p.id
        LEFT JOIN teaching_reform_projectLevels l ON p.levelId = l.id
        WHERE 
            (
                CASE
                    WHEN review_status_param = 'all' THEN TRUE
                    WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                    WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
										WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                    ELSE FALSE
                END
            )
            AND (
                CASE
                    WHEN range_param = 'all' THEN TRUE
                    WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                    WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                    ELSE FALSE
                END
            )
        GROUP BY u.id, u.nickname, u.username, u.studentNumber
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_teaching_research_awards_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_teaching_research_awards_total_score`;
delimiter ;;
CREATE PROCEDURE `get_teaching_research_awards_total_score`(IN p_range VARCHAR(10), -- 'in', 'out', 'all'
    IN p_review_status VARCHAR(10),
    IN p_user_id VARCHAR(36))
BEGIN
    DECLARE v_start_time DATE;
    DECLARE v_end_time DATE;
    DECLARE v_time_interval_name VARCHAR(50);
    
    -- 获取时间区间信息
    SELECT startTime, endTime, name INTO v_start_time, v_end_time, v_time_interval_name
    FROM time_interval
    WHERE name = 'teachingResearchAwards' LIMIT 1;
    
    -- 构建奖励过滤条件
    SET @filter_condition = '';
    
    -- 添加用户ID过滤（当p_user_id为NULL时不过滤）
    IF p_user_id IS NOT NULL THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND trap.participantId = "', p_user_id, '"');
    END IF;
    
    -- 根据审核状态构建过滤条件
    IF p_review_status = 'reviewed' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tra.ifReviewer = 1');
    ELSEIF p_review_status = 'rejected' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tra.ifReviewer = 0');
    ELSEIF p_review_status = 'pending' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tra.ifReviewer IS NULL');
    END IF;
    
    -- 添加时间范围过滤
    IF p_range = 'in' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tra.awardTime >= "', v_start_time, '" AND tra.awardTime <= "', v_end_time, '"');
    ELSEIF p_range = 'out' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND (tra.awardTime < "', v_start_time, '" OR tra.awardTime > "', v_end_time, '")');
    END IF;
    
    -- 第一个结果集：按级别统计
    SET @sql_level_stats = CONCAT('
        SELECT 
            tral.id AS levelId,
            tral.levelName,
            COUNT(tra.id) AS count,
            SUM(tral.score * trap.allocationRatio) AS totalScore
        FROM teaching_research_awards tra
        LEFT JOIN teaching_research_award_levels tral ON tra.awardLevelId = tral.id
        LEFT JOIN teaching_research_award_participants trap ON tra.id = trap.awardId
        WHERE 1=1', @filter_condition, '
        GROUP BY tral.id, tral.levelName
        ORDER BY tral.id'
    );
    
    PREPARE stmt FROM @sql_level_stats;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 第二个结果集：总体统计
    SET @sql_total_stats = CONCAT('
        SELECT 
            COUNT(tra.id) AS totalAwards,
            SUM(tral.score * trap.allocationRatio) AS totalScore
        FROM teaching_research_awards tra
        LEFT JOIN teaching_research_award_levels tral ON tra.awardLevelId = tral.id
        LEFT JOIN teaching_research_award_participants trap ON tra.id = trap.awardId
        WHERE 1=1', @filter_condition
    );
    
    PREPARE stmt FROM @sql_total_stats;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 第三个结果集：时间区间信息
    SELECT v_start_time AS startTime, v_end_time AS endTime, v_time_interval_name AS name;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_teaching_workloads_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_teaching_workloads_total_score`;
delimiter ;;
CREATE PROCEDURE `get_teaching_workloads_total_score`(IN p_range VARCHAR(10), -- 'in', 'out', 'all'
    IN p_review_status VARCHAR(10),
    IN p_user_id VARCHAR(36))
BEGIN
    DECLARE v_start_time DATE;
    DECLARE v_end_time DATE;
    DECLARE v_time_interval_name VARCHAR(50);
    
    -- 获取时间区间信息
    SELECT startTime, endTime, name INTO v_start_time, v_end_time, v_time_interval_name
    FROM time_interval
    WHERE name = 'teachingWorkloads' LIMIT 1;
    
    -- 构建工作量过滤条件
    SET @filter_condition = '';
    
    -- 添加用户ID过滤（当p_user_id为NULL时不过滤）
    IF p_user_id IS NOT NULL THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND twp.participantId = "', p_user_id, '"');
    END IF;
    
    -- 根据审核状态构建过滤条件
    IF p_review_status = 'reviewed' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tw.ifReviewer = 1');
    ELSEIF p_review_status = 'rejected' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tw.ifReviewer = 0');
    ELSEIF p_review_status = 'pending' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tw.ifReviewer IS NULL');
    END IF;
    
    -- 添加时间范围过滤
    IF p_range = 'in' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tw.semester >= "', v_start_time, '" AND tw.semester <= "', v_end_time, '"');
    ELSEIF p_range = 'out' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND (tw.semester < "', v_start_time, '" OR tw.semester > "', v_end_time, '")');
    END IF;
    
    -- 第一个结果集：按级别统计
    SET @sql_level_stats = CONCAT('
        SELECT 
            twl.id AS levelId,
            twl.categoryName AS levelName,
            COUNT(tw.id) AS count,
            SUM(twl.score * twp.allocationRatio) AS totalScore
        FROM teaching_workloads tw
        LEFT JOIN teaching_workload_level twl ON tw.categoryId = twl.id
        LEFT JOIN teaching_workload_participants twp ON tw.id = twp.workloadId
        WHERE 1=1', @filter_condition, '
        GROUP BY twl.id, twl.categoryName
        ORDER BY twl.id'
    );
    
    PREPARE stmt FROM @sql_level_stats;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 第二个结果集：总体统计
    SET @sql_total_stats = CONCAT('
        SELECT 
            COUNT(tw.id) AS totalWorkloads,
            SUM(twl.score * twp.allocationRatio) AS totalScore
        FROM teaching_workloads tw
        LEFT JOIN teaching_workload_level twl ON tw.categoryId = twl.id
        LEFT JOIN teaching_workload_participants twp ON tw.id = twp.workloadId
        WHERE 1=1', @filter_condition
    );
    
    PREPARE stmt FROM @sql_total_stats;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 第三个结果集：时间区间信息
    SELECT v_start_time AS startTime, v_end_time AS endTime, v_time_interval_name AS name;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_textbooks_total_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_textbooks_total_score`;
delimiter ;;
CREATE PROCEDURE `get_textbooks_total_score`(IN range_param VARCHAR(50),    -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),
    IN userId_param VARCHAR(36))
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'textbooks'
    LIMIT 1;
    
    -- 按类别统计教材与著作数量和总分
    SELECT 
        tc.id AS categoryId,
        tc.categoryAndPosition AS categoryName,
        COUNT(t.id) AS count,
        SUM(CAST(IFNULL(tc.score, 0) AS DECIMAL(10,2))) AS totalScore
    FROM textbooks t
    LEFT JOIN textbook_categories tc ON t.categoryId = tc.id
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR t.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND t.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND t.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND t.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (t.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (t.publishDate < start_date OR t.publishDate > end_date) THEN TRUE
            ELSE FALSE
        END
    GROUP BY tc.id, tc.categoryAndPosition
    ORDER BY totalScore DESC;
    
    -- 计算总体统计
    SELECT 
        COUNT(t.id) AS totalTextbooks,
        CAST(SUM(IFNULL(tc.score, 0)) AS DECIMAL(10,2)) AS totalScore
    FROM textbooks t
    LEFT JOIN textbook_categories tc ON t.categoryId = tc.id
    WHERE
        -- 用户ID过滤（当userId_param为NULL时不过滤）
        (userId_param IS NULL OR t.userId = userId_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND t.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND t.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND t.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (t.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (t.publishDate < start_date OR t.publishDate > end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'textbooks'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_academic_appointments_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_academic_appointments_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_academic_appointments_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),    -- 'approved', 'rejected', 'pending', 'reviewed', 'all'
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'academicAppointments'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取任职总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM academicAppointments a
    WHERE 
        a.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (
                -- 开始年份在范围内
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                -- 结束年份在范围内
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                -- 跨越整个范围
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            WHEN range_param = 'out' AND NOT (
                -- 开始年份在范围内
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                -- 结束年份在范围内
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                -- 跨越整个范围
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户任职详情和得分
    SELECT 
        a.id AS appointmentId,
        a.associationName,
        a.position,
        a.startYear,
        a.endYear,
        a.levelId,
        al.levelName,
        CAST(al.score AS DECIMAL(10,2)) AS score,
        a.remark,
        a.ifReviewer,
        CASE 
            WHEN a.ifReviewer = 1 THEN '已通过'
            WHEN a.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        ur.nickname AS reviewerName,
        a.reviewComment,
        a.attachmentUrl,
        a.createdAt,
        a.updatedAt
    FROM academicAppointments a
    LEFT JOIN associationLevels al ON a.levelId = al.id
    LEFT JOIN user ur ON a.reviewerId = ur.id
    WHERE 
        a.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            WHEN range_param = 'out' AND NOT (
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            ELSE FALSE
        END
    ORDER BY a.startYear DESC, score DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户任职总体统计
    SELECT 
        COUNT(a.id) AS totalAppointments,
        CAST(SUM(IFNULL(al.score, 0)) AS DECIMAL(10,2)) AS totalScore,
        SUM(IF(a.endYear IS NULL, 1, 0)) AS currentAppointments, -- 当前正在进行的任职（结束年份为空）
        SUM(IF(a.ifReviewer = 1, 1, 0)) AS approvedAppointments, -- 已通过审核的任职数
        -- 计算活跃任职（与时间区间有交集）
        SUM(IF(
            (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
            (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
            (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date))),
            1, 0
        )) AS activeAppointments
    FROM academicAppointments a
    LEFT JOIN associationLevels al ON a.levelId = al.id
    WHERE 
        a.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            WHEN range_param = 'out' AND NOT (
                (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.endYear IS NOT NULL AND a.endYear BETWEEN YEAR(start_date) AND YEAR(end_date)) OR
                (a.startYear <= YEAR(start_date) AND (a.endYear IS NULL OR a.endYear >= YEAR(end_date)))
            ) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'academicAppointments'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_appointments_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_appointments_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_appointments_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'academicAppointments'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_appointments (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        appointmentCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_appointments;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_appointments (`rank`, userId, nickName, studentNumber, appointmentCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        a.userId AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(a.id) AS appointmentCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (a.startYear BETWEEN YEAR(start_date) AND YEAR(end_date)) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2))
                WHEN range_param = 'out' AND (a.startYear NOT BETWEEN YEAR(start_date) AND YEAR(end_date)) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2))
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2))
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM academicAppointments a
    INNER JOIN user u ON a.userId = u.id
    LEFT JOIN associationLevels l ON a.levelId = l.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY a.userId, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式（只有在单独执行时才返回结果）
    IF is_export_all != 2 THEN
        IF is_export_all = 1 THEN
            SELECT * FROM temp_appointments ORDER BY totalScore DESC;
        ELSE
            SELECT * FROM temp_appointments ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
        END IF;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_awards_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_awards_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_awards_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentAwardGuidance'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_awards (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        awardCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_awards;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_awards (`rank`, userId, nickName, studentNumber, awardCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(p.awardId) AS awardCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * p.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM student_award_guidance_participants p
    INNER JOIN user u ON p.userId = u.id
    INNER JOIN student_award_guidance_awards a ON p.awardId = a.id
    LEFT JOIN student_award_guidance_awardLevels l ON a.levelId = l.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) THEN TRUE
                WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式（保留原来的SELECT输出）
    IF is_export_all = 1 THEN
        SELECT * FROM temp_awards ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_awards ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_combined_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_combined_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_combined_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    -- 声明变量必须放在所有操作之前
    DECLARE offset_val INT;
    
    -- 设置偏移量
    SET offset_val = (page_param - 1) * limit_param;
    
    -- 创建临时表存储最终的合并结果
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_combined (
        userId VARCHAR(50) PRIMARY KEY,
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        totalItemCount INT DEFAULT 0,
        totalScore DECIMAL(10,2) DEFAULT 0,
        appointmentCount INT DEFAULT 0,
        appointmentScore DECIMAL(10,2) DEFAULT 0,
        awardCount INT DEFAULT 0,
        awardScore DECIMAL(10,2) DEFAULT 0,
        conferenceCount INT DEFAULT 0,
        conferenceScore DECIMAL(10,2) DEFAULT 0,
        paperCount INT DEFAULT 0,
        paperScore DECIMAL(10,2) DEFAULT 0,
        patentCount INT DEFAULT 0,
        patentScore DECIMAL(10,2) DEFAULT 0,
        researchCount INT DEFAULT 0,
        researchScore DECIMAL(10,2) DEFAULT 0,
        studentProjectCount INT DEFAULT 0,
        studentProjectScore DECIMAL(10,2) DEFAULT 0,
        teachingProjectCount INT DEFAULT 0,
        teachingProjectScore DECIMAL(10,2) DEFAULT 0,
        textbookCount INT DEFAULT 0,
        textbookScore DECIMAL(10,2) DEFAULT 0,
        teachingWorkloadCount INT DEFAULT 0,
        teachingWorkloadScore DECIMAL(10,2) DEFAULT 0,
        teachingResearchAwardCount INT DEFAULT 0,
        teachingResearchAwardScore DECIMAL(10,2) DEFAULT 0
    );
    
    -- 创建临时表存储各个排名结果
    -- 1. 学术任职
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_appointments (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        appointmentCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 2. 学生获奖指导
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_awards (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        awardCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 3. 学术会议
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_conferences (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        conferenceCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 4. 高水平论文
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_papers (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        paperCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 5. 专利
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_patents (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        patentCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 6. 科研项目
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_research_projects (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        projectCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 7. 学生项目指导
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_student_projects (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        projectCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 8. 教学改革项目
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_teaching_projects (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        projectCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 9. 教材
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_textbooks (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        textbookCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 10. 教学工作量
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_teaching_workloads (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        workloadCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 11. 教学科研奖励
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_teaching_research_awards (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        awardCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 调用各个存储过程并收集结果到临时表
    CALL get_user_appointments_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_awards_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_conferences_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_papers_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_patents_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_research_projects_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_student_projects_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_teaching_projects_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_textbooks_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_teaching_workloads_ranking(range_param, review_status_param, 100000, 1, 1);
    CALL get_user_teaching_research_awards_ranking(range_param, review_status_param, 100000, 1, 1);
    
    -- 直接将各项结果插入到合并表，使用INSERT ... ON DUPLICATE KEY UPDATE
    -- 1. 学术任职
    INSERT INTO temp_combined (userId, nickName, studentNumber, appointmentCount, appointmentScore)
    SELECT userId, nickName, studentNumber, appointmentCount, totalScore 
    FROM temp_appointments
    ON DUPLICATE KEY UPDATE 
        nickName = temp_appointments.nickName,
        studentNumber = temp_appointments.studentNumber,
        appointmentCount = temp_appointments.appointmentCount,
        appointmentScore = temp_appointments.totalScore;
    
    -- 2. 学生获奖指导
    INSERT INTO temp_combined (userId, nickName, studentNumber, awardCount, awardScore)
    SELECT userId, nickName, studentNumber, awardCount, totalScore 
    FROM temp_awards
    ON DUPLICATE KEY UPDATE 
        nickName = temp_awards.nickName,
        studentNumber = temp_awards.studentNumber,
        awardCount = temp_awards.awardCount,
        awardScore = temp_awards.totalScore;
    
    -- 3. 学术会议
    INSERT INTO temp_combined (userId, nickName, studentNumber, conferenceCount, conferenceScore)
    SELECT userId, nickName, studentNumber, conferenceCount, totalScore 
    FROM temp_conferences
    ON DUPLICATE KEY UPDATE 
        nickName = temp_conferences.nickName,
        studentNumber = temp_conferences.studentNumber,
        conferenceCount = temp_conferences.conferenceCount,
        conferenceScore = temp_conferences.totalScore;
    
    -- 4. 高水平论文
    INSERT INTO temp_combined (userId, nickName, studentNumber, paperCount, paperScore)
    SELECT userId, nickName, studentNumber, paperCount, totalScore 
    FROM temp_papers
    ON DUPLICATE KEY UPDATE 
        nickName = temp_papers.nickName,
        studentNumber = temp_papers.studentNumber,
        paperCount = temp_papers.paperCount,
        paperScore = temp_papers.totalScore;
    
    -- 5. 专利
    INSERT INTO temp_combined (userId, nickName, studentNumber, patentCount, patentScore)
    SELECT userId, nickName, studentNumber, patentCount, totalScore 
    FROM temp_patents
    ON DUPLICATE KEY UPDATE 
        nickName = temp_patents.nickName,
        studentNumber = temp_patents.studentNumber,
        patentCount = temp_patents.patentCount,
        patentScore = temp_patents.totalScore;
    
    -- 6. 科研项目
    INSERT INTO temp_combined (userId, nickName, studentNumber, researchCount, researchScore)
    SELECT userId, nickName, studentNumber, projectCount, totalScore 
    FROM temp_research_projects
    ON DUPLICATE KEY UPDATE 
        nickName = temp_research_projects.nickName,
        studentNumber = temp_research_projects.studentNumber,
        researchCount = temp_research_projects.projectCount,
        researchScore = temp_research_projects.totalScore;
    
    -- 7. 学生项目指导
    INSERT INTO temp_combined (userId, nickName, studentNumber, studentProjectCount, studentProjectScore)
    SELECT userId, nickName, studentNumber, projectCount, totalScore 
    FROM temp_student_projects
    ON DUPLICATE KEY UPDATE 
        nickName = temp_student_projects.nickName,
        studentNumber = temp_student_projects.studentNumber,
        studentProjectCount = temp_student_projects.projectCount,
        studentProjectScore = temp_student_projects.totalScore;
    
    -- 8. 教学改革项目
    INSERT INTO temp_combined (userId, nickName, studentNumber, teachingProjectCount, teachingProjectScore)
    SELECT userId, nickName, studentNumber, projectCount, totalScore 
    FROM temp_teaching_projects
    ON DUPLICATE KEY UPDATE 
        nickName = temp_teaching_projects.nickName,
        studentNumber = temp_teaching_projects.studentNumber,
        teachingProjectCount = temp_teaching_projects.projectCount,
        teachingProjectScore = temp_teaching_projects.totalScore;
    
    -- 9. 教材
    INSERT INTO temp_combined (userId, nickName, studentNumber, textbookCount, textbookScore)
    SELECT userId, nickName, studentNumber, textbookCount, totalScore 
    FROM temp_textbooks
    ON DUPLICATE KEY UPDATE 
        nickName = temp_textbooks.nickName,
        studentNumber = temp_textbooks.studentNumber,
        textbookCount = temp_textbooks.textbookCount,
        textbookScore = temp_textbooks.totalScore;
    
    -- 10. 教学工作量
    INSERT INTO temp_combined (userId, nickName, studentNumber, teachingWorkloadCount, teachingWorkloadScore)
    SELECT userId, nickName, studentNumber, workloadCount, totalScore 
    FROM temp_teaching_workloads
    ON DUPLICATE KEY UPDATE 
        nickName = temp_teaching_workloads.nickName,
        studentNumber = temp_teaching_workloads.studentNumber,
        teachingWorkloadCount = temp_teaching_workloads.workloadCount,
        teachingWorkloadScore = temp_teaching_workloads.totalScore;
    
    -- 11. 教学科研奖励
    INSERT INTO temp_combined (userId, nickName, studentNumber, teachingResearchAwardCount, teachingResearchAwardScore)
    SELECT userId, nickName, studentNumber, awardCount, totalScore 
    FROM temp_teaching_research_awards
    ON DUPLICATE KEY UPDATE 
        nickName = temp_teaching_research_awards.nickName,
        studentNumber = temp_teaching_research_awards.studentNumber,
        teachingResearchAwardCount = temp_teaching_research_awards.awardCount,
        teachingResearchAwardScore = temp_teaching_research_awards.totalScore;
    
    -- 计算总分和总项目数
    UPDATE temp_combined
    SET 
        totalItemCount = appointmentCount + awardCount + conferenceCount + 
                        paperCount + patentCount + researchCount + 
                        studentProjectCount + teachingProjectCount + textbookCount +
                        teachingWorkloadCount + teachingResearchAwardCount,
        totalScore = appointmentScore + awardScore + conferenceScore + 
                    paperScore + patentScore + researchScore + 
                    studentProjectScore + teachingProjectScore + textbookScore +
                    teachingWorkloadScore + teachingResearchAwardScore;
    
    -- 设置行号变量
    SET @row_number = 0;

    -- 尝试将数据写入last_result_table（供update_ranking_tables.sql使用）
    -- 使用简单的错误处理，避免表不存在时报错
    BEGIN
        DECLARE CONTINUE HANDLER FOR 1146 BEGIN END; -- 忽略"表不存在"错误
        
        -- 直接尝试操作表，如果表不存在会触发错误但会被忽略
        DELETE FROM last_result_table;
        
        INSERT INTO last_result_table (
            userId, nickName, studentNumber, totalItemCount, totalScore,
            appointmentCount, appointmentScore, awardCount, awardScore,
            conferenceCount, conferenceScore, paperCount, paperScore,
            patentCount, patentScore, researchCount, researchScore, 
            studentProjectCount, studentProjectScore, 
            teachingProjectCount, teachingProjectScore, 
            textbookCount, textbookScore, 
            teachingWorkloadCount, teachingWorkloadScore,
            teachingResearchAwardCount, teachingResearchAwardScore
        )
        SELECT 
            userId, nickName, studentNumber, totalItemCount, totalScore,
            appointmentCount, appointmentScore, awardCount, awardScore,
            conferenceCount, conferenceScore, paperCount, paperScore,
            patentCount, patentScore, researchCount, researchScore,
            studentProjectCount, studentProjectScore,
            teachingProjectCount, teachingProjectScore,
            textbookCount, textbookScore,
            teachingWorkloadCount, teachingWorkloadScore,
            teachingResearchAwardCount, teachingResearchAwardScore
        FROM temp_combined
        ORDER BY totalScore DESC;
    END;

    -- 根据是否导出全部决定查询方式
    IF is_export_all = 1 THEN
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            userId,
            nickName,
            studentNumber,
            totalItemCount,
            CAST(totalScore AS DECIMAL(10,2)) AS totalScore,
            CAST(appointmentScore AS DECIMAL(10,2)) AS appointmentScore,
            CAST(awardScore AS DECIMAL(10,2)) AS awardScore,
            CAST(conferenceScore AS DECIMAL(10,2)) AS conferenceScore,
            CAST(paperScore AS DECIMAL(10,2)) AS paperScore,
            CAST(patentScore AS DECIMAL(10,2)) AS patentScore,
            CAST(researchScore AS DECIMAL(10,2)) AS researchScore,
            CAST(studentProjectScore AS DECIMAL(10,2)) AS studentProjectScore,
            CAST(teachingProjectScore AS DECIMAL(10,2)) AS teachingProjectScore,
            CAST(textbookScore AS DECIMAL(10,2)) AS textbookScore,
            CAST(teachingWorkloadScore AS DECIMAL(10,2)) AS teachingWorkloadScore,
            CAST(teachingResearchAwardScore AS DECIMAL(10,2)) AS teachingResearchAwardScore,
            JSON_OBJECT(
                '学术任职', appointmentScore,
                '学生获奖指导', awardScore,
                '学术会议', conferenceScore,
                '高水平论文', paperScore,
                '专利', patentScore,
                '科研项目', researchScore,
                '学生项目指导', studentProjectScore,
                '教学改革项目', teachingProjectScore,
                '教材', textbookScore,
                '教学工作量', teachingWorkloadScore,
                '教学科研奖励', teachingResearchAwardScore
            ) AS scoreDetails
        FROM temp_combined
        ORDER BY totalScore DESC;
    ELSE
        SELECT 
            (@row_number:=@row_number + 1) AS `rank`,
            userId,
            nickName,
            studentNumber,
            totalItemCount,
            CAST(totalScore AS DECIMAL(10,2)) AS totalScore,
            CAST(appointmentScore AS DECIMAL(10,2)) AS appointmentScore,
            CAST(awardScore AS DECIMAL(10,2)) AS awardScore,
            CAST(conferenceScore AS DECIMAL(10,2)) AS conferenceScore,
            CAST(paperScore AS DECIMAL(10,2)) AS paperScore,
            CAST(patentScore AS DECIMAL(10,2)) AS patentScore,
            CAST(researchScore AS DECIMAL(10,2)) AS researchScore,
            CAST(studentProjectScore AS DECIMAL(10,2)) AS studentProjectScore,
            CAST(teachingProjectScore AS DECIMAL(10,2)) AS teachingProjectScore,
            CAST(textbookScore AS DECIMAL(10,2)) AS textbookScore,
            CAST(teachingWorkloadScore AS DECIMAL(10,2)) AS teachingWorkloadScore,
            CAST(teachingResearchAwardScore AS DECIMAL(10,2)) AS teachingResearchAwardScore,
            JSON_OBJECT(
                '学术任职', appointmentScore,
                '学生获奖指导', awardScore,
                '学术会议', conferenceScore,
                '高水平论文', paperScore,
                '专利', patentScore,
                '科研项目', researchScore,
                '学生项目指导', studentProjectScore,
                '教学改革项目', teachingProjectScore,
                '教材', textbookScore,
                '教学工作量', teachingWorkloadScore,
                '教学科研奖励', teachingResearchAwardScore
            ) AS scoreDetails
        FROM temp_combined
        ORDER BY totalScore DESC
        LIMIT limit_param OFFSET offset_val;
    END IF;
    
    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_combined;
    DROP TEMPORARY TABLE IF EXISTS temp_appointments;
    DROP TEMPORARY TABLE IF EXISTS temp_awards;
    DROP TEMPORARY TABLE IF EXISTS temp_conferences;
    DROP TEMPORARY TABLE IF EXISTS temp_papers;
    DROP TEMPORARY TABLE IF EXISTS temp_patents;
    DROP TEMPORARY TABLE IF EXISTS temp_research_projects;
    DROP TEMPORARY TABLE IF EXISTS temp_student_projects;
    DROP TEMPORARY TABLE IF EXISTS temp_teaching_projects;
    DROP TEMPORARY TABLE IF EXISTS temp_textbooks;
    DROP TEMPORARY TABLE IF EXISTS temp_teaching_workloads;
    DROP TEMPORARY TABLE IF EXISTS temp_teaching_research_awards;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_conferences_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_conferences_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_conferences_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),    -- 'approved', 'rejected', 'pending', 'reviewed', 'all'
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'conferences'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取会议总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM conferences c
    LEFT JOIN conferences_participants cp ON c.id = cp.conferenceId
    WHERE 
        (cp.participantId = user_id_param OR c.firstResponsibleId = user_id_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND c.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND c.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND c.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (c.holdTime BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (c.holdTime NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户会议详情和得分
    SELECT 
        c.id AS conferenceId,
        c.conferenceName,
        c.holdTime,
        cl.id AS levelId,
        cl.levelName,
        cl.score AS baseScore,
        cp.allocationRatio,
        cp.isLeader,
        CAST(cl.score * cp.allocationRatio AS DECIMAL(10,2)) AS actualScore,
        c.remark,
        c.ifReviewer,
        CASE 
            WHEN c.ifReviewer = 1 THEN '已通过'
            WHEN c.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        ur.nickname AS reviewerName,
        c.reviewComment,
        c.createdAt,
        c.updatedAt
    FROM conferences c
    LEFT JOIN conferences_levels cl ON c.levelId = cl.id
    LEFT JOIN conferences_participants cp ON c.id = cp.conferenceId AND cp.participantId = user_id_param
    LEFT JOIN user ur ON c.reviewerId = ur.id
    WHERE 
        (cp.participantId = user_id_param OR c.firstResponsibleId = user_id_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND c.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND c.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND c.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (c.holdTime BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (c.holdTime NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    ORDER BY c.holdTime DESC, actualScore DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户会议总体统计
    SELECT 
        COUNT(c.id) AS totalConferences,
        SUM(IF(cp.isLeader = 1 OR c.firstResponsibleId = user_id_param, 1, 0)) AS leaderConferenceCount,
        CAST(SUM(
            CASE
                WHEN cp.participantId = user_id_param THEN IFNULL(cl.score, 0) * cp.allocationRatio
                WHEN c.firstResponsibleId = user_id_param AND cp.participantId IS NULL THEN IFNULL(cl.score, 0)
                ELSE 0
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM conferences c
    LEFT JOIN conferences_levels cl ON c.levelId = cl.id
    LEFT JOIN conferences_participants cp ON c.id = cp.conferenceId AND cp.participantId = user_id_param
    WHERE 
        (cp.participantId = user_id_param OR c.firstResponsibleId = user_id_param)
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND c.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND c.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND c.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (c.holdTime BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (c.holdTime NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'conferences'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_conferences_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_conferences_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_conferences_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'conferences'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_conferences (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        conferenceCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_conferences;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_conferences (`rank`, userId, nickName, studentNumber, conferenceCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(cp.conferenceId) AS conferenceCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (c.holdTime BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(cl.score, 0) AS DECIMAL(10,2)) * cp.allocationRatio
                WHEN range_param = 'out' AND (c.holdTime NOT BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(cl.score, 0) AS DECIMAL(10,2)) * cp.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(cl.score, 0) AS DECIMAL(10,2)) * cp.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM conferences_participants cp
    INNER JOIN user u ON cp.participantId = u.id
    INNER JOIN conferences c ON cp.conferenceId = c.id
    LEFT JOIN conferences_levels cl ON c.levelId = cl.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND c.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND c.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND c.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (c.holdTime BETWEEN start_date AND end_date) THEN TRUE
                WHEN range_param = 'out' AND (c.holdTime NOT BETWEEN start_date AND end_date) THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式（保留原来的SELECT输出）
    IF is_export_all = 1 THEN
        SELECT * FROM temp_conferences ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_conferences ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_high_level_papers_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_high_level_papers_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_high_level_papers_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),    -- 'approved', 'rejected', 'pending', 'reviewed', 'all'
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'highLevelPapers'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取论文总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM high_level_papers hp
    INNER JOIN high_level_paper_participants hpp ON hp.id = hpp.paperId
    WHERE 
        hpp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND hp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND hp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND hp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (hp.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (hp.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户论文详情和得分
    SELECT 
        hp.id AS paperId,
        hp.title AS paperTitle,
        hp.journal,
        hp.publishDate,
        hpr.id AS levelId,
        hpr.paperLevel AS levelName,
        hpr.score AS baseScore,
        hpp.allocationRatio,
        hpp.isFirstAuthor,
        hpp.isCorrespondingAuthor,
        hpp.authorRank,
        CAST(hpr.score * hpp.allocationRatio AS DECIMAL(10,2)) AS actualScore,
        hp.doi,
        hp.impactFactor,
        hp.ifReviewer,
        CASE 
            WHEN hp.ifReviewer = 1 THEN '已通过'
            WHEN hp.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        ur.nickname AS reviewerName,
        hp.reviewComment,
        hp.createdAt,
        hp.updatedAt
    FROM high_level_papers hp
    LEFT JOIN high_level_papers_rules hpr ON hp.paperLevelId = hpr.id
    LEFT JOIN high_level_paper_participants hpp ON hp.id = hpp.paperId AND hpp.userId = user_id_param
    LEFT JOIN user ur ON hp.reviewerId = ur.id
    WHERE 
        hpp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND hp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND hp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND hp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (hp.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (hp.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    ORDER BY hp.publishDate DESC, actualScore DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户高水平论文总体统计，添加参与论文和主要作者论文的区分
    SELECT 
        COUNT(hp.id) AS totalPapers,  -- 总论文数
        SUM(IF(hpp.isFirstAuthor = 1, 1, 0)) AS firstAuthorCount,  -- 第一作者论文数
        SUM(IF(hpp.isCorrespondingAuthor = 1, 1, 0)) AS correspondingAuthorCount,  -- 通讯作者论文数
        SUM(IF(hpp.isFirstAuthor = 0 AND hpp.isCorrespondingAuthor = 0, 1, 0)) AS participantPaperCount,  -- 参与者论文数
        CAST(SUM(IFNULL(hpr.score, 0) * hpp.allocationRatio) AS DECIMAL(10,2)) AS totalScore
    FROM high_level_papers hp
    INNER JOIN high_level_paper_participants hpp ON hp.id = hpp.paperId
    LEFT JOIN high_level_papers_rules hpr ON hp.paperLevelId = hpr.id
    WHERE 
        hpp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND hp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND hp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND hp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (hp.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (hp.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'highLevelPapers'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_papers_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_papers_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_papers_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'highLevelPapers'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_papers (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        paperCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_papers;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_papers (`rank`, userId, nickName, studentNumber, paperCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(pp.paperId) AS paperCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (p.publishDate BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'out' AND (p.publishDate NOT BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(r.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM high_level_paper_participants pp
    INNER JOIN user u ON pp.userId = u.id
    INNER JOIN high_level_papers p ON pp.paperId = p.id
    LEFT JOIN high_level_papers_rules r ON p.paperLevelId = r.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (p.publishDate BETWEEN start_date AND end_date) THEN TRUE
                WHEN range_param = 'out' AND (p.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式（保留原来的SELECT输出）
    IF is_export_all = 1 THEN
        SELECT * FROM temp_papers ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_papers ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_patents_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_patents_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_patents_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in'(范围内), 'out'(范围外), 'all'(全部)
    IN review_status_param VARCHAR(50),    -- 'approved'(已批准), 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 'all'(全部)
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'patents'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取专利总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM patents p
    INNER JOIN patent_participants pp ON p.id = pp.patentId
    WHERE 
        pp.participantId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤（检查授权日期或转化日期）
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) THEN TRUE
            WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户专利详情和得分
    SELECT 
        p.id AS patentId,
        p.patentName,
        p.categoryId,
        pc.categoryName,
        p.authorizationDate,
        p.conversionDate,
        pc.score AS baseScore,
        pp.allocationRatio,
        pp.isLeader,
        CAST(pc.score * pp.allocationRatio AS DECIMAL(10,2)) AS actualScore,
        p.remark,
        p.ifReviewer,
        CASE 
            WHEN p.ifReviewer = 1 THEN '已通过'
            WHEN p.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        ur.nickname AS reviewerName,
        p.reviewComment,
        p.createdAt,
        p.updatedAt
    FROM patents p
    LEFT JOIN patent_categories pc ON p.categoryId = pc.id
    LEFT JOIN patent_participants pp ON p.id = pp.patentId AND pp.participantId = user_id_param
    LEFT JOIN user ur ON p.reviewerId = ur.id
    WHERE 
        pp.participantId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤（检查授权日期或转化日期）
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) THEN TRUE
            WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) THEN TRUE
            ELSE FALSE
        END
    ORDER BY p.authorizationDate DESC, actualScore DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户专利总体统计，添加参与专利和负责人专利的区分
    SELECT 
        COUNT(p.id) AS totalPatents,  -- 总专利数
        SUM(IF(pp.isLeader = 1, 1, 0)) AS leaderPatentCount,  -- 负责人专利数
        SUM(IF(pp.isLeader = 0, 1, 0)) AS participantPatentCount,  -- 参与者专利数
        CAST(SUM(IFNULL(pc.score, 0) * pp.allocationRatio) AS DECIMAL(10,2)) AS totalScore
    FROM patents p
    INNER JOIN patent_participants pp ON p.id = pp.patentId
    LEFT JOIN patent_categories pc ON p.categoryId = pc.id
    WHERE 
        pp.participantId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤（检查授权日期或转化日期）
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) THEN TRUE
            WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'patents'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_patents_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_patents_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_patents_ranking`(IN range_param VARCHAR(50),         -- 'in'(范围内), 'out'(范围外), 'all'(全部)
    IN review_status_param VARCHAR(50), -- 'approved'(已批准), 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 'all'(全部)
    IN limit_param INT,                 -- 每页数量
    IN page_param INT,                  -- 页码
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'patents'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_patents (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        patentCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_patents;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_patents (`rank`, userId, nickName, studentNumber, patentCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(pp.patentId) AS patentCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) 
                    THEN CAST(IFNULL(pc.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) 
                    THEN CAST(IFNULL(pc.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(pc.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM patent_participants pp
    INNER JOIN user u ON pp.participantId = u.id
    INNER JOIN patents p ON pp.patentId = p.id
    LEFT JOIN patent_categories pc ON p.categoryId = pc.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND ((p.authorizationDate BETWEEN start_date AND end_date) OR (p.conversionDate BETWEEN start_date AND end_date)) THEN TRUE
                WHEN range_param = 'out' AND ((p.authorizationDate NOT BETWEEN start_date AND end_date) AND (p.conversionDate IS NULL OR p.conversionDate NOT BETWEEN start_date AND end_date)) THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式
    IF is_export_all = 1 THEN
        SELECT * FROM temp_patents ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_patents ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_research_projects_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_research_projects_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_research_projects_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),    -- 'rejected', 'pending', 'reviewed', 'all'
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'researchProjects'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取项目总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM research_projects rp
    INNER JOIN research_project_participants rpp ON rp.id = rpp.projectId
    WHERE 
        rpp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND rp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND rp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND rp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (rp.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (rp.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户项目详情和得分
    SELECT 
        rp.id AS projectId,
        rp.projectId AS projectCode,
        rp.name AS projectName,
        rp.type,
        -- 使用与现有代码相同的类型名称映射
        CASE rp.type
            WHEN 'national_fund' THEN '国家自然科学基金'
            WHEN 'social_science' THEN '社会科学基金' 
            WHEN 'enterprise_cooperation' THEN '企业合作项目'
            WHEN 'international_cooperation' THEN '国际合作项目'
            WHEN 'university_project' THEN '校级项目'
            WHEN 'other' THEN '其他类型'
            ELSE rp.type
        END AS typeName,
        rp.approvalDate,
        rp.fundingAmount,
        rpl.levelName,
        rpl.score AS baseScore,
        rpp.allocationRatio,
        rpp.isLeader,
        rpp.participantRank,
        CAST(rpl.score * rpp.allocationRatio AS DECIMAL(10,2)) AS actualScore,
        rp.startDate,
        rp.endDate,
        rp.status,
        rp.ifReviewer,
        CASE 
            WHEN rp.ifReviewer = 1 THEN '已通过'
            WHEN rp.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        rp.createdAt,
        rp.updatedAt
    FROM research_projects rp
    INNER JOIN research_project_participants rpp ON rp.id = rpp.projectId
    LEFT JOIN research_projects_levels_rules rpl ON rp.levelId = rpl.id
    WHERE 
        rpp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND rp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND rp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND rp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (rp.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (rp.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    ORDER BY rp.approvalDate DESC, actualScore DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户科研项目总体统计，添加参与项目和领导项目的区分
    SELECT 
        COUNT(rp.id) AS totalProjects,  -- 总项目数
        SUM(IF(rpp.isLeader = 1, 1, 0)) AS leaderProjectCount,  -- 负责人项目数
        SUM(IF(rpp.isLeader = 0, 1, 0)) AS participantProjectCount,  -- 参与者项目数
        CAST(SUM(IFNULL(rpl.score, 0) * rpp.allocationRatio) AS DECIMAL(10,2)) AS totalScore
    FROM research_projects rp
    INNER JOIN research_project_participants rpp ON rp.id = rpp.projectId
    LEFT JOIN research_projects_levels_rules rpl ON rp.levelId = rpl.id
    WHERE 
        rpp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND rp.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND rp.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND rp.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (rp.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (rp.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'researchProjects'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_research_projects_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_research_projects_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_research_projects_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'researchProjects'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_research_projects (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        projectCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_research_projects;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_research_projects (`rank`, userId, nickName, studentNumber, projectCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(pp.projectId) AS projectCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM research_project_participants pp
    INNER JOIN user u ON pp.userId = u.id
    INNER JOIN research_projects p ON pp.projectId = p.id
    LEFT JOIN research_projects_levels_rules l ON p.levelId = l.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式（保留原来的SELECT输出）
    IF is_export_all = 1 THEN
        SELECT * FROM temp_research_projects ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_research_projects ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_student_award_guidance_awards_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_student_award_guidance_awards_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_student_award_guidance_awards_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),    -- 'approved', 'rejected', 'pending', 'reviewed', 'all'
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentAwardGuidance'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取获奖总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM student_award_guidance_awards a
    INNER JOIN student_award_guidance_participants ap ON a.id = ap.awardId
    WHERE 
        ap.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户获奖详情和得分
    SELECT 
        a.id AS awardId,
        a.awardName,
        a.department,
        a.awardDate,
        a.levelId,
        al.levelName,
        al.score AS baseScore,
        ap.allocationRatio,
        ap.isLeader,
        CAST(al.score * ap.allocationRatio AS DECIMAL(10,2)) AS actualScore,
        a.remark,
        a.ifReviewer,
        CASE 
            WHEN a.ifReviewer = 1 THEN '已通过'
            WHEN a.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        ur.nickname AS reviewerName,
        a.reviewComment,
        a.createdAt,
        a.updatedAt
    FROM student_award_guidance_awards a
    LEFT JOIN student_award_guidance_awardLevels al ON a.levelId = al.id
    LEFT JOIN student_award_guidance_participants ap ON a.id = ap.awardId AND ap.userId = user_id_param
    LEFT JOIN user ur ON a.reviewerId = ur.id
    WHERE 
        ap.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    ORDER BY a.awardDate DESC, actualScore DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户获奖总体统计，添加主要指导和参与指导的区分
    SELECT 
        COUNT(a.id) AS totalAwards,  -- 总获奖数
        SUM(IF(ap.isLeader = 1, 1, 0)) AS leaderAwardCount,  -- 主要指导获奖数
        SUM(IF(ap.isLeader = 0, 1, 0)) AS participantAwardCount,  -- 参与指导获奖数
        CAST(SUM(IFNULL(al.score, 0) * ap.allocationRatio) AS DECIMAL(10,2)) AS totalScore
    FROM student_award_guidance_awards a
    INNER JOIN student_award_guidance_participants ap ON a.id = ap.awardId
    LEFT JOIN student_award_guidance_awardLevels al ON a.levelId = al.id
    WHERE 
        ap.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND a.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND a.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND a.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (a.awardDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (a.awardDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'studentAwardGuidance'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_student_projects_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_student_projects_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_student_projects_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentProjectGuidance'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_student_projects (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        projectCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_student_projects;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_student_projects (`rank`, userId, nickName, studentNumber, projectCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(pp.projectId) AS projectCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM student_project_guidance_participants pp
    INNER JOIN user u ON pp.userId = u.id
    INNER JOIN student_project_guidance_projects p ON pp.projectId = p.id
    LEFT JOIN student_project_guidance_projectLevels l ON p.levelId = l.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式（保留原来的SELECT输出）
    IF is_export_all = 1 THEN
        SELECT * FROM temp_student_projects ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_student_projects ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_student_project_guidance_projects_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_student_project_guidance_projects_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_student_project_guidance_projects_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),    -- 'approved', 'rejected', 'pending', 'reviewed', 'all'
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'studentProjectGuidance'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取项目总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM student_project_guidance_projects p
    INNER JOIN student_project_guidance_participants tp ON p.id = tp.projectId
    WHERE 
        tp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户项目详情和得分
    SELECT 
        p.id AS projectId,
        p.projectNumber,
        p.projectName,
        p.levelId,
        pl.levelName,
        p.approvalDate,
        p.approvalDepartment,
        p.approvalFund,
        p.startYear,
        p.endYear,
        pl.score AS baseScore,
        tp.allocationRatio,
        tp.isLeader,
        CAST(pl.score * tp.allocationRatio AS DECIMAL(10,2)) AS actualScore,
        p.remark,
        p.ifReviewer,
        CASE 
            WHEN p.ifReviewer = 1 THEN '已通过'
            WHEN p.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        ur.nickname AS reviewerName,
        p.reviewComment,
        p.createdAt,
        p.updatedAt
    FROM student_project_guidance_projects p
    LEFT JOIN student_project_guidance_projectLevels pl ON p.levelId = pl.id
    LEFT JOIN student_project_guidance_participants tp ON p.id = tp.projectId AND tp.userId = user_id_param
    LEFT JOIN user ur ON p.reviewerId = ur.id
    WHERE 
        tp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    ORDER BY p.approvalDate DESC, actualScore DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户项目总体统计，添加主持项目和参与项目的区分
    SELECT 
        COUNT(p.id) AS totalProjects,  -- 总项目数
        SUM(IF(tp.isLeader = 1, 1, 0)) AS leaderProjectCount,  -- 主持项目数
        SUM(IF(tp.isLeader = 0, 1, 0)) AS participantProjectCount,  -- 参与项目数
        CAST(SUM(IFNULL(pl.score, 0) * tp.allocationRatio) AS DECIMAL(10,2)) AS totalScore
    FROM student_project_guidance_projects p
    INNER JOIN student_project_guidance_participants tp ON p.id = tp.projectId
    LEFT JOIN student_project_guidance_projectLevels pl ON p.levelId = pl.id
    WHERE 
        tp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'studentProjectGuidance'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_teaching_projects_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_teaching_projects_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_teaching_projects_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'teachingReformProjects'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_teaching_projects (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        projectCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_teaching_projects;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_teaching_projects (`rank`, userId, nickName, studentNumber, projectCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(pp.projectId) AS projectCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM teaching_reform_participants pp
    INNER JOIN user u ON pp.userId = u.id
    INNER JOIN teaching_reform_projects p ON pp.projectId = p.id
    LEFT JOIN teaching_reform_projectLevels l ON p.levelId = l.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
                WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式（保留原来的SELECT输出）
    IF is_export_all = 1 THEN
        SELECT * FROM temp_teaching_projects ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_teaching_projects ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_teaching_reform_projects_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_teaching_reform_projects_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_teaching_reform_projects_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),    -- 'approved', 'rejected', 'pending', 'reviewed', 'all'
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'teachingReformProjects'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取项目总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM teaching_reform_projects p
    INNER JOIN teaching_reform_participants tp ON p.id = tp.projectId
    WHERE 
        tp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户项目详情和得分
    SELECT 
        p.id AS projectId,
        p.projectNumber,
        p.projectName,
        p.levelId,
        pl.levelName,
        p.approvalDate,
        p.approvalDepartment,
        p.approvalFund,
        p.startYear,
        p.endYear,
        pl.score AS baseScore,
        tp.allocationRatio,
        tp.isLeader,
        CAST(pl.score * tp.allocationRatio AS DECIMAL(10,2)) AS actualScore,
        p.remark,
        p.ifReviewer,
        CASE 
            WHEN p.ifReviewer = 1 THEN '已通过'
            WHEN p.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        ur.nickname AS reviewerName,
        p.reviewComment,
        p.createdAt,
        p.updatedAt
    FROM teaching_reform_projects p
    LEFT JOIN teaching_reform_projectLevels pl ON p.levelId = pl.id
    LEFT JOIN teaching_reform_participants tp ON p.id = tp.projectId AND tp.userId = user_id_param
    LEFT JOIN user ur ON p.reviewerId = ur.id
    WHERE 
        tp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END
    ORDER BY p.approvalDate DESC, actualScore DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户项目总体统计，添加主持项目和参与项目的区分
    SELECT 
        COUNT(p.id) AS totalProjects,  -- 总项目数
        SUM(IF(tp.isLeader = 1, 1, 0)) AS leaderProjectCount,  -- 主持项目数
        SUM(IF(tp.isLeader = 0, 1, 0)) AS participantProjectCount,  -- 参与项目数
        CAST(SUM(IFNULL(pl.score, 0) * tp.allocationRatio) AS DECIMAL(10,2)) AS totalScore
    FROM teaching_reform_projects p
    INNER JOIN teaching_reform_participants tp ON p.id = tp.projectId
    LEFT JOIN teaching_reform_projectLevels pl ON p.levelId = pl.id
    WHERE 
        tp.userId = user_id_param
        -- 审核状态过滤
        AND CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (p.approvalDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (p.approvalDate NOT BETWEEN start_date AND end_date) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'teachingReformProjects'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_teaching_research_awards_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_teaching_research_awards_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_teaching_research_awards_detail`(IN p_user_id VARCHAR(36),     -- 用户ID，必填
    IN p_range VARCHAR(50),       -- 'in', 'out', 'all'
    IN p_review_status VARCHAR(50),  -- 'reviewed', 'pending', 'rejected', 'all'
    IN p_page_size INT,           -- 每页数量
    IN p_page_num INT)
BEGIN
    DECLARE v_start_time DATE;
    DECLARE v_end_time DATE;
    DECLARE v_time_interval_name VARCHAR(50);
    DECLARE v_offset INT;
    
    -- 计算分页偏移量
    SET v_offset = (p_page_num - 1) * p_page_size;
    
    -- 获取时间区间信息
    SELECT startTime, endTime, name INTO v_start_time, v_end_time, v_time_interval_name
    FROM time_interval
    WHERE name = 'teachingResearchAwards' LIMIT 1;
    
    -- 获取项目总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM teaching_research_awards tra
    INNER JOIN teaching_research_award_participants trap ON tra.id = trap.awardId
    WHERE 
        trap.participantId = p_user_id
        -- 审核状态过滤
        AND CASE 
            WHEN p_review_status = 'all' THEN TRUE
            WHEN p_review_status = 'reviewed' AND tra.ifReviewer = 1 THEN TRUE
            WHEN p_review_status = 'rejected' AND tra.ifReviewer = 0 THEN TRUE
            WHEN p_review_status = 'pending' AND tra.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN p_range = 'all' THEN TRUE
            WHEN p_range = 'in' AND (tra.awardTime BETWEEN v_start_time AND v_end_time) THEN TRUE
            WHEN p_range = 'out' AND (tra.awardTime NOT BETWEEN v_start_time AND v_end_time) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户奖励详情和得分
    SELECT 
        tra.id,
        tra.awardName,
        tral.levelName,
        tra.awardTime,
        tra.department,
        tral.score AS baseScore,
        trap.allocationRatio,
        trap.isLeader,
        CAST(tral.score * trap.allocationRatio AS DECIMAL(10,2)) AS userScore,
        tra.ifReviewer,
        CASE 
            WHEN tra.ifReviewer = 1 THEN '已通过'
            WHEN tra.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        tra.reviewComment,
        tra.createdAt,
        tra.updatedAt
    FROM teaching_research_awards tra
    INNER JOIN teaching_research_award_participants trap ON tra.id = trap.awardId
    LEFT JOIN teaching_research_award_levels tral ON tra.awardLevelId = tral.id
    WHERE 
        trap.participantId = p_user_id
        -- 审核状态过滤
        AND CASE 
            WHEN p_review_status = 'all' THEN TRUE
            WHEN p_review_status = 'reviewed' AND tra.ifReviewer = 1 THEN TRUE
            WHEN p_review_status = 'rejected' AND tra.ifReviewer = 0 THEN TRUE
            WHEN p_review_status = 'pending' AND tra.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN p_range = 'all' THEN TRUE
            WHEN p_range = 'in' AND (tra.awardTime BETWEEN v_start_time AND v_end_time) THEN TRUE
            WHEN p_range = 'out' AND (tra.awardTime NOT BETWEEN v_start_time AND v_end_time) THEN TRUE
            ELSE FALSE
        END
    ORDER BY tra.awardTime DESC, userScore DESC
    LIMIT p_page_size OFFSET v_offset;
    
    -- 返回用户奖励总体统计
    SELECT 
        COUNT(tra.id) AS totalAwards,  -- 总奖励数
        SUM(IF(trap.isLeader = 1, 1, 0)) AS leaderCount,  -- 负责人奖励数
        SUM(IF(trap.isLeader = 0, 1, 0)) AS participantCount,  -- 参与者奖励数
        CAST(SUM(IFNULL(tral.score, 0) * trap.allocationRatio) AS DECIMAL(10,2)) AS totalScore
    FROM teaching_research_awards tra
    INNER JOIN teaching_research_award_participants trap ON tra.id = trap.awardId
    LEFT JOIN teaching_research_award_levels tral ON tra.awardLevelId = tral.id
    WHERE 
        trap.participantId = p_user_id
        -- 审核状态过滤
        AND CASE 
            WHEN p_review_status = 'all' THEN TRUE
            WHEN p_review_status = 'reviewed' AND tra.ifReviewer = 1 THEN TRUE
            WHEN p_review_status = 'rejected' AND tra.ifReviewer = 0 THEN TRUE
            WHEN p_review_status = 'pending' AND tra.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN p_range = 'all' THEN TRUE
            WHEN p_range = 'in' AND (tra.awardTime BETWEEN v_start_time AND v_end_time) THEN TRUE
            WHEN p_range = 'out' AND (tra.awardTime NOT BETWEEN v_start_time AND v_end_time) THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT v_start_time AS startTime, v_end_time AS endTime, v_time_interval_name AS name;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_teaching_research_awards_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_teaching_research_awards_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_teaching_research_awards_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'teachingResearchAwards'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_teaching_research_awards (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        awardCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_teaching_research_awards;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_teaching_research_awards (`rank`, userId, nickName, studentNumber, awardCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(pp.awardId) AS awardCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (p.awardTime BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'out' AND (p.awardTime NOT BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM teaching_research_award_participants pp
    INNER JOIN user u ON pp.participantId = u.id
    INNER JOIN teaching_research_awards p ON pp.awardId = p.id
    LEFT JOIN teaching_research_award_levels l ON p.awardLevelId = l.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (p.awardTime BETWEEN start_date AND end_date) THEN TRUE
                WHEN range_param = 'out' AND (p.awardTime NOT BETWEEN start_date AND end_date) THEN TRUE
                ELSE FALSE
            END
        )
        AND p.status = 1
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式
    IF is_export_all = 1 THEN
        SELECT * FROM temp_teaching_research_awards ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_teaching_research_awards ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_teaching_workloads_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_teaching_workloads_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_teaching_workloads_detail`(IN p_user_id VARCHAR(36),
    IN p_range VARCHAR(10), -- 'in', 'out', 'all'
    IN p_review_status VARCHAR(10), -- 'reviewed', 'pending', 'rejected', 'all'
    IN p_page_size INT,
    IN p_page_num INT)
BEGIN
    DECLARE v_start_time DATE;
    DECLARE v_end_time DATE;
    DECLARE v_time_interval_name VARCHAR(50);
    DECLARE v_offset INT;
    
    -- 计算分页偏移量
    SET v_offset = (p_page_num - 1) * p_page_size;
    
    -- 获取时间区间信息
    SELECT startTime, endTime, name INTO v_start_time, v_end_time, v_time_interval_name
    FROM time_interval
    WHERE name = 'teachingWorkloads' LIMIT 1;
    
    -- 构建过滤条件
    SET @filter_condition = CONCAT(' AND twp.participantId = "', p_user_id, '"');
    
    -- 根据审核状态构建过滤条件
    IF p_review_status = 'reviewed' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tw.ifReviewer = 1');
    ELSEIF p_review_status = 'rejected' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tw.ifReviewer = 0');
    ELSEIF p_review_status = 'pending' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tw.ifReviewer IS NULL');
    END IF;
    
    -- 添加时间范围过滤
    IF p_range = 'in' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND tw.semester >= "', v_start_time, '" AND tw.semester <= "', v_end_time, '"');
    ELSEIF p_range = 'out' THEN
        SET @filter_condition = CONCAT(@filter_condition, ' AND (tw.semester < "', v_start_time, '" OR tw.semester > "', v_end_time, '")');
    END IF;
    
    -- 第一个结果集：总记录数
    SET @sql_count = CONCAT('
        SELECT COUNT(DISTINCT tw.id) AS totalCount
        FROM teaching_workloads tw
        JOIN teaching_workload_participants twp ON tw.id = twp.workloadId
        WHERE 1=1', @filter_condition
    );
    
    PREPARE stmt FROM @sql_count;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 第二个结果集：工作量列表
    SET @sql_workloads = CONCAT('
        SELECT 
            tw.id,
            tw.courseName,
            tw.courseType,
            tw.semester,
            tw.studentLevel,
            tw.courseNature,
            tw.teachingHours,
            twl.categoryName,
            twl.score AS baseScore,
            twp.allocationRatio AS userAllocationRatio,
            ROUND(twl.score * twp.allocationRatio / 100, 2) AS userScore,
            twp.isLeader,
            CASE 
                WHEN tw.ifReviewer = 1 THEN "已审核"
                WHEN tw.ifReviewer = 0 THEN "已拒绝"
                ELSE "待审核"
            END AS reviewStatus,
            tw.rejectReason,
            tw.createdAt,
            tw.updatedAt
        FROM teaching_workloads tw
        JOIN teaching_workload_participants twp ON tw.id = twp.workloadId
        LEFT JOIN teaching_workload_level twl ON tw.categoryId = twl.id
        WHERE 1=1', @filter_condition, '
        ORDER BY tw.updatedAt DESC
        LIMIT ', p_page_size, ' OFFSET ', v_offset
    );
    
    PREPARE stmt FROM @sql_workloads;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 第三个结果集：统计数据
    SET @sql_stats = CONCAT('
        SELECT 
            COUNT(DISTINCT tw.id) AS totalWorkloads,
            SUM(CASE WHEN twp.isLeader = 1 THEN 1 ELSE 0 END) AS leaderCount,
            SUM(CASE WHEN twp.isLeader = 0 THEN 1 ELSE 0 END) AS participantCount,
            SUM(ROUND(twl.score * twp.allocationRatio / 100, 2)) AS totalScore
        FROM teaching_workloads tw
        JOIN teaching_workload_participants twp ON tw.id = twp.workloadId
        LEFT JOIN teaching_workload_level twl ON tw.categoryId = twl.id
        WHERE 1=1', @filter_condition
    );
    
    PREPARE stmt FROM @sql_stats;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 第四个结果集：时间区间信息
    SELECT v_start_time AS startTime, v_end_time AS endTime, v_time_interval_name AS name;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_teaching_workloads_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_teaching_workloads_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_teaching_workloads_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'teachingWorkloads'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_teaching_workloads (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        workloadCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_teaching_workloads;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_teaching_workloads (`rank`, userId, nickName, studentNumber, workloadCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(pp.workloadId) AS workloadCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (SUBSTRING_INDEX(p.semester, '-', 1) BETWEEN YEAR(start_date) AND YEAR(end_date)) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'out' AND (SUBSTRING_INDEX(p.semester, '-', 1) NOT BETWEEN YEAR(start_date) AND YEAR(end_date)) 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(l.score, 0) AS DECIMAL(10,2)) * pp.allocationRatio
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM teaching_workload_participants pp
    INNER JOIN user u ON pp.participantId = u.id
    INNER JOIN teaching_workloads p ON pp.workloadId = p.id
    LEFT JOIN teaching_workload_level l ON p.categoryId = l.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND p.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND p.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND p.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (SUBSTRING_INDEX(p.semester, '-', 1) BETWEEN YEAR(start_date) AND YEAR(end_date)) THEN TRUE
                WHEN range_param = 'out' AND (SUBSTRING_INDEX(p.semester, '-', 1) NOT BETWEEN YEAR(start_date) AND YEAR(end_date)) THEN TRUE
                ELSE FALSE
            END
        )
        AND p.status = 1
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式
    IF is_export_all = 1 THEN
        SELECT * FROM temp_teaching_workloads ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_teaching_workloads ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_textbooks_detail
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_textbooks_detail`;
delimiter ;;
CREATE PROCEDURE `get_user_textbooks_detail`(IN user_id_param VARCHAR(50),   -- 用户ID，必填
    IN range_param VARCHAR(50),     -- 'in', 'out', 'all'
    IN review_status_param VARCHAR(50),    -- 'approved', 'rejected', 'pending', 'reviewed', 'all'
    IN limit_param INT,             -- 每页数量
    IN page_param INT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;
    
    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'textbooks'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    
    -- 获取教材与著作总数（用于分页）
    SELECT 
        COUNT(*) AS totalCount
    FROM textbooks t
    WHERE 
        t.userId = user_id_param AND
        -- 审核状态过滤
        CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND t.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND t.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND t.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (t.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (t.publishDate < start_date OR t.publishDate > end_date) THEN TRUE
            ELSE FALSE
        END;
    
    -- 获取用户教材与著作详情和得分
    SELECT 
        t.id AS textbookId,
        t.materialName,
        t.publishDate,
        t.categoryId,
        tc.categoryAndPosition AS categoryName,
        CAST(tc.score AS DECIMAL(10,2)) AS score,
        t.remark,
        t.ifReviewer,
        CASE 
            WHEN t.ifReviewer = 1 THEN '已通过'
            WHEN t.ifReviewer = 0 THEN '已拒绝'
            ELSE '待审核'
        END AS reviewStatus,
        ur.nickname AS reviewerName,
        t.reviewComment,
        t.attachmentUrl,
        t.createdAt,
        t.updatedAt
    FROM textbooks t
    LEFT JOIN textbook_categories tc ON t.categoryId = tc.id
    LEFT JOIN user ur ON t.reviewerId = ur.id
    WHERE 
        t.userId = user_id_param AND
        -- 审核状态过滤
        CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND t.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND t.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND t.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END
        -- 时间范围过滤
        AND CASE
            WHEN range_param = 'all' THEN TRUE
            WHEN range_param = 'in' AND (t.publishDate BETWEEN start_date AND end_date) THEN TRUE
            WHEN range_param = 'out' AND (t.publishDate < start_date OR t.publishDate > end_date) THEN TRUE
            ELSE FALSE
        END
    ORDER BY t.publishDate DESC, score DESC
    LIMIT limit_param OFFSET offset;
    
    -- 返回用户教材与著作总体统计
    SELECT 
        COUNT(t.id) AS totalTextbooks,
        CAST(SUM(IFNULL(tc.score, 0)) AS DECIMAL(10,2)) AS totalScore,
        SUM(IF(t.ifReviewer = 1, 1, 0)) AS approvedTextbooks, -- 已通过审核的教材数
        -- 计算统计范围内的教材数量
        SUM(IF(
            range_param = 'all' OR 
            (range_param = 'in' AND t.publishDate BETWEEN start_date AND end_date) OR
            (range_param = 'out' AND (t.publishDate < start_date OR t.publishDate > end_date)),
            1, 0
        )) AS activeTextbooks
    FROM textbooks t
    LEFT JOIN textbook_categories tc ON t.categoryId = tc.id
    WHERE 
        t.userId = user_id_param AND
        -- 审核状态过滤
        CASE 
            WHEN review_status_param = 'all' THEN TRUE
            WHEN review_status_param = 'reviewed' AND t.ifReviewer = 1 THEN TRUE
            WHEN review_status_param = 'rejected' AND t.ifReviewer = 0 THEN TRUE
            WHEN review_status_param = 'pending' AND t.ifReviewer IS NULL THEN TRUE
            ELSE FALSE
        END;
        
    -- 返回时间区间信息
    SELECT startTime, endTime, name
    FROM time_interval
    WHERE name = 'textbooks'
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for get_user_textbooks_ranking
-- ----------------------------
DROP PROCEDURE IF EXISTS `get_user_textbooks_ranking`;
delimiter ;;
CREATE PROCEDURE `get_user_textbooks_ranking`(IN range_param VARCHAR(50),
    IN review_status_param VARCHAR(50),
    IN limit_param INT,
    IN page_param INT,
    IN is_export_all TINYINT)
BEGIN
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE offset INT;

    -- 获取时间区间
    SELECT startTime, endTime INTO start_date, end_date
    FROM time_interval
    WHERE name = 'textbooks'
    LIMIT 1;
    
    -- 计算分页的 OFFSET
    SET offset = (page_param - 1) * limit_param;
    SET @row_number = 0;

    -- 创建临时表（如果不存在）
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_textbooks (
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        textbookCount INT,
        totalScore DECIMAL(10,2)
    );
    
    -- 清空临时表，准备插入新数据
    DELETE FROM temp_textbooks;
    
    -- 插入查询结果到临时表
    INSERT INTO temp_textbooks (`rank`, userId, nickName, studentNumber, textbookCount, totalScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        u.id AS userId,
        u.nickname AS nickName,
        u.studentNumber,
        COUNT(t.id) AS textbookCount,
        CAST(SUM(
            CASE 
                WHEN range_param = 'in' AND (t.publishDate BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(tc.score, 0) AS DECIMAL(10,2))
                WHEN range_param = 'out' AND (t.publishDate NOT BETWEEN start_date AND end_date) 
                    THEN CAST(IFNULL(tc.score, 0) AS DECIMAL(10,2))
                WHEN range_param = 'all' 
                    THEN CAST(IFNULL(tc.score, 0) AS DECIMAL(10,2))
                ELSE 0 
            END
        ) AS DECIMAL(10,2)) AS totalScore
    FROM textbooks t
    INNER JOIN user u ON t.userId = u.id
    LEFT JOIN textbook_categories tc ON t.categoryId = tc.id
    WHERE 
        (
            CASE 
                WHEN review_status_param = 'all' THEN TRUE
                WHEN review_status_param = 'reviewed' AND t.ifReviewer = 1 THEN TRUE
                WHEN review_status_param = 'rejected' AND t.ifReviewer = 0 THEN TRUE
                WHEN review_status_param = 'pending' AND t.ifReviewer IS NULL THEN TRUE
                ELSE FALSE
            END
        )
        AND (
            CASE
                WHEN range_param = 'all' THEN TRUE
                WHEN range_param = 'in' AND (t.publishDate BETWEEN start_date AND end_date) THEN TRUE
                WHEN range_param = 'out' AND (t.publishDate NOT BETWEEN start_date AND end_date) THEN TRUE
                ELSE FALSE
            END
        )
    GROUP BY u.id, u.nickname, u.studentNumber
    ORDER BY totalScore DESC;

    -- 根据是否导出全部决定查询方式（保留原来的SELECT输出）
    IF is_export_all = 1 THEN
        SELECT * FROM temp_textbooks ORDER BY totalScore DESC;
    ELSE
        SELECT * FROM temp_textbooks ORDER BY totalScore DESC LIMIT limit_param OFFSET offset;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for update_ranking_tables
-- ----------------------------
DROP PROCEDURE IF EXISTS `update_ranking_tables`;
delimiter ;;
CREATE PROCEDURE `update_ranking_tables`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    
    -- 创建三个表（如果不存在）
    -- 1. 审核过的并且在范围内的
    CREATE TABLE IF NOT EXISTS user_ranking_reviewed_in (
        id INT AUTO_INCREMENT PRIMARY KEY,
        updateTime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        totalItemCount INT DEFAULT 0,
        totalScore DECIMAL(10,2) DEFAULT 0,
        appointmentCount INT DEFAULT 0,
        appointmentScore DECIMAL(10,2) DEFAULT 0,
        awardCount INT DEFAULT 0,
        awardScore DECIMAL(10,2) DEFAULT 0,
        conferenceCount INT DEFAULT 0,
        conferenceScore DECIMAL(10,2) DEFAULT 0,
        paperCount INT DEFAULT 0,
        paperScore DECIMAL(10,2) DEFAULT 0,
        patentCount INT DEFAULT 0,
        patentScore DECIMAL(10,2) DEFAULT 0,
        researchCount INT DEFAULT 0,
        researchScore DECIMAL(10,2) DEFAULT 0,
        studentProjectCount INT DEFAULT 0,
        studentProjectScore DECIMAL(10,2) DEFAULT 0,
        teachingProjectCount INT DEFAULT 0,
        teachingProjectScore DECIMAL(10,2) DEFAULT 0,
        textbookCount INT DEFAULT 0,
        textbookScore DECIMAL(10,2) DEFAULT 0,
        teachingWorkloadCount INT DEFAULT 0,
        teachingWorkloadScore DECIMAL(10,2) DEFAULT 0,
        teachingResearchAwardCount INT DEFAULT 0,
        teachingResearchAwardScore DECIMAL(10,2) DEFAULT 0,
        INDEX (userId),
        INDEX (totalScore)
    );

    -- 2. 审核过的在范围外的
    CREATE TABLE IF NOT EXISTS user_ranking_reviewed_out (
        id INT AUTO_INCREMENT PRIMARY KEY,
        updateTime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        totalItemCount INT DEFAULT 0,
        totalScore DECIMAL(10,2) DEFAULT 0,
        appointmentCount INT DEFAULT 0,
        appointmentScore DECIMAL(10,2) DEFAULT 0,
        awardCount INT DEFAULT 0,
        awardScore DECIMAL(10,2) DEFAULT 0,
        conferenceCount INT DEFAULT 0,
        conferenceScore DECIMAL(10,2) DEFAULT 0,
        paperCount INT DEFAULT 0,
        paperScore DECIMAL(10,2) DEFAULT 0,
        patentCount INT DEFAULT 0,
        patentScore DECIMAL(10,2) DEFAULT 0,
        researchCount INT DEFAULT 0,
        researchScore DECIMAL(10,2) DEFAULT 0,
        studentProjectCount INT DEFAULT 0,
        studentProjectScore DECIMAL(10,2) DEFAULT 0,
        teachingProjectCount INT DEFAULT 0,
        teachingProjectScore DECIMAL(10,2) DEFAULT 0,
        textbookCount INT DEFAULT 0,
        textbookScore DECIMAL(10,2) DEFAULT 0,
        teachingWorkloadCount INT DEFAULT 0,
        teachingWorkloadScore DECIMAL(10,2) DEFAULT 0,
        teachingResearchAwardCount INT DEFAULT 0,
        teachingResearchAwardScore DECIMAL(10,2) DEFAULT 0,
        INDEX (userId),
        INDEX (totalScore)
    );

    -- 3. 审核过的全部
    CREATE TABLE IF NOT EXISTS user_ranking_reviewed_all (
        id INT AUTO_INCREMENT PRIMARY KEY,
        updateTime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        `rank` INT,
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        totalItemCount INT DEFAULT 0,
        totalScore DECIMAL(10,2) DEFAULT 0,
        appointmentCount INT DEFAULT 0,
        appointmentScore DECIMAL(10,2) DEFAULT 0,
        awardCount INT DEFAULT 0,
        awardScore DECIMAL(10,2) DEFAULT 0,
        conferenceCount INT DEFAULT 0,
        conferenceScore DECIMAL(10,2) DEFAULT 0,
        paperCount INT DEFAULT 0,
        paperScore DECIMAL(10,2) DEFAULT 0,
        patentCount INT DEFAULT 0,
        patentScore DECIMAL(10,2) DEFAULT 0,
        researchCount INT DEFAULT 0,
        researchScore DECIMAL(10,2) DEFAULT 0,
        studentProjectCount INT DEFAULT 0,
        studentProjectScore DECIMAL(10,2) DEFAULT 0,
        teachingProjectCount INT DEFAULT 0,
        teachingProjectScore DECIMAL(10,2) DEFAULT 0,
        textbookCount INT DEFAULT 0,
        textbookScore DECIMAL(10,2) DEFAULT 0,
        teachingWorkloadCount INT DEFAULT 0,
        teachingWorkloadScore DECIMAL(10,2) DEFAULT 0,
        teachingResearchAwardCount INT DEFAULT 0,
        teachingResearchAwardScore DECIMAL(10,2) DEFAULT 0,
        INDEX (userId),
        INDEX (totalScore)
    );

    -- 清空表
    TRUNCATE TABLE user_ranking_reviewed_in;
    TRUNCATE TABLE user_ranking_reviewed_out;
    TRUNCATE TABLE user_ranking_reviewed_all;

    -- 创建临时表存储排名数据（保持不变）
    DROP TEMPORARY TABLE IF EXISTS temp_user_ranking;
    CREATE TEMPORARY TABLE temp_user_ranking (
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        totalItemCount INT DEFAULT 0,
        totalScore DECIMAL(10,2) DEFAULT 0,
        appointmentCount INT DEFAULT 0,
        appointmentScore DECIMAL(10,2) DEFAULT 0,
        awardCount INT DEFAULT 0,
        awardScore DECIMAL(10,2) DEFAULT 0,
        conferenceCount INT DEFAULT 0,
        conferenceScore DECIMAL(10,2) DEFAULT 0,
        paperCount INT DEFAULT 0,
        paperScore DECIMAL(10,2) DEFAULT 0,
        patentCount INT DEFAULT 0,
        patentScore DECIMAL(10,2) DEFAULT 0,
        researchCount INT DEFAULT 0,
        researchScore DECIMAL(10,2) DEFAULT 0,
        studentProjectCount INT DEFAULT 0,
        studentProjectScore DECIMAL(10,2) DEFAULT 0,
        teachingProjectCount INT DEFAULT 0,
        teachingProjectScore DECIMAL(10,2) DEFAULT 0,
        textbookCount INT DEFAULT 0,
        textbookScore DECIMAL(10,2) DEFAULT 0,
        teachingWorkloadCount INT DEFAULT 0,
        teachingWorkloadScore DECIMAL(10,2) DEFAULT 0,
        teachingResearchAwardCount INT DEFAULT 0,
        teachingResearchAwardScore DECIMAL(10,2) DEFAULT 0
    );

    -- 创建接收存储过程结果的临时表
    DROP TEMPORARY TABLE IF EXISTS last_result_table;
    CREATE TEMPORARY TABLE last_result_table (
        userId VARCHAR(50),
        nickName VARCHAR(255),
        studentNumber VARCHAR(50),
        totalItemCount INT DEFAULT 0,
        totalScore DECIMAL(10,2) DEFAULT 0,
        appointmentCount INT DEFAULT 0,
        appointmentScore DECIMAL(10,2) DEFAULT 0,
        awardCount INT DEFAULT 0,
        awardScore DECIMAL(10,2) DEFAULT 0,
        conferenceCount INT DEFAULT 0,
        conferenceScore DECIMAL(10,2) DEFAULT 0,
        paperCount INT DEFAULT 0,
        paperScore DECIMAL(10,2) DEFAULT 0,
        patentCount INT DEFAULT 0,
        patentScore DECIMAL(10,2) DEFAULT 0,
        researchCount INT DEFAULT 0,
        researchScore DECIMAL(10,2) DEFAULT 0,
        studentProjectCount INT DEFAULT 0,
        studentProjectScore DECIMAL(10,2) DEFAULT 0,
        teachingProjectCount INT DEFAULT 0,
        teachingProjectScore DECIMAL(10,2) DEFAULT 0,
        textbookCount INT DEFAULT 0,
        textbookScore DECIMAL(10,2) DEFAULT 0,
        teachingWorkloadCount INT DEFAULT 0,
        teachingWorkloadScore DECIMAL(10,2) DEFAULT 0,
        teachingResearchAwardCount INT DEFAULT 0,
        teachingResearchAwardScore DECIMAL(10,2) DEFAULT 0
    );
    
    -- 处理范围内数据
    -- 1. 调用存储过程获取数据
    CALL get_user_combined_ranking("in", "reviewed", 1000000, 1, 1);
    
    -- 2. 将结果插入临时表
    INSERT INTO temp_user_ranking
    SELECT * FROM last_result_table;
    
    -- 3. 插入到目标表并添加排名
    SET @row_number = 0;
    INSERT INTO user_ranking_reviewed_in (`rank`, userId, nickName, studentNumber, totalItemCount, totalScore, 
        appointmentCount, appointmentScore, awardCount, awardScore, 
        conferenceCount, conferenceScore, paperCount, paperScore, 
        patentCount, patentScore, researchCount, researchScore, 
        studentProjectCount, studentProjectScore, 
        teachingProjectCount, teachingProjectScore, 
        textbookCount, textbookScore,
        teachingWorkloadCount, teachingWorkloadScore,
        teachingResearchAwardCount, teachingResearchAwardScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        userId, nickName, studentNumber, totalItemCount, totalScore, 
        appointmentCount, appointmentScore, awardCount, awardScore, 
        conferenceCount, conferenceScore, paperCount, paperScore, 
        patentCount, patentScore, researchCount, researchScore, 
        studentProjectCount, studentProjectScore, 
        teachingProjectCount, teachingProjectScore,
        textbookCount, textbookScore,
        teachingWorkloadCount, teachingWorkloadScore,
        teachingResearchAwardCount, teachingResearchAwardScore
    FROM temp_user_ranking
    ORDER BY totalScore DESC;
    
    -- 清空临时表准备下一个范围
    TRUNCATE TABLE temp_user_ranking;
    TRUNCATE TABLE last_result_table;
    
    -- 处理范围外数据
    CALL get_user_combined_ranking("out", "reviewed", 1000000, 1, 1);
    INSERT INTO temp_user_ranking SELECT * FROM last_result_table;
    SET @row_number = 0;
    INSERT INTO user_ranking_reviewed_out (`rank`, userId, nickName, studentNumber, totalItemCount, totalScore, 
        appointmentCount, appointmentScore, awardCount, awardScore, 
        conferenceCount, conferenceScore, paperCount, paperScore, 
        patentCount, patentScore, researchCount, researchScore, 
        studentProjectCount, studentProjectScore, 
        teachingProjectCount, teachingProjectScore, 
        textbookCount, textbookScore,
        teachingWorkloadCount, teachingWorkloadScore,
        teachingResearchAwardCount, teachingResearchAwardScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        userId, nickName, studentNumber, totalItemCount, totalScore, 
        appointmentCount, appointmentScore, awardCount, awardScore, 
        conferenceCount, conferenceScore, paperCount, paperScore, 
        patentCount, patentScore, researchCount, researchScore, 
        studentProjectCount, studentProjectScore, 
        teachingProjectCount, teachingProjectScore,
        textbookCount, textbookScore,
        teachingWorkloadCount, teachingWorkloadScore,
        teachingResearchAwardCount, teachingResearchAwardScore
    FROM temp_user_ranking
    ORDER BY totalScore DESC;
    
    TRUNCATE TABLE temp_user_ranking;
    TRUNCATE TABLE last_result_table;
    
    -- 处理全部数据
    CALL get_user_combined_ranking("all", "reviewed", 1000000, 1, 1);
    INSERT INTO temp_user_ranking SELECT * FROM last_result_table;
    SET @row_number = 0;
    INSERT INTO user_ranking_reviewed_all (`rank`, userId, nickName, studentNumber, totalItemCount, totalScore, 
        appointmentCount, appointmentScore, awardCount, awardScore, 
        conferenceCount, conferenceScore, paperCount, paperScore, 
        patentCount, patentScore, researchCount, researchScore, 
        studentProjectCount, studentProjectScore, 
        teachingProjectCount, teachingProjectScore, 
        textbookCount, textbookScore,
        teachingWorkloadCount, teachingWorkloadScore,
        teachingResearchAwardCount, teachingResearchAwardScore)
    SELECT 
        (@row_number:=@row_number + 1) AS `rank`,
        userId, nickName, studentNumber, totalItemCount, totalScore, 
        appointmentCount, appointmentScore, awardCount, awardScore, 
        conferenceCount, conferenceScore, paperCount, paperScore, 
        patentCount, patentScore, researchCount, researchScore, 
        studentProjectCount, studentProjectScore, 
        teachingProjectCount, teachingProjectScore,
        textbookCount, textbookScore,
        teachingWorkloadCount, teachingWorkloadScore,
        teachingResearchAwardCount, teachingResearchAwardScore
    FROM temp_user_ranking
    ORDER BY totalScore DESC;
    
    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_user_ranking;
    DROP TEMPORARY TABLE IF EXISTS last_result_table;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
