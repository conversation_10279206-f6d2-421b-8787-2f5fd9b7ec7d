import request from '../server'

// 社会服务相关接口
const api = {
  list: '/sys/social-service/list',
  detail: '/sys/social-service',
  create: '/sys/social-service',
  update: '/sys/social-service',
  delete: '/sys/social-service',
  import: '/sys/social-service/import',
  export: '/sys/social-service/export',
  typeDistribution: '/sys/social-service/stats/type-distribution',
  conferenceDistribution: '/sys/social-service/stats/conference-distribution',
  competitionDistribution: '/sys/social-service/stats/competition-distribution',
  teacherRanking: '/sys/social-service/stats/teacher-ranking'
}

/**
 * 获取社会服务列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getSocialServices(params) {
  try {
    console.log('调用社会服务列表接口，参数:', params);
    return request.get(api.list, params)
      .then(response => {
        console.log('社会服务列表接口响应:', response);
        return response;
      })
      .catch(error => {
        console.error('社会服务列表接口错误:', error);
        return Promise.reject(error);
      });
  } catch (error) {
    console.error('获取社会服务列表错误:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取社会服务详情
 * @param {string} id - 服务ID
 * @returns {Promise} - 返回Promise对象
 */
export function getSocialServiceDetail(id) {
  try {
    return request.get(`${api.detail}/${id}`)
  } catch (error) {
    console.error('获取社会服务详情错误:', error)
    return Promise.reject(error)
  }
}

/**
 * 创建社会服务
 * @param {Object} data - 服务数据
 * @returns {Promise} - 返回Promise对象
 */
export function addSocialService(data) {
  try {
    console.log('添加社会服务数据:', data)
    return request.post(api.create, data)
  } catch (error) {
    console.error('创建社会服务错误:', error)
    return Promise.reject(error)
  }
}

/**
 * 更新社会服务
 * @param {string} id - 服务ID
 * @param {Object} data - 服务数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateSocialService(id, data) {
  try {
    console.log('更新社会服务数据:', id, data)
    return request.put(`${api.update}/${id}`, data)
  } catch (error) {
    console.error('更新社会服务错误:', error)
    return Promise.reject(error)
  }
}

/**
 * 删除社会服务
 * @param {string} id - 服务ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteSocialService(id) {
  try {
    return request.delete(`${api.delete}/${id}`)
  } catch (error) {
    console.error('删除社会服务错误:', error)
    return Promise.reject(error)
  }
}

/**
 * 导入社会服务数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回Promise对象
 */
export function importSocialServices(file) {
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    return request.post(api.import, formData, null, 'multipart/form-data')
  } catch (error) {
    console.error('导入社会服务数据错误:', error)
    return Promise.reject(error)
  }
}

/**
 * 导出社会服务数据
 * @param {Object} params - 过滤参数
 * @returns {Promise} - 返回Promise对象
 */
export function exportSocialServices(params) {
  try {
    return request.get(api.export, params, { responseType: 'blob' })
  } catch (error) {
    console.error('导出社会服务数据错误:', error)
    return Promise.reject(error)
  }
}

/**
 * 获取服务类型分布
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getServiceTypeDistribution(params) {
  try {
    console.log('调用服务类型分布接口，参数:', params);
    return request.get(api.typeDistribution, params)
      .then(response => {
        console.log('服务类型分布接口响应:', response);
        return response;
      })
      .catch(error => {
        console.error('服务类型分布接口错误:', error);
        return Promise.reject(error);
      });
  } catch (error) {
    console.error('获取服务类型分布错误:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取会议参与分布
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getConferenceDistribution(params) {
  try {
    console.log('调用会议参与分布接口，参数:', params);
    return request.get(api.conferenceDistribution, params)
      .then(response => {
        console.log('会议参与分布接口响应:', response);
        return response;
      })
      .catch(error => {
        console.error('会议参与分布接口错误:', error);
        return Promise.reject(error);
      });
  } catch (error) {
    console.error('获取会议参与分布错误:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取竞赛指导分布
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getCompetitionDistribution(params) {
  try {
    console.log('调用竞赛指导分布接口，参数:', params);
    return request.get(api.competitionDistribution, params)
      .then(response => {
        console.log('竞赛指导分布接口响应:', response);
        return response;
      })
      .catch(error => {
        console.error('竞赛指导分布接口错误:', error);
        return Promise.reject(error);
      });
  } catch (error) {
    console.error('获取竞赛指导分布错误:', error);
    return Promise.reject(error);
  }
}

/**
 * 获取教师得分排名
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getTeacherRanking(params) {
  try {
    return request.get(api.teacherRanking, params)
  } catch (error) {
    console.error('获取教师得分排名错误:', error)
    return Promise.reject(error)
  }
} 