const moduleAlias = require('module-alias');
const path = require('path');

/**
 * alias.js
 *
 *<AUTHOR>
 *@date 2023/11/16
 *@Description:模块别名配置
 */

moduleAlias.addAliases({
    '@': path.join(__dirname),
    '@config': path.join(__dirname, 'config'),
    '@controllers': path.join(__dirname, 'controllers'),
    '@models': path.join(__dirname, 'models'),
    '@routes': path.join(__dirname, 'routes'),
    '@utils': path.join(__dirname, 'utils'),
    '@middleware': path.join(__dirname, 'middleware'),
    '@middlewares': path.join(__dirname, 'middleware'),
    '@db': path.join(__dirname, 'db'),
    '@scheduler': path.join(__dirname, 'scheduler'),
    '@utils/apiResponse': path.join(__dirname, 'utils/apiResponse'),
    '@utils/logger': path.join(__dirname, 'utils/logger')
});

