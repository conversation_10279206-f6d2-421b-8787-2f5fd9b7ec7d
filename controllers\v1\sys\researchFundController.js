const ResearchFund = require('../../../models/v1/mapping/researchFundModel');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

/**
 * 获取科研经费列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getResearchFunds = async (req, res) => {
  try {
    const { name, type, amount, leader, startDate, endDate, page = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) where.name = { [Op.like]: `%${name}%` };
    if (type) where.type = type;
    if (amount) where.amount = { [Op.gte]: amount };
    if (leader) where.leader = { [Op.like]: `%${leader}%` };
    if (startDate) where.startDate = { [Op.gte]: startDate };
    if (endDate) where.endDate = { [Op.lte]: endDate };
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await ResearchFund.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['createdAt', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取科研经费列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取科研经费列表失败',
      data: null
    });
  }
};

/**
 * 获取科研经费详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getResearchFundDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    const fund = await ResearchFund.findByPk(id);
    if (!fund) {
      return res.status(404).json({
        code: 404,
        message: '科研经费不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: fund
    });
  } catch (error) {
    console.error('获取科研经费详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取科研经费详情失败',
      data: null
    });
  }
};

/**
 * 创建科研经费
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createResearchFund = async (req, res) => {
  try {
    const { name, type, amount, startDate, endDate, leader, members, description, score, status } = req.body;
    
    // 校验必填参数
    if (!name) {
      return res.status(400).json({
        code: 400,
        message: '经费名称不能为空',
        data: null
      });
    }
    
    if (!type) {
      return res.status(400).json({
        code: 400,
        message: '经费类型不能为空',
        data: null
      });
    }
    
    if (!amount) {
      return res.status(400).json({
        code: 400,
        message: '经费金额不能为空',
        data: null
      });
    }
    
    // 验证经费金额是否在合理范围内
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum)) {
      return res.status(400).json({
        code: 400,
        message: '经费金额必须是数字',
        data: null
      });
    }
    
    // 检查是否超出DECIMAL(10,2)的范围
    if (amountNum < 0 || amountNum > 99999999.99) {
      return res.status(400).json({
        code: 400,
        message: '经费金额超出范围(0-99999999.99万元)',
        data: null
      });
    }
    
    // 确保最多保留两位小数
    const formattedAmount = parseFloat(amountNum.toFixed(2));
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        code: 400,
        message: '经费起止时间不能为空',
        data: null
      });
    }
    
    if (!leader) {
      return res.status(400).json({
        code: 400,
        message: '负责人不能为空',
        data: null
      });
    }
    
    // 处理成员列表
    let membersStr = '';
    if (members && Array.isArray(members)) {
      membersStr = members.join(',');
    } else if (members && typeof members === 'string') {
      membersStr = members;
    }
    
    // 创建科研经费
    const fund = await ResearchFund.create({
      id: uuidv4(),
      name,
      type,
      amount: formattedAmount,
      startDate,
      endDate,
      leader,
      members: membersStr,
      description,
      score: score || 0,
      status: status !== undefined ? status : 1
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: fund
    });
  } catch (error) {
    console.error('创建科研经费失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建科研经费失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 更新科研经费
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateResearchFund = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type, amount, startDate, endDate, leader, members, description, score, status } = req.body;
    
    // 查找科研经费
    const fund = await ResearchFund.findByPk(id);
    if (!fund) {
      return res.status(404).json({
        code: 404,
        message: '科研经费不存在',
        data: null
      });
    }
    
    // 处理成员列表
    let membersStr = '';
    if (members && Array.isArray(members)) {
      membersStr = members.join(',');
    } else if (members && typeof members === 'string') {
      membersStr = members;
    }
    
    // 处理金额
    let formattedAmount = fund.amount;
    if (amount !== undefined) {
      const amountNum = parseFloat(amount);
      if (isNaN(amountNum)) {
        return res.status(400).json({
          code: 400,
          message: '经费金额必须是数字',
          data: null
        });
      }
      
      // 检查是否超出DECIMAL(10,2)的范围
      if (amountNum < 0 || amountNum > 99999999.99) {
        return res.status(400).json({
          code: 400,
          message: '经费金额超出范围(0-99999999.99万元)',
          data: null
        });
      }
      
      // 确保最多保留两位小数
      formattedAmount = parseFloat(amountNum.toFixed(2));
    }
    
    // 更新科研经费
    await fund.update({
      name: name || fund.name,
      type: type || fund.type,
      amount: formattedAmount,
      startDate: startDate || fund.startDate,
      endDate: endDate || fund.endDate,
      leader: leader || fund.leader,
      members: membersStr || fund.members,
      description: description !== undefined ? description : fund.description,
      score: score !== undefined ? score : fund.score,
      status: status !== undefined ? status : fund.status
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: fund
    });
  } catch (error) {
    console.error('更新科研经费失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新科研经费失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 删除科研经费
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteResearchFund = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找科研经费
    const fund = await ResearchFund.findByPk(id);
    if (!fund) {
      return res.status(404).json({
        code: 404,
        message: '科研经费不存在',
        data: null
      });
    }
    
    // 删除科研经费
    await fund.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除科研经费失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除科研经费失败',
      data: null
    });
  }
};

/**
 * 导入科研经费数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importResearchFunds = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请选择要上传的文件',
        data: null
      });
    }
    
    const filePath = req.file.path;
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    
    const worksheet = workbook.getWorksheet(1);
    const rows = worksheet.getRows(2, worksheet.rowCount); // 从第2行开始，跳过表头
    
    const result = {
      total: rows.length,
      success: 0,
      failed: 0,
      errors: []
    };
    
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      
      try {
        // 从Excel获取数据
        const name = row.getCell(1).value;
        const type = row.getCell(2).value;
        const amount = parseFloat(row.getCell(3).value);
        const startDate = row.getCell(4).value;
        const endDate = row.getCell(5).value;
        const leader = row.getCell(6).value;
        const members = row.getCell(7).value;
        const description = row.getCell(8).value;
        
        // 验证必填字段
        if (!name) {
          throw new Error('经费名称不能为空');
        }
        
        if (!type) {
          throw new Error('经费类型不能为空');
        }
        
        if (!amount || isNaN(amount)) {
          throw new Error('经费金额必须是有效数字');
        }
        
        // 检查金额是否超出DECIMAL(10,2)的范围
        if (amount < 0 || amount > 99999999.99) {
          throw new Error(`行${i+2}的经费金额(${amount})超出范围(0-99999999.99万元)`);
        }
        
        // 确保最多保留两位小数
        const formattedAmount = parseFloat(amount.toFixed(2));
        
        if (!startDate || !endDate) {
          throw new Error('经费起止时间不能为空');
        }
        
        if (!leader) {
          throw new Error('负责人不能为空');
        }
        
        // 计算得分
        let score = 0;
        if (amount <= 20) {
          score = Math.floor(amount);
        } else {
          score = 20;
        }
        
        // 横向经费：单项≥100万视为省级课题
        if (type === 'horizontal' && amount >= 100) {
          score = 20;
        }
        
        // 创建科研经费
        await ResearchFund.create({
          id: uuidv4(),
          name,
          type,
          amount: formattedAmount,
          startDate,
          endDate,
          leader,
          members,
          description,
          score,
          status: 1
        });
        
        result.success++;
      } catch (error) {
        result.failed++;
        result.errors.push({
          row: i + 2, // Excel行号从1开始，加1是从第2行开始，再加1是跳过表头
          message: error.message
        });
      }
    }
    
    // 删除临时文件
    fs.unlinkSync(filePath);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: result
    });
  } catch (error) {
    console.error('导入科研经费数据失败:', error);
    
    // 删除临时文件
    if (req.file && req.file.path) {
      fs.unlinkSync(req.file.path);
    }
    
    return res.status(500).json({
      code: 500,
      message: '导入科研经费数据失败',
      data: null
    });
  }
};

/**
 * 导出科研经费数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportResearchFunds = async (req, res) => {
  try {
    const { name, type, amount, leader, startDate, endDate } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) where.name = { [Op.like]: `%${name}%` };
    if (type) where.type = type;
    if (amount) where.amount = { [Op.gte]: amount };
    if (leader) where.leader = { [Op.like]: `%${leader}%` };
    if (startDate) where.startDate = { [Op.gte]: startDate };
    if (endDate) where.endDate = { [Op.lte]: endDate };
    
    // 查询科研经费
    const funds = await ResearchFund.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });
    
    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('科研经费数据');
    
    // 添加表头
    worksheet.columns = [
      { header: '经费名称', key: 'name', width: 30 },
      { header: '经费类型', key: 'type', width: 15 },
      { header: '经费金额(万元)', key: 'amount', width: 15 },
      { header: '开始时间', key: 'startDate', width: 15 },
      { header: '结束时间', key: 'endDate', width: 15 },
      { header: '负责人', key: 'leader', width: 15 },
      { header: '成员', key: 'members', width: 30 },
      { header: '描述', key: 'description', width: 30 },
      { header: '得分', key: 'score', width: 10 },
      { header: '状态', key: 'status', width: 10 }
    ];
    
    // 添加数据
    for (const fund of funds) {
      worksheet.addRow({
        name: fund.name,
        type: fund.type,
        amount: fund.amount,
        startDate: fund.startDate,
        endDate: fund.endDate,
        leader: fund.leader,
        members: fund.members,
        description: fund.description,
        score: fund.score,
        status: fund.status === 1 ? '有效' : '无效'
      });
    }
    
    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=research-funds.xlsx');
    
    // 写入响应
    await workbook.xlsx.write(res);
    
    return res.end();
  } catch (error) {
    console.error('导出科研经费数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出科研经费数据失败',
      data: null
    });
  }
};

/**
 * 获取当前用户参与的科研经费列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserResearchFunds = async (req, res) => {
  try {
    const { userId, page = 1, pageSize = 10 } = req.query;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '用户ID不能为空',
        data: null
      });
    }
    
    // 构建查询条件：用户是负责人或成员
    const where = {
      [Op.or]: [
        { leader: userId },
        { members: { [Op.like]: `%${userId}%` } }
      ]
    };
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await ResearchFund.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['createdAt', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取用户参与的科研经费列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户参与的科研经费列表失败',
      data: null
    });
  }
};

/**
 * 获取所有科研经费列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllResearchFunds = async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await ResearchFund.findAndCountAll({
      offset,
      limit: Number(pageSize),
      order: [['createdAt', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取所有科研经费列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有科研经费列表失败',
      data: null
    });
  }
}; 