const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const studentAwardGuidanceAwardModel = require('./studentAwardGuidanceAwardsModel');
const userModel = require('./userModel');

// 定义指导学生获奖参与者模型
const StudentAwardGuidanceParticipant = sequelize.define('student_award_guidance_participants', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '记录ID'
    },
    awardId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '奖项ID'
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '用户ID'
    },
    allocationRatio: {
        type: DataTypes.DECIMAL(4, 2),
        allowNull: false,
        comment: '分配比例（0.00-1.00）'
    },
    isLeader: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 0,
        comment: '是否主要指导教师'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'student_award_guidance_participants',
    timestamps: true,
    indexes: [
        {
            name: 'uk_participant',
            unique: true,
            fields: ['awardId', 'userId']
        },
        {
            name: 'idx_award_participant_award',
            fields: ['awardId']
        },
        {
            name: 'idx_award_participant_user',
            fields: ['userId']
        },
        {
            name: 'idx_award_participant_leader',
            fields: ['isLeader']
        }
    ]
});

// 建立与用户的关联关系
StudentAwardGuidanceParticipant.belongsTo(userModel, {
    foreignKey: 'userId',
    as: 'user'
});

module.exports = StudentAwardGuidanceParticipant; 