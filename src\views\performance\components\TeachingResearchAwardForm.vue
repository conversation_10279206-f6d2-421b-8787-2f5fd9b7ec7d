<template>
  <a-modal
    :visible="visible"
    :title="isEdit ? '编辑教学科技奖励' : '新增教学科技奖励'"
    width="800px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <!-- 基本信息 -->
      <a-divider orientation="left">基本信息</a-divider>

      <a-form-item label="获奖名称" name="awardName">
        <a-input
          v-model:value="form.awardName"
          placeholder="请输入获奖名称"
          :maxlength="255"
          show-count
        />
      </a-form-item>

      <a-form-item label="获奖时间" name="awardTime">
        <a-date-picker
          v-model:value="form.awardTime"
          placeholder="请选择获奖时间"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="奖励级别" name="awardLevelId">
        <a-select
          v-model:value="form.awardLevelId"
          placeholder="请选择奖励级别"
          style="width: 100%"
          :loading="levelsLoading"
        >
          <a-select-option
            v-for="level in awardLevels"
            :key="level.id"
            :value="level.id"
          >
            {{ level.levelName }} ({{ level.score }}分)
          </a-select-option>
        </a-select>
      </a-form-item>



      <a-form-item label="系/教研室" name="department">
        <a-input
          v-model:value="form.department"
          placeholder="请输入系/教研室"
          :maxlength="100"
        />
      </a-form-item>

      <!-- 参与人员 -->
      <a-divider orientation="left">参与人员</a-divider>

      <a-form-item label="参与人员" name="participants">
        <a-form-item-rest>
          <div class="participants-container">
            <!-- 参与人员添加区域 -->
            <a-row :gutter="8" style="margin-bottom: 8px;">
              <a-col :xs="24" :sm="10" :md="10">
                <a-select
                  v-model:value="currentParticipant.participantId"
                  placeholder="请选择参与人员"
                  show-search
                  :filter-option="false"
                  :loading="usersLoading"
                  allow-clear
                  @search="handleUserSearch"
                  @change="handleCurrentParticipantChange"
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="user in users"
                    :key="user.id"
                    :value="user.id"
                  >
                    {{ user.nickname || user.username }} ({{ user.studentNumber || user.employeeNumber || '无工号' }})
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :xs="12" :sm="5" :md="5">
                <a-input
                  v-model:value="currentParticipant.employeeNumber"
                  placeholder="工号"
                  readonly
                />
              </a-col>
              <a-col :xs="8" :sm="5" :md="5">
                <a-input-number
                  v-model:value="currentParticipant.allocationRatio"
                  placeholder="分配比例"
                  :min="0"
                  :max="100"
                  :precision="2"
                  style="width: 100%"
                  addon-after="%"
                />
              </a-col>
              <a-col :xs="4" :sm="4" :md="4">
                <a-button
                  type="primary"
                  @click="addParticipant"
                  :disabled="!currentParticipant.participantId"
                  style="width: 100%"
                >
                  <PlusOutlined />
                </a-button>
              </a-col>
            </a-row>

            <!-- 已添加的参与人员列表 -->
            <div v-if="form.participants.length > 0" class="added-participants">
              <a-divider style="margin: 8px 0">已添加参与人员</a-divider>
              <p style="color: #666; font-size: 12px; margin-bottom: 8px;">
                已添加 {{ form.participants.length }} 位参与人员
              </p>
              <a-list
                :data-source="form.participants"
                size="small"
                bordered
              >
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <a-row style="width: 100%" align="middle">
                      <a-col :span="6">
                        <strong>{{ item.displayName }}</strong>
                      </a-col>
                      <a-col :span="5">
                        <span style="color: #666;">{{ item.employeeNumber || '无工号' }}</span>
                      </a-col>
                      <a-col :span="4">
                        <a-form-item-rest>
                          <a-switch
                            :checked="item.isLeader"
                            @change="(checked) => toggleLeader(index, checked)"
                            checkedChildren="负责人"
                            unCheckedChildren="参与者"
                            size="small"
                          />
                        </a-form-item-rest>
                      </a-col>
                      <a-col :span="5">
                        <a-form-item-rest>
                          <a-input-number
                            v-model:value="item.allocationRatio"
                            :min="0"
                            :max="100"
                            :precision="2"
                            size="small"
                            style="width: 100%"
                            addon-after="%"
                          />
                        </a-form-item-rest>
                      </a-col>
                      <a-col :span="4" style="text-align: right;">
                        <a-button
                          type="text"
                          danger
                          size="small"
                          @click="removeParticipant(index)"
                        >
                          <DeleteOutlined />
                        </a-button>
                      </a-col>
                    </a-row>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </a-form-item-rest>
      </a-form-item>

      <!-- 附件上传 -->
      <a-divider orientation="left">附件信息</a-divider>

      <a-form-item name="attachments" label="相关附件">
        <div style="display: flex; align-items: center; margin-bottom: 8px;">
          <a-upload
            v-model:file-list="fileList"
            :customRequest="handleFileUpload"
            :before-upload="beforeUpload"
            multiple
            :show-upload-list="false"
          >
            <a-button type="primary">
              <template #icon><UploadOutlined /></template>
              选择文件
            </a-button>
          </a-upload>
          <span style="margin-left: 16px; color: #666; font-size: 12px;">
            支持上传文档、图片或压缩文件，单个文件不超过10MB
          </span>
        </div>

        <!-- 使用表格显示已上传文件 -->
        <a-table
          :columns="fileColumns"
          :data-source="fileList"
          :pagination="false"
          size="small"
          style="margin-top: 16px;"
          rowKey="uid"
          v-if="fileList.length > 0"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'fileName'">
              <span :title="record.name">{{ record.originalFileName || record.name }}</span>
            </template>
            <template v-if="column.key === 'fileSize'">
              {{ formatFileSize(record.size || (record.data && record.data.size)) }}
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'done' ? 'success' : (record.status === 'error' ? 'error' : 'processing')">
                {{ record.status === 'done' ? '已上传' : (record.status === 'error' ? '上传失败' : '上传中') }}
              </a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="previewFile(record)" v-if="record.status === 'done'">
                  <template #icon><EyeOutlined /></template>
                  预览
                </a-button>
                <a-button type="link" size="small" @click="downloadFile(record)" v-if="record.status === 'done'">
                  <template #icon><DownloadOutlined /></template>
                  下载
                </a-button>
                <a-popconfirm
                  title="确定要删除该文件吗？此操作将同时删除服务器上的文件"
                  @confirm="confirmDeleteFile(record)"
                  okText="确认"
                  cancelText="取消"
                >
                  <a-button type="link" danger size="small">
                    <template #icon><DeleteOutlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="form.remark"
          placeholder="请输入备注信息"
          :rows="3"
          :maxlength="500"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  DeleteOutlined,
  PlusOutlined,
  UploadOutlined,
  EyeOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { getAwardLevels, createTeachingResearchAward, updateTeachingResearchAward } from '@/api/modules/api.teachingResearchAwards'
import { usersSearch } from '@/api/modules/api.users'
import { uploadFiles } from '@/api/modules/api.file'
import { uploadFile as utilUploadFile, previewFileById, downloadFileById, deleteFileById } from '@/utils/others'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const levelsLoading = ref(false)
const usersLoading = ref(false)
const formRef = ref()
const awardLevels = ref([])
const users = ref([])
const fileList = ref([])

// 用户搜索相关
const userSearchTimeout = ref(null)

// 文件列表表格列定义
const fileColumns = [
  {
    title: '文件名',
    key: 'fileName',
    ellipsis: true
  },
  {
    title: '大小',
    key: 'fileSize',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 280
  }
]

// 当前正在添加的参与人员
const currentParticipant = reactive({
  participantId: '',
  displayName: '',
  employeeNumber: '',
  allocationRatio: 0
})

// 表单数据
const form = reactive({
  awardName: '',
  awardTime: null,
  awardLevelId: '',
  department: '',
  remark: '',
  attachmentUrl: '',
  fileIds: [],
  participants: []
})

// 表单验证规则
const rules = {
  awardName: [
    { required: true, message: '请输入获奖名称', trigger: 'blur' }
  ],
  awardTime: [
    { required: true, message: '请选择获奖时间', trigger: 'change' }
  ],
  awardLevelId: [
    { required: true, message: '请选择奖励级别', trigger: 'change' }
  ],
  participants: [
    {
      validator: (rule, value) => {
        if (!value || value.length === 0) {
          return Promise.reject('请至少添加一位参与人员')
        }
        const hasLeader = value.some(p => p.isLeader)
        if (!hasLeader) {
          return Promise.reject('请指定一位负责人')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ]
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadAwardLevels()
    loadUsers()
    if (props.isEdit && props.formData) {
      initFormData()
    } else {
      resetForm()
    }
  }
})

// 加载奖励级别
const loadAwardLevels = async () => {
  try {
    levelsLoading.value = true
    const response = await getAwardLevels()
    if (response.code === 200) {
      awardLevels.value = response.data || []
    } else {
      console.error('加载奖励级别失败:', response.message)
      awardLevels.value = []
      message.error(response.message || '加载奖励级别失败')
    }
  } catch (error) {
    console.error('加载奖励级别失败:', error)
    awardLevels.value = []
    message.error('加载奖励级别失败')
  } finally {
    levelsLoading.value = false
  }
}

// 加载用户列表
const loadUsers = async (searchKeyword = '') => {
  try {
    usersLoading.value = true
    const response = await usersSearch({
      keyword: searchKeyword,
      pageSize: 50 // 获取更多用户选项
    })
    if (response.code === 200) {
      users.value = response.data || []
    } else {
      message.error(response.message || '加载用户列表失败')
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    message.error('加载用户列表失败')
  } finally {
    usersLoading.value = false
  }
}

// 初始化表单数据
const initFormData = () => {
  const data = props.formData
  console.log('初始化表单数据:', data)

  Object.assign(form, {
    awardName: data.awardName || '',
    awardTime: data.awardTime ? dayjs(data.awardTime) : null,
    awardLevelId: data.awardLevelId || '',
    department: data.department || '',
    remark: data.remark || '',
    attachmentUrl: data.attachmentUrl || '',
    participants: data.participants?.map(p => {
      console.log('处理参与人员:', p)
      // 从API返回的数据结构中获取用户信息
      const participant = p.participant || {}
      const displayName = participant.nickname || participant.username || participant.realName || '未知用户'
      const employeeNumber = participant.studentNumber || participant.employeeNumber || ''

      return {
        id: p.id || Date.now() + Math.random(), // 确保有唯一ID
        participantId: p.participantId,
        displayName: displayName,
        employeeNumber: employeeNumber,
        allocationRatio: (parseFloat(p.allocationRatio) * 100) || 0, // 转换为百分比显示
        isLeader: Boolean(p.isLeader)
      }
    }) || []
  })

  console.log('初始化后的参与人员:', form.participants)

  // 处理附件 - 使用API返回的attachments数据
  if (data.attachments && Array.isArray(data.attachments)) {
    fileList.value = data.attachments.map((file, index) => ({
      uid: `-${index}`,
      name: file.name || file.originalName || `附件${index + 1}`,
      status: 'done',
      url: file.url,
      response: { file: { uid: file.id } },
      data: file
    }))

    // 设置fileIds
    form.fileIds = data.attachments.map(file => file.id)
  } else {
    // 兼容旧的处理方式
    if (data.attachmentUrl) {
      fileList.value = [{
        uid: '1',
        name: '附件',
        status: 'done',
        url: data.attachmentUrl
      }]
    }

    // 如果有fileIds，初始化文件列表
    if (data.fileIds && Array.isArray(data.fileIds)) {
      form.fileIds = [...data.fileIds]
    }
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    awardName: '',
    awardTime: null,
    awardLevelId: '',
    department: '',
    remark: '',
    attachmentUrl: '',
    fileIds: [],
    participants: []
  })

  // 重置当前参与人员数据
  Object.assign(currentParticipant, {
    participantId: '',
    displayName: '',
    employeeNumber: '',
    allocationRatio: 0
  })

  fileList.value = []
}

// 当前参与人员选择处理
const handleCurrentParticipantChange = (userId) => {
  if (userId && users.value.length > 0) {
    const selectedUser = users.value.find(user => user.id === userId)
    if (selectedUser) {
      currentParticipant.displayName = selectedUser.nickname || selectedUser.username || '未知'
      currentParticipant.employeeNumber = selectedUser.studentNumber || selectedUser.employeeNumber || ''
    }
  } else {
    // 清空时重置
    currentParticipant.displayName = ''
    currentParticipant.employeeNumber = ''
  }
}

// 添加参与人员
const addParticipant = () => {
  if (!currentParticipant.participantId) {
    message.error('请选择参与人员')
    return
  }

  // 检查是否已经添加过该用户
  const existingParticipant = form.participants.find(p => p.participantId === currentParticipant.participantId)
  if (existingParticipant) {
    message.error('该参与人员已经添加过了')
    return
  }

  // 如果是第一个参与人员，默认设为负责人
  const isFirstParticipant = form.participants.length === 0

  // 添加到参与人员列表
  form.participants.push({
    id: Date.now() + Math.random(), // 添加唯一ID
    participantId: currentParticipant.participantId,
    displayName: currentParticipant.displayName,
    employeeNumber: currentParticipant.employeeNumber,
    allocationRatio: currentParticipant.allocationRatio,
    isLeader: isFirstParticipant // 第一个参与人员默认为负责人
  })

  // 重置当前参与人员数据
  currentParticipant.participantId = ''
  currentParticipant.displayName = ''
  currentParticipant.employeeNumber = ''
  currentParticipant.allocationRatio = 0

  message.success('参与人员添加成功')
}

// 移除参与人员
const removeParticipant = (index) => {
  const participant = form.participants[index]

  // 如果移除的是负责人，需要检查是否还有其他参与人员
  if (participant.isLeader && form.participants.length > 1) {
    // 将第一个非当前参与人员设为负责人
    const nextLeaderIndex = form.participants.findIndex((p, i) => i !== index)
    if (nextLeaderIndex !== -1) {
      form.participants[nextLeaderIndex].isLeader = true
    }
  }

  form.participants.splice(index, 1)
  message.success('参与人员移除成功')
}

// 切换负责人状态
const toggleLeader = (index, isLeader) => {
  // 如果设置为负责人，先将其他所有人设为非负责人
  if (isLeader) {
    form.participants.forEach((p, i) => {
      if (i !== index) {
        p.isLeader = false
      }
    })
  } else {
    // 如果尝试取消负责人，检查是否还有其他负责人
    const hasOtherLeader = form.participants.some((p, i) => i !== index && p.isLeader)
    if (!hasOtherLeader) {
      message.warning('项目必须有一个负责人')
      // 防止切换，保持当前参与人为负责人
      form.participants[index].isLeader = true
      return
    }
  }

  // 修改指定参与人的负责人状态
  form.participants[index].isLeader = isLeader
}

// 用户搜索处理
const handleUserSearch = (value) => {
  // 清除之前的定时器
  if (userSearchTimeout.value) {
    clearTimeout(userSearchTimeout.value)
  }

  // 如果输入为空，清空选项
  if (!value || value.trim() === '') {
    users.value = []
    return
  }

  // 设置300ms延迟，避免频繁请求
  userSearchTimeout.value = setTimeout(() => {
    loadUsers(value.trim())
  }, 300)
}



// 文件上传前处理
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB！')
    return false
  }
  return true
}

// 处理文件上传
const handleFileUpload = ({ file, onSuccess, onError, onProgress }) => {
  utilUploadFile({
    file,
    uploadApi: uploadFiles,
    id: form.id || props.formData?.id || 'temp_' + Date.now(),
    relatedId: form.id || props.formData?.id,
    class: 'teaching_research_awards',
    onProgress,
    onSuccess: (res) => {
      if (res && res.code === 200 && res.data) {
        // 将文件ID添加到form.fileIds中
        if (!form.fileIds) form.fileIds = []
        form.fileIds.push(res.data.id)

        // 设置attachmentUrl为文件夹路径（与其他项目保持一致）
        if (res.data.filePath && !form.attachmentUrl) {
          // 提取文件夹路径，去掉文件名
          const folderPath = res.data.filePath.substring(0, res.data.filePath.lastIndexOf('\\') + 1)
          form.attachmentUrl = folderPath
        }
      }
      onSuccess(res)
    },
    onError,
    fileList: fileList.value,
    formState: null // 不传递formState，避免utils/others.js修改attachmentUrl为数组
  })
}

// 文件大小格式化函数
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 文件预览函数
const previewFile = (file) => {
  console.log('预览文件对象:', file)
  const fileId = file.response?.file?.uid || file.data?.id
  if (fileId) {
    previewFileById(fileId)
  } else {
    message.warning('无法获取文件ID，预览失败')
  }
}

// 文件下载函数
const downloadFile = (file) => {
  const fileId = file.response?.file?.uid || file.data?.id
  const fileName = file.name || file.originalFileName || '下载文件'
  if (fileId) {
    downloadFileById(fileId, fileName)
  } else {
    message.warning('无法获取文件ID，下载失败')
  }
}

// 确认删除文件
const confirmDeleteFile = async (file) => {
  try {
    const fileId = file.response?.file?.uid || file.data?.id
    if (fileId) {
      const result = await deleteFileById(fileId)
      if (result.success !== false) {
        // 从文件列表中移除
        const index = fileList.value.findIndex(f => f.uid === file.uid)
        if (index > -1) {
          fileList.value.splice(index, 1)
        }

        // 从form.fileIds中移除
        if (form.fileIds) {
          const fileIdIndex = form.fileIds.indexOf(fileId)
          if (fileIdIndex > -1) {
            form.fileIds.splice(fileIdIndex, 1)
          }
        }

        message.success('文件删除成功')
      }
    } else {
      message.warning('无法获取文件ID，删除失败')
    }
  } catch (error) {
    console.error('删除文件失败:', error)
    message.error('删除文件失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    // 找到负责人
    const leader = form.participants.find(p => p.isLeader)
    if (!leader) {
      message.error('请指定一位负责人')
      loading.value = false
      return
    }

    // 准备提交数据
    const submitData = {
      awardName: form.awardName,
      awardTime: form.awardTime ? form.awardTime.format('YYYY-MM-DD') : null,
      awardLevelId: form.awardLevelId,
      firstResponsibleId: leader.participantId, // 使用负责人的ID
      department: form.department,
      remark: form.remark,
      attachmentUrl: Array.isArray(form.attachmentUrl) ? form.attachmentUrl[0] || '' : (form.attachmentUrl || ''), // 确保是字符串
      fileIds: form.fileIds || [],
      participants: form.participants.map(p => ({
        participantId: p.participantId,
        allocationRatio: p.allocationRatio / 100, // 转换为小数
        isLeader: p.isLeader
      }))
    }

    // 如果是编辑模式，添加ID
    if (props.isEdit && props.formData?.id) {
      submitData.id = props.formData.id
    }

    // 调用API
    let response
    if (props.isEdit) {
      response = await updateTeachingResearchAward(submitData)
    } else {
      response = await createTeachingResearchAward(submitData)
    }

    if (response.code === 200) {
      message.success(props.isEdit ? '更新成功' : '创建成功')
      emit('success')
      handleCancel()
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    if (error.errors) {
      message.error('请检查表单填写')
    } else {
      message.error(error.message || '操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}
</script>

<style scoped>
.participants-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.participant-item {
  margin-bottom: 8px;
}

.participant-item:last-child {
  margin-bottom: 0;
}

.upload-tip {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}
</style>
