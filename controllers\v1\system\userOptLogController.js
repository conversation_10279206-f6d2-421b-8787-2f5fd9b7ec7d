const userOptLogModel = require('../../../models/v1/mapping/userOptLogModel');
const {body, validationResult} = require('express-validator');
const apiResponse = require('../../../utils/apiResponse');
const {Op} = require('sequelize');
const {authMiddleware, checkApiPermission, checkUserRole} = require("../../../middleware/authMiddleware");
/**
 * 获取所有操作日志
 * @route POST /v1/sys/optLog/list
 * @group 操作日志 - 用户操作日志管理接口
 * @param {object} query.body - 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'createdAt', order: 'ascend'}}
 * @returns {object} 200 - {status: "success", message: "Success.", data: {result: [], current: 1, pageSize: 15, total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getAll = [
    authMiddleware,
    checkApiPermission('sys:optLog:list'),
    async (req, res, next) => {
        try {
            // 从请求中获取分页、排序和查询参数
            let query = req.body;
            let params = query.params || {};
            let current = Number(query.pagination?.current || 1) || 1;
            let pageSize = Number(query.pagination?.pageSize || 15) || 15;
            let sortColumn = query.sort?.columnKey || 'createdAt';
            let sortOrder = query.sort?.order === 'ascend' ? 'ASC' : 'DESC';

            // 构建查询条件
            let whereConditions = {};
            for (let key in params) {
                if (params.hasOwnProperty(key)) {
                    whereConditions[key] = {[Op.like]: `%${params[key]}%`};
                }
            }
            // 查询数据库获取整张表的总记录数
            const totalCount = await userOptLogModel.count();
            // 查询数据库
            const optInfo = await userOptLogModel.findAll({
                where: whereConditions,
                order: [[sortColumn, sortOrder]],
                offset: (current - 1) * pageSize,
                limit: pageSize,
            });
            return apiResponse.successResponseWithData(res, "Success.", {
                result: optInfo,
                current,
                pageSize,
                total: totalCount
            })
        } catch (err) {
            next(err);
        }
    }
];


/**
 * 创建操作日志
 * @route POST /v1/sys/optLog/create
 * @group 操作日志 - 用户操作日志管理接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} action.body.required - 操作行为
 * @param {string} description.body - 操作描述
 * @param {string} ip.body - IP地址
 * @param {string} userAgent.body - 用户代理
 * @returns {object} 200 - {status: "success", message: "操作日志创建成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.create = [
    authMiddleware,
    checkApiPermission('sys:optLog:create'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            // 创建新操作日志
            await userOptLogModel.create({...req.body});
            return apiResponse.successResponse(res, "操作日志创建成功.");
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取单个操作日志
 * @route POST /v1/sys/optLog/findOne
 * @group 操作日志 - 用户操作日志管理接口
 * @param {number} id.body.required - 日志ID
 * @returns {object} 200 - {status: "success", message: "Success.", data: {日志信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */

exports.findOne = [
    authMiddleware,
    checkApiPermission('sys:optLog:findOne'),
    body("id").notEmpty().withMessage('操作日志ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            const optId = req.body.id;
            const optInfo = await userOptLogModel.findByPk(optId);

            if (!optInfo) {
                return apiResponse.notFoundResponse(res, "操作日志不存在.");
            }

            return apiResponse.successResponseWithData(res, "Success.", optInfo);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 更新操作日志
 * @route POST /v1/sys/optLog/update
 * @group 操作日志 - 用户操作日志管理接口
 * @param {number} id.body.required - 日志ID
 * @param {string} action.body - 操作行为
 * @param {string} description.body - 操作描述
 * @param {string} ip.body - IP地址
 * @param {string} userAgent.body - 用户代理
 * @returns {object} 200 - {status: "success", message: "操作日志更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.update = [
    authMiddleware,
    checkApiPermission('sys:optLog:update'),
    body("id").notEmpty().withMessage('操作日志ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const optId = req.body.id;

            const optInfo = await userOptLogModel.findByPk(optId);
            if (!optInfo) {
                return apiResponse.notFoundResponse(res, "操作日志不存在.");
            }

            await optInfo.update({...req.body});

            return apiResponse.successResponse(res, "操作日志更新成功.");
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 删除操作日志
 * @route POST /v1/sys/optLog/delete
 * @group 操作日志 - 用户操作日志管理接口
 * @param {number} id.body.required - 日志ID
 * @returns {object} 200 - {status: "success", message: "操作日志删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.delete = [
    authMiddleware,
    checkApiPermission('sys:optLog:delete'),
    body("id").notEmpty().withMessage('操作日志ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const optId = req.body.id;

            const optInfo = await userOptLogModel.findByPk(optId);
            if (!optInfo) {
                return apiResponse.notFoundResponse(res, "操作日志不存在.");
            }

            await optInfo.destroy();

            return apiResponse.successResponse(res, "操作日志删除成功.");
        } catch (err) {
            next(err);
        }
    }
];
