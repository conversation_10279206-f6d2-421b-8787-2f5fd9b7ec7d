const express = require('express');
const router = express.Router();
const scoreWeightController = require('../../../controllers/v1/rules/scoreWeightsController');

/**
 * 获取评分权重列表
 * @route GET /v1/sys/score-weights/weights
 * @group 评分权重管理 - 评分权重相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/weights', scoreWeightController.getScoreWeights);

/**
 * 获取所有权重及使用情况
 * @route GET /v1/sys/score-weights/weights-with-usage
 * @group 评分权重管理 - 评分权重相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/weights-with-usage', scoreWeightController.getWeightsWithUsage);

/**
 * 获取评分权重详情
 * @route GET /v1/sys/score-weights/weight/:id
 * @group 评分权重管理 - 评分权重相关接口
 * @param {string} id.path.required - 权重ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/weight/:id', scoreWeightController.getWeightDetail);

/**
 * 创建评分权重
 * @route POST /v1/sys/score-weights/weight/create
 * @group 评分权重管理 - 评分权重相关接口
 * @param {string} categoryName.body.required - 类别名称
 * @param {string} categoryCode.body.required - 类别代码
 * @param {number} weight.body - 权重系数
 * @param {string} description.body - 描述
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/weight/create', scoreWeightController.createWeight);

/**
 * 更新评分权重
 * @route POST /v1/sys/score-weights/weight/update
 * @group 评分权重管理 - 评分权重相关接口
 * @param {string} id.body.required - 权重ID
 * @param {string} categoryName.body - 类别名称
 * @param {string} categoryCode.body - 类别代码
 * @param {number} weight.body - 权重系数
 * @param {string} description.body - 描述
 * @param {number} status.body - 状态：0-禁用，1-启用
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/weight/update', async (req, res) => {
  const { id, ...updateData } = req.body;
  req.params = { id };
  req.body = updateData;
  await scoreWeightController.updateWeight(req, res);
});

/**
 * 删除评分权重
 * @route POST /v1/sys/score-weights/weight/delete
 * @group 评分权重管理 - 评分权重相关接口
 * @param {string} id.body.required - 权重ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/weight/delete', async (req, res) => {
  const { id } = req.body;
  req.params = { id };
  await scoreWeightController.deleteWeight(req, res);
});

module.exports = router;