const express = require('express');
const router = express.Router();
const studentProjectGuidanceProjectController = require('../../../controllers/v1/studentProjectGuidanceProjects/studentProjectGuidanceProjectsController');
const multer = require('multer');
const path = require('path');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建学生项目指导项目权限中间件函数
const projectsPermission = (action) => createModulePermission('studentProjectGuidanceProjects', action);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/student_project_guidance/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'student-project-guidance-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

/**
 * 获取立项列表
 * @route POST /v1/sys/student-project-guidance-projects/list
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {string} projectName - 项目名称（模糊搜索）
 * @param {string} levelId - 项目级别ID
 * @param {number} startYear - 执行起始年
 * @param {number} endYear - 执行结束年
 * @param {string} userId - 用户ID（可选，传入则获取用户参与的立项）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 范围筛选，可选值：all, in, out
 * @param {string} reviewStatus - 审核状态筛选，可选值：all, reviewed, unreviewed
 * @param {boolean} isExport - 是否导出数据，设为true时返回所有数据不分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, current: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', 
  authMiddleware, 
  projectsPermission('list'), 
  studentProjectGuidanceProjectController.getProjects
);

/**
 * 导入立项数据
 * @route POST /v1/sys/student-project-guidance-projects/projects/import
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects/import', 
  authMiddleware, 
  projectsPermission('import'), 
  upload.single('file'), 
  studentProjectGuidanceProjectController.importProjects
);

/**
 * 导出立项数据
 * @route POST /v1/sys/student-project-guidance-projects/projects/export
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {string} projectName - 项目名称（模糊搜索）
 * @param {string} levelId - 项目级别ID
 * @param {number} startYear - 执行起始年
 * @param {number} endYear - 执行结束年
 * @param {string} range - 范围筛选
 * @param {string} reviewStatus - 审核状态筛选
 * @returns {object} 200 - {code: 200, message: "导出成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects/export', 
  authMiddleware, 
  projectsPermission('export'), 
  studentProjectGuidanceProjectController.exportProjects
);

/**
 * 创建立项
 * @route POST /v1/sys/student-project-guidance/project/create
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {string} projectName.body.required - 项目名称
 * @param {string} projectNumber.body - 项目编号
 * @param {string} approvalDepartment.body.required - 下达部门
 * @param {string} approvalDate.body.required - 获批日期
 * @param {number} startYear.body.required - 执行起始年
 * @param {number} endYear.body.required - 执行结束年
 * @param {string} levelId.body.required - 项目级别ID
 * @param {string} remark.body - 备注
 * @param {Array} participants.body.required - 参与者数组，包含userId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/create', 
  authMiddleware, 
  projectsPermission('create'), 
  upload.array('files', 5), 
  studentProjectGuidanceProjectController.createProject
);

/**
 * 更新立项
 * @route POST /v1/sys/student-project-guidance-projects/project/update
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {string} id.body.required - 项目ID
 * @param {string} projectName.body - 项目名称
 * @param {string} projectNumber.body - 项目编号
 * @param {string} approvalDepartment.body - 下达部门
 * @param {string} approvalDate.body - 获批日期
 * @param {number} startYear.body - 执行起始年
 * @param {number} endYear.body - 执行结束年
 * @param {string} levelId.body - 项目级别ID
 * @param {string} remark.body - 备注
 * @param {Array} participants.body - 参与者数组，包含userId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @param {Array} deletedFileIds.body - 要删除的文件ID数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/update', 
  authMiddleware, 
  projectsPermission('update'), 
  upload.array('files', 5), 
  async (req, res) => {
    await studentProjectGuidanceProjectController.updateProject(req, res);
  }
);

/**
 * 删除立项
 * @route POST /v1/sys/student-project-guidance-projects/project/delete
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/delete', 
  authMiddleware, 
  projectsPermission('delete'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await studentProjectGuidanceProjectController.deleteProject(req, res);
  }
);

/**
 * 获取立项详情
 * @route POST /v1/sys/student-project-guidance-projects/project/detail
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/detail', 
  authMiddleware, 
  projectsPermission('detail'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await studentProjectGuidanceProjectController.getProjectDetail(req, res);
  }
);

/**
 * 获取立项时间分布数据
 * @route POST /v1/sys/student-project-guidance-projects/statistics/time-distribution
 * @group 指导学生立项统计 - 指导学生立项统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的立项
 * @param {string} reviewStatus - 审核状态: 'all',  'rejected', 'pending', 'reviewed'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {months: ["YYYY-MM",...], data: [数量,...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/time-distribution', 
  authMiddleware, 
  projectsPermission('timeDistribution'), 
  studentProjectGuidanceProjectController.getTimeDistribution
);

/**
 * 审核立项
 * @route POST /v1/sys/student-project-guidance-projects/project/review
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {string} id.body.required - 项目ID
 * @param {boolean} reviewStatus.body.required - 审核状态
 * @param {string} reviewComment.body - 审核意见
 * @param {string} reviewer.body.required - 审核人ID
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/review', 
  authMiddleware, 
  projectsPermission('review'), 
  studentProjectGuidanceProjectController.reviewProject
);

/**
 * 获取项目级别分布数据
 * @route POST /v1/sys/student-project-guidance-projects/statistics/level-distribution
 * @group 指导学生立项统计 - 指导学生立项级别分布统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} userId - 用户ID，可选
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{levelName: '级别名称', count: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/level-distribution', 
  authMiddleware, 
  projectsPermission('levelDistribution'), 
  studentProjectGuidanceProjectController.getLevelDistribution
);

/**
 * 获取教师项目排名数据
 * @route POST /v1/sys/student-project-guidance-projects/statistics/teacher-ranking
 * @group 指导学生立项统计 - 教师项目排名统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} reviewStatus - 审核状态: 'all',  'rejected', 'pending', 'reviewed'
 * @param {number} page - 页码
 * @param {number} pageSize - 每页记录数
 * @param {boolean} isExport - 是否导出所有数据，导出时不应用分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{userId, userName, studentNumber, totalProjects, totalScore}], pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-ranking', 
  authMiddleware, 
  projectsPermission('teacherRanking'), 
  studentProjectGuidanceProjectController.getTeacherProjectRanking
);

/**
 * 获取教师项目详情
 * @route POST /v1/sys/student-project-guidance-projects/statistics/teacher-projects
 * @group 指导学生立项统计 - 教师项目详情
 * @param {string} userId - 用户ID
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 数据范围
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], totalScore: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-project-details', 
  authMiddleware, 
  projectsPermission('teacherProjectDetails'), 
  studentProjectGuidanceProjectController.getTeacherProjectDetails
);

/**
 * 获取项目统计概览数据
 * @route POST /v1/sys/student-project-guidance-projects/statistics/overview
 * @group 指导学生立项统计 - 项目统计概览
 * @param {string} userId - 用户ID，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalProjects, activeProjects, averageScore, reviewedRate}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/overview', 
  authMiddleware, 
  projectsPermission('overview'), 
  studentProjectGuidanceProjectController.getProjectStatistics
);

/**
 * 获取项目总分统计
 * @route POST /v1/sys/student-project-guidance-projects/statistics/projects-total-score
 * @group 指导学生立项统计 - 统计分数相关接口
 * @param {string} range - 数据范围: 'in' (统计范围内), 'out' (统计范围外), 'all' (全部，默认)
 * @param {string} reviewStatus - 审核状态: 'rejected' (已拒绝), 'pending' (待审核), 'reviewed' (已审核), 'all' (全部，默认)
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {levelStats: [{levelId, levelName, count, totalScore}], overallStats: {totalProjects, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/projects-total-score', 
  authMiddleware, 
  projectsPermission('projectsTotalScore'), 
  studentProjectGuidanceProjectController.getProjectsTotalScore
);

/**
 * 获取用户项目详情
 * @route POST /v1/sys/student-project-guidance-projects/user/details
 * @group 指导学生立项统计 - 用户项目详情相关接口
 * @param {string} userId.required - 用户ID
 * @param {string} range - 数据范围: 'in' (统计范围内), 'out' (统计范围外), 'all' (全部，默认)
 * @param {string} reviewStatus - 审核状态: 'rejected' (已拒绝), 'pending' (待审核), 'reviewed' (已审核), 'all' (全部，默认)
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{项目详情}], totalCount: 总记录数, statistics: {totalProjects, leaderProjectCount, participantProjectCount, totalScore}, timeInterval: {startTime, endTime, name}, pagination: {page, pageSize, total}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/details', 
  authMiddleware, 
  projectsPermission('userDetails'), 
  studentProjectGuidanceProjectController.getUserProjectsDetail
);

/**
 * 重新提交学生项目指导项目审核
 * @route POST /v1/sys/student-project-guidance-projects/project/reapply
 * @group 指导学生立项管理 - 指导学生立项相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/reapply',
  authMiddleware,
  projectsPermission('reapply'),
  studentProjectGuidanceProjectController.reapply
);

/**
 * 获取审核状态概览
 * @route POST /v1/sys/student-project-guidance-projects/statistics/review-status-overview
 * @group 指导学生立项统计 - 指导学生立项统计相关接口
 * @param {string} range.body - 查询范围：'in'|'out'|'all'，默认'all'
 * @param {string} userId.body - 用户ID，可选
 * @returns {object} 200 - 审核状态统计数据
 * @security JWT
 */
router.post('/statistics/review-status-overview',
  authMiddleware,
  projectsPermission('reviewStatusOverview'),
  studentProjectGuidanceProjectController.getReviewStatusOverview
);

module.exports = router;