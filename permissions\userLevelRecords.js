/**
 * 用户职称记录模块权限配置
 * 简化版 - 角色验证
 */
module.exports = {
  // 查看用户职称记录列表权限
  list: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 管理员和教师都可以查看职称记录列表
  },
  
  // 创建用户职称记录权限
  create: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以创建职称记录
  },
  
  // 更新用户职称记录权限
  update: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以更新职称记录
  },
  
  // 删除用户职称记录权限
  delete: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以删除职称记录
  },
  
  // 查看用户职称记录详情权限
  detail: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 管理员和教师都可以查看职称记录详情
  }
};
