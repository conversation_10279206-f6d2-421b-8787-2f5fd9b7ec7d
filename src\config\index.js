/**
 * 全局配置文件
 * 集中管理系统配置，统一维护并方便修改
 */

// 强制使用相对路径，不管环境变量如何设置
const apiBaseUrl = '/v1';

console.log('环境变量中的API基础URL12345:', import.meta.env.VITE_API_BASE_URL);
console.log('使用的API基础URL:', apiBaseUrl);
console.log('当前环境:', import.meta.env.MODE);

const config = {
  // 后端API基本URL
  apiBaseUrl: apiBaseUrl,
  
  // 前端服务器配置
  frontendServer: {
    port: import.meta.env.VITE_PORT || 3090,
    host: import.meta.env.VITE_HOST || '0.0.0.0'
  },
  
  // 是否允许跨域请求时携带cookie
  withCredentials: false, // 强制设置为false，避免CORS问题
  
  // 其他配置
  requestTimeout: parseInt(import.meta.env.VITE_TIMEOUT || '10000'),
  appTitle: import.meta.env.VITE_APP_TITLE || '暨南大学基础医学与公共卫生学院教师绩效评定与管理平台',
  
  // 当前环境
  env: import.meta.env.MODE
};

// 调试信息
console.log('应用配置信息12345:', {
  环境: config.env,
  API地址: config.apiBaseUrl,
  超时设置: config.requestTimeout,
  应用标题: config.appTitle
});

export default config;
