<template>
  <div v-show="getShow">
    <h2 class="login-title">
      登录
      <span v-if="state.isAdminMode" class="mode-indicator admin-mode">管理员模式</span>
      <span v-else class="mode-indicator teacher-mode">教师模式</span>
    </h2>
    <a-form
        loading
        :model="state.formState"
        name="normal_login"
        class="login-form"
        @finish="onFinish"
        @finishFailed="onFinishFailed"
    >
      <a-form-item
          name="username"
          :rules="[{ required: true, message: '请输入用户名或昵称!' }]"
      >
        <a-input v-model:value="state.formState.username" allowClear autocomplete="off" size="large"
                 placeholder="用户名或昵称">
          <template #prefix>
            <IconFont type="icon-yonghuming" style="font-size: 18px; color: #7fc7a0;"/>
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
          name="password"
          :rules="[{ required: true, message: '请输入密码!' }]"
      >
        <a-input-password v-model:value="state.formState.password" allowClear autocomplete="off" placeholder="密码"
                          size="large">
          <template #prefix>
            <IconFont type="icon-mima" style="font-size: 18px; color: #7fc7a0;"/>
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item
          name="code"
          :rules="[{ required: true, message: '请输入验证码!' }]"
      >
        <div style="display: flex">
          <a-input v-model:value="state.formState.code" allowClear autocomplete="off" size="large" placeholder="验证码">
            <template #prefix>
              <IconFont type="icon-yanzhengma" style="font-size: 18px; color: #7fc7a0;"/>
            </template>
          </a-input>
          <div class="login-code" v-html="state.captchaSvg" @click="getCaptcha"></div>
        </div>
      </a-form-item>
      <a-form-item>
        <a-form-item name="remember" no-style>
          <a-checkbox v-model:checked="state.formState.remember">记住密码</a-checkbox>
        </a-form-item>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" style="width: 100%" :disabled="disabled" html-type="submit" size="large"
                  class="login-form-button">
          登录
        </a-button>
      </a-form-item>
    </a-form>
    <div>
      <a-space class="login-btn-list">
        <a-button disabled @click="setLoginState('register')" class="login-form-button">
          注册
        </a-button>
        <a-button @click="toggleLoginMode" class="login-form-button" :type="state.isAdminMode ? 'primary' : 'default'">
          {{ state.isAdminMode ? '切换到教师' : '管理员登录' }}
        </a-button>
        <a-button disabled class="login-form-button">
          手机登录
        </a-button>
        <a-button disabled class="login-form-button">
          扫二维码登录
        </a-button>
      </a-space>
    </div>

  </div>
</template>

<script setup>
import {reactive, computed} from 'vue';

import {useAuthStore} from '../../../../stores/auth.js';
import {useLoginState} from '@/hooks/sys/useLogin.js';
import {authCaptcha} from "../../../../api/modules/api.auth";

const authStore = useAuthStore()
const {setLoginState, getLoginState} = useLoginState();
const getShow = computed(() => {
  return getLoginState.value === 'login'
});


const state = reactive({
  formState: {
    username: 'teacher_003',
    password: '123456',
    code: ''
  },
  captchaSvg: '',
  isAdminMode: false // 添加管理员模式状态
})

const getCaptcha = () => {
  authCaptcha().then(res => {
    state.captchaSvg = res.data
    console.log('获取验证码成功:', res.data)
  }).catch(err => {
    console.error('获取验证码失败:', err)
  })
}

// 切换登录模式的方法
const toggleLoginMode = () => {
  state.isAdminMode = !state.isAdminMode
  if (state.isAdminMode) {
    // 切换到管理员模式
    state.formState.username = 'admin'
    state.formState.password = '123456'
  } else {
    // 切换到教师模式
    state.formState.username = 'teacher_003'
    state.formState.password = '123456'
  }
}

const onFinish = values => {
  console.log('提交登录请求到真实服务器:', {...values, password: '***'});
  authStore.login(values).then(() => {
    console.log('登录成功');
  }).catch(err => {
    console.error('登录失败:', err);
    getCaptcha();
  })
};
const onFinishFailed = errorInfo => {
  console.log('Failed:', errorInfo);
};
const disabled = computed(() => {
  return !(state.formState.username && state.formState.password);
});

getCaptcha()

</script>

<style lang="scss" scoped>
$jnu-blue: #7fc7a0;
$jnu-gold: #e6bf7c;

.login-title {
  margin-bottom: 30px;
  color: $jnu-blue;
  display: flex;
  align-items: center;
  gap: 12px;

  .mode-indicator {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: normal;

    &.admin-mode {
      background-color: #ff4d4f;
      color: white;
    }

    &.teacher-mode {
      background-color: $jnu-blue;
      color: white;
    }
  }
}

.hr {
  font-size: .9rem;
  color: #575656;
}

.login-form {
  max-width: 400px;
  background-color: #fff;
  overflow: hidden;

  .login-code {
    cursor: pointer;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin-left: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
    
    &:hover {
      border-color: $jnu-blue;
    }
  }
}

.login-btn-list {
  display: flex;
  flex-wrap: wrap;
  
  .login-form-button {
    &:hover {
      color: $jnu-blue;
      border-color: $jnu-blue;
    }
  }
}

:deep(.ant-btn-primary) {
  background-color: $jnu-blue;
  border-color: $jnu-blue;
  
  &:hover, &:focus {
    background-color: lighten($jnu-blue, 10%);
    border-color: lighten($jnu-blue, 10%);
  }
}

:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: $jnu-blue;
  border-color: $jnu-blue;
}

:deep(.ant-input:hover), :deep(.ant-input:focus), :deep(.ant-input-affix-wrapper:hover), :deep(.ant-input-affix-wrapper:focus) {
  border-color: $jnu-blue;
}

:deep(.ant-input-affix-wrapper-focused) {
  border-color: $jnu-blue;
  box-shadow: 0 0 0 2px rgba(0, 62, 126, 0.2);
}

.other-login-type {
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 22px;

  .type-item {
    cursor: pointer;

    &:hover {
      color: $jnu-blue !important;
    }
  }
}
</style>
