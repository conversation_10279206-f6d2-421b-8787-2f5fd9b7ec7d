const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const researchProjectParticipantsModel = sequelize.define(
  'research_project_participants',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '记录ID'
    },
    projectId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '项目ID'
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '用户ID'
    },
    allocationRatio: {
      type: DataTypes.DECIMAL(4, 2),
      allowNull: false,
      comment: '分配比例（0.00-1.00）'
    },
    participantRank: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '参与者排名'
    },
    isLeader: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否项目负责人'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '更新时间'
    }
  },
  {
    sequelize,
    modelName: 'research_project_participants',
    tableName: 'research_project_participants',
    timestamps: true
  }
);

// 在文件加载后添加关联，避免循环依赖问题
const setupAssociations = () => {
  const userModel = require('./userModel');
  const researchProjectModel = require('./researchProjectModel');

  // 与用户表关联
  researchProjectParticipantsModel.belongsTo(userModel, {
    foreignKey: 'userId',
    as: 'user'
  });

  // 与项目表关联
  researchProjectParticipantsModel.belongsTo(researchProjectModel, {
    foreignKey: 'projectId',
    as: 'project'
  });
};

// 导出模型时调用关联设置
setTimeout(setupAssociations, 0);

module.exports = researchProjectParticipantsModel; 