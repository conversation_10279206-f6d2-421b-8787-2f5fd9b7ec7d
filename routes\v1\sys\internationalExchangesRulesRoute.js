const express = require('express');
const router = express.Router();
const internationalExchangesRulesController = require('../../../controllers/v1/sys/internationalExchangesRulesController');

/**
 * 获取国际交流规则列表
 * @route GET /v1/sys/international-exchanges-rules/list
 * @group 国际交流规则管理 - 国际交流规则相关接口
 * @param {string} project.query - 项目名称（模糊搜索）
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 0, page: 1, pageSize: 10, list: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list',  internationalExchangesRulesController.getInternationalExchangesRules);

/**
 * 获取国际交流规则详情
 * @route GET /v1/sys/international-exchanges-rules/detail
 * @group 国际交流规则管理 - 国际交流规则相关接口
 * @param {string} id.query - 规则ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {规则详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail', internationalExchangesRulesController.getInternationalExchangeRuleDetail);

/**
 * 创建国际交流规则
 * @route POST /v1/sys/international-exchanges-rules/create
 * @group 国际交流规则管理 - 国际交流规则相关接口
 * @param {string} project.body.required - 项目名称
 * @param {number} score.body.required - 分数
 * @param {string} description.body - 描述
 * @param {string} additional_info.body - 附加信息
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {创建的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', internationalExchangesRulesController.createInternationalExchangeRule);

/**
 * 更新国际交流规则
 * @route PUT /v1/sys/international-exchanges-rules/update
 * @group 国际交流规则管理 - 国际交流规则相关接口
 * @param {string} id.body.required - 规则ID
 * @param {string} project.body - 项目名称
 * @param {number} score.body - 分数
 * @param {string} description.body - 描述
 * @param {string} additional_info.body - 附加信息
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {更新后的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/update', internationalExchangesRulesController.updateInternationalExchangeRule);

/**
 * 删除国际交流规则
 * @route DELETE /v1/sys/international-exchanges-rules/delete
 * @group 国际交流规则管理 - 国际交流规则相关接口
 * @param {string} id.query.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete', internationalExchangesRulesController.deleteInternationalExchangeRule);

/**
 * 批量删除国际交流规则
 * @route POST /v1/sys/international-exchanges-rules/batch-delete
 * @group 国际交流规则管理 - 国际交流规则相关接口
 * @param {array} ids.body.required - 规则ID列表
 * @returns {object} 200 - {code: 200, message: "批量删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/batch-delete', internationalExchangesRulesController.batchDeleteInternationalExchangeRules);

module.exports = router; 