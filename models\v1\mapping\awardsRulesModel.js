const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义奖项核算规则模型
module.exports = sequelize.define('awards_rules', // 数据库表名为awards_rules
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: '主键，使用 UUID 唯一标识每条记录',
        },
        awardLevel: {
            type: DataTypes.STRING(255),
            notNull: true,
            allowNull: false,
            comment: '奖项级别（如国家级一等奖、省部级二等奖等）',
        },
        rank: {
            type: DataTypes.INTEGER,
            notNull: true,
            allowNull: false,
            comment: '完成人排序（第几位）',
        },
        score: {
            type: DataTypes.DECIMAL(10, 2),
            notNull: true,
            allowNull: false,
            comment: '核算分数',
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: '奖项的详细描述（如国家级一等奖、省部级二等奖等）',
        },
        unitCount: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 1,
            comment: '合作单位数目（用于分数除法）',
        },
        createdBy: {
            type: DataTypes.UUID,
            notNull: true,
            allowNull: false,
            comment: '创建者 ID（userId）',
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录创建时间',
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录最后修改时间',
        },
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
        indexes: [
            {
                unique: true,
                fields: ['award_level', 'rank'],
                name: 'uk_award_level_rank'
            }
        ]
    }); 