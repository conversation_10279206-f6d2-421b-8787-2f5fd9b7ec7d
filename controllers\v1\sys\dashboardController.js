const { Teacher, User } = require('../../../models');
const Award = require('../../../models/v1/mapping/awardsModel');
const SocialService = require('../../../models/v1/mapping/socialServicesModel');
const EmploymentQuality = require('../../../models/v1/mapping/employmentQualityModel');
const Deduction = require('../../../models/v1/mapping/deductionModel');
const { Op, Sequelize } = require('sequelize');

/**
 * 获取总体数据统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTotalStatistics = async (req, res) => {
  try {
    // 获取总教师数
    const totalTeachers = await Teacher.count({ where: { status: 1 } });
    
    // 获取总奖项数
    const totalAwards = await Award.count({ where: { status: 1 } });
    
    // 获取总社会服务数
    const totalServices = await SocialService.count({ where: { status: 1 } });
    
    // 获取总就业质量记录数
    const totalEmployments = await EmploymentQuality.count({ where: { status: 1 } });
    
    // 获取总扣分记录数
    const totalDeductions = await Deduction.count({ where: { status: 1 } });
    
    // 计算总得分
    const totalScores = {
      awards: await Award.sum('score', { where: { status: 1 } }) || 0,
      services: await SocialService.sum('score', { where: { status: 1 } }) || 0,
      employments: await EmploymentQuality.sum('score', { where: { status: 1 } }) || 0,
      deductions: await Deduction.sum('deductionScore', { where: { status: 1 } }) || 0
    };
    
    // 计算净总分
    const netTotalScore = totalScores.awards + totalScores.services + totalScores.employments - totalScores.deductions;
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalTeachers,
        totalAwards,
        totalServices,
        totalEmployments,
        totalDeductions,
        scores: totalScores,
        netTotalScore
      }
    });
  } catch (error) {
    console.error('获取总体数据统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取总体数据统计失败',
      data: null
    });
  }
};

/**
 * 获取各模块得分占比
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getScoreDistribution = async (req, res) => {
  try {
    // 获取各模块总分
    const awardsScore = await Award.sum('score', { where: { status: 1 } }) || 0;
    const servicesScore = await SocialService.sum('score', { where: { status: 1 } }) || 0;
    const employmentsScore = await EmploymentQuality.sum('score', { where: { status: 1 } }) || 0;
    
    // 计算总分和占比
    const totalScore = awardsScore + servicesScore + employmentsScore;
    
    const distribution = [
      {
        name: '教学与科研获奖',
        value: awardsScore,
        percentage: totalScore ? ((awardsScore / totalScore) * 100).toFixed(2) : 0
      },
      {
        name: '社会服务',
        value: servicesScore,
        percentage: totalScore ? ((servicesScore / totalScore) * 100).toFixed(2) : 0
      },
      {
        name: '就业质量',
        value: employmentsScore,
        percentage: totalScore ? ((employmentsScore / totalScore) * 100).toFixed(2) : 0
      }
    ];
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalScore,
        distribution
      }
    });
  } catch (error) {
    console.error('获取各模块得分占比失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取各模块得分占比失败',
      data: null
    });
  }
};

/**
 * 获取教师排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherRanking = async (req, res) => {
  try {
    const { limit = 10, module } = req.query;
    
    let teachers = [];
    const limitNum = parseInt(limit);
    
    if (module === 'awards') {
      // 获取奖项评分排名
      const awardRanking = await Award.findAll({
        attributes: [
          'teacher',
          [Sequelize.fn('SUM', Sequelize.col('score')), 'totalScore'],
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        where: { status: 1 },
        group: ['teacher'],
        order: [[Sequelize.literal('totalScore'), 'DESC']],
        limit: limitNum
      });
      
      teachers = awardRanking.map(item => ({
        name: item.teacher,
        score: parseFloat(item.getDataValue('totalScore') || 0),
        count: parseInt(item.getDataValue('count') || 0),
        module: '教学与科研获奖'
      }));
    } else if (module === 'services') {
      // 获取社会服务评分排名
      const serviceRanking = await SocialService.findAll({
        attributes: [
          'teacher',
          [Sequelize.fn('SUM', Sequelize.col('score')), 'totalScore'],
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        where: { status: 1 },
        group: ['teacher'],
        order: [[Sequelize.literal('totalScore'), 'DESC']],
        limit: limitNum
      });
      
      teachers = serviceRanking.map(item => ({
        name: item.teacher,
        score: parseFloat(item.getDataValue('totalScore') || 0),
        count: parseInt(item.getDataValue('count') || 0),
        module: '社会服务'
      }));
    } else if (module === 'deductions') {
      // 获取扣分排名
      const deductionRanking = await Deduction.findAll({
        attributes: [
          'teacherName',
          [Sequelize.fn('SUM', Sequelize.col('deductionScore')), 'totalScore'],
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        where: { status: 1 },
        group: ['teacherName'],
        order: [[Sequelize.literal('totalScore'), 'DESC']],
        limit: limitNum
      });
      
      teachers = deductionRanking.map(item => ({
        name: item.teacherName,
        score: parseFloat(item.getDataValue('totalScore') || 0),
        count: parseInt(item.getDataValue('count') || 0),
        module: '扣分'
      }));
    } else {
      // 获取综合排名（奖项+社会服务-扣分）
      // 此处需要在应用层面合并计算
      const awardScores = await Award.findAll({
        attributes: [
          'teacher',
          [Sequelize.fn('SUM', Sequelize.col('score')), 'score']
        ],
        where: { status: 1 },
        group: ['teacher']
      });
      
      const serviceScores = await SocialService.findAll({
        attributes: [
          'teacher',
          [Sequelize.fn('SUM', Sequelize.col('score')), 'score']
        ],
        where: { status: 1 },
        group: ['teacher']
      });
      
      const deductionScores = await Deduction.findAll({
        attributes: [
          'teacherName',
          [Sequelize.fn('SUM', Sequelize.col('deductionScore')), 'score']
        ],
        where: { status: 1 },
        group: ['teacherName']
      });
      
      // 合并所有教师得分
      const teacherScoresMap = new Map();
      
      awardScores.forEach(item => {
        const name = item.teacher;
        const score = parseFloat(item.getDataValue('score') || 0);
        teacherScoresMap.set(name, {
          name,
          score,
          deductionScore: 0
        });
      });
      
      serviceScores.forEach(item => {
        const name = item.teacher;
        const score = parseFloat(item.getDataValue('score') || 0);
        
        if (teacherScoresMap.has(name)) {
          const current = teacherScoresMap.get(name);
          current.score += score;
        } else {
          teacherScoresMap.set(name, {
            name,
            score,
            deductionScore: 0
          });
        }
      });
      
      deductionScores.forEach(item => {
        const name = item.teacherName;
        const deductionScore = parseFloat(item.getDataValue('score') || 0);
        
        if (teacherScoresMap.has(name)) {
          const current = teacherScoresMap.get(name);
          current.deductionScore = deductionScore;
        } else {
          teacherScoresMap.set(name, {
            name,
            score: 0,
            deductionScore
          });
        }
      });
      
      // 计算净得分并排序
      const teacherRankingArray = Array.from(teacherScoresMap.values())
        .map(item => ({
          name: item.name,
          score: item.score - item.deductionScore,
          grossScore: item.score,
          deductionScore: item.deductionScore,
          module: '综合评分'
        }))
        .sort((a, b) => b.score - a.score);
      
      teachers = teacherRankingArray.slice(0, limitNum);
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: teachers
    });
  } catch (error) {
    console.error('获取教师排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师排名失败',
      data: null
    });
  }
};

/**
 * 获取评分趋势
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getScoreTrend = async (req, res) => {
  try {
    const { startYear, endYear } = req.query;
    
    // 获取年份范围
    const currentYear = new Date().getFullYear();
    const start = startYear ? parseInt(startYear) : currentYear - 4;
    const end = endYear ? parseInt(endYear) : currentYear;
    
    // 存储各年份得分
    const yearlyScores = [];
    
    for (let year = start; year <= end; year++) {
      // 获取指定年份的奖项得分
      const awardScore = await Award.sum('score', {
        where: {
          status: 1,
          [Op.and]: [
            Sequelize.where(Sequelize.fn('YEAR', Sequelize.col('awardDate')), year)
          ]
        }
      }) || 0;
      
      // 获取指定年份的社会服务得分
      const serviceScore = await SocialService.sum('score', {
        where: {
          status: 1,
          [Op.and]: [
            Sequelize.where(Sequelize.fn('YEAR', Sequelize.col('startDate')), year)
          ]
        }
      }) || 0;
      
      // 获取指定年份的就业质量得分
      const employmentScore = await EmploymentQuality.sum('score', {
        where: {
          status: 1,
          year: year.toString()
        }
      }) || 0;
      
      // 获取指定年份的扣分
      const deductionScore = await Deduction.sum('deductionScore', {
        where: {
          status: 1,
          [Op.and]: [
            Sequelize.where(Sequelize.fn('YEAR', Sequelize.col('deductionDate')), year)
          ]
        }
      }) || 0;
      
      yearlyScores.push({
        year,
        awards: parseFloat(awardScore),
        services: parseFloat(serviceScore),
        employments: parseFloat(employmentScore),
        deductions: parseFloat(deductionScore),
        total: parseFloat(awardScore) + parseFloat(serviceScore) + parseFloat(employmentScore),
        net: parseFloat(awardScore) + parseFloat(serviceScore) + parseFloat(employmentScore) - parseFloat(deductionScore)
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: yearlyScores
    });
  } catch (error) {
    console.error('获取评分趋势失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取评分趋势失败',
      data: null
    });
  }
};

/**
 * 获取各模块指标完成情况
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getModuleCompletion = async (req, res) => {
  try {
    const { year } = req.query;
    const targetYear = year || new Date().getFullYear().toString();
    
    // 设定各模块的目标分数（实际应该从配置中获取）
    const targets = {
      awards: 1000,
      services: 800,
      employments: 600
    };
    
    // 获取当年奖项得分
    const awardScore = await Award.sum('score', {
      where: {
        status: 1,
        [Op.and]: [
          Sequelize.where(Sequelize.fn('YEAR', Sequelize.col('awardDate')), targetYear)
        ]
      }
    }) || 0;
    
    // 获取当年社会服务得分
    const serviceScore = await SocialService.sum('score', {
      where: {
        status: 1,
        [Op.and]: [
          Sequelize.where(Sequelize.fn('YEAR', Sequelize.col('startDate')), targetYear)
        ]
      }
    }) || 0;
    
    // 获取当年就业质量得分
    const employmentScore = await EmploymentQuality.sum('score', {
      where: {
        status: 1,
        year: targetYear
      }
    }) || 0;
    
    // 计算完成率
    const completion = [
      {
        module: '教学与科研获奖',
        target: targets.awards,
        actual: parseFloat(awardScore),
        completion: ((parseFloat(awardScore) / targets.awards) * 100).toFixed(2)
      },
      {
        module: '社会服务',
        target: targets.services,
        actual: parseFloat(serviceScore),
        completion: ((parseFloat(serviceScore) / targets.services) * 100).toFixed(2)
      },
      {
        module: '就业质量',
        target: targets.employments,
        actual: parseFloat(employmentScore),
        completion: ((parseFloat(employmentScore) / targets.employments) * 100).toFixed(2)
      }
    ];
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        year: targetYear,
        completion
      }
    });
  } catch (error) {
    console.error('获取各模块指标完成情况失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取各模块指标完成情况失败',
      data: null
    });
  }
}; 