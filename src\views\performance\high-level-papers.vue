<template>
    <div class="performance-container high-level-papers-container">
      <!-- 添加错误信息展示区域 -->
      <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
      
      <a-card title="高水平论文管理" :bordered="false" class="performance-card">
        <template #extra>
          <a-space>
            <!-- <a-upload
              :customRequest="handleImport"
              :show-upload-list="false"
              :before-upload="beforeUpload"
            >
              <a-button type="primary" v-permission="'score:highLevelPapers:admin:update'">
                <template #icon><UploadOutlined /></template>
                导入数据
              </a-button>
            </a-upload>
            <a-upload
              :customRequest="handleJsonImport"
              :show-upload-list="false"
              :before-upload="beforeJsonUpload"
              accept=".json"
            >
              <a-button type="primary" v-permission="'score:highLevelPapers:admin:update'">
                <template #icon><UploadOutlined /></template>
                导入JSON数据
              </a-button>
            </a-upload> -->
            <a-upload
              :customRequest="handleExcelToJsonConvert"
              :show-upload-list="false"
              :before-upload="beforeExcelUpload"
              accept=".xlsx,.xls,.csv"
            >
              <a-button type="primary" v-permission="'score:highLevelPapers:admin:update'">
                <template #icon><FileExcelOutlined /></template>
                Excel数据导入
              </a-button>
            </a-upload>
            <a-button type="primary" @click="showAddModal" v-permission="'score:highLevelPapers:self:create'">
              <template #icon><PlusOutlined /></template>
              添加论文
            </a-button>
            <a-button :type="showPersonalPapers ? 'default' : 'primary'" @click="togglePersonalPapers" v-permission="'score:highLevelPapers:admin'">
              <template #icon><UserOutlined /></template>
              {{ showPersonalPapers ? '查看全部论文' : '查看我的论文' }}
            </a-button>
          </a-space>
        </template>
        <!-- 论文填写说明区域 -->
        <a-card title="论文填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
          <a-alert
            class="mb-16"
            message="论文统计时间范围"
            :description="`统计时间：${timeRangeText || '加载中...'}`"
            type="info"
            show-icon
          />
          <div class="rule-content">
            <p><strong>填写说明：</strong></p>
            <ol class="detail-list">
              <li>出版时间范围在统计时间内，以图书馆检索证明为准</li>
              <li>收录情况必须出具图书馆的检索证明和论文首页</li>
              <li>参与分配者按照贡献多少，由多到少依次填写</li>
              <li>"分配比例基数"和"分配比例"填写小数，不要填写百分数</li>
              <li>中文论文必须属于北图核心目录</li>
              <li>该表由通讯作者填写，如第一作者单位我院而通讯作者不是，由第一作者填写</li>
              <li>出现在本表中的人必须为我院教职工</li>
              <li>分配比例基数根据通讯作者情况计算</li>
              <li>所有作者分配比例"总分配比例"应为1</li>
            </ol>
          </div>
        </a-card>
        
        <!-- 全局统计数据概览 - 始终显示在最上面 -->
        <!-- <a-row :gutter="16" style="margin-bottom: 24px" v-if="!showPersonalPapers">
          <a-col :span="24">
            <a-card :bordered="false">
              <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <div class="title" style="font-size: 16px; font-weight: bold;">高水平论文统计概览</div>
                <div class="filter">
                  <a-select
                    v-model:value="overallStatsYear"
                    style="width: 120px"
                    placeholder="选择年份"
                    @change="loadOverallStats"
                  >
                    <a-select-option v-for="year in availableYears" :key="year" :value="year">{{ year }}</a-select-option>
                  </a-select>
                </div>
              </div>
                <a-row :gutter="16">
                  <a-col :span="6">
                    <a-statistic 
                      title="论文总数" 
                      :value="overallStats.paperCount || 0"
                      :value-style="{ fontSize: '24px', color: '#1890ff' }"
                    >
                      <template #prefix>
                        <file-text-outlined />
                      </template>
                    </a-statistic>
                  </a-col>
                  <a-col :span="6">
                    <a-statistic 
                      title="总得分" 
                      :value="overallStats.totalScore || 0" 
                      :precision="2"
                      :value-style="{ fontSize: '24px', color: '#3f8600' }"
                    >
                      <template #prefix>
                        <trophy-outlined />
                      </template>
                    </a-statistic>
                  </a-col>
                  <a-col :span="6">
                    <a-statistic 
                      title="平均分/篇" 
                      :value="averageScore" 
                      :precision="2"
                      :value-style="{ fontSize: '24px', color: '#722ed1' }"
                    >
                      <template #prefix>
                        <rise-outlined />
                      </template>
                    </a-statistic>
                  </a-col>
                  <a-col :span="6">
                    <a-statistic 
                      title="类型数量" 
                      :value="Object.keys(overallStats.typeDistribution || {}).length"
                      :value-style="{ fontSize: '24px', color: '#fa8c16' }"
                    >
                      <template #prefix>
                        <appstore-outlined />
                      </template>
                    </a-statistic>
                  </a-col>
                </a-row>
            </a-card>
          </a-col>
        </a-row> -->
        
        <!-- 个人统计卡片 - 个人视图 -->
        <!-- <a-row :gutter="16" style="margin-bottom: 24px" v-if="showPersonalPapers">
          <a-col :span="24">
            <a-card :bordered="false">
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-statistic 
                      title="我的总得分" 
                      :value="personalStats.totalScore || 0" 
                      :precision="2"
                      :value-style="{ fontSize: '24px', color: '#3f8600' }"
                    >
                      <template #prefix>
                        <trophy-outlined />
                      </template>
                    </a-statistic>
                  </a-col>
                  <a-col :span="8">
                    <a-statistic 
                      title="论文总数" 
                      :value="personalStats.paperCount || 0"
                      :value-style="{ fontSize: '24px', color: '#1890ff' }"
                    >
                      <template #prefix>
                        <file-text-outlined />
                      </template>
                    </a-statistic>
                  </a-col>
                  <a-col :span="8">
                    <a-statistic 
                      title="院内排名" 
                      :value="personalStats.rank || '--'" 
                      :value-style="{ fontSize: '24px', color: '#cf1322' }"
                    >
                      <template #suffix>
                        <span>/ {{ personalStats.totalUsers || '--' }}</span>
                      </template>
                      <template #prefix>
                        <crown-outlined />
                      </template>
                    </a-statistic>
                  </a-col>
                </a-row>
            </a-card>
          </a-col>
        </a-row> -->
        
        <!-- 图表区域 -->
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="审核状态分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="reviewStatusChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('reviewStatus', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="reviewStatusChartRef" id="reviewStatusChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="论文类型分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="typeChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('type', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="typeChartRef" id="typeChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="年度论文分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="yearChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('year', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="yearChartRef" id="yearChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="影响因子分布" size="small" class="performance-card chart-container impact-factor-chart">
              <template #extra>
                <a-select
                  v-model:value="impactFactorChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('impactFactor', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="impactFactorChartRef" id="impactFactorChartContainer" class="chart-wrapper echarts-container"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <!-- 添加用户论文得分统计表格，放在所有图表之后、搜索表单之前 -->
        
        <!-- 用户论文得分统计表格 -->
        <a-card title="用户论文得分统计" :bordered="false" style="margin-top: 24px;">
          <template #extra>
            <a-space>
              <a-input-search
                v-model:value="userScoreSearchParams.nickname"
                v-permission="'score:highLevelPapers:admin:list'"
                placeholder="用户昵称"
                style="width: 150px;"
                @search="fetchAllUsersTotalScore"
                @pressEnter="fetchAllUsersTotalScore"
              />
              <a-select
                v-model:value="userScoreChartRange"
                style="width: 150px;"
                @change="handleUserScoreRangeChange"
              >
                <a-select-option value="in">统计范围内</a-select-option>
                <a-select-option value="out">统计范围外</a-select-option>
                <a-select-option value="all">全部论文</a-select-option>
              </a-select>
              <!-- <a-select
                v-model:value="userScoreSearchParams.reviewStatus"
                style="width: 150px;"
                placeholder="审核状态"
                @change="fetchAllUsersTotalScore"
              >
                <a-select-option value="all">全部</a-select-option>
                <a-select-option value="reject">已拒绝</a-select-option>
                <a-select-option value="pending">待审核</a-select-option>
                <a-select-option value="reviewed">已审核</a-select-option>
              </a-select> -->
              <a-button type="primary" @click="exportUserScoreData" :loading="exporting" v-permission="'score:highLevelPapers:admin:list'">
                <template #icon><DownloadOutlined /></template>
                导出
              </a-button>
            </a-space>
          </template>
          <a-table
            :columns="userScoreColumns"
            :data-source="userScoreData"
            :pagination="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' ? userScorePagination : false"
            :loading="userScoreLoading"
            rowKey="userId"
            @change="handleUserScoreTableChange"
            :scroll="{ x: 800 }"
            :bordered="true"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'rank'">
                <a-tag :color="getRankColor(record.rank)">{{ record.rank }}</a-tag>
              </template>
              <template v-else-if="column.key === 'totalScore'">
                <span style="font-weight: bold; color: #1890ff;">{{ record.totalScore ? parseFloat(record.totalScore).toFixed(2) : '0.00' }}分</span>
              </template>
              <template v-else-if="column.key === 'details'">
                <a-button type="link" @click="showUserScoreDetails(record)"
                v-if="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId"
                >
                  查看详情
                </a-button>
              </template>
            </template>
          </a-table>
        </a-card>
        
        <!-- 搜索表单 -->
        <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
          <a-form :model="searchForm" @finish="handleSearch" layout="vertical" class="performance-form">
            <a-row :gutter="[12, 8]">
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="论文题目" name="title">
                  <a-input
                    v-model:value="searchForm.title"
                    placeholder="请输入论文题目"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4" v-permission="'score:highLevelPapers:admin:list'">
                <a-form-item label="作者" name="authors">
                  <a-input
                    v-model:value="searchForm.authors"
                    placeholder="请输入作者姓名"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="论文类型" name="type">
                  <a-select
                    v-model:value="searchForm.type"
                    placeholder="请选择论文类型"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="item in paperTypeOptions" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="期刊名称" name="journal">
                  <a-input
                    v-model:value="searchForm.journal"
                    placeholder="请输入期刊名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="出版时间" name="publishDate">
                  <a-range-picker
                    @change="handleDateRangeChange"
                    :format="'YYYY-MM-DD'"
                    style="width: 100%"
                    :placeholder="['开始日期', '结束日期']"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="审核状态" name="reviewStatus">
                  <a-select
                    v-model:value="searchForm.reviewStatus"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部论文</a-select-option>
                    <a-select-option value="reviewed">已审核</a-select-option>
                    <a-select-option value="rejected">已拒绝</a-select-option>
                    <a-select-option value="pending">待审核</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="统计范围" name="range">
                  <a-select
                    v-model:value="searchForm.range"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="in">统计范围内</a-select-option>
                    <a-select-option value="out">统计范围外</a-select-option>
                    <a-select-option value="all">全部论文</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
                <a-form-item label=" " style="margin-bottom: 0;">
                  <div class="search-actions-inline">
                    <a-button type="primary" html-type="submit" size="default">
                      <template #icon><search-outlined /></template>
                      搜索
                    </a-button>
                    <a-button @click="resetSearch" size="default">
                      <template #icon><reload-outlined /></template>
                      重置
                    </a-button>
                    <a-button type="default" @click="exportFilteredPapers" :loading="exportingTable" size="default">
                      <template #icon><DownloadOutlined /></template>
                      导出
                    </a-button>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
        
        <!-- 数据表格 -->
        <div class="performance-table">
          <a-table
            :columns="columns"
            :data-source="dataSource"
            :loading="loading"
            :pagination="pagination"
            @change="handleTableChange"
            rowKey="id"
            :scroll="{ x: 1200 }"
            :bordered="true"
          >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ record.title }}</span>
            </template>
            <template v-else-if="column.key === 'authors'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ record.authors }}</span>
            </template>
            <template v-else-if="column.key === 'journal'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ record.journal }}</span>
            </template>
            <template v-else-if="column.key === 'calculatedScore'">
              <span v-if="record.isInTimeRange !== false" style="font-weight: bold; color: #1890ff;">{{ record.calculatedScore ? parseFloat(record.calculatedScore).toFixed(2) : '0.00' }}分</span>
              <span v-else style="color: #999999;">不计分</span>
            </template>
            <template v-else-if="column.key === 'ifReviewer'">
              <a-tag :color="record.ifReviewer == true ? 'success' : (record.ifReviewer == false ? 'error' : 'warning')">
                {{ record.ifReviewer == 1 ? '已审核' : (record.ifReviewer == false ? '已拒绝' : '待审核') }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'reviewComment'">
              <span style="word-break: break-all; white-space: pre-wrap;">{{ record.reviewComment }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-dropdown placement="bottomRight" :trigger="['click']">
                <template #overlay>
                  <a-menu class="action-dropdown-menu">
                    <!-- 编辑选项 -->
                    <a-menu-item
                      key="edit"
                      v-if="record.ifReviewer != 1 && hasPerms(showPersonalPapers ? 'score:highLevelPapers:self:update' : 'score:highLevelPapers:admin:update')"
                    >
                      <a @click="showEditModal(record)" class="action-menu-item">
                        <EditOutlined />
                        <span>编辑</span>
                      </a>
                    </a-menu-item>

                    <!-- 重新提交审核选项 -->
                    <a-menu-item
                      key="resubmit"
                      v-if="record.ifReviewer == 0 && hasPerms('score:highLevelPapers:self:reapply')"
                    >
                      <a @click="handleResubmit(record)" class="action-menu-item">
                        <ReloadOutlined />
                        <span>重新提交审核</span>
                      </a>
                    </a-menu-item>

                    <!-- 审核选项 - 仅管理员视图显示 -->
                    <a-menu-item
                      key="review"
                      v-if="!showPersonalPapers && !record.ifReviewer && hasPerms('score:highLevelPapers:admin:review')"
                    >
                      <a @click="showReviewModal(record)" class="action-menu-item">
                        <AuditOutlined />
                        <span>审核</span>
                      </a>
                    </a-menu-item>

                    <a-menu-divider v-if="record.ifReviewer != 1 || (!showPersonalPapers && !record.ifReviewer)" />

                    <!-- 删除选项 -->
                    <a-menu-item
                      key="delete"
                      v-if="hasPerms(showPersonalPapers ? 'score:highLevelPapers:self:delete' : 'score:highLevelPapers:admin:delete')"
                    >
                      <a @click="confirmDelete(record)" class="action-menu-item text-danger">
                        <DeleteOutlined />
                        <span>删除</span>
                      </a>
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small" class="action-trigger-btn">
                  操作
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </template>
          </template>
          </a-table>

          <div class="table-footer">
            <div class="table-info">
              <span>总分：{{ totalScore }}分</span>
            </div>
          </div>
        </div>
        
        <!-- 新增/编辑模态框 -->
        <a-modal
          :title="modalTitle"
          :visible="modalVisible"
          @ok="handleModalOk"
          @cancel="handleModalCancel"
          :confirmLoading="confirmLoading"
          width="90%"
          :style="{ maxWidth: '1200px' }"
          :getContainer="false"
          destroyOnClose
        >
          <a-form
            :model="formState"
            :rules="rules"
            ref="formRef"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 18 }"
          >
            <a-form-item label="提交人" name="submitter">
              <a-select
                v-model:value="formState.submitter"
                placeholder="请选择提交人"
                :filter-option="false"
                show-search
                allow-clear
                :loading="submitterSearchLoading"
                @search="handleSubmitterSearch"
                :not-found-content="submitterSearchLoading ? undefined : '未找到匹配结果'"
                @change="handleSubmitterChange"
              >
                <a-select-option v-for="(option, index) in submitterOptions" :key="option.id || index" :value="option.nickname || option.username" :data="option">
                  {{ option.nickname || option.username }} ({{ option.studentNumber || '无工号' }})
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="提交人排位" name="submitterRanking">
              <a-input v-model:value="formState.submitterRanking" placeholder="请输入提交人排位" />
            </a-form-item>
            <a-form-item label="论文题目" name="title">
              <a-textarea 
                v-model:value="formState.title" 
                placeholder="请输入论文题目" 
                :auto-size="{ minRows: 2, maxRows: 4 }"
                :show-count="true"
              />
            </a-form-item>
            <a-form-item label="期刊名称" name="journal">
              <a-input v-model:value="formState.journal" placeholder="请输入期刊名称" />
            </a-form-item>
            <a-form-item label="出版时间" name="publishDate">
              <a-date-picker
                v-model:value="formState.publishDate"
                style="width: 100%"
                placeholder="请选择出版时间"
                format="YYYY-MM-DD"
              />
            </a-form-item>
            <a-form-item label="论文类型" name="type">
              <a-select v-model:value="formState.type" placeholder="请选择论文类型" style="width: 100%" @change="handlePaperTypeChange">
                <a-select-option v-for="item in paperTypeOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
                <a-form-item label="影响因子" name="impactFactor">
                  <a-input-number
                    v-model:value="formState.impactFactor"
                    :min="0"
                :step="0.001"
                    style="width: 100%"
                    placeholder="请输入影响因子"
                  />
            </a-form-item>
            <a-form-item label="本学院通讯作者人数" name="collegeCorrespondentAuthorNumber">
              <a-input-number
                v-model:value="formState.collegeCorrespondentAuthorNumber"
                :min="0"
                style="width: 100%"
                placeholder="请输入本学院通讯作者人数"
              />
            </a-form-item>
            <a-form-item label="通讯作者总人数" name="correspondentAuthorNumber">
              <a-input-number
                v-model:value="formState.correspondentAuthorNumber"
                :min="0"
                style="width: 100%"
                placeholder="请输入通讯作者总人数"
              />
            </a-form-item>
            <a-form-item label="分配比例基数" name="allocationProportionBase">
              <a-input v-model:value="formState.allocationProportionBase" placeholder="请输入分配比例基数" />
            </a-form-item>
            <a-form-item label="总分配比例" name="totalAllocationProportion">
              <a-input v-model:value="formState.totalAllocationProportion" placeholder="请输入总分配比例" />
            </a-form-item>
            
            <!-- 添加第一单位是否为我们大学和第一作者类型选项 -->
            <a-form-item label="第一单位归属" name="isFirstAffiliationOurs">
              <a-radio-group v-model:value="formState.isFirstAffiliationOurs">
                <a-radio :value="true">我们大学</a-radio>
                <a-radio :value="false">非我们大学</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <a-form-item label="第一作者类型" name="firstAuthorType">
              <a-radio-group v-model:value="formState.firstAuthorType">
                <a-radio :value="true">我院研究生</a-radio>
                <a-radio :value="false">非我院研究生</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <!-- 作者列表区域，不使用a-form-item包裹，避免多个表单元素验证问题 -->
            <div class="form-section">
              <div class="form-section-label">
                <label style="color: #000000d9; font-size: 14px;">作者列表</label>
              </div>
              <div class="form-section-content">
                <div class="authors-container">
                  <!-- 作者添加区域 -->
                  <a-row :gutter="16" style="margin-bottom: 16px;">
                    <a-col :span="8">
                      <a-select
                        v-model:value="currentAuthor.nickname"
                        placeholder="请选择论文作者"
                        :filter-option="false"
                        show-search
                        allow-clear
                        :loading="authorsSearchLoading"
                        @search="handleAuthorsSearch"
                        :not-found-content="authorsSearchLoading ? undefined : '未找到匹配结果'"
                        @change="handleCurrentAuthorChange"
                        :dropdownMatchSelectWidth="false"
                        style="width: 100%"
                      >
                        <a-select-option 
                          v-for="(option, index) in authorsOptions" 
                          :key="option.id || index" 
                          :value="option.nickname || option.username" 
                          :data="option"
                        >
                          {{ option.nickname || option.username }} ({{ option.studentNumber || '无工号' }})
                        </a-select-option>
                      </a-select>
                    </a-col>
                    <a-col :span="4">
                      <a-input-number 
                        v-model:value="currentAuthor.allocation" 
                        placeholder="分配比例" 
                        :min="0" 
                        :max="100" 
                        :precision="2"
                        style="width: 100%"
                        addonAfter="%"
                      />
                    </a-col>
                    <a-col :span="3">
                      <a-input-number 
                        v-model:value="currentAuthor.authorRank" 
                        placeholder="排名" 
                        :min="1" 
                        style="width: 100%"
                      />
                    </a-col>
                    <a-col :span="3">
                      <a-checkbox v-model:checked="currentAuthor.isFirstAuthor">第一作者</a-checkbox>
                    </a-col>
                    <a-col :span="3">
                      <a-checkbox v-model:checked="currentAuthor.isCorrespondingAuthor">通讯作者</a-checkbox>
                    </a-col>
                    <a-col :span="3">
                      <a-button type="primary" @click="handleAddAuthor">
                        <template #icon><PlusOutlined /></template>
                        添加
                      </a-button>
                    </a-col>
                  </a-row>
                  
                  <!-- 已添加作者列表 -->
                  <a-divider v-if="formState.authorsList && formState.authorsList.length > 0" style="margin: 8px 0">已添加作者</a-divider>
                  <div v-if="formState.authorsList && formState.authorsList.length > 0">
                    <p style="color: #666; font-size: 12px; margin-bottom: 8px;">已添加 {{ formState.authorsList.length }} 位作者</p>
                    <a-table
                      :columns="authorColumns"
                      :data-source="formState.authorsList" 
                      :pagination="false"
                      size="small"
                      bordered
                    >
                      <template #bodyCell="{ column, record, index }">
                        <template v-if="column.key === 'authorRank'">
                          <a-input-number
                            v-model:value="record.authorRank"
                            :min="1"
                            style="width: 80px"
                          />
                        </template>
                        <template v-if="column.key === 'allocation'">
                          <a-input-number
                            v-model:value="record.allocation"
                            :min="0"
                            :max="100"
                            :precision="2"
                            style="width: 90px"
                            addon-after="%"
                          />
                        </template>
                        <template v-if="column.key === 'role'">
                          <div class="author-roles">
                            <a-checkbox v-model:checked="record.isFirstAuthor">第一作者</a-checkbox>
                            <a-checkbox v-model:checked="record.isCorrespondingAuthor">通讯作者</a-checkbox>
                          </div>
                        </template>
                        <template v-if="column.key === 'action'">
                          <a-button 
                            type="link" 
                            danger 
                            size="small" 
                            @click="handleRemoveAuthor(index)"
                          >
                            删除
                          </a-button>
                        </template>
                      </template>
                    </a-table>
                  </div>
                  <div v-else style="color: #999; text-align: center; padding: 10px; border: 1px dashed #ddd; border-radius: 4px;">
                    还没有添加作者，请先选择作者并点击"添加"按钮
                  </div>
                </div>
              </div>
            </div>
            
            <a-form-item label="备注" name="remark">
              <a-textarea
                v-model:value="formState.remark"
                :rows="4"
                placeholder="请输入备注"
              />
            </a-form-item>
            <a-form-item label="引用次数" name="citations">
              <a-input-number
                v-model:value="formState.citations"
                :min="0"
                style="width: 100%"
                placeholder="请输入引用次数"
              />
            </a-form-item>
            <a-form-item label="计算分数" name="calculatedScore">
              <a-input-number
                v-model:value="formState.calculatedScore"
                :min="0"
                :step="0.1"
                style="width: 100%"
                placeholder="根据论文类型自动计算"
                disabled
              />
            </a-form-item>
            
            <!-- 添加附件上传 - 从表单项解耦，避免多控件验证问题 -->
            <div class="form-section">
              <div class="form-section-label" style="text-align: right; padding-right: 8px;">
                <label style="color: #000000d9; font-size: 14px;">上传附件</label>
              </div>
              <div class="form-section-content">
                <a-upload
                  v-model:file-list="fileList"
                  :customRequest="handleFileUpload"
                  :before-upload="beforeUploadFile"
                  multiple
                  :show-upload-list="false"
                >
                  <a-button type="primary">
                    <template #icon><UploadOutlined /></template>
                    选择文件
                  </a-button>
                </a-upload>
                
                <!-- 文件列表 -->
                <div v-if="fileList.length > 0" style="margin-top: 16px;">
                  <div style="margin-bottom: 8px; font-weight: 500; color: #333;">
                    已上传文件 ({{ fileList.length }}/5)
                  </div>
                  <div style="max-height: 200px; overflow-y: auto; border: 1px solid #f0f0f0; border-radius: 6px;">
                    <div
                      v-for="(file, index) in fileList"
                      :key="file.uid || index"
                      style="display: flex; align-items: center; padding: 8px 12px; border-bottom: 1px solid #f5f5f5; background: #fff;"
                      :style="{
                        'border-bottom': index === fileList.length - 1 ? 'none' : '1px solid #f5f5f5',
                        'background': file.status === 'error' ? '#fff2f0' : (file.status === 'uploading' ? '#f6ffed' : '#fff')
                      }"
                    >
                      <!-- 文件图标和信息 -->
                      <div style="display: flex; align-items: center; flex: 1; min-width: 0;">
                        <FileOutlined style="margin-right: 8px; color: #1890ff; font-size: 14px;" />
                        <div style="flex: 1; min-width: 0;">
                          <div
                            style="font-size: 13px; color: #333; margin-bottom: 2px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                            :title="file.originalFileName || file.name"
                          >
                            {{ file.originalFileName || file.name }}
                          </div>
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="font-size: 11px; color: #999;">
                              {{ formatFileSize(file.size || (file.data && file.data.size)) }}
                            </span>
                            <a-tag
                              :color="file.status === 'done' ? 'success' : (file.status === 'error' ? 'error' : 'processing')"
                              size="small"
                              style="margin: 0; font-size: 10px; padding: 0 4px; line-height: 16px;"
                            >
                              {{ file.status === 'done' ? '已上传' : (file.status === 'error' ? '上传失败' : '上传中') }}
                            </a-tag>
                          </div>
                        </div>
                      </div>

                      <!-- 操作按钮 -->
                      <div style="display: flex; align-items: center; gap: 4px; margin-left: 8px;" v-if="file.status === 'done'">
                        <a-button type="link" size="small" @click="previewFile(file)" style="padding: 2px 4px; height: auto; font-size: 11px;">
                          <template #icon><EyeOutlined style="font-size: 11px;" /></template>
                          预览
                        </a-button>
                        <a-button type="link" size="small" @click="downloadFile(file)" style="padding: 2px 4px; height: auto; font-size: 11px;">
                          <template #icon><DownloadOutlined style="font-size: 11px;" /></template>
                          下载
                        </a-button>
                        <a-popconfirm
                          title="确定要删除该文件吗？"
                          @confirm="confirmDeleteFile(file)"
                          okText="确认"
                          cancelText="取消"
                        >
                          <a-button type="link" danger size="small" style="padding: 2px 4px; height: auto; font-size: 11px;">
                            <template #icon><DeleteOutlined style="font-size: 11px;" /></template>
                            删除
                          </a-button>
                        </a-popconfirm>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div style="margin-top: 8px; color: #666; font-size: 12px;">
                  支持上传PDF、Word、Excel等文件，单个文件不超过10MB，最多上传5个文件
                </div>
              </div>
            </div>
          </a-form>
        </a-modal>
      </a-card>
  
        <!-- 用户详情模态框 -->
        <a-modal
          v-model:visible="userScoreDetailVisible"
          :title="`${userScoreDetailData?.nickname || '用户'}的论文得分详情`"
          width="1200px"
          :footer="null"
          :autofocus="false"
          :focusTriggerAfterClose="false"
        >
          <a-spin :spinning="!userScoreDetailData">
            <a-table
              :columns="[
                { title: '论文题目', dataIndex: 'title', key: 'title', ellipsis: false, width: 180 },
                { title: '期刊名称', dataIndex: 'journal', key: 'journal', width: 180 },
                { title: '发表日期', dataIndex: 'publishDate', key: 'publishDate', width: 100 },
                { title: '论文类型', dataIndex: 'type', key: 'type', width: 120 },
                { title: '作者数', dataIndex: 'authorCount', key: 'authorCount', width: 70 },
                { title: '分配比例', dataIndex: 'allocationRatio', key: 'allocationRatio', width: 70 },
                { title: '个人得分', dataIndex: 'userScore', key: 'userScore', width: 90 }
              ]"
              :data-source="userScoreDetailData?.papers"
              :pagination="{
                pageSize: 10,
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50'],
                showTotal: total => `共 ${total} 条`
              }"
              @change="handleUserDetailTableChange"
              :scroll="{ x: 800 }"
              :bordered="true"
              rowKey="id"
            >
              <template #bodyCell="{ column, record, text }">
                <template v-if="column.key === 'journal'">
                  <span style="word-break: break-all; white-space: pre-wrap; line-height: 1.4;">{{ text }}</span>
                </template>
                <template v-else-if="column.key === 'allocationRatio'">
                  {{ (record.allocationRatio * 100).toFixed(2) }}%
                </template>
                <template v-else-if="column.key === 'userScore'">
                  <span style="font-weight: bold; color: #1890ff;">{{ record.userScore ? parseFloat(record.userScore).toFixed(2) : '0.00' }}分</span>
                </template>
              </template>
            </a-table>
            <div style="margin-top: 16px; text-align: right; font-weight: bold;">
              总得分: {{ userScoreDetailData?.totalScore ? parseFloat(userScoreDetailData.totalScore).toFixed(2) : '0.00' }}分
            </div>
          </a-spin>
        </a-modal>
  
        <!-- 文件预览模态框 -->
        <a-modal
          :visible="previewVisible"
          :title="previewTitle"
          :footer="null"
          @cancel="previewVisible = false"
        >
          <img alt="预览图片" style="width: 100%" :src="previewImage" />
        </a-modal>
        
        <!-- 审核模态框 -->
        <a-modal
          title="论文审核"
          :visible="reviewModalVisible"
          @ok="handleReviewSubmit"
          @cancel="handleReviewCancel"
          :confirmLoading="reviewConfirmLoading"
          width="700px"
        >
          <div v-if="currentReviewPaper">
            <div class="paper-info-section">
              <h3>{{ currentReviewPaper.title }}</h3>
              <p><strong>期刊名称：</strong>{{ currentReviewPaper.journal }}</p>
              <p><strong>出版时间：</strong>{{ currentReviewPaper.publishDate ? dayjs(currentReviewPaper.publishDate).format('YYYY-MM-DD') : '-' }}</p>
              <p><strong>论文类型：</strong>{{ currentReviewPaper.type }}</p>
              <p><strong>影响因子：</strong>{{ currentReviewPaper.impactFactor || 0 }}</p>
              <p><strong>作者：</strong>{{ currentReviewPaper.authors }}</p>
            </div>
            
            <a-divider />
            
            <a-form :model="reviewForm" layout="vertical">
              <a-form-item label="审核意见" name="reviewComment">
                <a-textarea 
                  v-model:value="reviewForm.reviewComment" 
                  :rows="4" 
                  placeholder="请输入审核意见（选填）" 
                />
              </a-form-item>
              
              <a-form-item label="审核结果" name="reviewStatus">
                <a-radio-group v-model:value="reviewForm.reviewStatus">
                  <a-radio :value="1">通过</a-radio>
                  <a-radio :value="0">拒绝</a-radio>
                </a-radio-group>
              </a-form-item>
              
              <!-- 显示附件列表 -->
              <div v-if="currentReviewPaper.attachments && currentReviewPaper.attachments.length > 0">
                <h4>附件列表</h4>
                <a-list size="small">
                  <a-list-item v-for="(file, index) in currentReviewPaper.attachments" :key="index">
                    <template #actions>
                      <a-button type="link" size="small" @click="previewFile(file)">
                        <template #icon><EyeOutlined /></template>
                        预览
                      </a-button>
                      <a-button type="link" size="small" @click="downloadFile(file)">
                        <template #icon><DownloadOutlined /></template>
                        下载
                      </a-button>
                    </template>
                    <a-list-item-meta
                      :title="file.name || file.originalFileName || `附件${index + 1}`"
                      :description="formatFileSize(file.size || (file.data && file.data.size)) || '未知大小'"
                    >
                      <template #avatar>
                        <file-outlined />
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </a-list>
              </div>
              <div v-else>
                <a-empty description="暂无附件" />
              </div>
            </a-form>
          </div>
          <a-spin v-else />
        </a-modal>
  
        <!-- 论文导入结果模态框 -->
        <a-modal
          title="论文导入结果"
          :visible="importResultVisible"
          :width="800"
          :footer="null"
          :maskClosable="false"
          :closable="!importInProgress"
        >
          <div style="margin-bottom: 16px;">
            <a-progress 
              :percent="importResults.total > 0 ? Math.floor((importResults.current / importResults.total) * 100) : 0" 
              :status="importInProgress ? 'active' : (importResults.failed > 0 ? 'exception' : 'success')" 
            />
            <div style="margin-top: 16px; display: flex; justify-content: space-between;">
              <span>总记录数: <b>{{ importResults.total }}</b></span>
              <span>已处理: <b>{{ importResults.current }}</b></span>
              <span>成功: <b style="color: #52c41a">{{ importResults.success }}</b></span>
              <span>失败: <b style="color: #ff4d4f">{{ importResults.failed }}</b></span>
            </div>
          </div>
          
          <a-table
            :dataSource="importResults.details"
            :columns="resultColumns"
            rowKey="index"
            :pagination="{ pageSize: 10 }"
            :rowClassName="(record) => record.status === 'error' ? 'import-row-error' : ''"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'status'">
                <a-tag v-if="text === 'success'" color="success">成功</a-tag>
                <a-tag v-else color="error">失败</a-tag>
              </template>
            </template>
          </a-table>
          
          <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
            <a-space>
              <a-button 
                @click="exportFailedRecords" 
                :disabled="importInProgress || importResults.failed === 0"
                type="danger"
              >
                <template #icon><DownloadOutlined /></template>
                导出失败记录
              </a-button>
              <a-button 
                type="primary" 
                @click="importResultVisible = false"
                :disabled="importInProgress"
              >
                完成
              </a-button>
            </a-space>
          </div>
        </a-modal>

        <!-- 论文导入预览模态框 -->
        <a-modal
          title="论文导入预览"
          :visible="importPreviewVisible"
          width="90%"
          :maskClosable="false"
          :footer="null"
          @cancel="handleCancelImportPreview"
        >
          <template v-if="importPreviewLoading">
            <div style="text-align: center; padding: 40px;">
              <a-spin size="large" />
              <p style="margin-top: 20px;">正在解析数据，请稍候...</p>
            </div>
          </template>
          <template v-else>
            <div style="margin-bottom: 16px;">
              <a-alert
                :type="userIdCheckResults.notFound > 0 ? 'warning' : 'success'"
                :message="userIdCheckResults.notFound > 0 ? 
                  `存在${userIdCheckResults.notFound}个用户ID未找到，这些记录可能导入失败` : 
                  '所有用户ID均已找到'"
                show-icon
              />
              <div style="margin-top: 8px;">
                <a-space>
                  <span>共找到 <b>{{ importPreviewData.length }}</b> 条记录</span>
                  <a-button type="primary" @click="handleStartImport" :loading="importInProgress">
                    开始导入
                  </a-button>
                  <a-button @click="handleCancelImportPreview">
                    取消
                  </a-button>
                  <a-button type="primary" @click="handleDownloadJson">
                    <template #icon><DownloadOutlined /></template>
                    下载JSON
                  </a-button>
                </a-space>
              </div>
            </div>
            
            <a-table
              :columns="previewColumns"
              :dataSource="importPreviewData.map((item, index) => ({ 
                ...item, 
                index: index + 1,
                key: index
              }))"
              :pagination="{ pageSize: 10 }"
              :rowClassName="(record) => record.userIdCheckStatus === 'notFound' ? 'import-row-error' : ''"
              :scroll="{ x: 1200 }"
              size="small"
              bordered
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'userIdCheckStatus'">
                  <a-tag v-if="text === 'checking'" color="blue">检查中</a-tag>
                  <a-tag v-else-if="text === 'found'" color="green">已找到</a-tag>
                  <a-tag v-else color="red">未找到</a-tag>
                </template>
                <template v-else-if="column.dataIndex === 'authors'">
                  <div>
                    <div v-if="record.rawData?.participants && record.rawData.participants.length">
                      <div v-for="(participant, pIndex) in record.rawData.participants" :key="pIndex">
                        {{ participant.name || '未命名' }}
                        <a-tag v-if="participant.found !== undefined" :color="participant.found ? 'green' : 'red'">
                          {{ participant.found ? '已找到' : '未找到' }}
                        </a-tag>
                        <span style="color: #999; margin-left: 5px;">
                          (排名:{{ participant.authorRank || pIndex+1 }}
                          {{ participant.isFirstAuthor ? '/第一作者' : '' }}
                          {{ participant.isCorrespondingAuthor ? '/通讯作者' : '' }}
                          {{ participant.allocationRatio ? `/分配比例:${participant.allocationRatio}` : '' }})
                        </span>
                      </div>
                    </div>
                    <span v-else>{{ text || '-' }}</span>
                  </div>
                </template>
              </template>
            </a-table>
          </template>
        </a-modal>
    </div>
  </template>
  
  <script setup>
  // 添加组件加载日志
  console.log('高水平论文组件开始加载...');
  
  // 阻止第三方天气API的请求
  if (window.XMLHttpRequest) {
    const originalSend = XMLHttpRequest.prototype.send;
    XMLHttpRequest.prototype.send = function(...args) {
      if (this._url && (this._url.includes('***********') || 
          this._url.includes('*************') || 
          this._url.includes('tianqi.com'))) {
        // 阻止向这些IP地址和tianqi.com发送请求
        console.log('已阻止请求到:', this._url);
        return;
      }
      return originalSend.apply(this, args);
    };
    
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      this._url = url;
      return originalOpen.apply(this, [method, url, ...args]);
    };
  }
  
  // 导入文件处理工具函数
  import { previewFile as utilPreviewFile, downloadFile as utilDownloadFile, deleteFile as utilDeleteFile } from '@/utils/others';
  import { excelToHighLevelPapersJson, downloadJson } from '@/utils/fileUtils'; // 导入新增的Excel处理函数
  import { ref, reactive, computed, onMounted, onBeforeUnmount, watch, onErrorCaptured, nextTick, h } from 'vue';
  import {
    getHighLevelPapers,
    getPersonalHighLevelPapers,
    getAllUsersTotalScore,
    getUserTotalScore,
    getPaperTypeDistribution,
    getPaperYearlyDistribution,
    getPaperImpactFactorDistribution,
    getPaperStats,
    deleteHighLevelPaper,
    addHighLevelPaper,
    getReviewStatusOverview
  } from '@/api/modules/api.high-level-papers';
  import { uploadFiles, deleteFile as apiDeleteFile } from '@/api/modules/api.file'
  import { getAllHighLevelPapersRules } from '@/api/rules/highLevelPapersRules';
  // 导入ECharts
  import * as echarts from 'echarts';
  import { 
    message, 
    Modal
  } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { useRouter } from 'vue-router';
  import { useUserRole } from '../../../composables/useUserRole';
  const { getUserRole } = useUserRole();
  import { hasPerms } from '@/libs/util.common';

  import useUserId from '@/composables/useUserId'
  const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()

  // 导入图标组件
  import {
    PlusOutlined,
    DownloadOutlined,
    SearchOutlined,
    ReloadOutlined,
    UserOutlined,
    TrophyOutlined,
    FileTextOutlined,
    CrownOutlined,
    RiseOutlined,
    AppstoreOutlined,
    UploadOutlined,
    EyeOutlined,
    DeleteOutlined,
    FileOutlined,
    FileExcelOutlined,
    EditOutlined,
    DownOutlined,
    AuditOutlined
  } from '@ant-design/icons-vue';
  import { usersSearch } from '@/api/modules/api.users'; // 导入用户搜索API
  import { getScoreTimeRange } from '@/api/modules/api.home';
  console.log('导入完成，初始化路由...');
  const router = useRouter();
  // 判断是否为开发环境
  const isDev = process.env.NODE_ENV === 'development';
  
  // 错误信息展示
  const errorMessage = ref('');
  
  // 添加日志函数
  const addLog = (type, message, data = null) => {
    console.log(`[${type}] ${message}`, data);
    
    // 错误类型时更新错误信息
    if (type === 'error') {
      errorMessage.value = `${message}: ${data ? JSON.stringify(data) : '未知错误'}`;
    }
  };
  
  // 日志工具 - 兼容旧代码
  const logInfo = (msg, data) => {
    addLog('info', msg, data);
  };
  
  const logError = (msg, error) => {
    addLog('error', msg, error);
    
    // 在控制台中展开错误堆栈
    if (error && error.stack) {
      console.error(`[高水平论文] 错误堆栈:`, error.stack);
    }
    
    // 设置错误信息
    errorMessage.value = `${msg}: ${error ? (error.message || JSON.stringify(error)) : '未知错误'}`;
  };
  
  const logDebug = (msg, data) => {
    if (isDev) {
      addLog('debug', msg, data);
    }
  };
  
  // 全局错误处理
  window.addEventListener('error', (event) => {
    logError('页面发生全局错误', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    });
  });
  
  // 添加handleUnhandledRejection函数的定义
  const handleUnhandledRejection = (event) => {
    console.error('未处理的Promise异常:', event.reason);
    logError('未处理的Promise异常', event.reason);
  };
  
  window.addEventListener('unhandledrejection', (event) => {
    logError('未处理的Promise异常', event.reason);
  });
  
  // 图表范围控制变量定义
  const typeChartRange = ref('in');
  const yearChartRange = ref('in');
  const impactFactorChartRange = ref('in');
  const reviewStatusChartRange = ref('in');
  
  // 添加图表审核状态控制变量
  const typeChartReviewStatus = ref('reviewed');
  const yearChartReviewStatus = ref('reviewed');
  const impactFactorChartReviewStatus = ref('reviewed');
  
  // 统计年份选择
  const overallStatsYear = ref(new Date().getFullYear());
  const availableYears = ref([
    new Date().getFullYear(),
    new Date().getFullYear() - 1,
    new Date().getFullYear() - 2,
    new Date().getFullYear() - 3,
    new Date().getFullYear() - 4
  ]);
  
  // 初始化统计对象，避免未定义错误
  const overallStats = ref({
    paperCount: 0,
    totalScore: 0,
    typeDistribution: {}
  });
  
  const personalStats = ref({
    paperCount: 0,
    totalScore: 0,
    rank: '--',
    totalUsers: '--',
    typeDistribution: {},
    yearlyDistribution: {}
  });
  
  // 图表引用
  const typeChartRef = ref(null);
  const yearChartRef = ref(null);
  const impactFactorChartRef = ref(null);
  const reviewStatusChartRef = ref(null);
  const paperTypeChartRef = ref(null);
  const publishTimeChartRef = ref(null);
  const personalScoreChartRef = ref(null);
  const scoreProgressChartRef = ref(null);
  
  // 用户得分表格列定义
  const userScoreColumns = [
    {
      title: '排名',
      key: 'rank',
      dataIndex: 'rank',
      width: 80,
      align: 'center'
    },
    {
      title: '用户名',
      dataIndex: 'nickname',
      key: 'nickname',
      width: 150
    },
    {
      title: '工号',
      dataIndex: 'studentNumber',
      key: 'studentNumber',
      width: 120
    },
    {
      title: '论文数量',
      dataIndex: 'paperCount',
      key: 'paperCount',
      width: 100,
      align: 'center'
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: 120,
      align: 'center',
      sorter: true
    },
    {
      title: '操作',
      key: 'details',
      width: 100,
      align: 'center'
    }
  ];
  
  // 表单验证规则
  const rules = {
    title: [{ required: true, message: '请输入论文题目' }],
    journal: [{ required: true, message: '请输入期刊名称' }],
    publishDate: [{ required: true, message: '请选择出版时间' }],
    type: [{ required: true, message: '请选择论文类型' }]
  };
  
  // 添加在其他状态变量附近
  const currentRole = ref('');
  const currentUserId = ref('');

  // charts初始化状态
  const chartsInitialized = ref(false);

  // 数据源和加载状态
  const dataSource = ref([]);
  const loading = ref(false);
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50'],
    showTotal: (total) => `共 ${total} 条`,
  });
  
  // 是否显示个人论文
  const showPersonalPapers = ref(false);
  
  // 表单状态
  const searchForm = reactive({
    title: '',
    authors: '',
    type: undefined,
    journal: '',
    publishStartDate: undefined,
    publishEndDate: undefined,
    reviewStatus: 'reviewed', // 将审核状态加入搜索表单
    range: 'in' // 将统计范围加入搜索表单
  });
  
  // 模态框状态
  const modalVisible = ref(false);
  const modalTitle = ref('新增论文');
  const confirmLoading = ref(false);
  const formRef = ref(null);
  const isEdit = ref(false);
  
  // 表单状态对象
  const formState = reactive({
    authorsList: [],
    fileList: [],
    fileIds: [], // 添加文件ID数组
    attachmentUrl: [], // 添加文件路径数组
    deletedFiles: [] // 添加已删除文件ID数组
  });
  
  // 作者相关
  const authorsOptions = ref([]);
  const authorsSearchLoading = ref(false);
  const currentAuthor = reactive({
    id: '',
    nickname: '',
    username: '',
    studentNumber: '',
    allocation: 0,
    authorRank: 1,
    isFirstAuthor: false,
    isCorrespondingAuthor: false
  });
  
  // 提交人搜索相关
  const submitterOptions = ref([]);
  const submitterSearchLoading = ref(false);
  
  // 用户得分数据
  const userScoreData = ref([]);
  const userScoreLoading = ref(false);
  const userScoreSearchParams = reactive({
    nickname: '',
    paperId: ''
  });
  const userScorePagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50'],
    showTotal: total => `共 ${total} 条`
  });
  const userScoreChartRange = ref('in');
  const exporting = ref(false); // 添加导出状态变量
  
  // 查看用户得分详情
  const userScoreDetailVisible = ref(false);
  const userScoreDetailData = ref(null);
  const userScoreDetailLoading = ref(false);
  
  // 请求锁和请求ID管理
  const requestLocks = reactive({
    papersList: false,
    userTotalScore: false
  });
  
  const requestIds = reactive({
    papersList: null,
    userTotalScore: null
  });
  
  // 表格列配置
  const columns = computed(() => [
    {
      title: '论文题目',
      dataIndex: 'title',
      key: 'title',
      width: 180,
      ellipsis: true,
    },
    {
      title: '作者',
      dataIndex: 'authors',
      key: 'authors',
      width: 120,
      ellipsis: true,
    },
    {
      title: '期刊',
      dataIndex: 'journal',
      key: 'journal',
      width: 140,
      ellipsis: true,
    },
    {
      title: '出版时间',
      dataIndex: 'publishDate',
      key: 'publishDate',
      width: 90,
      sorter: true,
      customRender: ({ text }) => {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      }
    },
    {
      title: '影响因子',
      dataIndex: 'impactFactor',
      key: 'impactFactor',
      sorter: true,
      width: 80,
    },
    {
      title: '计算分数',
      dataIndex: 'calculatedScore',
      key: 'calculatedScore',
      sorter: true,
      width: 90,
    },
    {
      title: '审核状态',
      dataIndex: 'ifReviewer',
      key: 'ifReviewer',
      width: 80,
    },
    {
      title: '审核建议',
      dataIndex: 'reviewComment',
      key: 'reviewComment',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      align: 'center'
    },
  ]);
  
  // 平均分计算
  const averageScore = computed(() => {
    if (!overallStats.value.paperCount || overallStats.value.paperCount === 0) return 0;
    return (overallStats.value.totalScore / overallStats.value.paperCount).toFixed(2);
  });
  
  // 总分计算
  const totalScore = computed(() => {
    return dataSource.value.reduce((sum, item) => {
      const score = parseFloat(item.calculatedScore || 0);
      return sum + (isNaN(score) ? 0 : score);
    }, 0).toFixed(2);
  });
  
  // 获取排名颜色
  const getRankColor = (rank) => {
    if (rank === 1) return 'gold';
    if (rank === 2) return 'silver';
    if (rank === 3) return '#cd7f32'; // bronze
    return 'blue';
  };
  
  // 图表实例对象
  const typeChart = ref(null);
  const yearChart = ref(null);
  const impactFactorChart = ref(null);
  const reviewStatusChart = ref(null);
  
  // 图表初始化函数
  const initCharts = () => {
    try {
      console.log('初始化图表');
      chartsInitialized.value = true;
      
      // 使用nextTick确保DOM已经渲染完成
      nextTick(() => {
        console.log('DOM已更新，准备初始化图表实例');
        console.log('typeChartRef存在:', !!typeChartRef.value);
        console.log('yearChartRef存在:', !!yearChartRef.value);
        console.log('impactFactorChartRef存在:', !!impactFactorChartRef.value);
        
        // 确保图表DOM元素存在且尺寸合适
        setTimeout(() => {
          try {
            // 初始化类型分布图表
            if (typeChartRef.value && !typeChart.value) {
              const el = document.getElementById('typeChartContainer');
              console.log('类型图表DOM元素:', el);
              console.log('尺寸:', el ? `${el.clientWidth}x${el.clientHeight}` : '未知');
              
              if (typeChartRef.value.clientWidth === 0) {
                console.warn('类型图表容器宽度为0，设置默认宽度');
                typeChartRef.value.style.width = '100%';
              }
              
              try {
                console.log('初始化类型分布图表');
                typeChart.value = echarts.init(typeChartRef.value);
                loadPaperTypeDistribution(typeChartRange.value, typeChartReviewStatus.value);
              } catch (error) {
                console.error('初始化类型分布图表失败:', error);
                // 尝试延迟重新初始化
                setTimeout(() => {
                  try {
                    typeChart.value = echarts.init(typeChartRef.value);
                    loadPaperTypeDistribution(typeChartRange.value, typeChartReviewStatus.value);
                  } catch (retryError) {
                    console.error('重试初始化类型分布图表失败:', retryError);
                  }
                }, 500);
              }
            }
            
            // 初始化年度分布图表
            if (yearChartRef.value && !yearChart.value) {
              const el = document.getElementById('yearChartContainer');
              console.log('年度图表DOM元素:', el);
              console.log('尺寸:', el ? `${el.clientWidth}x${el.clientHeight}` : '未知');
              
              if (yearChartRef.value.clientWidth === 0) {
                console.warn('年度图表容器宽度为0，设置默认宽度');
                yearChartRef.value.style.width = '100%';
              }
              
              try {
                console.log('初始化年度分布图表');
                yearChart.value = echarts.init(yearChartRef.value);
                loadPaperYearlyDistribution(yearChartRange.value, yearChartReviewStatus.value);
              } catch (error) {
                console.error('初始化年度分布图表失败:', error);
                // 尝试延迟重新初始化
                setTimeout(() => {
                  try {
                    yearChart.value = echarts.init(yearChartRef.value);
                    loadPaperYearlyDistribution(yearChartRange.value, yearChartReviewStatus.value);
                  } catch (retryError) {
                    console.error('重试初始化年度分布图表失败:', retryError);
                  }
                }, 500);
              }
            }
            
            // 初始化影响因子分布图表
            if (impactFactorChartRef.value && !impactFactorChart.value) {
              const el = document.getElementById('impactFactorChartContainer');
              console.log('影响因子图表DOM元素:', el);
              console.log('尺寸:', el ? `${el.clientWidth}x${el.clientHeight}` : '未知');

              if (impactFactorChartRef.value.clientWidth === 0) {
                console.warn('影响因子图表容器宽度为0，设置默认宽度');
                impactFactorChartRef.value.style.width = '100%';
              }

              try {
                console.log('初始化影响因子分布图表');
                impactFactorChart.value = echarts.init(impactFactorChartRef.value);
                loadPaperImpactFactorDistribution(impactFactorChartRange.value, impactFactorChartReviewStatus.value);
              } catch (error) {
                console.error('初始化影响因子分布图表失败:', error);
                // 尝试延迟重新初始化
                setTimeout(() => {
                  try {
                    impactFactorChart.value = echarts.init(impactFactorChartRef.value);
                    loadPaperImpactFactorDistribution(impactFactorChartRange.value, impactFactorChartReviewStatus.value);
                  } catch (retryError) {
                    console.error('重试初始化影响因子分布图表失败:', retryError);
                  }
                }, 500);
              }
            }

            // 初始化审核状态分布图表
            if (reviewStatusChartRef.value && !reviewStatusChart.value) {
              const el = document.getElementById('reviewStatusChartContainer');
              console.log('审核状态图表DOM元素:', el);
              console.log('尺寸:', el ? `${el.clientWidth}x${el.clientHeight}` : '未知');

              if (reviewStatusChartRef.value.clientWidth === 0) {
                console.warn('审核状态图表容器宽度为0，设置默认宽度');
                reviewStatusChartRef.value.style.width = '100%';
              }

              try {
                console.log('初始化审核状态分布图表');
                reviewStatusChart.value = echarts.init(reviewStatusChartRef.value);
                loadReviewStatusOverview(reviewStatusChartRange.value);
              } catch (error) {
                console.error('初始化审核状态分布图表失败:', error);
                // 尝试延迟重新初始化
                setTimeout(() => {
                  try {
                    reviewStatusChart.value = echarts.init(reviewStatusChartRef.value);
                    loadReviewStatusOverview(reviewStatusChartRange.value);
                  } catch (retryError) {
                    console.error('重试初始化审核状态分布图表失败:', retryError);
                  }
                }, 500);
              }
            }
          } catch (error) {
            console.error('图表初始化过程中出错:', error);
          }
        }, 200); // 延迟200ms确保DOM完全渲染
        
        // 添加窗口大小变化时重绘图表
        window.addEventListener('resize', handleResize);
      });
    } catch (error) {
      console.error('图表初始化失败:', error);
    }
  };
  
  // 处理窗口大小变化
  const handleResize = () => {
    try {
      // 使用防抖处理，避免频繁调用
      if (handleResize.timer) {
        clearTimeout(handleResize.timer);
      }

      handleResize.timer = setTimeout(() => {
        try {
          // 重绘所有图表，添加更严格的检查
          if (typeChart.value && typeof typeChart.value.resize === 'function' && !typeChart.value.isDisposed()) {
            try {
              // 检查图表是否有有效的配置
              const option = typeChart.value.getOption();
              if (option && option.series && Array.isArray(option.series) && option.series.length > 0) {
                typeChart.value.resize();
              }
            } catch (error) {
              console.error('类型图表resize失败:', error);
              // 如果resize失败，尝试重新初始化图表
              setTimeout(() => {
                try {
                  if (typeChartRef.value && !typeChart.value.isDisposed()) {
                    loadPaperTypeDistribution(typeChartRange.value, typeChartReviewStatus.value);
                  }
                } catch (retryError) {
                  console.error('类型图表重新初始化失败:', retryError);
                }
              }, 200);
            }
          }

          if (yearChart.value && typeof yearChart.value.resize === 'function' && !yearChart.value.isDisposed()) {
            try {
              // 检查图表是否有有效的配置
              const option = yearChart.value.getOption();
              if (option && option.series && Array.isArray(option.series) && option.series.length > 0) {
                yearChart.value.resize();
              }
            } catch (error) {
              console.error('年度图表resize失败:', error);
              // 如果resize失败，尝试重新初始化图表
              setTimeout(() => {
                try {
                  if (yearChartRef.value && !yearChart.value.isDisposed()) {
                    loadPaperYearlyDistribution(yearChartRange.value, yearChartReviewStatus.value);
                  }
                } catch (retryError) {
                  console.error('年度图表重新初始化失败:', retryError);
                }
              }, 200);
            }
          }

          if (impactFactorChart.value && typeof impactFactorChart.value.resize === 'function' && !impactFactorChart.value.isDisposed()) {
            try {
              // 检查图表是否有有效的配置
              const option = impactFactorChart.value.getOption();
              if (option && option.series && Array.isArray(option.series) && option.series.length > 0) {
                impactFactorChart.value.resize();
              }
            } catch (error) {
              console.error('影响因子图表resize失败:', error);
              // 如果resize失败，尝试重新初始化图表
              setTimeout(() => {
                try {
                  if (impactFactorChartRef.value && !impactFactorChart.value.isDisposed()) {
                    loadPaperImpactFactorDistribution(impactFactorChartRange.value, impactFactorChartReviewStatus.value);
                  }
                } catch (retryError) {
                  console.error('影响因子图表重新初始化失败:', retryError);
                }
              }, 200);
            }
          }

          if (reviewStatusChart.value && typeof reviewStatusChart.value.resize === 'function' && !reviewStatusChart.value.isDisposed()) {
            try {
              // 检查图表是否有有效的配置
              const option = reviewStatusChart.value.getOption();
              if (option && option.series && Array.isArray(option.series) && option.series.length > 0) {
                reviewStatusChart.value.resize();
              }
            } catch (error) {
              console.error('审核状态图表resize失败:', error);
              // 如果resize失败，尝试重新初始化图表
              setTimeout(() => {
                try {
                  if (reviewStatusChartRef.value && !reviewStatusChart.value.isDisposed()) {
                    loadReviewStatusOverview(reviewStatusChartRange.value);
                  }
                } catch (retryError) {
                  console.error('审核状态图表重新初始化失败:', retryError);
                }
              }, 200);
            }
          }
        } catch (error) {
          console.error('图表resize过程中出错:', error);
        }
      }, 150); // 增加防抖时间到150ms
    } catch (error) {
      console.error('handleResize函数执行失败:', error);
    }
  };
  
  // 组件卸载时移除事件监听
  onBeforeUnmount(() => {
    try {
      // 清理resize定时器
      if (handleResize.timer) {
        clearTimeout(handleResize.timer);
        handleResize.timer = null;
      }

      // 移除事件监听
      window.removeEventListener('resize', handleResize);

      // 销毁图表实例
      try {
        if (typeChart.value && typeof typeChart.value.dispose === 'function') {
          typeChart.value.dispose();
          typeChart.value = null;
        }
      } catch (error) {
        console.error('销毁类型图表失败:', error);
      }

      try {
        if (yearChart.value && typeof yearChart.value.dispose === 'function') {
          yearChart.value.dispose();
          yearChart.value = null;
        }
      } catch (error) {
        console.error('销毁年度图表失败:', error);
      }

      try {
        if (impactFactorChart.value && typeof impactFactorChart.value.dispose === 'function') {
          impactFactorChart.value.dispose();
          impactFactorChart.value = null;
        }
      } catch (error) {
        console.error('销毁影响因子图表失败:', error);
      }

      try {
        if (reviewStatusChart.value && typeof reviewStatusChart.value.dispose === 'function') {
          reviewStatusChart.value.dispose();
          reviewStatusChart.value = null;
        }
      } catch (error) {
        console.error('销毁审核状态图表失败:', error);
      }
    } catch (error) {
      console.error('组件卸载清理失败:', error);
    }
  });
  
  // 切换图表范围的方法
  const changeChartRange = (chartType, range) => {
    console.log(`切换${chartType}图表范围到: ${range}`);

    if (chartType === 'type') {
        typeChartRange.value = range;
        loadPaperTypeDistribution(range, typeChartReviewStatus.value);
    } else if (chartType === 'year') {
        yearChartRange.value = range;
        loadPaperYearlyDistribution(range, yearChartReviewStatus.value);
    } else if (chartType === 'impactFactor') {
        impactFactorChartRange.value = range;
        loadPaperImpactFactorDistribution(range, impactFactorChartReviewStatus.value);
    } else if (chartType === 'reviewStatus') {
      reviewStatusChartRange.value = range;
      loadReviewStatusOverview(range);
    }
  };
  
  // 添加切换图表审核状态的方法
  const changeChartReviewStatus = (chartType, status) => {
    console.log(`切换${chartType}图表审核状态到: ${status}`);
    
    if (chartType === 'type') {
        typeChartReviewStatus.value = status;
        loadPaperTypeDistribution(typeChartRange.value, status);
    } else if (chartType === 'year') {
        yearChartReviewStatus.value = status;
        loadPaperYearlyDistribution(yearChartRange.value, status);
    } else if (chartType === 'impactFactor') {
        impactFactorChartReviewStatus.value = status;
        loadPaperImpactFactorDistribution(impactFactorChartRange.value, status);
    }
  };
  
  // 加载整体统计数据
  const loadOverallStats = () => {
    console.log('加载整体统计数据');
    
      const params = {
        year: overallStatsYear.value,
      userOnly: showPersonalPapers.value ? 'true' : 'false'
      };
      
    // 如果是个人视图，添加用户ID
      if (showPersonalPapers.value) {
      params.userId = userId.value;
    }
    
    getPaperStats(params)
      .then(response => {
      if (response?.code === 200) {
      if (showPersonalPapers.value) {
            // 更新个人统计
            personalStats.value = response.data || {
    paperCount: 0,
              totalScore: 0,
              rank: '--',
              totalUsers: '--',
    typeDistribution: {},
    yearlyDistribution: {}
            };
      } else {
            // 更新整体统计
            overallStats.value = response.data || {
    paperCount: 0,
    totalScore: 0,
              typeDistribution: {}
            };
          }
    } else {
          console.error('获取统计数据失败:', response);
        }
      })
      .catch(error => {
        console.error('获取统计数据失败:', error);
      });
    
    return Promise.resolve();
  };
  
  // 加载论文类型分布数据
  const loadPaperTypeDistribution = (range = 'in', reviewStatus = 'all') => {
    console.log(`加载论文类型分布，范围: ${range}，审核状态: ${reviewStatus}`);
    
    // 确保图表实例存在
    if (!typeChartRef.value) {
      console.error('类型图表引用不存在');
      return Promise.resolve();
    }
    
    // 如果图表实例不存在，尝试初始化
    if (!typeChart.value && typeChartRef.value) {
      try {
        console.log('尝试初始化类型图表');
        typeChart.value = echarts.init(typeChartRef.value);
      } catch (error) {
        console.error('类型图表初始化失败:', error);
        return Promise.resolve();
      }
    }
    
    // 如果图表实例仍然不存在，退出
    if (!typeChart.value) {
      console.error('无法创建类型图表实例');
      return Promise.resolve();
    }

    typeChart.value.showLoading();
    
    return getPaperTypeDistribution({ 
      range,
      reviewStatus,
      userId: showPersonalPapers.value ? userId.value : undefined
    })
    .then(response => {
      if (!typeChart.value) {
        console.error('typeChart实例已不存在，可能已被销毁');
        return;
      }
      
      typeChart.value.hideLoading();
      
      if (response?.code === 200) {
        console.log('获取论文类型分布成功');
        
        const data = response.data || [];
        const chartData = data
          .filter(item => item && typeof item === 'object' && item.name && typeof item.value !== 'undefined')
          .map(item => ({
            name: String(item.name),
            value: Number(item.value) || 0
          }))
          .filter(item => item.value > 0); // 过滤掉值为0的数据
        
        console.log('论文类型分布数据:', chartData);
        
        if (chartData.length === 0) {
          console.warn('没有非零数据可显示');
          // 添加一个默认数据，以便显示"暂无数据"
          chartData.push({
            name: '暂无数据',
            value: 0
          });
        }
        
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            left: 10,
            bottom: 10,
            data: chartData.map(item => item.name)
          },
          series: [
            {
              name: '论文类型',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 16,
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: chartData.map(item => ({
                name: item.name,
                value: item.value,
                itemStyle: item.itemStyle || {}
              }))
            }
          ]
        };
        
        try {
          console.log('设置类型图表选项');
          typeChart.value.setOption(option, true);
          console.log('类型图表选项设置成功');
        } catch (error) {
          console.error('设置类型图表选项失败:', error);
        }
      } else {
        console.error('获取论文类型分布失败:', response);
        message.error('获取论文类型分布失败');
      }
    })
    .catch(error => {
      if (typeChart.value) {
        typeChart.value.hideLoading();
      }
      console.error('获取论文类型分布出错:', error);
      message.error('获取论文类型分布失败: ' + (error.message || '未知错误'));
    });
  };
  
  const loadPaperYearlyDistribution = (range = 'in', reviewStatus = 'all') => {
    console.log(`加载论文年度分布，范围: ${range}，审核状态: ${reviewStatus}`);
    
    // 确保图表实例存在
    if (!yearChartRef.value) {
      return Promise.resolve();
    }
    
    // 如果图表实例不存在，尝试初始化
    if (!yearChart.value && yearChartRef.value) {
      try {
        yearChart.value = echarts.init(yearChartRef.value);
      } catch (error) {
        console.error('年度图表初始化失败:', error);
        return Promise.resolve();
      }
    }
    
    // 如果图表实例仍然不存在，退出
    if (!yearChart.value) {
      return Promise.resolve();
    }
    
    yearChart.value.showLoading();
    
    return getPaperYearlyDistribution({ 
      range,
      reviewStatus,
      userId: showPersonalPapers.value ? userId.value : undefined
    })
    .then(response => {
      yearChart.value.hideLoading();
      
      if (response?.code === 200) {
        console.log('获取论文年度分布成功');
        
        const data = response.data || {};

        // 安全处理数据
        const months = Array.isArray(data.months) ? data.months : [];
        const chartData = Array.isArray(data.data) ? data.data.map(item => Number(item) || 0) : [];

        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: months,
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '论文数量',
              minInterval: 1 // 确保Y轴刻度是整数
            }
          ],
          series: [
            {
              name: '论文数量',
              type: 'bar',
              barWidth: '60%',
              data: chartData,
              itemStyle: {
                color: '#1890ff'
              },
              emphasis: {
                itemStyle: {
                  color: '#40a9ff'
                }
              }
            }
          ]
        };
        
        yearChart.value.setOption(option);
      } else {
        console.error('获取论文年度分布失败:', response);
        message.error('获取论文年度分布失败');
      }
    })
    .catch(error => {
      yearChart.value.hideLoading();
      console.error('获取论文年度分布出错:', error);
      message.error('获取论文年度分布失败: ' + (error.message || '未知错误'));
    });
  };
  
  const loadPaperImpactFactorDistribution = (range = 'in', reviewStatus = 'all') => {
    console.log(`加载影响因子分布，范围: ${range}，审核状态: ${reviewStatus}`);
    
    // 确保图表实例存在
    if (!impactFactorChartRef.value) {
      return Promise.resolve();
    }
    
    // 如果图表实例不存在，尝试初始化
    if (!impactFactorChart.value && impactFactorChartRef.value) {
      try {
        impactFactorChart.value = echarts.init(impactFactorChartRef.value);
      } catch (error) {
        console.error('影响因子图表初始化失败:', error);
        return Promise.resolve();
      }
    }
    
    // 如果图表实例仍然不存在，退出
    if (!impactFactorChart.value) {
      return Promise.resolve();
    }
    
    impactFactorChart.value.showLoading();
    
    return getPaperImpactFactorDistribution({ 
      range,
      reviewStatus,
      userId: showPersonalPapers.value ? userId.value : undefined
    })
    .then(response => {
      impactFactorChart.value.hideLoading();
      
      if (response?.code === 200) {
        console.log('获取影响因子分布成功');
        
        const data = response.data || {};
        const categories = Object.keys(data).filter(key => key && typeof data[key] !== 'undefined');
        const values = categories.map(key => Number(data[key]) || 0);
        
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '8%', // 增加左边距，将图表右移
            right: '8%', // 增加右边距，压缩图表宽度
            bottom: '20%', // 增加底部空间
            top: '10%', // 增加顶部空间
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: categories,
              axisTick: {
                alignWithLabel: true
              },
              name: '影响因子范围',
              axisLabel: {
                interval: 0, // 显示所有标签
                rotate: 0, // 不旋转，水平显示
                fontSize: 9, // 进一步减小字体大小
                margin: 5, // 减小标签与轴的距离
                formatter: function(value) {
                  // 更激进的截断，只显示前6个字符
                  if (value && value.length > 6) {
                    return value.substring(0, 6) + '..';
                  }
                  return value;
                }
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '论文数量',
              minInterval: 1 // 确保Y轴刻度是整数
            }
          ],
          series: [
            {
              name: '论文数量',
              type: 'bar',
              barWidth: '40%', // 减小柱状图宽度，从60%减到40%
              data: values,
              itemStyle: {
                color: '#52c41a'
              },
              emphasis: {
                itemStyle: {
                  color: '#73d13d'
                }
              }
            }
          ]
        };
        
        impactFactorChart.value.setOption(option);
      } else {
        console.error('获取影响因子分布失败:', response);
        message.error('获取影响因子分布失败');
      }
    })
    .catch(error => {
      impactFactorChart.value.hideLoading();
      console.error('获取影响因子分布出错:', error);
      message.error('获取影响因子分布失败: ' + (error.message || '未知错误'));
    });
  };

  // 加载审核状态概览
  const loadReviewStatusOverview = async (range = 'in') => {
    console.log(`加载审核状态概览，范围: ${range}`);

    // 确保图表实例存在
    if (!reviewStatusChartRef.value) {
      return Promise.resolve();
    }

    // 如果图表实例不存在，尝试初始化
    if (!reviewStatusChart.value && reviewStatusChartRef.value) {
      try {
        reviewStatusChart.value = echarts.init(reviewStatusChartRef.value);
      } catch (error) {
        console.error('审核状态图表初始化失败:', error);
        return Promise.resolve();
      }
    }

    // 如果图表实例仍然不存在，退出
    if (!reviewStatusChart.value) {
      return Promise.resolve();
    }

    reviewStatusChart.value.showLoading();

    try {
      const params = { range };

      // 如果是个人视图，添加用户ID
      if (showPersonalPapers.value && userId.value) {
        params.userId = userId.value;
      }

      const response = await getReviewStatusOverview(params);

      if (response?.code === 200 && response.data) {
        const { reviewed, pending, rejected } = response.data;

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'horizontal',
            left: 'left',
            bottom: 0,
            textStyle: {
              fontSize: 12
            }
          },
          series: [{
            name: '审核状态',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {
                value: reviewed,
                name: '已审核',
                itemStyle: { color: '#52c41a' }
              },
              {
                value: pending,
                name: '待审核',
                itemStyle: { color: '#faad14' }
              },
              {
                value: rejected,
                name: '已拒绝',
                itemStyle: { color: '#ff4d4f' }
              }
            ]
          }]
        };

        reviewStatusChart.value.setOption(option);
      } else {
        console.error('获取审核状态概览数据失败:', response?.message || '未知错误');
        message.error('获取审核状态概览数据失败');
      }
    } catch (error) {
      console.error('加载审核状态概览失败:', error);
      message.error('加载审核状态概览失败');
    } finally {
      reviewStatusChart.value.hideLoading();
    }
  };
  
  // 加载个人统计数据
  const loadPersonalStats = () => {
    console.log('加载个人统计数据');
    // 在此添加加载逻辑
    return Promise.resolve();
  };
  
  // 处理用户得分表格分页变化
  const handleUserScoreTableChange = (pagination, filters, sorter) => {
    // 只更新必要的分页属性，保持其他配置不变
    userScorePagination.current = pagination.current;
    userScorePagination.pageSize = pagination.pageSize;
    // 确保其他配置属性保持不变
    userScorePagination.showSizeChanger = true;
    userScorePagination.pageSizeOptions = ['10', '20', '50'];
    userScorePagination.showTotal = total => `共 ${total} 条`;
    fetchAllUsersTotalScore();
  };
  
  // 处理用户得分范围变化
  const handleUserScoreRangeChange = (value) => {
    userScoreChartRange.value = value;
    fetchAllUsersTotalScore();
  };
  
  // 重置用户得分搜索条件
  const resetUserScoreSearch = () => {
    userScoreSearchParams.nickname = '';
    userScoreSearchParams.paperId = '';
    userScoreChartRange.value = 'all';
    userScorePagination.current = 1;
    fetchAllUsersTotalScore();
  };
  
  const showUserScoreDetails = (record) => {
    userScoreDetailLoading.value = true;
    
    // 获取用户详细得分情况
    getUserTotalScore({
      userId: record.userId,
      range: userScoreChartRange.value,
      page: 1,
      pageSize: 10
    }).then(response => {
      if (response?.code === 200) {
        userScoreDetailData.value = response.data;
        userScoreDetailVisible.value = true;
        } else {
        message.error('获取用户详情失败');
      }
    }).catch(error => {
      console.error('获取用户详情错误:', error);
      message.error('获取用户详情出错');
    }).finally(() => {
      userScoreDetailLoading.value = false;
    });
  };
  
  // 获取所有用户论文得分排名
  const fetchAllUsersTotalScore = async () => {
    
    // 如果有请求正在进行中，跳过重复请求
    if (requestLocks.userTotalScore) {
      return;
    }
    
    // 生成新的请求ID并锁定
    const requestId = Date.now() + Math.random().toString(16).slice(2);
    requestIds.userTotalScore = requestId;
    requestLocks.userTotalScore = true;
    
    userScoreLoading.value = true;
    
    try {
      // 构建请求参数
      const params = {
        range: userScoreChartRange.value,
        reviewStatus: userScoreSearchParams.reviewStatus,
        page: userScorePagination.current,
        pageSize: userScorePagination.pageSize,
        limit: 100, // 添加limit参数，限制最多返回100条数据
        _t: Date.now() // 时间戳
      };
      
      // 只添加非空参数
      if (userScoreSearchParams.nickname) {
        params.nickname = userScoreSearchParams.nickname.trim();
      }
      
      if (userScoreSearchParams.paperId) {
        params.paperId = userScoreSearchParams.paperId.trim();
      }
      
      // 如果是个人视图，添加userId参数
      if (showPersonalPapers.value) {
        try {
          // 等待Promise解析，获取实际ID值
          const currentUserId = await getUserId(true);
          if (currentUserId) {
            params.userId = currentUserId;
          } else {
            console.warn('无法获取用户ID用于用户得分排名查询');
          }
        } catch (error) {
          console.error('获取用户ID失败:', error);
        }
      }
      
      // 发送请求
      const response = await getAllUsersTotalScore(params);
      
      // 如果请求ID不一致，表示已经有新的请求，丢弃结果
      if (requestIds.userTotalScore !== requestId) {
        console.log('用户得分排名请求已过期，丢弃结果');
        return;
      }
      
      console.log('获取用户得分排名API响应');
      if (response?.code === 200) {
        console.log('获取用户得分排名成功，项数:', response.data.list?.length);
        userScoreData.value = response.data.list;
        userScorePagination.total = response.data.pagination.total;
      } else {
        console.error('获取用户得分排名失败:', response);
        message.error('获取用户得分排名失败');
      }
    } catch (error) {
      console.error('获取用户得分排名错误:', error);
      message.error('获取用户得分排名出错');
    } finally {
      userScoreLoading.value = false;
      requestLocks.userTotalScore = false;
    }
  };
  
  // 简化数据获取方法
  const fetchData = async () => {
    console.log('加载论文数据');
    
    // 如果有请求正在进行中，跳过重复请求
    if (requestLocks.papersList) {
      console.log('跳过重复的论文列表数据请求，正在处理中');
      return;
    }
    
    // 生成唯一ID
    const requestId = Date.now() + Math.random().toString(16).slice(2);
    requestIds.papersList = requestId;
    requestLocks.papersList = true;
    
    loading.value = true;
    errorMessage.value = '';
    
    try {
      // 构建查询参数
      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        _t: Date.now(), // 时间戳
        range: searchForm.range // 使用searchForm中的range
      };
      
      // 添加审核状态过滤
      if (searchForm.reviewStatus !== 'all') {
        params.reviewStatus = searchForm.reviewStatus === 'rejected' ? 'rejected' : 
                              searchForm.reviewStatus === 'reviewed' ? 'reviewed' : 'pending';
      }
      
      // 添加搜索条件
      if (searchForm.title) params.title = searchForm.title.trim();
      if (searchForm.authors) params.authors = searchForm.authors.trim();
      if (searchForm.type) params.type = searchForm.type;
      if (searchForm.journal) params.journal = searchForm.journal.trim();
      if (searchForm.publishStartDate) {
        params.publishStartDate = searchForm.publishStartDate.format('YYYY-MM-DD');
      }
      if (searchForm.publishEndDate) {
        params.publishEndDate = searchForm.publishEndDate.format('YYYY-MM-DD');
      }
      
      console.log('查询参数:', params);
      
      let response;
      if (showPersonalPapers.value) {
        try {
          // 确保获取到用户ID并等待Promise完成
          const currentUserId = await getUserId(true);
          console.log("currentUserId========",currentUserId);
          
          if (!currentUserId) {
            throw new Error('无法获取用户ID，请重新登录');
          }
          
          console.log('使用用户ID请求个人论文数据:', currentUserId);
          // 使用实际的用户ID值，而不是Promise对象
          response = await getPersonalHighLevelPapers(params);
          console.log("response========",response); 
        } catch (idError) {
          console.error('获取用户ID失败:', idError);
          throw new Error('获取用户ID失败: ' + idError.message);
        }
          } else {
        response = await getHighLevelPapers(params);
      }
      
      // 如果请求ID不一致，表示已经有新的请求，丢弃结果
      if (requestIds.papersList !== requestId) {
        console.log('论文列表数据请求已过期，丢弃结果');
      return;
    }
    
      if (response?.code === 200) {
        // 处理返回的数据，为缺少的字段设置默认值
        dataSource.value = (response.data.list || []).map(item => {
          // 从participants构建作者信息
          let authors = '';
          let allocationRatio = '';
          
          if (item.participants && item.participants.length > 0) {
            // 按authorRank排序作者
            const sortedParticipants = [...item.participants].sort((a, b) => 
              (a.authorRank || 999) - (b.authorRank || 999)
            );
            
            authors = sortedParticipants.map(p => 
              p.user ? (p.user.nickname || p.user.username) : p.userId
            ).join(', ');
            
            allocationRatio = sortedParticipants.map(p => 
              p.allocationRatio
            ).join(', ');
          }
          
          return {
          ...item,
          // 确保数值类型
          impactFactor: item.impactFactor ? Number(item.impactFactor) : 0,
            calculatedScore: item.paperLevel && item.paperLevel.score ? Number(item.paperLevel.score) : 0,
            // 处理新字段
            authors,
            allocationRatio,
            doi: item.doi || '',
          // 确保日期是 dayjs 对象
          publishDate: item.publishDate ? dayjs(item.publishDate) : dayjs(),
          // 处理审核人显示
          reviewerName: item.reviewerName || 
                         (item.reviewer ? (item.reviewer.nickname || item.reviewer.username) : 
                         (item.reviewerId || ''))
          };
        });
        
        // 打印处理后的数据以便调试
        console.log('处理后的数据源项数:', dataSource.value.length);
        
        pagination.total = response.data.pagination?.total || 0;
        
        // 如果图表未初始化，则初始化
        if (!chartsInitialized.value) {
          nextTick(() => {
            initCharts();
          });
        }
        } else {
        console.error('获取论文数据失败:', response);
        message.error(response?.message || '获取论文数据失败');
      }
    } catch (error) {
      console.error('获取论文数据出错:', error);
      errorMessage.value = `获取论文数据失败121: ${error.message || '未知错误'}`;
      message.error('获取论文数据失败: ' + (error.message || '未知错误'));
      dataSource.value = [];
    } finally {
      loading.value = false;
      requestLocks.papersList = false;
    }
  };
  
  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      searchForm.publishStartDate = dates[0];
      searchForm.publishEndDate = dates[1];
    } else {
      searchForm.publishStartDate = undefined;
      searchForm.publishEndDate = undefined;
    }
  };
  
  // 搜索和重置
  const handleSearch = () => {
    pagination.current = 1;
    fetchData();
  };
  
  const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
      if (key === 'reviewStatus') {
        searchForm[key] = 'reviewed'; // 重置为默认值'reviewed'
      } else if (key === 'range') {
        searchForm[key] = 'in'; // 重置为默认值'in'
      } else {
        searchForm[key] = undefined;
      }
    });
    searchForm.title = '';
    searchForm.authors = '';
    searchForm.journal = '';
    pagination.current = 1;
    fetchData();
  };
  
  // 切换个人/全部论文显示
  const togglePersonalPapers = () => {
    showPersonalPapers.value = !showPersonalPapers.value;
    fetchData();
  };
  
  // 显示添加模态框
  const showAddModal = async () => {
    console.log('显示添加论文弹窗');
    // 设置为添加模式
    isEdit.value = false;
    // 重置表单
    resetForm();
    
    // 确保fileIds和attachmentUrl数组被初始化
    formState.fileIds = [];
    formState.attachmentUrl = [];
    formState.deletedFiles = [];
    
    // 尝试获取当前用户ID作为默认提交人
    try {
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        formState.submitterId = currentUserId;
        
        // 获取当前用户信息以显示在下拉框中
        const userInfo = await usersSearch({ keyword: currentUserId });
        if (userInfo && userInfo.code === 200 && Array.isArray(userInfo.data) && userInfo.data.length > 0) {
          const currentUser = userInfo.data[0];
          formState.submitter = currentUser.nickname || currentUser.username || '';
          submitterOptions.value = [{
            id: currentUser.id,
            nickname: currentUser.nickname || currentUser.username || '未知',
            studentNumber: currentUser.studentNumber || '无工号'
          }];
        }
      }
    } catch (error) {
      console.warn('获取当前用户信息失败:', error);
    }
    
    // 使用nextTick确保DOM更新后再显示模态框
    nextTick(() => {
      // 更新模态框标题
      modalTitle.value = '新增论文';
      // 立即显示模态框
      modalVisible.value = true;
    });
  };
  
  // 显示编辑模态框
  const showEditModal = async (record) => {
    console.log('显示编辑论文弹窗', record);
    isEdit.value = true;
    resetForm();
    loading.value = true;
  
    try {
      // 从API导入获取详情方法
      const { getHighLevelPaperDetail } = await import('@/api/modules/api.high-level-papers');
      
      // 获取论文详情
      console.log('正在获取论文详情:', record.id);
      const response = await getHighLevelPaperDetail(record.id);
      
      if (!response || response.code !== 200 || !response.data) {
        message.error('获取论文详情失败');
        return;
      }
      
      const paperData = response.data;
      console.log('获取论文详情成功:', paperData);
      
      // 更新模态框标题
    modalTitle.value = '编辑论文';
      
      // 填充表单数据
      formState.id = paperData.id;
      formState.title = paperData.title || '';
      formState.journal = paperData.journal || '';
      formState.type = paperData.paperLevel ? paperData.paperLevel.paperLevel : '';
      formState.impactFactor = paperData.impactFactor || 0;
      formState.collegeCorrespondentAuthorNumber = paperData.collegeCorrespondentAuthorNumber || 0;
      formState.correspondentAuthorNumber = paperData.correspondentAuthorNumber || 0;
      formState.citations = paperData.citations || 0;
      formState.remark = paperData.remark || '';
      // 添加分配比例相关字段的赋值
      formState.allocationProportionBase = paperData.allocationProportionBase || 1;
      formState.totalAllocationProportion = paperData.totalAllocationProportion || 1;
      // 直接使用paperLevel中的score作为计算分数
      formState.calculatedScore = paperData.paperLevel && paperData.paperLevel.score ? Number(paperData.paperLevel.score) : 0;
      // 设置第一单位归属和第一作者类型
      formState.isFirstAffiliationOurs = paperData.isFirstAffiliationOurs === undefined ? true : !!paperData.isFirstAffiliationOurs;
      formState.firstAuthorType = paperData.firstAuthorType === undefined ? true : !!paperData.firstAuthorType;
      // 移除score字段的设置
      
      // 处理日期字段
      if (paperData.publishDate) {
        formState.publishDate = dayjs(paperData.publishDate);
      }
      
      // 输出调试信息
      console.log('论文类型:', formState.type);
      console.log('论文类型ID映射:', paperTypeIdMap.value);
      if (formState.type) {
        console.log('对应的论文类型ID:', paperTypeIdMap.value[formState.type]);
      }
      
      // 处理提交人信息
      if (paperData.submitter) {
        formState.submitter = paperData.submitter.nickname || paperData.submitter.username;
        formState.submitterId = paperData.submitterId || paperData.submitter.id;
        
        // 添加到选项中以便显示
        submitterOptions.value = [{
          id: paperData.submitter.id,
          nickname: paperData.submitter.nickname || paperData.submitter.username || '未知',
          studentNumber: paperData.submitter.studentNumber || '无工号'
        }];
      } else if (paperData.submitterId) {
        // 如果只有submitterId但没有提交人详情，则尝试获取用户信息
        try {
          const userInfo = await usersSearch({ keyword: paperData.submitterId });
          if (userInfo && userInfo.code === 200 && Array.isArray(userInfo.data) && userInfo.data.length > 0) {
            const submitterUser = userInfo.data.find(user => user.id === paperData.submitterId) || userInfo.data[0];
            formState.submitter = submitterUser.nickname || submitterUser.username;
            formState.submitterId = submitterUser.id;
            submitterOptions.value = [{
              id: submitterUser.id,
              nickname: submitterUser.nickname || submitterUser.username || '未知',
              studentNumber: submitterUser.studentNumber || '无工号'
            }];
          }
        } catch (error) {
          console.warn('获取提交人信息失败:', error);
        }
      }
      
      // 处理作者列表
      formState.authorsList = [];
      
      if (paperData.participants && Array.isArray(paperData.participants)) {
        formState.authorsList = paperData.participants.map(p => {
          const userData = p.user || {};
          return {
          id: p.userId,
            nickname: userData.nickname || userData.username || '未知',
            studentNumber: userData.studentNumber || '',
            allocation: parseFloat(p.allocationRatio * 100).toFixed(2),
            authorRank: p.authorRank || 1,
            isFirstAuthor: !!p.isFirstAuthor,
            isCorrespondingAuthor: !!p.isCorrespondingAuthor
          };
        });
        
        console.log('处理作者列表完成，共', formState.authorsList.length, '位作者');
      }
      
      // 处理附件列表
      fileList.value = [];
      
      // 首先检查paperData.attachments，这是后端修改后应该提供的字段
      if (paperData.attachments && Array.isArray(paperData.attachments) && paperData.attachments.length > 0) {
        console.log('从attachments字段获取到文件列表:', paperData.attachments.length, '个文件');
        fileList.value = paperData.attachments.map((attachment, index) => {
          // 将文件ID添加到fileIds数组
          if (attachment.id) {
            formState.fileIds.push(attachment.id);
          }
          
          // 将文件路径添加到attachmentUrl数组
          const filePath = attachment.url || attachment.fileUrl || attachment.filePath;
          if (filePath) {
            formState.attachmentUrl.push(filePath);
          }
          
          return {
            uid: `-${index}`,
            id: attachment.id, // 直接在文件对象上设置id
            name: attachment.name || attachment.fileName || `附件${index + 1}`,
            status: 'done',
            url: filePath,
            filePath: filePath, // 直接在文件对象上设置filePath
            size: attachment.fileSize || attachment.size,
            type: attachment.fileType || attachment.mimeType,
            response: { 
              id: attachment.id, 
              fileInfo: attachment 
            }
          };
        });
      }
      // 如果paperData.attachments不存在或为空，尝试从其他可能的字段获取文件信息
      else if (paperData.fileList && Array.isArray(paperData.fileList)) {
        console.log('从fileList字段获取到文件列表');
        fileList.value = paperData.fileList.map((file, index) => {
          // 将文件ID添加到fileIds数组
          if (file.id) {
            formState.fileIds.push(file.id);
          }
          
          // 将文件路径添加到attachmentUrl数组
          const filePath = file.url || file.filePath;
          if (filePath) {
            formState.attachmentUrl.push(filePath);
          }
          
          return {
            uid: `-${index}`,
            id: file.id,
            name: file.fileName || file.name || `附件${index + 1}`,
            status: 'done',
            url: filePath,
            filePath: filePath,
            size: file.size || file.fileSize,
            type: file.type || file.mimeType,
            response: { id: file.id, fileInfo: file }
          };
        });
      }
      // 如果记录本身有attachmentUrl字段，也尝试处理
      else if (paperData.attachmentUrl) {
        console.log('从attachmentUrl字段获取文件信息');
        // 如果attachmentUrl是数组
        if (Array.isArray(paperData.attachmentUrl)) {
          fileList.value = paperData.attachmentUrl.map((url, index) => {
            // 将文件路径添加到attachmentUrl数组
            if (url) {
              formState.attachmentUrl.push(url);
            }
            
            return {
              uid: `-${index}`,
              name: `附件${index + 1}`,
              status: 'done',
              url: url,
              filePath: url,
              response: { fileInfo: { filePath: url } }
            };
          });
        } 
        // 如果attachmentUrl是字符串，确保它不是目录路径
        else if (typeof paperData.attachmentUrl === 'string') {
          // 检查是否以斜杠或反斜杠结尾，一般表示目录
          if (!paperData.attachmentUrl.endsWith('/') && !paperData.attachmentUrl.endsWith('\\')) {
            const url = paperData.attachmentUrl;
            // 将文件路径添加到attachmentUrl数组
            formState.attachmentUrl.push(url);
            
            fileList.value = [{
              uid: '-1',
              name: '附件1',
              status: 'done',
              url: url,
              filePath: url,
              response: { fileInfo: { filePath: url } }
            }];
          }
        }
      }
      // 最后，处理原始记录中可能有的attachments字段
      else if (record.attachments && Array.isArray(record.attachments)) {
        console.log('从原始记录的attachments字段获取文件列表');
        fileList.value = record.attachments.map((attachment, index) => ({
          uid: `-${index}`,
          name: attachment.fileName || attachment.name || `附件${index + 1}`,
          status: 'done',
          url: attachment.url || attachment.fileUrl || attachment.filePath,
          size: attachment.fileSize || attachment.size,
          type: attachment.fileType || attachment.type,
          response: { id: attachment.id, fileInfo: attachment }
        }));
      }
      
      console.log('文件列表处理完成，共', fileList.value.length, '个文件');
      
      // 显示模态框
      modalVisible.value = true;
    } catch (error) {
      console.error('获取论文详情失败:', error);
      message.error('获取论文详情失败: ' + (error.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  };
  
  // 重置表单
  const resetForm = () => {
    // 重置表单引用
    if (formRef.value) {
      formRef.value.resetFields();
    }
    
    // 重置表单状态
    Object.assign(formState, {
      id: undefined,
      title: '',
      journal: '',
      publishDate: null,
      type: undefined,
      impactFactor: 0,
      collegeCorrespondentAuthorNumber: 0,
      correspondentAuthorNumber: 0,
      allocationProportionBase: '',
      totalAllocationProportion: '',
      submitter: '',
      submitterId: '', // 添加submitterId字段
      submitterRanking: '',
      authorsList: [],
      remark: '',
      citations: 0,
      calculatedScore: 0,
      score: 0,
      status: 1,
      fileIds: [], // 添加fileIds字段
      attachmentUrl: [], // 添加attachmentUrl字段
      isFirstAffiliationOurs: true, // 默认第一单位是我们大学
      firstAuthorType: true // 默认第一作者是我院研究生
    });
    
    // 清空文件列表
    fileList.value = [];
    
    // 清空提交人和作者选项
    submitterOptions.value = [];
    authorsOptions.value = [];
    
    // 清空当前作者
    Object.assign(currentAuthor, {
    id: '',
    nickname: '',
    username: '',
    studentNumber: '',
    allocation: 0,
    authorRank: 1,
    isFirstAuthor: false,
    isCorrespondingAuthor: false
  });
  };
  
  // 修改模态框确认方法
  const handleModalOk = async () => {
    try {
      // 表单验证
      await formRef.value.validate();
      
      confirmLoading.value = true;
      
      // 准备提交的数据
      const formData = new FormData();
      
      // 将表单数据转换为FormData格式
      Object.keys(formState).forEach(key => {
        if (key !== 'authorsList' && key !== 'fileList' && key !== 'fileIds' && key !== 'attachmentUrl' && 
            formState[key] !== undefined && formState[key] !== null && key !== 'submitterId') {
          if (key === 'publishDate' && formState[key]) {
            formData.append(key, dayjs(formState[key]).format('YYYY-MM-DD'));
          } else if (key === 'isFirstAffiliationOurs' || key === 'firstAuthorType') {
            // 将布尔值转换为0或1
            formData.append(key, formState[key] ? '1' : '0');
          } else {
            formData.append(key, formState[key]);
          }
        }
      });
      
      // 添加 paperLevelId 参数
      if (formState.type && paperTypeIdMap.value[formState.type]) {
        formData.append('paperLevelId', paperTypeIdMap.value[formState.type]);
        console.log('添加 paperLevelId:', paperTypeIdMap.value[formState.type], '论文类型:', formState.type);
      }
      
      // 添加submitterId - 确保只添加一次
      if (formState.submitterId) {
        formData.append('submitterId', formState.submitterId);
        console.log('添加 submitterId:', formState.submitterId);
      }
      
      // 处理作者列表数据 - 更新参数传递，包括authorRank、isFirstAuthor和isCorrespondingAuthor
      if (formState.authorsList && formState.authorsList.length > 0) {
        // 对作者列表进行排序（按authorRank排序）
        const sortedAuthors = [...formState.authorsList].sort((a, b) => (a.authorRank || 999) - (b.authorRank || 999));
        
        sortedAuthors.forEach((author, index) => {
          formData.append(`participants[${index}][userId]`, author.id);
          formData.append(`participants[${index}][authorRank]`, author.authorRank || (index + 1));
          formData.append(`participants[${index}][allocationRatio]`, author.allocation / 100);
          formData.append(`participants[${index}][isFirstAuthor]`, author.isFirstAuthor ? '1' : '0');
          formData.append(`participants[${index}][isCorrespondingAuthor]`, author.isCorrespondingAuthor ? '1' : '0');
        });
  
        console.log(`处理 ${sortedAuthors.length} 位作者信息完成`);
      }
      
      // 处理文件数据 - 改进文件处理逻辑
      console.log('处理文件数据，当前文件列表:', fileList.value);
      if (fileList.value && fileList.value.length > 0) {
        // 收集所有已上传成功的文件ID，使用多种可能的路径获取ID
        const uploadedFileIds = fileList.value
          .filter(file => file.status === 'done')
          .map(file => {
            // 直接从文件对象获取ID，或从response中获取，或从其他可能的位置获取
            const fileId = file.id || 
                         (file.response && file.response.id) || 
                         (file.response && file.response.file && file.response.file.id) ||
                         (file.response && file.response.fileInfo && file.response.fileInfo.id);
                         
            console.log(`文件 ${file.name} ID: ${fileId}`);
            return fileId;
          })
          .filter(id => id); // 过滤掉undefined或null
        
        console.log('收集到的有效文件ID:', uploadedFileIds);
        
        // 使用JSON格式传递文件ID数组
        if (uploadedFileIds.length > 0) {
          formData.append('fileIds', JSON.stringify(uploadedFileIds));
          console.log('添加到formData的fileIds:', JSON.stringify(uploadedFileIds));
        }
        
        // 处理文件路径
        const filePaths = fileList.value
          .filter(file => file.status === 'done')
          .map(file => {
            const path = file.filePath || 
                        (file.response && file.response.fileInfo && file.response.fileInfo.filePath) || 
                        file.url || '';
                        
            return path;
          })
          .filter(path => path); // 过滤空路径
        
        console.log('收集到的有效文件路径:', filePaths);
        
        // 使用JSON格式传递文件路径数组
        if (filePaths.length > 0) {
          formData.append('attachmentUrl', JSON.stringify(filePaths));
          console.log('添加到formData的attachmentUrl:', JSON.stringify(filePaths));
        }
      } else {
        console.log('未提供文件，跳过文件处理');
        // 如果是编辑模式且没有文件，明确传递空数组表示删除所有文件
        if (isEdit.value) {
          formData.append('fileIds', JSON.stringify([]));
          formData.append('attachmentUrl', JSON.stringify([]));
          console.log('编辑模式：明确传递空文件列表');
        }
      }
      
      // 确保使用formState中的fileIds和attachmentUrl(如果已设置)
      if (formState.fileIds && formState.fileIds.length > 0 && !formData.get('fileIds')) {
        formData.append('fileIds', JSON.stringify(formState.fileIds));
        console.log('从formState添加fileIds:', JSON.stringify(formState.fileIds));
      }
      
      if (formState.attachmentUrl && formState.attachmentUrl.length > 0 && !formData.get('attachmentUrl')) {
        formData.append('attachmentUrl', JSON.stringify(formState.attachmentUrl));
        console.log('从formState添加attachmentUrl:', JSON.stringify(formState.attachmentUrl));
      }
      
      // 打印formData中的所有键值对，便于调试
      for (const pair of formData.entries()) {
        console.log('FormData Entry:', pair[0], '=', pair[1]);
      }
      
      // 打印提交数据的摘要，但不显示整个FormData以避免过于冗长
      console.log(`准备${isEdit.value ? '更新' : '创建'}论文，标题: "${formState.title}", ` +
                  `提交人: ${formState.submitter}, 作者数: ${formState.authorsList?.length || 0}, ` +
                  `文件数: ${fileList.value?.length || 0}, 计算分数: ${formState.calculatedScore}`);
      
      // 导入API方法
      const { addHighLevelPaper, updateHighLevelPaper } = await import('@/api/modules/api.high-level-papers');
      
      // 根据是否编辑模式选择API
      let response;
      if (isEdit.value) {
        if (!formState.id) {
          throw new Error('编辑模式下缺少论文ID');
        }
        
        console.log(`正在更新论文ID: ${formState.id}`);
        formData.append('id', formState.id);
        console.log("formData====",formData);
        
        response = await updateHighLevelPaper(formState.id, formData);
      } else {
        console.log('正在创建新论文');
        response = await addHighLevelPaper(formData);
      }
        
      console.log(`API响应:`, response);
      
      if (response && (response.code === 200 || response.code === 201)) {
        message.success(`${isEdit.value ? '更新' : '添加'}论文成功`);
        
        // 关闭模态框
        modalVisible.value = false;
        
        // 刷新数据
        fetchData();
        loadOverallStats();
        
        // 刷新图表
        if (!showPersonalPapers.value) {
          loadPaperTypeDistribution(typeChartRange.value, typeChartReviewStatus.value);
          loadPaperYearlyDistribution(yearChartRange.value, yearChartReviewStatus.value);
          loadPaperImpactFactorDistribution(impactFactorChartRange.value, impactFactorChartReviewStatus.value);
        }
            } else {
        message.error(response?.message || `${isEdit.value ? '更新' : '添加'}论文失败`);
        }
      } catch (error) {
      console.error(`${isEdit.value ? '更新' : '添加'}论文出错:`, error);
      message.error(`${isEdit.value ? '更新' : '添加'}论文失败: ` + (error.message || '未知错误'));
      } finally {
      confirmLoading.value = false;
    }
  };
  
  // 模态框取消
  const handleModalCancel = () => {
    modalVisible.value = false;
  };
  
  // 确认删除
  const confirmDelete = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => handleDelete(record)
    })
  }

  // 处理删除
  const handleDelete = async (record) => {
    try {
      console.log('删除论文', record);
      const res = await deleteHighLevelPaper(record.id);
      if (res.code === 200) {
        message.success('删除成功');
        // 刷新数据列表
        fetchData();
      } else {
        message.error(res.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败: ' + (error.message || '未知错误'));
      console.error('删除论文出错:', error);
    }
  };
  
  // 处理审核
  const handleReview = async (record) => {
    if (!record || !record.id) {
      message.error('论文ID不存在，无法审核');
      return;
    }
    
    loading.value = true;
    try {
      const { reviewPaper } = await import('@/api/modules/api.high-level-papers');
      const response = await reviewPaper(record.id, {
        reviewStatus: 1, // 默认通过
        reviewComment: '',
        reviewer: null // 使用当前用户
      });
      
      if (response && response.code === 200) {
        message.success('论文审核成功');
      
        // 刷新数据
        fetchData();
        loadOverallStats();
        
        // 刷新图表数据
        if (!showPersonalPapers.value) {
          loadPaperTypeDistribution(typeChartRange.value, typeChartReviewStatus.value);
          loadPaperYearlyDistribution(yearChartRange.value, yearChartReviewStatus.value);
          loadPaperImpactFactorDistribution(impactFactorChartRange.value, impactFactorChartReviewStatus.value);
        }
      } else {
        message.error(response?.message || '论文审核失败');
      }
    } catch (error) {
      console.error('审核论文出错:', error);
      message.error('论文审核失败: ' + (error.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  };
  
  // 表格变化处理
  const handleTableChange = (pag) => {
    // 只更新必要的分页属性，保持其他配置不变
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    // 确保其他配置属性保持不变
    pagination.showSizeChanger = true;
    pagination.pageSizeOptions = ['10', '20', '50'];
    pagination.showTotal = (total) => `共 ${total} 条`;
    fetchData();
  };
  
  // 作者相关方法
  const handleAuthorsSearch = (value) => {
    // 清除之前的延时搜索
    if (authorsSearchTimeout.value) {
      clearTimeout(authorsSearchTimeout.value);
    }
    
    // 如果搜索词为空，则清空选项列表并返回
    if (!value || value.trim() === '') {
      authorsOptions.value = [];
      return;
    }
    
    // 设置搜索中状态
    authorsSearchLoading.value = true;
    
    // 延时搜索，避免频繁请求
    authorsSearchTimeout.value = setTimeout(() => {
      // 调用用户搜索API
      usersSearch({ keyword: value.trim() })
        .then((res) => {
          // 处理API返回结果
          if (res && res.code === 200) {
            // 将返回的用户数据保存到选项中
            authorsOptions.value = res.data || [];
          } else {
            authorsOptions.value = [];
          }
        })
        .catch((error) => {
          console.error('搜索作者失败:', error);
          message.error('搜索作者失败');
          authorsOptions.value = [];
        })
        .finally(() => {
          authorsSearchLoading.value = false;
        });
    }, 300); // 300ms的防抖延迟
  };
  
  const handleCurrentAuthorChange = (value, option) => {
    if (option && option.data) {
      currentAuthor.id = option.data.id;
      currentAuthor.studentNumber = option.data.studentNumber || '';
    }
  };
  
  const handleAddAuthor = () => {
    if (!currentAuthor.nickname || !currentAuthor.id) {
      message.warning('请先选择作者');
      return;
    }
    
    if (!currentAuthor.allocation || currentAuthor.allocation <= 0) {
      message.warning('请输入有效的分配比例');
      return;
    }
    
    if (!currentAuthor.authorRank || currentAuthor.authorRank <= 0) {
      message.warning('请输入有效的作者排名');
      return;
    }
    
    // 检查是否已存在此作者
    const existingIndex = formState.authorsList.findIndex(a => a.id === currentAuthor.id);
    if (existingIndex >= 0) {
      message.warning('此作者已添加，请勿重复添加');
      return;
    }
    
    // 添加到作者列表
    formState.authorsList.push({
      id: currentAuthor.id,
      nickname: currentAuthor.nickname,
      username: currentAuthor.username,
      studentNumber: currentAuthor.studentNumber,
      allocation: currentAuthor.allocation,
      authorRank: currentAuthor.authorRank,
      isFirstAuthor: currentAuthor.isFirstAuthor,
      isCorrespondingAuthor: currentAuthor.isCorrespondingAuthor
    });
    
    // 重置当前作者
    currentAuthor.id = '';
    currentAuthor.nickname = '';
    currentAuthor.username = '';
    currentAuthor.studentNumber = '';
    currentAuthor.allocation = 0;
    currentAuthor.authorRank = formState.authorsList.length + 1; // 默认为下一个排序
    currentAuthor.isFirstAuthor = false;
    currentAuthor.isCorrespondingAuthor = false;
  };
  
  const handleRemoveAuthor = (index) => {
    formState.authorsList.splice(index, 1);
  };
  
  // 提交人搜索方法
  const handleSubmitterSearch = (value) => {
    // 清除之前的延时搜索
    if (submitterSearchTimeout.value) {
      clearTimeout(submitterSearchTimeout.value);
    }
    
    // 如果搜索词为空，则清空选项列表并返回
    if (!value || value.trim() === '') {
      submitterOptions.value = [];
      return;
    }
    
    // 设置搜索中状态
    submitterSearchLoading.value = true;
    
    // 延时搜索，避免频繁请求
    submitterSearchTimeout.value = setTimeout(() => {
      // 调用用户搜索API
      usersSearch({ keyword: value.trim() })
        .then((res) => {
          // 处理API返回结果
          if (res && res.code === 200) {
            // 将返回的用户数据保存到选项中
            submitterOptions.value = res.data || [];
          } else {
            submitterOptions.value = [];
          }
        })
        .catch((error) => {
          console.error('搜索提交人失败:', error);
          message.error('搜索提交人失败');
          submitterOptions.value = [];
        })
        .finally(() => {
          submitterSearchLoading.value = false;
        });
    }, 300); // 300ms的防抖延迟
  };
  
  const handleSubmitterChange = (value, option) => {
    if (option && option.data) {
      formState.submitterId = option.data.id;
    }
  };
  
  // 导入导出
  const handleImport = async ({ file }) => {
    try {
      const { importHighLevelPapers } = await import('@/api/modules/api.high-level-papers');
      
      loading.value = true;
      
      // 创建表单数据
      const formData = new FormData();
      formData.append('file', file);
      
      // 调用导入API
      const response = await importHighLevelPapers(formData);
      
      if (response && response.code === 200) {
        message.success(`导入成功，成功${response.data?.success || 0}条，失败${response.data?.failed || 0}条`);
        
        // 如果有导入失败的记录，在控制台显示错误详情
        if (response.data?.failed > 0 && response.data?.errors && response.data.errors.length > 0) {
          console.log('导入错误详情:', response.data.errors);
        }
        
        // 刷新数据
        fetchData();
        loadOverallStats();
        
        // 刷新图表
    if (!showPersonalPapers.value) {
          loadPaperTypeDistribution(typeChartRange.value, typeChartReviewStatus.value);
          loadPaperYearlyDistribution(yearChartRange.value, yearChartReviewStatus.value);
          loadPaperImpactFactorDistribution(impactFactorChartRange.value, impactFactorChartReviewStatus.value);
        }
        } else {
        message.error(response?.message || '导入失败');
      }
    } catch (error) {
      console.error('导入数据出错:', error);
      message.error('导入失败: ' + (error.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  };
  
  const beforeUpload = (file) => {
    // 验证文件类型
    const isExcel = 
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                    file.type === 'application/vnd.ms-excel';
    
    if (!isExcel) {
      message.error('只能上传Excel文件!');
      return false;
    }
    
    // 文件大小限制：10MB
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB!');
      return false;
    }
    
    return true; // 返回true允许上传
  };
  
  // 挂载时初始化
  onMounted(async () => {
    console.log('高水平论文页面已挂载');

    try {
      // 添加ResizeObserver错误处理（这是浏览器的已知问题，不影响功能）
      const originalError = window.onerror;
      window.onerror = (message, source, lineno, colno, error) => {
        if (message && message.includes('ResizeObserver loop completed with undelivered notifications')) {
          // 忽略ResizeObserver的循环错误，这是浏览器的已知问题
          return true;
        }
        // 其他错误继续处理
        if (originalError) {
          return originalError(message, source, lineno, colno, error);
        }
        return false;
      };

      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize);

      // 初始化用户ID和角色
      console.log('正在初始化用户ID和角色...');
      
      // 获取当前用户角色
      try {
        currentRole.value = await getUserRole();
        console.log("当前用户角色:", currentRole.value);
        console.log("roleAuth===", currentRole.value ? currentRole.value.roleAuth : 'undefined');
      } catch (error) {
        console.error("获取用户角色失败:", error);
        message.error("获取用户角色信息失败，某些功能可能受限");
      }

      // 初始化用户ID
      try {
        currentUserId.value = await getUserId(true);
        userId.value = currentUserId.value; // 保持原有逻辑兼容性
        console.log('当前用户ID:', currentUserId.value);
      } catch (error) {
        console.error("获取用户ID失败:", error);
        message.error("获取用户ID失败，某些功能可能受限");
        currentUserId.value = '';
        userId.value = '';
      }
      
      // 如果是教师角色，默认显示个人论文
      if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
        showPersonalPapers.value = true;
        console.log('用户为教师角色，默认显示个人论文');
      }

      // 如果存在路由参数，优先使用路由参数
      const route = router.currentRoute.value;
      if (route.query.userId) {
        userId.value = route.query.userId;
        showPersonalPapers.value = true;
        console.log('从路由获取用户ID:', userId.value);
      }
      
      if (showPersonalPapers.value) {
        console.log('当前用户ID:', userId.value);
      }
    
      // 直接加载数据，不使用nextTick
      console.log('开始加载数据...');
      
      // 加载统计数据
      await loadOverallStats().catch(error => {
        console.error('加载统计数据失败:', error);
        message.error(`加载统计数据失败: ${error.message || '未知错误'}`);
      });
      
      // 初始化加载数据
      await fetchData().catch(error => {
        console.error('加载论文数据失败:', error);
        message.error(`加载论文数据失败: ${error.message || '未知错误'}`);
      });
      
      // 使用nextTick确保DOM已经渲染完成后再初始化图表
      nextTick(() => {
        // 初始化图表
        initCharts();
        
        // 如果是个人视图，加载个人统计数据
        if (showPersonalPapers.value && userId.value) {
          loadPersonalStats();
        }
      });
      
      // 加载用户论文得分排名数据
      await fetchAllUsersTotalScore();
    
      loadPaperTypeOptions();
  
      // 添加导入JSON模式相关处理（从第二个onMounted合并过来）
      nextTick(() => {
        // 保存原始函数引用，确保只执行一次
        if (!originalHandleModalOkRef.value) {
          originalHandleModalOkRef.value = handleModalOk;
        }
        
        // 替换为新的处理函数
        const originalFn = handleModalOk;
        window.handleModalOk = async function(...args) {
          // 调用原始处理逻辑
          await originalFn.apply(this, args);
          
          // 如果是JSON导入模式，处理下一条数据
          if (isJsonImportMode.value) {
            setTimeout(() => {
              handleJsonFormSubmit();
            }, 500);
          }
        };
      });
      
      // 添加对showPersonalPapers和userId的监听
      watch([showPersonalPapers, userId], () => {
        // 延迟执行，确保DOM已经更新
        nextTick(() => {
          console.log('视图模式或用户ID变化，重新初始化图表');
          // 销毁现有图表实例
          if (typeChart.value) typeChart.value.dispose();
          if (yearChart.value) yearChart.value.dispose();
          if (impactFactorChart.value) impactFactorChart.value.dispose();
          
          // 重置图表实例
          typeChart.value = null;
          yearChart.value = null;
          impactFactorChart.value = null;
          
          // 重新初始化图表
          initCharts();
        });
      });
    } catch (error) {
      console.error('页面初始化错误:', error);
      message.error(`页面初始化错误: ${error.message || '未知错误'}`);
    }
  });
  
  // 文件上传处理方法
  const handleRemove = (file) => {
    const index = fileList.value.indexOf(file);
    const newFileList = fileList.value.slice();
    newFileList.splice(index, 1);
    fileList.value = newFileList;
    return true;
  };
  
  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    previewImage.value = file.url || file.preview;
    previewVisible.value = true;
    previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1);
  };
  
  const handleFileChange = ({ fileList: newFileList }) => {
    fileList.value = newFileList;
  };
  
  
  // Base64转换工具函数
  const getBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };
  
  // 文件上传相关
  const fileList = ref([]);
  const previewVisible = ref(false);
  const previewImage = ref('');
  const previewTitle = ref('');
  
  // 添加计算分数的方法
  const updateCalculatedScore = async () => {
    if (!formState.type) {
      message.warning('请先选择论文类型');
      return;
    }
  
    try {
      // 可以调用后端API计算分数
      // 这里是一个示例实现
      const { calculatePaperScore } = await import('@/api/modules/api.high-level-papers');
      
      const params = {
        type: formState.type,
        impactFactor: formState.impactFactor || 0,
        citations: formState.citations || 0
      };
      
      const response = await calculatePaperScore(params);
      
      if (response && response.code === 200) {
        formState.calculatedScore = response.data?.score || 0;
        message.success('计算分数成功');
      } else {
        message.error(response?.message || '计算分数失败');
      }
    } catch (error) {
      console.error('计算分数出错:', error);
      message.error('计算分数失败: ' + (error.message || '未知错误'));
    }
  };
  
  // 添加导出用户得分数据的方法
  const exportUserScoreData = async () => {
    exporting.value = true;
    try {
      // 导入XLSX库
      const XLSX = await import('xlsx');
      
      // 准备请求参数
      const params = {
        range: userScoreChartRange.value,
        reviewStatus: userScoreSearchParams.reviewStatus || 'reviewed',
        isExport: true,
        _t: Date.now()
      };
      
      // 添加昵称过滤
      if (userScoreSearchParams.nickname) {
        params.nickname = userScoreSearchParams.nickname.trim();
      }
      
      // 添加论文ID过滤
      if (userScoreSearchParams.paperId) {
        params.paperId = userScoreSearchParams.paperId.trim();
      }
      
      // 调用API
      const response = await getAllUsersTotalScore(params);
      
      if (response?.code === 200 && response.data && response.data.list) {
        // 转换数据为导出格式
        const exportData = response.data.list.map((item, index) => ({
          '排名': item.rank,
          '用户ID': item.userId,
          '用户昵称': item.nickname || '',
          '学号/工号': item.studentNumber || '',
          '论文数量': item.paperCount || 0,
          '第一作者数': item.firstAuthorCount || 0,
          '通讯作者数': item.correspondingAuthorCount || 0,
          '总分': parseFloat(item.totalScore || 0).toFixed(2)
        }));
        
        // 创建工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 添加到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '用户论文得分');
        
        // 导出文件
        XLSX.writeFile(wb, `用户论文得分数据_${new Date().toISOString().split('T')[0]}.xlsx`);
        
        message.success('导出成功');
      } else {
        message.error(response?.message || '导出失败');
      }
    } catch (error) {
      console.error('导出用户得分数据失败:', error);
      message.error('导出用户得分数据失败: ' + (error.message || '未知错误'));
    } finally {
      exporting.value = false;
    }
  };
  
  // 搜索延时变量
  const submitterSearchTimeout = ref(null);
  const authorsSearchTimeout = ref(null);
  
  // 文件预览函数
  const previewFile = (file) => {
    utilPreviewFile(file, {
      onImagePreview: ({ url, title }) => {
        previewImage.value = url;
        previewTitle.value = title;
        previewVisible.value = true;
      }
    });
  };
  
  // 文件下载函数
  const downloadFile = (file) => {
    utilDownloadFile(file);
  };
  
  // 确认删除文件
  const confirmDeleteFile = async (file) => {
    await utilDeleteFile(file, {
      deleteApi: apiDeleteFile,
      onSuccess: (file, fileId) => {
        // 从文件列表中移除
        fileList.value = fileList.value.filter(item => item.uid !== file.uid);
        
        // 从formState中移除文件ID和路径
        if (formState.fileIds && fileId) {
          formState.fileIds = formState.fileIds.filter(id => id !== fileId);
        }
        
        if (formState.attachmentUrl && file.response?.fileInfo?.filePath) {
          formState.attachmentUrl = formState.attachmentUrl.filter(path => 
            path !== file.response.fileInfo.filePath
          );
        }
      }
    });
  };
  
  // 处理上传前检查
  const beforeUploadFile = (file) => {
    // 检查文件类型
    const allowedTypes = [
      // 文档类型
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      // 图片类型
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp'
    ];
    
    const isAllowedType = allowedTypes.includes(file.type);
    if (!isAllowedType) {
      message.error('文件类型不支持！请上传文档或图片文件');
      return isAllowedType || Upload.LIST_IGNORE;
    }
    
    // 检查单个文件大小限制（10MB）
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('单个文件不能超过10MB！');
      return isLt10M || Upload.LIST_IGNORE;
    }
    
    // 检查文件总数限制
    if (fileList.value.length >= 5) {
      message.error('最多只能上传5个文件！');
      return Upload.LIST_IGNORE;
    }
    
    return true;
  };
  
  // 格式化文件大小
  const formatFileSize = (size) => {
    if (!size || size === undefined) return '未知大小';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let fileSize = size;
    let unitIndex = 0;
    
    while (fileSize >= 1024 && unitIndex < units.length - 1) {
      fileSize /= 1024;
      unitIndex++;
    }
    
    return `${fileSize.toFixed(2)} ${units[unitIndex]}`;
  };
  
  // 文件列表相关变量
  const fileColumns = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'fileName',
      ellipsis: true
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'fileSize',
      width: 100
    },
    {
      title: '操作',
      key: 'action',
      width: 200
    }
  ];
  
  // 文件上传处理函数
  const handleFileUpload = ({ file, onSuccess, onError }) => {
    try {
      console.log('开始上传文件:', file.name);
      
      // 使用文件上传API
      const options = {
        files: file,
        id: formState.id || 'temp_' + Date.now(), // 使用论文ID或临时ID
        relatedId: formState.id, // 关联ID
        class: 'high_level_papers' // 使用class参数代替relatedType
      };
      
      // 显示上传中提示
      message.loading(`正在上传文件: ${file.name}...`, 0.5);
      
      uploadFiles(options)
        .then(response => {
          console.log('文件上传响应:', response);
          
          if (response && response.code === 200 && response.data && response.data.length > 0) {
            console.log('文件上传成功:', response.data);
            const fileInfo = response.data[0];
            
            // 创建一个新的文件对象
            const newFile = {
              ...file,
              uid: fileInfo.id || file.uid,
              id: fileInfo.id, // 直接在文件对象上设置id，方便后续收集
              url: fileInfo.url || '',
              status: 'done',
              response: {
                id: fileInfo.id,
                fileInfo: fileInfo // 保存完整的文件信息
              },
              serverFile: true,  // 标记为已上传到服务器的文件
              originalFileName: fileInfo.originalName || file.name, // 使用自定义属性保存原始文件名
              filePath: fileInfo.filePath // 保存文件路径信息
            };
            
            // 更新文件列表中对应文件的状态
            const index = fileList.value.findIndex(item => item.uid === file.uid);
            if (index !== -1) {
              fileList.value[index] = newFile;
            } else {
              fileList.value.push(newFile);
            }
            
            // 确保formState中有文件数组初始化
            if (!formState.fileIds) {
              formState.fileIds = [];
            }
            
            if (!formState.attachmentUrl) {
              formState.attachmentUrl = [];
            }
            
            // 将文件ID添加到formState中
            if (fileInfo.id && !formState.fileIds.includes(fileInfo.id)) {
              formState.fileIds.push(fileInfo.id);
            }
            
            // 将文件路径添加到formState中
            if (fileInfo.filePath && !formState.attachmentUrl.includes(fileInfo.filePath)) {
              formState.attachmentUrl.push(fileInfo.filePath);
            }
            
            console.log('文件上传完成，formState更新:');
            console.log('- fileIds:', formState.fileIds);
            console.log('- attachmentUrl:', formState.attachmentUrl);
            
            // 成功回调，传递完整的文件信息供表单提交时使用
            onSuccess({id: fileInfo.id, fileInfo: fileInfo});
            
            message.success(`文件 ${file.name} 上传成功`);
          } else {
            console.error('文件上传失败:', response);
            // 更新文件列表中对应文件的状态
            const index = fileList.value.findIndex(item => item.uid === file.uid);
            if (index !== -1) {
              fileList.value[index] = {
                ...fileList.value[index],
                status: 'error'
              };
            }
            onError(new Error(response?.message || '上传失败'));
            message.error('文件上传失败: ' + (response?.message || '未知错误'));
          }
        })
        .catch(error => {
          console.error('文件上传异常:', error);
          // 更新文件列表中对应文件的状态
          const index = fileList.value.findIndex(item => item.uid === file.uid);
          if (index !== -1) {
            fileList.value[index] = {
              ...fileList.value[index],
              status: 'error'
            };
          }
          onError(error);
          message.error('文件上传失败: ' + (error.message || '未知错误'));
        });
    } catch (error) {
      console.error('处理文件上传失败:', error);
      // 更新文件列表中对应文件的状态
      const index = fileList.value.findIndex(item => item.uid === file.uid);
      if (index !== -1) {
        fileList.value[index] = {
          ...fileList.value[index],
          status: 'error'
        };
      }
      onError(error);
      message.error('文件上传失败: ' + (error.message || '未知错误'));
    }
  };
  
  // 更新 paperTypeOptions 的定义，添加映射对象
  const paperTypeOptions = ref([]);
  const paperTypeIdMap = ref({}); // 添加ID映射对象
  
  const loadPaperTypeOptions = async () => {
    try {
      const res = await getAllHighLevelPapersRules();
      if (res && res.code === 200 && Array.isArray(res.data.list)) {
        // 存储论文类型id的映射
        const typeMap = {};
        res.data.list.forEach(item => {
          typeMap[item.paperLevel] = item.id;
        });
        paperTypeIdMap.value = typeMap;
  
        // 修改下拉选项，使用id作为实际值
        paperTypeOptions.value = res.data.list.map(item => ({
          value: item.paperLevel, // 保持使用paperLevel作为显示值以避免影响现有逻辑
          label: item.paperLevel,
          id: item.id // 存储id以便需要时使用
        }));
      }
    } catch (e) {
      console.error('获取论文类型失败:', e);
      paperTypeOptions.value = [];
      paperTypeIdMap.value = {};
    }
  };
  
  // 定义authorColumns（添加在声明columns变量之后）
  const authorColumns = [
    { title: '姓名', dataIndex: 'nickname', key: 'nickname' },
    { title: '工号', dataIndex: 'studentNumber', key: 'studentNumber' },
    { title: '排名', dataIndex: 'authorRank', key: 'authorRank' },
    { title: '分配比例', dataIndex: 'allocation', key: 'allocation' },
    { title: '角色', dataIndex: 'role', key: 'role' },
    { title: '操作', key: 'action' }
  ];
  
  // 添加handleEditAuthor函数 (添加在handleRemoveAuthor函数之后)
  const handleEditAuthor = (index) => {
    const author = formState.authorsList[index];
    if (author) {
      // 将作者信息复制到当前作者对象，进行编辑
      currentAuthor.id = author.id;
      currentAuthor.nickname = author.nickname;
      currentAuthor.username = author.username || '';
      currentAuthor.studentNumber = author.studentNumber || '';
      currentAuthor.allocation = author.allocation;
      currentAuthor.authorRank = author.authorRank || (index + 1);
      currentAuthor.isFirstAuthor = author.isFirstAuthor || false;
      currentAuthor.isCorrespondingAuthor = author.isCorrespondingAuthor || false;
      
      // 删除该作者，等待重新添加
      handleRemoveAuthor(index);
    }
  };
  
  // handlePaperTypeChange函数实现
  const handlePaperTypeChange = (value) => {
    console.log('论文类型变更为:', value);
    
    if (!value) {
      formState.calculatedScore = 0;
      return;
    }
    
    // 查找对应的论文类型选项
    const selectedOption = paperTypeOptions.value.find(item => item.value === value);
    
    if (selectedOption) {
      // 找到对应的论文级别ID
      const typeId = paperTypeIdMap.value[value];
      console.log('论文类型ID:', typeId, '类型:', value);
      
      // 获取当前已加载的论文规则
      getAllHighLevelPapersRules().then(res => {
        if (res && res.code === 200 && Array.isArray(res.data.list)) {
          const matchedRule = res.data.list.find(rule => rule.id === typeId);
          
          if (matchedRule) {
            // 设置计算分数
            formState.calculatedScore = parseFloat(matchedRule.score);
            console.log('自动设置计算分数:', formState.calculatedScore, '基于论文类型:', value);
          } else {
            console.warn('未找到匹配的论文类型规则');
            formState.calculatedScore = 0;
          }
        }
      }).catch(error => {
        console.error('获取论文规则出错:', error);
      });
    } else {
      console.warn('未找到所选论文类型:', value);
      formState.calculatedScore = 0;
    }
  };
  
  // 审核模态框状态
  const reviewModalVisible = ref(false);
  const reviewConfirmLoading = ref(false);
  const currentReviewPaper = ref(null);
  const reviewForm = reactive({
    reviewComment: '',
    reviewStatus: 1
  });
  
  // 处理审核提交
  const handleReviewSubmit = async () => {
    reviewConfirmLoading.value = true;
    try {
      if (!currentReviewPaper.value || !currentReviewPaper.value.id) {
        message.error('无法获取论文ID');
        return;
      }
      
      const { reviewPaper } = await import('@/api/modules/api.high-level-papers');
      const response = await reviewPaper(currentReviewPaper.value.id, {
        reviewStatus: reviewForm.reviewStatus,
        reviewComment: reviewForm.reviewComment
      });
      
      if (response && response.code === 200) {
        message.success('审核成功');
        reviewModalVisible.value = false;
        
        // 刷新数据
        fetchData();
        loadOverallStats();
        
        // 刷新图表数据
        if (!showPersonalPapers.value) {
          loadPaperTypeDistribution(typeChartRange.value, typeChartReviewStatus.value);
          loadPaperYearlyDistribution(yearChartRange.value, yearChartReviewStatus.value);
          loadPaperImpactFactorDistribution(impactFactorChartRange.value, impactFactorChartReviewStatus.value);
        }
      } else {
        message.error(response?.message || '审核失败');
      }
    } catch (error) {
      console.error('审核出错:', error);
      message.error('审核失败: ' + (error.message || '未知错误'));
    } finally {
      reviewConfirmLoading.value = false;
    }
  };
  
  // 处理审核取消
  const handleReviewCancel = () => {
    reviewModalVisible.value = false;
    currentReviewPaper.value = null;
  };
  
  // 显示审核模态框
  const showReviewModal = async (record) => {
    // 清空之前的表单数据
    reviewForm.reviewComment = '';
    reviewForm.reviewStatus = 1;
    
    try {
      // 获取论文详情
      const { getHighLevelPaperDetail } = await import('@/api/modules/api.high-level-papers');
      const response = await getHighLevelPaperDetail(record.id);
      
      if (response && response.code === 200) {
        currentReviewPaper.value = response.data;
        
        // 处理附件列表
        if (response.data.attachments && Array.isArray(response.data.attachments)) {
          // 确保附件对象格式正确
          currentReviewPaper.value.attachments = response.data.attachments.map((attachment, index) => ({
            uid: attachment.id || `-${index}`,
            name: attachment.name || attachment.fileName || `附件${index + 1}`,
            originalFileName: attachment.originalFileName || attachment.name || attachment.fileName,
            status: 'done',
            url: attachment.url || attachment.fileUrl || attachment.filePath,
            filePath: attachment.filePath || attachment.url || attachment.fileUrl,
            size: attachment.fileSize || attachment.size,
            type: attachment.fileType || attachment.mimeType,
            response: { 
              id: attachment.id, 
              fileInfo: attachment 
            }
          }));
        } else if (response.data.attachmentUrl) {
          // 如果没有attachments数组但有attachmentUrl，尝试构建附件列表
          if (typeof response.data.attachmentUrl === 'string') {
            // 如果路径不是指向目录（不以斜杠结尾），则认为是一个文件
            if (!response.data.attachmentUrl.endsWith('/') && !response.data.attachmentUrl.endsWith('\\')) {
              currentReviewPaper.value.attachments = [{
                uid: '-1',
                name: '附件1',
                originalFileName: '附件1',
                status: 'done',
                url: response.data.attachmentUrl,
                filePath: response.data.attachmentUrl,
                response: { fileInfo: { filePath: response.data.attachmentUrl } }
              }];
            } else {
              currentReviewPaper.value.attachments = [];
            }
          } else if (Array.isArray(response.data.attachmentUrl)) {
            currentReviewPaper.value.attachments = response.data.attachmentUrl.map((url, index) => ({
              uid: `-${index}`,
              name: `附件${index + 1}`,
              originalFileName: `附件${index + 1}`,
              status: 'done',
              url: url,
              filePath: url,
              response: { fileInfo: { filePath: url } }
            }));
          }
        } else {
          currentReviewPaper.value.attachments = [];
        }
        
        // 打开模态框
        reviewModalVisible.value = true;
      } else {
        message.error('获取论文详情失败');
      }
    } catch (error) {
      console.error('获取论文详情失败:', error);
      message.error('获取论文详情失败: ' + (error.message || '未知错误'));
    }
  };
  
  // 导出当前筛选结果
  const exportingTable = ref(false);
  const exportFilteredPapers = async () => {
    try {
      exportingTable.value = true;
      
      // 导入XLSX库
      const XLSX = await import('xlsx');
      
      // 获取当前用户ID
      const currentUserId = showPersonalPapers.value ? await getUserId(true) : undefined;
      
      // 构建查询参数，与搜索相同但不分页
      const exportParams = {
        title: searchForm.title,
        authors: searchForm.authors,
        type: searchForm.type,
        journal: searchForm.journal,
        publishStartDate: searchForm.publishStartDate?.format('YYYY-MM-DD'),
        publishEndDate: searchForm.publishEndDate?.format('YYYY-MM-DD'),
        reviewStatus: searchForm.reviewStatus !== 'all' ? searchForm.reviewStatus : undefined,
        range: searchForm.range, // 使用searchForm中的range
        userId: currentUserId, // 正确设置userId参数
        isExport: true, // 告知后端这是导出操作
        page: 1,
        pageSize: 10000 // 设置一个足够大的值以获取所有数据
      };
      
      // 调用查询API获取所有符合筛选条件的数据
      let response;
      if (showPersonalPapers.value) {
        // 确保传递的是参数对象而不是直接传userId
        response = await getPersonalHighLevelPapers(exportParams);
      } else {
        response = await getHighLevelPapers(exportParams);
      }
      
      if (!response || !response.data || !response.data.list || !Array.isArray(response.data.list) || response.data.list.length === 0) {
        message.error('导出失败：未找到符合条件的记录');
        return;
      }
      
      // 准备Excel数据
      const data = response.data.list.map(item => {
        // 处理作者信息
        const authors = item.authorDetails ? item.authorDetails
          .sort((a, b) => (a.authorRank || 999) - (b.authorRank || 999))
          .map(a => {
            const name = a.name || '未知';
            const ratio = a.allocationRatio ? `(${(a.allocationRatio * 100).toFixed(2)}%)` : '';
            const firstAuthor = a.isFirstAuthor ? '[第一作者]' : '';
            const correspondingAuthor = a.isCorrespondingAuthor ? '[通讯作者]' : '';
            return `${name}${ratio}${firstAuthor}${correspondingAuthor}`;
          }).join('; ') : (item.authors || '');
        
        return {
          '论文ID': item.id,
          '论文题目': item.title || '',
          '作者': authors,
          '期刊': item.journal || '',
          '出版时间': item.publishDate ? dayjs(item.publishDate).format('YYYY-MM-DD') : '',
          '影响因子': item.impactFactor || 0,
          '论文类型': item.type || (item.paperLevel ? item.paperLevel.paperLevel : ''),
          '计算分数': item.isInTimeRange === false ? '不计分' : (item.calculatedScore || (item.paperLevel ? item.paperLevel.score : 0)),
          '审核状态': item.ifReviewer == 1 ? '已审核' : (item.ifReviewer == 0 ? '已拒绝' : '待审核'),
          '是否在统计范围内': item.isInTimeRange ? '是' : '否',
          '创建时间': item.createdAt ? dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss') : '',
          '提交人': item.submitter ? (item.submitter.nickname || item.submitter.username || '') : ''
        };
      });
      
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(data);
      
      // 设置列宽
      const columnWidths = [
        { wch: 20 }, // 论文ID
        { wch: 40 }, // 论文题目
        { wch: 30 }, // 作者
        { wch: 25 }, // 期刊
        { wch: 15 }, // 出版时间
        { wch: 10 }, // 影响因子
        { wch: 15 }, // 论文类型
        { wch: 10 }, // 计算分数
        { wch: 10 }, // 审核状态
        { wch: 15 }, // 是否在统计范围内
        { wch: 20 }, // 创建时间
        { wch: 15 }  // 提交人
      ];
      worksheet['!cols'] = columnWidths;
      
      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '高水平论文列表');
      
      // 导出Excel文件
      const currentDate = dayjs().format('YYYY-MM-DD');
      XLSX.writeFile(workbook, `高水平论文列表_${currentDate}.xlsx`);
      
      message.success('导出成功');
    } catch (error) {
      console.error('导出数据出错:', error);
      message.error('导出失败: ' + (error.message || '未知错误'));
    } finally {
      exportingTable.value = false;
    }
  };
  
  const handleUserDetailTableChange = (pagination, filters, sorter) => {
    // 处理表格分页变化
    console.log('用户详情表格分页变化:', pagination);
  };
  
  // JSON导入相关变量
  const jsonData = ref([]);
  const currentJsonIndex = ref(0);
  const isJsonImportMode = ref(false);
  
  // 原始函数引用
  const originalHandleModalOkRef = ref(null);
  const originalShowAddModalRef = ref(null);
  
  // 使用函数包装的方式处理模态框提交
  const handleJsonFormSubmit = async () => {
    try {
      // 先调用原来的表单提交逻辑
      if (typeof handleModalOk === 'function') {
        await handleModalOk();
      }
      
      // 处理下一条JSON数据
      if (isJsonImportMode.value && jsonData.value.length > 0) {
        // 如果还有更多数据
        if (currentJsonIndex.value < jsonData.value.length - 1) {
          // 增加索引
          currentJsonIndex.value++;
          
          // 提示用户
          message.success(`正在处理第 ${currentJsonIndex.value + 1}/${jsonData.value.length} 条数据`);
          
          // 等待短暂时间后显示下一条
          setTimeout(() => {
            showAddModal();
            nextTick(() => {
              fillFormWithJsonData(jsonData.value[currentJsonIndex.value]);
            });
          }, 1000);
        } else {
          // 所有数据导入完成
          message.success('所有JSON数据已导入完成');
          resetJsonImportState();
        }
      }
    } catch (error) {
      console.error('JSON表单处理错误:', error);
      message.error('处理表单时出错: ' + (error.message || '未知错误'));
    }
  };
  
  // 重置JSON导入状态
  const resetJsonImportState = () => {
    jsonData.value = [];
    currentJsonIndex.value = 0;
    isJsonImportMode.value = false;
  };
  
  // 处理JSON文件导入
  const handleJsonImport = async ({ file }) => {
    try {
      // 读取文件内容并解析
      const fileContent = await readFileAsText(file);
      let parsedData = [];
      
      try {
        parsedData = JSON.parse(fileContent);
        if (!Array.isArray(parsedData) || parsedData.length === 0) {
          message.error('JSON文件格式错误或没有数据');
          return;
        }
      } catch (error) {
        console.error('解析JSON文件失败:', error);
        message.error('解析JSON文件失败: ' + (error.message || '文件格式错误'));
        return;
      }
      
      // 处理解析后的JSON数据
      processJsonData(parsedData);
      
    } catch (error) {
      console.error('处理JSON导入失败:', error);
      message.error('处理导入失败: ' + (error.message || '未知错误'));
      importPreviewLoading.value = false;
      loading.value = false;
    }
  };
  
  // 导出失败的记录
  const exportFailedRecords = () => {
    try {
      if (!importResults.value.details || importResults.value.failed === 0) {
        message.warning('没有失败记录可导出');
        return;
      }
      
      // 从详情中找出失败的记录
      const failedRecords = importResults.value.details
        .filter(detail => detail.status === 'error')
        .map(detail => detail.originalData || { title: detail.paperTitle, message: detail.message });
      
      if (failedRecords.length === 0) {
        message.warning('没有有效的失败记录可导出');
        return;
      }
      
      // 创建Blob对象
      const blob = new Blob([JSON.stringify(failedRecords, null, 2)], { type: 'application/json' });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `失败论文记录_${new Date().toISOString().split('T')[0]}.json`;
      
      // 触发下载
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      message.success('失败记录已导出');
    } catch (error) {
      console.error('导出失败记录出错:', error);
      message.error('导出失败: ' + (error.message || '未知错误'));
    }
  };
  
  // 修改开始导入函数，保存原始数据用于导出
  const handleStartImport = async () => {
    if (importPreviewData.value.length === 0) {
      message.warning('没有可导入的数据');
      return;
    }
    
    importInProgress.value = true;
    
    // 初始化导入结果
    importResults.value = {
      total: importPreviewData.value.length,
      current: 0,
      success: 0,
      failed: 0,
      details: []
    };
    
    try {
      // 依次处理每条数据
      for (let i = 0; i < importPreviewData.value.length; i++) {
        const previewItem = importPreviewData.value[i];
        const paperData = previewItem.rawData;
        importResults.value.current = i + 1;
        
        try {
          // 构建提交数据
          const formData = new FormData();
          
          // 设置基本字段
          formData.append('title', paperData.title || '');
          formData.append('journal', paperData.journal || '');
          
          // 处理日期
          if (paperData.publishDate) {
            formData.append('publishDate', paperData.publishDate);
          }
          
          // 添加论文类型和paperLevelId
          const paperType = paperData.paperLevel || paperData.type || '';
          if (paperType) {
            formData.append('type', paperType);
            
            // 查找对应的论文类型ID
            const matchingType = paperTypeOptions.value.find(option => 
              option.label === paperType || option.label.includes(paperType) || paperType.includes(option.label)
            );
            
            if (matchingType && paperTypeIdMap.value[matchingType.value]) {
              formData.append('paperLevelId', paperTypeIdMap.value[matchingType.value]);
            }
          }
          
          // 其他字段
          formData.append('impactFactor', paperData.impactFactor || '0');
          formData.append('citations', paperData.citations || '0');
          formData.append('remark', paperData.remark || '');
          formData.append('collegeCorrespondentAuthorNumber', paperData.collegeCorrespondentAuthorNumber || '0');
          formData.append('correspondentAuthorNumber', paperData.correspondentAuthorNumber || '0');
          formData.append('allocationProportionBase', paperData.allocationProportionBase || '1');
          formData.append('totalAllocationProportion', paperData.totalAllocationProportion || '1');
          formData.append('submitterRanking', paperData.submitterRanking || '');
          formData.append('calculatedScore', '10');
          formData.append('status', '1');
          formData.append('isFirstAffiliationOurs', paperData.isFirstAffiliationOurs !== undefined ? 
                          (paperData.isFirstAffiliationOurs ? '1' : '0') : '1');
          formData.append('firstAuthorType', paperData.firstAuthorType !== undefined ? 
                          (paperData.firstAuthorType ? '1' : '0') : '1');
          formData.append('deletedFiles', '');
          
          // 处理提交人 - 使用预览阶段已经查找的用户ID
          let submitterId = previewItem.userId;
          
          if (submitterId) {
            formData.append('submitter', paperData.submitter || '');
            formData.append('submitterId', submitterId);
            
            // 处理作者列表
            if (paperData.participants && Array.isArray(paperData.participants)) {
              // 为每个参与者查找正确的用户ID
              for (let j = 0; j < paperData.participants.length; j++) {
                const participant = paperData.participants[j];
                const authorName = participant.name;
                
                if (!authorName) {
                  formData.append(`participants[${j}][userId]`, submitterId);  // 默认使用submitterId
                  formData.append(`participants[${j}][authorRank]`, participant.authorRank || (j + 1).toString());
                  formData.append(`participants[${j}][allocationRatio]`, participant.allocationRatio || '1');
                  formData.append(`participants[${j}][isFirstAuthor]`, participant.isFirstAuthor ? '1' : '0');
                  formData.append(`participants[${j}][isCorrespondingAuthor]`, participant.isCorrespondingAuthor ? '1' : '0');
                  continue;
                }
                
                try {
                  // 为每个作者单独查找用户ID
                  const userSearchResult = await usersSearch({ keyword: authorName });
                  let participantUserId = submitterId; // 默认使用提交者ID
                  
                  if (userSearchResult && userSearchResult.code === 200 && userSearchResult.data.length > 0) {
                    // 找到了用户ID，使用第一个匹配结果
                    participantUserId = userSearchResult.data[0].id;
                    console.log(`为作者 "${authorName}" 找到用户ID: ${participantUserId}`);
                  } else {
                    console.warn(`未找到作者 "${authorName}" 的用户ID，使用默认ID: ${participantUserId}`);
                  }
                  
                  // 使用找到的用户ID
                  formData.append(`participants[${j}][userId]`, participantUserId);
                  formData.append(`participants[${j}][authorRank]`, participant.authorRank || (j + 1).toString());
                  formData.append(`participants[${j}][allocationRatio]`, participant.allocationRatio || '1');
                  formData.append(`participants[${j}][isFirstAuthor]`, participant.isFirstAuthor ? '1' : '0');
                  formData.append(`participants[${j}][isCorrespondingAuthor]`, participant.isCorrespondingAuthor ? '1' : '0');
                } catch (error) {
                  console.error(`查找作者 "${authorName}" 的用户ID失败:`, error);
                  // 发生错误时使用默认ID
                  formData.append(`participants[${j}][userId]`, submitterId);
                  formData.append(`participants[${j}][authorRank]`, participant.authorRank || (j + 1).toString());
                  formData.append(`participants[${j}][allocationRatio]`, participant.allocationRatio || '1');
                  formData.append(`participants[${j}][isFirstAuthor]`, participant.isFirstAuthor ? '1' : '0');
                  formData.append(`participants[${j}][isCorrespondingAuthor]`, participant.isCorrespondingAuthor ? '1' : '0');
                }
              }
            } else {
              formData.append(`participants[0][userId]`, submitterId);
              formData.append(`participants[0][authorRank]`, '1');
              formData.append(`participants[0][allocationRatio]`, '1');
              formData.append(`participants[0][isFirstAuthor]`, '1');
              formData.append(`participants[0][isCorrespondingAuthor]`, '1');
            }
          } else {
            // 如果没有找到submitterId，尝试获取当前用户ID
            const currentUserId = await getUserId(true);
            if (currentUserId) {
              formData.append('submitterId', currentUserId);
              formData.append(`participants[0][userId]`, currentUserId);
              formData.append(`participants[0][authorRank]`, '1');
              formData.append(`participants[0][allocationRatio]`, '1');
              formData.append(`participants[0][isFirstAuthor]`, '1');
              formData.append(`participants[0][isCorrespondingAuthor]`, '1');
            }
          }
          
          // 调用API创建论文
          const response = await addHighLevelPaper(formData);
          
          if (response && (response.code === 200 || response.code === 201)) {
            importResults.value.success++;
            importResults.value.details.push({
              status: 'success',
              paperTitle: paperData.title || `未命名论文${i+1}`,
              message: '创建成功'
            });
          } else {
            importResults.value.failed++;
            importResults.value.details.push({
              status: 'error',
              paperTitle: paperData.title || `未命名论文${i+1}`,
              message: response?.message || '未知错误',
              originalData: paperData // 保存原始数据用于导出
            });
          }
        } catch (error) {
          importResults.value.failed++;
          importResults.value.details.push({
            status: 'error',
            paperTitle: paperData.title || `未命名论文${i+1}`,
            message: error.message || '未知错误',
            originalData: paperData // 保存原始数据用于导出
          });
        }
      }
      
      // 完成导入，显示结果
      message.destroy();
      importPreviewVisible.value = false;
      importResultVisible.value = true;
      
      // 刷新数据
      fetchData();
      loadOverallStats();
      
    } catch (error) {
      console.error('批量导入失败:', error);
      message.error('批量导入失败: ' + (error.message || '未知错误'));
    } finally {
      importInProgress.value = false;
    }
  };
  
  // 读取文件为文本
  const readFileAsText = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(new Error('读取文件失败'));
      reader.readAsText(file);
    });
  };
  
  // 填充表单数据
  const fillFormWithJsonData = async (data) => {
    console.log('填充JSON数据:', data);
    
    // 重置表单
    resetForm();
    
    // 基本信息填充
    formState.title = data.title || data.论文题目 || '';
    formState.journal = data.journal || data.期刊名称 || '';
    
    // 处理日期 - 尝试多种可能的格式
    if (data.publishDate || data.出版时间) {
      const dateStr = data.publishDate || data.出版时间;
      formState.publishDate = dayjs(dateStr);
    }
    
    // 论文类型
    const paperType = data.paperLevel || data.type || data.论文类型 || '';
    if (paperType) {
      // 查找最匹配的论文类型
      const matchingType = paperTypeOptions.value.find(option => 
        option.label === paperType || option.label.includes(paperType) || paperType.includes(option.label)
      );
      
      if (matchingType) {
        formState.type = matchingType.value;
        // 触发论文类型变化事件来自动计算分数
        if (typeof handlePaperTypeChange === 'function') {
          handlePaperTypeChange(matchingType.value);
        }
      }
    }
    
    // 影响因子
    formState.impactFactor = data.impactFactor || data.影响因子 || 0;
    
    // 引用次数
    formState.citations = data.citations || data.引用次数 || 0;
    
    // 备注
    formState.remark = data.remark || data.备注 || '';
    
    // 处理第一单位归属和第一作者类型
    formState.isFirstAffiliationOurs = data.isFirstAffiliationOurs !== undefined ? data.isFirstAffiliationOurs : true;
    formState.firstAuthorType = data.firstAuthorType !== undefined ? data.firstAuthorType : true;
    
    // 处理通讯作者相关
    formState.collegeCorrespondentAuthorNumber = data.collegeCorrespondentAuthorNumber || 0;
    formState.correspondentAuthorNumber = data.correspondentAuthorNumber || 0;
    formState.allocationProportionBase = data.allocationProportionBase || '';
    formState.totalAllocationProportion = data.totalAllocationProportion || '';
    
    // 处理提交人 - 使用模糊搜索
    if (data.submitter || data.提交人) {
      const submitterName = data.submitter || data.提交人;
      try {
        await handleSubmitterSearch(submitterName);
        
        // 如果找到匹配的提交人，选择第一个
        if (submitterOptions.value.length > 0) {
          formState.submitter = submitterOptions.value[0].nickname;
          formState.submitterId = submitterOptions.value[0].id;
        } else {
          formState.submitter = submitterName;
        }
        
        // 处理提交人排位
        formState.submitterRanking = data.submitterRanking || '';
      } catch (error) {
        console.error('搜索提交人失败:', error);
      }
    }
    
    // 处理作者列表
    formState.authorsList = [];
    
    // 解析作者列表
    if (data.participants && Array.isArray(data.participants)) {
      // 直接使用participants数组
      for (let i = 0; i < data.participants.length; i++) {
        const participant = data.participants[i];
        const authorName = participant.name;
        if (!authorName) continue;
        
        try {
          // 使用模糊搜索查找作者
          await handleAuthorsSearch(authorName);
          
          // 创建作者对象
          let authorObj = {
            nickname: authorName,
            allocation: participant.allocationRatio ? (participant.allocationRatio * 100) : 100 / data.participants.length,
            authorRank: participant.authorRank || i + 1,
            isFirstAuthor: !!participant.isFirstAuthor,
            isCorrespondingAuthor: !!participant.isCorrespondingAuthor
          };
          
          // 如果找到匹配的作者，使用匹配的作者信息
          if (authorsOptions.value.length > 0) {
            const matchedAuthor = authorsOptions.value[0];
            authorObj.id = matchedAuthor.id;
            authorObj.nickname = matchedAuthor.nickname || matchedAuthor.username || authorName;
            authorObj.studentNumber = matchedAuthor.studentNumber || '';
          }
          
          // 添加到作者列表
          formState.authorsList.push(authorObj);
        } catch (error) {
          console.error(`处理作者 ${authorName} 失败:`, error);
        }
      }
    } else if (data.authors || data.作者) {
      const authorsArray = Array.isArray(data.authors) ? data.authors : 
                          (typeof data.authors === 'string' ? data.authors.split(/[,;，；、]/) : 
                          (typeof data.作者 === 'string' ? data.作者.split(/[,;，；、]/) : 
                          (Array.isArray(data.作者) ? data.作者 : [])));
      
      // 依次添加作者
      for (let i = 0; i < authorsArray.length; i++) {
        const authorName = typeof authorsArray[i] === 'string' ? authorsArray[i].trim() : authorsArray[i];
        if (!authorName) continue;
        
        try {
          // 使用模糊搜索查找作者
          await handleAuthorsSearch(authorName);
          
          // 创建作者对象
          let authorObj = {
            nickname: authorName,
            allocation: data.allocationRatios && data.allocationRatios[i] ? 
                      data.allocationRatios[i] * 100 : 100 / authorsArray.length,
            authorRank: i + 1,
            isFirstAuthor: i === 0, // 第一个作者为第一作者
            isCorrespondingAuthor: false
          };
          
          // 如果找到匹配的作者，使用匹配的作者信息
          if (authorsOptions.value.length > 0) {
            const matchedAuthor = authorsOptions.value[0];
            authorObj.id = matchedAuthor.id;
            authorObj.nickname = matchedAuthor.nickname || matchedAuthor.username || authorName;
            authorObj.studentNumber = matchedAuthor.studentNumber || '';
          }
          
          // 添加到作者列表
          formState.authorsList.push(authorObj);
        } catch (error) {
          console.error(`处理作者 ${authorName} 失败:`, error);
        }
      }
    }
  };
  
  // 处理JSON上传前检查
  const beforeJsonUpload = (file) => {
    const isJSON = file.type === 'application/json' || file.name.endsWith('.json');
    if (!isJSON) {
      message.error('只能上传JSON文件!');
      return false;
    }
    
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB!');
      return false;
    }
    
    return true;
  };
  
  // 监听模态框状态，处理取消操作
  watch(() => modalVisible.value, (newVal, oldVal) => {
    // 当模态框关闭时
    if (!newVal && oldVal && isJsonImportMode.value) {
      // 这里可能是用户取消了导入，我们需要清理状态
      if (confirmLoading.value === false) {
        // 如果不是正在提交，则认为是取消操作
        resetJsonImportState();
        message.info('已取消JSON数据导入');
      }
    }
  });
  
  // 添加导入结果相关变量
  const importResults = ref({
    total: 0,
    current: 0,
    success: 0,
    failed: 0,
    details: []
  });
  const importResultVisible = ref(false);
  
  // 添加导入预览相关变量
  const importPreviewVisible = ref(false);
  const importPreviewData = ref([]);
  const importPreviewLoading = ref(false);
  const importInProgress = ref(false);
  const modifiedImportData = ref([]); // 用于存储用户修改后的数据
  const userIdCheckResults = ref({ notFound: 0 });
  
  // 取消导入预览
  const handleCancelImportPreview = () => {
    importPreviewVisible.value = false;
    importPreviewData.value = [];
    modifiedImportData.value = [];
    importPreviewLoading.value = false;
  };
  
  // Excel转JSON相关逻辑
  const convertingExcel = ref(false);
  
  // 添加一个全局变量存储最近转换的Excel数据
  const lastConvertedExcelData = ref(null);
  
  // 处理Excel到JSON转换
  const handleExcelToJsonConvert = async ({ file }) => {
    // 创建唯一的消息ID
    const messageKey = `excel_convert_${Date.now()}`;
    
    try {
      convertingExcel.value = true;
      
      // 显示正在处理的提示，使用key可以后续精确销毁
      message.loading({ 
        content: '正在解析Excel文件，请稍候...', 
        key: messageKey,
        duration: 0 // 不自动消失
      });
      
      // 获取文件基本名（不含扩展名）
      const fileName = file.name.split('.').slice(0, -1).join('.') || 'paper_export';
      
      // 默认配置：表头在第三行 (从0开始计数，索引为2)
      const options = {
        headerRow: 2, // 第3行作为表头（索引从0开始，所以是2）
        sheetName: null // 默认使用第一个工作表
      };
      
      console.log('开始转换Excel文件:', fileName);
      
      // 转换Excel为JSON
      const papers = await excelToHighLevelPapersJson(file, options);
      
      // 更新消息为处理完成
      message.loading({ 
        content: '数据解析完成...', 
        key: messageKey,
        duration: 1 // 1秒后自动消失
      });
      
      if (papers.length === 0) {
        message.warning('未从Excel文件中提取到任何有效数据', 3);
        return;
      }
      
      // 确保创建的JSON数据完全符合后端API的要求格式
      const optimizedData = papers.map(paper => {
        // 移除可能的undefined值
        const cleanPaper = {};
        
        // 只保留有值的字段
        Object.entries(paper).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            cleanPaper[key] = value;
          }
        });
        
        return cleanPaper;
      });
      
      // 保存最近转换的数据以便直接导入
      lastConvertedExcelData.value = optimizedData;
      
      console.log(`Excel数据处理完成，共 ${optimizedData.length} 条数据`);
      
      // 弹出确认框，提供下载与直接导入两个选项
      Modal.confirm({
        title: 'Excel转换成功',
        content: `已成功从Excel中提取${optimizedData.length}条论文记录。请选择操作方式：`,
        okText: '直接导入系统',
        cancelText: '下载JSON文件',
        onOk: () => {
          // 直接导入数据
          handleDirectImport(optimizedData);
        },
        onCancel: async () => {
          // 下载JSON文件
          try {
            await downloadJson(optimizedData, fileName);
            message.success(`已成功下载JSON文件`, 3);
          } catch (downloadErr) {
            console.error('下载JSON文件失败:', downloadErr);
            message.error('下载失败，请尝试使用"直接导入"功能', 5);
          }
        }
      });
      
    } catch (error) {
      console.error('Excel转JSON处理失败:', error);
      // 确保任何loading消息都被清除
      message.destroy(messageKey);
      message.error(`Excel文件处理失败: ${error.message || '未知错误'}`, 5);
    } finally {
      // 最后确保状态复位
      convertingExcel.value = false;
      
      // 延迟0.5秒后销毁所有可能存在的消息，确保不会有消息一直存在
      setTimeout(() => {
        message.destroy(messageKey);
      }, 500);
    }
  };
  
  // 处理直接导入Excel转换的数据
  const handleDirectImport = (data) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      message.error('没有可导入的数据');
      return;
    }
    
    // 调用现有的JSON导入预览逻辑
    processJsonData(data);
  };
  
  // 处理JSON数据的公共方法
  const processJsonData = async (parsedData) => {
    // 不论是什么文件，都先进入预览模式
    importPreviewLoading.value = true;
    message.loading('正在解析JSON数据并检查用户ID，请稍候...', 0);
    
    // 初始化用户ID检查结果
    userIdCheckResults.value = { notFound: 0 };

    // 准备预览数据
    const previewItems = parsedData.map((item, index) => ({
      index: index + 1,
      title: item.title || '',
      journal: item.journal || '',
      publishDate: item.publishDate || '',
      type: item.paperLevel || item.type || '',
      submitter: item.submitter || '',
      submitterRanking: item.submitterRanking || '',
      collegeCorrespondentAuthorNumber: item.collegeCorrespondentAuthorNumber || 0,
      correspondentAuthorNumber: item.correspondentAuthorNumber || 0,
      allocationProportionBase: item.allocationProportionBase ? parseFloat(item.allocationProportionBase).toFixed(2) : '1.00',
      totalAllocationProportion: item.totalAllocationProportion ? parseFloat(item.totalAllocationProportion).toFixed(2) : '1.00',
      isFirstAffiliationOurs: item.isFirstAffiliationOurs === false ? false : true,
      firstAuthorType: item.firstAuthorType === false ? false : true,
      remark: item.remark || '',
      // 作者信息 - 优先使用participants，如果没有则使用authors数组
      authors: item.participants ? 
        item.participants.map(p => p.name).join(', ') : 
        (Array.isArray(item.authors) ? item.authors.join(', ') : item.authors || ''),
      userIdCheckStatus: 'checking', // 初始状态为"检查中"
      rawData: item // 保存原始数据
    }));
    
    importPreviewData.value = previewItems;
    
    // 显示预览弹窗
    importPreviewVisible.value = true;
    
    // 开始检查用户ID
    for (let i = 0; i < previewItems.length; i++) {
      const item = previewItems[i];
      const submitter = item.submitter;
      
      if (submitter) {
        try {
          const userSearchResult = await usersSearch({ keyword: submitter });
          
          if (userSearchResult && userSearchResult.code === 200 && userSearchResult.data.length > 0) {
            // 找到了用户ID
            item.userIdCheckStatus = 'found';
            item.userId = userSearchResult.data[0].id;
          } else {
            // 未找到用户ID
            item.userIdCheckStatus = 'notFound';
            userIdCheckResults.value.notFound++;
          }
        } catch (error) {
          console.error(`检查用户"${submitter}"ID失败:`, error);
          item.userIdCheckStatus = 'notFound';
          userIdCheckResults.value.notFound++;
        }
      } else {
        // 没有提交人信息
        item.userIdCheckStatus = 'notFound';
        userIdCheckResults.value.notFound++;
      }
      
      // 每5条更新一次显示
      if (i % 5 === 0 || i === previewItems.length - 1) {
        importPreviewData.value = [...previewItems];
      }
    }
    
    // 更新最终结果
    importPreviewData.value = [...previewItems];
    
    // 初始化修改后的数据为预览数据的副本
    modifiedImportData.value = [...importPreviewData.value];
    
    message.destroy();
    importPreviewLoading.value = false;
  };
  
  // Excel文件上传前检查
  const beforeExcelUpload = (file) => {
    // 检查文件类型
    const isExcel = 
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
      file.type === 'application/vnd.ms-excel' || 
      file.name.endsWith('.csv');
    
    if (!isExcel) {
      message.error('只能上传Excel文件 (.xlsx, .xls, .csv)!');
      return false;
    }
    
    // 文件大小限制：20MB
    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      message.error('文件大小不能超过20MB!');
      return false;
    }
    
    // 显示处理提示，但不设置持续时间，由handleExcelToJsonConvert函数来控制关闭
    // 使用唯一的key值，以便后续可以精确地销毁这个消息
    const messageKey = `upload_excel_${Date.now()}`;
    message.loading({
      content: '准备处理Excel文件...',
      key: messageKey,
      duration: 3 // 设置3秒后自动消失，避免卡住界面
    });
    
    console.log('Excel文件检查通过，准备开始转换');
    return true; // 返回true允许上传
  };
  
  
  // 导入预览列定义
  const previewColumns = [
    { title: '序号', dataIndex: 'index', width: 60, fixed: 'left' },
    { title: '标题', dataIndex: 'title', ellipsis: true, width: 200, fixed: 'left' },
    { title: '期刊', dataIndex: 'journal', width: 150 },
    { title: '出版时间', dataIndex: 'publishDate', width: 100 },
    { title: '类型', dataIndex: 'type', width: 150 },
    { title: '提交人', dataIndex: 'submitter', width: 100 },
    { title: '提交人排名', dataIndex: 'submitterRanking', width: 100 },
    { title: '本学院通讯作者人数', dataIndex: 'collegeCorrespondentAuthorNumber', width: 150 },
    { title: '通讯作者总人数', dataIndex: 'correspondentAuthorNumber', width: 120 },
    { title: '分配比例基数', dataIndex: 'allocationProportionBase', width: 120 },
    { title: '总分配比例', dataIndex: 'totalAllocationProportion', width: 120 },
    { title: '作者信息', dataIndex: 'authors', width: 250 },
    { title: '用户ID状态', dataIndex: 'userIdCheckStatus', width: 120, fixed: 'right' }
  ];
  
  // 导入结果列定义
  const resultColumns = [
    { title: '序号', dataIndex: 'index', width: 60 },
    { title: '论文标题', dataIndex: 'paperTitle', ellipsis: true, width: 200 },
    { title: '状态', dataIndex: 'status', width: 80 },
    { title: '消息', dataIndex: 'message', ellipsis: true, width: 300 }
  ];
  
  // 下载预览数据为JSON文件
  const handleDownloadJson = async () => {
    try {
      if (!importPreviewData.value || importPreviewData.value.length === 0) {
        message.warning('没有可下载的数据');
        return;
      }
      
      // 提取原始数据
      const originalData = importPreviewData.value.map(item => item.rawData);
      
      // 下载为JSON文件
      await downloadJson(originalData, `论文数据_${dayjs().format('YYYY-MM-DD')}`);
      message.success('JSON文件下载成功');
    } catch (error) {
      console.error('下载JSON文件失败:', error);
      message.error('下载失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理重新提交审核
  const handleResubmit = (record) => {
    Modal.confirm({
      title: '确认重新提交审核',
      content: '是否确认将该论文重新提交审核？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await resubmitForReview(record.id);
      }
    });
  };

  // 重新提交审核接口调用
  const resubmitForReview = async (id) => {
    try {
      loading.value = true;
      const { reapplyReview } = await import('@/api/modules/api.high-level-papers');
      const response = await reapplyReview({ id });
      
      if (response && response.code === 200) {
        message.success('重新提交审核成功');
        fetchData(); // 刷新数据
        loadOverallStats(); // 刷新统计数据
        
        // 刷新图表数据
        if (!showPersonalPapers.value) {
          loadPaperTypeDistribution(typeChartRange.value, typeChartReviewStatus.value);
          loadPaperYearlyDistribution(yearChartRange.value, yearChartReviewStatus.value);
          loadPaperImpactFactorDistribution(impactFactorChartRange.value, impactFactorChartReviewStatus.value);
        }
      } else {
        message.error(response?.message || '重新提交审核失败');
      }
    } catch (error) {
      console.error('重新提交审核失败:', error);
      message.error('重新提交审核失败: ' + (error.message || error));
    } finally {
      loading.value = false;
    }
  };
  
  // 监听个人视图和用户ID变化，重新初始化图表
  watch([showPersonalPapers, userId], () => {
    // 延迟执行，确保DOM已经更新
    nextTick(() => {
      console.log('视图模式或用户ID变化，重新初始化图表');
      // 销毁现有图表实例
      if (typeChart.value) typeChart.value.dispose();
      if (yearChart.value) yearChart.value.dispose();
      if (impactFactorChart.value) impactFactorChart.value.dispose();
      
      // 重置图表实例
      typeChart.value = null;
      yearChart.value = null;
      impactFactorChart.value = null;
      
      // 重新初始化图表
      initCharts();
    });
  });

  // 添加时间范围文本
  const timeRangeText = ref('');

  // 获取时间范围
  const getTimeRange = () => {
    // 调用API获取时间范围
    getScoreTimeRange('highLevelPapers').then(res => {
      if (res.code === 200 && res.data) {
        timeRangeText.value = res.data.timeRange || '';
      } else {
        timeRangeText.value = '暂无时间范围数据';
      }
    }).catch(error => {
      console.error('获取时间范围失败:', error);
      timeRangeText.value = '获取时间范围失败';
    });
  };

  // 在onMounted中调用
  onMounted(() => {
    getTimeRange();
  });
  </script>
  
  <style scoped>
  /* 页面特定样式 - 已提取到公共样式文件 */


  


  </style> 
  
  <style>
  /* 页面特定的全局样式 - 已提取到公共样式文件 */

  /* 响应式图表 - 页面特定 */
  @media (max-width: 1200px) {
    .ant-col:nth-child(3) {
      margin-top: 16px;
    }
  }
  </style>

  <!-- 引入公共样式 -->
  <style lang="scss">
  @import '@/styles/performance-common.scss';
  </style>