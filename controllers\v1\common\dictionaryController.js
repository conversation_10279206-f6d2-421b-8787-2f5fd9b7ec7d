const { Dictionary } = require('../../../models');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');

/**
 * 获取数据字典列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDictionaries = async (req, res) => {
  try {
    const { type, code, name, page = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (type) where.type = { [Op.like]: `%${type}%` };
    if (code) where.code = { [Op.like]: `%${code}%` };
    if (name) where.name = { [Op.like]: `%${name}%` };
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await Dictionary.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['type', 'ASC'], ['sort', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取数据字典列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取数据字典列表失败',
      data: null
    });
  }
};

/**
 * 根据类型获取数据字典
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDictionaryByType = async (req, res) => {
  try {
    const { type } = req.params;
    
    if (!type) {
      return res.status(400).json({
        code: 400,
        message: '字典类型不能为空',
        data: null
      });
    }
    
    const dictionaries = await Dictionary.findAll({
      where: { type },
      order: [['sort', 'ASC'], ['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: dictionaries
    });
  } catch (error) {
    console.error('获取数据字典失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取数据字典失败',
      data: null
    });
  }
};

/**
 * 获取数据字典详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDictionaryById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const dictionary = await Dictionary.findByPk(id);
    if (!dictionary) {
      return res.status(404).json({
        code: 404,
        message: '数据字典不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: dictionary
    });
  } catch (error) {
    console.error('获取数据字典详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取数据字典详情失败',
      data: null
    });
  }
};

/**
 * 创建数据字典
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createDictionary = async (req, res) => {
  try {
    const { type, code, name, value, description, sort = 0, status = 1 } = req.body;
    
    // 校验必填参数
    if (!type || !code || !name || value === undefined) {
      return res.status(400).json({
        code: 400,
        message: '类型、编码、名称和值不能为空',
        data: null
      });
    }
    
    // 检查是否已存在相同类型和编码的记录
    const existingDictionary = await Dictionary.findOne({
      where: {
        type,
        code
      }
    });
    
    if (existingDictionary) {
      return res.status(400).json({
        code: 400,
        message: `类型 ${type} 下已存在编码为 ${code} 的数据字典`,
        data: null
      });
    }
    
    // 创建数据字典
    const dictionary = await Dictionary.create({
      id: uuidv4(),
      type,
      code,
      name,
      value,
      description: description || '',
      sort,
      status
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: dictionary
    });
  } catch (error) {
    console.error('创建数据字典失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建数据字典失败',
      data: null
    });
  }
};

/**
 * 更新数据字典
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateDictionary = async (req, res) => {
  try {
    const { id } = req.params;
    const { type, code, name, value, description, sort, status } = req.body;
    
    // 校验必填参数
    if (!type || !code || !name || value === undefined) {
      return res.status(400).json({
        code: 400,
        message: '类型、编码、名称和值不能为空',
        data: null
      });
    }
    
    // 查找数据字典
    const dictionary = await Dictionary.findByPk(id);
    if (!dictionary) {
      return res.status(404).json({
        code: 404,
        message: '数据字典不存在',
        data: null
      });
    }
    
    // 如果类型或编码变更，检查是否与其他记录冲突
    if (type !== dictionary.type || code !== dictionary.code) {
      const existingDictionary = await Dictionary.findOne({
        where: {
          type,
          code,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingDictionary) {
        return res.status(400).json({
          code: 400,
          message: `类型 ${type} 下已存在编码为 ${code} 的数据字典`,
          data: null
        });
      }
    }
    
    // 更新数据字典
    await dictionary.update({
      type,
      code,
      name,
      value,
      description: description || dictionary.description,
      sort: sort !== undefined ? sort : dictionary.sort,
      status: status !== undefined ? status : dictionary.status
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: dictionary
    });
  } catch (error) {
    console.error('更新数据字典失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新数据字典失败',
      data: null
    });
  }
};

/**
 * 删除数据字典
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteDictionary = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找数据字典
    const dictionary = await Dictionary.findByPk(id);
    if (!dictionary) {
      return res.status(404).json({
        code: 404,
        message: '数据字典不存在',
        data: null
      });
    }
    
    // 删除数据字典
    await dictionary.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除数据字典失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除数据字典失败',
      data: null
    });
  }
};

/**
 * 批量删除数据字典
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteDictionaries = async (req, res) => {
  try {
    const { ids } = req.body;
    
    // 校验参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '字典ID不能为空',
        data: null
      });
    }
    
    // 批量删除数据字典
    const result = await Dictionary.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });
    
    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: {
        count: result
      }
    });
  } catch (error) {
    console.error('批量删除数据字典失败:', error);
    return res.status(500).json({
      code: 500,
      message: '批量删除数据字典失败',
      data: null
    });
  }
};

/**
 * 获取所有数据字典类型
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDictionaryTypes = async (req, res) => {
  try {
    const { name, code, page = 1, pageSize = 10 } = req.body;
    
    // 构建查询条件
    const where = { isDictionaryType: true };
    if (name) where.name = { [Op.like]: `%${name}%` };
    if (code) where.code = { [Op.like]: `%${code}%` };
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await Dictionary.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['sort', 'ASC'], ['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        total: count,
        page: Number(page),
        pageSize: Number(pageSize)
      }
    });
  } catch (error) {
    console.error('获取字典类型列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取字典类型列表失败',
      data: null
    });
  }
};

/**
 * 获取字典类型详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDictionaryTypeById = async (req, res) => {
  try {
    const { id } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '字典类型ID不能为空',
        data: null
      });
    }
    
    const dictionaryType = await Dictionary.findOne({
      where: { 
        id,
        isDictionaryType: true
      }
    });
    
    if (!dictionaryType) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: dictionaryType
    });
  } catch (error) {
    console.error('获取字典类型详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取字典类型详情失败',
      data: null
    });
  }
};

/**
 * 创建字典类型
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createDictionaryType = async (req, res) => {
  try {
    const { name, code, description, sort = 0, status = 1 } = req.body;
    
    // 校验必填参数
    if (!name || !code) {
      return res.status(400).json({
        code: 400,
        message: '类型名称和编码不能为空',
        data: null
      });
    }
    
    // 检查是否已存在相同编码的记录
    const existingType = await Dictionary.findOne({
      where: {
        code,
        isDictionaryType: true
      }
    });
    
    if (existingType) {
      return res.status(400).json({
        code: 400,
        message: `已存在编码为 ${code} 的字典类型`,
        data: null
      });
    }
    
    // 创建字典类型
    const dictionaryType = await Dictionary.create({
      id: uuidv4(),
      name,
      code,
      description: description || '',
      sort,
      status,
      isDictionaryType: true,
      value: code // 对于类型，value等于code
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: dictionaryType
    });
  } catch (error) {
    console.error('创建字典类型失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建字典类型失败',
      data: null
    });
  }
};

/**
 * 更新字典类型
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateDictionaryType = async (req, res) => {
  try {
    const { id, name, description, sort, status } = req.body;
    
    // 校验必填参数
    if (!id || !name) {
      return res.status(400).json({
        code: 400,
        message: 'ID和类型名称不能为空',
        data: null
      });
    }
    
    // 查找字典类型
    const dictionaryType = await Dictionary.findOne({
      where: { 
        id,
        isDictionaryType: true
      }
    });
    
    if (!dictionaryType) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在',
        data: null
      });
    }
    
    // 更新字典类型
    await dictionaryType.update({
      name,
      description: description || dictionaryType.description,
      sort: sort !== undefined ? sort : dictionaryType.sort,
      status: status !== undefined ? status : dictionaryType.status
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: dictionaryType
    });
  } catch (error) {
    console.error('更新字典类型失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新字典类型失败',
      data: null
    });
  }
};

/**
 * 删除字典类型
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteDictionaryType = async (req, res) => {
  try {
    const { id } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '字典类型ID不能为空',
        data: null
      });
    }
    
    // 查找字典类型
    const dictionaryType = await Dictionary.findOne({
      where: { 
        id,
        isDictionaryType: true
      }
    });
    
    if (!dictionaryType) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在',
        data: null
      });
    }
    
    // 检查是否有关联的字典数据
    const relatedData = await Dictionary.findOne({
      where: {
        type: dictionaryType.code,
        isDictionaryType: false
      }
    });
    
    if (relatedData) {
      return res.status(400).json({
        code: 400,
        message: '该字典类型下存在字典数据，无法删除',
        data: null
      });
    }
    
    // 删除字典类型
    await dictionaryType.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除字典类型失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除字典类型失败',
      data: null
    });
  }
};

/**
 * 获取字典数据列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDictionaryData = async (req, res) => {
  try {
    const { typeId, label, value, page = 1, pageSize = 10 } = req.body;
    
    // 查询类型
    let typeCode = null;
    if (typeId) {
      const type = await Dictionary.findOne({
        where: { 
          id: typeId,
          isDictionaryType: true
        }
      });
      
      if (!type) {
        return res.status(404).json({
          code: 404,
          message: '字典类型不存在',
          data: null
        });
      }
      
      typeCode = type.code;
    }
    
    // 构建查询条件
    const where = { isDictionaryType: false };
    if (typeCode) where.type = typeCode;
    if (label) where.name = { [Op.like]: `%${label}%` };
    if (value) where.value = { [Op.like]: `%${value}%` };
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await Dictionary.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['sort', 'ASC'], ['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        total: count,
        page: Number(page),
        pageSize: Number(pageSize)
      }
    });
  } catch (error) {
    console.error('获取字典数据列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取字典数据列表失败',
      data: null
    });
  }
};

/**
 * 获取字典数据详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDictionaryDataById = async (req, res) => {
  try {
    const { id } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '字典数据ID不能为空',
        data: null
      });
    }
    
    const dictionaryData = await Dictionary.findOne({
      where: { 
        id,
        isDictionaryType: false
      }
    });
    
    if (!dictionaryData) {
      return res.status(404).json({
        code: 404,
        message: '字典数据不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: dictionaryData
    });
  } catch (error) {
    console.error('获取字典数据详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取字典数据详情失败',
      data: null
    });
  }
};

/**
 * 创建字典数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createDictionaryData = async (req, res) => {
  try {
    const { typeId, label, value, description, sort = 0, status = 1 } = req.body;
    
    // 校验必填参数
    if (!typeId || !label || !value) {
      return res.status(400).json({
        code: 400,
        message: '类型ID、标签名和标签值不能为空',
        data: null
      });
    }
    
    // 查询类型
    const type = await Dictionary.findOne({
      where: { 
        id: typeId,
        isDictionaryType: true
      }
    });
    
    if (!type) {
      return res.status(404).json({
        code: 404,
        message: '字典类型不存在',
        data: null
      });
    }
    
    // 检查是否已存在相同标签值的记录
    const existingData = await Dictionary.findOne({
      where: {
        type: type.code,
        value,
        isDictionaryType: false
      }
    });
    
    if (existingData) {
      return res.status(400).json({
        code: 400,
        message: `类型 ${type.name} 下已存在标签值为 ${value} 的字典数据`,
        data: null
      });
    }
    
    // 创建字典数据
    const dictionaryData = await Dictionary.create({
      id: uuidv4(),
      type: type.code,
      code: `${type.code}_${value}`,
      name: label,
      value,
      description: description || '',
      sort,
      status,
      isDictionaryType: false
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: dictionaryData
    });
  } catch (error) {
    console.error('创建字典数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建字典数据失败',
      data: null
    });
  }
};

/**
 * 更新字典数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateDictionaryData = async (req, res) => {
  try {
    const { id, label, description, sort, status } = req.body;
    
    // 校验必填参数
    if (!id || !label) {
      return res.status(400).json({
        code: 400,
        message: 'ID和标签名不能为空',
        data: null
      });
    }
    
    // 查找字典数据
    const dictionaryData = await Dictionary.findOne({
      where: { 
        id,
        isDictionaryType: false
      }
    });
    
    if (!dictionaryData) {
      return res.status(404).json({
        code: 404,
        message: '字典数据不存在',
        data: null
      });
    }
    
    // 更新字典数据
    await dictionaryData.update({
      name: label,
      description: description || dictionaryData.description,
      sort: sort !== undefined ? sort : dictionaryData.sort,
      status: status !== undefined ? status : dictionaryData.status
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: dictionaryData
    });
  } catch (error) {
    console.error('更新字典数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新字典数据失败',
      data: null
    });
  }
};

/**
 * 删除字典数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteDictionaryData = async (req, res) => {
  try {
    const { id } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '字典数据ID不能为空',
        data: null
      });
    }
    
    // 查找字典数据
    const dictionaryData = await Dictionary.findOne({
      where: { 
        id,
        isDictionaryType: false
      }
    });
    
    if (!dictionaryData) {
      return res.status(404).json({
        code: 404,
        message: '字典数据不存在',
        data: null
      });
    }
    
    // 删除字典数据
    await dictionaryData.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除字典数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除字典数据失败',
      data: null
    });
  }
}; 