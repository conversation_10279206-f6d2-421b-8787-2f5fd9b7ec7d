import { message } from 'ant-design-vue';
import { deleteFile as apiDeleteFile } from '@/api/modules/api.file'
/**
 * 统一文件预览函数
 * @param {Object} file - 文件对象
 * @param {Object} options - 可选配置
 * @param {Function} options.onImagePreview - 图片预览回调函数，用于组件内图片预览
 * @param {String} options.apiBaseUrl - API基础URL，默认使用环境变量
 */
export const previewFile = (file, options = {}) => {
  try {
    console.log('准备预览文件:', file);
    
    // 提取API基础URL
    const apiBaseUrl = options.apiBaseUrl || import.meta.env.VITE_API_BASE_URL;
    
    // 尝试获取文件ID - 兼容不同组件的文件对象结构
    const fileId = file.response?.file?.id || file.response?.id || file.data?.id;
    
    // 尝试获取文件URL - 兼容不同组件的文件对象结构
    const fileUrl = file.url || 
                   (file.response && file.response.fileInfo && (file.response.fileInfo.url || file.response.fileInfo.filePath)) || 
                   (file.data && file.data.filePath);
    
    // 检查是否为图片文件
    const isImage = fileUrl && /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileUrl);
    
    // 优先判断是否为图片并调用预览回调
    if (isImage && options.onImagePreview && typeof options.onImagePreview === 'function') {
      options.onImagePreview({
        url: fileUrl,
        title: file.name || file.originalName || '预览图片'
      });
      return;
    }
    
    // 如果有文件ID，优先使用API预览
    if (fileId) {
      message.loading('正在打开预览...', 0.5);
      const previewUrl = `${apiBaseUrl}/sys/file/preview/${fileId}`;
      console.log('预览URL:', previewUrl);
      window.open(previewUrl, '_blank');
      return;
    }
    
    // 如果只有URL，直接在新窗口打开
    if (fileUrl) {
      window.open(fileUrl, '_blank');
      return;
    }
    
    // 都没有，显示错误信息
    message.warning('无法预览此文件，文件信息不完整');
  } catch (error) {
    console.error('预览文件失败:', error);
    message.error('预览失败，请重试');
  }
};


/**
 * 统一文件下载函数
 * @param {Object} file - 文件对象
 * @param {Object} options - 可选配置
 * @param {String} options.apiBaseUrl - API基础URL，默认使用环境变量
 */
export const downloadFile = (file, options = {}) => {
  try {
    console.log('准备下载文件:', file);
    
    // 提取API基础URL
    const apiBaseUrl = options.apiBaseUrl || import.meta.env.VITE_API_BASE_URL;
    
    // 尝试获取文件ID - 兼容不同组件的文件对象结构
    const fileId = file.response?.file?.id || file.response?.id || file.data?.id;
    
    // 获取文件名
    const fileName = file.originalFileName || file.name || '下载文件';
    
    // 如果有文件ID，优先使用API下载
    if (fileId) {
      message.loading('准备下载文件...', 0.5);
      const downloadUrl = `${apiBaseUrl}/sys/file/download/${fileId}`;
      console.log('下载URL:', downloadUrl);
      
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = downloadUrl;
      link.setAttribute('download', fileName);
      link.setAttribute('rel', 'noopener noreferrer');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success(`文件 ${fileName} 下载成功`);
      return;
    }
    
    // 尝试获取文件URL - 兼容不同组件的文件对象结构
    const fileUrl = file.url || 
                   (file.response && file.response.fileInfo && (file.response.fileInfo.url || file.response.fileInfo.filePath)) || 
                   (file.data && file.data.filePath);
    
    // 如果有URL，使用URL下载
    if (fileUrl) {
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success(`正在下载: ${fileName}`);
      return;
    }
    
    // 都没有，显示错误信息
    message.warning('无法下载此文件，文件信息不完整');
  } catch (error) {
    console.error('下载文件失败:', error);
    message.error('下载失败，请重试');
  }
};

/**
 * 统一文件删除函数
 * @param {Object} file - 文件对象
 * @param {Object} options - 可选配置
 * @param {Function} options.deleteApi - 删除文件的API函数，接受fileId参数
 * @param {Function} options.onSuccess - 删除成功回调
 * @param {Function} options.onError - 删除失败回调
 * @returns {Promise} - 返回删除操作的Promise
 */
export const deleteFile = async (file, options = {}) => {
  try {
    message.loading('正在删除文件...', 0.5);
    
    // 尝试获取文件ID - 兼容不同组件的文件对象结构
    const fileId = file.response?.file?.id || file.response?.id || file.data?.id;
    
    // 如果文件已经上传到服务器并且有ID
    if (fileId && options.deleteApi && typeof options.deleteApi === 'function') {
      // 调用删除文件API
      const response = await options.deleteApi(fileId);
      
      if (response && (response.code === 200 || response.code === 0)) {
        message.success('文件删除成功');
        
        // 调用成功回调
        if (options.onSuccess && typeof options.onSuccess === 'function') {
          options.onSuccess(file, fileId);
        }
        
        return { success: true, file, fileId };
      } else {
        const errorMsg = response?.message || '删除文件失败';
        message.error(errorMsg);
        
        // 调用失败回调
        if (options.onError && typeof options.onError === 'function') {
          options.onError(errorMsg);
        }
        
        return { success: false, error: errorMsg };
      }
    } else {
      // 文件尚未上传到服务器或没有提供删除API
      message.success('文件已移除');
      
      // 调用成功回调
      if (options.onSuccess && typeof options.onSuccess === 'function') {
        options.onSuccess(file);
      }
      
      return { success: true, file, local: true };
    }
  } catch (error) {
    console.error('删除文件失败:', error);
    const errorMsg = '删除文件失败: ' + (error.message || '未知错误');
    message.error(errorMsg);
    
    // 调用失败回调
    if (options.onError && typeof options.onError === 'function') {
      options.onError(errorMsg, error);
    }
    
    return { success: false, error: errorMsg, originalError: error };
  }
};

/**
 * 通过文件ID直接预览文件
 * @param {String} fileId - 文件ID
 * @param {Object} options - 可选配置
 * @param {String} options.apiBaseUrl - API基础URL，默认使用环境变量
 */
export const previewFileById = (fileId, options = {}) => {
  console.log('准备预览文件:', fileId);
  try {
    if (!fileId) {
      message.warning('文件ID不能为空');
      return;
    }
    
    // 提取API基础URL
    const apiBaseUrl = options.apiBaseUrl || import.meta.env.VITE_API_BASE_URL;
    
    // 直接构建预览URL
    message.loading('正在打开预览...', 0.5);
    const previewUrl = `${apiBaseUrl}/sys/file/preview/${fileId}`;
    console.log('预览URL:', previewUrl);
    window.open(previewUrl, '_blank');
    
  } catch (error) {
    console.error('预览文件失败:', error);
    message.error('预览失败，请重试');
  }
};

/**
 * 直接通过ID下载文件
 * @param {String} fileId - 文件ID
 * @param {String} fileName - 文件名称，默认为"下载文件"
 * @param {Object} options - 可选配置
 * @param {String} options.apiBaseUrl - API基础URL，默认使用环境变量
 */
export const downloadFileById = (fileId, fileName = '下载文件', options = {}) => {
  try {
    // 验证文件ID
    if (!fileId) {
      message.warning('文件ID不能为空');
      return;
    }
    
    // 提取API基础URL
    const apiBaseUrl = options.apiBaseUrl || import.meta.env.VITE_API_BASE_URL;
    
    // 下载文件
    message.loading('准备下载文件...', 0.5);
    const downloadUrl = `${apiBaseUrl}/sys/file/download/${fileId}`;
    console.log('下载URL:', downloadUrl);
    
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = downloadUrl;
    link.setAttribute('download', fileName);
    link.setAttribute('rel', 'noopener noreferrer');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success(`文件 ${fileName} 下载成功`);
  } catch (error) {
    console.error('下载文件失败:', error);
    message.error('下载失败，请重试');
  }
};

/**
 * 直接通过ID删除文件
 * @param {String} fileId - 文件ID
 * @param {Object} options - 可选配置
 * @param {Function} options.deleteApi - 删除文件的API函数，接受fileId参数
 * @param {Function} options.onSuccess - 删除成功回调
 * @param {Function} options.onError - 删除失败回调
 * @returns {Promise} - 返回删除操作的Promise
 */
export const deleteFileById = async (fileId, options = {}) => {
  try {
    // 验证文件ID
    if (!fileId) {
      const errorMsg = '文件ID不能为空';
      message.warning(errorMsg);
      
      if (options.onError) {
        options.onError(errorMsg);
      }
      
      return { success: false, error: errorMsg };
    }
    
    message.loading('正在删除文件...', 0.5);
    
    // 调用删除文件API
    const response = await apiDeleteFile(fileId);
    
    if (response && (response.code === 200 || response.code === 0)) {
      message.success('文件删除成功');
      
      // 调用成功回调
      if (options.onSuccess) {
        options.onSuccess(fileId);
      }
      
      return { success: true, fileId };
    } else {
      const errorMsg = response?.message || '删除文件失败';
      message.error(errorMsg);
      
      // 调用失败回调
      if (options.onError) {
        options.onError(errorMsg);
      }
      
      return { success: false, error: errorMsg };
    }
  } catch (error) {
    console.error('删除文件失败:', error);
    const errorMsg = '删除文件失败: ' + (error.message || '未知错误');
    message.error(errorMsg);
    
    // 调用失败回调
    if (options.onError) {
      options.onError(errorMsg, error);
    }
    
    return { success: false, error: errorMsg, originalError: error };
  }
};

/**
 * 统一文件上传函数
 * @param {Object} options - 上传配置
 * @param {File} options.file - 要上传的文件对象
 * @param {Function} options.uploadApi - 上传文件的API函数
 * @param {String} options.id - 关联的ID，如果是新建，可以使用临时ID
 * @param {String} options.relatedId - 关联的ID，通常是项目/专利/教材等的ID
 * @param {String} options.class - 文件关联的类别，如'research_projects'、'patents'等
 * @param {Function} options.onProgress - 上传进度回调
 * @param {Function} options.onSuccess - 上传成功回调
 * @param {Function} options.onError - 上传失败回调
 * @param {Function} options.onComplete - 上传完成回调（无论成功失败）
 * @param {Array} options.fileList - 文件列表，如果提供，将自动更新此列表
 * @param {Object} options.formState - 表单状态对象，如果提供，将自动更新fileIds和attachmentUrl
 * @returns {Promise} - 返回上传操作的Promise
 */
export const uploadFile = (options) => {
  const {
    file,
    uploadApi,
    id = 'temp_' + Date.now(),
    relatedId,
    class: fileClass,
    onProgress,
    onSuccess,
    onError,
    onComplete,
    fileList,
    formState
  } = options;

  if (!file) {
    const error = new Error('文件对象不能为空');
    if (onError) onError(error);
    return Promise.reject(error);
  }

  if (!uploadApi || typeof uploadApi !== 'function') {
    const error = new Error('上传API函数不能为空');
    if (onError) onError(error);
    return Promise.reject(error);
  }

  return new Promise((resolve, reject) => {
    try {
      console.log('开始上传文件:', file.name);
      
      // 准备上传API参数
      const apiOptions = {
        files: file,
        id: id,
        relatedId: relatedId,
        class: fileClass
      };
      
      // 显示上传中提示
      message.loading(`正在上传文件: ${file.name}...`, 0.5);
      
      // 如果文件列表存在，更新或添加上传中状态的文件
      if (fileList && Array.isArray(fileList)) {
        // 检查文件是否已经在列表中
        const existingFileIndex = fileList.findIndex(item => 
          (item.uid === file.uid) || 
          (item.originFileObj && item.originFileObj.uid === file.uid) ||
          (item.name === file.name && item.size === file.size)
        );

        const uploadingFile = {
          uid: file.uid || `upload_${Date.now()}`,
          name: file.name,
          originalFileName: file.name,
          size: file.size,
          type: file.type,
          status: 'uploading',
          percent: 0,
          originFileObj: file
        };
        
        // 如果文件已存在，更新状态而不是添加新条目
        if (existingFileIndex !== -1) {
          fileList[existingFileIndex] = {
            ...fileList[existingFileIndex],
            ...uploadingFile
          };
        } else {
          // 否则添加到文件列表
          fileList.push(uploadingFile);
        }
        
        // 模拟上传进度 - 只对当前上传的文件进行处理
        if (onProgress) {
          const interval = setInterval(() => {
            // 重新查找文件索引，因为列表可能已经变化
            const fileIndex = fileList.findIndex(f => f.uid === uploadingFile.uid);
            if (fileIndex !== -1) {
              const targetFile = fileList[fileIndex];
              if (targetFile && targetFile.status === 'uploading' && targetFile.percent < 99) {
                targetFile.percent = Math.min(99, targetFile.percent + 10);
                onProgress({ percent: targetFile.percent });
              } else {
                clearInterval(interval);
              }
            } else {
              clearInterval(interval);
            }
          }, 200);
        }
      }
      
      // 调用上传API
      uploadApi(apiOptions)
        .then(response => {
          console.log('文件上传响应:', response);
          
          if (response && response.code === 200 && response.data && response.data.length > 0) {
            console.log('文件上传成功:', response.data);
            const fileInfo = response.data[0];
            
            // 创建一个新的文件对象
            const newFile = {
              ...file,
              uid: fileInfo.id || file.uid,
              url: fileInfo.url || '',
              status: 'done',
              response: {
                id: fileInfo.id,
                fileInfo: fileInfo // 保存完整的文件信息
              },
              serverFile: true,  // 标记为已上传到服务器的文件
              originalFileName: fileInfo.originalName || file.name,
              filePath: fileInfo.filePath // 保存文件路径信息
            };
            
            // 如果文件列表存在，更新文件状态
            if (fileList && Array.isArray(fileList)) {
              // 查找所有匹配该文件的条目（可能有多个）
              const indices = [];
              fileList.forEach((item, index) => {
                if ((item.uid === file.uid) || 
                    (item.originFileObj && item.originFileObj.uid === file.uid) ||
                    (item.name === file.name && item.size === file.size)) {
                  indices.push(index);
                }
              });
              
              // 如果找到多个匹配项，保留第一个，删除其余的
              if (indices.length > 1) {
                // 更新第一个匹配项
                fileList[indices[0]] = newFile;
                
                // 删除其他匹配项（从后往前删除避免索引变化问题）
                for (let i = indices.length - 1; i > 0; i--) {
                  fileList.splice(indices[i], 1);
                }
              } else if (indices.length === 1) {
                // 只有一个匹配项，直接更新
                fileList[indices[0]] = newFile;
              } else {
                // 没有找到匹配项，添加新文件
                fileList.push(newFile);
              }
            }
            
            // 如果表单状态对象存在，更新文件相关字段
            if (formState) {
              // 确保formState中有文件数组初始化
              if (!formState.fileIds) {
                formState.fileIds = [];
              }
              
              if (!formState.attachmentUrl) {
                formState.attachmentUrl = [];
              }
              
              // 将文件ID添加到formState中
              if (!formState.fileIds.includes(fileInfo.id)) {
                formState.fileIds.push(fileInfo.id);
              }
              
              // 将文件路径添加到formState中
              if (!formState.attachmentUrl.includes(fileInfo.filePath)) {
                formState.attachmentUrl.push(fileInfo.filePath);
              }
              
              console.log('文件上传完成，formState更新:');
              console.log('- fileIds:', formState.fileIds);
              console.log('- attachmentUrl:', formState.attachmentUrl);
            }
            
            // 成功消息
            message.success(`文件 ${file.name} 上传成功`);
            
            // 调用成功回调
            if (onSuccess) {
              onSuccess({
                file: newFile,
                response: { id: fileInfo.id, fileInfo: fileInfo }
              });
            }
            
            // 调用完成回调
            if (onComplete) {
              onComplete({ file: newFile, success: true });
            }
            
            resolve({
              success: true,
              file: newFile,
              fileInfo
            });
          } else {
            console.error('文件上传失败:', response);
            
            // 更新文件状态
            if (fileList && Array.isArray(fileList)) {
              // 查找所有该文件的条目
              fileList.forEach((item, index) => {
                if ((item.uid === file.uid) || 
                    (item.originFileObj && item.originFileObj.uid === file.uid) ||
                    (item.name === file.name && item.size === file.size)) {
                  fileList[index] = {
                    ...fileList[index],
                    status: 'error',
                    error: response?.message || '上传失败'
                  };
                }
              });
            }
            
            const error = new Error(response?.message || '上传失败');
            
            // 错误消息
            message.error('文件上传失败: ' + (response?.message || '未知错误'));
            
            // 调用错误回调
            if (onError) {
              onError(error);
            }
            
            // 调用完成回调
            if (onComplete) {
              onComplete({ file, success: false, error });
            }
            
            reject(error);
          }
        })
        .catch(error => {
          console.error('文件上传异常:', error);
          
          // 更新文件状态
          if (fileList && Array.isArray(fileList)) {
            // 查找所有该文件的条目
            fileList.forEach((item, index) => {
              if ((item.uid === file.uid) || 
                  (item.originFileObj && item.originFileObj.uid === file.uid) ||
                  (item.name === file.name && item.size === file.size)) {
                fileList[index] = {
                  ...fileList[index],
                  status: 'error',
                  error: error
                };
              }
            });
          }
          
          // 错误消息
          message.error('文件上传失败: ' + (error.message || '未知错误'));
          
          // 调用错误回调
          if (onError) {
            onError(error);
          }
          
          // 调用完成回调
          if (onComplete) {
            onComplete({ file, success: false, error });
          }
          
          reject(error);
        });
    } catch (error) {
      console.error('处理文件上传失败:', error);
      
      // 更新文件状态
      if (fileList && Array.isArray(fileList)) {
        // 查找所有该文件的条目
        fileList.forEach((item, index) => {
          if ((item.uid === file.uid) || 
              (item.originFileObj && item.originFileObj.uid === file.uid) ||
              (item.name === file.name && item.size === file.size)) {
            fileList[index] = {
              ...fileList[index],
              status: 'error',
              error: error
            };
          }
        });
      }
      
      // 错误消息
      message.error('文件上传失败: ' + (error.message || '未知错误'));
      
      // 调用错误回调
      if (onError) {
        onError(error);
      }
      
      // 调用完成回调
      if (onComplete) {
        onComplete({ file, success: false, error });
      }
      
      reject(error);
    }
  });
};
