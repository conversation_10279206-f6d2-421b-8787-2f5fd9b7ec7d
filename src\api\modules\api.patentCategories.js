import request from '../server'

// 获取专利分类列表
export function getPatentCategories() {
  return request.get('/patentCategories/categories')
}

// 获取分类及其专利数量
export function getCategoriesWithCount() {
  return request.get('/patentCategories/categories-with-count')
}

// 获取专利分类详情
export function getPatentCategoryDetail(id) {
  return request.get(`/patentCategories/category/${id}`)
}

// 创建专利分类
export function createPatentCategory(data) {
  return request.post('/patentCategories/category/create', data || {})
}

// 更新专利分类
export function updatePatentCategory(id, data) {
  return request.post('/patentCategories/category/update', { id, ...data })
}

// 删除专利分类
export function deletePatentCategory(id) {
  return request.post('/patentCategories/category/delete', { id })
}

/**
 * 获取专利分类分布数据
 * @param {Object} data - 请求参数
 * @param {String} data.range - 数据范围: 'in', 'out', 'all'
 * @param {String} data.userId - 用户ID，可选，用于过滤特定用户的数据
 * @returns {Promise}
 */
export function getCategoryDistribution(data) {
  return request.post('/patentCategories/statistics/distribution', data)
}