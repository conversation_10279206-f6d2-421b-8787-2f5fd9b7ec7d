const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义时间区间模型
module.exports = sequelize.define('time_interval',
    {
        id: {
            type: DataTypes.CHAR(36),
            primaryKey: true,
            allowNull: false,
            comment: '主键ID',
        },
        category: {
            type: DataTypes.STRING(50),
            allowNull: false,
            comment: '类型',
        },
        name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            comment: '名称',
        },
        nameC: {
            type: DataTypes.STRING(255),
            allowNull: false,
            comment: '中文名称',
        },
        startTime: {
            type: DataTypes.DATE,
            allowNull: false,
            comment: '开始时间',
        },
        endTime: {
            type: DataTypes.DATE,
            allowNull: false,
            comment: '结束时间',
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: '描述',
        },
        createBy: {
            type: DataTypes.STRING(255),
            allowNull: false,
            comment: '创建者',
        }
    },
    {
        freezeTableName: true,
        timestamps: false
    }); 