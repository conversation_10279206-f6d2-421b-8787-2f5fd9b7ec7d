const { ValidationError } = require('../utils/errors');

/**
 * 验证国际交流创建请求
 */
const validateCreateExchange = (req, res, next) => {
  const { 
    usernameList, 
    userIdList,
    name,
    type, 
    country, 
    institution,
    startDate,
    endDate,
    content,
    score
  } = req.body;
  
  const errors = [];
  
  // 验证参与用户名称
  if (!usernameList || (Array.isArray(usernameList) && usernameList.length === 0)) {
    errors.push({
      field: 'usernameList',
      message: '参与用户不能为空'
    });
  }
  
  // 验证用户ID (如果前端传递)
  if (userIdList !== undefined && Array.isArray(userIdList) && userIdList.length === 0) {
    errors.push({
      field: 'userIdList',
      message: '用户ID不能为空'
    });
  }
  
  // 验证交流类型
  if (!type) {
    errors.push({
      field: 'type',
      message: '交流类型不能为空'
    });
  }
  
  // 验证交流国家/地区
  if (!country) {
    errors.push({
      field: 'country',
      message: '交流国家/地区不能为空'
    });
  }
  
  // 验证交流机构
  if (!institution) {
    errors.push({
      field: 'institution',
      message: '交流机构不能为空'
    });
  }
  
  // 验证交流开始时间
  if (!startDate) {
    errors.push({
      field: 'startDate',
      message: '开始时间不能为空'
    });
  }
  
  // 验证交流结束时间
  if (!endDate) {
    errors.push({
      field: 'endDate',
      message: '结束时间不能为空'
    });
  }
  
  // 验证时间范围
  if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
    errors.push({
      field: 'dateRange',
      message: '开始时间不能晚于结束时间'
    });
  }
  
  // 验证交流内容
  if (!content) {
    errors.push({
      field: 'content',
      message: '交流内容不能为空'
    });
  }
  
  // 验证评分
  if (score === undefined || score === null) {
    errors.push({
      field: 'score',
      message: '评分不能为空'
    });
  }
  
  // 如果有错误，返回验证错误
  if (errors.length > 0) {
    return next(new ValidationError('请求参数验证失败', errors));
  }
  
  // 验证通过，继续执行
  next();
};

/**
 * 验证国际交流更新请求
 */
const validateUpdateExchange = (req, res, next) => {
  const { 
    usernameList, 
    userIdList,
    name,
    type, 
    country, 
    institution,
    startDate,
    endDate,
    content,
    score
  } = req.body;
  
  const errors = [];
  
  // 验证参与用户名称
  if (!usernameList || (Array.isArray(usernameList) && usernameList.length === 0)) {
    errors.push({
      field: 'usernameList',
      message: '参与用户不能为空'
    });
  }
  
  // 验证用户ID (如果前端传递)
  if (userIdList !== undefined && Array.isArray(userIdList) && userIdList.length === 0) {
    errors.push({
      field: 'userIdList',
      message: '用户ID不能为空'
    });
  }
  
  // 验证交流类型
  if (!type) {
    errors.push({
      field: 'type',
      message: '交流类型不能为空'
    });
  }
  
  // 验证交流国家/地区
  if (!country) {
    errors.push({
      field: 'country',
      message: '交流国家/地区不能为空'
    });
  }
  
  // 验证交流机构
  if (!institution) {
    errors.push({
      field: 'institution',
      message: '交流机构不能为空'
    });
  }
  
  // 验证交流开始时间
  if (!startDate) {
    errors.push({
      field: 'startDate',
      message: '开始时间不能为空'
    });
  }
  
  // 验证交流结束时间
  if (!endDate) {
    errors.push({
      field: 'endDate',
      message: '结束时间不能为空'
    });
  }
  
  // 验证时间范围
  if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
    errors.push({
      field: 'dateRange',
      message: '开始时间不能晚于结束时间'
    });
  }
  
  // 验证交流内容
  if (!content) {
    errors.push({
      field: 'content',
      message: '交流内容不能为空'
    });
  }
  
  // 验证评分
  if (score === undefined || score === null) {
    errors.push({
      field: 'score',
      message: '评分不能为空'
    });
  }
  
  // 如果有错误，返回验证错误
  if (errors.length > 0) {
    return next(new ValidationError('请求参数验证失败', errors));
  }
  
  // 验证通过，继续执行
  next();
};

/**
 * 验证删除请求
 */
const validateDeleteExchange = (req, res, next) => {
  const { id } = req.params;
  
  if (!id) {
    return next(new ValidationError('请求参数验证失败', [{
      field: 'id',
      message: '交流ID不能为空'
    }]));
  }
  
  next();
};

/**
 * 验证个人统计请求
 */
const validatePersonalStats = (req, res, next) => {
  console.log('----------- 验证个人统计请求 -----------');
  console.log('请求参数:', req.query);
  
  // 记录查询参数中的userId
  if (req.query.userId) {
    console.log(`处理个人统计请求，用户ID: ${req.query.userId}`);
  } else {
    console.log('处理个人统计请求，未提供用户ID');
  }
  
  // userId参数现在是可选的
  // 即使没有提供userId，也允许请求通过
  console.log('验证通过，继续请求');
  next();
};

module.exports = {
  validateCreateExchange,
  validateUpdateExchange,
  validateDeleteExchange,
  validatePersonalStats
}; 