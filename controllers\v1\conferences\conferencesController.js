const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const multer = require('multer');
const { Op } = require('sequelize');
const Sequelize = require('sequelize');
const db = require('../../../models/v1'); // 导入数据库连接实例
const { isProjectInTimeRange, getTimeIntervalByName, getUserInfoFromRequest, convertStoredProcResultToArray, getSequelizeInstance } = require('../../../utils/others');
const conferenceModel = require('../../../models/v1/mapping/conferencesModel');
const conferenceLevelModel = require('../../../models/v1/mapping/conferencesLevelsModel');
const conferenceParticipantModel = require('../../../models/v1/mapping/conferenceParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const fileModel = require('../../../models/v1/mapping/fileModel');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');
const dayjs = require('dayjs');
const upload = multer({ dest: 'uploads/' });

// 导入工具函数
const { updateUserRankings } = require('../../../utils/rankingUtils');

/**
 * 获取会议列表（统一接口）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getConferences = async (req, res) => {
  try {
    const { 
      conferenceName, 
      levelId, 
      holdTimeStart, 
      holdTimeEnd, 
      userId, 
      page = 1, 
      pageSize = 10,
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'all'(全部), 'reviewed'(已审核), 'pending'(待审核), 'rejected'(已拒绝)
      sortField = 'holdTime', // 排序字段，默认按举办时间排序
      sortOrder = 'desc', // 排序方式，默认降序
      query, // 关键词搜索
      isExport = false // 是否导出，如果为true则不分页
    } = req.body;
    
    // 构建查询条件
    const whereCondition = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }
    
    // 会议名称模糊搜索
    if (conferenceName) {
      whereCondition.conferenceName = { [Op.like]: `%${conferenceName}%` };
    }
    
    // 会议级别过滤
    if (levelId) {
      whereCondition.levelId = levelId;
    }
    
    // 举办时间范围过滤
    if (holdTimeStart || holdTimeEnd) {
      whereCondition.holdTime = {};
    if (holdTimeStart) {
        whereCondition.holdTime[Op.gte] = holdTimeStart;
    }
    if (holdTimeEnd) {
        whereCondition.holdTime[Op.lte] = holdTimeEnd;
      }
    }
    
    // 关键词搜索
    if (query) {
      whereCondition[Op.or] = [
        { conferenceName: { [Op.like]: `%${query}%` } },
        { remark: { [Op.like]: `%${query}%` } }
      ];
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("conferences");
    
    // 根据range参数添加时间筛选条件
    if (range === 'in' && timeInterval) {
      // 在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime },
          { [Op.is]: null }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 如果提供了userId，添加用户筛选条件
    if (userId) {
      // 查询用户参与的会议ID
      const participations = await conferenceParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['conferenceId']
      });
      
      const conferenceIds = participations.map(p => p.conferenceId);
      
      // 查询用户作为第一负责人的会议ID
      const firstResponsibleConferences = await conferenceModel.findAll({
        where: { firstResponsibleId: userId },
        attributes: ['id']
      });
      
      const firstResponsibleIds = firstResponsibleConferences.map(c => c.id);
      
      // 合并两种情况的会议ID
      const allConferenceIds = [...new Set([...conferenceIds, ...firstResponsibleIds])];
      
      if (allConferenceIds.length > 0) {
        whereCondition.id = { [Op.in]: allConferenceIds };
      } else {
        // 用户没有参与任何会议，返回空结果
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: {
            list: [],
            pagination: {
              page: pageNum,
              pageSize: pageSizeNum,
              total: 0,
              totalPages: 0
            }
          }
        });
      }
    }
    
    // 查询会议数据 - 如果是导出，不使用分页限制
    const queryOptions = {
      where: whereCondition,
      order: [[sortField, sortOrder]],
      include: [
        {
          model: conferenceLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ]
    };
    
    // 仅在非导出模式下应用分页
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }
    
    const { count, rows } = await conferenceModel.findAndCountAll(queryOptions);
    
    // 处理会议数据并获取参与者信息
    const processedConferences = [];
    
    for (const conference of rows) {
      const conferenceJson = conference.toJSON();
      
      // 查询参与者
      const participants = await conferenceParticipantModel.findAll({
        where: { conferenceId: conference.id },
        include: [
          {
            model: userModel,
            as: 'participant',
            attributes: ['id', 'nickname', 'username', 'studentNumber']
          }
        ]
      });
      
      // 添加参与者信息
      conferenceJson.participants = participants.map(p => p.toJSON());
      
      // 计算用户在此会议中的分配比例（如果提供了userId）
      if (userId) {
        const userParticipation = participants.find(p => p.participantId === userId);
        if (userParticipation) {
          conferenceJson.userAllocationRatio = userParticipation.allocationRatio;
          conferenceJson.isLeader = userParticipation.isLeader === 1;
        } else if (conference.firstResponsibleId === userId) {
          // 如果用户是第一负责人但不在参与者表中
          conferenceJson.isLeader = true;
        }
      }
      
      // 检查是否在统计时间范围内（仅用于前端展示）
      let isInTimeRange = false;
      if (timeInterval && conferenceJson.holdTime) {
        isInTimeRange = isProjectInTimeRange(conferenceJson.holdTime, timeInterval.startTime, timeInterval.endTime);
      }
      
      conferenceJson.isInTimeRange = isInTimeRange;
      
      // 直接添加到处理后的会议列表中，不再根据range筛选
      processedConferences.push(conferenceJson);
    }
    
    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: processedConferences,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取会议列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议列表失败',
      error: error.message
    });
  }
};

/**
 * 获取会议详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getConferenceDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少会议ID',
        data: null
      });
    }
    
    // 查询会议详情
    const conference = await conferenceModel.findByPk(id, {
      include: [
        {
          model: conferenceLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });
    
    if (!conference) {
      return res.status(404).json({
        code: 404,
        message: '未找到会议',
        data: null
      });
    }
    
    // 将会议数据转换为JSON对象
    const conferenceData = conference.toJSON();
    
    // 查询参与者信息
    const participants = await conferenceParticipantModel.findAll({
      where: { conferenceId: id },
      include: [
        {
          model: userModel,
          as: 'participant',
          attributes: ['id', 'nickname', 'username', 'studentNumber']
        }
      ]
    });
    
    // 添加参与者信息
    conferenceData.participants = participants.map(p => p.toJSON());
    
    // 查找参与者表中的负责人（兼容旧逻辑）
    const leader = participants.find(p => p.isLeader === 1);
    if (leader) {
      conferenceData.leader = leader.toJSON();
    }
    
    // 查询会议关联的文件列表
    const files = await fileModel.findAll({
      where: {
        projectId: id,
        relatedId: id,
        relatedType: 'conferences'
      },
      attributes: [
        'id', 
        'fileName', 
        'originalName', 
        'filePath', 
        'fileSize', 
        'mimeType', 
        'extension',
        'uploaderId', 
        'relatedId', 
        'relatedType', 
        'createdAt', 
        'updatedAt'
      ],
      order: [['createdAt', 'DESC']]
    });
    
    // 处理文件信息，添加URL和其他前端需要的信息
    conferenceData.attachments = files.map(file => {
      const fileData = file.toJSON();
      // 构造文件URL
      const filePath = fileData.filePath;
      const url = filePath ? (filePath.startsWith('/') ? filePath : `/${filePath}`) : '';
      
      return {
        id: fileData.id,
        name: fileData.originalName,
        fileName: fileData.fileName,
        size: fileData.fileSize,
        type: fileData.mimeType,
        extension: fileData.extension,
        url: url,
        filePath: fileData.filePath,
        uploadTime: fileData.createdAt
      };
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: conferenceData
    });
  } catch (error) {
    console.error('获取会议详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议详情失败',
      error: error.message
    });
  }
};

/**
 * 创建会议
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createConference = async (req, res) => {
  // 获取数据库连接实例
  let dbSequelize;
  let transactionCompleted = false; // 添加变量跟踪事务状态
  try {
    dbSequelize = getSequelizeInstance(conferenceModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();
  
  try {
    const {
      conferenceName,
      levelId,
      holdTime,
      remark,
      participants,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl    // 接收前端传递的文件路径
    } = req.body;
    
    // 验证必要字段
    if (!conferenceName || !levelId || !holdTime || !participants || !participants.length) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少必要字段',
        data: null
      });
    }
    
    // 验证会议级别是否存在
    const level = await conferenceLevelModel.findByPk(levelId, { transaction });
    if (!level) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到会议级别',
        data: null
      });
    }
    
    // 验证参与者格式
    const hasLeader = participants.some(p => p.isLeader === 1 || p.isLeader === true);
    if (!hasLeader) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '必须指定至少一名负责人',
        data: null
      });
    }
    
    // 验证分配比例总和
    const totalRatio = participants.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
    if (Math.abs(totalRatio - 1) > 0.01) {  // 允许0.01的误差
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '所有参与者的分配比例之和必须等于1',
        data: null
      });
    }
    
    // 验证参与者用户是否存在
    const participantUserIds = participants.map(p => p.participantId);
    const users = await userModel.findAll({
      where: { id: { [Op.in]: participantUserIds } },
      transaction
    });
    
    if (users.length !== participantUserIds.length) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '存在无效的参与者ID',
        data: null
      });
    }
    
    // 获取第一负责人ID
    const leader = participants.find(p => p.isLeader === 1 || p.isLeader === true);
    const firstResponsibleId = leader ? leader.participantId : null;
    
    if (!firstResponsibleId) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '无法确定第一负责人',
        data: null
      });
    }
    
    // 创建会议
    const conferenceId = uuidv4();
    const conference = await conferenceModel.create({
      id: conferenceId,
      conferenceName,
      levelId,
      holdTime,
      remark,
      firstResponsibleId,
      ifReviewed: null, // 默认未审核
      attachmentUrl: null, // 初始设置为null，稍后更新
      createdAt: new Date(),
      updatedAt: new Date()
    }, { transaction });
    
    // 创建参与者记录
    const participantPromises = participants.map(p => {
      return conferenceParticipantModel.create({
        id: uuidv4(),
        conferenceId: conference.id,
        participantId: p.participantId,
        allocationRatio: parseFloat(p.allocationRatio || 0),
        isLeader: p.isLeader === true || p.isLeader === 1 ? 1 : 0
      }, { transaction });
    });
    
    await Promise.all(participantPromises);
    
    // 处理关联文件 - 使用前端传递的文件ID而非重新创建文件记录
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl === 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
      
      // 如果有文件ID，关联到会议
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            projectId: conference.id, // 设置文件的conferenceId为新创建的会议ID
            relatedId: conference.id,
            relatedType: 'conferences' // 使用conferences作为relatedType
          };
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath; // 使用filePath字段，这是文件模型中的正确字段名
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );

          processedFileIds.push(fileId);
        }
      }
    } else if (req.files && req.files.length > 0) {
      // 处理通过multer上传的文件
      const uploadedFiles = req.files;
      
      for (const file of uploadedFiles) {
        // 创建文件记录
        const fileRecord = await fileModel.create({
          id: uuidv4(),
          originalName: file.originalname,
          filePath: file.path,
          mimeType: file.mimetype,
          size: file.size,
          projectId: conference.id,
          relatedId: conference.id,
          relatedType: 'conferences'
        }, { transaction });
        
        processedFileIds.push(fileRecord.id);
      }
    }
    
    // 提交事务
    await transaction.commit();
    transactionCompleted = true; // 标记事务已完成
    
    // 事务提交成功后，异步移动文件到指定的会议目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'conferences'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${conference.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 新增变量跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: conference.id,
                relatedId: conference.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新会议的attachmentUrl为会议文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的会议文件夹路径，确保不包含文件名
            const conferenceFolderPath = `uploads\\conferences\\${conference.id}\\`;
            
            await conference.update({
              attachmentUrl: conferenceFolderPath
            });
            console.log(`已更新会议 ${conference.id} 的attachmentUrl为: ${conferenceFolderPath}`);
            
          } catch (updateError) {
            console.error('更新会议attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响会议创建的返回结果，仅记录错误
        console.error('移动文件到会议目录失败:', moveError);
      }
    }
    
    // 查询创建的会议详情
    try {
      const createdConference = await conferenceModel.findByPk(conferenceId, {
        include: [
          {
            model: conferenceLevelModel,
            as: 'level',
            attributes: ['id', 'levelName', 'score']
          },
          {
            model: conferenceParticipantModel,
            as: 'participants',
            include: [
              {
                model: userModel,
                as: 'participant',
                attributes: ['id', 'nickname', 'username', 'studentNumber']
              }
            ]
          }
        ]
      });
      
      // 查询关联的文件
      const files = await fileModel.findAll({
        where: {
          projectId: conferenceId,
          relatedId: conferenceId,
          relatedType: 'conferences'
        }
      });
      
      // 添加文件信息到响应数据
      const responseData = createdConference.toJSON();
      responseData.attachments = files.map(file => file.toJSON());
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
        data: responseData
      });
    } catch (queryError) {
      console.error('查询会议详情失败:', queryError);
      // 即使查询详情失败，会议仍然已创建成功
      return res.status(201).json({
        code: 200,
        message: '创建成功，但获取详情失败',
        data: {
          id: conferenceId
        },
        error: queryError.message
      });
    }
  } catch (error) {
    console.log("error====",error);
    
    // 只有在事务未完成时才尝试回滚
    if (!transactionCompleted) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        console.error('回滚事务失败:', rollbackError);
      }
    }
    
    console.error('创建会议失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建会议失败',
      error: error.message
    });
  }
};

/**
 * 更新会议
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateConference = async (req, res) => {
  console.log('开始更新会议，记录更新过程');
  // 获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(conferenceModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();
  
  try {
    const { id } = req.params;
    const {
      conferenceName,
      levelId,
      holdTime,
      remark,
      participants,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl    // 接收前端传递的文件路径
    } = req.body;
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少会议ID',
        data: null
      });
    }
    
    // 查询会议是否存在
    const conference = await conferenceModel.findByPk(id, {
      include: [
        {
          model: conferenceParticipantModel,
          as: 'participants',
          required: false
        },
        {
          model: conferenceLevelModel,
          as: 'level',
          required: false
        }
      ],
      transaction
    });
    
    if (!conference) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到会议',
        data: null
      });
    }
    
    // 权限检查：管理员或者会议的参与者可以编辑
    const userInfo = await getUserInfoFromRequest(req);
    const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');
    
    // 检查是否是会议的第一负责人
    const isFirstResponsible = conference.firstResponsibleId === userInfo.id;
      
    // 检查用户是否是会议参与者
    const isParticipant = await conferenceParticipantModel.findOne({
          where: {
            conferenceId: id,
        participantId: userInfo.id
      },
      transaction
    });
    
    if (!isAdmin && !isFirstResponsible && !isParticipant) {
      await transaction.rollback();
          return res.status(403).json({
            code: 403,
        message: '您没有权限编辑该会议',
            data: null
          });
        }
    
    // 如果更新级别，确认级别存在
    if (levelId && levelId !== conference.levelId) {
      const level = await conferenceLevelModel.findByPk(levelId, { transaction });
      if (!level) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '未找到会议级别',
          data: null
        });
      }
    }
    
    // 如果更新参与者，验证参与者格式
    if (participants && participants.length > 0) {
      // 验证参与者格式
      const hasLeader = participants.some(p => p.isLeader === 1 || p.isLeader === true);
      if (!hasLeader) {
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '必须指定至少一名负责人',
          data: null
        });
      }
      
      // 验证分配比例总和
      const totalRatio = participants.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
      if (Math.abs(totalRatio - 1) > 0.01) {  // 允许0.01的误差
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '所有参与者的分配比例之和必须等于1',
          data: null
        });
      }
      
      // 验证参与者用户是否存在
      const participantUserIds = participants.map(p => p.participantId);
      const users = await userModel.findAll({
        where: { id: { [Op.in]: participantUserIds } },
        transaction
      });
      
      if (users.length !== participantUserIds.length) {
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '存在无效的参与者ID',
          data: null
        });
      }
    }
    
    // 如果会议已审核，需要更新用户排名
    if (conference.ifReviewer == 1) {
      try {
        console.log('开始更新用户排名数据，会议ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("conferences");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断会议变更前后是否在时间区间内（使用holdTime判断）
        const oldHoldTime = conference.holdTime;
        const newHoldTime = holdTime || conference.holdTime;
        console.log('会议举办时间 - 原始:', oldHoldTime, '新:', newHoldTime);
        
        const wasInTimeRange = timeInterval ? 
          isDateInTimeRange(oldHoldTime, timeInterval.startTime, timeInterval.endTime) : 
          false;
          
        const isInTimeRange = timeInterval ? 
          isDateInTimeRange(newHoldTime, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('会议时间范围状态 - 原始:', wasInTimeRange ? '在范围内' : '不在范围内', 
                   '变更后:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表（针对减分操作）
        let oldRankingTables = [];
        if (wasInTimeRange) {
          oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        // 确定需要更新的排名表（针对加分操作）
        let newRankingTables = [];
        if (isInTimeRange) {
          newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('排名表 - 原始:', oldRankingTables, '新:', newRankingTables);
        
        // 获取旧的级别分数
        const oldBaseScore = conference.level ? conference.level.score : 0;
        console.log('原始级别分数:', oldBaseScore);
        
        // 获取新的级别分数
        let newBaseScore = oldBaseScore;
        if (levelId && levelId !== conference.levelId) {
          const newLevel = await conferenceLevelModel.findByPk(levelId, { transaction });
          if (newLevel) {
            newBaseScore = newLevel.score || 0;
          }
        }
        console.log('新级别分数:', newBaseScore);
        
        // 获取旧的参与者名单
        const oldParticipants = conference.participants || [];
        console.log('原始参与者数量:', oldParticipants.length);
        
        // 准备新的参与者列表
        let newParticipants = [];
        if (participants && participants.length > 0) {
          newParticipants = participants.map(p => ({
            participantId: p.participantId,
            allocationRatio: parseFloat(p.allocationRatio || 0),
            isLeader: p.isLeader === 1 || p.isLeader === true
          }));
        }
        console.log('新参与者数量:', newParticipants.length);
        
        // 从原始参与者中找出要删除的参与者 - 他们在旧列表中但不在新列表中
        const oldUserIds = oldParticipants.map(p => p.participantId);
        const newUserIds = newParticipants.map(p => p.participantId);
        
        // 找出要删除的参与者
        const deletedUserIds = oldUserIds.filter(id => !newUserIds.includes(id));
        console.log('要删除的参与者:', deletedUserIds);
        
        // 找出保留的参与者
        const keptUserIds = oldUserIds.filter(id => newUserIds.includes(id));
        console.log('保留的参与者:', keptUserIds);
        
        // 找出新增的参与者
        const addedUserIds = newUserIds.filter(id => !oldUserIds.includes(id));
        console.log('新增的参与者:', addedUserIds);
        
        // 1. 处理要删除的参与者 - 减少项目数量和分数
        if (deletedUserIds.length > 0) {
          const deletedParticipants = oldParticipants.filter(p => deletedUserIds.includes(p.participantId));
          console.log('被删除的参与者完整数据:', JSON.stringify(deletedParticipants));
          
          const deletedIds = [];
          const deletedRatios = [];
          const deletedScores = [];
          const countDeltas = []; // 固定值为1的数组表示每人减少1个项目
          
          for (const participant of deletedParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            deletedIds.push(userId);
            deletedRatios.push(ratio);
            deletedScores.push(participantScore);
            countDeltas.push(1); // 每个被删除的参与者项目数-1
          }
          
          console.log('被删除参与者的排名更新数据:', {
            userIds: deletedIds,
            countDeltas: countDeltas,
            scores: deletedScores
          });
          
          // 对每个排名表执行减分操作
          for (const table of oldRankingTables) {
            console.log(`为被删除的参与者更新排名表 ${table}`);
            await updateUserRankings(
              deletedIds,
              table,
              'conferences',
              countDeltas, // 使用固定值1表示项目计数-1
              deletedScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去被删除参与者的分数和项目数');
        }
        
        // 2. 处理保留但分配比例变化的参与者 - 先减去原有分数，后面再加上新分数
        if (keptUserIds.length > 0) {
          const keptOldParticipants = oldParticipants.filter(p => keptUserIds.includes(p.participantId));
          console.log('保留的参与者原始数据:', JSON.stringify(keptOldParticipants));
          
          const keptIds = [];
          const keptRatios = [];
          const keptScores = [];
          
          for (const participant of keptOldParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            keptIds.push(userId);
            keptRatios.push(ratio);
            keptScores.push(participantScore);
          }
          
          console.log('保留参与者的减分数据:', {
            userIds: keptIds,
            scores: keptScores
          });
          
          // 减去原有分数（但不减少项目计数）
          for (const table of oldRankingTables) {
            console.log(`为保留的参与者减去原有分数：${table}`);
            // 传递0表示不改变项目计数，只改变分数
            const zeroCounts = Array(keptIds.length).fill(0);
            await updateUserRankings(
              keptIds,
              table,
              'conferences',
              zeroCounts, // 项目计数不变
              keptScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去保留参与者的原有分数');
        }
        
        // 3. 处理所有新参与者名单（包括保留的和新增的）- 增加分数，对新增的也增加项目数
        if (newParticipants.length > 0) {
          console.log('新参与者完整数据:', JSON.stringify(newParticipants));
          
          const allNewIds = [];
          const allNewRatios = [];
          const allNewScores = [];
          const allCountDeltas = []; // 对新增参与者设为1，对保留的参与者设为0
          
          for (const participant of newParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = newBaseScore * ratio;
            
            allNewIds.push(userId);
            allNewRatios.push(ratio);
            allNewScores.push(participantScore);
            
            // 对新增参与者项目数+1，对保留的参与者项目数不变
            if (addedUserIds.includes(userId)) {
              allCountDeltas.push(1);
            } else {
              allCountDeltas.push(0);
            }
          }
          
          console.log('所有新参与者的加分数据:', {
            userIds: allNewIds,
            countDeltas: allCountDeltas,
            scores: allNewScores
          });
          
          // 为所有参与者添加分数，但只为新增参与者增加项目计数
          for (const table of newRankingTables) {
            console.log(`为所有新参与者更新排名表 ${table}`);
            await updateUserRankings(
              allNewIds,
              table,
              'conferences',
              allCountDeltas,
              allNewScores,
              transaction,
              "add" // 加分操作
            );
          }
          console.log('成功为参与者更新排名表');
        }
      } catch (error) {
        console.error('更新用户排名时出错:', error);
        await transaction.rollback();
        return res.status(500).json({
          code: 500,
          message: '更新用户排名时出错',
          error: error.message
        });
      }
    }
    
    // 更新会议基本信息
    const updateData = {};
    if (conferenceName !== undefined) updateData.conferenceName = conferenceName;
    if (levelId !== undefined) updateData.levelId = levelId;
    if (holdTime !== undefined) updateData.holdTime = holdTime;
    if (remark !== undefined) updateData.remark = remark;
    // 设置标准化的附件文件夹路径
    updateData.attachmentUrl = `uploads\\conferences\\${id}\\`;
    updateData.updatedAt = new Date();
      
      // 获取第一负责人ID
    if (participants && participants.length > 0) {
      const leader = participants.find(p => p.isLeader === 1 || p.isLeader === true);
      if (leader) {
        updateData.firstResponsibleId = leader.participantId;
      }
    }
    
    await conference.update(updateData, { transaction });
    
    // 如果更新参与者
    if (participants && participants.length > 0) {
      // 删除原有参与者
      await conferenceParticipantModel.destroy({
        where: { conferenceId: id },
        transaction
      });
      
      // 创建新参与者
      await Promise.all(participants.map(async (p) => {
        return await conferenceParticipantModel.create({
          id: uuidv4(),
          conferenceId: id,
          participantId: p.participantId,
          allocationRatio: parseFloat(p.allocationRatio || 0),
          isLeader: p.isLeader === true || p.isLeader === 1 ? 1 : 0
        }, { transaction });
      }));
    }
    
    // 处理文件关联
    let processedFileIds = [];
    let fileIdArray = [];
    
    // 解析文件ID数组
    if (fileIds) {
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
    } else {
      fileIdArray = [];
    }
    
    // 解析attachmentUrl
    let attachmentUrlArray = [];
    if (attachmentUrl) {
      if (typeof attachmentUrl === 'string') {
        try {
          attachmentUrlArray = JSON.parse(attachmentUrl);
        } catch (error) {
          console.error('解析文件路径出错:', error);
          attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
        }
      } else if (Array.isArray(attachmentUrl)) {
        attachmentUrlArray = attachmentUrl;
      }
    }
    
    // 获取当前会议关联的所有未删除文件
    const existingFiles = await fileModel.findAll({
      where: {
        projectId: id,
        relatedType: 'conferences',
        relatedId: id
      },
      attributes: ['id'],
      transaction
    });
    
    // 找出需要删除的文件ID（存在于现有文件但不在fileIds中的文件）
    if (existingFiles.length > 0) {
      const existingFileIds = existingFiles.map(file => file.id);
      const filesToDelete = existingFileIds.filter(fileId => !fileIdArray.includes(fileId));
      
      // 删除文件记录和物理文件
      if (filesToDelete.length > 0) {
        console.log(`将删除以下文件: ${filesToDelete.join(', ')}`);
        
        // 首先获取这些文件的完整信息，包括filePath
        const filesInfo = await fileModel.findAll({
          where: { 
            id: { [Op.in]: filesToDelete }
          },
          transaction
        });
        
        // 删除物理文件（记录路径以便后面删除）
        const physicalFilesToDelete = filesInfo.map(file => file.filePath).filter(Boolean);
        
        // 从数据库中删除文件记录
        await fileModel.destroy({
          where: { 
            id: { [Op.in]: filesToDelete }
          },
          transaction
        });
        
        // 事务提交后再删除物理文件
        process.nextTick(() => {
          physicalFilesToDelete.forEach(filePath => {
            try {
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                console.log(`成功删除物理文件: ${filePath}`);
              }
            } catch (err) {
              console.error(`删除物理文件失败: ${filePath}`, err);
            }
          });
        });
      }
    }
    
    // 处理新上传的文件
    if (attachmentUrlArray.length > 0) {
      // 确保上传目录存在
      const uploadDir = `uploads/conferences/${id}`;
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      
      // 处理每个上传的文件
      for (const fileUrl of attachmentUrlArray) {
        try {
          // 检查是否已经有关联的文件记录
          const existingFile = await fileModel.findOne({
            where: { 
              filePath: fileUrl,
              relatedType: 'conferences',
              relatedId: id
            },
            transaction
          });
          
          if (!existingFile) {
            // 解析文件名
            const fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
            
            // 创建新的文件记录
            const fileRecord = await fileModel.create({
              id: uuidv4(),
              fileName: fileName,
              filePath: fileUrl,
              relatedType: 'conferences',
              relatedId: id,
              projectId: id,
              uploadUserId: userInfo.id,
              uploadTime: new Date()
            }, { transaction });
            
            console.log(`成功创建文件记录: ${fileName}`);
            processedFileIds.push(fileRecord.id);
          } else {
            console.log(`文件已存在: ${fileUrl}`);
            processedFileIds.push(existingFile.id);
          }
        } catch (fileError) {
          console.error(`处理文件失败: ${fileUrl}`, fileError);
          // 继续处理下一个文件
        }
      }
    }
    
    // 清理会议附件上传临时目录
    try {
      const tempDir = `uploads/temp/conferences/${id}`;
      if (fs.existsSync(tempDir)) {
        fs.rmdir(tempDir, { recursive: true }, (err) => {
          if (err) {
            console.error(`清理临时目录失败: ${tempDir}`, err);
          } else {
            console.log(`成功清理临时目录: ${tempDir}`);
          }
        });
      }
    } catch (tempCleanError) {
      console.warn('清理临时目录出错:', tempCleanError);
    }
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: {
        id: id,
        processedFileIds: processedFileIds
      }
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('更新会议失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新会议失败',
      error: error.message
    });
  }
};

/**
 * 删除会议
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteConference = async (req, res) => {
  console.log('开始删除会议，记录删除过程');
  // 获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(conferenceModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();
  
  try {
    const { id } = req.params;
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少会议ID',
        data: null
      });
    }
    
    // 查询会议是否存在
    const conference = await conferenceModel.findByPk(id, { 
      include: [
        {
          model: conferenceParticipantModel,
          as: 'participants',
          required: false
        },
        {
          model: conferenceLevelModel,
          as: 'level',
          required: false
        }
      ],
      transaction 
    });
    
    if (!conference) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到会议',
        data: null
      });
    }
    
    // 权限检查：管理员或者会议的参与者可以删除
    const userInfo = await getUserInfoFromRequest(req);
    const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');
    
    // 检查是否是会议的第一负责人
    const isFirstResponsible = conference.firstResponsibleId === userInfo.id;
      
    // 检查用户是否是会议参与者
    const isParticipant = await conferenceParticipantModel.findOne({
          where: {
            conferenceId: id,
        participantId: userInfo.id,
            isLeader: 1
      },
      transaction
        });
        
    if (!isAdmin && !isFirstResponsible && !isParticipant) {
      await transaction.rollback();
          return res.status(403).json({
            code: 403,
        message: '您没有权限删除该会议',
            data: null
          });
        }

    // 如果会议已审核，需要更新用户排名
    if (conference.ifReviewer == 1) {
      try {
        console.log('开始处理删除会议的排名更新，会议ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("conferences");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断会议是否在时间区间内（使用holdTime判断）
        const isInTimeRange = timeInterval ? 
          isDateInTimeRange(conference.holdTime, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('会议时间范围状态:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('需要更新的排名表:', rankingTables);
        
        // 获取会议的级别分数
        const baseScore = conference.level ? conference.level.score : 0;
        console.log('会议级别分数:', baseScore);
        
        // 获取会议的所有参与者
        const participants = conference.participants || [];
        console.log('会议参与者数量:', participants.length);
        
        // 如果有参与者，批量处理减分操作
        if (participants.length > 0) {
          // 准备批量更新的数据
          const userIds = [];
          const scores = [];
          
          // 收集所有参与者数据
          for (const participant of participants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = baseScore * ratio;
            
            userIds.push(userId);
            scores.push(participantScore);
          }
          
          console.log('删除会议的参与者数据:', {
            userIds: userIds,
            scores: scores
          });
          
          // 创建固定值为1的计数数组，表示每个用户项目数-1
          const countDeltas = Array(userIds.length).fill(1);
          
          // 对每个排名表执行减分操作（一次性批量处理所有参与者）
          for (const table of rankingTables) {
            console.log(`为被删除会议的所有参与者更新排名表 ${table}`);
            await updateUserRankings(
              userIds,
              table,
              'conferences',
              countDeltas, // 使用固定值1表示会议计数-1
              scores, // 使用参与者分数数组
              transaction,
              "subtract" // 减分操作
            );
          }
          
          console.log('成功从排名表中减去会议参与者的分数和会议数');
        }
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 收集会议文件夹路径
    let conferenceFolderPaths = new Set();
    
    // 处理会议附件 - 检查是否有附件模型并进行处理
    try {
      const conferenceFiles = await fileModel.findAll({
        where: { 
          projectId: id,
          relatedId: id,
          relatedType: 'conferences'
        },
        transaction
      });
      
      if (conferenceFiles && conferenceFiles.length > 0) {
        console.log(`找到${conferenceFiles.length}个与会议关联的文件记录`);
        
        // 从文件路径中提取会议文件夹路径
        for (const file of conferenceFiles) {
          if (file.filePath) {
            // 从filePath中提取会议文件夹路径
            const folderPath = file.filePath.substring(0, file.filePath.lastIndexOf('/'));
            if (folderPath.includes('conferences')) {
              conferenceFolderPaths.add(folderPath);
            }
          }
          
          // 将文件标记为已删除
          await file.destroy({ transaction });
        }
      } else {
        console.log('未找到与会议关联的文件记录');
      }
    } catch (attachmentError) {
      console.warn('处理会议文件时出错:', attachmentError);
      // 继续执行，不因附件删除失败而中断整个操作
    }
    
    // 删除会议参与者
    await conferenceParticipantModel.destroy({
      where: { conferenceId: id },
      transaction
    });
    
    // 标记会议为已删除，而不是物理删除
    await conference.destroy({ transaction });
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，执行文件系统删除操作（这些操作不能回滚，所以放在事务之外）
    try {
      // 直接使用会议ID构造会议文件夹路径
      const conferenceFolderPath = `uploads/conferences/${id}`;
      console.log(`尝试删除会议文件夹: ${conferenceFolderPath}`);
      
      // 如果fileController中有deleteDirectoryUtil方法，使用它删除文件夹
      if (typeof fileController !== 'undefined' && typeof fileController.deleteDirectoryUtil === 'function') {
        await fileController.deleteDirectoryUtil(conferenceFolderPath);
      } else {
        // 否则使用fs模块直接删除
        if (fs.existsSync(conferenceFolderPath)) {
          fs.rmdirSync(conferenceFolderPath, { recursive: true });
          console.log(`成功删除会议文件夹: ${conferenceFolderPath}`);
        }
      }
    } catch (fsError) {
      console.warn('删除文件系统中的文件时出错:', fsError);
      // 数据库事务已提交成功，文件系统操作失败不影响API返回结果
    }
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('删除会议失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除会议失败',
      error: error.message
    });
  }
};

/**
 * 导出会议数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportConferences = async (req, res) => {
  try {
    const { 
      conferenceName, 
      levelId, 
      holdTimeStart, 
      holdTimeEnd,
      userId,
      range = 'all',
      reviewStatus = 'all',
      fileName = '会议数据.xlsx'
    } = req.query;
    
    // 构建查询条件
    const whereCondition = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }
    
    if (conferenceName) {
      whereCondition.conferenceName = { [Op.like]: `%${conferenceName}%` };
    }
    
    if (levelId) {
      whereCondition.levelId = levelId;
    }
    
    // 举办时间范围过滤
    if (holdTimeStart || holdTimeEnd) {
      whereCondition.holdTime = {};
    if (holdTimeStart) {
        whereCondition.holdTime[Op.gte] = holdTimeStart;
    }
    if (holdTimeEnd) {
        whereCondition.holdTime[Op.lte] = holdTimeEnd;
      }
    }
    
    // 如果提供了userId，先获取用户参与的会议ID
    if (userId) {
      // 查询用户参与的会议
      const participations = await conferenceParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['conferenceId']
      });
      
      // 查询用户作为第一负责人的会议
      const firstResponsibleConferences = await conferenceModel.findAll({
        where: { firstResponsibleId: userId },
        attributes: ['id']
      });
      
      // 合并两种情况的会议ID
      const participationIds = participations.map(p => p.conferenceId);
      const firstResponsibleIds = firstResponsibleConferences.map(c => c.id);
      const conferenceIds = [...new Set([...participationIds, ...firstResponsibleIds])];
      
      if (conferenceIds.length === 0) {
        // 该用户没有参与会议
        return res.status(200).json({
          code: 200,
          message: '导出成功',
          data: []
        });
      }
      
      whereCondition.id = { [Op.in]: conferenceIds };
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("conferences");
    
    // 根据range参数添加时间筛选条件
    if (range === 'in' && timeInterval) {
      // 在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime },
          { [Op.is]: null }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    // 查询数据
    const conferences = await conferenceModel.findAll({
      where: whereCondition,
      order: [['holdTime', 'DESC']],
      include: [
        {
          model: conferenceLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });
    
    // 处理会议数据
    const processedConferences = [];
    
    for (const conference of conferences) {
      const conferenceJson = conference.toJSON();
      
      // 检查是否在统计时间范围内（仅用于前端显示）
      let isInTimeRange = false;
      if (timeInterval && conferenceJson.holdTime) {
        isInTimeRange = isProjectInTimeRange(conferenceJson.holdTime, timeInterval.startTime, timeInterval.endTime);
      }
      
      conferenceJson.isInTimeRange = isInTimeRange;
      
      // 直接添加到处理后的会议列表中，不再根据range筛选
      processedConferences.push(conferenceJson);
    }
    
    // 为每个会议获取参与者信息
    for (const conference of processedConferences) {
      // 查询参与者
      const participants = await conferenceParticipantModel.findAll({
        where: { conferenceId: conference.id },
        include: [
          {
            model: userModel,
            as: 'participant',
            attributes: ['id', 'nickname', 'username', 'studentNumber']
          }
        ]
      });
      
      // 获取所有参与者名单（包括分配比例）
      const participantNames = [];
      
      participants.forEach(p => {
        const participantJson = p.toJSON();
        const userName = participantJson.participant?.nickname || participantJson.participant?.username || '';
        const ratio = participantJson.allocationRatio || 0;
        const formattedRatio = (ratio * 100).toFixed(0);
        const isLeader = participantJson.isLeader === 1 ? '[负责人]' : '';
        
        if (userName) {
          participantNames.push(`${userName}${isLeader}(${formattedRatio}%)`);
        }
      });
      
      conference.participantNames = participantNames.join(', ');
    }
    
    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('会议列表');
    
    // 定义表头
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: '会议名称', key: 'conferenceName', width: 30 },
      { header: '会议级别', key: 'levelName', width: 15 },
      { header: '举办时间', key: 'holdTime', width: 15 },
      { header: '备注', key: 'remark', width: 30 },
      { header: '第一负责人', key: 'firstResponsible', width: 15 },
      { header: '参与者', key: 'participants', width: 30 },
      { header: '审核人', key: 'reviewer', width: 15 },
      { header: '是否审核', key: 'ifReviewed', width: 10 },
      { header: '创建时间', key: 'createdAt', width: 20 },
      { header: '更新时间', key: 'updatedAt', width: 20 }
    ];
      
      // 添加数据行
    processedConferences.forEach(conference => {
      worksheet.addRow({
        id: conference.id,
        conferenceName: conference.conferenceName,
        levelName: conference.level?.levelName || '',
        holdTime: conference.holdTime,
        remark: conference.remark || '',
        firstResponsible: conference.firstResponsible?.nickname || conference.firstResponsible?.username || '',
        participants: conference.participantNames,
        reviewer: conference.reviewer?.nickname || conference.reviewer?.username || '',
        ifReviewed: conference.ifReviewer === 1 ? '已审核' : (conference.ifReviewer === 0 ? '已拒绝' : '待审核'),
        createdAt: conference.createdAt,
        updatedAt: conference.updatedAt
      });
    });
    
    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(fileName)}`);
    
    // 将工作簿写入响应
    await workbook.xlsx.write(res);
    
    // 结束响应
    res.end();
  } catch (error) {
    console.error('导出会议数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出会议数据失败',
      error: error.message
    });
  }
};

/**
 * 导入会议数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importConferences = async (req, res) => {
  // 获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(conferenceModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();
  
  try {
    // 检查是否存在文件
    if (!req.file) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '未上传文件',
        data: null
      });
    }
    
    // 读取上传的Excel文件
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(req.file.path);
    const worksheet = workbook.getWorksheet(1);
    
    // 准备结果统计
    const result = {
      total: 0,
      success: 0,
      failed: 0,
      errors: []
    };
    
    // 获取表头映射
    const headers = {};
    worksheet.getRow(1).eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value;
    });
    
    // 处理每一行数据
    const promises = [];
    
    worksheet.eachRow((row, rowNumber) => {
      // 跳过表头
      if (rowNumber === 1) return;
      
      result.total++;
      
      const processRow = async () => {
        try {
          const conferenceData = {};
          const participantsData = [];
          
          row.eachCell((cell, colNumber) => {
            const fieldName = headers[colNumber];
            if (fieldName) {
              // 根据字段名称设置对应的数据
              switch(fieldName) {
                case '会议名称':
                  conferenceData.conferenceName = cell.value;
                  break;
                case '会议级别':
                  // 需要查询会议级别ID
                  conferenceData.levelName = cell.value;
                  break;
                case '举办时间':
                  conferenceData.holdTime = cell.value;
                  break;
                case '备注':
                  conferenceData.remark = cell.value;
                  break;
                case '第一负责人':
                  conferenceData.firstResponsible = cell.value;
                  break;
                case '负责人': // 兼容旧格式
                  if (!conferenceData.firstResponsible) {
                    conferenceData.firstResponsible = cell.value;
                  }
                  conferenceData.leader = cell.value;
                  break;
                case '参与者':
                  conferenceData.participants = cell.value;
                  break;
                default:
                  break;
              }
            }
          });
          
          // 验证必填字段
          if (!conferenceData.conferenceName) {
            throw new Error('会议名称不能为空');
          }
          
          if (!conferenceData.levelName) {
            throw new Error('会议级别不能为空');
          }
          
          if (!conferenceData.holdTime) {
            throw new Error('举办时间不能为空');
          }
          
          const firstResponsibleName = conferenceData.firstResponsible || conferenceData.leader;
          if (!firstResponsibleName) {
            throw new Error('第一负责人不能为空');
          }
          
          // 查询会议级别ID
          const level = await conferenceLevelModel.findOne({
            where: { levelName: conferenceData.levelName },
            transaction
          });
          
          if (!level) {
            throw new Error(`未找到会议级别: ${conferenceData.levelName}`);
          }
          
          conferenceData.levelId = level.id;
          
          // 查询第一负责人信息
          const firstResponsible = await userModel.findOne({
            where: {
              [Op.or]: [
                { nickname: firstResponsibleName },
                { username: firstResponsibleName }
              ]
            },
            transaction
          });
          
          if (!firstResponsible) {
            throw new Error(`未找到第一负责人: ${firstResponsibleName}`);
          }
          
          conferenceData.firstResponsibleId = firstResponsible.id;
          
          // 处理参与者
          if (conferenceData.participants) {
            const participantNames = conferenceData.participants.split(',').map(name => name.trim());
            const participantUsers = await userModel.findAll({
              where: {
                [Op.or]: [
                  { nickname: { [Op.in]: participantNames } },
                  { username: { [Op.in]: participantNames } }
                ]
              },
              transaction
            });
            
            if (participantUsers.length === 0 && participantNames.length > 0) {
              throw new Error(`未找到参与者: ${conferenceData.participants}`);
            }
            
            // 默认分配比例，第一负责人0.6，其他均分0.4
            const leaderRatio = 0.6;
            const otherParticipantsCount = participantUsers.length;
            const otherRatio = otherParticipantsCount > 0 ? (1 - leaderRatio) / otherParticipantsCount : 0;
            
            // 添加第一负责人到参与者列表（如果不在列表中）
            const firstResponsibleInParticipants = participantUsers.some(u => u.id === firstResponsible.id);
            
            if (!firstResponsibleInParticipants) {
              participantsData.push({
                participantId: firstResponsible.id,
                allocationRatio: leaderRatio,
                isLeader: 1
              });
              
              // 其他参与者均分剩余的分配比例
              participantUsers.forEach(user => {
                participantsData.push({
                  participantId: user.id,
                  allocationRatio: otherRatio,
                  isLeader: 0
                });
              });
            } else {
              // 第一负责人已在参与者列表中
              participantUsers.forEach(user => {
                participantsData.push({
                  participantId: user.id,
                  allocationRatio: user.id === firstResponsible.id ? leaderRatio : otherRatio,
                  isLeader: user.id === firstResponsible.id ? 1 : 0
                });
              });
            }
          } else {
            // 只有第一负责人
            participantsData.push({
              participantId: firstResponsible.id,
              allocationRatio: 1,
              isLeader: 1
            });
          }
          
          // 创建会议记录
          const conferenceId = uuidv4();
          const conference = await conferenceModel.create({
            id: conferenceId,
            conferenceName: conferenceData.conferenceName,
            levelId: conferenceData.levelId,
            holdTime: conferenceData.holdTime,
            remark: conferenceData.remark,
            firstResponsibleId: conferenceData.firstResponsibleId,
            ifReviewed: 0, // 默认未审核
            createdAt: new Date(),
            updatedAt: new Date()
          }, { transaction });
          
          // 创建参与者记录
          const participantPromises = participantsData.map(p => {
            return conferenceParticipantModel.create({
              id: uuidv4(),
              conferenceId: conference.id,
              participantId: p.participantId,
              allocationRatio: p.allocationRatio,
              isLeader: p.isLeader
            }, { transaction });
          });
          
          await Promise.all(participantPromises);
          
          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            row: rowNumber,
            message: error.message
          });
        }
      };
      
      promises.push(processRow());
    });
    
    // 等待所有处理完成
    await Promise.all(promises);
    
    // 提交事务
    await transaction.commit();
    
    // 删除临时文件
    fs.unlinkSync(req.file.path);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: result
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('导入会议数据失败:', error);
    
    // 如果文件存在，删除临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    return res.status(500).json({
      code: 500,
      message: '导入会议数据失败',
      error: error.message
    });
  }
};

/**
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isDateInTimeRange(dateStr, startDate, endDate) {
  if (!dateStr || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const dateObj = new Date(dateStr);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 日期必须在开始日期和结束日期之间
  return dateObj >= startDateObj && dateObj <= endDateObj;
}

/**
 * 获取会议时间分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTimeDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;
    
    // 构建查询条件
    let whereCondition = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }
    
    let conferenceIds = [];
    
    // 如果提供了userId，只统计该用户参与的会议
    if (userId) {
      // 查询用户参与的会议
      const participations = await conferenceParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['conferenceId']
      });
      
      // 查询用户作为第一负责人的会议
      const firstResponsibleConferences = await conferenceModel.findAll({
        where: { firstResponsibleId: userId },
        attributes: ['id']
      });
      
      // 合并两种情况的会议ID
      const participationIds = participations.map(p => p.conferenceId);
      const firstResponsibleIds = firstResponsibleConferences.map(c => c.id);
      conferenceIds = [...new Set([...participationIds, ...firstResponsibleIds])];
      
      if (conferenceIds.length === 0) {
        // 该用户没有参与会议
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: { months: [], data: [] }
        });
      }
      
      whereCondition.id = { [Op.in]: conferenceIds };
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("conferences");
    
    // 根据range参数添加时间筛选条件
    if (range === 'in' && timeInterval) {
      // 在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime },
          { [Op.is]: null }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    // 查询所有会议
    const conferences = await conferenceModel.findAll({
      where: whereCondition,
      order: [['holdTime', 'DESC']]
    });
    
    // 初始化时间数据
    const timeData = {};
    
    // 统计会议时间分布
    conferences.forEach(conference => {
      const conferenceJson = conference.toJSON();
      
      if (conferenceJson.holdTime) {
        try {
          // 使用holdTime作为时间点
          const holdTime = new Date(conferenceJson.holdTime);
          
          if (holdTime) {
            // 检查会议是否在时间范围内（仅用于前端显示）
            const isInRange = timeInterval && conferenceJson.holdTime ? 
              isProjectInTimeRange(conferenceJson.holdTime, timeInterval.startTime, timeInterval.endTime) : 
              false;
              
            conferenceJson.isInTimeRange = isInRange;
            
            // 按年月分组，格式化为YYYY-MM
            const yearMonth = dayjs(holdTime).format('YYYY-MM');
            timeData[yearMonth] = (timeData[yearMonth] || 0) + 1;
          }
        } catch (e) {
          console.error('处理时间数据错误:', e);
        }
      }
    });
    
    // 按时间排序
    const sortedMonths = Object.keys(timeData).sort();
    
    // 构建前端所需的格式
    const result = {
      months: sortedMonths,
      data: sortedMonths.map(month => timeData[month])
    };
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取会议时间分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议时间分布数据失败',
      error: error.message
    });
  }
};

/**
 * 审核会议
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewConference = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(conferenceModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { reviewStatus, reviewComment, reviewer, id } = req.body;
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少会议ID',
        data: null
      });
    }
    console.log("req.body=",req.body);
    
    if (!reviewer) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少审核人ID',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 检查会议是否存在
    const conference = await conferenceModel.findByPk(id, { transaction });
    if (!conference) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '会议不存在',
        data: null
      });
    }
    
    // 权限检查 - 只有管理员可以审核
    const hasPermission = userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER';
    
    if (!hasPermission) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核会议',
        data: null
      });
    }

    // 更新审核状态
    const updateData = {
      reviewerId: reviewer,
      ifReviewer: !!reviewStatus,
      reviewedAt: new Date()
    };
    
    // 添加审核意见（如果有）
    if (reviewComment !== undefined) {
      updateData.reviewComment = reviewComment;
    }
    
    await conference.update(updateData, { transaction });
    
    // 会议审核通过后，更新用户排名数据
    if (reviewStatus) {
      try {
        // 获取会议级别对应的分数
        const conferenceLevel = await conferenceLevelModel.findByPk(conference.levelId, { transaction });
        if (!conferenceLevel) {
          console.error(`未找到会议级别信息，levelId: ${conference.levelId}`);
          throw new Error('未找到会议级别信息');
        }
        
        const baseScore = conferenceLevel.score || 0;
        
        // 获取会议所有参与者及其分配比例
        const participants = await conferenceParticipantModel.findAll({
          where: { conferenceId: id },
          transaction
        });
        console.log("participants==",participants);
        
        if (participants && participants.length > 0) {
          // 准备用户ID数组和得分数组
          const participantUserIds = [];
          const participantScores = [];
          
          // 计算每个参与者的得分
          for (const participant of participants) {
            const userId = participant.participantId;
            const allocationRatio = parseFloat(participant.allocationRatio) || 0;
            
            // 计算该参与者应得的分数 = 会议基础分 * 分配比例
            const userScore = baseScore * allocationRatio;
            participantUserIds.push(userId);
            participantScores.push(userScore);
          }
          
          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("conferences");
          
          // 判断会议是否在时间区间内
          const isInTimeRange = timeInterval && conference.holdTime ? 
            isProjectInTimeRange(conference.holdTime, timeInterval.startTime, timeInterval.endTime) : 
            false;
          
          console.log(`会议ID ${id} 是否在统计时间区间内: ${isInTimeRange}`);
          console.log(`会议参与者数量: ${participantUserIds.length}, 基础分数: ${baseScore}`);
          
          // 根据会议是否在时间区间内，更新不同的排名表
          let rankingTables = [];
          
          if (isInTimeRange) {
            // 在区间内：更新范围内表和全部表
            rankingTables = [
              'user_ranking_reviewed_in', 
              'user_ranking_reviewed_all'
            ];
            console.log(`更新范围内排名表和全部排名表`);
          } else {
            // 不在区间内：更新范围外表和全部表
            rankingTables = [
              'user_ranking_reviewed_out', 
              'user_ranking_reviewed_all'
            ];
            console.log(`更新范围外排名表和全部排名表`);
          }
          
          try {
          console.log("participantUserIds=",participantUserIds);
          
            for (const table of rankingTables) {
              // 更新所有参与者的排名数据：每人计数+1，分数增加各自的得分
              await updateUserRankings(
                participantUserIds,          // 所有参与者的用户ID数组
                table,                       // 表名
                'conferences',                // 类型名
                Array(participantUserIds.length).fill(1), // 每个参与者计数+1
                participantScores,           // 每个参与者的得分数组
                transaction,                 // 传递事务对象
                "add"                        // 操作类型：加分
              );
            }
          } catch (rankingError) {
            console.error('更新排名表失败:', rankingError);
            // 对于所有错误，都应当回滚事务以保证数据一致性
            await transaction.rollback();
            throw new Error(`更新排名失败: ${rankingError.message}`);
          }
        } else {
          console.log(`会议ID ${id} 没有参与者，无需更新排名`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 检查是否已经回滚，如果没有则回滚事务
        if (!error.message || !error.message.includes('更新排名失败')) {
          await transaction.rollback();
          throw error; // 将错误向上传播
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    // 查询更新后的会议详情
    const updatedConference = await conferenceModel.findByPk(id, {
      include: [
        {
          model: conferenceLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score']
        },
        {
          model: conferenceParticipantModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'participant',
              attributes: ['id', 'nickname', 'username', 'studentNumber']
            }
          ]
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: updatedConference
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('会议审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '会议审核失败: ' + error.message,
      error: error.message
    });
  }
};

/**
 * 获取会议统计概览数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getConferenceStatistics = async (req, res) => {
  try {
    const { userId } = req.body;
    
    // 构建查询条件
    let whereCondition = {};
    let conferenceIds = [];
    
    // 如果提供了userId，只统计该用户参与的会议
    if (userId) {
      // 查询用户参与的会议
      const participations = await conferenceParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['conferenceId']
      });
      
      // 查询用户作为第一负责人的会议
      const firstResponsibleConferences = await conferenceModel.findAll({
        where: { firstResponsibleId: userId },
        attributes: ['id']
      });
      
      // 合并两种情况的会议ID
      const participationIds = participations.map(p => p.conferenceId);
      const firstResponsibleIds = firstResponsibleConferences.map(c => c.id);
      conferenceIds = [...new Set([...participationIds, ...firstResponsibleIds])];
      
      if (conferenceIds.length === 0) {
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: {
            totalConferences: 0,
            activeConferences: 0,
            averageScore: 0,
            reviewedRate: 0
          }
        });
      }
      
      whereCondition.id = { [Op.in]: conferenceIds };
    }
    
    // 查询所有会议
    const conferences = await conferenceModel.findAll({
      where: whereCondition,
      include: [
        {
          model: conferenceLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
        }
      ]
    });
    
    // 统计数据
    const totalConferences = conferences.length;
    
    // 获取当前时间
    const now = new Date();
    const currentDate = now.toISOString().split('T')[0]; // 当前日期，格式为 YYYY-MM-DD
    
    // 统计当前日期的会议（当前日期的会议）
    const activeConferences = conferences.filter(conference => {
      return conference.holdTime === currentDate;
    }).length;
    
    // 计算平均分数
    const totalScore = conferences.reduce((sum, conference) => {
      return sum + (conference.level?.score || 0);
    }, 0);
    const averageScore = totalConferences > 0 ? totalScore / totalConferences : 0;
    
    // 统计已审核的比例
    const reviewedConferences = conferences.filter(conference => conference.ifReviewer === 1).length;
    const reviewedRate = totalConferences > 0 ? reviewedConferences / totalConferences : 0;
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalConferences,
        activeConferences,
        averageScore,
        reviewedRate,
        reviewedConferences,
        totalScore
      }
    });
  } catch (error) {
    console.error('获取会议统计概览失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议统计概览失败',
      error: error.message
    });
  }
};

/**
 * 获取会议级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus } = req.body;
    
    // 构建查询条件
    let whereCondition = {};
    
    // 更新审核状态筛选逻辑
    if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }
    
    let conferenceIds = [];
    
    // 如果提供了userId，只统计该用户参与的会议
    if (userId) {
      // 查询用户参与的会议
      const participations = await conferenceParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['conferenceId']
      });
      
      // 查询用户作为第一负责人的会议
      const firstResponsibleConferences = await conferenceModel.findAll({
        where: { firstResponsibleId: userId },
        attributes: ['id']
      });
      
      // 合并两种情况的会议ID
      const participationIds = participations.map(p => p.conferenceId);
      const firstResponsibleIds = firstResponsibleConferences.map(c => c.id);
      conferenceIds = [...new Set([...participationIds, ...firstResponsibleIds])];
      
      if (conferenceIds.length === 0) {
        // 该用户没有参与会议
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
      
      whereCondition.id = { [Op.in]: conferenceIds };
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("conferences");
    
    // 根据range参数添加时间筛选条件
    if (range === 'in' && timeInterval) {
      // 在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime },
          { [Op.is]: null }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    // 查询所有会议
    const conferences = await conferenceModel.findAll({
      where: whereCondition,
      include: [
        {
          model: conferenceLevelModel,
          as: 'level',
          attributes: ['id', 'levelName'],
          required: true,
        },
      ]
    });
    
    // 初始化级别数据
    const levelData = {};
    
    // 统计会议级别分布
    conferences.forEach(conference => {
      const conferenceJson = conference.toJSON();
      
      // 检查会议是否在时间范围内（仅用于前端展示）
      const isInRange = timeInterval && conferenceJson.holdTime ? 
        isProjectInTimeRange(conferenceJson.holdTime, timeInterval.startTime, timeInterval.endTime) : 
        false;
      
      conferenceJson.isInTimeRange = isInRange;
      
      const levelName = conferenceJson.level.levelName;
      levelData[levelName] = (levelData[levelName] || 0) + 1;
    });
    
    // 转换为前端期望的格式：[{levelName, count}]
    const result = Object.entries(levelData).map(([levelName, count]) => ({ levelName, count }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取会议级别分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议级别分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取教师会议排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherConferenceRanking = async (req, res) => {
  try {
    const { 
      range = 'all', 
      reviewStatus,
      nickname = '',
      limit = 10,
      page = 1,
      isExport = false
    } = req.body;
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(limit);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 根据range选择对应的排名表模型
    let RankingModel;
    switch(range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }
    
    // 查询条件：按会议总分降序排序
    const queryOptions = {
      order: [['conferenceScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'conferenceCount',
        'conferenceScore'
      ]
    };
    
    // 如果提供了昵称，添加筛选条件
    if (nickname) {
      queryOptions.where = {
        nickName: {
          [Op.like]: `%${nickname}%`
        }
      };
    }

    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }
    
    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);
    
    // 格式化返回数据
    const rankingData = rows.map((item, index) => ({
      rank: item.rank || index + 1 + offset,
      userId: item.userId,
      name: item.nickName,
      employeeNumber: item.studentNumber,
      conferenceCount: item.conferenceCount || 0,
      conferenceScore: parseFloat(item.conferenceScore || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取教师会议排名成功',
      data: {
        list: rankingData,
        pagination: {
          current: pageNum,
          pageSize: pageSizeNum,
          total: count
        }
      }
    });
  } catch (error) {
    console.error('获取教师会议排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师会议排名失败',
      error: error.message
    });
  }
};

/**
 * 获取教师会议详情数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherConferenceDetails = async (req, res) => {
  try {
    const { userId, page = 1, pageSize = 10, range = 'in', reviewStatus } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }
    
    // 查询用户
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("conferences");
    
    // 查询用户参与的会议ID
    const participations = await conferenceParticipantModel.findAll({
      where: { participantId: userId },
      attributes: ['conferenceId']
    });
    
    // 查询用户作为第一负责人的会议ID
    const firstResponsibleConferences = await conferenceModel.findAll({
      where: { firstResponsibleId: userId },
      attributes: ['id']
    });
    
    // 合并两种情况的会议ID
    const participationIds = participations.map(p => p.conferenceId);
    const firstResponsibleIds = firstResponsibleConferences.map(c => c.id);
    const conferenceIds = [...new Set([...participationIds, ...firstResponsibleIds])];
    
    // 如果用户没有参与任何会议
    if (conferenceIds.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          list: [],
          totalScore: 0,
          user: {
            id: user.id,
            name: user.nickname || user.username,
            employeeNumber: user.studentNumber
          },
          pagination: {
            total: 0,
            page: pageNum,
            pageSize: pageSizeNum,
            totalPages: 0
          }
        }
      });
    }
    
    // 构建查询条件
    const whereCondition = {
      id: { [Op.in]: conferenceIds }
    };
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }
    
    // 根据range参数添加时间筛选条件
    if (range === 'in' && timeInterval) {
      // 在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的会议记录
      whereCondition.holdTime = { 
        ...(whereCondition.holdTime || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime },
          { [Op.is]: null }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    // 查询用户参与的所有会议
    const { count, rows } = await conferenceModel.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: conferenceLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
      order: [['holdTime', 'DESC']],
      offset,
      limit
    });
    
    // 处理会议数据
    const processedConferences = [];
    let totalScore = 0;
    
    // 处理每个会议数据
    for (const conference of rows) {
      const conferenceJson = conference.toJSON();
      
      // 查询参与者信息
      const participants = await conferenceParticipantModel.findAll({
        where: { conferenceId: conference.id },
        include: [
          {
            model: userModel,
            as: 'participant',
            attributes: ['id', 'nickname', 'username', 'studentNumber']
          }
        ]
      });
      
      // 计算用户在此会议中的分配比例
      const userParticipation = participants.find(p => p.participantId === userId);
      let userAllocationRatio = 0;
      let isLeader = false;
      
      if (userParticipation) {
        userAllocationRatio = userParticipation.allocationRatio;
        isLeader = userParticipation.isLeader === 1;
      } else if (conference.firstResponsibleId === userId) {
        // 如果用户是第一负责人但不在参与者表中
        isLeader = true;
        userAllocationRatio = 1.0; // 默认为100%
      }
      
      // 计算用户在此会议中获得的分数
      const conferenceScore = (conference.level?.score || 0) * userAllocationRatio;
      totalScore += conferenceScore;
      
      // 添加参与者信息
      conferenceJson.participants = participants.map(p => p.toJSON());
      conferenceJson.userAllocationRatio = userAllocationRatio;
      conferenceJson.isLeader = isLeader;
      conferenceJson.userScore = conferenceScore;
      
      // 检查是否在统计时间范围内（仅用于前端显示）
      let isInTimeRange = false;
      if (timeInterval && conferenceJson.holdTime) {
        isInTimeRange = isProjectInTimeRange(conferenceJson.holdTime, timeInterval.startTime, timeInterval.endTime);
      }
      
      conferenceJson.isInTimeRange = isInTimeRange;
      
      // 添加到处理后的会议列表
      processedConferences.push(conferenceJson);
    }
    
    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: processedConferences,
        totalScore: totalScore,
        user: {
          id: user.id,
          name: user.nickname || user.username,
          employeeNumber: user.studentNumber
        },
        pagination: {
          total: count,
          page: pageNum,
          pageSize: pageSizeNum,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取教师会议详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师会议详情失败',
      error: error.message
    });
  }
}; 

/**
 * 获取会议总分统计（按级别和总体）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getConferencesTotalScore = async (req, res) => {
  try {
    const { 
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all' // 审核状态筛选: 'all'(全部), 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
    } = req.body;
    
    // 获取数据库连接实例
    let sequelize;
    if (conferenceModel.sequelize) {
      sequelize = conferenceModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }
    
    let userId = null; 
    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    // 调用存储过程
    const results = await sequelize.query(
      'CALL get_conferences_total_score(?, ?, ?)',
      {
        replacements: [
          range || 'all',
          reviewStatus || 'all',
          userId
        ],
        type: sequelize.QueryTypes.RAW
      }
    );
    
    console.log("存储过程返回结果:", JSON.stringify(results));
    
    // 处理查询结果
    let levelStats = [];
    let totalStats = { totalConferences: 0, totalScore: 0 };
    let timeInterval = null;
    
    // 分析和处理返回的结果结构
    if (Array.isArray(results)) {
      // 如果结果是扁平数组（没有嵌套）且包含levelId和levelName字段，则这是级别统计
      if (results.length > 0 && results[0] && typeof results[0].levelId === 'string') {
        levelStats = results.map(item => ({
          ...item,
          count: parseInt(item.count || 0),
          totalScore: parseFloat(item.totalScore || 0)
        }));
      } 
      // 如果结果是嵌套数组，则按照预期的三个结果集处理
      else if (results.length > 0) {
        // 第一个结果集：级别统计
        if (Array.isArray(results[0])) {
          levelStats = results[0].map(item => ({
            ...item,
            count: parseInt(item.count || 0),
            totalScore: parseFloat(item.totalScore || 0)
          }));
        }
        
        // 第二个结果集：总体统计
        if (results.length > 1 && Array.isArray(results[1]) && results[1].length > 0) {
          const totalStatsData = results[1][0];
          if (totalStatsData) {
            totalStats = {
              totalConferences: parseInt(totalStatsData.totalConferences || 0),
              totalScore: parseFloat(totalStatsData.totalScore || 0)
            };
          }
        }
        
        // 第三个结果集：时间区间
        if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
          const timeData = results[2][0];
          if (timeData) {
            timeInterval = {
              startTime: timeData.startTime,
              endTime: timeData.endTime,
              name: timeData.name
            };
          }
        }
      }
    }
    
    // 如果有级别统计但没有总体统计，则计算总体统计
    if (levelStats.length > 0 && (totalStats.totalConferences === 0 || totalStats.totalScore === 0)) {
      totalStats = {
        totalConferences: levelStats.reduce((sum, item) => sum + (item.count || 0), 0),
        totalScore: parseFloat(levelStats.reduce((sum, item) => sum + (item.totalScore || 0), 0).toFixed(2))
      };
    }
    
    // 如果没有从存储过程获得时间区间，则从数据库直接获取
    if (!timeInterval) {
      try {
        const timeIntervalData = await getTimeIntervalByName("conferences");
        if (timeIntervalData) {
          timeInterval = {
            startTime: timeIntervalData.startTime,
            endTime: timeIntervalData.endTime,
            name: timeIntervalData.name
          };
        }
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        levelStats: levelStats,
        totalStats: totalStats,
        timeInterval: timeInterval
      }
    });
  } catch (error) {
    console.error('获取会议总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户会议详情列表及得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserConferencesDetail = async (req, res) => {
  try {
    const { 
      userId,                // 用户ID - 必填
      range = 'all',        // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      pageSize = 10,        // 每页记录数
      pageNum = 1           // 当前页码
    } = req.body;

    // 验证必填参数
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：userId',
        data: null
      });
    }
    
    // 获取数据库连接实例
    let sequelize;
    if (conferenceModel.sequelize) {
      sequelize = conferenceModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }
    
    // 调用存储过程
    const results = await sequelize.query(
      'CALL get_user_conferences_detail(?, ?, ?, ?, ?)',
      {
        replacements: [
          userId,
          range || 'all',
          reviewStatus || 'all',
          pageSize,
          pageNum
        ],
        type: sequelize.QueryTypes.RAW
      }
    );
    
    console.log("存储过程返回结果:", JSON.stringify(results));
    
    // 处理查询结果
    let totalCount = 0;
    let conferencesList = [];
    let statsData = { totalConferences: 0, leaderConferenceCount: 0, totalScore: 0 };
    let timeInterval = null;
    
    // 分析和处理返回的结果结构
    if (Array.isArray(results) && results.length > 0) {
      // 第一个结果集: 总记录数
      if (results[0] && Array.isArray(results[0]) && results[0].length > 0) {
        totalCount = parseInt(results[0][0].totalCount || 0);
      }
      
      // 第二个结果集: 会议列表
      if (results.length > 1 && Array.isArray(results[1])) {
        conferencesList = results[1].map(item => ({
          ...item,
          baseScore: parseFloat(item.baseScore || 0),
          actualScore: parseFloat(item.actualScore || 0),
          allocationRatio: parseFloat(item.allocationRatio || 0)
        }));
      }
      
      // 第三个结果集: 统计数据
      if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
        const stats = results[2][0];
        statsData = {
          totalConferences: parseInt(stats.totalConferences || 0),
          leaderConferenceCount: parseInt(stats.leaderConferenceCount || 0),
          totalScore: parseFloat(stats.totalScore || 0)
        };
      }
      
      // 第四个结果集: 时间区间
      if (results.length > 3 && Array.isArray(results[3]) && results[3].length > 0) {
        const timeData = results[3][0];
        timeInterval = {
          startTime: timeData.startTime,
          endTime: timeData.endTime,
          name: timeData.name
        };
      }
    }
    
    // 如果没有从存储过程获得时间区间，则从数据库直接获取
    if (!timeInterval) {
      try {
        const timeIntervalData = await getTimeIntervalByName("conferences");
        if (timeIntervalData) {
          timeInterval = {
            startTime: timeIntervalData.startTime,
            endTime: timeIntervalData.endTime,
            name: timeIntervalData.name
          };
        }
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }
    
    // 查询用户信息
    const user = await userModel.findByPk(userId);
    const userData = user ? {
      id: user.id,
      name: user.nickname || user.username,
      employeeNumber: user.studentNumber
    } : { id: userId, name: '未知用户', employeeNumber: '' };
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalCount,
        pageSize,
        pageNum,
        conferences: conferencesList,
        stats: statsData,
        timeInterval,
        user: userData
      }
    });
  } catch (error) {
    console.error('获取用户会议详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户会议详情失败',
      error: error.message
    });
  }
};

/**
 * 重新提交会议审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapply = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(conferenceModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少会议ID',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找会议
    const conference = await conferenceModel.findByPk(id, { transaction });
    
    if (!conference) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '会议不存在',
        data: null
      });
    }
    
    // 检查会议所有权或管理员权限
    const isOwner = conference.userId === userInfo.id;
    const isAdmin = userInfo.role.roleAuth === 'TEACHER-LV1' || userInfo.role.roleAuth === 'SUPER' || userInfo.role.roleAuth === 'ADMIN-LV2';
    
    if (!isOwner && !isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限重新提交该会议',
        data: null
      });
    }
    
    // 检查当前审核状态，只有被拒绝的会议可以重新提交
    if (conference.ifReviewer != 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '只有被拒绝的会议可以重新提交审核',
        data: null
      });
    }
    
    // 更新会议状态为待审核
    await conference.update({
      ifReviewer: null,  // 设置为待审核状态
      reviewComment: null, // 清空之前的审核意见
      reviewerId: null // 清空之前的审核人
    }, { transaction });
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();

    console.error('重新提交审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '重新提交审核失败',
      error: error.message
    });
  }
};

/**
 * 获取审核状态概览
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReviewStatusOverview = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("conferences");
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const whereCondition = {};

    // 时间范围筛选
    if (range === 'in' && timeInterval) {
      whereCondition.holdTime = {
        [Op.between]: [timeInterval.startTime, timeInterval.endTime]
      };
    } else if (range === 'out' && timeInterval) {
      whereCondition.holdTime = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }

    // 构建用户筛选条件
    let userCondition = {};
    if (userId) {
      userCondition = {
        include: [{
          model: conferenceParticipantModel,
          as: 'participants',
          where: { participantId: userId },
          required: true
        }]
      };
    } else if (userInfo.role.roleAuth === 'TEACHER-LV1') {
      userCondition = {
        include: [{
          model: conferenceParticipantModel,
          as: 'participants',
          where: { participantId: userInfo.id },
          required: true
        }]
      };
    }

    // 查询各状态数量
    const [reviewed, pending, rejected] = await Promise.all([
      conferenceModel.count({
        where: { ...whereCondition, ifReviewer: 1 },
        ...userCondition
      }),
      conferenceModel.count({
        where: { ...whereCondition, ifReviewer: null },
        ...userCondition
      }),
      conferenceModel.count({
        where: { ...whereCondition, ifReviewer: 0 },
        ...userCondition
      })
    ]);

    const total = reviewed + pending + rejected;
    const reviewedRate = total > 0 ? ((reviewed / total) * 100).toFixed(1) : 0;

    return res.status(200).json({
      code: 200,
      message: '获取审核状态数据成功',
      data: {
        reviewed,
        pending,
        rejected,
        total,
        reviewedRate: parseFloat(reviewedRate)
      }
    });
  } catch (error) {
    console.error('获取审核状态概览失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取审核状态概览失败',
      error: error.message
    });
  }
};