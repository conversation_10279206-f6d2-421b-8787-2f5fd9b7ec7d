const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义专利分类模型
const PatentCategory = sequelize.define('patent_categories', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    categoryName: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '分类名称'
    },
    score: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        comment: '分数'
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '分类描述'
    },
    status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: '状态：0-禁用，1-启用'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'patent_categories',
    timestamps: true,
    indexes: [
        {
            name: 'idx_patent_category_name',
            fields: ['categoryName']
        },
        {
            name: 'idx_patent_category_status',
            fields: ['status']
        }
    ]
});

module.exports = PatentCategory; 