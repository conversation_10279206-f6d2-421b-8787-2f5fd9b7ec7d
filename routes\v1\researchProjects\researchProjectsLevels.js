const express = require('express');
const router = express.Router();
const researchProjectsLevelsController = require('../../../controllers/v1/researchProjects/researchProjectsLevelsController');

/**
 * 获取科研项目级别列表
 * @route GET /v1/sys/research/research-projects-levels/list
 * @group 科研项目管理 - 科研项目级别相关接口
 * @param {string} level_name.query - 项目级别名称（模糊搜索）
 * @param {number} score.query - 核算分数
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0, page: 1, pageSize: 10}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list', researchProjectsLevelsController.getResearchProjectsLevels);

/** 
 * 获取所有科研项目级别
 * @route GET /v1/sys/research/research-projects-levels/all
 * @group 科研项目管理 - 科研项目级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/all', researchProjectsLevelsController.getAllResearchProjectsLevels);

/**
 * 获取科研项目级别详情
 * @route GET /v1/sys/research/research-projects-levels/detail
 * @group 科研项目管理 - 科研项目级别相关接口
 * @param {string} id.query.required - 项目级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail', researchProjectsLevelsController.getResearchProjectsLevelDetail);

/**
 * 创建科研项目级别
 * @route POST /v1/sys/research/research-projects-levels/create
 * @group 科研项目管理 - 科研项目级别相关接口
 * @param {string} level_name.body.required - 项目级别名称
 * @param {number} score.body.required - 核算分数
 * @param {string} description.body - 项目级别描述
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', researchProjectsLevelsController.createResearchProjectsLevel);

/**
 * 更新科研项目级别
 * @route PUT /v1/sys/research/research-projects-levels/update
 * @group 科研项目管理 - 科研项目级别相关接口
 * @param {string} id.body.required - 项目级别ID
 * @param {string} level_name.body - 项目级别名称
 * @param {number} score.body - 核算分数
 * @param {string} description.body - 项目级别描述
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/update', researchProjectsLevelsController.updateResearchProjectsLevel);

/**
 * 删除科研项目级别
 * @route DELETE /v1/sys/research/research-projects-levels/delete
 * @group 科研项目管理 - 科研项目级别相关接口
 * @param {string} id.query.required - 项目级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete', researchProjectsLevelsController.deleteResearchProjectsLevel);

/**
 * 批量删除科研项目级别
 * @route DELETE /v1/sys/research/research-projects-levels/batch-delete
 * @group 科研项目管理 - 科研项目级别相关接口
 * @param {Array} ids.query.required - 项目级别ID列表
 * @returns {object} 200 - {code: 200, message: "批量删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/batch-delete', researchProjectsLevelsController.batchDeleteResearchProjectsLevels);

module.exports = router; 