<template>
  <div class="project-levels">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
    
    <a-card title="项目级别管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            添加项目级别
          </a-button>
        </a-space>
      </template>

      <a-alert
        message="项目级别说明"
        description="项目评分根据项目级别进行计算。在统计范围内的项目将根据级别获得相应分数，不在统计范围内的分数为0。"
        type="info"
        show-icon
        style="margin-bottom: 16px;"
      />

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="isLoading"
        rowKey="id"
        @change="handleTableChange"
        :bordered="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'score'">
            <span>{{ record.score }}分</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这个项目级别吗？"
                @confirm="handleDelete(record)"
              >
                <a class="text-danger">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑级别模态框 -->
    <a-modal
      :visible="modalVisible"
      :title="isEdit ? '编辑项目级别' : '添加项目级别'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="confirmLoading"
    >
      <a-form 
        ref="formRef" 
        :model="formState" 
        :rules="rules" 
        :label-col="{ span: 6 }" 
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item name="levelName" label="级别名称">
          <a-input v-model:value="formState.levelName" placeholder="请输入级别名称" />
        </a-form-item>
        
        <a-form-item name="score" label="级别分数">
          <a-input-number 
            v-model:value="formState.score" 
            placeholder="请输入级别分数" 
            :min="0" 
            :precision="1"
            style="width: 100%;" 
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import * as echarts from 'echarts'
import { debounce } from 'lodash'
import {
  getProjectLevels,
  getProjectLevelDetail,
  createProjectLevel,
  updateProjectLevel,
  deleteProjectLevel
} from '@/api/modules/api.studentProjectGuidanceProjectLevels'

// 图表相关
let chart = null
const chartRange = ref('in')


// 表格列定义
const columns = [
  {
    title: '级别名称',
    dataIndex: 'levelName',
    key: 'levelName',
  },
  {
    title: '分数',
    dataIndex: 'score',
    key: 'score',
    sorter: true,
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 数据源
const dataSource = ref([])
const isLoading = ref(false)
const errorMessage = ref('')

// 搜索表单
const searchForm = reactive({
  levelName: '',
})

// 重置搜索条件
const resetSearch = () => {
  searchForm.levelName = ''
  handleSearch()
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)

// 表单引用
const formRef = ref(null)

// 表单数据
const formState = reactive({
  levelName: '',
  score: 0,
})

// 校验规则
const rules = {
  levelName: [{ required: true, message: '请输入级别名称', trigger: 'blur' }],
  score: [{ required: true, message: '请输入级别分数', trigger: 'change' }],
}

// 记录图表是否已初始化的状态
const chartInitialized = ref(false)

// 加载数据
onMounted(() => {
  fetchData()
  
  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', debounce(() => {
    try {
      if (chart) chart.resize()
    } catch (error) {
      console.error('调整图表大小出错:', error)
    }
  }, 200))
})

// 获取列表数据
const fetchData = async () => {
  isLoading.value = true
  errorMessage.value = ''
  
  try {
    // 处理搜索参数
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
    }

    if (searchForm.levelName) {
      params.levelName = searchForm.levelName
    }

    const response = await getProjectLevels(params)
    
    if (response && response.code === 200) {
      dataSource.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0
      
      // 通过延迟更新图表，防止重复初始化
      nextTick(() => {
        // 只在首次加载或图表不存在时初始化
        if (!chartInitialized.value) {
          initChart(chartRange.value)
          chartInitialized.value = true
        }
      })
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = '获取项目级别列表失败：' + (response?.message || '未知错误')
    }
  } catch (error) {
    console.error('获取项目级别列表失败:', error)
    message.error('获取项目级别列表失败: ' + (error.message || '未知错误'))
    errorMessage.value = '获取项目级别列表失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  
  // 处理排序
  if (sorter && sorter.field) {
    // 这里可以添加排序逻辑
  }
  
  fetchData()
}

// 显示添加级别弹窗
const showAddModal = () => {
  isEdit.value = false
  currentRecord.value = null
  
  // 重置表单
  formState.levelName = ''
  formState.score = 0
  
  // 显示弹窗
  modalVisible.value = true
  
  // 如果表单ref存在，重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 处理编辑
const handleEdit = async (record) => {
  isEdit.value = true
  currentRecord.value = record
  
  try {
    const response = await getProjectLevelDetail(record.id)
    
    if (response && response.code === 200) {
      const detail = response.data
      
      // 设置表单数据
      formState.levelName = detail.levelName
      formState.score = detail.score
      
      // 显示弹窗
      modalVisible.value = true
    } else {
      message.error(response?.message || '获取项目级别详情失败')
    }
  } catch (error) {
    console.error('获取项目级别详情失败:', error)
    message.error('获取项目级别详情失败: ' + (error.message || '未知错误'))
  }
}

// 处理模态框确认
const handleModalOk = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    confirmLoading.value = true
    
    let response
    
    if (isEdit.value && currentRecord.value) {
      // 更新级别
      response = await updateProjectLevel(currentRecord.value.id, {
        levelName: formState.levelName,
        score: formState.score
      })
    } else {
      // 添加级别
      response = await createProjectLevel({
        levelName: formState.levelName,
        score: formState.score
      })
    }
    
    if (response && response.code === 200) {
      message.success(isEdit.value ? '更新成功' : '添加成功')
      modalVisible.value = false
      fetchData() // 刷新数据
      
      // 更新图表
      setTimeout(() => {
        initChart(chartRange.value)
      }, 500)
    } else {
      message.error(response?.message || (isEdit.value ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    console.error(isEdit.value ? '更新项目级别失败:' : '添加项目级别失败:', error)
    message.error((isEdit.value ? '更新项目级别失败: ' : '添加项目级别失败: ') + (error.message || '未知错误'))
  } finally {
    confirmLoading.value = false
  }
}

// 取消弹窗
const handleModalCancel = () => {
  modalVisible.value = false
}

// 删除级别
const handleDelete = async (record) => {
  try {
    const response = await deleteProjectLevel(record.id)
    
    if (response && response.code === 200) {
      message.success('删除成功')
      fetchData() // 刷新数据
      
      // 更新图表
      setTimeout(() => {
        initChart(chartRange.value)
      }, 500)
    } else {
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('删除项目级别失败:', error)
    message.error('删除项目级别失败: ' + (error.message || '未知错误'))
  }
}
</script>

<style scoped>
.project-levels {
  margin: 24px;
  border: 1px solid #f0f0f0;
}

.text-danger {
  color: #ff4d4f;
}
</style> 