const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义科研经费模型
const ResearchFund = sequelize.define('researchFunds', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: 'ID'
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '项目名称'
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '经费类型'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '经费金额(万元)'
  },
  startDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '开始时间'
  },
  endDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '结束时间'
  },
  leader: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '项目负责人'
  },
  members: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '项目成员'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '项目描述'
  },
  score: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '得分'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    defaultValue: 1,
    comment: '状态'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'research_funds',
  timestamps: true,
  indexes: [
    {
      name: 'idx_research_fund_leader',
      fields: ['leader']
    },
    {
      name: 'idx_research_fund_type',
      fields: ['type']
    },
    {
      name: 'idx_research_fund_status',
      fields: ['status']
    }
  ]
});

module.exports = ResearchFund; 