const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const conferenceLevelModel = require('./conferencesLevelsModel');
const userModel = require('./userModel');

// 定义会议模型
const Conference = sequelize.define('conferences', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    conferenceName: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '会议名称'
    },
    levelId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '级别ID（外键）'
    },
    holdTime: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '举办时间'
    },
    firstResponsibleId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '第一负责人ID'
    },
    remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注'
    },
    reviewerId: {
        type: DataTypes.CHAR(36),
        allowNull: true,
        comment: '审核人ID'
    },
    ifReviewer: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        comment: '审核状态（0，拒审核 1，审核，null未审核）'
      },
      attachmentUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '附件URL'
      },
      reviewComment: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '审核意见'
      },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'conferences',
    timestamps: true,
    indexes: [
        {
            name: 'idx_conference_name',
            fields: ['conferenceName']
        },
        {
            name: 'idx_hold_time',
            fields: ['holdTime']
        },
        {
            name: 'idx_level_id',
            fields: ['levelId']
        }
    ]
});

// 建立与会议级别的关联关系
Conference.belongsTo(conferenceLevelModel, {
    foreignKey: 'levelId',
    as: 'level'
});

// 建立与第一负责人的关联关系
Conference.belongsTo(userModel, {
    foreignKey: 'firstResponsibleId',
    as: 'firstResponsible'
});

// 建立与审核人的关联关系
Conference.belongsTo(userModel, {
    foreignKey: 'reviewerId',
    as: 'reviewer'
});


// 使用 setTimeout 延迟建立关联关系
setTimeout(() => {
    // 在导出模块后引入conferenceParticipantsModel并设置关联关系
    const conferenceParticipantsModel = require('./conferenceParticipantsModel');
    
    // 建立与会议参与者的关联关系
    Conference.hasMany(conferenceParticipantsModel, {
        foreignKey: 'conferenceId',
        as: 'participants'
    }); 
      }, 0); 

module.exports = Conference;



