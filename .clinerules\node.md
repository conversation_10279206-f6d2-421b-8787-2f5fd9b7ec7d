# Node.js 后端项目规范（node.md）

## 1. 技术框架与模块划分
- 框架：Express 4.18+ + Sequelize ORM + MySQL
- 模块结构：controllers, services, models, middleware, utils
- 使用 RBAC 权限系统、JWT 认证、RESTful 接口风格

## 2. 项目结构建议
- `/controllers/v1/`：按版本和业务模块拆分
- `/middleware/`：包含验证、权限、限流、安全、错误处理等
- `/models/v1/mapping/`：数据库模型定义
- `/utils/`：响应封装、日志、加密等通用工具
- `/routes/v1/`：路由入口
- `/uploads/`：上传文件管理
- `/config/`：数据库和 Swagger 配置

## 3. 类型与注释
- 推荐迁移至 TypeScript，或增强 JSDoc 支持
- 所有模型与接口需定义类型或接口结构

## 4. 接口与错误处理
- 所有接口统一返回如下格式：
```js
{ code: 200, message: 'success', data: {}, total: 0 }
