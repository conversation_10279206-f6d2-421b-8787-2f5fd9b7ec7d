const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fileController = require('../../../controllers/v1/common/fileController');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 存储目录根据文件类型动态确定
    let uploadDir = 'uploads/';
    if (req.body.class === 'research_project') {
      uploadDir += 'research_projects/'; // 确保使用复数形式
    } else {
      uploadDir += req.body.class ? req.body.class + '/' : 'general/';
    }
    
    // 确保目录存在
    const fs = require('fs');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    // 正确处理中文文件名
    const originalName = Buffer.from(file.originalname, 'binary').toString('utf8');
    const ext = path.extname(originalName);
    
    // 标准化文件名前缀
    let prefix = 'file';
    if (req.body.class === 'research_project') {
      prefix = 'research'; // 使用统一的前缀
    } else if (req.body.class) {
      prefix = req.body.class;
    }
    
    cb(null, `${prefix}-${uniqueSuffix}${ext}`);
  }
});

// 配置文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    // 文档类型
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    // 图片类型
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/webp',
    // 压缩文件
    'application/zip',
    'application/x-rar-compressed'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

// 创建multer实例
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 限制文件大小为10MB
  }
});

/**
 * 上传单个或多个文件
 * @route POST /v1/sys/file/upload
 * @group 文件管理 - 文件上传下载接口
 * @param {file[]} files.formData.required - 上传的文件
 * @param {string} id.formData.required - 用于创建文件夹的唯一ID
 * @param {string} class.formData - 文件分类，也用作存储路径名称
 * @param {string} pathName.formData - 可选的路径名称(已废弃，优先使用class)
 * @param {string} description.formData - 文件描述
 * @param {string} relatedId.formData - 关联ID，默认使用id参数
 * @param {string} relatedType.formData - 关联类型，默认使用pathName参数
 * @returns {object} 200 - {code: 200, message: "文件上传成功", data: [...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/upload', upload.array('files', 10), fileController.uploadFile);
/**
 * 上传多个文件
 * @route POST /v1/sys/file/upload-multiple
 * @group 文件管理 - 文件上传下载接口
 * @param {file[]} files.formData.required - 上传的多个文件
 * @param {string} description.formData - 文件描述
 * @param {string} relatedId.formData - 关联ID
 * @param {string} relatedType.formData - 关联类型
 * @returns {object} 200 - {code: 200, message: "文件上传成功", data: [...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/upload-multiple', upload.array('files', 10), fileController.uploadFile);

/**
 * 上传科研项目附件
 * @route POST /v1/sys/file/upload-project-attachments
 * @group 文件管理 - 文件上传下载接口
 * @param {file[]} files.formData.required - 上传的多个文件
 * @param {string} projectId.formData.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "项目附件上传成功", data: [...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/upload-project-attachments', upload.array('files', 10), fileController.uploadProjectAttachments);

/**
 * 根据ID下载文件
 * @route GET /v1/sys/file/download/:id
 * @group 文件管理 - 文件上传下载接口
 * @param {string} id.path.required - 文件ID
 * @returns {file} 200 - 文件流
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/download/:id', fileController.downloadFile);

/**
 * 根据ID预览文件（在线查看）
 * @route GET /v1/sys/file/preview/:id
 * @group 文件管理 - 文件上传下载接口
 * @param {string} id.path.required - 文件ID
 * @returns {file} 200 - 文件流
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/preview/:id', fileController.previewFile);

/**
 * 删除文件
 * @route DELETE /v1/sys/file/delete/:id
 * @group 文件管理 - 文件上传下载接口
 * @param {string} id.path.required - 文件ID
 * @returns {object} 200 - {code: 200, message: "文件删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete/:id', fileController.deleteFile);

/**
 * 获取关联文件列表
 * @route POST /v1/sys/file/related-files
 * @group 文件管理 - 文件上传下载接口
 * @param {string} relatedId.body.required - 关联ID
 * @param {string} relatedType.body.required - 关联类型
 * @returns {object} 200 - {code: 200, message: "获取文件列表成功", data: [...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/related-files', fileController.getRelatedFiles);

/**
 * 移动文件到项目文件夹
 * @route POST /v1/sys/file/move-to-project
 * @group 文件管理 - 文件上传下载接口
 * @param {string} projectId.body.required - 项目ID
 * @param {string|array} fileIds.body.required - 文件ID或ID数组
 * @param {string} class.body - 文件分类，用于确定存储路径
 * @returns {object} 200 - {code: 200, message: "文件移动处理完成", data: [...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/move-to-project', fileController.moveFileToProjectFolder);

/**
 * 删除目录
 * @route POST /v1/sys/file/delete-directory
 * @group 文件管理 - 文件上传下载接口
 * @param {string} path.body.required - 要删除的目录路径
 * @returns {object} 200 - {code: 200, message: "目录删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete-directory', fileController.deleteDirectory);

module.exports = router;