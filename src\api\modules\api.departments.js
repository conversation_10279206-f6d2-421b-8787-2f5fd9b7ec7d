import service from '@/utils/request'

/**
 * 部门管理相关API
 */

/**
 * 获取部门列表（分页）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页条数
 * @param {string} params.departmentName - 部门名称（模糊搜索）
 * @param {number} params.status - 状态筛选
 * @returns {Promise} 部门列表
 */
export const getDepartments = (params) => {
  return service.get('/sys/departments', { params }).then(response => {
    return response
  })
}

/**
 * 获取所有部门（不分页，用于下拉选择）
 * @returns {Promise} 所有部门列表
 */
export const getAllDepartments = () => {
  return service.get('/sys/departments/all').then(response => {
    return response
  })
}

/**
 * 获取部门树形结构
 * @returns {Promise} 部门树
 */
export const getDepartmentTree = () => {
  return service.get('/sys/departments/tree').then(response => {
    return response
  })
}

/**
 * 获取部门详情
 * @param {string|number} id - 部门ID
 * @returns {Promise} 部门详情
 */
export const getDepartmentDetail = (id) => {
  return service.get(`/sys/departments/${id}`).then(response => {
    return response
  })
}

/**
 * 创建部门
 * @param {Object} data - 部门数据
 * @param {string} data.departmentName - 部门名称
 * @param {string} data.departmentCode - 部门代码
 * @param {string} data.description - 部门描述
 * @param {string} data.parentId - 上级部门ID
 * @param {number} data.sort - 排序
 * @returns {Promise} 创建结果
 */
export const createDepartment = (data) => {
  return service.post('/sys/departments', data).then(response => {
    return response
  })
}

/**
 * 更新部门
 * @param {string|number} id - 部门ID
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
export const updateDepartment = (id, data) => {
  return service.put(`/sys/departments/${id}`, data).then(response => {
    return response
  })
}

/**
 * 删除部门
 * @param {string|number} id - 部门ID
 * @returns {Promise} 删除结果
 */
export const deleteDepartment = (id) => {
  return service.delete(`/sys/departments/${id}`).then(response => {
    return response
  })
}

/**
 * 获取部门统计信息
 * @returns {Promise} 统计信息
 */
export const getDepartmentStatistics = () => {
  return service.get('/sys/departments/statistics').then(response => {
    return response
  })
}
