<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :width="500"
    :footer="null"
    :maskClosable="false"
    :closable="false"
    destroyOnClose
    class="notification-popup"
  >
    <div class="notification-content">
      <div class="notification-header">
        <div class="sender-info">
          <span class="sender-label">发送人：</span>
          <span class="sender-name">{{ sender }}</span>
        </div>
        <div class="send-time">
          <span>{{ sendTime }}</span>
        </div>
      </div>
      <div class="notification-body" v-html="content"></div>
      <div class="notification-footer">
        <a-checkbox v-model:checked="noMoreReminders">不再提醒</a-checkbox>
        <div class="notification-actions">
          <a-button type="primary" @click="handleConfirm">确认收到</a-button>
          <a-button v-if="notificationLink" @click="handleViewDetails">查看详情</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '@/stores/notification'
import { message } from 'ant-design-vue'

const props = defineProps({
  notificationId: {
    type: String,
    default: ''
  }
})

const router = useRouter()
const notificationStore = useNotificationStore()

// 弹窗状态
const visible = ref(false)
const title = ref('系统通知')
const sender = ref('系统管理员')
const sendTime = ref('2023-08-15 10:00:00')
const content = ref('欢迎使用暨南大学基础医学与公共卫生学院教师绩效评定与管理平台！')
const notificationLink = ref('')
const noMoreReminders = ref(false)
const currentNotification = ref(null)

// 监听通知ID变化
watch(() => props.notificationId, (newVal) => {
  if (newVal) {
    showNotification(newVal)
  }
}, { immediate: true })

// 显示通知
const showNotification = (id) => {
  // 检查是否设置了不再提醒
  const noReminder = localStorage.getItem('noNotificationReminder')
  if (noReminder === 'true') {
    return
  }
  
  // 获取通知详情
  const notification = notificationStore.notifications.find(item => item.key === id)
  if (notification) {
    currentNotification.value = notification
    title.value = notification.title
    sender.value = notification.sender
    sendTime.value = notification.sendTime
    content.value = notification.content
    notificationLink.value = notification.link || ''
    visible.value = true
  }
}

// 确认按钮
const handleConfirm = () => {
  if (currentNotification.value) {
    // 标记为已读
    notificationStore.markAsRead(currentNotification.value.key)
    
    // 保存不再提醒设置
    if (noMoreReminders.value) {
      localStorage.setItem('noNotificationReminder', 'true')
    }
    
    visible.value = false
    message.success('已确认收到通知')
  }
}

// 查看详情
const handleViewDetails = () => {
  if (notificationLink.value) {
    router.push(notificationLink.value)
  }
  visible.value = false
}

// 检查是否有未读重要通知
onMounted(() => {
  // 如果没有设置不再提醒，则检查是否有重要通知需要显示
  const noReminder = localStorage.getItem('noNotificationReminder')
  if (noReminder !== 'true') {
    const importantNotification = notificationStore.notifications.find(
      item => item.status === '未读' && item.type === '重要'
    )
    if (importantNotification) {
      showNotification(importantNotification.key)
    }
  }
})
</script>

<style scoped>
.notification-content {
  padding: 10px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.sender-info {
  display: flex;
  align-items: center;
}

.sender-label {
  color: rgba(0, 0, 0, 0.45);
  margin-right: 5px;
}

.sender-name {
  font-weight: bold;
}

.send-time {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.notification-body {
  padding: 10px 0;
  min-height: 100px;
  line-height: 1.6;
}

.notification-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.notification-actions {
  display: flex;
  gap: 10px;
}
</style> 