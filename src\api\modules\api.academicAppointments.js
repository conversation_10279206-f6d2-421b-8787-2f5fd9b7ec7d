import request from '../server'
import { saveAs } from 'file-saver'

/**
 * 获取学术任职列表
 * @param {Object} params - 请求参数
 * @param {string} [params.associationName] - 协会/期刊名称（模糊搜索）
 * @param {string} [params.levelId] - 级别ID
 * @param {string} [params.startYear] - 起始年份
 * @param {string} [params.endYear] - 结束年份
 * @param {string} [params.userId] - 用户ID（可选，如果提供则获取特定用户的学术任职）
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @param {string} [params.range='all'] - 统计范围筛选：'in'(范围内), 'out'(范围外), 'all'(全部)
 * @param {string} [params.reviewStatus='all'] - 审核状态筛选：'all'(全部), 'reviewed'(已审核), 'pending'(待审核), 'rejected'(已拒绝)
 * @returns {Promise} - 请求结果
 */
export function getAppointments(params) {
  return request.post('/academicAppointments/list', params || {})
}

/**
 * 获取学术任职详情
 * @param {String} id - 学术任职ID
 * @returns {Promise} 响应结果
 */
export function getAppointmentDetail(id) {
  return request.post('/academicAppointments/project/detail', { id })
}

/**
 * 创建学术任职
 * @param {Object} data - 学术任职数据
 * @returns {Promise} 响应结果
 */
export function addAppointment(data) {
  return request.post('/academicAppointments/project/create', data || {})
}

/**
 * 更新学术任职
 * @param {String} id - 学术任职ID
 * @param {Object} data - 学术任职数据
 * @returns {Promise} 响应结果
 */
export function updateAppointment(id, data) {
  return request.post('/academicAppointments/project/update', { id, ...data })
}

/**
 * 删除学术任职
 * @param {String} params - 学术任职ID
 * @returns {Promise} 响应结果
 */
export function deleteAppointment(params) {
  return request.post('/academicAppointments/project/delete', params)
}

/**
 * 审核学术任职
 * @param {Object} data - 额外数据，包含审核人信息
 * @returns {Promise} 响应结果
 */
export function reviewAppointment(data) {
  return request.post('/academicAppointments/project/review', data)
}

/**
 * 导入学术任职数据
 * @param {File} file - 上传的Excel文件
 * @returns {Promise} 响应结果
 */
export function importAppointments(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/academicAppointments/projects/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出学术任职数据
 * @param {Object} params - 查询参数
 * @returns {Promise} 响应结果
 */
export function exportAppointments(params) {
  return request.post('/academicAppointments/projects/export', params, { responseType: 'blob' })
    .then(response => {
      // 处理文件下载
      const fileName = params.fileName || '学术任职数据.xlsx'
      saveAs(new Blob([response]), fileName)
      return { code: 200, message: '导出成功' }
    })
}

/**
 * 获取学术任职时间分布
 * @param {Object} data - 请求参数
 * @returns {Promise} 响应结果
 */
export function getAppointmentYearlyTrend(data) {
  return request.post('/academicAppointments/statistics/time-distribution', data || {})
}

/**
 * 获取学术任职级别分布数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getAppointmentLevelDistribution(params = {}) {
  return request.post('/academicAppointments/statistics/level-distribution', params)
}

/**
 * 获取教师学术任职排名数据
 * @param {Object} params - 查询参数，包括range、reviewStatus和limit
 * @returns {Promise} - 请求结果
 */
export function getTeacherAppointmentRanking(params = {}) {
  return request.post('/academicAppointments/statistics/teacher-ranking', params)
}

/**
 * 获取教师学术任职详情
 * @param {Object} data
 * @returns Promise
 */
export function getTeacherAppointmentDetails(data) {
  return request.post('/academicAppointments/statistics/teacher-project-details', data)
}

/**
 * 获取学术任职统计概览数据
 * @param {Object} params - 查询参数，包括userId
 * @returns {Promise} - 请求结果
 */
export function getAppointmentStatistics(params = {}) {
  return request.post('/academicAppointments/statistics/overview', params)
}

/**
 * 获取审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getReviewStatusOverview(params = {}) {
  return request.post('/academicAppointments/statistics/review-status-overview', params)
}

/**
 * 获取学术任职统计数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */

export function getAcademicAppointmentsTotalScore(params) {
  return request.post('/academicAppointments/statistics/appointments-total-score', params);
}

/**
 * 获取用户学术任职详情
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */

export function getUserAcademicAppointmentsDetail(params) {
  return request.post('/academicAppointments/user/appointment-details', params);
}

/**重新提交审核 */
export function reapplyReview(params) {
  return request.post('/academicAppointments/reapply', params)
}
