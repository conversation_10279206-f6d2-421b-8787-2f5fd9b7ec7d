<template>
  <a-layout class="login-box">
    <a-layout-header class="login-logo">
      <div class="jnu-header-logo">
        <img src="https://jnumed.jnu.edu.cn/_upload/tpl/02/6a/618/template618/images/logo.png" height="35"/>
        <span class="jnu-header-title">暨南大学基础医学与公共卫生学院教师绩效评定与管理平台</span>
      </div>
    </a-layout-header>
    <a-layout-content class="login-content">
      <a-row align="middle" justify="center" class="login-content-main">
        <a-col class="login-content-bgc" :pull="1">
          <img class="jnu-logo" :src="'https://jnumed.jnu.edu.cn/_upload/tpl/02/6a/618/template618/images/logo.png'" width="180">
          <h2>{{setting.websiteInfo.desc || '暨南大学基础医学与公共卫生学院教师绩效评定与管理平台'}}</h2>
          <p style="margin-top: 15px">
            <a href='https://jnumed.jnu.edu.cn/main.htm' target="_blank" class="jnu-link">暨南大学基础医学与公共卫生学院官网</a>
          </p>
        </a-col>

        <a-col class="login-content-form">
          <h2 class="form-title">教师登录</h2>
          <LoginForm/>
          <RegisterForm/>
        </a-col>
      </a-row>
    </a-layout-content>
    <a-layout-footer class="login-footer">
      <div v-if="setting.reference.show" class="copyright">{{ ` Copyright ©${new Date().getFullYear()} by` }}<a target="_blank" class="out-link"
                                                                                  :href="setting.reference.authorizationUrl">@{{
          setting.reference.authorization
        }}</a>
        . All rights reserved. | <a target="_blank" class="out-link"
                                    href="https://beian.miit.gov.cn/#/Integrated/index">{{
            setting.reference.number
          }}</a>
      </div>
    </a-layout-footer>
  </a-layout>
</template>

<script setup>
import ZyHeaderLogo from "comps/common/ZyHeaderLogo.vue";
import {reactive, ref, computed} from 'vue';
import {UserOutlined, LockOutlined} from '@ant-design/icons-vue';
import {useAuthStore} from '../../../stores/auth.js';
import LoginForm from "./components/LoginForm.vue";
import RegisterForm from "./components/RegisterForm.vue";
import ZyLogo from "comps/common/ZyLogo.vue";
import setting from "@/setting.js";

const authStore = useAuthStore()
const formState = reactive({
  username: 'admin',
  password: 'admin',
  code: '',
  remember: true,
});
const onFinish = values => {
  console.log('Success:', values);
  authStore.login()

};
const onFinishFailed = errorInfo => {
  console.log('Failed:', errorInfo);
};
const disabled = computed(() => {
  return !(formState.username && formState.password);
});

</script>

<style lang="scss" scoped>
$color-bg: #ffffff;
$jnu-blue: #7fc7a0;
$jnu-gold: #e6bf7c;

.login-box {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: $color-bg;
  background-image: linear-gradient(135deg, 
                    rgba(0, 62, 126, 0.05) 25%, 
                    rgba(218, 165, 32, 0.05) 25%, 
                    rgba(218, 165, 32, 0.05) 50%, 
                    rgba(0, 62, 126, 0.05) 50%, 
                    rgba(0, 62, 126, 0.05) 75%, 
                    rgba(218, 165, 32, 0.05) 75%, 
                    rgba(218, 165, 32, 0.05) 100%);
  background-size: 40px 40px;

  .login-logo, .login-footer {
    color: $jnu-blue;
    background-color: $color-bg;
  }

  .login-logo {
    border-bottom: 2px solid $jnu-blue;
    
    .jnu-header-logo {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 15px;
      
      img {
        margin-right: 8px;
        max-height: 35px;
        width: auto;
      }
      
      .jnu-header-title {
        font-size: 1.2rem;
        font-weight: bold;
        color: $jnu-blue;
      }
    }
  }

  .login-content {
    height: 100%;

    .login-content-main {
      height: 100%;

      .login-content-bgc {
        text-align: center;
        padding-bottom: 100px;
        
        h2 {
          color: $jnu-blue;
          font-weight: bold;
          margin-top: 20px;
        }
        
        .jnu-logo {
          border-radius: 0;
          padding: 6px;
          background-color: white;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
          margin-bottom: 15px;
          max-height: 60px;
          width: auto;
        }
        
        .jnu-link {
          display: inline-block;
          padding: 8px 16px;
          background-color: $jnu-blue;
          color: white;
          border-radius: 4px;
          text-decoration: none;
          font-weight: bold;
          transition: all 0.3s ease;
          
          &:hover {
            background-color: darken($jnu-blue, 10%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
          }
        }
      }

      .login-content-form {
        border: 1px solid #e3e3e3;
        padding: 40px;
        border-radius: 8px;
        overflow: hidden;
        box-sizing: border-box;
        background-color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        
        .form-title {
          color: $jnu-blue;
          text-align: center;
          margin-bottom: 20px;
          font-weight: bold;
        }

        .login-title {
          margin-bottom: 30px;
        }

        .login-form {
          max-width: 400px;
          background-color: #fff;
          overflow: hidden;

          .login-form-button {
            margin-right: 15px;
            background-color: $jnu-blue;
            border-color: $jnu-blue;
            
            &:hover {
              background-color: darken($jnu-blue, 10%);
              border-color: darken($jnu-blue, 10%);
            }
          }
        }
      }
    }
  }

  .login-footer {
    text-align: center;
    font-size: .8rem;
    color: #828181;
    
    .out-link {
      color: $jnu-blue;
      &:hover {
        color: $jnu-gold;
      }
    }
  }
}
</style>
