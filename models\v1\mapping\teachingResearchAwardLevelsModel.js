/**
 * 教学科技奖励级别模型
 * 遵循项目命名规范：驼峰命名法
 */

const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const TeachingResearchAwardLevels = sequelize.define('TeachingResearchAwardLevels', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: '级别ID'
  },
  levelName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '级别名称'
  },
  score: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '基础分数'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '级别描述'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1-启用，0-禁用）'
  }
}, {
  tableName: 'teaching_research_award_levels',
  timestamps: true,
  indexes: [
    {
      name: 'uk_level_name',
      unique: true,
      fields: ['levelName']
    },
    {
      name: 'idx_level_status',
      fields: ['status']
    },
    {
      name: 'idx_level_score',
      fields: ['score']
    }
  ]
});

// 建立模型关联关系
const setupAssociations = () => {
  const TeachingResearchAwards = require('./teachingResearchAwardsModel');

  // 与奖励的关联
  TeachingResearchAwardLevels.hasMany(TeachingResearchAwards, {
    foreignKey: 'awardLevelId',
    as: 'awards'
  });
};

// 延迟设置关联以避免循环依赖
setTimeout(setupAssociations, 0);

module.exports = TeachingResearchAwardLevels;
