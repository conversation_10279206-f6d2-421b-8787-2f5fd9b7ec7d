// 性能管理页面表单样式
// 用于统一所有性能页面的表单样式

// 基础表单样式
.performance-form {
  .ant-form-item {
    margin-bottom: 16px;
    
    .ant-form-item-label {
      font-weight: 500;
      color: #262626;
      
      > label {
        font-size: 14px;
        
        &.ant-form-item-required {
          &::before {
            color: #ff4d4f;
          }
        }
      }
    }
    
    .ant-form-item-control {
      .ant-form-item-control-input {
        .ant-input,
        .ant-select,
        .ant-date-picker,
        .ant-input-number {
          border-radius: 6px;
          
          &:focus,
          &.ant-input-focused,
          &.ant-select-focused {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
        
        .ant-input-affix-wrapper {
          border-radius: 6px;
          
          &:focus,
          &.ant-input-affix-wrapper-focused {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }
      
      .ant-form-item-explain {
        font-size: 12px;
        margin-top: 4px;
        
        &.ant-form-item-explain-error {
          color: #ff4d4f;
        }
      }
    }
  }
  
  // 行内表单样式
  &.inline-form {
    .ant-form-item {
      margin-bottom: 8px;
      margin-right: 16px;
      display: inline-block;
      
      .ant-form-item-label {
        margin-bottom: 4px;
      }
    }
  }
}

// 模态框表单样式
.modal-form {
  .ant-form-item {
    margin-bottom: 20px;
    
    .ant-form-item-label {
      padding-bottom: 8px;
    }
  }
  
  // 文件上传区域
  .upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    background-color: #fafafa;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #1890ff;
    }
    
    &.dragover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
    
    .upload-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }
    
    .upload-text {
      color: #666;
      font-size: 14px;
      
      .upload-hint {
        color: #999;
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }
  
  // 文件列表样式
  .upload-file-list {
    margin-top: 12px;
    
    .upload-file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      margin-bottom: 8px;
      background-color: #fafafa;
      
      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        
        .file-icon {
          margin-right: 8px;
          color: #1890ff;
        }
        
        .file-name {
          font-size: 14px;
          color: #262626;
          margin-right: 8px;
        }
        
        .file-size {
          font-size: 12px;
          color: #999;
        }
      }
      
      .file-actions {
        display: flex;
        gap: 8px;
        
        .ant-btn {
          padding: 4px 8px;
          height: auto;
          font-size: 12px;
        }
      }
      
      &.file-error {
        border-color: #ff4d4f;
        background-color: #fff2f0;
        
        .file-name {
          color: #ff4d4f;
        }
      }
      
      &.file-uploading {
        border-color: #1890ff;
        background-color: #f0f8ff;
      }
    }
  }
}

// 作者列表表单样式
.authors-container {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
  
  .authors-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .authors-title {
      font-weight: 600;
      color: #262626;
    }
    
    .authors-count {
      font-size: 12px;
      color: #999;
    }
  }
  
  .author-input-row {
    display: flex;
    gap: 12px;
    align-items: end;
    margin-bottom: 12px;
    
    .author-select {
      flex: 2;
    }
    
    .author-rank {
      flex: 1;
      min-width: 80px;
    }
    
    .author-ratio {
      flex: 1;
      min-width: 100px;
    }
    
    .author-flags {
      flex: 1;
      min-width: 120px;
      
      .ant-checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        .ant-checkbox-wrapper {
          font-size: 12px;
        }
      }
    }
    
    .author-actions {
      .ant-btn {
        height: 32px;
      }
    }
  }
  
  .authors-list {
    .author-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      margin-bottom: 8px;
      background-color: white;
      
      .author-info {
        flex: 1;
        
        .author-name {
          font-weight: 500;
          color: #262626;
        }
        
        .author-details {
          font-size: 12px;
          color: #666;
          margin-top: 2px;
          
          .author-tag {
            display: inline-block;
            padding: 2px 6px;
            background-color: #f0f0f0;
            border-radius: 2px;
            margin-right: 4px;
            font-size: 11px;
          }
        }
      }
      
      .author-actions {
        .ant-btn {
          padding: 4px 8px;
          height: auto;
          font-size: 12px;
        }
      }
    }
  }
  
  .authors-empty {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 14px;
  }
}

// 表单区域分组样式
.form-section {
  margin-bottom: 24px;
  
  .form-section-label {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    
    label {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }
  
  .form-section-content {
    padding-left: 0;
  }
}

// 响应式表单样式
@media (max-width: 768px) {
  .performance-form {
    .ant-form-item {
      margin-bottom: 12px;
    }
    
    &.inline-form {
      .ant-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: 12px;
      }
    }
  }
  
  .modal-form {
    .upload-area {
      padding: 16px;
      
      .upload-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }
    }
  }
  
  .authors-container {
    padding: 12px;
    
    .author-input-row {
      flex-direction: column;
      gap: 8px;
      
      .author-select,
      .author-rank,
      .author-ratio,
      .author-flags {
        flex: none;
        width: 100%;
        min-width: auto;
      }
      
      .author-flags {
        .ant-checkbox-group {
          flex-direction: row;
          flex-wrap: wrap;
        }
      }
    }
    
    .author-item {
      flex-direction: column;
      align-items: stretch;
      
      .author-info {
        margin-bottom: 8px;
      }
      
      .author-actions {
        align-self: flex-end;
      }
    }
  }
}

@media (max-width: 576px) {
  .modal-form {
    .upload-file-list {
      .upload-file-item {
        flex-direction: column;
        align-items: stretch;
        
        .file-info {
          margin-bottom: 8px;
        }
        
        .file-actions {
          justify-content: center;
        }
      }
    }
  }
}
