const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const multer = require('multer');
const { Op } = require('sequelize');
const { getTimeIntervalByName, getUserInfoFromRequest, getSequelizeInstance,isProjectInTimeRange } = require('../../../utils/others');
const patentModel = require('../../../models/v1/mapping/patentsModel');
const patentCategoryModel = require('../../../models/v1/mapping/patentCategoriesModel');
const patentParticipantModel = require('../../../models/v1/mapping/patentParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const fileModel = require('../../../models/v1/mapping/fileModel');
const upload = multer({ dest: 'uploads/' });
const { updateUserRankings } = require('../../../utils/rankingUtils');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');

/**
 * 获取专利列表（统一接口）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPatents = async (req, res) => {
  try {
    const { 
      patentName, 
      categoryId, 
      authorizationDateStart, 
      authorizationDateEnd, 
      conversionDateStart,
      conversionDateEnd,
      userId, 
      page = 1, 
      pageSize = 10,
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'all'(全部), 'reviewed'(已审核), 'unreviewed'(未审核)
      isExport = false // 是否是导出操作
    } = req.body;
    
    // 构建查询条件
    const where = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'reject') {
      where.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null; // 待审核
    } else if (reviewStatus === 'all') {
      // 不添加筛选条件
    }
    // 'all' 状态不添加筛选条件
    
    if (patentName) {
      where.patentName = { [Op.like]: `%${patentName}%` };
    }
    
    if (categoryId) {
      where.categoryId = categoryId;
    }
    
    if (authorizationDateStart) {
      where.authorizationDate = { ...where.authorizationDate, [Op.gte]: authorizationDateStart };
    }
    
    if (authorizationDateEnd) {
      where.authorizationDate = { ...where.authorizationDate, [Op.lte]: authorizationDateEnd };
    }
    
    if (conversionDateStart) {
      where.conversionDate = { ...where.conversionDate, [Op.gte]: conversionDateStart };
    }
    
    if (conversionDateEnd) {
      where.conversionDate = { ...where.conversionDate, [Op.lte]: conversionDateEnd };
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("patents");
    
    // 根据range参数添加到数据库查询条件中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内 - 复杂条件：任一日期在范围内或两者都在范围内
        const authorizationInRange = {
          authorizationDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        const conversionInRange = {
          conversionDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 两者都存在时的条件
        const bothDatesExist = {
          [Op.and]: [
            { authorizationDate: { [Op.ne]: null } },
            { conversionDate: { [Op.ne]: null } }
          ]
        };
        
        // 只有授权日期存在时的条件
        const onlyAuthExist = {
          [Op.and]: [
            { authorizationDate: { [Op.ne]: null } },
            { conversionDate: null }
          ]
        };
        
        // 只有转化日期存在时的条件
        const onlyConvExist = {
          [Op.and]: [
            { authorizationDate: null },
            { conversionDate: { [Op.ne]: null } }
          ]
        };
        
        // 构建最终的范围内条件
        where[Op.or] = [
          // 两个日期都存在且都在范围内
          {
            [Op.and]: [
              bothDatesExist,
              authorizationInRange,
              conversionInRange
            ]
          },
          // 只有授权日期存在且在范围内
          {
            [Op.and]: [
              onlyAuthExist,
              authorizationInRange
            ]
          },
          // 只有转化日期存在且在范围内
          {
            [Op.and]: [
              onlyConvExist,
              conversionInRange
            ]
          }
        ];
      } else if (range === 'out') {
        // 在时间范围外 - 两者都存在时两者都在范围外，只有一个存在时该字段在范围外
        const authorizationOutRange = {
          [Op.or]: [
            { authorizationDate: { [Op.lt]: intervalStartTime } },
            { authorizationDate: { [Op.gt]: intervalEndTime } }
          ]
        };
        
        const conversionOutRange = {
          [Op.or]: [
            { conversionDate: { [Op.lt]: intervalStartTime } },
            { conversionDate: { [Op.gt]: intervalEndTime } }
          ]
        };
        
        // 两者都存在时的条件
        const bothDatesExist = {
          [Op.and]: [
            { authorizationDate: { [Op.ne]: null } },
            { conversionDate: { [Op.ne]: null } }
          ]
        };
        
        // 只有授权日期存在时的条件
        const onlyAuthExist = {
          [Op.and]: [
            { authorizationDate: { [Op.ne]: null } },
            { conversionDate: null }
          ]
        };
        
        // 只有转化日期存在时的条件
        const onlyConvExist = {
          [Op.and]: [
            { authorizationDate: null },
            { conversionDate: { [Op.ne]: null } }
          ]
        };
        
        // 构建最终的范围外条件
        where[Op.or] = [
          // 两个日期都存在且都在范围外
          {
            [Op.and]: [
              bothDatesExist,
              authorizationOutRange,
              conversionOutRange
            ]
          },
          // 只有授权日期存在且在范围外
          {
            [Op.and]: [
              onlyAuthExist,
              authorizationOutRange
            ]
          },
          // 只有转化日期存在且在范围外
          {
            [Op.and]: [
              onlyConvExist,
              conversionOutRange
            ]
          }
        ];
      }
    }
    
    // 如果提供了userId，添加用户筛选条件
    if (userId) {
      // 查询用户参与的专利ID
      const participations = await patentParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['patentId']
      });
      
      const patentIds = participations.map(p => p.patentId);
      
      // 查询用户作为第一负责人的专利ID
      const firstResponsiblePatents = await patentModel.findAll({
        where: { firstResponsibleID: userId },
        attributes: ['id']
      });
      
      const firstResponsiblePatentIds = firstResponsiblePatents.map(p => p.id);
      
      // 合并两种情况的专利ID
      const allPatentIds = [...new Set([...patentIds, ...firstResponsiblePatentIds])];
      
      if (allPatentIds.length > 0) {
        // 如果之前已有where[Op.or]条件，需要合并条件
        if (where[Op.or]) {
          const previousOrCondition = where[Op.or];
          where[Op.and] = [
            { id: { [Op.in]: allPatentIds } },
            { [Op.or]: previousOrCondition }
          ];
          delete where[Op.or]; // 移除旧的条件
        } else {
          where.id = { [Op.in]: allPatentIds };
        }
      } else {
        // 用户没有参与任何专利，返回空结果
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: {
            list: [],
            pagination: {
              page: pageNum,
              pageSize: pageSizeNum,
              total: 0,
              totalPages: 0
            }
          }
        });
      }
    }
    
    console.log('查询条件:', JSON.stringify(where, null, 2));
    
    // 查询专利数据
    const { count, rows } = await patentModel.findAndCountAll({
      where,
      offset: isExport ? undefined : offset,
      limit: isExport ? undefined : limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: patentCategoryModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });
    
    // 处理专利数据并获取参与者信息
    const processedPatents = [];
    
    for (const patent of rows) {
      const patentJson = patent.toJSON();
      
      // 查询参与者
      const participants = await patentParticipantModel.findAll({
        where: { patentId: patent.id },
        include: [
          {
            model: userModel,
            as: 'participant',
            attributes: ['id', 'nickname', 'username', 'studentNumber']
          }
        ]
      });
      
      // 添加参与者信息
      patentJson.participants = participants.map(p => p.toJSON());
      
      // 计算用户在此专利中的分配比例（如果提供了userId）
      if (userId) {
        const userParticipation = participants.find(p => p.participantId === userId);
        if (userParticipation) {
          patentJson.userAllocationRatio = userParticipation.allocationRatio;
          patentJson.isLeader = userParticipation.isLeader === 1;
        } else if (patent.firstResponsibleID === userId) {
          // 如果用户是第一负责人但不在参与者表中
          patentJson.isLeader = true;
        }
      }
      
      // 判断专利是否在时间范围内（用于前端展示，不再用于筛选）
      let isInTimeRange = false;
      if (timeInterval) {
        isInTimeRange = isPatentInTimeRange(patentJson, timeInterval.startTime, timeInterval.endTime);
      }
      
      patentJson.isInTimeRange = isInTimeRange;
      
      // 处理score字段
      if (isInTimeRange && patentJson.category) {
        patentJson.score = patentJson.category.score; // 在范围内，使用分类的分数
      } else {
        patentJson.score = 0; // 不在范围内，分数设为0
      }
      
      // 不再根据range参数筛选，直接添加到结果集合
      processedPatents.push(patentJson);
    }
    
    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: processedPatents,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取专利列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利列表失败',
      error: error.message
    });
  }
};

/**
 * 获取专利详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPatentDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少专利ID',
        data: null
      });
    }
    
    // 查询专利详情
    const patent = await patentModel.findByPk(id, {
      include: [
        {
          model: patentCategoryModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });
    
    if (!patent) {
      return res.status(404).json({
        code: 404,
        message: '未找到专利',
        data: null
      });
    }
    
    // 将专利数据转换为JSON对象
    const patentData = patent.toJSON();
    
    // 查询参与者信息
    const participants = await patentParticipantModel.findAll({
      where: { patentId: id },
      include: [
        {
          model: userModel,
          as: 'participant',
          attributes: ['id', 'nickname', 'username', 'studentNumber']
        }
      ]
    });
    
    // 添加参与者信息
    patentData.participants = participants.map(p => p.toJSON());
    
    // 查找参与者表中的负责人（兼容旧逻辑）
    const leader = participants.find(p => p.isLeader === 1);
    if (leader) {
      patentData.leader = leader.toJSON();
    }
    
    // 查询专利关联的文件列表
    const files = await fileModel.findAll({
      where: {
        relatedId: id,
        relatedType: 'patents',
        isDeleted: 0
      },
      attributes: [
        'id', 
        'fileName', 
        'originalName', 
        'filePath', 
        'fileSize', 
        'mimeType', 
        'extension',
        'uploaderId', 
        'relatedId', 
        'relatedType', 
        'createdAt', 
        'updatedAt'
      ],
      order: [['createdAt', 'DESC']]
    });
    
    // 处理文件信息，添加URL和其他前端需要的信息
    patentData.attachments = files.map(file => {
      const fileData = file.toJSON();
      // 构造文件URL
      const filePath = fileData.filePath;
      const url = filePath.startsWith('/') ? filePath : `/${filePath}`;
      
      return {
        id: fileData.id,
        name: fileData.originalName,
        fileName: fileData.fileName,
        size: fileData.fileSize,
        type: fileData.mimeType,
        extension: fileData.extension,
        url: url,
        filePath: fileData.filePath,
        uploadTime: fileData.createdAt
      };
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: patentData
    });
  } catch (error) {
    console.error('获取专利详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利详情失败',
      error: error.message
    });
  }
};

/**
 * 创建专利
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createPatent = async (req, res) => {
  // 获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(patentModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const {
      patentName,
      categoryId,
      authorizationDate,
      conversionDate,
      remark,
      participants,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl    // 接收前端传递的文件路径
    } = req.body;
    
    console.log("req.body:", req.body);
    
    // 验证必要字段
    if (!patentName || !categoryId || !authorizationDate) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少必要字段',
        data: null
      });
    }
    
    // 处理参与者数据 - 适应新的传递格式
    let participantsArray = [];
    
    // 检查 participants 是否为数组或可以转换为数组的对象
    if (participants) {
      if (Array.isArray(participants)) {
        participantsArray = participants;
      } else if (typeof participants === 'object' && participants !== null) {
        // 尝试从对象中提取参与者数组
        participantsArray = Object.values(participants);
      } else if (typeof participants === 'string') {
        // 尝试解析 JSON 字符串
        try {
          participantsArray = JSON.parse(participants);
        } catch (e) {
          console.error('解析参与者 JSON 失败:', e);
        }
      }
    }
    
    // 验证是否有参与者
    if (!participantsArray || !participantsArray.length) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少参与者数据',
        data: null
      });
    }
    
    // 验证参与者格式
    const hasLeader = participantsArray.some(p => 
      p.isLeader === 1 || p.isLeader === '1' || p.isLeader === true
    );
    
    if (!hasLeader) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '必须指定至少一名负责人',
        data: null
      });
    }
    
    // 验证分配比例总和
    const totalRatio = participantsArray.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
    if (Math.abs(totalRatio - 1) > 0.01) {  // 允许0.01的误差
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '所有参与者的分配比例之和必须等于1',
        data: null
      });
    }
    
    // 获取第一负责人ID
    const leader = participantsArray.find(p => 
      p.isLeader === 1 || p.isLeader === '1' || p.isLeader === true
    );
    const firstResponsibleID = leader ? leader.participantId : null;
    
    if (!firstResponsibleID) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '无法确定第一负责人',
        data: null
      });
    }
    
    // 创建专利
    const patent = await patentModel.create({
      patentName,
      categoryId,
      authorizationDate,
      conversionDate,
      remark,
      firstResponsibleID, // 添加第一负责人ID
      // 初始设置为null，稍后使用专利ID更新
      attachmentUrl: null
    }, { transaction });
    console.log("patent:", patent);
    // 创建参与者记录
    const participantPromises = participantsArray.map(p => {
      // 确保 isLeader 值为数字类型
      const isLeader = p.isLeader === true || p.isLeader === '1' || p.isLeader === 1 ? 1 : 0;
      
      return patentParticipantModel.create({
        patentId: patent.id,
        participantId: p.participantId,
        allocationRatio: parseFloat(p.allocationRatio || 0),
        isLeader: isLeader
      }, { transaction });
    });
    
    await Promise.all(participantPromises);
    
    // 处理关联文件 - 使用前端传递的文件ID而非重新创建文件记录
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组，可能以JSON字符串形式传递
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl，可能以JSON字符串形式传递
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl === 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
      
      // 如果有文件ID，关联到专利
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            projectId: patent.id, // 设置文件的projectId为专利ID
            patentId: patent.id, // 设置patentId字段为专利ID
            relatedId: patent.id,
            relatedType: 'patents' // 使用patents作为relatedType
          };
          console.log("updateData===",updateData);
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath; // 使用filePath字段，这是文件模型中的正确字段名
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );

          processedFileIds.push(fileId);
        }
      }
    } else {
      console.log('未提供文件ID，跳过文件关联处理');
    }
    
    // 提交事务
    await transaction.commit();

    // 事务提交成功后，异步移动文件到指定的专利目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'patents'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${patent.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 新增变量跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId, isDeleted: 0 }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                patentId: patent.id,
                projectId: patent.id,
                relatedId: patent.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
              
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新专利的attachmentUrl为专利文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的专利文件夹路径，确保不包含文件名
            const patentFolderPath = `uploads\\patents\\${patent.id}\\`;
            
            await patent.update({
              attachmentUrl: patentFolderPath
            });
            console.log(`已更新专利 ${patent.id} 的attachmentUrl为: ${patentFolderPath}`);
            
          } catch (updateError) {
            console.error('更新专利attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响专利创建的返回结果，仅记录错误
        console.error('移动文件到专利目录失败:', moveError);
      }
    }
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: patent
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('创建专利失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建专利失败',
      error: error.message
    });
  }
};

/**
 * 更新专利
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updatePatent = async (req, res) => {
  try {
    // 获取数据库连接实例并创建事务
    let sequelize;
    try {
      sequelize = getSequelizeInstance(patentModel);
    } catch (error) {
      return res.status(500).json({
        code: 500,
        message: '无法获取数据库连接实例',
        error: error.message
      });
    }
    
    // 创建事务
    const transaction = await sequelize.transaction();

    const userInfo = await getUserInfoFromRequest(req);
    console.log("req.params", req.params);
    console.log("req.body", req.body);
    const { id } = req.params;
    const userId = userInfo.id;    
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少专利ID',
        data: null
      });
    }
    
    const {
      patentName,
      categoryId,
      authorizationDate,
      conversionDate,
      remark,
      participants,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl,   // 接收前端传递的文件路径
      deletedFileIds   // 要删除的文件ID
    } = req.body;
    console.log("req.body要删除的文件ID", req.body);
    
    // 查询专利是否存在
    const patent = await patentModel.findByPk(id, {
      include: [
        {
          model: patentParticipantModel,
          as: 'participants',
          required: false
        },
        {
          model: patentCategoryModel,
          as: 'category',
          required: false
        }
      ],
      transaction
    });
    
    if (!patent) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到专利',
        data: null
      });
    }
    
    // 权限检查：管理员或者第一负责人可以编辑
    const isAdmin = userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER';
    
    if (!isAdmin) {
      // 检查是否是负责人 (现在检查两个地方)
      const isFirstResponsible = patent.firstResponsibleID === userId;
      
      if (!isFirstResponsible) {
        // 如果不是专利表中的第一负责人，再检查是否在参与者表中是负责人
        const userParticipation = await patentParticipantModel.findOne({
          where: {
            patentId: id,
            participantId: userId,
            isLeader: 1
          },
          transaction
        });
        
        if (!userParticipation) {
          await transaction.rollback();
          return res.status(403).json({
            code: 403,
            message: '您没有权限编辑该专利，只能编辑自己负责的专利',
            data: null
          });
        }
      }
    }

    // 如果专利已审核，需要更新用户排名
    if (patent.ifReviewer === 1) {
      try {
        console.log('开始更新用户排名数据，专利ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("patents");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断专利变更前后是否在时间区间内（使用authorizationDate判断）
        const oldAuthorizationDate = patent.authorizationDate;
        const newAuthorizationDate = authorizationDate || patent.authorizationDate;
        console.log('专利授权日期 - 原始:', oldAuthorizationDate, '新:', newAuthorizationDate);
        
        const wasInTimeRange = timeInterval ? 
          isProjectInTimeRange(oldAuthorizationDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
          
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(newAuthorizationDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('专利时间范围状态 - 原始:', wasInTimeRange ? '在范围内' : '不在范围内', 
                   '变更后:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表（针对减分操作）
        let oldRankingTables = [];
        if (wasInTimeRange) {
          oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        // 确定需要更新的排名表（针对加分操作）
        let newRankingTables = [];
        if (isInTimeRange) {
          newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('排名表 - 原始:', oldRankingTables, '新:', newRankingTables);
        
        // 获取旧的分类分数
        const oldBaseScore = patent.category ? patent.category.score : 0;
        console.log('原始分类分数:', oldBaseScore);
        
        // 获取新的分类分数
        let newBaseScore = oldBaseScore;
        if (categoryId && categoryId !== patent.categoryId) {
          const newCategory = await patentCategoryModel.findByPk(categoryId, { transaction });
          if (newCategory) {
            newBaseScore = newCategory.score || 0;
          }
        }
        console.log('新分类分数:', newBaseScore);
        
        // 获取旧的参与者名单
        const oldParticipants = patent.participants || [];
        console.log('原始参与者数量:', oldParticipants.length);
        
        // 准备新的参与者列表
        let newParticipants = [];
        
        // 处理参与者数据 - 适应新的传递格式
        if (participants) {
          if (Array.isArray(participants)) {
            newParticipants = participants;
          } else if (typeof participants === 'object' && participants !== null) {
            // 尝试从对象中提取参与者数组
            newParticipants = Object.values(participants);
          } else if (typeof participants === 'string') {
            // 尝试解析 JSON 字符串
            try {
              newParticipants = JSON.parse(participants);
            } catch (e) {
              console.error('解析参与者 JSON 失败:', e);
            }
          }
        }
        console.log('新参与者数量:', newParticipants.length);
        
        // 从原始参与者中找出要删除的参与者 - 他们在旧列表中但不在新列表中
        const oldUserIds = oldParticipants.map(p => p.participantId);
        const newUserIds = newParticipants.map(p => p.participantId);
        
        // 找出要删除的参与者
        const deletedUserIds = oldUserIds.filter(id => !newUserIds.includes(id));
        console.log('要删除的参与者:', deletedUserIds);
        
        // 找出保留的参与者
        const keptUserIds = oldUserIds.filter(id => newUserIds.includes(id));
        console.log('保留的参与者:', keptUserIds);
        
        // 找出新增的参与者
        const addedUserIds = newUserIds.filter(id => !oldUserIds.includes(id));
        console.log('新增的参与者:', addedUserIds);
        
        // 1. 处理要删除的参与者 - 减少项目数量和分数
        if (deletedUserIds.length > 0) {
          const deletedParticipants = oldParticipants.filter(p => deletedUserIds.includes(p.participantId));
          console.log('被删除的参与者完整数据:', JSON.stringify(deletedParticipants));
          
          const deletedIds = [];
          const deletedRatios = [];
          const deletedScores = [];
          const countDeltas = []; // 固定值为1的数组表示每人减少1个项目
          
          for (const participant of deletedParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            deletedIds.push(userId);
            deletedRatios.push(ratio);
            deletedScores.push(participantScore);
            countDeltas.push(1); // 每个被删除的参与者项目数-1
          }
          
          console.log('被删除参与者的排名更新数据:', {
            userIds: deletedIds,
            countDeltas: countDeltas,
            scores: deletedScores
          });
          
          // 对每个排名表执行减分操作
          for (const table of oldRankingTables) {
            console.log(`为被删除的参与者更新排名表 ${table}`);
            await updateUserRankings(
              deletedIds,
              table,
              'patents',
              countDeltas, // 使用固定值1表示项目计数-1
              deletedScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去被删除参与者的分数和项目数');
        }
        
        // 2. 处理保留但分配比例变化的参与者 - 先减去原有分数，后面再加上新分数
        if (keptUserIds.length > 0) {
          const keptOldParticipants = oldParticipants.filter(p => keptUserIds.includes(p.participantId));
          console.log('保留的参与者原始数据:', JSON.stringify(keptOldParticipants));
          
          const keptIds = [];
          const keptRatios = [];
          const keptScores = [];
          
          for (const participant of keptOldParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            keptIds.push(userId);
            keptRatios.push(ratio);
            keptScores.push(participantScore);
          }
          
          console.log('保留参与者的减分数据:', {
            userIds: keptIds,
            scores: keptScores
          });
          
          // 减去原有分数（但不减少项目计数）
          for (const table of oldRankingTables) {
            console.log(`为保留的参与者减去原有分数：${table}`);
            // 传递0表示不改变项目计数，只改变分数
            const zeroCounts = Array(keptIds.length).fill(0);
            await updateUserRankings(
              keptIds,
              table,
              'patents',
              zeroCounts, // 项目计数不变
              keptScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去保留参与者的原有分数');
        }
        
        // 3. 处理所有新参与者名单（包括保留的和新增的）- 增加分数，对新增的也增加项目数
        if (newParticipants.length > 0) {
          console.log('新参与者完整数据:', JSON.stringify(newParticipants));
          
          const allNewIds = [];
          const allNewRatios = [];
          const allNewScores = [];
          const allCountDeltas = []; // 对新增参与者设为1，对保留的参与者设为0
          
          for (const participant of newParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = newBaseScore * ratio;
            
            allNewIds.push(userId);
            allNewRatios.push(ratio);
            allNewScores.push(participantScore);
            
            // 对新增参与者项目数+1，对保留的参与者项目数不变
            if (addedUserIds.includes(userId)) {
              allCountDeltas.push(1);
            } else {
              allCountDeltas.push(0);
            }
          }
          
          console.log('所有新参与者的加分数据:', {
            userIds: allNewIds,
            countDeltas: allCountDeltas,
            scores: allNewScores
          });
          
          // 为所有参与者添加分数，但只为新增参与者增加项目计数
          for (const table of newRankingTables) {
            console.log(`为所有参与者更新排名表：${table}`);
            await updateUserRankings(
              allNewIds,
              table,
              'patents',
              allCountDeltas, // 使用差异化的计数更新：新增的+1，保留的不变
              allNewScores,
              transaction,
              "add" // 加分操作
            );
          }
          console.log('成功更新所有参与者的分数和新增参与者的项目数');
        }
        
        console.log('成功完成专利参与者的排名数据更新');
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 更新专利基本信息
    const updateData = {};
    if (patentName !== undefined) updateData.patentName = patentName;
    if (categoryId !== undefined) updateData.categoryId = categoryId;
    if (authorizationDate !== undefined) updateData.authorizationDate = authorizationDate;
    if (conversionDate !== undefined) updateData.conversionDate = conversionDate;
    if (remark !== undefined) updateData.remark = remark;
    // 设置标准化的附件文件夹路径
    updateData.attachmentUrl = `uploads\\patents\\${id}\\`;
    
    // 处理参与者数据 - 适应新的传递格式
    let participantsArray = [];
    
    // 检查 participants 是否为数组或可以转换为数组的对象
    if (participants) {
      if (Array.isArray(participants)) {
        participantsArray = participants;
      } else if (typeof participants === 'object' && participants !== null) {
        // 尝试从对象中提取参与者数组
        participantsArray = Object.values(participants);
      } else if (typeof participants === 'string') {
        // 尝试解析 JSON 字符串
        try {
          participantsArray = JSON.parse(participants);
        } catch (e) {
          console.error('解析参与者 JSON 失败:', e);
        }
      }
    }
    
    // 处理参与者更新逻辑
    if (participantsArray && participantsArray.length > 0) {
      // 验证参与者格式
      const hasLeader = participantsArray.some(p => 
        p.isLeader === 1 || p.isLeader === '1' || p.isLeader === true
      );
      
      if (!hasLeader) {
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '必须指定至少一名负责人',
          data: null
        });
      }
      
      // 验证分配比例总和
      const totalRatio = participantsArray.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
      if (Math.abs(totalRatio - 1) > 0.01) {  // 允许0.01的误差
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '所有参与者的分配比例之和必须等于1',
          data: null
        });
      }
      
      // 获取第一负责人ID
      const leader = participantsArray.find(p => 
        p.isLeader === 1 || p.isLeader === '1' || p.isLeader === true
      );
      
      if (leader) {
        updateData.firstResponsibleID = leader.participantId;
      }
    }
    
    await patent.update(updateData, { transaction });
    
    // 如果有参与者信息，更新参与者
    if (participantsArray && participantsArray.length > 0) {
      // 删除旧的参与者记录
      await patentParticipantModel.destroy({
        where: { patentId: id },
        transaction
      });
      
      // 创建新的参与者记录
      const participantPromises = participantsArray.map(p => {
        // 确保 isLeader 值为数字类型
        const isLeader = p.isLeader === true || p.isLeader === '1' || p.isLeader === 1 ? 1 : 0;
        
        return patentParticipantModel.create({
          patentId: patent.id,
          participantId: p.participantId,
          allocationRatio: parseFloat(p.allocationRatio || 0),
          isLeader: isLeader
        }, { transaction });
      });
      
      await Promise.all(participantPromises);
    }
    
    // 处理要删除的文件
    if (deletedFileIds) {
      let deletedFileIdArray = [];
      
      // 解析要删除的文件ID数组
      if (typeof deletedFileIds === 'string') {
        try {
          deletedFileIdArray = JSON.parse(deletedFileIds);
        } catch (error) {
          console.error('解析要删除的文件ID出错:', error);
          deletedFileIdArray = [deletedFileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(deletedFileIds)) {
        deletedFileIdArray = deletedFileIds;
      }
      
      // 标记文件为已删除
      if (deletedFileIdArray.length > 0) {
        await fileModel.update(
          { isDeleted: 1 },
          { 
            where: { id: { [Op.in]: deletedFileIdArray } },
            transaction
          }
        );
      }
    }
    
    // 处理文件关联
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl === 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
      
      // 如果有文件ID，更新关联关系
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            projectId: patent.id, // 设置文件的projectId为专利ID
            patentId: patent.id, // 设置patentId字段为专利ID
            relatedId: patent.id,
            relatedType: 'patents'
          };
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath;
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );
          
          processedFileIds.push(fileId);
        }
      }
    } else {
      console.log('未提供文件ID，跳过文件关联处理');
    }
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，异步移动文件到指定的专利目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'patents'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${patent.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId, isDeleted: 0 }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                patentId: patent.id,
                projectId: patent.id,
                relatedId: patent.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新专利的attachmentUrl为专利文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            const patentFolderPath = `uploads\\patents\\${patent.id}\\`;
            
            await patent.update({
              attachmentUrl: patentFolderPath
            });
            console.log(`已更新专利 ${patent.id} 的attachmentUrl为: ${patentFolderPath}`);
          } catch (updateError) {
            console.error('更新专利attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响专利更新的返回结果，仅记录错误
        console.error('移动文件到专利目录失败:', moveError);
      }
    }
    
    // 重新加载最新数据
    const updatedPatent = await patentModel.findByPk(id, {
      include: [
        {
          model: patentCategoryModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });
    
    // 查询更新后的参与者信息
    const updatedParticipants = await patentParticipantModel.findAll({
      where: { patentId: id },
      include: [
        {
          model: userModel,
          as: 'participant',
          attributes: ['id', 'nickname', 'username', 'studentNumber']
        }
      ]
    });
    
    // 组装返回数据
    const responseData = updatedPatent.toJSON();
    responseData.participants = updatedParticipants.map(p => p.toJSON());
    
    // 查询关联的文件
    const files = await fileModel.findAll({
      where: {
        relatedId: id,
        relatedType: 'patents',
        isDeleted: 0
      }
    });
    
    // 添加文件信息到响应数据
    responseData.attachments = files.map(file => file.toJSON());
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: responseData
    });
  } catch (error) {
    console.error('更新专利失败:', error);
    // 确保事务回滚
    if (transaction) {
      await transaction.rollback();
    }
    return res.status(500).json({
      code: 500,
      message: '更新专利失败',
      error: error.message
    });
  }
};

/**
 * 删除专利
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deletePatent = async (req, res) => {
  // 获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(patentModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const userInfo = await getUserInfoFromRequest(req);
    const userId = userInfo.id;
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少专利ID',
        data: null
      });
    }
    
    // 查询专利是否存在，添加关联查询以获取参与者和分类信息
    const patent = await patentModel.findByPk(id, {
      include: [
        {
          model: patentParticipantModel,
          as: 'participants',
          required: false
        },
        {
          model: patentCategoryModel,
          as: 'category',
          required: false
        }
      ],
      transaction
    });
    
    if (!patent) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到专利',
        data: null
      });
    }
    
    // 权限检查：管理员或者第一负责人可以删除
    const isAdmin = userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER';
    
    if (!isAdmin) {
      // 检查是否是第一负责人
      const isFirstResponsible = patent.firstResponsibleID === userId;
      
      if (!isFirstResponsible) {
        // 如果不是专利表中的第一负责人，再检查是否在参与者表中是负责人
        const userParticipation = await patentParticipantModel.findOne({
          where: {
            patentId: id,
            participantId: userId,
            isLeader: 1
          },
          transaction
        });
        
        if (!userParticipation) {
          await transaction.rollback();
          return res.status(403).json({
            code: 403,
            message: '您没有权限删除该专利，只能删除自己负责的专利',
            data: null
          });
        }
      }
    }
    
    // 如果专利已审核，需要更新用户排名
    if (patent.ifReviewer === 1) {
      try {
        console.log('开始处理删除专利的排名更新，专利ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("patents");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断专利是否在时间区间内（使用authorizationDate判断）
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(patent.authorizationDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('专利时间范围状态:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('需要更新的排名表:', rankingTables);
        
        // 获取专利的分类分数
        const baseScore = patent.category ? patent.category.score : 0;
        console.log('专利分类分数:', baseScore);
        
        // 获取专利的所有参与者
        const participants = patent.participants || [];
        console.log('专利参与者数量:', participants.length);
        
        // 如果有参与者，批量处理减分操作
        if (participants.length > 0) {
          // 准备批量更新的数据
          const userIds = [];
          const scores = [];
          
          // 收集所有参与者数据
          for (const participant of participants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = baseScore * ratio;
            
            userIds.push(userId);
            scores.push(participantScore);
          }
          
          console.log('删除专利的参与者数据:', {
            userIds: userIds,
            scores: scores
          });
          
          // 创建固定值为1的计数数组，表示每个用户项目数-1
          const countDeltas = Array(userIds.length).fill(1);
          
          // 对每个排名表执行减分操作（一次性批量处理所有参与者）
          for (const table of rankingTables) {
            console.log(`为被删除专利的所有参与者更新排名表 ${table}`);
            await updateUserRankings(
              userIds,
              table,
              'patents',
              countDeltas, // 使用固定值1表示项目计数-1
              scores, // 使用参与者分数数组
              transaction,
              "subtract" // 减分操作
            );
          }
          
          console.log('成功从排名表中减去专利参与者的分数和项目数');
        }
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 查找专利关联的文件
    const patentFiles = await fileModel.findAll({
      where: { 
        [Op.or]: [
          { relatedId: id, relatedType: 'patents' },
          { projectId: id, relatedType: 'patents' }
        ]
      },
      transaction
    });
    
    // 收集文件夹路径
    let patentFolderPaths = new Set();
    
    if (patentFiles && patentFiles.length > 0) {
      console.log(`找到${patentFiles.length}个与专利关联的文件记录`);
      
      // 从文件路径中提取专利文件夹路径
      for (const file of patentFiles) {
        if (file.filePath) {
          try {
            // 从filePath中提取专利文件夹路径
            const folderPath = file.filePath.substring(0, file.filePath.lastIndexOf('\\'));
            if (folderPath.includes('patents')) {
              patentFolderPaths.add(folderPath);
            }
          } catch (error) {
            console.error('处理文件路径时出错:', error);
          }
        }
      }
      
      // 硬删除文件记录，而不是标记为已删除
      await fileModel.destroy({
        where: { 
          [Op.or]: [
            { relatedId: id, relatedType: 'patents' },
            { projectId: id, relatedType: 'patents' }
          ]
        },
        transaction
      });
      
      console.log(`已硬删除${patentFiles.length}条文件记录`);
    } else {
      console.log('未找到与专利关联的文件记录');
    }
    
    // 删除专利参与者记录
    await patentParticipantModel.destroy({
      where: { patentId: id },
      transaction
    });
    
    // 删除专利
    await patent.destroy({ transaction });
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，异步删除文件系统中的文件
    try {
      // 导入文件控制器
      const fileController = require('../common/fileController');
      
      // 构造专利文件夹路径
      const patentFolderPath = `uploads/patents/${id}`;
      console.log(`尝试删除专利文件夹: ${patentFolderPath}`);
      await fileController.deleteDirectoryUtil(patentFolderPath);
      
      // 如果有收集到的文件夹路径，也尝试删除
      if (patentFolderPaths.size > 0) {
        for (const folderPath of patentFolderPaths) {
          console.log(`尝试删除文件夹: ${folderPath}`);
          await fileController.deleteDirectoryUtil(folderPath);
        }
      }
    } catch (fsError) {
      // 文件系统操作失败不影响专利删除的返回结果，仅记录错误
      console.error('删除专利文件时出错:', fsError);
    }
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('删除专利失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除专利失败',
      error: error.message
    });
  }
};

/**
 * 导出专利数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportPatents = async (req, res) => {
  try {
    const { patentName, categoryId, authorizationDateStart, authorizationDateEnd } = req.query;
    
    // 构建查询条件
    const where = {};
    
    if (patentName) {
      where.patentName = { [Op.like]: `%${patentName}%` };
    }
    
    if (categoryId) {
      where.categoryId = categoryId;
    }
    
    if (authorizationDateStart) {
      where.authorizationDate = { ...where.authorizationDate, [Op.gte]: authorizationDateStart };
    }
    
    if (authorizationDateEnd) {
      where.authorizationDate = { ...where.authorizationDate, [Op.lte]: authorizationDateEnd };
    }
    
    // 查询数据
    const patents = await patentModel.findAll({
      where,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: patentCategoryModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });
    
    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('专利列表');
    
    // 定义表头
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: '专利名称', key: 'patentName', width: 30 },
      { header: '专利类型', key: 'categoryName', width: 15 },
      { header: '授权时间', key: 'authorizationDate', width: 15 },
      { header: '转化时间', key: 'conversionDate', width: 15 },
      { header: '得分', key: 'score', width: 10 },
      { header: '备注', key: 'remark', width: 30 },
      { header: '第一负责人', key: 'firstResponsible', width: 15 },
      { header: '参与者', key: 'participants', width: 30 },
      { header: '是否审核', key: 'ifReviewer', width: 10 },
      { header: '创建时间', key: 'createdAt', width: 20 },
      { header: '更新时间', key: 'updatedAt', width: 20 }
    ];
    
    // 获取每个专利的参与者信息
    for (const patent of patents) {
      const patentJson = patent.toJSON();
      
      // 查询参与者
      const participants = await patentParticipantModel.findAll({
        where: { patentId: patent.id },
        include: [
          {
            model: userModel,
            as: 'participant',
            attributes: ['id', 'nickname', 'username', 'studentNumber']
          }
        ]
      });
      
      // 获取所有参与者名单（包括分配比例）
      const participantNames = [];
      
      participants.forEach(p => {
        const participantJson = p.toJSON();
        const userName = participantJson.participant?.nickname || participantJson.participant?.username || '';
        const ratio = participantJson.allocationRatio || 0;
        const formattedRatio = (ratio * 100).toFixed(0);
        const isLeader = participantJson.isLeader === 1 ? '[负责人]' : '';
        
        if (userName) {
          participantNames.push(`${userName}${isLeader}(${formattedRatio}%)`);
        }
      });
      
      // 添加数据行
      worksheet.addRow({
        id: patentJson.id,
        patentName: patentJson.patentName,
        categoryName: patentJson.category?.categoryName || '',
        authorizationDate: patentJson.authorizationDate,
        conversionDate: patentJson.conversionDate || '',
        score: patentJson.category?.score || '',
        remark: patentJson.remark || '',
        firstResponsible: patentJson.firstResponsible?.nickname || patentJson.firstResponsible?.username || '',
        participants: participantNames.join(', '),
        ifReviewer: patentJson.ifReviewer === 1 ? '已审核' : patentJson.ifReviewer === 0 ? '已拒绝' : '待审核',
        createdAt: patentJson.createdAt,
        updatedAt: patentJson.updatedAt
      });
    }
    
    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=patents.xlsx');
    
    // 将工作簿写入响应
    await workbook.xlsx.write(res);
    
    // 结束响应
    res.end();
  } catch (error) {
    console.error('导出专利数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出专利数据失败',
      error: error.message
    });
  }
};

/**
 * 导入专利数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importPatents = async (req, res) => {
  try {
    // 检查是否存在文件
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未上传文件',
        data: null
      });
    }
    
    // 读取上传的Excel文件
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(req.file.path);
    const worksheet = workbook.getWorksheet(1);
    
    // 准备结果统计
    const result = {
      total: 0,
      success: 0,
      failed: 0,
      errors: []
    };
    
    // 获取表头映射
    const headers = {};
    worksheet.getRow(1).eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value;
    });
    
    // 处理每一行数据
    const promises = [];
    
    worksheet.eachRow((row, rowNumber) => {
      // 跳过表头
      if (rowNumber === 1) return;
      
      result.total++;
      
      const processRow = async () => {
        try {
          const patentData = {};
          const participantsData = [];
          
          row.eachCell((cell, colNumber) => {
            const fieldName = headers[colNumber];
            if (fieldName) {
              // 根据字段名称设置对应的数据
              switch(fieldName) {
                case '专利名称':
                  patentData.patentName = cell.value;
                  break;
                case '专利类型':
                  // 需要查询专利类型ID
                  patentData.categoryName = cell.value;
                  break;
                case '授权时间':
                  patentData.authorizationDate = cell.value;
                  break;
                case '转化时间':
                  patentData.conversionDate = cell.value;
                  break;
                case '备注':
                  patentData.remark = cell.value;
                  break;
                case '第一负责人':
                  patentData.firstResponsible = cell.value;
                  break;
                case '负责人': // 兼容旧格式
                  if (!patentData.firstResponsible) {
                    patentData.firstResponsible = cell.value;
                  }
                  patentData.leader = cell.value;
                  break;
                case '参与者':
                  patentData.participants = cell.value;
                  break;
                default:
                  break;
              }
            }
          });
          
          // 验证必填字段
          if (!patentData.patentName) {
            throw new Error('专利名称不能为空');
          }
          
          if (!patentData.categoryName) {
            throw new Error('专利类型不能为空');
          }
          
          if (!patentData.authorizationDate) {
            throw new Error('授权时间不能为空');
          }
          
          const firstResponsibleName = patentData.firstResponsible || patentData.leader;
          if (!firstResponsibleName) {
            throw new Error('第一负责人不能为空');
          }
          
          // 查询专利类型ID
          const category = await patentCategoryModel.findOne({
            where: { categoryName: patentData.categoryName }
          });
          
          if (!category) {
            throw new Error(`未找到专利类型: ${patentData.categoryName}`);
          }
          
          patentData.categoryId = category.id;
          
          // 查询第一负责人信息
          const firstResponsible = await userModel.findOne({
            where: {
              [Op.or]: [
                { nickname: firstResponsibleName },
                { username: firstResponsibleName }
              ]
            }
          });
          
          if (!firstResponsible) {
            throw new Error(`未找到第一负责人: ${firstResponsibleName}`);
          }
          
          patentData.firstResponsibleID = firstResponsible.id;
          
          // 处理参与者
          if (patentData.participants) {
            const participantNames = patentData.participants.split(',').map(name => name.trim());
            const participantUsers = await userModel.findAll({
              where: {
                [Op.or]: [
                  { nickname: { [Op.in]: participantNames } },
                  { username: { [Op.in]: participantNames } }
                ]
              }
            });
            
            if (participantUsers.length === 0 && participantNames.length > 0) {
              throw new Error(`未找到参与者: ${patentData.participants}`);
            }
            
            // 默认分配比例，第一负责人0.6，其他均分0.4
            const leaderRatio = 0.6;
            const otherParticipantsCount = participantUsers.length;
            const otherRatio = otherParticipantsCount > 0 ? (1 - leaderRatio) / otherParticipantsCount : 0;
            
            // 添加第一负责人到参与者列表（如果不在列表中）
            const firstResponsibleInParticipants = participantUsers.some(u => u.id === firstResponsible.id);
            
            if (!firstResponsibleInParticipants) {
              participantsData.push({
                participantId: firstResponsible.id,
                allocationRatio: leaderRatio,
                isLeader: 1
              });
              
              // 其他参与者均分剩余的分配比例
              participantUsers.forEach(user => {
                participantsData.push({
                  participantId: user.id,
                  allocationRatio: otherRatio,
                  isLeader: 0
                });
              });
            } else {
              // 第一负责人已在参与者列表中
              participantUsers.forEach(user => {
                participantsData.push({
                  participantId: user.id,
                  allocationRatio: user.id === firstResponsible.id ? leaderRatio : otherRatio,
                  isLeader: user.id === firstResponsible.id ? 1 : 0
                });
              });
            }
          } else {
            // 只有第一负责人
            participantsData.push({
              participantId: firstResponsible.id,
              allocationRatio: 1,
              isLeader: 1
            });
          }
          
          // 创建专利记录
          const patent = await patentModel.create({
            patentName: patentData.patentName,
            categoryId: patentData.categoryId,
            authorizationDate: patentData.authorizationDate,
            conversionDate: patentData.conversionDate,
            remark: patentData.remark,
            firstResponsibleID: patentData.firstResponsibleID,
            ifReviewer: null // 默认待审核
          });
          
          // 创建参与者记录
          const participantPromises = participantsData.map(p => {
            return patentParticipantModel.create({
              patentId: patent.id,
              participantId: p.participantId,
              allocationRatio: p.allocationRatio,
              isLeader: p.isLeader
            });
          });
          
          await Promise.all(participantPromises);
          
          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            row: rowNumber,
            message: error.message
          });
        }
      };
      
      promises.push(processRow());
    });
    
    // 等待所有处理完成
    await Promise.all(promises);
    
    // 删除临时文件
    fs.unlinkSync(req.file.path);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: result
    });
  } catch (error) {
    console.error('导入专利数据失败:', error);
    
    // 如果文件存在，删除临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    return res.status(500).json({
      code: 500,
      message: '导入专利数据失败',
      error: error.message
    });
  }
};

/**
 * 获取专利分类列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPatentCategories = async (req, res) => {
  try {
    const categories = await patentCategoryModel.findAll({
      where: { status: 1 }, // 只返回启用的分类
      order: [['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: categories
    });
  } catch (error) {
    console.error('获取专利分类列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利分类列表失败',
      error: error.message
    });
  }
};

/**
 * 获取专利分类分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCategoryDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body; // range: 'in', 'out', 'all'; reviewStatus: 'all', 'reviewed', 'reject', 'pending'
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("patents");
    
    // 构建查询条件
    const where = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'reject') {
      where.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null; // 待审核
    }
    // 'all' 状态不添加筛选条件
    
    // 根据range参数添加到数据库查询条件中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内 - 复杂条件：任一日期在范围内或两者都在范围内
        const authorizationInRange = {
          authorizationDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        const conversionInRange = {
          conversionDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 构建范围内条件
        where[Op.or] = [
          authorizationInRange,
          conversionInRange
        ];
      } else if (range === 'out') {
        // 在时间范围外 - 两者都在范围外
        const authorizationOutRange = {
          [Op.or]: [
            { authorizationDate: { [Op.lt]: intervalStartTime } },
            { authorizationDate: { [Op.gt]: intervalEndTime } },
            { authorizationDate: null }
          ]
        };
        
        const conversionOutRange = {
          [Op.or]: [
            { conversionDate: { [Op.lt]: intervalStartTime } },
            { conversionDate: { [Op.gt]: intervalEndTime } },
            { conversionDate: null }
          ]
        };
        
        where[Op.and] = [
          authorizationOutRange,
          conversionOutRange
        ];
      }
    }
    
    // 如果提供了userId，添加用户筛选条件
    if (userId) {
      // 查询用户参与的专利ID
      const participations = await patentParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['patentId']
      });
      
      const patentIds = participations.map(p => p.patentId);
      
      if (patentIds.length === 0) {
        // 该用户没有参与专利
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
      
      // 添加专利ID筛选条件
      if (where[Op.or] || where[Op.and]) {
        // 需要合并条件
        where[Op.and] = where[Op.and] || [];
        where[Op.and].push({ id: { [Op.in]: patentIds } });
      } else {
        where.id = { [Op.in]: patentIds };
      }
    }
    
    // 查询专利
    const patents = await patentModel.findAll({
      where,
      include: [
        {
          model: patentCategoryModel,
          as: 'category',
          attributes: ['id', 'categoryName'],
          required: true,
        },
      ]
    });
    
    // 初始化分类数据
    const categoryData = {};
    
    // 统计专利分类分布
    patents.forEach(patent => {
      const patentJson = patent.toJSON();
      const categoryName = patentJson.category.categoryName;
      categoryData[categoryName] = (categoryData[categoryName] || 0) + 1;
    });
    
    // 转换为前端期望的格式：[{name, value}]
    const result = Object.entries(categoryData).map(([name, value]) => ({ name, value }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取专利分类分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利分类分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取专利时间分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTimeDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body; // range: 'in', 'out', 'all'; reviewStatus: 'all', 'reviewed', 'reject', 'pending'
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("patents");
    
    // 构建查询条件
    const where = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'reject') {
      where.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null; // 待审核
    }
    // 'all' 状态不添加筛选条件
    
    // 根据range参数添加到数据库查询条件中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内 - 复杂条件：任一日期在范围内或两者都在范围内
        const authorizationInRange = {
          authorizationDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        const conversionInRange = {
          conversionDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 构建范围内条件
        where[Op.or] = [
          authorizationInRange,
          conversionInRange
        ];
      } else if (range === 'out') {
        // 在时间范围外 - 两者都在范围外
        const authorizationOutRange = {
          [Op.or]: [
            { authorizationDate: { [Op.lt]: intervalStartTime } },
            { authorizationDate: { [Op.gt]: intervalEndTime } },
            { authorizationDate: null }
          ]
        };
        
        const conversionOutRange = {
          [Op.or]: [
            { conversionDate: { [Op.lt]: intervalStartTime } },
            { conversionDate: { [Op.gt]: intervalEndTime } },
            { conversionDate: null }
          ]
        };
        
        where[Op.and] = [
          authorizationOutRange,
          conversionOutRange
        ];
      }
    }
    
    // 如果提供了userId，添加用户筛选条件
    if (userId) {
      // 查询用户参与的专利ID
      const participations = await patentParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['patentId']
      });
      
      const patentIds = participations.map(p => p.patentId);
      
      if (patentIds.length === 0) {
        // 该用户没有参与专利
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: { months: [], data: [] }
        });
      }
      
      // 添加专利ID筛选条件
      if (where[Op.or] || where[Op.and]) {
        // 需要合并条件
        where[Op.and] = where[Op.and] || [];
        where[Op.and].push({ id: { [Op.in]: patentIds } });
      } else {
        where.id = { [Op.in]: patentIds };
      }
    }
    
    // 查询专利
    const patents = await patentModel.findAll({
      where
    });
    
    // 初始化时间数据
    const timeData = {};
    
    // 统计专利时间分布
    patents.forEach(patent => {
      const patentJson = patent.toJSON();
      
      if (patentJson.authorizationDate) {
        try {
          // 提取年月，格式：YYYY-MM
          const month = patentJson.authorizationDate.substring(0, 7);
          
          if (month && month.length >= 7) {
            timeData[month] = (timeData[month] || 0) + 1;
          }
        } catch (e) {
          console.error('处理时间数据错误:', e);
        }
      }
    });
    
    // 按时间排序
    const sortedMonths = Object.keys(timeData).sort();
    
    // 构建前端所需的格式
    const result = {
      months: sortedMonths,
      data: sortedMonths.map(month => timeData[month])
    };
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取专利时间分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利时间分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取用户专利总得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserTotalScore = async (req, res) => {
  try {
    const { 
      userId, 
      range = 'all',
      page = 1, 
      pageSize = 10 
    } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '请提供用户ID',
        data: null
      });
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    let startDate, endDate;
    
    if (req.body.timeRange && req.body.timeRange.startDate && req.body.timeRange.endDate) {
      // 使用前端传入的自定义时间范围
      startDate = req.body.timeRange.startDate;
      endDate = req.body.timeRange.endDate;
    } else {
      // 使用系统默认的时间区间
      const timeInterval = await getTimeIntervalByName("patents");
      startDate = timeInterval ? timeInterval.startTime : null;
      endDate = timeInterval ? timeInterval.endTime : null;
    }
    
    // 构建查询条件
    const where = { 
      ifReviewer: 1 // 只统计已审核的专利
    };
    
    // 根据range参数添加到数据库查询条件中
    if (startDate && endDate && range !== 'all') {
      const intervalStartTime = new Date(startDate);
      const intervalEndTime = new Date(endDate);
      
      if (range === 'in') {
        // 在时间范围内 - 复杂条件：任一日期在范围内或两者都在范围内
        const authorizationInRange = {
          authorizationDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        const conversionInRange = {
          conversionDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 构建范围内条件
        where[Op.or] = [
          authorizationInRange,
          conversionInRange
        ];
      } else if (range === 'out') {
        // 在时间范围外 - 两者都在范围外
        const authorizationOutRange = {
          [Op.or]: [
            { authorizationDate: { [Op.lt]: intervalStartTime } },
            { authorizationDate: { [Op.gt]: intervalEndTime } },
            { authorizationDate: null }
          ]
        };
        
        const conversionOutRange = {
          [Op.or]: [
            { conversionDate: { [Op.lt]: intervalStartTime } },
            { conversionDate: { [Op.gt]: intervalEndTime } },
            { conversionDate: null }
          ]
        };
        
        where[Op.and] = [
          authorizationOutRange,
          conversionOutRange
        ];
      }
    }
    
    // 查询用户参与的已审核专利
    const participations = await patentParticipantModel.findAll({
      where: { participantId: userId },
      include: [
        {
          model: patentModel,
          as: 'patent',
          where: where,
          include: [
            {
              model: patentCategoryModel,
              as: 'category',
              attributes: ['id', 'categoryName', 'score'],
              required: true,
            }
          ],
          required: true,
        }
      ]
    });
    
    // 统计专利得分和详情
    let totalScore = 0;
    const patentDetails = [];
    
    participations.forEach(participation => {
      const patent = participation.patent;
      const category = patent.category;
      
      const patentScore = parseFloat(category.score) || 0;
      const userRatio = participation.allocationRatio || 0;
      const userScore = patentScore * userRatio;
      
      totalScore += userScore;
      
      patentDetails.push({
        id: patent.id,
        patentName: patent.patentName,
        categoryName: category.categoryName,
        authorizationDate: patent.authorizationDate,
        conversionDate: patent.conversionDate,
        totalScore: patentScore,
        userScore: userScore,
        role: participation.isLeader === 1 ? 'leader' : 'member',
        allocationRatio: userRatio
      });
    });
    
    // 按用户得分降序排序
    patentDetails.sort((a, b) => b.userScore - a.userScore);
    
    // 计算总数
    const totalPatents = patentDetails.length;
    
    // 应用分页
    const pagedPatents = patentDetails.slice(offset, offset + limit);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        userId: userId,
        totalScore: totalScore,
        list: pagedPatents,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: totalPatents,
          totalPages: Math.ceil(totalPatents / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户专利总得分失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户专利总得分失败',
      error: error.message
    });
  }
};

/**
 * 获取所有用户专利总分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllUsersTotalScore = async (req, res) => {
  try {
    const { 
      range = 'all',
      page = 1, 
      pageSize,
      nickname = '',
      isExport = false
    } = req.body;
    
    const limit = pageSize != null ? pageSize : 10;
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(limit);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 根据range选择对应的排名表模型
    let RankingModel;
    switch(range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }
    
    // 查询条件：按专利总分降序排序
    const queryOptions = {
      order: [['patentScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'patentCount',
        'patentScore'
      ]
    };
    
    // 添加昵称模糊查询条件
    if (nickname) {
      queryOptions.where = {
        nickName: {
          [Op.like]: `%${nickname}%`
        }
      };
    }
    
    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }

    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);
    
    // 格式化返回数据
    const formattedResults = rows.map((item, index) => ({
      rank: index + 1 + offset,
      userId: item.userId,
      nickname: item.nickName,
      studentNumber: item.studentNumber,
      patentCount: parseInt(item.patentCount || 0),
      totalScore: parseFloat(item.patentScore || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: formattedResults,
        pagination: {
          total: count,
          page: pageNum,
          pageSize: pageSizeNum,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取所有用户专利总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有用户专利总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户专利详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserPatentDetails = async (req, res) => {
  try {
    const { userId, page = 1, pageSize = 10, range = 'in', reviewStatus } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }
    
    // 查询用户
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("patents");
    
    // 查询用户参与的专利ID
    const participations = await patentParticipantModel.findAll({
      where: { participantId: userId },
      attributes: ['patentId']
    });
    
    // 如果用户没有参与任何专利
    if (participations.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          list: [],
          totalScore: 0,
          user: {
            id: user.id,
            name: user.nickname || user.username,
            employeeNumber: user.studentNumber
          },
          pagination: {
            total: 0,
            page: pageNum,
            pageSize: pageSizeNum,
            totalPages: 0
          }
        }
      });
    }
    
    // 获取所有专利ID
    const patentIds = participations.map(p => p.patentId);
    
    // 构建查询条件
    const whereCondition = {
      id: { [Op.in]: patentIds }
    };
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'reject') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = { [Op.is]: null };
    }
    
    // 根据range参数添加到数据库查询条件中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内 - 复杂条件：任一日期在范围内或两者都在范围内
        const authorizationInRange = {
          authorizationDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        const conversionInRange = {
          conversionDate: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 构建范围内条件
        whereCondition[Op.or] = [
          authorizationInRange,
          conversionInRange
        ];
      } else if (range === 'out') {
        // 在时间范围外 - 两者都在范围外
        const authorizationOutRange = {
          [Op.or]: [
            { authorizationDate: { [Op.lt]: intervalStartTime } },
            { authorizationDate: { [Op.gt]: intervalEndTime } },
            { authorizationDate: null }
          ]
        };
        
        const conversionOutRange = {
          [Op.or]: [
            { conversionDate: { [Op.lt]: intervalStartTime } },
            { conversionDate: { [Op.gt]: intervalEndTime } },
            { conversionDate: null }
          ]
        };
        
        whereCondition[Op.and] = [
          authorizationOutRange,
          conversionOutRange
        ];
      }
    }
    
    // 查询用户参与的所有专利
    const { count, rows } = await patentModel.findAndCountAll({
      where: whereCondition,
      offset,
      limit,
      order: [['authorizationDate', 'DESC']],
      include: [
        {
          model: patentCategoryModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        }
      ]
    });
    
    // 计算总得分
    let totalScore = 0;
    
    // 查询用户的所有专利（不分页）以计算总分
    const allPatents = await patentModel.findAll({
      where: whereCondition,
      include: [
        {
          model: patentCategoryModel,
          as: 'category',
          attributes: ['score'],
          required: false
        }
      ]
    });
    
    // 查询每个专利的用户分配比例
    for (const patent of allPatents) {
      if (patent.category && patent.category.score) {
        // 查询用户在该专利中的分配比例
        const participation = await patentParticipantModel.findOne({
          where: {
            patentId: patent.id,
            participantId: userId
          }
        });
        
        if (participation) {
          // 计算得分 = 专利分数 * 用户分配比例
          const score = patent.category.score * participation.allocationRatio;
          totalScore += score;
        }
      }
    }
    
    // 处理专利详情数据，添加用户分配信息
    const patentDetails = await Promise.all(rows.map(async (patent) => {
      const patentJson = patent.toJSON();
      
      // 查询用户参与情况
      const participation = await patentParticipantModel.findOne({
        where: {
          patentId: patent.id,
          participantId: userId
        },
        include: [
          {
            model: userModel,
            as: 'participant',
            attributes: ['id', 'nickname', 'username', 'studentNumber'],
            required: false
          }
        ]
      });
      
      if (participation) {
        patentJson.userAllocationRatio = participation.allocationRatio;
        patentJson.isLeader = participation.isLeader === 1;
        patentJson.role = participation.isLeader === 1 ? 'leader' : 'member';
        patentJson.userScore = patent.category ? patent.category.score * participation.allocationRatio : 0;
        patentJson.totalScore = patent.category ? patent.category.score : 0;
      }
      
      // 格式化审核状态
      patentJson.reviewStatusText = patentJson.ifReviewer === 1 ? '已通过' : 
                                  (patentJson.ifReviewer === 0 ? '已拒绝' : '待审核');
      
      // 添加是否在统计时间范围内的标记（用于前端展示）
      if (timeInterval) {
        patentJson.isInTimeRange = isPatentInTimeRange(patentJson, timeInterval.startTime, timeInterval.endTime);
      } else {
        patentJson.isInTimeRange = false;
      }
      
      return patentJson;
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: patentDetails,
        totalScore: parseFloat(totalScore.toFixed(2)),
        user: {
          id: user.id,
          name: user.nickname || user.username,
          employeeNumber: user.studentNumber
        },
        pagination: {
          total: count,
          page: pageNum,
          pageSize: pageSizeNum,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户专利详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户专利详情失败',
      error: error.message
    });
  }
};

/**
 * 判断日期是否在时间范围内
 * @param {string} dateStr - 日期字符串
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isDateInTimeRange(dateStr, startDate, endDate) {
  if (!dateStr || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const dateObj = new Date(dateStr);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 日期必须在开始日期和结束日期之间
  return dateObj >= startDateObj && dateObj <= endDateObj;
}

/**
 * 审核专利
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewPatent = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(patentModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { reviewStatus = 1, reviewComment, reviewer } = req.body;
    
    console.log(`审核专利，ID: ${id}, 状态: ${reviewStatus}, 意见: ${reviewComment}`);
    
    // 参数验证
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少专利ID参数',
        data: null
      });
    }
    
    const userInfo = await getUserInfoFromRequest(req);
    
    // 权限检查：只有管理员才可以审核专利
    if (userInfo.role.roleAuth !== 'ADMIN-LV2' && userInfo.role.roleAuth !== 'SUPER') {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核专利',
        data: null
      });
    }
    
    // 检查专利是否存在
    const patent = await patentModel.findByPk(id, { transaction });
    if (!patent) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '专利不存在',
        data: null
      });
    }

    // 更新审核状态
    const updateData = {
      ifReviewer: reviewStatus,
      reviewerId: reviewer || userInfo.id,
      reviewedAt: new Date()
    };
    
    // 添加审核意见（如果有）
    if (reviewComment !== undefined) {
      updateData.reviewComment = reviewComment;
    }
    
    await patent.update(updateData, { transaction });
    
    // 专利审核通过后，更新用户排名数据
    if (reviewStatus === 1) {
      try {
        // 获取专利级别对应的分数
        const patentLevel = await patentCategoryModel.findByPk(patent.categoryId, { transaction });
        if (!patentLevel) {
          console.error(`未找到专利级别信息，levelId: ${patent.categoryId}`);
          throw new Error('未找到专利级别信息');
        }
        
        const baseScore = patentLevel.score || 0;
        
        // 获取专利所有参与者及其分配比例
        const participants = await patentParticipantModel.findAll({
          where: { patentId: id },
          transaction
        });
        console.log("participants===", participants);
        
        if (participants && participants.length > 0) {
          // 准备用户ID数组和得分数组
          const participantUserIds = [];
          const participantScores = [];
          
          // 计算每个参与者的得分
          for (const participant of participants) {
            const userId = participant.participantId; // 修正：使用正确的字段名 participantId
            const allocationRatio = parseFloat(participant.allocationRatio) || 0;
            
            // 验证用户ID是否有效
            if (!userId) {
              console.error('参与者没有有效的用户ID:', participant);
              throw new Error('参与者数据不完整，缺少用户ID');
            }

            // 计算该参与者应得的分数 = 专利基础分 * 分配比例
            const userScore = baseScore * allocationRatio;
            participantUserIds.push(userId);
            participantScores.push(userScore);
          }
          
          // 验证是否有有效的参与者
          if (participantUserIds.length === 0 || participantUserIds.some(id => !id)) {
            throw new Error('未找到有效的参与者用户ID');
          }
          
          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("patents");
          
          // 判断专利是否在时间区间内
          const isInTimeRange = timeInterval ? 
            isPatentInTimeRange(patent, timeInterval.startTime, timeInterval.endTime) : 
            false;
          
          console.log(`专利ID ${id} 是否在统计时间区间内: ${isInTimeRange}`);
          console.log(`专利参与者数量: ${participantUserIds.length}, 基础分数: ${baseScore}`);
          
          // 根据专利是否在时间区间内，更新不同的排名表
          let rankingTables = [];
          
          if (isInTimeRange) {
            // 在区间内：更新范围内表和全部表
            rankingTables = [
              'user_ranking_reviewed_in', 
              'user_ranking_reviewed_all'
            ];
            console.log(`更新范围内排名表和全部排名表`);
          } else {
            // 不在区间内：更新范围外表和全部表
            rankingTables = [
              'user_ranking_reviewed_out', 
              'user_ranking_reviewed_all'
            ];
            console.log(`更新范围外排名表和全部排名表`);
          }
          console.log("participantUserIds===", participantUserIds);
          
          try {
            for (const table of rankingTables) {
              // 更新所有参与者的排名数据：每人计数+1，分数增加各自的得分
              await updateUserRankings(
                participantUserIds,          // 所有参与者的用户ID数组
                table,                       // 表名
                'patents',                    // 类型名
                Array(participantUserIds.length).fill(1), // 每个参与者计数+1
                participantScores,           // 每个参与者的得分数组
                transaction,                 // 传递事务对象
                "add"                        // 操作类型：加分
              );
            }
          } catch (rankingError) {
            console.error('更新排名表失败:', rankingError);
            // 所有排名更新错误都应该回滚事务
            await transaction.rollback();
            throw new Error(`更新排名失败: ${rankingError.message}`);
          }
        } else {
          console.log(`专利ID ${id} 没有参与者，无需更新排名`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 检查是否已经回滚，如果没有则回滚事务
        if (!error.message || !error.message.includes('更新排名失败')) {
          await transaction.rollback();
          throw error; // 将错误向上传播
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    console.log(`专利 ${id} 审核成功，审核人ID: ${updateData.reviewerId}, 状态: ${reviewStatus}`);

    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('专利审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '专利审核失败: ' + error.message,
      error: error.message
    });
  }
};

/**
 * 获取专利总分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPatentsTotalScore = async (req, res) => {
  try {
    const { range = 'all', reviewStatus = 'all' } = req.body;

    // 查找数据库连接，通过已有模型获取sequelize实例
    let sequelize;
    if (patentModel.sequelize) {
      sequelize = patentModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }

    let userId = null; 
    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    // 调用存储过程获取专利总分统计
    const results = await sequelize.query(
      'CALL get_patents_total_score(?, ?, ?)',
      {
        replacements: [range || 'all', reviewStatus || 'all', userId || null],
        type: sequelize.QueryTypes.RAW,
        raw: true,
        nest: true
      }
    );

    console.log("专利统计存储过程返回结果:", JSON.stringify(results));

    // 处理存储过程的结果
    let typeStats = [];
    let totalStats = { totalPatents: 0, totalScore: 0 };
    let timeInterval = null;

    // 修正处理逻辑，直接处理返回的单个数组
    if (Array.isArray(results) && results.length > 0) {
      // 如果存储过程返回了单个数组，直接使用它作为类型统计
      typeStats = results.map(item => ({
        typeId: item.typeId || item.categoryId,
        typeName: item.typeName || item.categoryName,
        count: parseInt(item.count || 0),
        totalScore: parseFloat(item.totalScore || 0)
      }));
      
      // 根据typeStats计算总体统计
      totalStats = {
        totalPatents: typeStats.reduce((sum, item) => sum + (item.count || 0), 0),
        totalScore: parseFloat(typeStats.reduce((sum, item) => sum + (item.totalScore || 0), 0).toFixed(2))
      };
    }
    
    // 获取时间区间
    try {
      timeInterval = await getTimeIntervalByName("patents");
    } catch (error) {
      console.error("获取时间区间失败:", error);
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        typeStats,
        totalStats,
        timeInterval
      }
    });
  } catch (error) {
    console.error('获取专利总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户专利详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserPatentsDetail = async (req, res) => {
  try {
    const { 
      userId, 
      range = 'all', 
      reviewStatus = 'all', 
      page = 1, 
      pageSize = 10 
    } = req.body;

    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }

    // 查询用户是否存在
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 查找数据库连接，通过已有模型获取sequelize实例
    let sequelize;
    if (patentModel.sequelize) {
      sequelize = patentModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }

    // 调用存储过程获取用户专利详情
    const results = await sequelize.query(
      'CALL get_user_patents_detail(?, ?, ?, ?, ?)',
      {
        replacements: [
          userId,
          range || 'all',
          reviewStatus || 'all',
          parseInt(pageSize),
          parseInt(page)
        ],
        type: sequelize.QueryTypes.RAW,
        raw: true,
        nest: true
      }
    );

    // 处理存储过程的结果
    let totalCount = 0;
    let patents = [];
    let stats = {};
    let timeInterval = {};

    // 使用工具函数或直接处理结果
    const resultSets = Array.isArray(results) ? results : [];

    // 第一个结果集：总记录数
    if (resultSets.length > 0 && Array.isArray(resultSets[0]) && resultSets[0].length > 0) {
      totalCount = parseInt(resultSets[0][0].totalCount || 0);
    }

    // 第二个结果集：专利详情列表
    if (resultSets.length > 1 && Array.isArray(resultSets[1])) {
      patents = resultSets[1].map(item => ({
        patentId: item.patentId,
        patentName: item.patentName,
        categoryId: item.categoryId,
        categoryName: item.categoryName,
        authorizationDate: item.authorizationDate,
        conversionDate: item.conversionDate,
        baseScore: parseFloat(item.baseScore || 0),
        allocationRatio: parseFloat(item.allocationRatio || 0),
        isLeader: item.isLeader === 1,
        actualScore: parseFloat(item.actualScore || 0).toFixed(2),
        remark: item.remark,
        ifReviewer: item.ifReviewer,
        reviewStatus: item.reviewStatus,
        reviewerName: item.reviewerName,
        reviewComment: item.reviewComment,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));
    }

    // 第三个结果集：统计数据
    if (resultSets.length > 2 && Array.isArray(resultSets[2]) && resultSets[2].length > 0) {
      stats = {
        totalPatents: parseInt(resultSets[2][0].totalPatents || 0),
        leaderPatentCount: parseInt(resultSets[2][0].leaderPatentCount || 0),
        participantPatentCount: parseInt(resultSets[2][0].participantPatentCount || 0),
        totalScore: parseFloat(resultSets[2][0].totalScore || 0).toFixed(2)
      };
    }

    // 第四个结果集：时间区间
    if (resultSets.length > 3 && Array.isArray(resultSets[3]) && resultSets[3].length > 0) {
      timeInterval = {
        startTime: resultSets[3][0].startTime,
        endTime: resultSets[3][0].endTime,
        name: resultSets[3][0].name
      };
    } else {
      // 如果没有第四个结果集，尝试获取时间区间
      timeInterval = await getTimeIntervalByName("patents");
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: patents,
        stats,
        timeInterval,
        user: {
          id: user.id,
          name: user.nickname || user.username,
          employeeNumber: user.studentNumber
        },
        pagination: {
          total: totalCount,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(totalCount / parseInt(pageSize))
        }
      }
    });
  } catch (error) {
    console.error('获取用户专利详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户专利详情失败',
      error: error.message
    });
  }
};

/**
 * 判断专利是否在指定时间范围内
 * @param {Object} patent - 专利对象，包含 authorizationDate 和 conversionDate 字段
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @returns {boolean} - 是否在范围内
 */
function isPatentInTimeRange(patent, startTime, endTime) {
  if (!startTime || !endTime) return false;
  
  // 将时间字符串转换为日期对象
  const startTimeObj = new Date(startTime);
  const endTimeObj = new Date(endTime);
  
  // 检查授权日期
  let authorizationInRange = false;
  if (patent.authorizationDate) {
    const authDate = new Date(patent.authorizationDate);
    authorizationInRange = (authDate >= startTimeObj && authDate <= endTimeObj);
  }
  
  // 检查转化日期
  let conversionInRange = false;
  if (patent.conversionDate) {
    const convDate = new Date(patent.conversionDate);
    conversionInRange = (convDate >= startTimeObj && convDate <= endTimeObj);
  }
  
  // 如果两个日期都存在，则至少一个在范围内即可
  // 如果只有一个日期存在，则该日期需在范围内
  // 如果两个日期都不存在，返回false
  if (patent.authorizationDate && patent.conversionDate) {
    return authorizationInRange || conversionInRange;
  } else if (patent.authorizationDate) {
    return authorizationInRange;
  } else if (patent.conversionDate) {
    return conversionInRange;
  }
  
  return false;
}

/**
 * 重新提交专利审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapply = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(patentModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少专利ID',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找专利
    const patent = await patentModel.findByPk(id, { transaction });
    
    if (!patent) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '专利不存在',
        data: null
      });
    }
    
    // 检查专利所有权或管理员权限
    const isOwner = patent.userId === userInfo.id;
    const isAdmin = userInfo.role.roleAuth === 'TEACHER-LV1' || userInfo.role.roleAuth === 'SUPER' || userInfo.role.roleAuth === 'ADMIN-LV2';
    
    if (!isOwner && !isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限重新提交该专利',
        data: null
      });
    }
    
    // 检查当前审核状态，只有被拒绝的专利可以重新提交
    if (patent.ifReviewer != 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '只有被拒绝的专利可以重新提交审核',
        data: null
      });
    }
    
    // 更新专利状态为待审核
    await patent.update({
      ifReviewer: null,  // 设置为待审核状态
      reviewComment: null, // 清空之前的审核意见
      reviewerId: null // 清空之前的审核人
    }, { transaction });
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();

    console.error('重新提交审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '重新提交审核失败',
      error: error.message
    });
  }
};

/**
 * 获取审核状态概览
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReviewStatusOverview = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("patents");
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const whereCondition = {};

    // 时间范围筛选
    if (range === 'in' && timeInterval) {
      whereCondition.authorizationDate = {
        [Op.between]: [timeInterval.startTime, timeInterval.endTime]
      };
    } else if (range === 'out' && timeInterval) {
      whereCondition.authorizationDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }

    // 构建用户筛选条件
    let userCondition = {};
    if (userId) {
      userCondition = {
        include: [{
          model: patentParticipantModel,
          as: 'participants',
          where: { participantId: userId },
          required: true
        }]
      };
    } else if (userInfo.role.roleAuth === 'TEACHER-LV1') {
      userCondition = {
        include: [{
          model: patentParticipantModel,
          as: 'participants',
          where: { participantId: userInfo.id },
          required: true
        }]
      };
    }

    // 查询各状态数量
    const [reviewed, pending, rejected] = await Promise.all([
      patentModel.count({
        where: { ...whereCondition, ifReviewer: 1 },
        ...userCondition
      }),
      patentModel.count({
        where: { ...whereCondition, ifReviewer: null },
        ...userCondition
      }),
      patentModel.count({
        where: { ...whereCondition, ifReviewer: 0 },
        ...userCondition
      })
    ]);

    const total = reviewed + pending + rejected;
    const reviewedRate = total > 0 ? ((reviewed / total) * 100).toFixed(1) : 0;

    return res.status(200).json({
      code: 200,
      message: '获取审核状态数据成功',
      data: {
        reviewed,
        pending,
        rejected,
        total,
        reviewedRate: parseFloat(reviewedRate)
      }
    });
  } catch (error) {
    console.error('获取审核状态概览失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取审核状态概览失败',
      error: error.message
    });
  }
};
