const express = require('express');
const homeController = require('../../../controllers/v1/common/homeController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

const router = express.Router();

// 创建首页权限中间件函数
const homePermission = (action) => createModulePermission('home', action);

/**
 * 获取首页统计数据
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {stats: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats', authMiddleware, homePermission('stats'), homeController.getHomeStats);

/**
 * 获取首页数据概览
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {stats: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/overview', authMiddleware, homePermission('overview'), homeController.getHomeOverview);

/**
 * 获取最近通知
 * @route POST /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @param {number} limit.body - 获取数量，默认5
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {notifications: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/notifications', authMiddleware, homePermission('notifications'), homeController.getRecentNotifications);

/**
 * 获取待办事项
 * @route POST /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @param {number} limit.body - 获取数量，默认5
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {todos: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/todos', authMiddleware, homePermission('todos'), homeController.getTodos);

/**
 * 获取绩效趋势
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @param {string} type.body - 趋势类型（month/week）
 * @param {number} limit.body - 获取数量，默认6
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {trend: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/performance-trend', authMiddleware, homePermission('performanceTrend'), homeController.getPerformanceTrend);

/**
 * 获取部门排名
 * @route POST /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @param {string} type.body - 排名类型（performance/research/education）
 * @param {number} limit.body - 获取数量，默认5
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {ranks: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/department-rank', authMiddleware, homePermission('departmentRank'), homeController.getDepartmentRank);

/**
 * 获取首页统计图表
 * @route POST /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {stats: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/charts', authMiddleware, homePermission('charts'), homeController.getHomeCharts);

/**
 * 获取用户综合数据（含个人评分、趋势和建议）
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {basic_info: {}, dimension_scores: [], score_trend: {}, improvement_suggestions: [], department_ranking: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/user-dashboard', authMiddleware, homePermission('userDashboard'), homeController.getUserDashboard);

/**
 * 获取教师职称分布
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 数据分析相关接口
 * @param {string} department.query - 部门名称（可选）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "", value: 0}]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/teacher-titles', authMiddleware, homePermission('teacherTitles'), homeController.getTeacherTitles);

/**
 * 获取项目级别分布
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 数据分析相关接口
 * @param {number} year.query - 年份
 * @param {string} department.query - 部门名称（可选）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "", value: 0}]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/project-levels', authMiddleware, homePermission('projectLevels'), homeController.getProjectLevels);

/**
 * 获取院系教师分布
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 数据分析相关接口
 * @param {number} status.query - 状态（可选，1表示在职）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "", value: 0}]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/department-teachers', authMiddleware, homePermission('departmentTeachers'), homeController.getDepartmentTeachers);

/**
 * 获取项目年度分布
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 数据分析相关接口
 * @param {number} start_year.query - 开始年份
 * @param {number} end_year.query - 结束年份
 * @param {string} department.query - 部门名称（可选）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{year: 0, count: 0}]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/project-years', authMiddleware, homePermission('projectYears'), homeController.getProjectYears);

/**
 * 获取用户统计数据
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 数据统计相关接口
 * @param {string} userId.query - 用户ID（可选，不传则统计所有用户）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalItems: 0, totalScore: 0, academicAppointments: 0, ...}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/user-stats', authMiddleware, homePermission('userStats'), homeController.getUserStats);

/**
 * 获取用户综合排名数据
 * @route POST /v1/sys/home/<USER>
 * @group 首页管理 - 数据分析相关接口
 * @param {string} range.body - 范围（all/in/out）
 * @param {string} reviewStatus.body - 审核状态（all/reviewed/pending/rejected）
 * @param {number} page.body - 页码
 * @param {number} limit.body - 每页数量
 * @param {boolean} isExport.body - 是否导出全部
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/combined-ranking', authMiddleware, homePermission('combinedRanking'), homeController.getCombinedRanking);

/**
 * 获取我的评分数据
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @param {string} userId.query - 用户ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {basic_info: {}, rankings: {}, dimension_scores: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/my-score', authMiddleware, homePermission('myScore'), homeController.getMyScore);

/**
 * 获取对应时间区间
 * @route GET /v1/sys/home/<USER>
 * @group 首页管理 - 首页相关接口
 * @param {string} type.query - 类型（academicAppointments/conferences/patents/highLevelPapers/researchProjects/teachingWorkloads） 
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {time_range: ""}}
 */
router.get('/score-time-range', authMiddleware, homePermission('scoreTimeRange'), homeController.getScoreTimeRange);

module.exports = router; 