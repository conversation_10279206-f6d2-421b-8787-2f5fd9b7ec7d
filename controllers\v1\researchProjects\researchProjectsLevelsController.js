const { Op } = require('sequelize');
const { getUserInfoFromRequest } = require('../../../utils/others');
const researchProjectsLevelsModel = require('../../../models/v1/mapping/researchProjectsLevelsModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取科研项目级别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getResearchProjectsLevels = async (req, res) => {
    try {
        console.log('🔍 获取科研项目级别列表 - 请求参数:', req.query);
        
        // 获取查询参数
        const { page = 1, pageSize = 10, levelName } = req.query;
        
        // 确保 page 和 pageSize 是有效的数字，否则使用默认值
        const pageNum = page ? parseInt(page) : 1;
        const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

        // 如果 page 或 pageSize 是无效数字，返回默认值
        const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
        const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;
        
        console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

        // 构建查询条件
        const where = {};
        if (levelName) {
            where.levelName = { [Op.like]: `%${levelName}%` };
        }
        
        console.log('🔍 最终查询条件:', JSON.stringify(where));

        // 分页查询
        const offset = (validPage - 1) * validPageSize;
        try {
            console.log('📚 执行数据库查询...');
            const { count, rows } = await researchProjectsLevelsModel.findAndCountAll({
                where,
                offset,
                limit: validPageSize,
                order: [['createdAt', 'DESC']] // 默认按时间倒序
            });
            
            console.log(`✅ 查询成功: 共${count}条记录`);
            
            const totalPages = Math.ceil(count / validPageSize);
            
            return res.status(200).json({
                code: 200,
                message: '获取成功',
                data: {
                    total: count,
                    page: validPage,
                    pageSize: validPageSize,
                    totalPages,
                    list: rows
                }
            });
        } catch (dbError) {
            console.error('❌ 数据库查询失败:', dbError);
            return res.status(500).json({
                code: 500,
                message: `数据库查询失败: ${dbError.message}`,
                data: null
            });
        }
    } catch (error) {
        console.error('❌ 获取科研项目级别列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取科研项目级别列表失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取所有科研项目级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllResearchProjectsLevels = async (req, res) => {
    try {
        const levels = await researchProjectsLevelsModel.findAll();
        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: levels    
        });
    } catch (error) {
        console.error('❌ 获取所有科研项目级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取所有科研项目级别失败: ${error.message}`,
            data: null
        });
    }
};  

/**
 * 获取科研项目级别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getResearchProjectsLevelDetail = async (req, res) => {
    try {
        const { id } = req.query;
        console.log('🔍 获取科研项目级别详情 - ID:', id);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        const level = await researchProjectsLevelsModel.findByPk(id);
        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '未找到该级别',
                data: null
            });
        }
        
        console.log(`✅ 查询成功: 级别ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: level
        });
    } catch (error) {
        console.error('❌ 获取科研项目级别详情失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取科研项目级别详情失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 创建科研项目级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createResearchProjectsLevel = async (req, res) => {
    try {
        const { levelName, score } = req.body;
        console.log('📝 创建科研项目级别 - 请求数据:', { levelName, score });
        
        // 校验必填参数
        if (!levelName || score === undefined) {
            return res.status(400).json({
                code: 400,
                message: '级别名称和基础分数不能为空',
                data: null
            });
        }
        
        // 检查级别名称是否已存在
        const existingLevel = await researchProjectsLevelsModel.findOne({
            where: { levelName }
        });

        if (existingLevel) {
            return res.status(400).json({
                code: 400,
                message: '该级别名称已存在',
                data: null
            });
        }
        
        const userInfo = await getUserInfoFromRequest(req);
        const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');

        if (!isAdmin) {
            return res.status(403).json({
              code: 403,
              message: '您没有权限创建科研项目级别',
              data: null
            });
          }

        // 创建级别
        const level = await researchProjectsLevelsModel.create({
            id: uuidv4(),
            levelName,
            score,
            createdBy: userInfo.id
        });
        
        console.log(`✅ 创建成功: 级别ID ${level.id}`);
        
        return res.status(201).json({
            code: 200,
            message: '创建成功',
            data: level
        });
    } catch (error) {
        console.error('❌ 创建科研项目级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `创建科研项目级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 更新科研项目级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateResearchProjectsLevel = async (req, res) => {
    try {
        const { id, levelName, score } = req.body;
        console.log('📝 更新科研项目级别 - 请求数据:', { id, levelName, score });

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        // 查找级别
        const level = await researchProjectsLevelsModel.findByPk(id);
        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '未找到该级别',
                data: null
            });
        }

        // 如果更新了级别名称，检查是否与其他记录重复
        if (levelName && levelName !== level.levelName) {
            const existingLevel = await researchProjectsLevelsModel.findOne({
                where: {
                    id: { [Op.ne]: id },
                    levelName
                }
            });

            if (existingLevel) {
                return res.status(400).json({
                    code: 400,
                    message: '该级别名称已存在',
                    data: null
                });
            }
        }

        // 更新级别
        await level.update({
            levelName: levelName || level.levelName,
            score: score !== undefined ? score : level.score
        });
        
        console.log(`✅ 更新成功: 级别ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '更新成功',
            data: level
        });
    } catch (error) {
        console.error('❌ 更新科研项目级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `更新科研项目级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 删除科研项目级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteResearchProjectsLevel = async (req, res) => {
    try {
        const { id } = req.query;
        console.log('🗑️ 删除科研项目级别 - ID:', id);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        const level = await researchProjectsLevelsModel.findByPk(id);
        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '未找到该级别',
                data: null
            });
        }

        await level.destroy();
        
        console.log(`✅ 删除成功: 级别ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 删除科研项目级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `删除科研项目级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 批量删除科研项目级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteResearchProjectsLevels = async (req, res) => {
    try {
        const { ids } = req.body;
        console.log('🗑️ 批量删除科研项目级别 - IDs:', ids);

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                code: 400,
                message: 'ID列表不能为空',
                data: null
            });
        }

        await researchProjectsLevelsModel.destroy({
            where: {
                id: {
                    [Op.in]: ids
                }
            }
        });
        
        console.log(`✅ 批量删除成功: 共${ids.length}条记录`);
        
        return res.status(200).json({
            code: 200,
            message: '批量删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 批量删除科研项目级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `批量删除科研项目级别失败: ${error.message}`,
            data: null
        });
    }
}; 