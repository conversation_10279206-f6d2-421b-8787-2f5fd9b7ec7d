import service from '../server'

// 教学工作量相关接口 - 移除重复的/v1前缀，因为baseURL已包含
const api = {
  // 主要功能接口 - 与后端路由完全匹配
  list: '/sys/teaching-workloads/list',
  detail: '/sys/teaching-workloads/detail',
  create: '/sys/teaching-workloads/create',
  update: '/sys/teaching-workloads/update',
  delete: '/sys/teaching-workloads/delete',
  review: '/sys/teaching-workloads/review',
  reapply: '/sys/teaching-workloads/reapply',
  export: '/sys/teaching-workloads/export',
  import: '/sys/teaching-workloads/import',
  // 级别相关接口 - 与后端路由完全匹配
  levelsList: '/sys/teaching-workloads/levels',
  levelsAll: '/sys/teaching-workloads/levels/all',
  levelsCreate: '/sys/teaching-workloads/level/create',
  levelsUpdate: '/sys/teaching-workloads/level/update',
  levelsDelete: '/sys/teaching-workloads/level/delete',
  levelsDetail: '/sys/teaching-workloads/level/detail',
  levelsBatchDelete: '/sys/teaching-workloads/levels/batch-delete',
  // 参与者相关接口
  participantsList: '/sys/teaching-workloads/participants',
  participantsCreate: '/sys/teaching-workloads/participant/create',
  participantsUpdate: '/sys/teaching-workloads/participant/update',
  participantsDelete: '/sys/teaching-workloads/participant/delete',
  participantsDetail: '/sys/teaching-workloads/participant/detail',
  // 统计相关接口
  reviewStatusStats: '/sys/teaching-workloads/stats/review-status',
  courseTypeStats: '/sys/teaching-workloads/stats/course-type',
  semesterStats: '/sys/teaching-workloads/stats/semester',
  scoreStats: '/sys/teaching-workloads/stats/score',
  totalScoreStats: '/sys/teaching-workloads/stats/total-score',
  // 排行榜相关接口
  teacherRanking: '/sys/teaching-workloads/teacher-ranking',
  // 用户工作量详情接口
  userWorkloadDetails: '/sys/teaching-workloads/user-workload-details',
  userWorkloadsDetail: '/sys/teaching-workloads/user-workloads-detail'
}

// 获取教学工作量列表 - 使用POST方法与后端匹配
export const getTeachingWorkloadsList = (data) => {
  return service.post(api.list, data)
}

// 获取教学工作量详情 - 使用POST方法与后端匹配
export const getTeachingWorkloadDetail = (data) => {
  return service.post(api.detail, data)
}

// 创建教学工作量 - 使用POST方法与后端匹配
export const createTeachingWorkload = (data) => {
  return service.post(api.create, data)
}

// 更新教学工作量 - 使用POST方法与后端匹配
export const updateTeachingWorkload = (data) => {
  const { id, ...updateData } = data
  return service.post(`${api.update}/${id}`, updateData)
}

// 删除教学工作量 - 使用DELETE方法与后端匹配
export const deleteTeachingWorkload = (id) => {
  return service.delete(`${api.delete}/${id}`)
}

// 审核教学工作量 - 使用POST方法与后端匹配
export const reviewTeachingWorkload = (data) => {
  console.log('审核API请求数据:', JSON.stringify(data, null, 2))
  return service.post(api.review, data).then(response => {
    console.log('审核API原始响应:', JSON.stringify(response, null, 2))
    return response
  }).catch(error => {
    console.error('审核API错误:', error)
    console.error('审核API错误详情:', error.response?.data || error.message)
    throw error
  })
}

// 重新提交审核 - 使用POST方法与后端匹配
export const reapplyTeachingWorkload = (id) => {
  return service.post(api.reapply, { id })
}

// 导出教学工作量
export const exportTeachingWorkloads = (data) => {
  return service.post(api.export, data)
}

// 导入教学工作量
export const importTeachingWorkloads = (data) => {
  return service.post(api.import, data)
}

// 获取工作量级别列表（分页）- 使用GET方法与后端匹配
export const getWorkloadLevelsList = (params) => {
  return service.get(api.levelsList, { params })
}

// 获取所有启用的工作量级别 - 使用GET方法与后端匹配
export const getWorkloadLevels = () => {
  return service.get(api.levelsAll)
}

// 创建工作量级别 - 使用POST方法与后端匹配
export const createWorkloadLevel = (data) => {
  return service.post(api.levelsCreate, data)
}

// 获取工作量级别详情 - 使用GET方法与后端匹配
export const getWorkloadLevelDetail = (id) => {
  return service.get(`${api.levelsDetail}/${id}`)
}

// 更新工作量级别 - 使用PUT方法与后端匹配
export const updateWorkloadLevel = (data) => {
  const { id, ...updateData } = data
  return service.put(`${api.levelsUpdate}/${id}`, updateData)
}

// 删除工作量级别 - 使用DELETE方法与后端匹配
export const deleteWorkloadLevel = (id) => {
  return service.delete(`${api.levelsDelete}/${id}`)
}

// 批量删除工作量级别 - 使用POST方法与后端匹配
export const batchDeleteWorkloadLevels = (data) => {
  return service.post(api.levelsBatchDelete, data)
}

// ==================== 参与者管理相关接口 ====================

// 获取教学工作量参与者列表
export const getWorkloadParticipantsList = (params) => {
  return service.get(api.participantsList, { params })
}

// 创建教学工作量参与者
export const createWorkloadParticipant = (data) => {
  return service.post(api.participantsCreate, data)
}

// 获取教学工作量参与者详情
export const getWorkloadParticipantDetail = (id) => {
  return service.get(`${api.participantsDetail}/${id}`)
}

// 更新教学工作量参与者
export const updateWorkloadParticipant = (data) => {
  const { id, ...updateData } = data
  return service.put(`${api.participantsUpdate}/${id}`, updateData)
}

// 删除教学工作量参与者
export const deleteWorkloadParticipant = (id) => {
  return service.delete(`${api.participantsDelete}/${id}`)
}

// ==================== 统计相关接口 ====================

// 获取审核状态统计
export const getReviewStatusStats = (data) => {
  return service.post(api.reviewStatusStats, data)
}

// 获取课程类型统计
export const getCourseTypeStats = (data) => {
  return service.post(api.courseTypeStats, data)
}

// 获取学期统计
export const getSemesterStats = (data) => {
  return service.post(api.semesterStats, data)
}

// 获取得分统计
export const getScoreStats = (data) => {
  return service.post(api.scoreStats, data)
}

// ==================== 排行榜相关接口 ====================

// 获取教师教学工作量排名
export const getTeacherWorkloadRanking = (data) => {
  return service.post(api.teacherRanking, data)
}

// 获取用户教学工作量详情
export const getUserWorkloadDetails = (data) => {
  return service.post(api.userWorkloadDetails, data)
}

// 获取教学工作量总分统计
export const getTeachingWorkloadsTotalScore = (data) => {
  return service.post(api.totalScoreStats, data)
}

// 获取用户教学工作量详情列表及得分
export const getUserWorkloadsDetail = (data) => {
  return service.post(api.userWorkloadsDetail, data)
}
