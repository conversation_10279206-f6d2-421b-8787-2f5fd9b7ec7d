<template>
  <div class="notification-send">
    <a-card title="发送通知" :bordered="false">
      <!-- <template #extra>
        <a-button @click="goToTemplates">
          <template #icon><AppstoreOutlined /></template>
          模板管理
        </a-button>
      </template>
       -->
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="选择模板" name="templateId">
          <a-select
            v-model:value="formState.templateId"
            placeholder="请选择通知模板（可选）"
            allowClear
            @change="handleTemplateChange"
            style="width: 100%"
          >
            <a-select-option v-for="template in templates" :key="template.id" :value="template.id">
              {{ template.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="通知标题" name="title">
          <a-input v-model:value="formState.title" placeholder="请输入通知标题" />
        </a-form-item>

        <a-form-item label="通知类型" name="type">
          <a-select
            v-model:value="formState.type"
            placeholder="请选择通知类型"
            :options="typeOptions"
          />
        </a-form-item>

        <a-form-item label="发送方式" name="sendMode">
          <a-radio-group v-model:value="formState.sendMode" @change="handleTargetTypeChange">
            <a-radio value="all">全员发送</a-radio>
            <a-radio value="department">按部门发送</a-radio>
            <a-radio value="user_level">按用户级别发送</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item
          v-if="formState.sendMode === 'department'"
          label="选择部门"
          name="targetDepartmentId"
        >
          <a-select
            v-model:value="formState.targetDepartmentId"
            placeholder="请选择部门"
            :loading="departmentsLoading"
            :options="departmentOptions"
          />
        </a-form-item>

        <a-form-item
          v-if="formState.sendMode === 'user_level'"
          label="选择用户级别"
          name="targetUserLevelId"
        >
          <a-select
            v-model:value="formState.targetUserLevelId"
            placeholder="请选择用户级别"
            :loading="userLevelsLoading"
            :options="userLevelOptions"
          />
        </a-form-item>

        <a-form-item
          v-if="formState.sendMode === 'single'"
          label="选择用户"
          name="userId"
        >
          <a-select
            v-model:value="formState.userId"
            placeholder="请选择用户"
            show-search
            :filter-option="false"
            :loading="usersLoading"
            @search="handleUserSearch"
            @change="handleUserChange"
          >
            <a-select-option v-for="user in userOptions" :key="user.value" :value="user.value">
              {{ user.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="通知内容" name="content">
          <a-textarea
            v-model:value="formState.content"
            :rows="6"
            placeholder="请输入通知内容"
          />
          <div class="template-placeholder" v-if="selectedTemplateDescription">
            <a-alert type="info" show-icon>
              <template #message>模板说明</template>
              <template #description>{{ selectedTemplateDescription }}</template>
            </a-alert>
          </div>
        </a-form-item>

        <!-- <a-form-item label="附件" name="attachments">
          <a-upload
            v-model:file-list="fileList"
            :action="uploadUrl"
            :before-upload="beforeUpload"
          >
            <a-button>
              <template #icon><UploadOutlined /></template>
              选择文件
            </a-button>
          </a-upload>
        </a-form-item> -->

        <!-- <a-form-item label="发送时间" name="sendTime">
          <a-radio-group v-model:value="formState.sendTimeType" @change="handleSendTimeChange">
            <a-radio value="now">立即发送</a-radio>
            <a-radio value="scheduled">定时发送</a-radio>
          </a-radio-group>
          <a-date-picker
            v-if="formState.sendTimeType === 'scheduled'"
            v-model:value="formState.scheduledTime"
            :show-time="{ format: 'HH:mm' }"
            format="YYYY-MM-DD HH:mm"
            style="width: 100%; margin-top: 8px;"
            :disabled-date="disabledDate"
          />
        </a-form-item> -->

        <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit">发送</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { UploadOutlined, AppstoreOutlined } from '@ant-design/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  createNotification,
  getDepartmentsList,
  getUserLevelsList
} from '@/api/modules/api.notifications'
import { getAllDepartments } from '@/api/modules/api.departments'
import { usersSearch } from '@/api/modules/api.users'
// import { useAuthStore } from '@/stores/auth' // 不再需要，后端自动获取用户信息
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()
const formRef = ref(null)
// const authStore = useAuthStore() // 不再需要

// 模板数据
const templates = ref([
  {
    id: '1',
    name: '系统维护通知',
    type: 'system',
    title: '系统维护通知',
    content: '尊敬的用户：\n\n系统将于{date}进行维护升级，维护时间预计{hours}小时，维护期间系统将暂停服务。\n\n给您带来的不便，敬请谅解！',
    description: '用于系统维护时发送通知，需要填写维护日期和预计时长。',
    createTime: '2023-12-01 10:00:00'
  },
  {
    id: '2',
    name: '评分开始提醒',
    type: 'reminder',
    title: '{year}年度绩效评分工作开始通知',
    content: '各位老师：\n\n{year}年度绩效评分工作已经开始，请于{deadline}前完成相关数据填报和自评工作。\n\n评分系统访问地址：http://performance.jnu.edu.cn',
    description: '用于通知年度绩效评分工作开始，需要填写年份和截止日期。',
    createTime: '2023-12-05 14:30:00'
  },
  {
    id: '3',
    name: '评分结果公示',
    type: 'result',
    title: '{year}年度绩效评分结果公示',
    content: '各位老师：\n\n{year}年度绩效评分工作已经结束，评分结果已经公示。请登录系统查看您的评分结果，如有异议，请于{deadline}前提出申诉。',
    description: '用于通知评分结果公示，需要填写年份和申诉截止日期。',
    createTime: '2023-12-10 09:15:00'
  }
])

// 表单数据
const formState = reactive({
  templateId: '',
  title: '',
  type: 'normal',
  sendMode: 'all',
  targetDepartmentId: '',
  targetUserLevelId: '',
  userId: '',
  content: '',
  abstract: '',
  priority: 'normal',
  sendTimeType: 'now',
  scheduledTime: null
})

// 验证函数 - 需要在表单验证规则之前定义
const validateDepartment = (rule, value) => {
  if (formState.sendMode === 'department' && !value) {
    return Promise.reject('请选择部门')
  }
  return Promise.resolve()
}

const validateUserLevel = (rule, value) => {
  if (formState.sendMode === 'user_level' && !value) {
    return Promise.reject('请选择用户级别')
  }
  return Promise.resolve()
}

const validateUser = (rule, value) => {
  if (formState.sendMode === 'single' && !value) {
    return Promise.reject('请选择用户')
  }
  return Promise.resolve()
}

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入通知标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度在2-50个字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择通知类型', trigger: 'change' }
  ],
  sendMode: [
    { required: true, message: '请选择发送方式', trigger: 'change' }
  ],
  targetDepartmentId: [
    { required: true, message: '请选择部门', trigger: 'change', validator: validateDepartment }
  ],
  targetUserLevelId: [
    { required: true, message: '请选择用户级别', trigger: 'change', validator: validateUserLevel }
  ],
  userId: [
    { required: true, message: '请选择用户', trigger: 'change', validator: validateUser }
  ],
  content: [
    { required: true, message: '请输入通知内容', trigger: 'blur' },
    { min: 10, message: '内容不能少于10个字符', trigger: 'blur' }
  ]
}

// 选中的模板描述
const selectedTemplateDescription = computed(() => {
  if (!formState.templateId) return ''
  const template = templates.value.find(t => t.id === formState.templateId)
  return template ? template.description : ''
})

// 通知类型选项
const typeOptions = [
  { label: '系统通知', value: 'system' },
  { label: '重要通知', value: 'important' },
  { label: '提醒通知', value: 'reminder' },
  { label: '普通通知', value: 'normal' },
  { label: '结果通知', value: 'result' }
]

// 部门、用户级别和用户选项
const departmentOptions = ref([])
const userLevelOptions = ref([])
const userOptions = ref([])
const departmentsLoading = ref(false)
const userLevelsLoading = ref(false)
const usersLoading = ref(false)



// 加载部门列表
const loadDepartments = async () => {
  try {
    departmentsLoading.value = true
    const response = await getAllDepartments()
    console.log('部门列表响应:', response)

    if (response.code === 200) {
      departmentOptions.value = response.data.map(dept => ({
        label: dept.departmentName,
        value: dept.id
      }))
      console.log('部门选项:', departmentOptions.value)
    } else {
      console.error('部门列表响应格式错误:', response)
      message.error('获取部门列表失败')
    }
  } catch (error) {
    console.error('加载部门列表失败:', error)
    message.error('加载部门列表失败')
  } finally {
    departmentsLoading.value = false
  }
}

// 加载用户级别列表
const loadUserLevels = async () => {
  try {
    userLevelsLoading.value = true
    const response = await getUserLevelsList()
    console.log('用户级别列表响应:', response)

    if (response.code === 200) {
      userLevelOptions.value = response.data.map(level => ({
        label: level.levelName,
        value: level.id
      }))
      console.log('用户级别选项:', userLevelOptions.value)
    } else {
      console.error('用户级别列表响应格式错误:', response)
      message.error('获取用户级别列表失败')
    }
  } catch (error) {
    console.error('加载用户级别列表失败:', error)
    message.error('加载用户级别列表失败')
  } finally {
    userLevelsLoading.value = false
  }
}

// 加载用户列表
const loadUsers = async (searchKeyword = '') => {
  try {
    usersLoading.value = true
    const response = await usersSearch({
      keyword: searchKeyword,
      pageSize: 50 // 限制返回数量
    })
    console.log('用户列表响应:', response)

    if (response.code === 200) {
      userOptions.value = response.data.list.map(user => ({
        label: `${user.nickname || user.username} (${user.studentNumber || user.username})`,
        value: user.id
      }))
      console.log('用户选项:', userOptions.value)
    } else {
      console.error('用户列表响应格式错误:', response)
      message.error('获取用户列表失败')
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    message.error('加载用户列表失败')
  } finally {
    usersLoading.value = false
  }
}

// 处理用户搜索
const handleUserSearch = (value) => {
  if (value) {
    loadUsers(value)
  }
}

// 处理用户选择
const handleUserChange = (value) => {
  formState.userId = value
}

// 处理发送方式变化
const handleTargetTypeChange = (e) => {
  const sendMode = e.target.value
  formState.sendMode = sendMode

  // 清空相关字段
  formState.targetDepartmentId = ''
  formState.targetUserLevelId = ''
  formState.userId = ''

  // 根据选择的类型加载对应数据
  if (sendMode === 'department' && departmentOptions.value.length === 0) {
    loadDepartments()
  } else if (sendMode === 'user_level' && userLevelOptions.value.length === 0) {
    loadUserLevels()
  } else if (sendMode === 'single' && userOptions.value.length === 0) {
    loadUsers()
  }
}

// 文件上传相关
const fileList = ref([])
const uploadUrl = '/api/upload' // 替换为实际的上传接口

const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  return true
}

// 发送时间处理
const handleSendTimeChange = (e) => {
  if (e.target.value === 'now') {
    formState.scheduledTime = null
  } else if (!formState.scheduledTime) {
    // 默认设置为明天这个时间
    formState.scheduledTime = dayjs().add(1, 'day')
  }
}

// 不允许选择过去的日期
const disabledDate = (current) => {
  return current && current < dayjs().startOf('day')
}

// 处理模板选择
const handleTemplateChange = (value) => {
  if (!value) {
    return
  }
  
  const template = templates.value.find(t => t.id === value)
  if (template) {
    formState.title = template.title
    formState.type = template.type
    formState.content = template.content
  }
}

// 跳转到模板管理
const goToTemplates = () => {
  router.push('/notification/template')
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 构建通知数据
    const notificationData = {
      type: formState.type,
      title: formState.title,
      content: formState.content,
      abstract: formState.content.substring(0, 100), // 取前100个字符作为摘要
      sendMode: formState.sendMode
      // initiatorId 由后端自动获取，不需要前端传递
    }

    // 根据发送方式添加对应字段
    if (formState.sendMode === 'department') {
      notificationData.targetDepartmentId = formState.targetDepartmentId
    } else if (formState.sendMode === 'user_level') {
      notificationData.targetUserLevelId = formState.targetUserLevelId
    } else if (formState.sendMode === 'single') {
      notificationData.userId = formState.userId
    }

    // 处理定时发送
    if (formState.sendTimeType === 'scheduled' && formState.scheduledTime) {
      const scheduledTime = formState.scheduledTime.format('YYYY-MM-DD HH:mm:ss')
      message.warning(`定时发送功能暂未实现，将于 ${scheduledTime} 发送！`)
      return
    }

    // 发送通知
    const response = await createNotification(notificationData)

    if (response.code === 200) {
      message.success(`通知发送成功！共发送给 ${response.data.recipientCount} 个用户`)
      router.push('/notification/list')
    } else {
      message.error(response.message || '发送失败')
    }

  } catch (error) {
    console.error('发送通知失败:', error)
    if (error.response?.data?.message) {
      message.error(error.response.data.message)
    } else {
      message.error('发送通知失败，请重试')
    }
  }
}

// 重置表单
const handleReset = () => {
  formRef.value.resetFields()
  fileList.value = []
  formState.sendTimeType = 'now'
  formState.scheduledTime = null
  formState.templateId = ''
  formState.sendMode = 'all'
  formState.targetDepartmentId = ''
  formState.targetUserLevelId = ''
  formState.userId = ''
}

// 从URL参数中获取模板ID
onMounted(() => {
  const templateId = route.query.templateId
  if (templateId) {
    formState.templateId = templateId
    handleTemplateChange(templateId)
  }
})
</script>

<style scoped>
.notification-send {
  padding: 24px;
}

.template-placeholder {
  margin-top: 8px;
}
</style> 