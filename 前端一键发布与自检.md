下面是一份简洁的 **前端一键发布与自检** 文档（Markdown）。直接保存为 `DEPLOY_FRONTEND.md` 即可。

---

````markdown
# 前端一键发布与自检（Vue + Nginx，bind mount）

> 适用环境：
> - 前端目录：`/root/jxpd/jn-jxpd-admin`
> - 构建产物：`/root/jxpd/jn-jxpd-admin/dist`
> - 对外目录（绑定挂载）：`/var/www/jxpd-admin`
> - Nginx 已指向 `/var/www/jxpd-admin`

---

## 一键发布（拉代码 + 构建 + 部署）

> 脚本：`update_frontend.sh`（已放在 `/root/jxpd/jn-jxpd-admin/`）

**常用：拉代码 + 构建 + 部署 + 重载 Nginx**
```bash
cd /root/jxpd/jn-jxpd-admin
DO_GIT_PULL=1 ./update_frontend.sh
````

**仅构建部署，不重载 Nginx**

```bash
DO_NGINX_RELOAD=0 ./update_frontend.sh
```

**若 bind mount 失败，自动回退到 rsync（默认开启）**

```bash
FALLBACK_RSYNC=1 ./update_frontend.sh
```

---

## 快速自检

1. **确认挂载正常（不应出现 `/deleted`）**

```bash
findmnt /var/www/jxpd-admin
# 期望：SOURCE 指向 /root/jxpd/jn-jxpd-admin/dist（无 //deleted）
```

2. **确认文件已更新**

```bash
# 两端应一致（无输出表示一致）
diff -qr /root/jxpd/jn-jxpd-admin/dist /var/www/jxpd-admin | head || true
```

3. **确认 Nginx 返回最新 index.html**

```bash
# 本机回源
curl -sI http://127.0.0.1/ | grep -iE 'HTTP/1|Last-Modified|Content-Length'
# （可选）比对 dist/index.html 大小
wc -c /root/jxpd/jn-jxpd-admin/dist/index.html
```

4. **浏览器端强刷缓存**

* Windows：`Ctrl + F5`
* macOS：`Cmd + Shift + R`

---

## 常见问题与快速修复

### 1. `Device or resource busy`（更新时报）

**原因**：`dist/` 仍在被 bind mount 占用。
**修复**：脚本会自动 `umount -l` 再构建；如手工处理：

```bash
sudo umount -l /var/www/jxpd-admin
npm run build
sudo mount --bind /root/jxpd/jn-jxpd-admin/dist /var/www/jxpd-admin
```

### 2. `findmnt` 显示 `/dist//deleted`

**原因**：挂载存在时删除/重建了源目录。
**修复**：重新挂载一次即可：

```bash
sudo umount -l /var/www/jxpd-admin
sudo mount --bind /root/jxpd/jn-jxpd-admin/dist /var/www/jxpd-admin
```

### 3. 页面 405/404，接口 502

* **405**：Nginx 未把该前缀转发到后端（如 `/v1`）。

  * 确认 `location /v1/ { proxy_pass http://127.0.0.1:<端口>; }`
* **502**：后端未监听或端口不一致。

  * 查端口：`ss -ltnp | grep node`
  * 改 Nginx：`sed -i 's#127\.0\.0\.1:3000#127.0.0.1:<实际端口>#g' /etc/nginx/sites-available/jxpd-admin && nginx -t && systemctl reload nginx`

### 4. 登录 400（验证码/会话丢失）

**原因**：IP + HTTP 环境下，后端 session cookie 设了 `secure:true`。
**修复**：后端 `express-session` 改为：

```js
app.set('trust proxy', 1);
cookie: { secure: false, sameSite: 'lax' }
```

重启后端：`pm2 restart jxpd-server`

### 5. Nginx 报 `conflicting server name "_" on 0.0.0.0:80`

**原因**：启用了多个同为 `server_name _;` 的站点。
**修复**：`ls -1 /etc/nginx/sites-enabled/`，只保留一个，删除多余的 `*.conf` 链接，然后 `nginx -t && systemctl reload nginx`。

### 6. 访问是旧版本（缓存问题）

* 强制刷新（见“快速自检 #4”）
* 确认 Nginx 对静态资源有缓存头（可留 `immutable`），但 `index.html` 不缓存：

  * `location / { try_files $uri /index.html; }` 通常 OK

### 7. 没有 /var/www/jxpd-admin 导致挂载失败

**修复**：

```bash
sudo mkdir -p /var/www/jxpd-admin
sudo mount --bind /root/jxpd/jn-jxpd-admin/dist /var/www/jxpd-admin
```

**持久化**：写入 `/etc/fstab`：

```
/root/jxpd/jn-jxpd-admin/dist  /var/www/jxpd-admin  none  bind  0  0
```

---

## 附：常用排查命令

```bash
# Nginx 配置与日志
sudo nginx -t
sudo systemctl reload nginx
sudo tail -f /var/log/nginx/access.log /var/log/nginx/error.log

# 检查后端端口
ss -ltnp | grep -E ':3000|:3089'
pm2 list
pm2 logs jxpd-server --lines 100
```

> 只要运行：`DO_GIT_PULL=1 ./update_frontend.sh`
> 就能完成 **拉代码 → 构建 → 部署 →（可选）重载 Nginx** 的全流程。

```
---
```
