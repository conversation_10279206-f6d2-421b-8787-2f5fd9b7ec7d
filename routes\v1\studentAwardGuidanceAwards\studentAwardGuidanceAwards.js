const express = require('express');
const router = express.Router();
const studentAwardGuidanceAwardController = require('../../../controllers/v1/studentAwardGuidanceAwards/studentAwardGuidanceAwardsController');
const multer = require('multer');
const path = require('path');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建学生获奖指导奖项权限中间件函数
const awardsPermission = (action) => createModulePermission('studentAwardGuidanceAwards', action);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/student_award_guidance/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'student-award-guidance-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

/**
 * 获取获奖列表
 * @route POST /v1/sys/student-award-guidance/awards/list
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {string} awardName - 奖项名称（模糊搜索）
 * @param {string} levelId - 奖项级别ID
 * @param {string} awardDateStart - 获奖开始日期
 * @param {string} awardDateEnd - 获奖结束日期
 * @param {string} userId - 用户ID（可选，传入则获取用户参与的奖项）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 范围筛选，可选值：all, in, out
 * @param {string} reviewStatus - 审核状态筛选，可选值：all, rejected, pending, reviewed
 * @param {boolean} isExport - 是否导出数据，设为true时返回所有数据不分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, current: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/awards/list', 
  authMiddleware, 
  awardsPermission('list'), 
  studentAwardGuidanceAwardController.getAwards
);

/**
 * 导入获奖数据
 * @route POST /v1/sys/student-award-guidance/awards/import
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/awards/import', 
  authMiddleware, 
  awardsPermission('import'), 
  upload.single('file'), 
  studentAwardGuidanceAwardController.importAwards
);

/**
 * 导出获奖数据
 * @route POST /v1/sys/student-award-guidance/awards/export
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {string} awardName - 奖项名称（模糊搜索）
 * @param {string} levelId - 奖项级别ID
 * @param {string} awardDateStart - 获奖开始日期
 * @param {string} awardDateEnd - 获奖结束日期
 * @param {string} range - 范围筛选
 * @param {string} reviewStatus - 审核状态筛选
 * @returns {object} 200 - {code: 200, message: "导出成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/awards/export', 
  authMiddleware, 
  awardsPermission('export'), 
  studentAwardGuidanceAwardController.exportAwards
);

/**
 * 创建获奖记录
 * @route POST /v1/sys/student-award-guidance/award/create
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {string} awardName.body.required - 奖项名称
 * @param {string} department.body.required - 颁发部门
 * @param {string} awardDate.body.required - 获奖日期
 * @param {string} levelId.body.required - 奖项级别ID
 * @param {string} remark.body - 备注
 * @param {Array} participants.body.required - 参与者数组，包含userId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/award/create', 
  authMiddleware, 
  awardsPermission('create'), 
  upload.array('files', 5), 
  studentAwardGuidanceAwardController.createAward
);

/**
 * 更新获奖记录
 * @route POST /v1/sys/student-award-guidance/award/update
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {string} id.body.required - 奖项ID
 * @param {string} awardName.body - 奖项名称
 * @param {string} department.body - 颁发部门
 * @param {string} awardDate.body - 获奖日期
 * @param {string} levelId.body - 奖项级别ID
 * @param {string} remark.body - 备注
 * @param {Array} participants.body - 参与者数组，包含userId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @param {Array} deletedFileIds.body - 要删除的文件ID数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/award/update', 
  authMiddleware, 
  awardsPermission('update'), 
  upload.array('files', 5), 
  async (req, res) => {
    const { id, ...updateData } = req.body;
    req.params = { id };
    req.body = updateData;
    await studentAwardGuidanceAwardController.updateAward(req, res);
  }
);

/**
 * 删除获奖记录
 * @route POST /v1/sys/student-award-guidance/award/delete
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {string} id.body.required - 奖项ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/award/delete/:id', 
  authMiddleware, 
  awardsPermission('delete'), 
  studentAwardGuidanceAwardController.deleteAward
);

/**
 * 获取获奖详情
 * @route POST /v1/sys/student-award-guidance/award/detail
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {string} id.body.required - 奖项ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/award/detail', 
  authMiddleware, 
  awardsPermission('detail'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await studentAwardGuidanceAwardController.getAwardDetail(req, res);
  }
);

/**
 * 获取获奖时间分布数据
 * @route POST /v1/sys/student-award-guidance/statistics/time-distribution
 * @group 指导学生获奖统计 - 指导学生获奖统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的获奖
 * @param {string} reviewStatus - 审核状态: 'all', 'rejected', 'pending', 'reviewed'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {months: ["YYYY-MM",...], data: [数量,...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/time-distribution', 
  authMiddleware, 
  awardsPermission('timeDistribution'), 
  studentAwardGuidanceAwardController.getTimeDistribution
);

/**
 * 审核获奖记录
 * @route POST /v1/sys/student-award-guidance/award/review
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {string} id.body.required - 奖项ID
 * @param {boolean} reviewStatus.body.required - 审核状态
 * @param {string} reviewComment.body - 审核意见
 * @param {string} reviewer.body.required - 审核人ID
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/award/review', 
  authMiddleware, 
  awardsPermission('review'), 
  studentAwardGuidanceAwardController.reviewAward
);

/**
 * 获取获奖级别分布数据
 * @route POST /v1/sys/student-award-guidance/statistics/level-distribution
 * @group 指导学生获奖统计 - 指导学生获奖级别分布统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} userId - 用户ID，可选
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{levelName: '级别名称', count: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/level-distribution', 
  authMiddleware, 
  awardsPermission('levelDistribution'), 
  studentAwardGuidanceAwardController.getLevelDistribution
);

/**
 * 获取教师获奖排名数据
 * @route POST /v1/sys/student-award-guidance/statistics/teacher-ranking
 * @group 指导学生获奖统计 - 教师获奖排名统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} reviewStatus - 审核状态: 'all', 'rejected', 'pending', 'reviewed'
 * @param {number} page - 页码
 * @param {number} pageSize - 每页记录数
 * @param {boolean} isExport - 是否导出所有数据，导出时不应用分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{userId, userName, studentNumber, totalProjects, totalScore}], pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-ranking', 
  authMiddleware, 
  awardsPermission('teacherRanking'), 
  studentAwardGuidanceAwardController.getTeacherAwardRanking
);

/**
 * 获取教师获奖详情
 * @route POST /v1/sys/student-award-guidance/statistics/teacher-award-details
 * @group 指导学生获奖统计 - 教师获奖详情
 * @param {string} userId - 用户ID
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 数据范围
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], totalScore: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-award-details', 
  authMiddleware, 
  awardsPermission('teacherDetails'), 
  studentAwardGuidanceAwardController.getTeacherAwardDetails
);

/**
 * 获取获奖统计概览数据
 * @route POST /v1/sys/student-award-guidance/statistics/overview
 * @group 指导学生获奖统计 - 获奖统计概览
 * @param {string} userId - 用户ID，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalAwards, activeAwards, averageScore, reviewedRate}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/overview', 
  authMiddleware, 
  awardsPermission('overview'), 
  studentAwardGuidanceAwardController.getAwardStatistics
);

/**
 * 获取获奖总分统计
 * @route POST /v1/sys/student-award-guidance/statistics/awards-total-score
 * @group 指导学生获奖统计 - 获奖分数统计相关接口
 * @param {string} range - 数据范围: 'in' (统计范围内), 'out' (统计范围外), 'all' (全部，默认)
 * @param {string} reviewStatus - 审核状态: 'rejected' (已拒绝), 'pending' (待审核), 'reviewed' (已审核), 'all' (全部，默认)
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {levelStats: [{levelId, levelName, count, totalScore}], overallStats: {totalAwards, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/awards-total-score', 
  authMiddleware, 
  awardsPermission('totalScore'), 
  studentAwardGuidanceAwardController.getAwardsTotalScore
);

/**
 * 获取用户获奖详情
 * @route POST /v1/sys/student-award-guidance/user/details
 * @group 指导学生获奖统计 - 用户获奖详情相关接口
 * @param {string} userId.required - 用户ID
 * @param {string} range - 数据范围: 'in' (统计范围内), 'out' (统计范围外), 'all' (全部，默认)
 * @param {string} reviewStatus - 审核状态: 'rejected' (已拒绝), 'pending' (待审核), 'reviewed' (已审核), 'all' (全部，默认)
 * @param {number} page - 页码，默认1 
 * @param {number} pageSize - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{奖项详情}], totalCount: 总记录数, statistics: {totalAwards, leaderAwardCount, participantAwardCount, totalScore}, timeInterval: {startTime, endTime, name}, pagination: {page, pageSize, total}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/details', 
  authMiddleware, 
  awardsPermission('userDetails'), 
  studentAwardGuidanceAwardController.getUserAwardsDetail
);

/**
 * 重新提交学生获奖指导奖项审核
 * @route POST /v1/sys/student-award-guidance/award/reapply
 * @group 指导学生获奖管理 - 指导学生获奖相关接口
 * @param {string} id.body.required - 奖项ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/award/reapply',
  authMiddleware,
  awardsPermission('reapply'),
  studentAwardGuidanceAwardController.reapply
);

/**
 * 获取审核状态概览
 * @route POST /v1/sys/student-award-guidance-awards/statistics/review-status-overview
 * @group 学生获奖指导统计 - 学生获奖指导统计相关接口
 * @param {string} range.body - 查询范围：'in'|'out'|'all'，默认'all'
 * @param {string} userId.body - 用户ID，可选
 * @returns {object} 200 - 审核状态统计数据
 * @security JWT
 */
router.post('/statistics/review-status-overview',
  authMiddleware,
  awardsPermission('reviewStatusOverview'),
  studentAwardGuidanceAwardController.getReviewStatusOverview
);

module.exports = router;