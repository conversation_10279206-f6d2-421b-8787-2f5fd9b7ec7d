<template>
  <div class="backup-management">
    <a-card title="数据库备份管理">
      <!-- 操作按钮区域 -->
      <template #extra>
        <a-space>
          <a-button @click="refreshData" :loading="loading.refresh">
            <template #icon><ReloadOutlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="createBackup" :loading="loading.create">
            <template #icon><DatabaseOutlined /></template>
            立即备份
          </a-button>
        </a-space>
      </template>

      <!-- 备份配置信息 -->
      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="8">
          <a-statistic title="备份文件总数" :value="backupStats.totalFiles" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="总占用空间" :value="backupStats.totalSize" suffix="MB" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="最近备份" :value="backupStats.lastBackupTime" />
        </a-col>
      </a-row>

      <!-- 定时任务状态 -->
      <a-card size="small" title="定时任务状态" style="margin-bottom: 24px;">
        <a-descriptions :column="2">
          <a-descriptions-item label="备份任务状态">
            <a-tag :color="schedulerStatus.backup?.running ? 'green' : 'red'">
              {{ schedulerStatus.backup?.running ? '运行中' : '已停止' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="下次执行时间">
            每月1号凌晨2点
          </a-descriptions-item>
        </a-descriptions>
        <a-space style="margin-top: 16px;">
          <a-button 
            v-if="!schedulerStatus.backup?.running"
            @click="startScheduler" 
            :loading="loading.scheduler"
            type="primary"
            size="small"
          >
            启动定时任务
          </a-button>
          <a-button 
            v-else
            @click="stopScheduler" 
            :loading="loading.scheduler"
            danger
            size="small"
          >
            停止定时任务
          </a-button>
          <a-button @click="getSchedulerStatus" :loading="loading.schedulerStatus" size="small">
            刷新状态
          </a-button>
        </a-space>
      </a-card>

      <!-- 备份文件列表 -->
      <a-table
        :columns="columns"
        :data-source="backupList"
        :loading="loading.list"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="fileName"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'fileName'">
            <a-tooltip :title="record.fileName">
              <span>{{ record.fileName }}</span>
            </a-tooltip>
          </template>
          
          <template v-if="column.key === 'size'">
            <span>{{ record.sizeFormatted }}</span>
          </template>
          
          <template v-if="column.key === 'createdAt'">
            <span>{{ record.createdAtFormatted }}</span>
          </template>
          
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button 
                size="small" 
                @click="downloadBackup(record)"
                :loading="loading.download === record.fileName"
              >
                <template #icon><DownloadOutlined /></template>
                下载
              </a-button>
              <a-popconfirm
                title="确定要删除这个备份文件吗？"
                @confirm="deleteBackup(record)"
              >
                <a-button 
                  size="small" 
                  danger
                  :loading="loading.delete === record.fileName"
                >
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  DatabaseOutlined,
  DownloadOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const backupList = ref([])
const schedulerStatus = ref({})
const loading = ref({
  list: false,
  create: false,
  refresh: false,
  download: null,
  delete: null,
  scheduler: false,
  schedulerStatus: false
})

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列定义
const columns = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    key: 'fileName',
    ellipsis: true
  },
  {
    title: '文件大小',
    dataIndex: 'size',
    key: 'size',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 160
  }
]

// 计算备份统计信息
const backupStats = computed(() => {
  const totalFiles = backupList.value.length
  const totalSize = backupList.value.reduce((sum, file) => sum + (file.size || 0), 0) / (1024 * 1024)
  const lastBackupTime = backupList.value.length > 0 
    ? backupList.value[0].createdAtFormatted 
    : '暂无备份'
  
  return {
    totalFiles,
    totalSize: totalSize.toFixed(2),
    lastBackupTime
  }
})

// API调用函数
const apiCall = async (url, options = {}) => {
  const token = localStorage.getItem('zyadmin-1.0.0-token')
  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  })
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }
  
  return await response.json()
}

// 获取备份文件列表
const getBackupList = async () => {
  try {
    loading.value.list = true
    const result = await apiCall('/v1/admin/backup/list')
    
    if (result.status === 1) {
      backupList.value = result.data.list
      pagination.value.total = result.data.total
    } else {
      message.error('获取备份列表失败: ' + result.message)
    }
  } catch (error) {
    console.error('获取备份列表失败:', error)
    message.error('获取备份列表失败')
  } finally {
    loading.value.list = false
  }
}

// 创建备份
const createBackup = async () => {
  try {
    loading.value.create = true
    const result = await apiCall('/v1/admin/backup/create', { method: 'POST' })
    
    if (result.status === 1) {
      message.success('备份创建成功')
      await getBackupList()
    } else {
      message.error('备份创建失败: ' + result.message)
    }
  } catch (error) {
    console.error('创建备份失败:', error)
    message.error('创建备份失败')
  } finally {
    loading.value.create = false
  }
}

// 下载备份文件
const downloadBackup = async (record) => {
  try {
    loading.value.download = record.fileName
    const token = localStorage.getItem('zyadmin-1.0.0-token')
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = `/v1/admin/backup/download/${record.fileName}?token=${token}`
    link.download = record.fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    message.success('开始下载备份文件')
  } catch (error) {
    console.error('下载备份文件失败:', error)
    message.error('下载备份文件失败')
  } finally {
    loading.value.download = null
  }
}

// 删除备份文件
const deleteBackup = async (record) => {
  try {
    loading.value.delete = record.fileName
    const result = await apiCall(`/v1/admin/backup/${record.fileName}`, { method: 'DELETE' })
    
    if (result.status === 1) {
      message.success('备份文件删除成功')
      await getBackupList()
    } else {
      message.error('删除备份文件失败: ' + result.message)
    }
  } catch (error) {
    console.error('删除备份文件失败:', error)
    message.error('删除备份文件失败')
  } finally {
    loading.value.delete = null
  }
}

// 获取定时任务状态
const getSchedulerStatus = async () => {
  try {
    loading.value.schedulerStatus = true
    const result = await apiCall('/v1/admin/backup/scheduler/status')
    
    if (result.status === 1) {
      schedulerStatus.value = result.data.tasks
    } else {
      message.error('获取任务状态失败: ' + result.message)
    }
  } catch (error) {
    console.error('获取任务状态失败:', error)
    message.error('获取任务状态失败')
  } finally {
    loading.value.schedulerStatus = false
  }
}

// 启动定时任务
const startScheduler = async () => {
  try {
    loading.value.scheduler = true
    const result = await apiCall('/v1/admin/backup/scheduler/start', { method: 'POST' })
    
    if (result.status === 1) {
      message.success('定时任务启动成功')
      await getSchedulerStatus()
    } else {
      message.error('启动定时任务失败: ' + result.message)
    }
  } catch (error) {
    console.error('启动定时任务失败:', error)
    message.error('启动定时任务失败')
  } finally {
    loading.value.scheduler = false
  }
}

// 停止定时任务
const stopScheduler = async () => {
  try {
    loading.value.scheduler = true
    const result = await apiCall('/v1/admin/backup/scheduler/stop', { method: 'POST' })
    
    if (result.status === 1) {
      message.success('定时任务停止成功')
      await getSchedulerStatus()
    } else {
      message.error('停止定时任务失败: ' + result.message)
    }
  } catch (error) {
    console.error('停止定时任务失败:', error)
    message.error('停止定时任务失败')
  } finally {
    loading.value.scheduler = false
  }
}

// 刷新所有数据
const refreshData = async () => {
  loading.value.refresh = true
  try {
    await Promise.all([
      getBackupList(),
      getSchedulerStatus()
    ])
  } finally {
    loading.value.refresh = false
  }
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  getBackupList()
}

// 组件挂载时初始化
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.backup-management {
  padding: 24px;
}
</style>
