const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const patentModel = require('./patentsModel');
const userModel = require('./userModel');

// 定义专利参与者模型
const PatentParticipant = sequelize.define('patent_participants', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    patentId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '专利ID'
    },
    participantId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '参与者ID'
    },
    allocationRatio: {
        type: DataTypes.DECIMAL(10, 4),
        allowNull: false,
        defaultValue: 0,
        comment: '分配比例'
    },
    isLeader: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 0,
        comment: '是否是负责人：0-不是，1-是'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'patent_participants',
    timestamps: true,
    indexes: [
        {
            name: 'idx_patent_participant_patent',
            fields: ['patentId']
        },
        {
            name: 'idx_patent_participant_user',
            fields: ['participantId']
        },
        {
            name: 'idx_patent_participant_leader',
            fields: ['isLeader']
        }
    ]
});

// 建立与专利的关联关系
PatentParticipant.belongsTo(patentModel, {
    foreignKey: 'patentId',
    as: 'patent'
});

// 建立与用户的关联关系
PatentParticipant.belongsTo(userModel, {
    foreignKey: 'participantId',
    as: 'participant'
});

module.exports = PatentParticipant; 