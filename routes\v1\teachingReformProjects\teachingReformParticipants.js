const express = require('express');
const router = express.Router();
const participantController = require('../../../controllers/v1/teachingReformProjects/teachingReformParticipantsController');

/**
 * 获取用户项目总得分
 * @route POST /v1/sys/teaching-reform-participant/statistics/user-total-score
 * @group 教学改革项目参与者统计 - 教学改革项目参与者统计相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {object} timeRange.body - 可选的自定义时间范围，包含startDate和endDate字段
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {userId: "用户ID", totalScore: 总分, list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/user-total-score', participantController.getUserTotalScore);

/**
 * 获取所有用户项目总得分统计
 * @route POST /v1/sys/teaching-reform-participant/statistics/all-users-total-score
 * @group 教学改革项目参与者统计 - 教学改革项目参与者统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {object} timeRange.body - 可选的自定义时间范围，包含startDate和endDate字段
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @param {string} sortField.body - 排序字段，可选值：'totalScore'(总分),'projectCount'(项目数),'leaderProjectCount'(主持项目数)，默认'totalScore'
 * @param {string} sortOrder.body - 排序方向，可选值：'asc'(升序),'desc'(降序)，默认'desc'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/all-users-total-score', participantController.getAllUsersTotalScore);

module.exports = router; 