const express = require('express');
const router = express.Router();
const paperController = require('../../../controllers/v1/highLevelPapers/highLevelPapersController');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建高水平论文权限中间件函数
const papersPermission = (action) => createModulePermission('highLevelPapers', action);

/**
 * 获取论文列表
 * @route GET /v1/sys/high-level-papers/list
 * @group 论文管理 - 高水平论文相关接口
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @param {string} title.query - 论文题目（模糊搜索）
 * @param {string} paperLevelId.query - 论文级别ID
 * @param {string} type.query - 论文类型（根据paperLevel值查询）
 * @param {string} journal.query - 期刊名称（模糊搜索）
 * @param {string} authors.query - 作者名称（模糊搜索）
 * @param {string} publishStartDate.query - 发表开始日期
 * @param {string} publishEndDate.query - 发表结束日期
 * @param {string} userId.query - 作者ID
 * @param {string} reviewStatus.query - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {boolean} isExport.query - 是否为导出操作，如为true则不进行分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {page: 1, pageSize: 10, total: 0, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list', 
  authMiddleware, 
  papersPermission('list'), 
  paperController.getPapers
);

/**
 * 获取论文统计数据
 * @route GET /v1/sys/high-level-papers/stats
 * @group 论文管理 - 高水平论文相关接口
 * @param {boolean} userOnly.query - 是否只统计当前用户的论文
 * @param {string} year.query - 指定统计年份
 * @param {string} userId.query - 用户ID，如果未提供则使用当前登录用户ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {paperCount, totalScore, rank, totalUsers, typeDistribution, yearlyDistribution}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats', 
  authMiddleware, 
  papersPermission('getStats'), 
  paperController.getPaperStats
);

/**
 * 获取论文类型分布数据（支持范围过滤）
 * @route POST /v1/sys/high-level-papers/type-distribution
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.body - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {string} reviewStatus.body - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的论文，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name, value}, ...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/type-distribution', 
  authMiddleware, 
  papersPermission('typeDistribution'), 
  paperController.getPaperTypeDistribution
);

/**
 * 获取论文类型分布数据（支持范围过滤）- GET方法
 * @route GET /v1/sys/high-level-papers/type-distribution
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.query - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {string} reviewStatus.query - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {string} userId.query - 用户ID，如果提供则只统计该用户参与的论文，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name, value}, ...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/type-distribution', 
  authMiddleware, 
  papersPermission('typeDistribution'), 
  paperController.getPaperTypeDistribution
);

/**
 * 获取论文年度分布数据（支持范围过滤）
 * @route POST /v1/sys/high-level-papers/yearly-distribution
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.body - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {string} reviewStatus.body - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的论文，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {months: [], data: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/yearly-distribution', 
  authMiddleware, 
  papersPermission('yearlyDistribution'), 
  paperController.getPaperYearlyDistribution
);

/**
 * 获取论文年度分布数据（支持范围过滤）- GET方法
 * @route GET /v1/sys/high-level-papers/yearly-distribution
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.query - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {string} reviewStatus.query - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {string} userId.query - 用户ID，如果提供则只统计该用户参与的论文，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {months: [], data: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/yearly-distribution', 
  authMiddleware, 
  papersPermission('yearlyDistribution'), 
  paperController.getPaperYearlyDistribution
);

/**
 * 获取论文影响因子分布数据（支持范围过滤）
 * @route POST /v1/sys/high-level-papers/impact-factor-distribution
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.body - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {string} reviewStatus.body - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的论文，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {"0-1": 10, "1-2": 5, ...}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/impact-factor-distribution', 
  authMiddleware, 
  papersPermission('impactFactorDistribution'), 
  paperController.getPaperImpactFactorDistribution
);

/**
 * 获取论文影响因子分布数据（支持范围过滤）- GET方法
 * @route GET /v1/sys/high-level-papers/impact-factor-distribution
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.query - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {string} reviewStatus.query - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {string} userId.query - 用户ID，如果提供则只统计该用户参与的论文，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {"0-1": 10, "1-2": 5, ...}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/impact-factor-distribution', 
  authMiddleware, 
  papersPermission('impactFactorDistribution'), 
  paperController.getPaperImpactFactorDistribution
);

/**
 * 获取教师排名分布数据（支持范围过滤）
 * @route POST /v1/sys/high-level-papers/teacher-ranking-distribution
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.body - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {string} reviewStatus.body - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {number} limit.body - 返回的排名数量，默认10
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的论文，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 100, list: [{name, score, paperCount, rank}, ...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/teacher-ranking-distribution', 
  authMiddleware, 
  papersPermission('teacherRanking'), 
  paperController.getPaperTeacherRanking
);

/**
 * 获取教师排名分布数据（支持范围过滤）- GET方法
 * @route GET /v1/sys/high-level-papers/teacher-ranking-distribution
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.query - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {string} reviewStatus.query - 审核状态: 'reviewed'(已审核), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部), 默认'all'
 * @param {number} limit.query - 返回的排名数量，默认10
 * @param {string} userId.query - 用户ID，如果提供则只统计该用户参与的论文，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 100, list: [{name, score, paperCount, rank}, ...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/teacher-ranking-distribution', 
  authMiddleware, 
  papersPermission('teacherRanking'), 
  paperController.getPaperTeacherRanking
);

/**
 * 导出论文数据
 * @route GET /v1/sys/high-level-papers/export
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} title.query - 论文题目（模糊搜索）
 * @param {string} journal.query - 期刊名称（模糊搜索）
 * @param {string} publishStartDate.query - 发表开始日期
 * @param {string} publishEndDate.query - 发表结束日期
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/export', 
  authMiddleware, 
  papersPermission('export'), 
  paperController.exportPapers
);

/**
 * 导入论文数据
 * @route POST /v1/sys/high-level-papers/import
 * @group 论文管理 - 高水平论文相关接口
 * @param {file} file.body.required - Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', 
  authMiddleware, 
  papersPermission('import'), 
  upload.single('file'), 
  paperController.importPapers
);

/**
 * 获取论文详情
 * @route GET /v1/sys/high-level-papers/:id
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} id.path.required - 论文ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {论文详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail/:id', 
  authMiddleware, 
  papersPermission('detail'), 
  paperController.getPaperById
);

/**
 * 创建论文
 * @route POST /v1/sys/high-level-papers
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} title.body.required - 论文题目
 * @param {string} journal.body.required - 期刊名称
 * @param {string} paperLevelId.body.required - 论文级别ID
 * @param {string} doi.body - DOI标识
 * @param {string} publishDate.body.required - 发表日期
 * @param {number} impactFactor.body - 影响因子
 * @param {boolean} isFirstAffiliationOurs.body - 第一单位是否我们大学，默认true
 * @param {array} files.formData - 上传的文件数组
 * @param {array} fileIds.body - 文件ID数组
 * @param {array} attachmentUrl.body - 附件URL数组
 * @param {string} submitterId.body - 提交人ID
 * @param {array} participants.body.required - 参与者数组，格式：[{userId, allocationRatio, authorRank, isFirstAuthor, isCorrespondingAuthor}]
 * @returns {object} 201 - {code: 201, message: "创建成功", data: {id: "论文ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/', 
  authMiddleware, 
  papersPermission('create'), 
  upload.array('files', 5), 
  paperController.createPaper
);

/**
 * 更新论文
 * @route PUT /v1/sys/high-level-papers/:id
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} id.path.required - 论文ID
 * @param {string} title.body - 论文题目
 * @param {string} journal.body - 期刊名称
 * @param {string} paperLevelId.body - 论文级别ID
 * @param {string} doi.body - DOI标识
 * @param {string} publishDate.body - 发表日期
 * @param {number} impactFactor.body - 影响因子
 * @param {boolean} isFirstAffiliationOurs.body - 第一单位是否我们大学
 * @param {string} attachmentUrl.body - 附件URL
 * @param {string} submitterId.body - 提交人ID
 * @param {array} participants.body - 参与者数组，格式：[{userId, allocationRatio, authorRank, isFirstAuthor, isCorrespondingAuthor}]
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {论文详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update/:id', 
  authMiddleware, 
  papersPermission('update'), 
  upload.array('files', 5), 
  paperController.updatePaper
);

/**
 * 删除论文
 * @route DELETE /v1/sys/high-level-papers/delete/:id
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} id.path.required - 论文ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete/:id', 
  authMiddleware, 
  papersPermission('delete'), 
  paperController.deletePaper
);

/**
 * 获取用户论文总得分
 * @route POST /v1/sys/high-level-papers/user-total-score
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {userId, totalScore, list: [], pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user-total-score', 
  authMiddleware, 
  papersPermission('userTotalScore'), 
  paperController.getUserTotalScore
);

/**
 * 获取所有用户论文得分排名
 * @route POST /v1/sys/high-level-papers/teacher-ranking
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} range.body - 数据范围: 'in'(统计范围内), 'out'(统计范围外), 'all'(全部), 默认'all'
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页数量，默认10
 * @param {string} nickname.body - 用户昵称（模糊搜索）
 * @param {string} paperId.body - 论文ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/teacher-ranking', 
  authMiddleware, 
  papersPermission('allUsersTotalScore'), 
  paperController.getAllUsersTotalScore
);

/**
 * 论文审核
 * @route POST /v1/sys/high-level-papers/:id/review
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} id.path.required - 论文ID
 * @param {number} reviewStatus.body - 审核状态(1:通过 0:拒绝)
 * @param {string} reviewComment.body - 审核意见
 * @param {string} reviewer.body - 审核人ID
 * @returns {object} 200 - {code: 200, message: "审核成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/:id/review', 
  authMiddleware, 
  papersPermission('review'), 
  paperController.reviewPaper
);

/**
 * 获取高水平论文总分统计（按级别和总体）
 * @route POST /v1/sys/high-level-papers/statistics/papers-total-score
 * @group 论文统计 - 论文统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)，默认'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {levelStats: [{levelId, levelName, count, totalScore},...], totalStats: {totalPapers, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/papers-total-score', 
  authMiddleware, 
  papersPermission('paperisTotalScore'), 
  paperController.getPapersTotalScore
);

/**
 * 获取用户高水平论文详情列表及得分
 * @route POST /v1/sys/high-level-papers/user/details
 * @group 论文管理 - 论文相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)，默认'all'
 * @param {number} pageSize.body - 每页记录数，默认10
 * @param {number} pageNum.body - 当前页码，默认1
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalCount, pageSize, pageNum, papers: [{paperId, paperTitle, journal, publishDate, levelName, baseScore, allocationRatio, actualScore, reviewStatus}], stats: {totalPapers, firstAuthorCount, correspondingAuthorCount, participantPaperCount, totalScore}, timeInterval: {startTime, endTime, name}, user: {id, name, employeeNumber}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/details', 
  authMiddleware, 
  papersPermission('userPapersDetail'), 
  paperController.getUserPapersDetail
);

/**
 * 重新提交高水平论文审核
 * @route POST /v1/sys/high-level-papers/reapply
 * @group 论文管理 - 高水平论文相关接口
 * @param {string} id.body.required - 论文ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/reapply',
  authMiddleware,
  papersPermission('reapply'),
  paperController.reapply
);

/**
 * 获取审核状态概览
 * @route POST /v1/sys/high-level-papers/statistics/review-status-overview
 * @group 论文统计 - 论文统计相关接口
 * @param {string} range.body - 查询范围：'in'|'out'|'all'，默认'all'
 * @param {string} userId.body - 用户ID，可选
 * @returns {object} 200 - 审核状态统计数据
 * @security JWT
 */
router.post('/statistics/review-status-overview',
  authMiddleware,
  papersPermission('reviewStatusOverview'),
  paperController.getReviewStatusOverview
);

module.exports = router;