/**
 * 数据库备份管理控制器
 * 提供备份相关的API接口
 */

const { runBackupProcess, getBackupList } = require('../../../utils/databaseBackup');
const { scheduler } = require('../../../utils/scheduler');
const apiResponse = require('../../../utils/apiResponse');
const { authMiddleware, checkApiPermission } = require('../../../middleware/authMiddleware');

/**
 * 获取备份文件列表
 */
exports.getBackupList = [
    authMiddleware,
    checkApiPermission('admin:backup:view'),
    async (req, res) => {
        try {
            const backupList = getBackupList();
            
            return apiResponse.successResponseWithData(res, "获取备份列表成功", {
                list: backupList,
                total: backupList.length
            });
        } catch (error) {
            console.error('获取备份列表失败:', error);
            return apiResponse.ErrorResponse(res, "获取备份列表失败");
        }
    }
];

/**
 * 手动执行备份
 */
exports.createBackup = [
    authMiddleware,
    checkApiPermission('admin:backup:create'),
    async (req, res) => {
        try {
            console.log('收到手动备份请求');
            
            const result = await runBackupProcess();
            
            if (result.success) {
                return apiResponse.successResponseWithData(res, "数据库备份成功", {
                    backupFilePath: result.backupFilePath,
                    backupList: result.backupList
                });
            } else {
                return apiResponse.ErrorResponse(res, result.message || "数据库备份失败");
            }
        } catch (error) {
            console.error('手动备份失败:', error);
            return apiResponse.ErrorResponse(res, "备份执行失败");
        }
    }
];

/**
 * 获取定时任务状态
 */
exports.getSchedulerStatus = [
    authMiddleware,
    checkApiPermission('admin:backup:view'),
    async (req, res) => {
        try {
            const status = scheduler.getAllTasksStatus();
            
            return apiResponse.successResponseWithData(res, "获取任务状态成功", {
                tasks: status,
                isInitialized: scheduler.isInitialized
            });
        } catch (error) {
            console.error('获取任务状态失败:', error);
            return apiResponse.ErrorResponse(res, "获取任务状态失败");
        }
    }
];

/**
 * 启动定时备份任务
 */
exports.startBackupTask = [
    authMiddleware,
    checkApiPermission('admin:backup:manage'),
    async (req, res) => {
        try {
            scheduler.startTask('backup');
            
            return apiResponse.successResponse(res, "备份任务已启动");
        } catch (error) {
            console.error('启动备份任务失败:', error);
            return apiResponse.ErrorResponse(res, "启动备份任务失败");
        }
    }
];

/**
 * 停止定时备份任务
 */
exports.stopBackupTask = [
    authMiddleware,
    checkApiPermission('admin:backup:manage'),
    async (req, res) => {
        try {
            scheduler.stopTask('backup');
            
            return apiResponse.successResponse(res, "备份任务已停止");
        } catch (error) {
            console.error('停止备份任务失败:', error);
            return apiResponse.ErrorResponse(res, "停止备份任务失败");
        }
    }
];

/**
 * 下载备份文件
 */
exports.downloadBackup = [
    authMiddleware,
    checkApiPermission('admin:backup:download'),
    async (req, res) => {
        try {
            const { fileName } = req.params;
            
            if (!fileName) {
                return apiResponse.validationErrorWithData(res, "文件名不能为空");
            }
            
            const backupList = getBackupList();
            const backupFile = backupList.find(file => file.fileName === fileName);
            
            if (!backupFile) {
                return apiResponse.notFoundResponse(res, "备份文件不存在");
            }
            
            // 设置下载响应头
            res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
            res.setHeader('Content-Type', 'application/octet-stream');
            
            // 发送文件
            res.sendFile(backupFile.filePath, (err) => {
                if (err) {
                    console.error('下载备份文件失败:', err);
                    if (!res.headersSent) {
                        return apiResponse.ErrorResponse(res, "下载文件失败");
                    }
                }
            });
            
        } catch (error) {
            console.error('下载备份文件失败:', error);
            return apiResponse.ErrorResponse(res, "下载文件失败");
        }
    }
];

/**
 * 删除备份文件
 */
exports.deleteBackup = [
    authMiddleware,
    checkApiPermission('admin:backup:delete'),
    async (req, res) => {
        try {
            const { fileName } = req.params;
            
            if (!fileName) {
                return apiResponse.validationErrorWithData(res, "文件名不能为空");
            }
            
            const fs = require('fs');
            const path = require('path');
            const { BACKUP_CONFIG } = require('../../../utils/databaseBackup');
            
            const filePath = path.join(BACKUP_CONFIG.backupDir, fileName);
            
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                return apiResponse.notFoundResponse(res, "备份文件不存在");
            }
            
            // 检查文件名是否为备份文件
            if (!fileName.startsWith(BACKUP_CONFIG.filePrefix)) {
                return apiResponse.validationErrorWithData(res, "无效的备份文件");
            }
            
            // 删除文件
            fs.unlinkSync(filePath);
            
            console.log(`备份文件已删除: ${fileName}`);
            
            return apiResponse.successResponse(res, "备份文件删除成功");
            
        } catch (error) {
            console.error('删除备份文件失败:', error);
            return apiResponse.ErrorResponse(res, "删除文件失败");
        }
    }
];

/**
 * 获取备份配置信息
 */
exports.getBackupConfig = [
    authMiddleware,
    checkApiPermission('admin:backup:view'),
    async (req, res) => {
        try {
            const { BACKUP_CONFIG, DB_CONFIG } = require('../../../utils/databaseBackup');
            
            const config = {
                backupDir: BACKUP_CONFIG.backupDir,
                retentionDays: BACKUP_CONFIG.retentionDays,
                filePrefix: BACKUP_CONFIG.filePrefix,
                compress: BACKUP_CONFIG.compress,
                database: DB_CONFIG.database,
                host: DB_CONFIG.host,
                port: DB_CONFIG.port
            };
            
            return apiResponse.successResponseWithData(res, "获取备份配置成功", config);
        } catch (error) {
            console.error('获取备份配置失败:', error);
            return apiResponse.ErrorResponse(res, "获取备份配置失败");
        }
    }
];
