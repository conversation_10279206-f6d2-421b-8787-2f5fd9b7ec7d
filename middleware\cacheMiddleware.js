const NodeCache = require('node-cache');

// 创建缓存实例
const cache = new NodeCache({
  stdTTL: 600, // 默认缓存时间10分钟
  checkperiod: 120 // 检查过期缓存的时间间隔
});

/**
 * 缓存中间件
 * @param {number} ttl - 缓存时间（秒）
 */
const createCache = (ttl = 600) => {
  return (req, res, next) => {
    const key = req.originalUrl || req.url;
    
    // 检查缓存
    const cachedResponse = cache.get(key);
    if (cachedResponse) {
      return res.json(cachedResponse);
    }

    // 保存原始的res.json方法
    const originalJson = res.json;
    
    // 重写res.json方法
    res.json = function(data) {
      // 缓存响应
      cache.set(key, data, ttl);
      return originalJson.call(this, data);
    };

    next();
  };
};

module.exports = {
  cache,
  createCache
}; 