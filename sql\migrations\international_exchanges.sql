-- 国际交流表
CREATE TABLE IF NOT EXISTS `international_exchanges` (
  `id` CHAR(36) NOT NULL COMMENT 'ID',
  `name` VARCHAR(255) NOT NULL COMMENT '交流名称',
  `type` VARCHAR(50) NOT NULL COMMENT '交流类型',
  `country` VARCHAR(255) NOT NULL COMMENT '交流国家/地区',
  `institution` VARCHAR(255) DEFAULT NULL COMMENT '交流机构',
  `startDate` DATE NOT NULL COMMENT '开始时间',
  `endDate` DATE NOT NULL COMMENT '结束时间',
  `userIdList` TEXT DEFAULT NULL COMMENT '参与用户ID列表',
  `usernameList` TEXT DEFAULT NULL COMMENT '参与用户列表',
  `content` TEXT DEFAULT NULL COMMENT '交流内容',
  `result` TEXT DEFAULT NULL COMMENT '交流成果',
  `score` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态',
  `createdAt` DATETIME NOT NULL COMMENT '创建时间',
  `updatedAt` DATETIME NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_exchange_type` (`type`),
  INDEX `idx_exchange_country` (`country`),
  INDEX `idx_exchange_institution` (`institution`),
  INDEX `idx_exchange_dates` (`startDate`, `endDate`),
  INDEX `idx_exchange_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国际交流表';

-- 如果需要修改已有表添加字段
ALTER TABLE `international_exchanges` 
ADD COLUMN IF NOT EXISTS `institution` VARCHAR(255) DEFAULT NULL COMMENT '交流机构' AFTER `country`,
ADD INDEX IF NOT EXISTS `idx_exchange_institution` (`institution`),
ADD INDEX IF NOT EXISTS `idx_exchange_dates` (`startDate`, `endDate`); 