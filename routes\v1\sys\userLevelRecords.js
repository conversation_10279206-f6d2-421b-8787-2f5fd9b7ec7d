const express = require('express');
const userLevelRecordsController = require('../../../controllers/v1/sys/userLevelRecordsController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

const router = express.Router();

// 创建用户职称记录权限中间件函数
const userLevelRecordsPermission = (action) => createModulePermission('userLevelRecords', action);

/**
 * 获取用户职称记录列表
 * @route GET /v1/sys/user-level-records
 * @group 用户职称记录管理 - 用户职称记录相关接口
 * @param {string} userId.query - 用户ID
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @returns {object} 200 - 成功返回职称记录列表
 * @security JWT
 */
router.get('/', 
  authMiddleware, 
  userLevelRecordsPermission('list'), 
  userLevelRecordsController.getUserLevelRecords
);

/**
 * 创建用户职称记录
 * @route POST /v1/sys/user-level-records
 * @group 用户职称记录管理 - 用户职称记录相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} levelId.body.required - 职称级别ID
 * @param {string} obtainedAt.body.required - 获得职称时间
 * @param {string} remarks.body - 备注
 * @returns {object} 201 - 成功创建职称记录
 * @security JWT
 */
router.post('/', 
  authMiddleware, 
  userLevelRecordsPermission('create'), 
  userLevelRecordsController.createUserLevelRecord
);

/**
 * 更新用户职称记录
 * @route PUT /v1/sys/user-level-records/:id
 * @group 用户职称记录管理 - 用户职称记录相关接口
 * @param {string} id.path.required - 记录ID
 * @param {string} levelId.body - 职称级别ID
 * @param {string} obtainedAt.body - 获得职称时间
 * @param {string} remarks.body - 备注
 * @returns {object} 200 - 成功更新职称记录
 * @security JWT
 */
router.put('/:id', 
  authMiddleware, 
  userLevelRecordsPermission('update'), 
  userLevelRecordsController.updateUserLevelRecord
);

/**
 * 删除用户职称记录
 * @route DELETE /v1/sys/user-level-records/:id
 * @group 用户职称记录管理 - 用户职称记录相关接口
 * @param {string} id.path.required - 记录ID
 * @returns {object} 200 - 成功删除职称记录
 * @security JWT
 */
router.delete('/:id', 
  authMiddleware, 
  userLevelRecordsPermission('delete'), 
  userLevelRecordsController.deleteUserLevelRecord
);

module.exports = router;
