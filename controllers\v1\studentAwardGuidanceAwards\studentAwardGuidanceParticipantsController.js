const { Op } = require('sequelize');
const { getTimeIntervalByName } = require('../../../utils/others');
const studentAwardGuidanceAwardModel = require('../../../models/v1/mapping/studentAwardGuidanceAwardsModel');
const studentAwardGuidanceAwardLevelModel = require('../../../models/v1/mapping/studentAwardGuidanceAwardLevelsModel');
const studentAwardGuidanceParticipantModel = require('../../../models/v1/mapping/studentAwardGuidanceParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');

/**
 * 获取用户参与获奖的统计信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserStatistics = async (req, res) => {
  try {
    const { userId, range = 'all', timeRange, page = 1, pageSize = 10 } = req.body;

    // 验证必要参数
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;

    // 获取统计时间区间
    let timeInterval;
    if (timeRange && timeRange.startTime && timeRange.endTime) {
      timeInterval = timeRange;
    } else {
      timeInterval = await getTimeIntervalByName("student_award_guidance");
    }

    // 查询用户参与的获奖记录
    const participations = await studentAwardGuidanceParticipantModel.findAll({
      where: { userId },
      include: [
        {
          model: studentAwardGuidanceAwardModel,
          as: 'award',
          include: [
            {
              model: studentAwardGuidanceAwardLevelModel,
              as: 'level',
              attributes: ['id', 'levelName', 'score']
            }
          ]
        }
      ]
    });

    // 获取范围内的获奖记录
    const filteredParticipations = participations.filter(p => {
      const isInTimeRange = timeInterval ? 
        isDateInTimeRange(p.award.awardDate, timeInterval.startTime, timeInterval.endTime) : 
        false;

      return (range === 'in' && isInTimeRange) || 
             (range === 'out' && !isInTimeRange) || 
             range === 'all';
    });

    // 统计分数
    let totalScore = 0;
    let awardCount = 0;
    let firstAuthorCount = 0;

    // 分页数据
    const paginatedData = filteredParticipations.slice(offset, offset + limit);

    // 处理统计数据
    paginatedData.forEach(p => {
      if (p.award && p.award.level) {
        const allocatedScore = p.award.level.score * p.allocationRatio;
        p.allocatedScore = allocatedScore;
        
        totalScore += allocatedScore;
        awardCount += 1;
        
        if (p.isLeader === 1) {
          firstAuthorCount += 1;
        }
      }
    });

    // 格式化返回数据
    const statistics = {
      totalScore,
      awardCount,
      firstAuthorCount
    };

    // 转换为前端所需格式
    const participationList = paginatedData.map(p => {
      return {
        id: p.id,
        awardId: p.awardId,
        awardName: p.award ? p.award.awardName : '',
        awardDate: p.award ? p.award.awardDate : '',
        isLeader: p.isLeader,
        allocationRatio: p.allocationRatio,
        levelName: p.award && p.award.level ? p.award.level.levelName : '',
        levelScore: p.award && p.award.level ? p.award.level.score : 0,
        allocatedScore: p.allocatedScore || 0,
        ifReviewer: p.award ? p.award.ifReviewer : 0
      };
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        statistics,
        list: participationList,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: filteredParticipations.length,
          totalPages: Math.ceil(filteredParticipations.length / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户参与获奖统计信息失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户参与获奖统计信息失败',
      error: error.message
    });
  }
};

/**
 * 获取所有用户参与获奖的统计信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllUsersStatistics = async (req, res) => {
  try {
    const { 
      range = 'all', 
      timeRange, 
      page = 1, 
      pageSize = 10,
      sortField = 'totalScore',
      sortOrder = 'desc'
    } = req.body;

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);

    // 获取统计时间区间
    let timeInterval;
    if (timeRange && timeRange.startTime && timeRange.endTime) {
      timeInterval = timeRange;
    } else {
      timeInterval = await getTimeIntervalByName("student_award_guidance");
    }

    // 构建时间范围查询条件
    let timeCondition = {};
    if (timeInterval && range !== 'all') {
      if (range === 'in') {
        timeCondition = {
          awardDate: {
            [Op.between]: [timeInterval.startTime, timeInterval.endTime]
          }
        };
      } else if (range === 'out') {
        timeCondition = {
          [Op.or]: [
            { awardDate: { [Op.lt]: timeInterval.startTime } },
            { awardDate: { [Op.gt]: timeInterval.endTime } }
          ]
        };
      }
    }

    // 聚合查询：按用户分组统计
    const userStatistics = await studentAwardGuidanceParticipantModel.findAll({
      attributes: [
        'userId',
        [sequelize.fn('COUNT', sequelize.col('studentAwardGuidanceParticipantModel.id')), 'awardCount'],
        [sequelize.fn('SUM', sequelize.literal('IF(isLeader = 1, 1, 0)')), 'firstAuthorCount'],
        [
          sequelize.fn(
            'SUM',
            sequelize.literal('`award->level`.`score` * `studentAwardGuidanceParticipantModel`.`allocationRatio`')
          ),
          'totalScore'
        ]
      ],
      include: [
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber', 'department']
        },
        {
          model: studentAwardGuidanceAwardModel,
          as: 'award',
          where: {
            ifReviewer: 1, // 只统计已审核的记录
            ...timeCondition
          },
          include: [
            {
              model: studentAwardGuidanceAwardLevelModel,
              as: 'level',
              attributes: ['id', 'levelName', 'score']
            }
          ]
        }
      ],
      group: ['userId'],
      having: sequelize.literal('awardCount > 0'), // 只显示有参与记录的用户
      raw: true,
      nest: true,
    });

    // 排序处理
    const sortedData = [...userStatistics].sort((a, b) => {
      const fieldA = getNestedValue(a, sortField);
      const fieldB = getNestedValue(b, sortField);
      
      if (sortOrder.toLowerCase() === 'asc') {
        return fieldA - fieldB;
      } else {
        return fieldB - fieldA;
      }
    });

    // 分页处理
    const total = sortedData.length;
    const paginatedData = sortedData.slice((pageNum - 1) * pageSizeNum, pageNum * pageSizeNum);

    // 格式化返回数据
    const formattedData = paginatedData.map(item => ({
      userId: item.userId,
      username: item.user?.username || '',
      nickname: item.user?.nickname || '',
      studentNumber: item.user?.studentNumber || '',
      department: item.user?.department || '',
      awardCount: parseInt(item.awardCount) || 0,
      firstAuthorCount: parseInt(item.firstAuthorCount) || 0,
      totalScore: parseFloat(item.totalScore) || 0
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: formattedData,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages: Math.ceil(total / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取所有用户参与获奖统计信息失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有用户参与获奖统计信息失败',
      error: error.message
    });
  }
};

/**
 * 判断日期是否在时间范围内的辅助函数
 * @param {string} dateStr - 日期字符串
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isDateInTimeRange(dateStr, startDate, endDate) {
  if (!dateStr || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const dateObj = new Date(dateStr);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 日期必须在开始日期和结束日期之间（含边界）
  return dateObj >= startDateObj && dateObj <= endDateObj;
}

/**
 * 获取嵌套对象的值
 * @param {Object} obj - 对象
 * @param {string} path - 路径
 * @returns {*} - 值
 */
function getNestedValue(obj, path) {
  // 处理特殊字段
  if (path === 'totalScore') {
    return parseFloat(obj.totalScore) || 0;
  }
  if (path === 'awardCount') {
    return parseInt(obj.awardCount) || 0;
  }
  if (path === 'firstAuthorCount') {
    return parseInt(obj.firstAuthorCount) || 0;
  }
  
  // 处理用户信息字段
  if (path.startsWith('user.')) {
    const userField = path.split('.')[1];
    return obj.user ? obj.user[userField] : '';
  }
  
  return obj[path];
} 