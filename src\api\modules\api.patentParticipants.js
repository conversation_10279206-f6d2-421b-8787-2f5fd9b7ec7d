import request from '../server'

/**
 * 获取用户专利总得分
 * @param {Object} params - 请求参数
 * @param {string} params.userId - 用户ID
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {Object} [params.timeRange] - 可选，自定义时间范围
 * @param {string} [params.timeRange.startDate] - 开始日期，格式 YYYY-MM-DD
 * @param {string} [params.timeRange.endDate] - 结束日期，格式 YYYY-MM-DD
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @returns {Promise} - 请求Promise对象
 */
export function getUserPatentTotalScore(params) {
  return request.post('/sys/patent-participant/statistics/user-total-score', params)
}

/**
 * 获取所有用户专利总得分统计
 * @param {Object} params - 请求参数
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {Object} [params.timeRange] - 可选，自定义时间范围
 * @param {string} [params.timeRange.startDate] - 开始日期，格式 YYYY-MM-DD
 * @param {string} [params.timeRange.endDate] - 结束日期，格式 YYYY-MM-DD
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @param {string} [params.sortField='totalScore'] - 排序字段
 * @param {string} [params.sortOrder='desc'] - 排序方向，'asc' 或 'desc'
 * @param {string} [params.nickname] - 可选，用户昵称，支持模糊搜索
 * @returns {Promise} - 请求Promise对象
 */
export function getAllUsersPatentTotalScore(params) {
  return request.post('/sys/patent-participant/statistics/all-users-total-score', params)
} 