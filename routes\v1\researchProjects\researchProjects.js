const express = require('express');
const router = express.Router();
const researchProjectController = require('../../../controllers/v1/researchProjects/researchProjectsController');
const { Op } = require('sequelize');
const multer = require('multer');
const path = require('path');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/research_projects/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'research-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

// 创建科研项目权限中间件函数
const researchPermission = (action) => createModulePermission('researchProjects', action);

/**
 * 获取科研项目列表（统一接口）
 * @route POST /v1/sys/research/projects
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} name - 项目名称（模糊搜索）
 * @param {string} level - 项目级别ID
 * @param {string} type - 项目类型
 * @param {string} leader - 项目负责人（模糊搜索名称或直接使用用户ID）
 * @param {string} submitterId - 提交者ID（直接使用用户ID，优先于leader参数）
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @param {string} approvalStartDate - 获批开始日期
 * @param {string} approvalEndDate - 获批结束日期
 * @param {string} userId - 用户ID（可选，如果提供则只返回该用户参与的项目）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)，默认'all'
 * @param {string} reviewStatus - 审核状态筛选: 'all'(全部), 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)，默认'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, page: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects', 
  authMiddleware, 
  researchPermission('list'), 
  researchProjectController.getProjects
);


/**
 * 导入科研项目数据
 * @route POST /v1/sys/research/research-projects/import
 * @group 科研项目管理 - 科研项目相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-projects/import', 
  authMiddleware,
  researchPermission('import'),
  upload.single('file'), 
  researchProjectController.importProjects
);

/**
 * 导出科研项目数据
 * @route GET /v1/sys/research/research-projects/export
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} name.query - 项目名称（模糊搜索）
 * @param {string} level.query - 项目级别
 * @param {string} type.query - 项目类型
 * @param {string} leader.query - 项目负责人（模糊搜索）
 * @param {string} startDate.query - 开始日期
 * @param {string} endDate.query - 结束日期
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/research-projects/export', 
  authMiddleware,
  researchPermission('export'),
  researchProjectController.exportProjects
);

/**
 * 创建科研项目（前端兼容接口）
 * @route POST /v1/sys/research/research-project/create
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} name.body.required - 项目名称
 * @param {string} level.body.required - 项目级别
 * @param {string} type.body.required - 项目类型
 * @param {string} startDate.body.required - 开始日期
 * @param {string} endDate.body.required - 结束日期
 * @param {string} leader.body.required - 项目负责人
 * @param {Array} members.body - 项目成员
 * @param {string} description.body - 项目描述
 * @param {number} score.body - 项目分数
 * @param {number} status.body - 项目状态，默认1（正常）
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-project/create', 
  authMiddleware,
  researchPermission('create'),
  upload.array('files', 5), 
  researchProjectController.createProject
);

/**
 * 更新科研项目（前端兼容接口）
 * @route POST /v1/sys/research/research-project/update/:id
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} id.path.required - 项目ID
 * @param {string} name.body - 项目名称
 * @param {string} level.body - 项目级别
 * @param {string} type.body - 项目类型
 * @param {string} startDate.body - 开始日期
 * @param {string} endDate.body - 结束日期
 * @param {string} leader.body - 项目负责人
 * @param {Array} members.body - 项目成员
 * @param {string} description.body - 项目描述
 * @param {number} score.body - 项目分数
 * @param {number} status.body - 项目状态
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-project/update/:id', 
  authMiddleware,
  researchPermission('update'),
  upload.array('files', 5), 
  researchProjectController.updateProject
);

/**
 * 审核科研项目
 * @route POST /v1/sys/research/research-project/review
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} id.body.required - 项目ID
 * @param {number} reviewStatus.body.required - 审核状态(1:通过 2:拒绝)
 * @param {string} reviewComment.body - 审核意见
 * @param {string} reviewer.body - 审核人ID
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-project/review', 
  authMiddleware,
  researchPermission('review'),
  researchProjectController.reviewProject
);

/**
 * 重新提交科研项目审核
 * @route POST /v1/sys/research/research-project/resubmit
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-project/reapply', 
  authMiddleware,
  researchPermission('reapply'),
  researchProjectController.reapplyProject
);

// 保留旧的路由以兼容
router.post('/research-project/update', 
  authMiddleware,
  researchPermission('update'),
  async (req, res) => {
    const { id, ...updateData } = req.body;
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    req.params = { id };
    req.body = updateData;
    await researchProjectController.updateProject(req, res);
  }
);

/**
 * 删除科研项目（前端兼容接口）
 * @route POST /v1/sys/research/research-project/delete
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-project/delete', 
  authMiddleware,
  researchPermission('delete'),
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await researchProjectController.deleteProject(req, res);
  }
);

/**
 * 获取项目详情（前端兼容接口）
 * @route POST /v1/sys/research/research-project/detail
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-project/detail', 
  authMiddleware,
  researchPermission('detail'),
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await researchProjectController.getProjectDetail(req, res);
  }
);

/**
 * 获取项目级别分布数据
 * @route POST /v1/sys/research/research-statistics/level-distribution
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的项目，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "级别名称", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-statistics/level-distribution', 
  authMiddleware,
  researchPermission('levelDistribution'),
  researchProjectController.getLevelDistribution
);

/**
 * 获取项目类型分布数据
 * @route POST /v1/sys/research/research-statistics/type-distribution
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的项目，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "类型名称", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-statistics/type-distribution', 
  authMiddleware,
  researchPermission('typeDistribution'),
  researchProjectController.getTypeDistribution
);

/**
 * 获取项目时间分布数据
 * @route POST /v1/sys/research/research-statistics/time-distribution
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的项目，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {months: ["YYYY-MM",...], data: [数量,...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-statistics/time-distribution', 
  authMiddleware,
  researchPermission('timeDistribution'),
  researchProjectController.getTimeDistribution
);

/**
 * 获取项目得分分布数据
 * @route POST /v1/sys/research/research-statistics/score-distribution
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的项目，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "分数范围", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-statistics/score-distribution', 
  authMiddleware,
  researchPermission('scoreDistribution'),
  researchProjectController.getScoreDistribution
);

/**
 * 获取项目负责人得分排名数据
 * @route POST /v1/sys/research/research-statistics/leader-ranking
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {number} limit.body - 返回数量限制，默认10
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的项目，不提供则根据当前用户权限返回对应数据
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id: "负责人ID", name: "负责人名称", score: 总分},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-statistics/leader-ranking', 
  authMiddleware,
  researchPermission('leaderRanking'),
  researchProjectController.getLeaderRanking
);

/**
 * 获取用户科研项目总得分
 * @route POST /v1/sys/research/research-statistics/user-total-score
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {object} timeRange.body - 可选的自定义时间范围，包含startDate和endDate字段
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {userId: "用户ID", totalScore: 总分, list: [{id, name, level, type, startDate, endDate, totalScore, userScore, role, allocationProportion}], pagination: {page: 1, pageSize: 10, total: 100, totalPages: 10}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-statistics/user-total-score', 
  authMiddleware,
  researchPermission('userTotalScore'),
  researchProjectController.getUserTotalScore
);

/**
 * 获取所有用户科研项目总得分统计
 * @route POST /v1/sys/research/research-statistics/teacher-ranking
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {object} timeRange.body - 可选的自定义时间范围，包含startDate和endDate字段
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @param {string} sortField.body - 排序字段，可选值：'totalScore'(总分),'userScore'(用户得分)，默认'totalScore'
 * @param {string} sortOrder.body - 排序方向，可选值：'asc'(升序),'desc'(降序)，默认'desc'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{userId: "用户ID", totalScore: 总分},...]}
 * @returns {Error} default - Unexpected error  
 * @security JWT
 */
router.post('/research-statistics/teacher-ranking', 
  authMiddleware,
  researchPermission('allUsersTotalScore'),
  (req, res, next) => {
    // 中间件处理：如果用户是TEACHER-LV1，限制page=1和pageSize=10
    const userRole = req.verifiedUser?.role?.roleAuth;
    if (userRole === 'TEACHER-LV1') {
      req.body.page = 1;
      req.body.pageSize = 10;
    }
    next();
  },
  researchProjectController.getAllUsersTotalScore
);

/**
 * 获取用户科研项目详情
 * @route POST /v1/sys/research/research-statistics/user-project-details
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值： 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 'all'(全部)，默认'all'
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{id, name, level, type, approvalDate, startDate, endDate, totalScore, userScore, role, userAllocationRatio, isLeader}], totalScore: 总分, user: {id, name, employeeNumber}, pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-statistics/user-project-details', 
  authMiddleware,
  researchPermission('userProjectDetails'),
  researchProjectController.getUserProjectDetails
);

/**
 * 获取科研项目总分统计（按类型和总体）
 * @route POST /v1/sys/research/research-statistics/projects-total-score
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 'all'(全部)，默认'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {typeStats: [{type, typeName, count, totalScore},...], totalStats: {totalProjects, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/research-statistics/projects-total-score', 
  authMiddleware,
  researchPermission('projectsTotalScore'),
  researchProjectController.getProjectsTotalScore
);


/**
 * 获取用户科研项目详情列表及得分
 * @route POST /v1/sys/research-projects/user/details
 * @group 科研项目管理 - 科研项目相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 'all'(全部)，默认'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{id, name, level, type, approvalDate, startDate, endDate, totalScore, userScore, role, userAllocationRatio, isLeader}], totalScore: 总分, user: {id, name, employeeNumber}, pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/details',
  authMiddleware,
  researchPermission('userProjectsDetail'),
  researchProjectController.getUserProjectsDetail
);

/**
 * 获取审核状态概览
 * @route POST /v1/sys/research-projects/statistics/review-status-overview
 * @group 科研项目统计 - 科研项目统计相关接口
 * @param {string} range.body - 查询范围：'in'|'out'|'all'，默认'all'
 * @param {string} userId.body - 用户ID，可选
 * @returns {object} 200 - 审核状态统计数据
 * @security JWT
 */
router.post('/statistics/review-status-overview',
  authMiddleware,
  researchPermission('reviewStatusOverview'),
  researchProjectController.getReviewStatusOverview
);

module.exports = router;