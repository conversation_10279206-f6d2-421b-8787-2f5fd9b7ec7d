const express = require('express');
const userLevelsController = require('../../../controllers/v1/sys/userLevelsController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

const router = express.Router();

// 创建用户级别权限中间件函数
const userLevelsPermission = (action) => createModulePermission('userLevels', action);

/**
 * 获取用户级别列表
 * @route GET /v1/sys/user-levels
 * @group 用户级别管理 - 用户级别相关接口
 * @param {string} levelName.query - 级别名称（模糊搜索）
 * @param {number} status.query - 状态筛选
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @returns {object} 200 - 成功返回用户级别列表
 * @security JWT
 */
router.get('/', 
  authMiddleware, 
  userLevelsPermission('list'), 
  userLevelsController.getUserLevels
);

/**
 * 获取所有用户级别（不分页）
 * @route GET /v1/sys/user-levels/all
 * @group 用户级别管理 - 用户级别相关接口
 * @returns {object} 200 - 成功返回所有用户级别
 * @security JWT
 */
router.get('/all', 
  authMiddleware, 
  userLevelsPermission('all'), 
  userLevelsController.getAllUserLevels
);

/**
 * 获取用户级别统计信息
 * @route GET /v1/sys/user-levels/statistics
 * @group 用户级别管理 - 用户级别相关接口
 * @returns {object} 200 - 成功返回统计信息
 * @security JWT
 */
router.get('/statistics', 
  authMiddleware, 
  userLevelsPermission('list'), 
  userLevelsController.getUserLevelStatistics
);

/**
 * 获取用户级别详情
 * @route GET /v1/sys/user-levels/:id
 * @group 用户级别管理 - 用户级别相关接口
 * @param {string} id.path - 用户级别ID
 * @returns {object} 200 - 成功返回用户级别详情
 * @security JWT
 */
router.get('/:id', 
  authMiddleware, 
  userLevelsPermission('detail'), 
  userLevelsController.getUserLevelDetail
);

/**
 * 创建用户级别
 * @route POST /v1/sys/user-levels
 * @group 用户级别管理 - 用户级别相关接口
 * @param {string} levelName.body - 级别名称
 * @param {string} description.body - 级别描述
 * @param {number} sort.body - 排序
 * @returns {object} 200 - 成功创建用户级别
 * @security JWT
 */
router.post('/', 
  authMiddleware, 
  userLevelsPermission('create'), 
  userLevelsController.createUserLevel
);

/**
 * 更新用户级别
 * @route PUT /v1/sys/user-levels/:id
 * @group 用户级别管理 - 用户级别相关接口
 * @param {string} id.path - 用户级别ID
 * @param {string} levelName.body - 级别名称
 * @param {string} description.body - 级别描述
 * @param {number} sort.body - 排序
 * @param {number} status.body - 状态
 * @returns {object} 200 - 成功更新用户级别
 * @security JWT
 */
router.put('/:id', 
  authMiddleware, 
  userLevelsPermission('update'), 
  userLevelsController.updateUserLevel
);

/**
 * 删除用户级别
 * @route DELETE /v1/sys/user-levels/:id
 * @group 用户级别管理 - 用户级别相关接口
 * @param {string} id.path - 用户级别ID
 * @returns {object} 200 - 成功删除用户级别
 * @security JWT
 */
router.delete('/:id', 
  authMiddleware, 
  userLevelsPermission('delete'), 
  userLevelsController.deleteUserLevel
);

module.exports = router;
