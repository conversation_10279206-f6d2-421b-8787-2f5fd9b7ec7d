const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const sequelize = require('sequelize');
const { getTimeIntervalByName } = require('../../../utils/others');
const projectModel = require('../../../models/v1/mapping/teachingReformProjectsModel');
const projectLevelModel = require('../../../models/v1/mapping/teachingReformProjectLevelsModel');
const participantModel = require('../../../models/v1/mapping/teachingReformParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');

/**
 * 获取用户在教学改革项目中的总得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserTotalScore = async (req, res) => {
  try {
    const { userId, timeRangeType = 'all' } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }
    
    // 获取时间区间
    let timeInterval = null;
    if (timeRangeType !== 'all') {
      timeInterval = await getTimeIntervalByName("teachingReformProjects");
      if (!timeInterval) {
        return res.status(404).json({
          code: 404,
          message: '未找到时间区间配置',
          data: null
        });
      }
    }
    
    // 查询用户参与的所有项目
    const userParticipations = await participantModel.findAll({
      where: { userId },
      include: [
        {
          model: projectModel,
          as: 'project',
          attributes: ['id', 'projectName', 'approvalDate', 'reviewerId', 'ifReviewer'],
          include: [
            {
              model: projectLevelModel,
              as: 'level',
              attributes: ['id', 'levelName', 'score']
            }
          ]
        }
      ]
    });
    
    // 如果用户没有参与任何项目，返回0分
    if (userParticipations.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          totalScore: 0,
          projectCount: 0,
          leadCount: 0,
          participantCount: 0
        }
      });
    }
    
    let totalScore = 0;
    let projectCount = 0;
    let leadCount = 0;
    let participantCount = 0;
    
    // 计算得分并统计数量
    userParticipations.forEach(participation => {
      const project = participation.project;
      
      // 跳过未审核通过的项目
      if (!project.ifReviewer) {
        return;
      }
      
      const projectData = project.toJSON();
      
      // 检查项目是否在时间范围内
      if (timeInterval && projectData.approvalDate) {
        const projectDate = new Date(projectData.approvalDate);
        const startDate = new Date(timeInterval.startTime);
        const endDate = new Date(timeInterval.endTime);
        
        if (projectDate < startDate || projectDate > endDate) {
          return; // 跳过不在时间范围内的项目
        }
      }
      
      // 统计项目数量
      projectCount++;
      
      // 是否为项目负责人
      if (participation.isLeader) {
        leadCount++;
      } else {
        participantCount++;
      }
      
      // 计算得分
      if (projectData.level && projectData.level.score) {
        const projectScore = projectData.level.score;
        // 根据分配比例计算个人得分
        const personalScore = projectScore * (participation.allocationRatio / 100);
        totalScore += personalScore;
      }
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalScore,
        projectCount,
        leadCount,
        participantCount
      }
    });
  } catch (error) {
    console.error('获取用户总得分失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户总得分失败',
      error: error.message
    });
  }
};

/**
 * 获取所有用户在教学改革项目中的总得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllUsersTotalScore = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, timeRangeType = 'all', sortField = 'totalScore', sortOrder = 'desc' } = req.body;
    
    // 获取时间区间
    let timeInterval = null;
    if (timeRangeType !== 'all') {
      timeInterval = await getTimeIntervalByName("teachingReformProjects");
      if (!timeInterval) {
        return res.status(404).json({
          code: 404,
          message: '未找到时间区间配置',
          data: null
        });
      }
    }
    
    // 获取所有用户
    const users = await userModel.findAll({
      attributes: ['id', 'nickname', 'username', 'studentNumber', 'email'],
      order: [['nickname', 'ASC']]
    });
    
    // 查询所有参与记录
    const allParticipations = await participantModel.findAll({
      include: [
        {
          model: projectModel,
          as: 'project',
          attributes: ['id', 'projectName', 'approvalDate', 'reviewerId', 'ifReviewer'],
          include: [
            {
              model: projectLevelModel,
              as: 'level',
              attributes: ['id', 'levelName', 'score']
            }
          ]
        }
      ]
    });
    
    // 计算每个用户的得分和项目统计
    const usersScores = users.map(user => {
      const userData = user.toJSON();
      let totalScore = 0;
      let projectCount = 0;
      let leadCount = 0;
      let participantCount = 0;
      
      // 找出该用户参与的所有项目
      const userParticipations = allParticipations.filter(p => p.userId === userData.id);
      
      if (userParticipations.length > 0) {
        userParticipations.forEach(participation => {
          const project = participation.project;
          
          // 跳过未审核通过的项目
          if (!project.ifReviewer) {
            return;
          }
          
          const projectData = project.toJSON();
          
          // 检查项目是否在时间范围内
          if (timeInterval && projectData.approvalDate) {
            const projectDate = new Date(projectData.approvalDate);
            const startDate = new Date(timeInterval.startTime);
            const endDate = new Date(timeInterval.endTime);
            
            if (projectDate < startDate || projectDate > endDate) {
              return; // 跳过不在时间范围内的项目
            }
          }
          
          // 统计项目数量
          projectCount++;
          
          // 是否为项目负责人
          if (participation.isLeader) {
            leadCount++;
          } else {
            participantCount++;
          }
          
          // 计算得分
          if (projectData.level && projectData.level.score) {
            const projectScore = projectData.level.score;
            // 根据分配比例计算个人得分
            const personalScore = projectScore * (participation.allocationRatio / 100);
            totalScore += personalScore;
          }
        });
      }
      
      return {
        ...userData,
        totalScore,
        projectCount,
        leadCount,
        participantCount
      };
    });
    
    // 按得分排序
    const sortedUsers = usersScores.sort((a, b) => {
      const fieldA = a[sortField];
      const fieldB = b[sortField];
      
      if (sortOrder === 'asc') {
        return fieldA - fieldB;
      } else {
        return fieldB - fieldA;
      }
    });
    
    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = page * pageSize;
    const paginatedUsers = sortedUsers.slice(startIndex, endIndex);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: sortedUsers.length,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        list: paginatedUsers
      }
    });
  } catch (error) {
    console.error('获取所有用户总得分失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有用户总得分失败',
      error: error.message
    });
  }
}; 