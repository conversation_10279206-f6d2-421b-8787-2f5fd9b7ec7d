const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const teachingResearchAwardsModel = sequelize.define(
  'teaching_research_awards',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '奖励ID'
    },
  awardName: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '获奖名称'
  },
  awardTime: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '获奖时间'
  },
    awardLevelId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '奖励级别ID（外键）'
    },
    firstResponsibleId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '第一负责人ID（外键，关联user.id）'
    },
  department: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '系/教研室'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  },
  attachmentUrl: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '附件URL'
  },
  reviewComment: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核意见（拒绝或同意）'
  },
    reviewerId: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: '审核人ID（外键，关联user.id）'
    },
    ifReviewer: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      comment: '审核状态（默认为空，0拒绝未审核 1，同意审核）'
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: 1,
      comment: '状态（1-有效，0-删除）'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '更新时间'
    }
  },
  {
    sequelize,
    modelName: 'teaching_research_awards',
    tableName: 'teaching_research_awards',
    timestamps: true
  }
);

// 在文件加载后添加关联，避免循环依赖问题
const setupAssociations = () => {
  const userModel = require('./userModel');
  const teachingResearchAwardLevelsModel = require('./teachingResearchAwardLevelsModel');

  // 与用户表关联
  teachingResearchAwardsModel.belongsTo(userModel, {
    foreignKey: 'firstResponsibleId',
    as: 'firstResponsible'
  });

  // 与奖励级别表关联
  teachingResearchAwardsModel.belongsTo(teachingResearchAwardLevelsModel, {
    foreignKey: 'awardLevelId',
    as: 'awardLevel'
  });

  // 与审核人的关联关系
  teachingResearchAwardsModel.belongsTo(userModel, {
    foreignKey: 'reviewerId',
    as: 'reviewer'
  });
};

// 确保关联设置被调用
setTimeout(setupAssociations, 0);

module.exports = teachingResearchAwardsModel;
