const express = require('express');
const notificationSettingsController = require('../../../controllers/v1/sys/notificationSettingsController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

const router = express.Router();

// 创建通知设置权限中间件函数
const notificationSettingsPermission = (action) => createModulePermission('notificationSettings', action);

/**
 * 获取用户通知设置
 * @route GET /v1/sys/notifications/settings/:userId
 * @group 通知设置管理 - 通知设置相关接口
 * @param {string} userId.path - 用户ID
 * @returns {object} 200 - 成功返回用户通知设置
 * @security JWT
 */
router.get('/:userId',
    authMiddleware,
    notificationSettingsPermission('view'),
    notificationSettingsController.getUserNotificationSettings
);

/**
 * 更新用户通知设置
 * @route PUT /v1/sys/notifications/settings/:userId
 * @group 通知设置管理 - 通知设置相关接口
 * @param {string} userId.path - 用户ID
 * @param {object} settings.body - 通知设置数据
 * @returns {object} 200 - 成功更新用户通知设置
 * @security JWT
 */
router.put('/:userId',
    authMiddleware,
    notificationSettingsPermission('update'),
    notificationSettingsController.updateUserNotificationSettings
);

/**
 * 重置用户通知设置为默认值
 * @route POST /v1/sys/notifications/settings/:userId/reset
 * @group 通知设置管理 - 通知设置相关接口
 * @param {string} userId.path - 用户ID
 * @returns {object} 200 - 成功重置用户通知设置
 * @security JWT
 */
router.post('/:userId/reset',
    authMiddleware,
    notificationSettingsPermission('reset'),
    notificationSettingsController.resetUserNotificationSettings
);

module.exports = router;
