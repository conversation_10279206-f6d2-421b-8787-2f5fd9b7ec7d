/*
 Navicat Premium Dump SQL

 Source Server         : aliyun
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41-0ubuntu0.20.04.1)
 Source Host           : ************:3306
 Source Schema         : jxpd

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41-0ubuntu0.20.04.1)
 File Encoding         : 65001

 Date: 27/04/2025 09:35:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for awards
-- ----------------------------
DROP TABLE IF EXISTS `awards`;
CREATE TABLE `awards`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖类型',
  `level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖级别',
  `awardDate` date NOT NULL COMMENT '获奖时间',
  `awardUnit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '获奖单位',
  `awardRank` int NULL DEFAULT NULL COMMENT '获奖排名',
  `teachers` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '获奖教师',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_award_type`(`type` ASC) USING BTREE,
  INDEX `idx_award_level`(`level` ASC) USING BTREE,
  INDEX `idx_award_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教学与科研获奖评分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of awards
-- ----------------------------
INSERT INTO `awards` VALUES ('859ccab4-ffaf-4f17-9929-8e2454934370', '666', '优秀教材奖', '市级', '2025-04-03', '暨南大学', 7, 'f35f39c3-95c5-4496-80fb-7877bf715aa7', 100.00, 1, '2025-04-12 01:00:25', '2025-04-12 01:00:25');
INSERT INTO `awards` VALUES ('f4bb1a46-66bb-49de-840a-7d8c04f3aebd', '666', '科研成果奖', '国家级', '2025-04-10', '暨南大学', 1, 'suhy', 59.00, 1, '2025-04-12 00:14:22', '2025-04-12 00:14:22');

-- ----------------------------
-- Table structure for awards_rules
-- ----------------------------
DROP TABLE IF EXISTS `awards_rules`;
CREATE TABLE `awards_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `awardLevel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '奖项级别（如国家级一等奖、省部级二等奖等）',
  `rank` int NOT NULL COMMENT '完成人排序（第几位）',
  `score` decimal(10, 2) NOT NULL COMMENT '核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '奖项的详细描述（如国家级一等奖、省部级二等奖等）',
  `unitCount` int NULL DEFAULT 1 COMMENT '合作单位数目（用于分数除法）',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_award_level_rank`(`awardLevel` ASC, `rank` ASC) USING BTREE COMMENT '确保奖项级别和排序的唯一性'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '教学与科研获奖核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of awards_rules
-- ----------------------------
INSERT INTO `awards_rules` VALUES ('7428211d-1912-11f0-b4f1-00163e035a15', '国家级一等奖，第一完成人', 1, 1000.00, '国家级一等奖，第一完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('74282e5b-1912-11f0-b4f1-00163e035a15', '国家级一等奖，第二完成人', 2, 600.00, '国家级一等奖，第二完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('742831c7-1912-11f0-b4f1-00163e035a15', '国家级一等奖，第三完成人', 3, 300.00, '国家级一等奖，第三完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('742832d4-1912-11f0-b4f1-00163e035a15', '国家级二等奖，第一完成人', 1, 400.00, '国家级二等奖，第一完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('742833ba-1912-11f0-b4f1-00163e035a15', '国家级二等奖，第二完成人', 2, 240.00, '国家级二等奖，第二完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('742834a7-1912-11f0-b4f1-00163e035a15', '国家级二等奖，第三完成人', 3, 160.00, '国家级二等奖，第三完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('742835af-1912-11f0-b4f1-00163e035a15', '国家级三等奖，第一完成人', 1, 200.00, '国家级三等奖，第一完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('7428369e-1912-11f0-b4f1-00163e035a15', '国家级三等奖，第二完成人', 2, 160.00, '国家级三等奖，第二完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('74283797-1912-11f0-b4f1-00163e035a15', '国家级三等奖，第三完成人', 3, 120.00, '国家级三等奖，第三完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('742838ae-1912-11f0-b4f1-00163e035a15', '省部级一等奖，第一完成人', 1, 200.00, '省部级一等奖，第一完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('742839a7-1912-11f0-b4f1-00163e035a15', '省部级一等奖，第二完成人', 2, 120.00, '省部级一等奖，第二完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('74283b1c-1912-11f0-b4f1-00163e035a15', '省部级二等奖，第一完成人', 1, 100.00, '省部级二等奖，第一完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `awards_rules` VALUES ('74283c09-1912-11f0-b4f1-00163e035a15', '省部级二等奖，第二完成人', 2, 60.00, '省部级二等奖，第二完成人', 1, '2025-04-14 17:25:49', '2025-04-15 09:49:24', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');

-- ----------------------------
-- Table structure for deductions
-- ----------------------------
DROP TABLE IF EXISTS `deductions`;
CREATE TABLE `deductions`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `username` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户姓名',
  `userId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
  `deductionType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '扣分类型',
  `deductionDate` date NOT NULL COMMENT '扣分时间',
  `deductionScore` decimal(10, 2) NOT NULL COMMENT '扣分分值',
  `deductionReason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '扣分原因',
  `handleResult` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '处理结果',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_deduction_type`(`deductionType` ASC) USING BTREE,
  INDEX `idx_deduction_date`(`deductionDate` ASC) USING BTREE,
  INDEX `idx_deduction_status`(`status` ASC) USING BTREE,
  INDEX `idx_deduction_userId`(`userId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '扣分管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of deductions
-- ----------------------------
INSERT INTO `deductions` VALUES ('1f116332-e123-498e-bcf6-55a35f5eb34b', '孙七', 'o5p6q7r8-s9t0-a1b2-c3d4-e5f6g7h8i9j0', '未就业', '2025-04-03', 4.00, '12', '12', 0, '2025-04-24 14:54:50', '2025-04-24 14:55:06');
INSERT INTO `deductions` VALUES ('32964480-f1ff-4e6e-95b4-3f298d1f583d', '孙七', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '未获得学位', '2025-04-02', 21.00, '123', '123', 1, '2025-04-24 16:11:38', '2025-04-24 16:11:38');
INSERT INTO `deductions` VALUES ('b7f48e35-beff-44a7-97ed-263ce7e2b6b4', '孙七', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '未就业', '2025-04-01', 100.00, '213', '123', 1, '2025-04-24 16:12:31', '2025-04-24 16:12:31');
INSERT INTO `deductions` VALUES ('d0c50f90-3a87-4ea0-8c8f-9e5f4b23a465', '孙七', 'b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '学术道德', '2023-08-15', 12.00, '所指导研究生学位论文抽检有问题', '暂停指导新入学研究生', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d1c50f81-3a87-4ea0-8c8f-9e5f4b23a456', '张三', 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '未获得学位', '2023-02-15', 5.00, '学生在最高培养年限内未获得硕士学位', '取消研究生招生资格1年', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d2c50f82-3a87-4ea0-8c8f-9e5f4b23a457', '张三', 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '未就业', '2023-05-20', 10.00, '研究生在毕业当年8月31日前未就业', '需参加就业指导培训', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d3c50f83-3a87-4ea0-8c8f-9e5f4b23a458', '李四', 'b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6', '师德师风', '2023-03-10', 15.00, '师生关系紧张，多次收到学生投诉', '暂停研究生招生资格，参加师德培训', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d4c50f84-3a87-4ea0-8c8f-9e5f4b23a459', '李四', 'b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6', '学术道德', '2023-06-05', 20.00, '指导研究生论文存在学术不端行为', '取消当年评优评先资格', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d5c50f85-3a87-4ea0-8c8f-9e5f4b23a460', '王五', 'c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7', '科研项目', '2023-01-25', 8.00, '博士生导师当年未主持在研国家级项目', '限期整改，提交改进计划', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d6c50f86-3a87-4ea0-8c8f-9e5f4b23a461', '王五', 'c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7', '未获得学位', '2023-04-12', 5.00, '学生在最高培养年限内未获得博士学位', '限制招生名额', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d7c50f87-3a87-4ea0-8c8f-9e5f4b23a462', '赵六', 'a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5', '未就业', '2023-07-18', 20.00, '研究生在毕业当年12月31日前未就业', '停招1年', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d8c50f88-3a87-4ea0-8c8f-9e5f4b23a463', '赵六', 'a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5', '科研项目', '2023-09-03', 7.00, '硕士生导师科研经费不足10万', '要求在下一年度内获取足够科研经费', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('d9c50f89-3a87-4ea0-8c8f-9e5f4b23a464', '孙七', 'b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '其他', '2023-02-28', 3.00, '拒绝承担研究生招生相关工作任务', '要求积极参与学院工作', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('e1c50f91-3a87-4ea0-8c8f-9e5f4b23a466', '周八', 'c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', '师德师风', '2023-05-30', 10.00, '对研究生态度粗暴，造成不良影响', '要求参加师德培训，并做出书面检讨', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('e2c50f92-3a87-4ea0-8c8f-9e5f4b23a467', '吴九', 'd4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', '未获得学位', '2023-03-22', 10.00, '连续两年所指导研究生未按期毕业', '暂停招生1年', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('e3c50f93-3a87-4ea0-8c8f-9e5f4b23a468', '郑十', 'h8i9j0k1-l2m3-n4o5-p6q7-r8s9t0a1b2c3', '学术道德', '2023-06-20', 25.00, '伪造研究数据，存在严重学术不端行为', '取消研究生招生资格', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('e4c50f94-3a87-4ea0-8c8f-9e5f4b23a469', '王一', 'i9j0k1l2-m3n4-o5p6-q7r8-s9t0a1b2c3d4', '未就业', '2023-01-10', 10.00, '研究生在毕业当年8月31日前未就业', '加强就业指导', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('e5c50f95-3a87-4ea0-8c8f-9e5f4b23a470', '王一', 'i9j0k1l2-m3n4-o5p6-q7r8-s9t0a1b2c3d4', '未获得学位', '2023-07-05', 5.00, '学生在最高培养年限内未获得硕士学位', '限制招生名额', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('e6c50f96-3a87-4ea0-8c8f-9e5f4b23a471', '李二', 'j0k1l2m3-n4o5-p6q7-r8s9-t0a1b2c3d4e5', '科研项目', '2023-04-28', 15.00, '博士生导师科研经费不足20万', '限期内补足科研经费', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('e7c50f97-3a87-4ea0-8c8f-9e5f4b23a472', '张三', 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '其他', '2023-09-15', 4.00, '填报导师培养力材料弄虚作假', '通报批评', 0, '2025-04-23 14:06:21', '2025-04-24 10:23:44');
INSERT INTO `deductions` VALUES ('e8c50f98-3a87-4ea0-8c8f-9e5f4b23a473', '书中枫叶', '61ec01da-fd50-4b5a-a377-de346150b655', '科研项目', '2023-02-10', 6.00, '未按时提交科研项目进度报告', '限期补交并作书面检讨', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('e9c50f99-3a87-4ea0-8c8f-9e5f4b23a474', '王老师', '88ec01da-fd50-4b5a-a377-de346150b666', '未获得学位', '2023-03-15', 5.00, '研究生超过最长学习年限未毕业', '限制下一年度招生名额', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('f0c50f00-3a87-4ea0-8c8f-9e5f4b23a475', '王老师', '88ec01da-fd50-4b5a-a377-de346150b666', '未就业', '2023-08-10', 10.00, '研究生毕业后未及时就业', '加强就业指导培训', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('f1c50f01-3a87-4ea0-8c8f-9e5f4b23a476', 'John Doe', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '学术道德', '2023-05-12', 18.00, '在学术期刊发表论文存在数据造假问题', '取消评优评先资格，并进行学术道德培训', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('f2c50f02-3a87-4ea0-8c8f-9e5f4b23a477', '赵六', 'd4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9', '师德师风', '2023-10-05', 8.00, '对学生指导不够耐心，造成不良影响', '要求参加师德培训', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('f3c50f03-3a87-4ea0-8c8f-9e5f4b23a478', '孙七', 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', '科研项目', '2023-11-20', 7.50, '未完成科研项目年度目标', '限期整改', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');
INSERT INTO `deductions` VALUES ('f4c50f04-3a87-4ea0-8c8f-9e5f4b23a479', '周八', 'f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0a1', '未就业', '2023-12-15', 20.00, '研究生在毕业当年12月31日前未就业', '停招1年', 1, '2025-04-23 14:06:21', '2025-04-23 14:06:21');

-- ----------------------------
-- Table structure for deductions_rules
-- ----------------------------
DROP TABLE IF EXISTS `deductions_rules`;
CREATE TABLE `deductions_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `unTimelyDegreeCount` int NULL DEFAULT NULL COMMENT '未按时获得学位的研究生数',
  `unTimelyDegreeDeduction` int NULL DEFAULT NULL COMMENT '未按时获得学位的扣减分数',
  `unemploymentDate` date NULL DEFAULT NULL COMMENT '未就业的截止日期（如 8 月 31 日、12 月 31 日）',
  `unemploymentDeduction` int NULL DEFAULT NULL COMMENT '未就业的扣减分数',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（与 id 一致的 UUID）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '扣分规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of deductions_rules
-- ----------------------------
INSERT INTO `deductions_rules` VALUES ('828e4d65-1946-11f0-b4f1-00163e035a15', 1, 5, NULL, NULL, '2025-04-14 23:38:27', '2025-04-15 16:08:37', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `deductions_rules` VALUES ('828e51cc-1946-11f0-b4f1-00163e035a15', 2, 10, NULL, NULL, '2025-04-14 23:38:27', '2025-04-14 23:39:18', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `deductions_rules` VALUES ('828e5452-1946-11f0-b4f1-00163e035a15', NULL, NULL, '2024-08-31', 10, '2025-04-14 23:38:27', '2025-04-15 16:08:50', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `deductions_rules` VALUES ('828e5554-1946-11f0-b4f1-00163e035a15', NULL, NULL, '2024-12-31', 20, '2025-04-14 23:38:27', '2025-04-14 23:39:19', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '院系ID',
  `department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '院系名称',
  PRIMARY KEY (`id` DESC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of departments
-- ----------------------------

-- ----------------------------
-- Table structure for employment_quality
-- ----------------------------
DROP TABLE IF EXISTS `employment_quality`;
CREATE TABLE `employment_quality`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `major` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '专业',
  `year` int NOT NULL COMMENT '年份',
  `employmentRate` decimal(5, 2) NOT NULL COMMENT '就业率',
  `employmentIndustry` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '就业行业',
  `employmentRegion` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '就业地区',
  `averageSalary` decimal(10, 2) NOT NULL COMMENT '平均薪资',
  `majorMatchRate` decimal(5, 2) NOT NULL COMMENT '专业匹配率',
  `employmentSatisfaction` decimal(5, 2) NOT NULL COMMENT '就业满意度',
  `score` decimal(5, 2) NOT NULL COMMENT '综合评分',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `userId` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_major`(`major` ASC) USING BTREE,
  INDEX `idx_year`(`year` ASC) USING BTREE,
  INDEX `idx_userId`(`userId` ASC) USING BTREE,
  INDEX `idx_employment_quality_major`(`major` ASC) USING BTREE,
  INDEX `idx_employment_quality_year`(`year` ASC) USING BTREE,
  INDEX `idx_employment_quality_status`(`status` ASC) USING BTREE,
  INDEX `idx_employment_quality_user_id`(`userId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '就业质量表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of employment_quality
-- ----------------------------
INSERT INTO `employment_quality` VALUES ('3ac9960d-5833-47b8-9f9e-64f7e7042377', '医学', 2021, 21.00, '制造业', '三线城市', 2000.00, 0.50, 52.00, 41.00, NULL, 1, 'f35f39c3-95c5-4496-80fb-7877bf715aa7', 'John Doe', '2025-04-21 16:01:10', '2025-04-21 16:01:10');
INSERT INTO `employment_quality` VALUES ('4dac7824-f58f-4c61-bc00-bb7b72280fea', '医学', 2025, 0.40, '教育', '二线城市', 4000.00, 0.40, 0.30, 100.00, NULL, 0, 'f35f39c3-95c5-4496-80fb-7877bf715aa7', 'John Doe', '2025-04-18 15:57:21', '2025-04-22 10:19:04');
INSERT INTO `employment_quality` VALUES ('b7a8d1c3-e6f2-4a5b-9c8d-7e6f5a4b3c2d', '计算机科学与技术', 2023, 95.50, 'IT', '一线城市', 12000.00, 85.50, 90.50, 92.00, NULL, 1, '001', '张三', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('c8b9e2d3-f7g4-5h6i-0j1k-8l9m6n5o4p3', '金融学', 2023, 92.30, '金融', '一线城市', 11000.00, 80.50, 88.50, 90.00, NULL, 1, '002', '李四', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('d9c0f3e4-g8h5-6i7j-1k2l-9m0n7o6p5q4', '教育学', 2023, 88.70, '教育', '二线城市', 8000.00, 90.50, 85.50, 88.00, NULL, 1, '003', '王五', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('e0d1g4f5-h9i6-7j8k-2l3m-0n1o8p7q6r5', '机械工程', 2023, 90.20, '制造业', '二线城市', 9000.00, 75.50, 82.50, 85.00, NULL, 0, '004', '赵六', '2025-04-18 14:18:53', '2025-04-22 10:19:30');
INSERT INTO `employment_quality` VALUES ('f1e2h5g6-i0j7-8k9l-3m4n-1o2p9q8r7s6', '工商管理', 2023, 87.50, '服务业', '三线城市', 7500.00, 70.50, 80.50, 82.00, NULL, 1, '005', '钱七', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('g2f3i6h7-j1k8-9l0m-4n5o-2p3q0r9s8t7', '计算机科学与技术', 2022, 93.20, 'IT', '一线城市', 11000.00, 83.20, 88.60, 90.00, NULL, 1, '001', '张三', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('h3g4j7i8-k2l9-0m1n-5o6p-3q4r1s0t9u8', '金融学', 2022, 90.10, '金融', '一线城市', 10000.00, 78.30, 86.70, 88.00, NULL, 1, '002', '李四', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('i4h5k8j9-l3m0-1n2o-6p7q-4r5s2t1u0v9', '教育学', 2022, 86.50, '教育', '二线城市', 7500.00, 88.20, 83.70, 86.00, NULL, 1, '003', '王五', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('j5i6l9k0-m4n1-2o3p-7q8r-5s6t3u2v1w0', '机械工程', 2022, 88.60, '制造业', '二线城市', 8500.00, 73.20, 80.40, 83.00, NULL, 1, '004', '赵六', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('k6j7m0l1-n5o2-3p4q-8r9s-6t7u4v3w2x1', '工商管理', 2022, 85.30, '服务业', '三线城市', 7000.00, 68.30, 78.60, 80.00, NULL, 1, '005', '钱七', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('l7k8n1m2-o6p3-4q5r-9s0t-7u8v5w4x3y2', '软件工程', 2023, 96.80, 'IT', '一线城市', 13500.00, 92.30, 93.50, 94.00, NULL, 1, '001', '张三', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('m8l9o2n3-p7q4-5r6s-0t1u-8v9w6x5y4z3', '数据科学', 2023, 94.50, 'IT', '一线城市', 13000.00, 89.70, 91.20, 93.00, NULL, 1, '002', '', '2025-04-18 14:18:53', '2025-04-22 10:19:39');
INSERT INTO `employment_quality` VALUES ('n9m0p3o4-q8r5-6s7t-1u2v-9w0x7y6z5a4', '人工智能', 2023, 95.80, 'IT', '一线城市', 14000.00, 91.40, 92.80, 95.00, NULL, 0, '006', '孙八', '2025-04-18 14:18:53', '2025-04-22 10:40:16');
INSERT INTO `employment_quality` VALUES ('o0n1q4p5-r9s6-7t8u-2v3w-0x1y8z7a6b5', '物联网工程', 2023, 93.70, 'IT', '一线城市', 12500.00, 88.90, 90.60, 91.00, NULL, 1, 'f35f39c3-95c5-4496-80fb-7877bf715aa7', 'John Doe', '2025-04-18 14:18:53', '2025-04-22 10:19:45');
INSERT INTO `employment_quality` VALUES ('p1o2r5q6-s0t7-8u9v-3w4x-1y2z9a8b7c6', '会计学', 2023, 89.40, '金融', '一线城市', 9500.00, 82.60, 86.30, 87.00, '123', 1, '008', '吴十', '2025-04-18 14:18:53', '2025-04-22 10:44:02');
INSERT INTO `employment_quality` VALUES ('q2p3s6r7-t1u8-9v0w-4x5y-2z3a0b9c8d7', '市场营销', 2023, 88.20, '服务业', '二线城市', 9000.00, 79.80, 84.50, 85.00, NULL, 1, '009', '郑十一', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('r3q4t7s8-u2v9-0w1x-5y6z-3a4b1c0d9e8', '国际贸易', 2023, 87.90, '服务业', '一线城市', 9200.00, 81.20, 85.70, 86.00, NULL, 1, '010', '王十二', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('s4r5u8t9-v3w0-1x2y-6z7a-4b5c2d1e0f9', '英语', 2023, 85.60, '教育', '二线城市', 8200.00, 87.30, 84.90, 85.00, NULL, 1, '011', '李十三', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('t5s6v9u0-w4x1-2y3z-7a8b-5c6d3e2f1g0', '法学', 2023, 86.80, '服务业', '一线城市', 9800.00, 85.40, 87.20, 88.00, NULL, 1, '012', '张十四', '2025-04-18 14:18:53', '2025-04-18 14:18:53');
INSERT INTO `employment_quality` VALUES ('u6t7w0v1-x5y2-3z4a-8b9c-6d7e4f3g2h1', '医学', 2023, 94.30, '医疗', '一线城市', 11200.00, 93.60, 91.80, 93.00, '阿萨', 1, '013', '刘十五', '2025-04-18 14:18:53', '2025-04-22 10:44:09');

-- ----------------------------
-- Table structure for high_level_papers
-- ----------------------------
DROP TABLE IF EXISTS `high_level_papers`;
CREATE TABLE `high_level_papers`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文题目',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文类型',
  `journal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发表期刊',
  `impactFactor` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '影响因子',
  `publishDate` date NOT NULL COMMENT '发表时间',
  `authors` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '作者列表',
  `firstAuthorType` tinyint(1) NOT NULL DEFAULT 1 COMMENT '第一作者类型(1:我院研究生,0:非我院研究生)',
  `hasCoFirstAuthor` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有共同第一作者(1:是,0:否)',
  `firstAuthorRank` int NULL DEFAULT NULL COMMENT '共同第一作者排名',
  `correspondingAuthor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '通讯作者',
  `citations` int NOT NULL DEFAULT 0 COMMENT '引用次数',
  `calculatedScore` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '计算得分',
  `score` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '评分',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_high_level_paper_type`(`type` ASC) USING BTREE,
  INDEX `idx_high_level_paper_status`(`status` ASC) USING BTREE,
  INDEX `idx_high_level_paper_corresponding_author`(`correspondingAuthor` ASC) USING BTREE,
  INDEX `idx_high_level_paper_publish_date`(`publishDate` ASC) USING BTREE,
  INDEX `idx_high_level_paper_first_author_type`(`firstAuthorType` ASC) USING BTREE,
  INDEX `idx_high_level_paper_co_first_author`(`hasCoFirstAuthor` ASC) USING BTREE,
  INDEX `idx_high_level_paper_calculated_score`(`calculatedScore` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '高水平论文评分表(包含第一作者类型和共同作者排名)' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of high_level_papers
-- ----------------------------
INSERT INTO `high_level_papers` VALUES ('5bc29a2f-bfbc-40f4-8a06-e5cf6ccb3e24', 'suhy的高水平论文', 'A1-I', 'TOP1', 2.00, '2025-04-01', '张三,李四,f35f39c3-95c5-4496-80fb-7877bf715aa7', 1, 0, NULL, '张三', 5, 50.00, 50.00, 1, '[plugin:vite:vue] [@vue/compiler-sfc] Identifier \'getHighLevelPapers\' has already been declared. (26:2)\n\nC:/Users/<USER>/Desktop/工作/暨南大学/暨南大学基础医学与公共卫生学院教师绩效评定与管理平台/ADMIN/src/views/performance/high-level-papers.vue\n463|  // 导入API方法\n464|  import { \n465|    getHighLevelPapers, \n466|    getPersonalHighLevelPapers,\n467|    getPersonalPaperStats,\nC:/Users/<USER>/Desktop/工作/暨南大学/暨南大学基础医学与公共卫生学院教师绩效评定与管理平台/ADMIN/src/views/performance/high-level-papers.vue:26:2\n16 |            </a-button>\n17 |            <a-button :type=\"showPersonalPapers ? \'default\' : \'primary\'\" @click=\"togglePersonalPapers\">\n18 |              <template #icon><user-outlined /></template>\n   |                                          ^\n19 |              {{ showPersonalPapers ? \'查看全部论文\' : \'查看我的论文\' }}\n20 |            </a-button>\n    at instantiate (C:\\Users\\<USER>\\Desktop\\工作\\暨南大学\\暨南大学基础医学与公共卫生学院教师绩效评定与管理平台\\ADMIN\\node_modules\\@babel\\parser\\lib\\index.js:653:32)\n    at cons', '2025-04-11 09:32:02', '2025-04-11 14:31:11');
INSERT INTO `high_level_papers` VALUES ('7404d8c5-7ab3-4461-8655-18124739958c', 'suhy的高水平论文2', '中文核心期刊', 'A类', 4.00, '2025-04-08', 'suhy,f35f39c3-95c5-4496-80fb-7877bf715aa7', 1, 1, 1, 'f35f39c3-95c5-4496-80fb-7877bf715aa7', 4, 1.00, 1.00, 1, '[plugin:vite:vue] [@vue/compiler-sfc] Identifier \'getHighLevelPapers\' has already been declared. (26:2)\n\nC:/Users/<USER>/Desktop/工作/暨南大学/暨南大学基础医学与公共卫生学院教师绩效评定与管理平台/ADMIN/src/views/performance/high-level-papers.vue\n463|  // 导入API方法\n464|  import { \n465|    getHighLevelPapers, \n466|    getPersonalHighLevelPapers,\n467|    getPersonalPaperStats,\nC:/Users/<USER>/Desktop/工作/暨南大学/暨南大学基础医学与公共卫生学院教师绩效评定与管理平台/ADMIN/src/views/performance/high-level-papers.vue:26:2\n16 |            </a-button>\n17 |            <a-button :type=\"showPersonalPapers ? \'default\' : \'primary\'\" @click=\"togglePersonalPapers\">\n18 |              <template #icon><user-outlined /></template>\n   |                                          ^\n19 |              {{ showPersonalPapers ? \'查看全部论文\' : \'查看我的论文\' }}\n20 |            </a-button>\n    at instantiate (C:\\Users\\<USER>\\Desktop\\工作\\暨南大学\\暨南大学基础医学与公共卫生学院教师绩效评定与管理平台\\ADMIN\\node_modules\\@babel\\parser\\lib\\index.js:653:32)\n    at cons', '2025-04-11 09:35:01', '2025-04-11 14:30:52');
INSERT INTO `high_level_papers` VALUES ('c9958c0c-e8cf-4588-91d7-8f517b5657d8', 'suhy的高水平论文1', 'A2-III', 'A类', 23.00, '2025-04-01', 'ADMIN,张三', 1, 0, NULL, 'ADMIN', 23, 0.00, 10.00, 1, '[plugin:vite:vue] [@vue/compiler-sfc] Identifier \'getHighLevelPapers\' has already been declared. (26:2)\n\nC:/Users/<USER>/Desktop/工作/暨南大学/暨南大学基础医学与公共卫生学院教师绩效评定与管理平台/ADMIN/src/views/performance/high-level-papers.vue\n463|  // 导入API方法\n464|  import { \n465|    getHighLevelPapers, \n466|    getPersonalHighLevelPapers,\n467|    getPersonalPaperStats,\nC:/Users/<USER>/Desktop/工作/暨南大学/暨南大学基础医学与公共卫生学院教师绩效评定与管理平台/ADMIN/src/views/performance/high-level-papers.vue:26:2\n16 |            </a-button>\n17 |            <a-button :type=\"showPersonalPapers ? \'default\' : \'primary\'\" @click=\"togglePersonalPapers\">\n18 |              <template #icon><user-outlined /></template>\n   |                                          ^\n19 |              {{ showPersonalPapers ? \'查看全部论文\' : \'查看我的论文\' }}\n20 |            </a-button>\n    at instantiate (C:\\Users\\<USER>\\Desktop\\工作\\暨南大学\\暨南大学基础医学与公共卫生学院教师绩效评定与管理平台\\ADMIN\\node_modules\\@babel\\parser\\lib\\index.js:653:32)\n    at cons', '2025-04-11 09:32:50', '2025-04-11 09:36:52');

-- ----------------------------
-- Table structure for high_level_papers_rules
-- ----------------------------
DROP TABLE IF EXISTS `high_level_papers_rules`;
CREATE TABLE `high_level_papers_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `paperLevel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '论文级别（如 A1 Ⅰ、A1 Ⅱ 等）',
  `baseScore` decimal(10, 2) NOT NULL COMMENT '基础核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '论文级别的详细描述（如中科院 JCR 分区等）',
  `nonDepartmentAuthorCoefficient` decimal(10, 2) NULL DEFAULT 0.90 COMMENT '非本院研究生第一作者的系数',
  `coFirstAuthorRankCoefficient` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '共同第一作者的排名系数（如 100%, 1/2, 1/3 等）',
  `maxPapersPerMentor` int NULL DEFAULT 5 COMMENT '每个导师最多填写的代表性论文数量',
  `nonDepartmentAuthorLimit` int NULL DEFAULT 1 COMMENT '非本院研究生第一作者的文章数量限制',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_paper_level`(`paperLevel` ASC) USING BTREE COMMENT '确保论文级别唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '高水平论文核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of high_level_papers_rules
-- ----------------------------
INSERT INTO `high_level_papers_rules` VALUES ('d4ef0afa-190f-11f0-b4f1-00163e035a15', 'A1 Ⅰ（或 top 期刊）', 50.00, '中科院 JCR 分区 A1 Ⅰ区或 top 期刊', 0.90, '100%, 1/2, 1/3', 5, 1, '2025-04-14 17:07:03', '2025-04-14 22:52:40', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `high_level_papers_rules` VALUES ('d4ef1135-190f-11f0-b4f1-00163e035a15', 'A1 Ⅱ', 20.00, '中科院 JCR 分区 A1 Ⅱ区', 0.90, '100%, 1/2, 1/3', 5, 1, '2025-04-14 17:07:03', '2025-04-14 18:04:01', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `high_level_papers_rules` VALUES ('d4ef15a2-190f-11f0-b4f1-00163e035a15', 'A2 Ⅲ（含 Aging Research）', 10.00, '中科院 JCR 分区 A2 Ⅲ区（含 Aging Research）', 0.90, '100%, 1/2, 1/3', 5, 1, '2025-04-14 17:07:03', '2025-04-14 18:04:01', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `high_level_papers_rules` VALUES ('d4ef4b85-190f-11f0-b4f1-00163e035a15', 'A2 Ⅳ', 1.00, '中科院 JCR 分区 A2 Ⅳ区', 0.90, '100%, 1/2, 1/3', 5, 1, '2025-04-14 17:07:03', '2025-04-14 18:04:01', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `high_level_papers_rules` VALUES ('d4ef4ebe-190f-11f0-b4f1-00163e035a15', '中国科技期刊卓越行动计划入选期刊目录', 10.00, '中国科技期刊卓越行动计划入选期刊目录', 0.90, '100%, 1/2, 1/3', 5, 1, '2025-04-14 17:07:03', '2025-04-14 18:04:01', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `high_level_papers_rules` VALUES ('d4ef4f89-190f-11f0-b4f1-00163e035a15', '中文核心期刊', 1.00, '中文核心期刊', 0.90, '100%, 1/2, 1/3', 5, 1, '2025-04-14 17:07:03', '2025-04-14 18:04:01', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');

-- ----------------------------
-- Table structure for international_exchanges
-- ----------------------------
DROP TABLE IF EXISTS `international_exchanges`;
CREATE TABLE `international_exchanges`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交流名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交流类型',
  `country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交流国家/地区',
  `institution` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '交流机构',
  `startDate` date NOT NULL COMMENT '开始时间',
  `endDate` date NOT NULL COMMENT '结束时间',
  `userIdList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '参与用户ID列表',
  `usernameList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '参与用户列表',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '交流内容',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '交流成果',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_exchange_type`(`type` ASC) USING BTREE,
  INDEX `idx_exchange_country`(`country` ASC) USING BTREE,
  INDEX `idx_exchange_institution`(`institution` ASC) USING BTREE,
  INDEX `idx_exchange_dates`(`startDate` ASC, `endDate` ASC) USING BTREE,
  INDEX `idx_exchange_status`(`status` ASC) USING BTREE,
  FULLTEXT INDEX `idx_exchange_user_ids`(`userIdList`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '国际交流表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of international_exchanges
-- ----------------------------
INSERT INTO `international_exchanges` VALUES ('8f9ba1ca-ef92-46c1-ab87-08692f992030', '测试项目', '短期培训', '中国', '领事馆', '2025-04-02', '2025-05-02', 'temp-1744617439097-lxp7c,temp-1744617618053-8eia7,temp-1744618408593-sci6m,temp-1744618408593-v475o,temp-1744618409767-ytij9,temp-1744618409767-d6tsy', 'f35f39c3-95c5-4496-80fb-7877bf715aa7,suhy', '测试', '算是', 89.00, 1, '2025-04-14 16:00:18', '2025-04-14 16:13:30');
INSERT INTO `international_exchanges` VALUES ('d722d565-e502-47ef-8cc8-9f723e088680', '测试项目', '国际会议', '中国', '领事馆', '2025-04-15', '2025-05-20', 'temp-1744616199042-4eswe,temp-1744616201994-9xbr8,temp-1744616205337-k3dkc,temp-1744616225621-pb41w', 'suhy', '吹牛皮', '吹得很棒', 100.00, 1, '2025-04-14 15:37:05', '2025-04-14 15:37:05');

-- ----------------------------
-- Table structure for international_exchanges_rules
-- ----------------------------
DROP TABLE IF EXISTS `international_exchanges_rules`;
CREATE TABLE `international_exchanges_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `project` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称（如来华学习交流、赴港澳台学习交流等）',
  `score` decimal(10, 2) NOT NULL COMMENT '核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目的详细描述（如来华学习交流超过 90 天的境外研究生）',
  `maxPeople` int NULL DEFAULT NULL COMMENT '最多记录个数',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId，与 id 一致的 UUID）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_project`(`project` ASC) USING BTREE COMMENT '确保项目名称唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '国际交流与合作评价核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of international_exchanges_rules
-- ----------------------------
INSERT INTO `international_exchanges_rules` VALUES ('1b07a1c8-1928-11f0-b4f1-00163e035a15', '来华学习交流超过 90 天的境外研究生', 20.00, '来华学习交流超过 90 天的境外研究生', NULL, '2025-04-14 20:00:49', '2025-04-14 20:01:20', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `international_exchanges_rules` VALUES ('1b07a761-1928-11f0-b4f1-00163e035a15', '赴港澳台学习交流连续超过 3 个月的本院研究生', 30.00, '第一个研究生加 30 分,如有第二个再加 20 分，如有第三个再加 10 分，最多计算 3 个研究生', 3, '2025-04-14 20:00:49', '2025-04-15 16:21:26', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `international_exchanges_rules` VALUES ('1b07a9ee-1928-11f0-b4f1-00163e035a15', '赴国外学习交流连续超过 3 个月小于一年的本院研究生', 50.00, '第一个研究生加 50 分,如有第二个再加 30 分，如有第三个再加 20 分，最多计算 3 个研究生', 3, '2025-04-14 20:00:49', '2025-04-15 16:21:28', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '唯一标识每条记录的主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录的类型，例如research_project',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目的标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目的详细内容',
  `created_at` datetime NOT NULL COMMENT '记录创建的时间戳',
  `abstract` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '与通知相关的摘要信息',
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '与记录关联的用户ID，使用UUID格式',
  `initiator_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通知的发起人ID，使用UUID格式',
  `is_read` tinyint NOT NULL DEFAULT 0 COMMENT '标识通知是否已被阅读，0表示未读，1表示已读',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of notifications
-- ----------------------------
INSERT INTO `notifications` VALUES (1, 'urgent', '开会', '礼堂三楼', '2025-04-09 16:30:22', '你好', '61ec01da-fd50-4b5a-a377-de346150b655', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', 0);

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限键',
  `parent_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '父级权限键（可选）',
  `auth` tinyint(1) NULL DEFAULT 0 COMMENT '是否是权限按钮',
  `status` int NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permissions
-- ----------------------------
INSERT INTO `permissions` VALUES ('02b8a444-f08a-4764-b6af-da78dfadc55d', '删除', 'notification:send:delete', 'notification:send', 1, 1, '2025-04-25 16:21:52', '2025-04-25 16:21:52');
INSERT INTO `permissions` VALUES ('04a33a70-1905-4d0e-a326-b444eab97f17', '发送通知', 'notification:send', 'notification', 0, 1, '2025-04-25 16:21:52', '2025-04-25 16:21:52');
INSERT INTO `permissions` VALUES ('0782054e-30b6-41ee-a2f8-25c5a426b3b8', '查询', 'sys:role:list', 'sys:role', 1, 1, '2023-11-18 10:24:28', '2023-11-18 15:34:41');
INSERT INTO `permissions` VALUES ('07bed29d-0f07-4610-9dcf-bd90b8649a53', '删除', 'data:dashboard:delete', 'data:dashboard', 1, 1, '2025-04-25 15:53:59', '2025-04-25 15:53:59');
INSERT INTO `permissions` VALUES ('0c4238e2-13cf-4be2-a479-0accf0f72e10', '增加', 'score:C:create', 'score:C', 1, 1, '2025-04-27 09:31:38', '2025-04-27 09:31:38');
INSERT INTO `permissions` VALUES ('0ce295ba-84d7-4992-8192-3e5d4b81f1f5', '删除', 'score:B:delete', 'score:B', 1, 1, '2025-04-27 09:28:52', '2025-04-27 09:28:52');
INSERT INTO `permissions` VALUES ('0db296c8-8e53-434e-be6b-e62ea9a24c8c', '角色管理', 'sys:role', 'sys', 0, 1, '2023-11-18 10:24:28', '2023-11-18 10:24:28');
INSERT INTO `permissions` VALUES ('0eb8799e-adb2-480c-a136-ba7664ca75d6', '超级管理权限', '*', '', 0, 1, '2023-11-18 06:06:35', '2023-11-18 15:42:43');
INSERT INTO `permissions` VALUES ('0f638383-3875-4ebd-99eb-12ae9bf8adb4', '增加', 'notification:template:create', 'notification:template', 1, 1, '2025-04-25 16:32:26', '2025-04-25 16:32:26');
INSERT INTO `permissions` VALUES ('10a11fec-a635-4f5b-80d9-0f4237be81eb', '查询', 'data:dashboard:list', 'data:dashboard', 1, 1, '2025-04-25 15:53:59', '2025-04-25 15:53:59');
INSERT INTO `permissions` VALUES ('12499228-5374-419c-bae9-cfac940c3243', '查询', 'notification:setting:list', 'notification:setting', 1, 1, '2025-04-25 16:31:37', '2025-04-25 16:31:37');
INSERT INTO `permissions` VALUES ('173ddbfc-08e7-4042-9796-d6a03ca58b2f', '用户管理', 'sys:user', 'sys', 0, 1, '2023-11-18 10:16:18', '2023-11-18 10:16:18');
INSERT INTO `permissions` VALUES ('17a21065-2e4e-44c8-97d4-dc02eac8d434', '查询', 'score:G:list', 'score:G', 1, 1, '2025-04-27 09:32:35', '2025-04-27 09:32:35');
INSERT INTO `permissions` VALUES ('1a4a4354-83c7-4903-8c07-f34af7c8345a', '更新', 'score:C:update', 'score:C', 1, 1, '2025-04-27 09:31:38', '2025-04-27 09:31:38');
INSERT INTO `permissions` VALUES ('1e0a5f75-74ba-4731-a0f8-b10067742dfa', '删除', 'score:D:delete', 'score:D', 1, 1, '2025-04-27 09:31:54', '2025-04-27 09:31:54');
INSERT INTO `permissions` VALUES ('1e2277d2-815e-4d1d-8938-6c1c4f0bb14d', '增加', 'data:home:create', 'data:home', 1, 1, '2025-04-25 15:51:53', '2025-04-25 15:51:53');
INSERT INTO `permissions` VALUES ('1ffd5e9a-0b9a-44bb-b472-d4d43f350a31', '删除', 'score:A:delete', 'score:A', 1, 1, '2025-04-27 09:28:20', '2025-04-27 09:28:20');
INSERT INTO `permissions` VALUES ('239bc2d2-12fc-48f9-914e-e592bb0678df', '删除', 'data:home:delete', 'data:home', 1, 1, '2025-04-25 15:51:53', '2025-04-25 15:51:53');
INSERT INTO `permissions` VALUES ('23e6e73e-9578-4130-bf72-ca808895cb5d', '增加', 'score:G:create', 'score:G', 1, 1, '2025-04-27 09:32:35', '2025-04-27 09:32:35');
INSERT INTO `permissions` VALUES ('2b67fe56-646f-4310-abb8-ec454524ca7f', '删除', 'score:C:delete', 'score:C', 1, 1, '2025-04-27 09:31:38', '2025-04-27 09:31:38');
INSERT INTO `permissions` VALUES ('2bdd2a5b-90cf-4a26-8b3a-732358e00653', '权限管理', 'sys:permissions', 'sys', 0, 1, '2023-11-18 10:15:38', '2023-11-18 10:15:38');
INSERT INTO `permissions` VALUES ('2be1b7cc-20c3-44b0-b358-e931b73305bc', '查询', 'notification:send:list', 'notification:send', 1, 1, '2025-04-25 16:21:52', '2025-04-25 16:21:52');
INSERT INTO `permissions` VALUES ('30dd831c-ef1e-4e4b-b584-f8a4f37c3130', '更新', 'score:D:update', 'score:D', 1, 1, '2025-04-27 09:31:54', '2025-04-27 09:31:54');
INSERT INTO `permissions` VALUES ('31cd2a47-5203-4d4e-a296-a58231103cbb', '通知列表', 'notification:list', 'notification', 0, 1, '2025-04-25 16:21:21', '2025-04-25 16:21:21');
INSERT INTO `permissions` VALUES ('32186941-1588-4525-864a-5187c65c0c00', '增加', 'score:H:create', 'score:H', 1, 1, '2025-04-27 09:33:01', '2025-04-27 09:33:01');
INSERT INTO `permissions` VALUES ('33244f44-ae8f-4067-a900-9d0049e75aa5', 'B科研经费', 'score:B', 'score', 0, 1, '2025-04-27 09:28:52', '2025-04-27 09:28:52');
INSERT INTO `permissions` VALUES ('33b201c5-0b85-4c98-a08d-55917ff79529', '增加', 'score:D:create', 'score:D', 1, 1, '2025-04-27 09:31:54', '2025-04-27 09:31:54');
INSERT INTO `permissions` VALUES ('34d2bc2d-7dd1-4837-97fe-eee7f3467363', 'F社会服务', 'score:F', 'score', 0, 1, '2025-04-27 09:32:20', '2025-04-27 09:32:20');
INSERT INTO `permissions` VALUES ('3732eabd-8658-4c81-bcd7-72a9dd523f94', '查询', 'score:C:list', 'score:C', 1, 1, '2025-04-27 09:31:38', '2025-04-27 09:31:38');
INSERT INTO `permissions` VALUES ('3912f77c-8b18-466b-93d8-0c2954c3e12d', '更新', 'data:dashboard:update', 'data:dashboard', 1, 1, '2025-04-25 15:54:00', '2025-04-25 15:54:00');
INSERT INTO `permissions` VALUES ('396c964d-014e-4392-a545-17e70a634bcf', '查询', 'score:D:list', 'score:D', 1, 1, '2025-04-27 09:31:54', '2025-04-27 09:31:54');
INSERT INTO `permissions` VALUES ('3ab88114-f6e7-4df0-bb8f-08b6ce7a732f', '更新', 'sys:permissions:update', 'sys:permissions', 1, 1, '2023-11-18 10:15:38', '2023-11-18 10:15:38');
INSERT INTO `permissions` VALUES ('3be6b9fc-01c4-42a3-926b-1e947eb0c72f', 'D教学与科研', 'score:D', 'score', 0, 1, '2025-04-27 09:31:54', '2025-04-27 09:31:54');
INSERT INTO `permissions` VALUES ('3d86bb62-d207-4f4c-83b3-d3df777e6d9e', '首页', 'data:home', 'data', 0, 1, '2025-04-25 15:51:53', '2025-04-25 15:51:53');
INSERT INTO `permissions` VALUES ('4004e9c8-1264-40d2-99ab-9a2fdae7b45f', '查询', 'sys:user:list', 'sys:user', 1, 1, '2023-11-18 10:16:18', '2023-11-18 10:16:18');
INSERT INTO `permissions` VALUES ('40d91521-0365-4903-bdd8-b0a6232fe446', '更新', 'sys:user:update', 'sys:user', 1, 1, '2023-11-18 10:16:18', '2023-11-18 10:16:18');
INSERT INTO `permissions` VALUES ('4391fced-05ac-47cc-b6b3-e0810fc9f10d', '更新', 'notification:list:update', 'notification:list', 1, 1, '2025-04-25 16:21:21', '2025-04-25 16:21:21');
INSERT INTO `permissions` VALUES ('45253de9-bba3-4302-bacc-b4e9b9845cb2', '删除', 'sys:user:delete', 'sys:user', 1, 1, '2023-11-18 10:16:18', '2023-11-18 10:16:18');
INSERT INTO `permissions` VALUES ('47bf914e-2d4a-4746-8922-f6864488c7f0', '更新', 'sys:role:update', 'sys:role', 1, 1, '2023-11-18 10:24:28', '2023-11-18 10:24:28');
INSERT INTO `permissions` VALUES ('498bc660-8839-4ffb-a737-ec4a9e0bfd55', '增加', 'data:dashboard:create', 'data:dashboard', 1, 1, '2025-04-25 15:54:00', '2025-04-25 15:54:00');
INSERT INTO `permissions` VALUES ('4d235480-9f63-4f75-a58d-e33fa12f9caa', 'A科研项目', 'score:A', 'score', 0, 1, '2025-04-27 09:28:20', '2025-04-27 09:28:20');
INSERT INTO `permissions` VALUES ('505c6646-b0b3-4579-9a56-f0960e9aea28', '更新', 'score:H:update', 'score:H', 1, 1, '2025-04-27 09:33:01', '2025-04-27 09:33:01');
INSERT INTO `permissions` VALUES ('50996070-ce69-4906-98c9-81c937e22a83', '增加', 'score:A:create', 'score:A', 1, 1, '2025-04-27 09:28:20', '2025-04-27 09:28:20');
INSERT INTO `permissions` VALUES ('51fa4d99-a136-4f57-8929-0c04f466ecb8', '增加', 'sys:permissions:create', 'sys:permissions', 1, 1, '2023-11-18 10:15:38', '2023-11-18 10:15:38');
INSERT INTO `permissions` VALUES ('5492e2af-1e4d-45c6-b21f-1abe63f030c3', '增加', 'score:F:create', 'score:F', 1, 1, '2025-04-27 09:32:20', '2025-04-27 09:32:20');
INSERT INTO `permissions` VALUES ('56cd15bf-e8c7-4753-a9fe-6843f8d1f06d', '删除', 'score:H:delete', 'score:H', 1, 1, '2025-04-27 09:33:01', '2025-04-27 09:33:01');
INSERT INTO `permissions` VALUES ('581eab91-f487-4c2d-af4c-bc50b767fc7f', '更新', 'score:F:update', 'score:F', 1, 1, '2025-04-27 09:32:20', '2025-04-27 09:32:20');
INSERT INTO `permissions` VALUES ('5b4f5224-1414-4966-b84d-5fae06ad339f', '查询', 'sys:permissions:list', 'sys:permissions', 1, 1, '2023-11-18 10:15:38', '2023-11-18 10:15:38');
INSERT INTO `permissions` VALUES ('5c4cc5ae-cf7f-4519-bca8-415d04e7d2b2', '删除', 'sys:permissions:delete', 'sys:permissions', 1, 1, '2023-11-18 10:15:38', '2023-11-18 10:15:38');
INSERT INTO `permissions` VALUES ('5f8d0d50-79f4-42ca-b72a-1de20bc39e83', '通知管理', 'notification', NULL, 0, 1, '2025-04-25 16:05:39', '2025-04-25 16:20:24');
INSERT INTO `permissions` VALUES ('65c8f96b-32d0-43a9-80c5-bcad90e441ad', '查询', 'score:A:list', 'score:A', 1, 1, '2025-04-27 09:28:20', '2025-04-27 09:28:20');
INSERT INTO `permissions` VALUES ('689992f4-589e-4f0d-bb8f-f7058c291b71', '增加', 'notification:center:create', 'notification:center', 1, 1, '2025-04-25 16:22:20', '2025-04-25 16:22:20');
INSERT INTO `permissions` VALUES ('69357585-ede8-4373-b1c1-2ca9a4b46129', '删除', 'data:dashboard:delete', 'data:dashboard', 1, 1, '2025-04-25 15:54:00', '2025-04-25 15:54:00');
INSERT INTO `permissions` VALUES ('6d70c6da-1216-4b37-858f-d7156d7d02a5', 'H扣分', 'score:H', 'score', 0, 1, '2025-04-27 09:33:01', '2025-04-27 09:33:01');
INSERT INTO `permissions` VALUES ('7317fd76-0a96-427d-a9a4-5a340361f0c2', '查询', 'notification:center:list', 'notification:center', 1, 1, '2025-04-25 16:22:20', '2025-04-25 16:22:20');
INSERT INTO `permissions` VALUES ('784ca7e0-da3c-440a-968a-86f8593f81d0', '增加', 'data:dashboard:create', 'data:dashboard', 1, 1, '2025-04-25 15:53:59', '2025-04-25 15:53:59');
INSERT INTO `permissions` VALUES ('787ce0c6-5a16-4aab-8585-13a8c36c71aa', '查询', 'score:F:list', 'score:F', 1, 1, '2025-04-27 09:32:20', '2025-04-27 09:32:20');
INSERT INTO `permissions` VALUES ('7e069988-0421-4b77-8f22-1104e13e2f0a', '更新', 'score:E:update', 'score:E', 1, 1, '2025-04-27 09:32:07', '2025-04-27 09:32:07');
INSERT INTO `permissions` VALUES ('81d443de-4c39-4a1c-b98e-bbc0d38a0259', '更新', 'score:A:update', 'score:A', 1, 1, '2025-04-27 09:28:20', '2025-04-27 09:28:20');
INSERT INTO `permissions` VALUES ('8255f78d-9f5f-4475-bef4-2f3e7670140a', '查询', 'score:H:list', 'score:H', 1, 1, '2025-04-27 09:33:01', '2025-04-27 09:33:01');
INSERT INTO `permissions` VALUES ('8266ac60-2cf0-42ec-83b4-d8392fcab3d0', '更新', 'score:G:update', 'score:G', 1, 1, '2025-04-27 09:32:35', '2025-04-27 09:32:35');
INSERT INTO `permissions` VALUES ('83e26bfa-80be-443f-8b24-954b935ffd79', '删除', 'score:F:delete', 'score:F', 1, 1, '2025-04-27 09:32:20', '2025-04-27 09:32:20');
INSERT INTO `permissions` VALUES ('8417267c-3afd-43be-b500-b0557f924419', '查询', 'score:E:list', 'score:E', 1, 1, '2025-04-27 09:32:07', '2025-04-27 09:32:07');
INSERT INTO `permissions` VALUES ('875e82f7-f50e-4f41-808a-f6336ef45a55', '数据大屏', 'data:dashboard', 'data', 0, 1, '2025-04-25 15:54:00', '2025-04-25 15:54:00');
INSERT INTO `permissions` VALUES ('8c57a46e-8aba-4c1e-b7f5-aad766dd4455', '删除', 'notification:template:delete', 'notification:template', 1, 1, '2025-04-25 16:32:26', '2025-04-25 16:32:26');
INSERT INTO `permissions` VALUES ('8f3815eb-a86e-4a98-b04d-20e9d23b962f', '删除', 'sys:role:delete', 'sys:role', 1, 1, '2023-11-18 10:24:28', '2023-11-18 10:24:28');
INSERT INTO `permissions` VALUES ('921fd53c-37b3-448a-ad04-c6b092c8b912', '更新', 'notification:center:update', 'notification:center', 1, 1, '2025-04-25 16:22:20', '2025-04-25 16:22:20');
INSERT INTO `permissions` VALUES ('9b5fac88-23da-49b6-92aa-c9869ba2cfbd', '增加', 'score:E:create', 'score:E', 1, 1, '2025-04-27 09:32:07', '2025-04-27 09:32:07');
INSERT INTO `permissions` VALUES ('9df1ca2b-952b-460e-95d9-0008995552fa', '删除', 'score:E:delete', 'score:E', 1, 1, '2025-04-27 09:32:07', '2025-04-27 09:32:07');
INSERT INTO `permissions` VALUES ('a2fe2895-1b8d-42dc-b87a-00d8e741ec58', '消息中心', 'notification:center', 'notification', 0, 1, '2025-04-25 16:22:20', '2025-04-25 16:22:20');
INSERT INTO `permissions` VALUES ('a43597c4-f103-4096-b7a6-90d4402385d8', '查询', 'score:B:list', 'score:B', 1, 1, '2025-04-27 09:28:52', '2025-04-27 09:28:52');
INSERT INTO `permissions` VALUES ('a49914c3-d966-4540-8374-c9604c5b48ba', '查询', 'data:home:list', 'data:home', 1, 1, '2025-04-25 15:51:53', '2025-04-25 15:51:53');
INSERT INTO `permissions` VALUES ('a6e21d6f-8412-413a-992c-7fddf79b7c2d', '数据管理', 'data', NULL, 0, 1, '2025-04-25 15:51:27', '2025-04-25 15:51:27');
INSERT INTO `permissions` VALUES ('aba4feb5-d94e-4e93-a09d-0a65b42da1a3', '更新', 'data:dashboard:update', 'data:dashboard', 1, 1, '2025-04-25 15:53:59', '2025-04-25 15:53:59');
INSERT INTO `permissions` VALUES ('ad2edf24-19ba-4d26-a59f-4947d0fbd094', '删除', 'notification:center:delete', 'notification:center', 1, 1, '2025-04-25 16:22:20', '2025-04-25 16:22:20');
INSERT INTO `permissions` VALUES ('ae90631b-6acd-4058-87ae-2cec68aea8b4', '更新', 'score:B:update', 'score:B', 1, 1, '2025-04-27 09:28:52', '2025-04-27 09:28:52');
INSERT INTO `permissions` VALUES ('b6b7d71d-5dbd-46f7-8258-72df9f53c0de', '删除', 'notification:list:delete', 'notification:list', 1, 1, '2025-04-25 16:21:21', '2025-04-25 16:21:21');
INSERT INTO `permissions` VALUES ('bb63ec47-1f71-4ffd-bbaf-cd0aa1bd72d9', '查询', 'notification:template:list', 'notification:template', 1, 1, '2025-04-25 16:32:26', '2025-04-25 16:32:26');
INSERT INTO `permissions` VALUES ('bc74d83a-5964-4134-b2ed-6a81bd830213', '更新', 'data:home:update', 'data:home', 1, 1, '2025-04-25 15:51:53', '2025-04-25 15:51:53');
INSERT INTO `permissions` VALUES ('bca0d38a-8bcd-4755-8c32-2afe02c74575', '通知设置', 'notification:setting', 'notification', 0, 1, '2025-04-25 16:31:37', '2025-04-25 16:31:37');
INSERT INTO `permissions` VALUES ('bca3ed04-a8ce-4003-97e1-cb3a108a6673', '系统管理', 'sys', NULL, 0, 1, '2023-11-18 10:14:41', '2023-11-18 15:42:47');
INSERT INTO `permissions` VALUES ('bed827c2-cb9c-479f-ba60-7e89ebfbe931', '删除', 'notification:setting:delete', 'notification:setting', 1, 1, '2025-04-25 16:31:37', '2025-04-25 16:31:37');
INSERT INTO `permissions` VALUES ('c25b16a0-4614-43b3-b21f-991c57233519', 'C高水平论文', 'score:C', 'score', 0, 1, '2025-04-27 09:31:38', '2025-04-27 09:31:38');
INSERT INTO `permissions` VALUES ('c4776871-6b31-42a9-b6d0-def932f520b5', '增加', 'notification:send:create', 'notification:send', 1, 1, '2025-04-25 16:21:52', '2025-04-25 16:21:52');
INSERT INTO `permissions` VALUES ('c5d274d9-eb6f-40af-886d-e4bdde24b4cf', '绩效评分管理', 'score', NULL, 0, 1, '2025-04-27 09:24:19', '2025-04-27 09:27:47');
INSERT INTO `permissions` VALUES ('cec36724-bd3d-46f5-8d47-26706c348580', '增加', 'sys:user:create', 'sys:user', 1, 1, '2023-11-18 10:16:18', '2023-11-18 10:16:18');
INSERT INTO `permissions` VALUES ('cec3ec91-bcac-4786-9e9c-1cc1d33e9c65', '更新', 'notification:template:update', 'notification:template', 1, 1, '2025-04-25 16:32:26', '2025-04-25 16:32:26');
INSERT INTO `permissions` VALUES ('d149a011-862e-4221-aa81-4c89cd38bc40', '增加', 'notification:list:create', 'notification:list', 1, 1, '2025-04-25 16:21:21', '2025-04-25 16:21:21');
INSERT INTO `permissions` VALUES ('d59d3cab-7bb3-47b9-9ff2-8f8931486c29', '查询', 'notification:list:list', 'notification:list', 1, 1, '2025-04-25 16:21:21', '2025-04-25 16:21:21');
INSERT INTO `permissions` VALUES ('d99fc4ab-5ff4-4194-b9bb-d107a1c8144e', 'G就业质量', 'score:G', 'score', 0, 1, '2025-04-27 09:32:35', '2025-04-27 09:32:35');
INSERT INTO `permissions` VALUES ('db0c3068-3a43-44a4-b2d5-078ff81a2554', '更新', 'notification:setting:update', 'notification:setting', 1, 1, '2025-04-25 16:31:37', '2025-04-25 16:31:37');
INSERT INTO `permissions` VALUES ('dd26e532-cb18-406d-ad5b-5e96241fa066', '更新', 'notification:send:update', 'notification:send', 1, 1, '2025-04-25 16:21:52', '2025-04-25 16:21:52');
INSERT INTO `permissions` VALUES ('e826c316-0b4d-48be-9351-19dc54db6438', '查询', 'data:dashboard:list', 'data:dashboard', 1, 1, '2025-04-25 15:54:00', '2025-04-25 15:54:00');
INSERT INTO `permissions` VALUES ('f2b078ce-9f29-46f3-84c0-0395828461b3', '删除', 'score:G:delete', 'score:G', 1, 1, '2025-04-27 09:32:35', '2025-04-27 09:32:35');
INSERT INTO `permissions` VALUES ('f2fc4b5d-f33c-446f-93bd-e5b6af947230', '增加', 'sys:role:create', 'sys:role', 1, 1, '2023-11-18 10:24:28', '2023-11-18 10:24:28');
INSERT INTO `permissions` VALUES ('f3ece12c-dd58-4210-9d00-7e0b7d81e6ee', '增加', 'score:B:create', 'score:B', 1, 1, '2025-04-27 09:28:52', '2025-04-27 09:28:52');
INSERT INTO `permissions` VALUES ('fb4158ec-b396-41d6-b0ac-ec80f804dfcf', '增加', 'notification:setting:create', 'notification:setting', 1, 1, '2025-04-25 16:31:37', '2025-04-25 16:31:37');
INSERT INTO `permissions` VALUES ('fc299cc7-eed2-4f49-89a0-48007544fdb4', 'E国际交流', 'score:E', 'score', 0, 1, '2025-04-27 09:32:07', '2025-04-27 09:32:07');
INSERT INTO `permissions` VALUES ('fe3ec237-ee2c-4d8d-bf63-f6bcbaa8b036', '通知模板', 'notification:template', 'notification', 0, 1, '2025-04-25 16:32:26', '2025-04-25 16:32:26');

-- ----------------------------
-- Table structure for research_funds
-- ----------------------------
DROP TABLE IF EXISTS `research_funds`;
CREATE TABLE `research_funds`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '经费类型',
  `amount` decimal(65, 2) NOT NULL COMMENT '经费金额(万元)',
  `startDate` date NOT NULL COMMENT '开始时间',
  `endDate` date NOT NULL COMMENT '结束时间',
  `leader` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目负责人',
  `members` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目成员',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目描述',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_research_fund_leader`(`leader` ASC) USING BTREE,
  INDEX `idx_research_fund_type`(`type` ASC) USING BTREE,
  INDEX `idx_research_fund_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '科研经费评分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of research_funds
-- ----------------------------
INSERT INTO `research_funds` VALUES ('2735fb2d-d7b9-4bb2-824d-c58dd4fca726', '测试项目', 'vertical', 267.00, '2025-04-16', '2026-04-16', '5f39c3-95c5-4496-80fb-7877bf715aa7', '5f39c3-95c5-4496-80fb-7877bf715aa7', '5f39c3-95c5-4496-80fb-7877bf715aa7\n\n博导需要主持在研国家级项目', 20.00, 1, '2025-04-16 10:37:32', '2025-04-16 10:37:32');

-- ----------------------------
-- Table structure for research_funds_rules
-- ----------------------------
DROP TABLE IF EXISTS `research_funds_rules`;
CREATE TABLE `research_funds_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `minAmount` decimal(15, 2) NOT NULL COMMENT '经费范围下限（单位：元）',
  `maxAmount` decimal(15, 2) NULL DEFAULT NULL COMMENT '经费范围上限（单位：元），可以为 NULL 表示无上限',
  `score` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定核算分数',
  `scoreFormula` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分数计算公式（当经费范围无上限时使用）',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_amount_range`(`minAmount` ASC, `maxAmount` ASC) USING BTREE COMMENT '确保经费范围唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '科研经费核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of research_funds_rules
-- ----------------------------
INSERT INTO `research_funds_rules` VALUES ('7008c6d6-190c-11f0-b4f1-00163e035a15', 0.00, 200000.00, 1.00, NULL, '2025-04-14 16:42:45', '2025-04-15 14:56:11', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `research_funds_rules` VALUES ('7008cc42-190c-11f0-b4f1-00163e035a15', 200000.00, NULL, NULL, '200000', '2025-04-14 16:42:45', '2025-04-14 18:04:06', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');

-- ----------------------------
-- Table structure for research_projects
-- ----------------------------
DROP TABLE IF EXISTS `research_projects`;
CREATE TABLE `research_projects`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目级别',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目类型',
  `startDate` date NOT NULL COMMENT '开始时间',
  `endDate` date NOT NULL COMMENT '结束时间',
  `leader` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目负责人的ID',
  `members` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目成员的ID（用,分割）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '项目描述',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '科研项目评分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of research_projects
-- ----------------------------
INSERT INTO `research_projects` VALUES ('29585e17-4902-4369-b381-55fb261c1a0f', '测试', '国家级重点项目、四青人才', '国家级重点项目、四青人才', '2021-04-16', '2025-04-19', '61ec01da-fd50-4b5a-a377-de346150b655', 'admin', 'adminadmin', 125.00, 1, '2025-04-19 00:00:00', '2025-04-19 00:00:00');
INSERT INTO `research_projects` VALUES ('61ec01da-fd50-4b5a-a377-de346150b655', '项目A', '国家级重大项目（国家基金重大项目、杰出青年基金、教育部长江学者、海外长江、万人计划领军人才）', '国家级重大项目（国家基金重大项目、杰出青年基金、教育部长江学者、海外长江、万人计划领军人才）', '2025-04-15', '2025-04-15', '61ec01da-fd50-4b5a-a377-de346150b655', '61ec01da-fd50-4b5a-a377-de346150b655,88ec01da-fd50-4b5a-a377-de346150b666', '这是一个高级研发项目，旨在开发新的系统功能。', 250.00, 1, '2025-04-15 00:00:00', '2025-04-15 00:00:00');
INSERT INTO `research_projects` VALUES ('88ec01da-fd50-4b5a-a377-de346150b666', '项目B', '国家级重点项目、四青人才', '国家级重点项目、四青人才', '2025-04-08', '2025-04-08', '88ec01da-fd50-4b5a-a377-de346150b666', '88ec01da-fd50-4b5a-a377-de346150b666,a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5', '这是一个设计项目，专注于用户界面的优化。', 125.00, 1, '2025-04-08 00:00:00', '2025-04-08 00:00:00');
INSERT INTO `research_projects` VALUES ('a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5', '项目C', '国家级重大项目的培育项目、省部级重大项目、省级人才称号', '国家级重大项目的培育项目、省部级重大项目、省级人才称号', '2025-04-02', '2025-04-02', 'a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5', 'a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5,a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '这是一个测试项目，用于验证系统的稳定性和性能。', 100.00, 1, '2025-04-02 00:00:00', '2025-04-02 00:00:00');
INSERT INTO `research_projects` VALUES ('a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '项目D', '国家级项目、省部级重点项目', '国家级项目、省部级重点项目', '2025-03-11', '2025-03-11', 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6,b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '这是一个高级研发项目，专注于新技术的探索。', 50.00, 1, '2025-03-11 00:00:00', '2025-03-11 00:00:00');
INSERT INTO `research_projects` VALUES ('ae153dc6-877a-40af-88a6-54c7b10976ca', 'Admin的项目', '国家级重大项目（国家基金重大项目、杰出青年基金、教育部长江学者、海外长江、万人计划领军人才）', '国家级重大项目（国家基金重大项目、杰出青年基金、教育部长江学者、海外长江、万人计划领军人才）', '2025-03-26', '2025-03-26', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', 'f35f39c3-95c5-4496-80fb-7877bf715aa7,ADMIN', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', 250.00, 1, '2025-03-26 00:00:00', '2025-03-26 00:00:00');
INSERT INTO `research_projects` VALUES ('b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '项目E', '国家级重大项目（国家基金重大项目、杰出青年基金、教育部长江学者、海外长江、万人计划领军人才）', '国家级重大项目（国家基金重大项目、杰出青年基金、教育部长江学者、海外长江、万人计划领军人才）', '2025-02-20', '2025-02-20', 'b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', 'b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6,b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', '这是一个设计项目，专注于用户体验的提升。', 250.00, 1, '2025-02-20 00:00:00', '2025-02-20 00:00:00');
INSERT INTO `research_projects` VALUES ('b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', '项目F', '国家级重点项目、四青人才', '国家级重点项目、四青人才', '2025-02-19', '2025-02-19', 'b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', 'b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7,c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', '这是一个测试项目，用于验证系统的安全性和可靠性。', 125.00, 1, '2025-02-19 00:00:00', '2025-02-19 00:00:00');
INSERT INTO `research_projects` VALUES ('c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', '项目G', '国家级重大项目的培育项目、省部级重大项目、省级人才称号', '国家级重大项目的培育项目、省部级重大项目、省级人才称号', '2025-03-31', '2025-03-31', 'c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', 'c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7,c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', '这是一个高级研发项目，专注于人工智能的应用。', 100.00, 1, '2025-03-31 00:00:00', '2025-03-31 00:00:00');
INSERT INTO `research_projects` VALUES ('c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', '项目H', '国家级项目、省部级重点项目', '国家级项目、省部级重点项目', '2024-11-27', '2024-11-27', 'c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', 'c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8,d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', '这是一个设计项目，专注于移动应用的界面设计。', 50.00, 1, '2024-11-27 00:00:00', '2024-11-27 00:00:00');
INSERT INTO `research_projects` VALUES ('d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', '项目I', '国家基金的小额资助项目', '国家基金的小额资助项目', '2024-12-18', '2024-12-18', 'd4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', 'd4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8,d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9', '这是一个测试项目，用于验证系统的兼容性和性能。', 38.00, 1, '2024-12-18 00:00:00', '2024-12-18 00:00:00');
INSERT INTO `research_projects` VALUES ('d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9', '项目J', '省部级项目（省基金、科技计划项目）', '省部级项目（省基金、科技计划项目）', '2025-04-03', '2025-04-03', 'd4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9', 'd4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9,e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', '这是一个高级研发项目，专注于云计算技术的应用。', 13.00, 1, '2025-04-03 00:00:00', '2025-04-03 00:00:00');

-- ----------------------------
-- Table structure for research_projects_levels_rules
-- ----------------------------
DROP TABLE IF EXISTS `research_projects_levels_rules`;
CREATE TABLE `research_projects_levels_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `levelName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `score` int NOT NULL,
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `level_name`(`levelName` ASC) USING BTREE,
  UNIQUE INDEX `uk_level_name`(`levelName` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of research_projects_levels_rules
-- ----------------------------
INSERT INTO `research_projects_levels_rules` VALUES ('400089c7-1909-11f0-b4f1-00163e035a15', '国家级重大项目（国家基金重大项目、杰出青年基金、教育部长江学者、海外长江、万人计划领军人才）', 251, '2025-04-14 16:19:56', '2025-04-22 09:05:38', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '国家级重大项目');
INSERT INTO `research_projects_levels_rules` VALUES ('40009549-1909-11f0-b4f1-00163e035a15', '国家级重点项目、四青人才', 125, '2025-04-14 16:19:56', '2025-04-14 18:04:53', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '国家级重点项目');
INSERT INTO `research_projects_levels_rules` VALUES ('400097d0-1909-11f0-b4f1-00163e035a15', '国家级重大项目的培育项目、省部级重大项目、省级人才称号', 100, '2025-04-14 16:19:56', '2025-04-14 18:04:53', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '省部级重大项目');
INSERT INTO `research_projects_levels_rules` VALUES ('40009df8-1909-11f0-b4f1-00163e035a15', '国家级项目、省部级重点项目', 50, '2025-04-14 16:19:56', '2025-04-14 18:04:53', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '国家级项目');
INSERT INTO `research_projects_levels_rules` VALUES ('40009fa7-1909-11f0-b4f1-00163e035a15', '国家基金的小额资助项目', 38, '2025-04-14 16:19:56', '2025-04-14 18:04:53', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '小额资助项目');
INSERT INTO `research_projects_levels_rules` VALUES ('4000a295-1909-11f0-b4f1-00163e035a15', '省部级项目（省基金、科技计划项目）', 13, '2025-04-14 16:19:56', '2025-04-14 18:04:53', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '省部级项目');

-- ----------------------------
-- Table structure for resources
-- ----------------------------
DROP TABLE IF EXISTS `resources`;
CREATE TABLE `resources`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `srcName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资源名称',
  `srcType` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资源类型',
  `previewPath` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '资源预览路径',
  `downloadPath` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '资源预览路径',
  `deletePath` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '资源删除路径',
  `userId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用户ID',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `srcSize` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '资源大小',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `userId`(`userId` ASC) USING BTREE,
  CONSTRAINT `resources_ibfk_1` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of resources
-- ----------------------------

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `roleName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `roleAuth` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色标识',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色备注',
  `status` int NULL DEFAULT 1 COMMENT '状态: 1-启用, 0-禁用',
  `createdAt` datetime NOT NULL COMMENT '创建时间',
  `updatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES ('305b70fe-cc8a-4126-b865-e98edd7dbff0', '教师', 'TEACHER-LV1', 'LV1', 1, '2025-04-25 15:42:24', '2025-04-25 15:42:24');
INSERT INTO `role` VALUES ('47914c7b-0fa5-485c-bc44-b5d571e1e89e', '超级管理员', 'SUPER', NULL, 1, '2023-11-17 08:13:30', '2023-11-18 15:53:27');
INSERT INTO `role` VALUES ('f222bed7-fcc6-4d42-b6ad-885caf4232fc', '访客', 'VISITOR-ADMIN', '访客权限', 1, '2023-11-18 15:54:04', '2023-11-23 15:44:43');

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `roleId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
  `permissionsId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限ID',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `roleId`(`roleId` ASC) USING BTREE,
  INDEX `permissionsId`(`permissionsId` ASC) USING BTREE,
  CONSTRAINT `role_permissions_ibfk_13` FOREIGN KEY (`roleId`) REFERENCES `role` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `role_permissions_ibfk_14` FOREIGN KEY (`permissionsId`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_permissions
-- ----------------------------
INSERT INTO `role_permissions` VALUES ('0d4737fe-0882-488b-9d3f-81b18791dc24', '47914c7b-0fa5-485c-bc44-b5d571e1e89e', '0eb8799e-adb2-480c-a136-ba7664ca75d6', '2023-11-18 06:26:11', '2023-11-18 06:26:11');
INSERT INTO `role_permissions` VALUES ('57f21c87-85ec-4639-aea7-6bc4e6cbdc34', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', '4004e9c8-1264-40d2-99ab-9a2fdae7b45f', '2023-11-23 03:18:43', '2023-11-23 03:18:43');
INSERT INTO `role_permissions` VALUES ('8c4e5577-9510-425e-94f4-37d5de7d06b6', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', '5b4f5224-1414-4966-b84d-5fae06ad339f', '2023-11-23 03:18:43', '2023-11-23 03:18:43');
INSERT INTO `role_permissions` VALUES ('a2cc2faa-8cd4-439d-9909-25fe7a779bf5', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', '0782054e-30b6-41ee-a2f8-25c5a426b3b8', '2023-11-20 10:54:41', '2023-11-20 10:54:41');

-- ----------------------------
-- Table structure for score_records
-- ----------------------------
DROP TABLE IF EXISTS `score_records`;
CREATE TABLE `score_records`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `teacherId` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '教师ID',
  `year` int NOT NULL COMMENT '评分年份',
  `researchProjectScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '科研项目得分',
  `researchFundScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '科研经费得分',
  `paperScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '论文得分',
  `awardScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '获奖得分',
  `exchangeScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '国际交流得分',
  `serviceScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '社会服务得分',
  `employmentScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '就业质量得分',
  `deductionScore` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '扣分得分',
  `totalScore` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_teacher_year`(`teacherId` ASC, `year` ASC) USING BTREE,
  INDEX `idx_score_record_teacher_id`(`teacherId` ASC) USING BTREE,
  INDEX `idx_score_record_year`(`year` ASC) USING BTREE,
  INDEX `idx_score_record_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评分记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of score_records
-- ----------------------------

-- ----------------------------
-- Table structure for social_services
-- ----------------------------
DROP TABLE IF EXISTS `social_services`;
CREATE TABLE `social_services`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务类型',
  `target` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务对象',
  `startDate` date NOT NULL COMMENT '开始时间',
  `endDate` date NOT NULL COMMENT '结束时间',
  `userIdList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参与用户ID',
  `usernameList` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参与用户名称\r\n',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '服务内容',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '服务成果',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '得分',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_social_service_type`(`type` ASC) USING BTREE,
  INDEX `idx_social_service_target`(`target` ASC) USING BTREE,
  INDEX `idx_social_service_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '社会服务评分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of social_services
-- ----------------------------
INSERT INTO `social_services` VALUES ('3b4d5e6f-7g8h-9i0j-1k2l-3m4n5o6p7q8r', '创办及参与重要学术期刊1', '创办及参与重要学术期刊', '主编', '2023-07-01', '2024-06-30', 'h8i9j0k1-l2m3-n4o5-p6q7-r8s9t0a1b2c3', '郑十', '担任某学术期刊主编', '期刊发展良好', 10.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('3b4d5e6f-7g8h-9i0j-1k2l-3m4n5o6p7q8s', '创办及参与重要学术期刊2', '创办及参与重要学术期刊', '副主编', '2023-07-01', '2024-06-30', 'i9j0k1l2-m3n4-o5p6-q7r8-s9t0a1b2c3d4', '王一', '担任某学术期刊副主编', '期刊发展良好', 5.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('3b4d5e6f-7g8h-9i0j-1k2l-3m4n5o6p7q8t', '创办及参与重要学术期刊3', '创办及参与重要学术期刊', '编委', '2023-07-01', '2024-06-30', 'j0k1l2m3-n4o5-p6q7-r8s9-t0a1b2c3d4e5', '李二', '担任某学术期刊编委', '期刊发展良好', 1.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('4c5d6e7f-8g9h-0i1j-2k3l-4m5n6o7p8q9r', '推进科学普及1', '推进科学普及', '推进科学普及', '2023-07-01', '2024-06-30', 'k1l2m3n4-o5p6-q7r8-s9t0-a1b2c3d4e5f6', '张三', '组织科学普及活动', '活动成功', 10.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('5b62d049-a9f3-453f-885a-b93131f85790', '学术会议报告1', 'student_conference', '省部级学术会议口头报告', '2022-07-23', '2025-04-06', '张三,5f39c3-95c5-4496-80fb-7877bf715aa7', '张三,5f39c3-95c5-4496-80fb-7877bf715aa7', 'essage\":\"服务名称、类型、服务对象、开始时间和结束时间不能为空\",\"data\":null}', 'essage\":\"服务名称、类型、服务对象、开始时间和结束时间不能为空\",\"data\":null}', 16.00, 1, '2025-04-17 10:48:17', '2025-04-17 10:48:17');
INSERT INTO `social_services` VALUES ('5d6e7f8g-9h0i-1j2k-3l4m-5n6o7p8q9r0s', '担任省级以上专业学会职务1', '担任省级以上专业学会职务', '副理事长以上', '2023-07-01', '2024-06-30', 'l2m3n4o5-p6q7-r8s9-t0a1-b2c3d4e5f6g7', '李四', '担任某专业学会副理事长', '职务履行良好', 10.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('61ec01da-fd50-4b5a-a377-de346150b655', '科技成果转化', '科技成果转化', '科技成果转化', '2023-07-01', '2024-06-30', 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', '张三', '参与某项科技成果转化工作', '成功转化', 10.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('6e7f8g9h-0i1j-2k3l-4m5n-6o7p8q9r0s1t', '发挥智库作用', '发挥智库作用', '政策法规、行业标准咨询并获采纳', '2023-07-01', '2024-06-30', 'm3n4o5p6-q7r8-s9t0-a1b2-c3d4e5f6g7h8', '王五', '为政策法规提供咨询', '咨询被采纳', 10.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('7f8g9h0i-1j2k-3l4m-5n6o-7p8q9r0s1t2u', '承担研究生招生、培养及学位点建设任务1', '承担研究生招生、培养及学位点建设任务', '招生命题', '2023-07-01', '2024-06-30', 'n4o5p6q7-r8s9-t0a1-b2c3-d4e5f6g7h8i9', '赵六', '参与研究生招生命题工作', '命题工作完成', 5.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('7f8g9h0i-1j2k-3l4m-5n6o-7p8q9r0s1t2v', '承担研究生招生、培养及学位点建设任务2', '承担研究生招生、培养及学位点建设任务', '本院研究生评审', '2023-07-01', '2024-06-30', 'o5p6q7r8-s9t0-a1b2-c3d4-e5f6g7h8i9j0', '孙七', '参与本院研究生评审工作', '评审工作完成', 1.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6h', '举办或参与重要学术会议1', '举办或参与重要学术会议', '国际会议（境外）口头报告', '2023-07-01', '2024-06-30', 'b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', '李四', '在国际会议上作口头报告', '报告成功', 10.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6i', '举办或参与重要学术会议2', '举办或参与重要学术会议', '国际会议（境外）展板展示', '2023-07-01', '2024-06-30', 'c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', '王五', '在国际会议上进行展板展示', '展示成功', 5.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6j', '举办或参与重要学术会议3', '举办或参与重要学术会议', '全国性学术会议口头报告', '2023-07-01', '2024-06-30', 'd4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9', '赵六', '在全国性学术会议上作口头报告', '报告成功', 6.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6k', '举办或参与重要学术会议4', '举办或参与重要学术会议', '全国性学术会议展板展示', '2023-07-01', '2024-06-30', 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', '孙七', '在全国性学术会议上进行展板展示', '展示成功', 3.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6l', '举办或参与重要学术会议5', '举办或参与重要学术会议', '省部级学术会议口头报告', '2023-07-01', '2024-06-30', 'f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0a1', '5f39c3-95c5-4496-80fb-7877bf715aa7', '在省部级学术会议上作口头报告', '报告成功', 4.00, 1, '2025-04-13 21:19:27', '2025-04-16 10:32:10');
INSERT INTO `social_services` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6m', '举办或参与重要学术会议6', '举办或参与重要学术会议', '省部级学术会议展板展示', '2023-07-01', '2024-06-30', 'g7h8i9j0-k1l2-m3n4-o5p6-q7r8s9t0a1b2', '吴九', '在省部级学术会议上进行展板展示', '展示成功', 2.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3v', '指导研究生参加学术创新、实验创新竞赛1', '指导研究生参加学术创新、实验创新竞赛', '国家级', '2023-07-01', '2024-06-30', 'p6q7r8s9-t0a1-b2c3-d4e5-f6g7h8i9j0k1', '周八', '指导研究生参加国家级竞赛', '获得奖项', 10.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3w', '指导研究生参加学术创新、实验创新竞赛2', '指导研究生参加学术创新、实验创新竞赛', '省级', '2023-07-01', '2024-06-30', 'q7r8s9t0-a1b2-c3d4-e5f6-g7h8i9j0k1l2', '吴九', '指导研究生参加省级竞赛', '获得奖项', 6.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('9d8c12a2-faed-40b5-a0ee-cfabc9cdef28', '测试项目', 'journal', '国际会议（境外）口头报告', '2025-04-01', '2025-05-29', '张三,5f39c3-95c5-4496-80fb-7877bf715aa7', '张三,5f39c3-95c5-4496-80fb-7877bf715aa7', 'essage\":\"服务名称、类型、服务对象、开始时间和结束时间不能为空\",\"data\":null}', 'essage\":\"服务名称、类型、服务对象、开始时间和结束时间不能为空\",\"data\":null}', 20.00, 1, '2025-04-17 10:36:47', '2025-04-17 10:36:47');
INSERT INTO `social_services` VALUES ('9dfe2aeb-fbe4-464a-9877-8d08d1ae3e94', '测试项目', 'journal', '国际会议（境外）口头报告', '2025-04-01', '2025-05-31', '张三,5f39c3-95c5-4496-80fb-7877bf715aa7', '张三,5f39c3-95c5-4496-80fb-7877bf715aa7', '是打的期望', '的撒带娃去W', 0.00, 1, '2025-04-17 10:18:50', '2025-04-17 10:18:50');
INSERT INTO `social_services` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4w', '学术会议报告1', '学术会议报告（研究生）', '国际会议（境外）口头报告', '2023-07-01', '2024-06-30', 'k1l2m3n4-o5p6-q7r8-s9t0-a1b2c3d4e5f6', '5f39c3-95c5-4496-80fb-7877bf715aa7', '在国际会议上作口头报告', '报告成功', 15.00, 1, '2025-04-13 21:19:27', '2025-04-16 10:22:44');
INSERT INTO `social_services` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4x', '学术会议报告2', '学术会议报告（研究生）', '国际会议（境外）展板展示', '2023-07-01', '2024-06-30', 'l2m3n4o5-p6q7-r8s9-t0a1-b2c3d4e5f6g7', '李四', '在国际会议上进行展板展示', '展示成功', 8.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4y', '学术会议报告3', '学术会议报告（研究生）', '全国性学术会议（二级协会及以上）口头报告', '2023-07-01', '2024-06-30', 'm3n4o5p6-q7r8-s9t0-a1b2-c3d4e5f6g7h8', '王五', '在全国性学术会议上作口头报告', '报告成功', 9.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4z', '学术会议报告4', '学术会议报告（研究生）', '全国性学术会议（二级协会及以上）展板展示', '2023-07-01', '2024-06-30', 'n4o5p6q7-r8s9-t0a1-b2c3-d4e5f6g7h8i9', '赵六', '在全国性学术会议上进行展板展示', '展示成功', 5.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v5w', '学术会议报告5', '学术会议报告（研究生）', '省部级学术会议（一级协会）口头报告', '2023-07-01', '2024-06-30', 'o5p6q7r8-s9t0-a1b2-c3d4-e5f6g7h8i9j0', '孙七', '在省部级学术会议上作口头报告', '报告成功', 6.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v5x', '学术会议报告6', '学术会议报告（研究生）', '省部级学术会议（一级协会）展板展示', '2023-07-01', '2024-06-30', 'f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0a1', '周八', '在省部级学术会议上进行展板展示', '展示成功', 3.00, 1, '2025-04-13 21:19:27', '2025-04-13 21:19:27');
INSERT INTO `social_services` VALUES ('f9c02208-d3c7-4285-a643-63360492db1d', '测试项目', 'conference', '国际会议（境外）口头报告', '2025-04-03', '2025-05-01', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '张三,5f39c3-95c5-4496-80fb-7877bf715aa7', 's', 's', 0.00, 1, '2025-04-16 10:21:12', '2025-04-16 10:21:12');

-- ----------------------------
-- Table structure for social_services_rules
-- ----------------------------
DROP TABLE IF EXISTS `social_services_rules`;
CREATE TABLE `social_services_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '唯一标识符，随机生成的 UUID',
  `contributionName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '贡献的主类型（如“创办及参与重要学术期刊”）',
  `secondaryOption` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '贡献的二级选择（如“主编”、“副主编”、“编委”等）',
  `score` int NOT NULL COMMENT '该二级选择对应的得分',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（与 id 一致的 UUID）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_contribution_secondary`(`contributionName` ASC, `secondaryOption` ASC) USING BTREE COMMENT '确保贡献类型和二级选择的唯一性'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '社会服务核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of social_services_rules
-- ----------------------------
INSERT INTO `social_services_rules` VALUES ('3b4d5e6f-7g8h-9i0j-1k2l-3m4n5o6p7q8r', '创办及参与重要学术期刊', '主编', 10, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('3b4d5e6f-7g8h-9i0j-1k2l-3m4n5o6p7q8s', '创办及参与重要学术期刊', '副主编', 5, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('3b4d5e6f-7g8h-9i0j-1k2l-3m4n5o6p7q8t', '创办及参与重要学术期刊', '编委', 1, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('4c5d6e7f-8g9h-0i1j-2k3l-4m5n6o7p8q9r', '推进科学普及', '推进科学普及', 10, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('5d6e7f8g-9h0i-1j2k-3l4m-5n6o7p8q9r0s', '担任省级以上专业学会职务', '副理事长以上', 10, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('61ec01da-fd50-4b5a-a377-de346150b655', '科技成果转化', '科技成果转化', 10, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('6e7f8g9h-0i1j-2k3l-4m5n-6o7p8q9r0s1t', '发挥智库作用', '政策法规、行业标准咨询并获采纳', 10, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('7f8g9h0i-1j2k-3l4m-5n6o-7p8q9r0s1t2u', '承担研究生招生、培养及学位点建设任务', '招生命题', 5, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('7f8g9h0i-1j2k-3l4m-5n6o-7p8q9r0s1t2v', '承担研究生招生、培养及学位点建设任务', '本院研究生评审', 1, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6h', '举办或参与重要学术会议', '国际会议（境外）口头报告', 10, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6i', '举办或参与重要学术会议', '国际会议（境外）展板展示', 5, '2025-04-14 20:14:27', '2025-04-14 20:14:46', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `social_services_rules` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6j', '举办或参与重要学术会议', '全国性学术会议口头报告', 6, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6j');
INSERT INTO `social_services_rules` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6k', '举办或参与重要学术会议', '全国性学术会议展板展示', 3, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6k');
INSERT INTO `social_services_rules` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6l', '举办或参与重要学术会议', '省部级学术会议口头报告', 4, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6l');
INSERT INTO `social_services_rules` VALUES ('8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6m', '举办或参与重要学术会议', '省部级学术会议展板展示', 2, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '8a3c9f4b-7d2e-4c5f-9b8a-1f2d3e4f5g6m');
INSERT INTO `social_services_rules` VALUES ('8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3v', '指导研究生参加学术创新、实验创新竞赛', '国家级', 10, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3v');
INSERT INTO `social_services_rules` VALUES ('8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3w', '指导研究生参加学术创新、实验创新竞赛', '省级', 6, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3w');
INSERT INTO `social_services_rules` VALUES ('8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3x', '指导研究生参加学术创新、实验创新竞赛', '校院级', 4, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '8g9h0i1j-2k3l-4m5n-6o7p-8q9r0s1t2u3x');
INSERT INTO `social_services_rules` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4w', '学术会议报告（研究生）', '国际会议（境外）口头报告', 15, '2025-04-14 20:14:27', '2025-04-15 15:41:58', '9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4w');
INSERT INTO `social_services_rules` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4x', '学术会议报告（研究生）', '国际会议（境外）展板展示', 8, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4x');
INSERT INTO `social_services_rules` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4y', '学术会议报告（研究生）', '全国性学术会议（二级协会及以上）口头报告', 9, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4y');
INSERT INTO `social_services_rules` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4z', '学术会议报告（研究生）', '全国性学术会议（二级协会及以上）展板展示', 5, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v4z');
INSERT INTO `social_services_rules` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v5w', '学术会议报告（研究生）', '省部级学术会议（一级协会）口头报告', 6, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v5w');
INSERT INTO `social_services_rules` VALUES ('9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v5x', '学术会议报告（研究生）', '省部级学术会议（一级协会）展板展示', 3, '2025-04-14 20:14:27', '2025-04-14 20:14:27', '9h0i1j2k-3l4m-5n6o-7p8q-9r0s1t2u3v5x');

-- ----------------------------
-- Table structure for teacher
-- ----------------------------
DROP TABLE IF EXISTS `teacher`;
CREATE TABLE `teacher`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '教师姓名',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职称',
  `department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属院系',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(1:在职 0:离职)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of teacher
-- ----------------------------

-- ----------------------------
-- Table structure for textbook_publishing_rules
-- ----------------------------
DROP TABLE IF EXISTS `textbook_publishing_rules`;
CREATE TABLE `textbook_publishing_rules`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键，使用 UUID 唯一标识每条记录',
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类别（如国家级规划教材、国家级课程等）',
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色（如主编、副主编、编委等）',
  `score` decimal(10, 2) NOT NULL COMMENT '核算分数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '类别的详细描述（如国家级规划教材、国家级课程等）',
  `additionalInfo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '额外信息（如需提供转让合同或应用证明等）',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `createdBy` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者 ID（userId）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_role`(`category` ASC, `role` ASC) USING BTREE COMMENT '确保类别和角色的唯一性'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '学术成果核算规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of textbook_publishing_rules
-- ----------------------------
INSERT INTO `textbook_publishing_rules` VALUES ('b10cb96f-1916-11f0-b4f1-00163e035a15', '出版的国家级规划教材', '主编', 100.00, '国家级规划教材（高等教育本科及以上）', NULL, '2025-04-14 17:56:09', '2025-04-15 17:00:35', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10cbe89-1916-11f0-b4f1-00163e035a15', '出版的国家级规划教材', '副主编', 50.00, '国家级规划教材（高等教育本科及以上）', NULL, '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10cc19e-1916-11f0-b4f1-00163e035a15', '出版的国家级规划教材', '编委', 20.00, '国家级规划教材（高等教育本科及以上）', NULL, '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10cc301-1916-11f0-b4f1-00163e035a15', '出版非国家级规划教材', '主编', 30.00, '非国家级规划教材', NULL, '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10cc446-1916-11f0-b4f1-00163e035a15', '出版非国家级规划教材', '副主编', 15.00, '非国家级规划教材', NULL, '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10cc5ac-1916-11f0-b4f1-00163e035a15', '出版非国家级规划教材', '编委', 5.00, '非国家级规划教材', NULL, '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10cc747-1916-11f0-b4f1-00163e035a15', '国家级课程及国家级虚拟仿真实验教学项目', '课程第一负责人', 100.00, '国家级课程（教育部发文为准）', '由课程第一负责人分配分数', '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10cc8c3-1916-11f0-b4f1-00163e035a15', '省部级课程及省级虚拟仿真实验教学项目', '课程第一负责人', 50.00, '省部级课程（省教育厅发文为准）', '由课程第一负责人分配分数', '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10ccad6-1916-11f0-b4f1-00163e035a15', '获得授权并已转化或应用的发明专利', '排名第一完成人', 50.00, '发明专利（需提供转让合同或应用证明等）', NULL, '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10ccc1d-1916-11f0-b4f1-00163e035a15', '获批的一类新药', '排名第一完成人', 400.00, '一类新药（需提供新药证书国药准字）', NULL, '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `textbook_publishing_rules` VALUES ('b10ccd70-1916-11f0-b4f1-00163e035a15', '其他获批的新药', '排名第一完成人', 50.00, '其他新药（需提供新药证书国药准字）', NULL, '2025-04-14 17:56:09', '2025-04-14 18:05:00', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');

-- ----------------------------
-- Table structure for time_interval
-- ----------------------------
DROP TABLE IF EXISTS `time_interval`;
CREATE TABLE `time_interval`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `nameC` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `startTime` date NOT NULL,
  `endTime` date NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `createBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of time_interval
-- ----------------------------
INSERT INTO `time_interval` VALUES ('550e8400-e29b-41d4-a716-446655440000', 'awards', 'awards', 'D获奖情况', '2024-07-01', '2025-06-30', '教学与科研获奖统计时间硕/博导截止到本年度 6 月 30 日。', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `time_interval` VALUES ('550e8400-e29b-41d4-a716-446655440001', 'deductions', 'deductions', 'G扣分', '2023-06-01', '2023-06-30', '在最高培养年限规定（硕士 5 年、博士 7 年）内\r\n未获得学位或未就业的内招研究生情况。', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `time_interval` VALUES ('550e8400-e29b-41d4-a716-446655440002', 'high_level_papers', 'highLevelPapers', 'C高水平论文', '2023-03-01', '2023-03-15', '高水平论文。硕/博统计上两年度 7 月 1 日-本年度\r\n6 月 30 日之间基础医学与公共卫生学院学籍的研究生为第一作\r\n者、导师为通讯作者的文章，如第一作者非我院研究生的文章乘\r\n以系数 0.9，每个导师最多填写 5 篇代表性论文，非暨南大学基\r\n础医学与公共卫生学院第一作者单位仅限一篇，其他文章第一作\r\n者单位必须是暨南大学基础医学与公共卫生学院（School of \r\nMedicine），公开发表的研究论文，以中科院 JCR 分区为准。各\r\n级别论文核算分数如下，如遇共同第一作者，本院研究生排名第\r\n一按发表期刊类别对应分值的 100%分配，本院研究生排名第二\r\n按发表期刊类别对应分值的 1/2 分配；本院研究生排名第三按发\r\n表期刊类别对应分值的 1/3 分配；以此类推。引进人才到校 5 年\r\n内可计算导师本人为第一或本人为通讯作者，每个导师最多填写\r\n5 篇代表性论文，非暨南大学基础医学与公共卫生学院第一作者\r\n单位仅限一篇，其他文章第一作者单位必须是暨南大学基础医学\r\n与公共卫生学院（School of Medicine）的文章。', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `time_interval` VALUES ('550e8400-e29b-41d4-a716-446655440003', 'international_exchanges', 'internationalExchanges', 'E国际交流', '2023-08-15', '2023-08-30', '硕/博导统计上年度 7 月 1 日-本年度 6 月 30 日之\r\n间研究生的国际交流与合作评价包括赴境外（含港澳台）学习交\r\n流连续超过 90 天的本院研究生数量、和来华学习交流超过 90\r\n天的境外研究生（含授予学位研究生）数量，由学院研究生招生\r\n工作领导小组或组织同行专家进行评价。', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `time_interval` VALUES ('550e8400-e29b-41d4-a716-446655440004', 'research_funds', 'researchFunds', 'B科研经费', '2023-01-01', '2024-12-31', '科研经费。硕/博导统计上年度 7 月 1 日-本年度 6\r\n月 30 日之间，导师主持的科研课题实际到账经费数（包括纵向\r\n与横向经费）', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `time_interval` VALUES ('550e8400-e29b-41d4-a716-446655440005', 'research_projects', 'researchProjects', 'A科研项目', '2024-07-01', '2025-06-30', '基础医学硕/博和预防医学硕导统计上\r\n年度 7 月 1 日-本年度 6 月 30 日之间导师主持的在研省部级及\r\n以上课题。应结题而未结题的不予统计。', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `time_interval` VALUES ('550e8400-e29b-41d4-a716-446655440006', 'social_services', 'socialServices', 'F社会服务', '2024-07-01', '2025-06-30', '导师在社会服务和学科建设方面的特色和贡献主要\r\n包括硕/博导统计上年度 7 月 1 日-本年度 6 月 30 日之间在科\r\n7\r\n技成果转化、举办或参与重要学术会议。研究生参加学术会议做报告情况。硕/博导统计\r\n上年度 7 月 1 日-本年度 6 月 30 日之间本院研究生在国际，国\r\n家和省级重要学术会议（由正规学术机构组织）上作口头报告和\r\n展板展示的分数（分级评分 1-5 分，15 分封顶）', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');
INSERT INTO `time_interval` VALUES ('550e8400-e29b-41d4-a716-446655440007', 'textbook_publishing', 'textbookPublishing', 'D出版书本', '2023-04-01', '2023-05-30', '近四年出版的国家级规划教材（高等教育本科及以\r\n上）、获批的各类国家级课程、专利转化及新药研制。统计时间\r\n硕/博导截止到本年度 6 月 30 日。', 'f35f39c3-95c5-4496-80fb-7877bf715aa7');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `roleId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色ID',
  `avatar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户头像',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'Vchs0bbdk2pr/Ac6DsHruw==' COMMENT '密码',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'John Doe' COMMENT '昵称',
  `department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属院系',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '状态',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES ('61ec01da-fd50-4b5a-a377-de346150b655', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'test', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '书中枫叶', NULL, '1', NULL, '2023-11-23 03:21:23', '2023-11-23 03:26:16');
INSERT INTO `user` VALUES ('88ec01da-fd50-4b5a-a377-de346150b666', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '王老师', NULL, '1', NULL, '2025-04-13 11:20:37', '2025-04-13 11:20:40');
INSERT INTO `user` VALUES ('a1b2c3-d4e5-f6g7-h8i9-j0k1l2m3n4o5', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_021', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '赵六', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_001', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '张三', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_022', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '孙七', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_002', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '李四', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_023', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '周八', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_003', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '王五', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_024', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '吴九', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_004', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '赵六', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_005', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '孙七', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('f35f39c3-95c5-4496-80fb-7877bf715aa7', '47914c7b-0fa5-485c-bc44-b5d571e1e89e', 'data:image/jpeg;base64,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', 'admin', 'Vchs0bbdk2pr/Ac6DsHruw==', '<EMAIL>', 'John Doe', NULL, '1', '超级管理员', '2023-11-17 08:01:35', '2023-11-23 15:45:48');
INSERT INTO `user` VALUES ('f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0a1', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_006', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '周八', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('g7h8i9j0-k1l2-m3n4-o5p6-q7r8s9t0a1b2', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_007', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '吴九', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('h8i9j0k1-l2m3-n4o5-p6q7-r8s9t0a1b2c3', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_008', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '郑十', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('i9j0k1l2-m3n4-o5p6-q7r8-s9t0a1b2c3d4', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_009', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '王一', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('j0k1l2m3-n4o5-p6q7-r8s9-t0a1b2c3d4e5', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_010', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '李二', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('k1l2m3n4-o5p6-q7r8-s9t0-a1b2c3d4e5f6', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_011', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '张三', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('l2m3n4o5-p6q7-r8s9-t0a1-b2c3d4e5f6g7', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_012', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '李四', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('m3n4o5p6-q7r8-s9t0-a1b2-c3d4e5f6g7h8', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_013', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '王五', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('n4o5p6q7-r8s9-t0a1-b2c3-d4e5f6g7h8i9', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_014', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '赵六', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('o5p6q7r8-s9t0-a1b2-c3d4-e5f6g7h8i9j0', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_015', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '孙七', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('p6q7r8s9-t0a1-b2c3-d4e5-f6g7h8i9j0k1', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_016', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '周八', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('q7r8s9t0-a1b2-c3d4-e5f6-g7h8i9j0k1l2', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_017', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '吴九', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('r8s9t0a1-b2c3-d4e5-f6g7-h8i9j0k1l2m3', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_018', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '郑十', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('s9t0a1-b2c3-d4e5-f6g7-h8i9j0k1l2m3', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_019', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '李四', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');
INSERT INTO `user` VALUES ('t0a1b2-c3d4-e5f6-g7h8-i9j0k1l2m3n4', 'f222bed7-fcc6-4d42-b6ad-885caf4232fc', 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100', 'teacher_020', 'Vchs0bbdk2pr/Ac6DsHruw==', NULL, '王五', NULL, '1', NULL, '2025-04-13 21:26:01', '2025-04-13 21:26:01');

-- ----------------------------
-- Table structure for user_opt_log
-- ----------------------------
DROP TABLE IF EXISTS `user_opt_log`;
CREATE TABLE `user_opt_log`  (
  `id` char(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `operator` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `operatorId` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作人ID',
  `module` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作模块',
  `platform` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作平台',
  `operatorIP` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '设备IP',
  `latitude` double NULL DEFAULT NULL COMMENT '纬度',
  `longitude` double NULL DEFAULT NULL COMMENT '经度',
  `address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '设备位置',
  `content` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '操作内容',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_opt_log
-- ----------------------------
INSERT INTO `user_opt_log` VALUES ('018b7d93-d36d-45c1-b244-aa554404c677', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\",\"remember\":true},\"responseStatus\":200,\"success\":true,\"duration\":469,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 14:06:18', '2025-04-09 14:06:18');
INSERT INTO `user_opt_log` VALUES ('02438910-df9d-4960-92ca-631434dc3653', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":500,\"success\":false,\"duration\":19,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":{}}\"}', '2025-04-15 09:41:43', '2025-04-15 09:41:43');
INSERT INTO `user_opt_log` VALUES ('034048c0-3316-4a56-a912-d705000b2816', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:14:06', '2023-11-18 07:14:06');
INSERT INTO `user_opt_log` VALUES ('0465d6ea-3e81-47ed-85d9-4dc478207d4b', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":10,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:06:22', '2025-04-15 10:06:22');
INSERT INTO `user_opt_log` VALUES ('059f2407-9904-4142-9340-8230fed629b5', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:23:32', '2023-11-18 07:23:32');
INSERT INTO `user_opt_log` VALUES ('06e0db57-62b8-4eef-a870-9aa22b4b0d85', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::ffff:127.0.0.1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":2,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-13 22:38:07', '2025-04-13 22:38:07');
INSERT INTO `user_opt_log` VALUES ('073f0405-6027-48cb-9017-a0c42bfb0285', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":6,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:58:23', '2025-04-15 09:58:23');
INSERT INTO `user_opt_log` VALUES ('09b5299b-9266-4132-b7b8-f4f379c42ee9', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":3,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:48:21', '2025-04-15 08:48:21');
INSERT INTO `user_opt_log` VALUES ('0a6ba4dd-e744-4577-89f5-e54a69872596', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":414,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 10:50:31', '2025-04-09 10:50:31');
INSERT INTO `user_opt_log` VALUES ('0befe29d-b789-447c-b35f-4a192e96d46b', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":446,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 10:21:02', '2025-04-15 10:21:02');
INSERT INTO `user_opt_log` VALUES ('0c429516-7c77-4af8-a6c3-20f49abd7726', 'John Doe', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', NULL, '未知', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/permissions', '2023-11-18 05:35:35', '2023-11-18 05:35:35');
INSERT INTO `user_opt_log` VALUES ('0d46a106-f017-4b18-b786-c5b118c2e7be', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:59:14', '2023-11-23 03:59:14');
INSERT INTO `user_opt_log` VALUES ('0db038a1-160a-44cf-88ab-d4c1540d3aad', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":9,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:04:13', '2025-04-15 10:04:13');
INSERT INTO `user_opt_log` VALUES ('0db7dfdc-f4c8-41dc-8783-fbbb478f2573', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":3,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:03:36', '2025-04-15 09:03:36');
INSERT INTO `user_opt_log` VALUES ('0e36d47e-3da7-46db-a683-e28da8a047e9', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":31,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-24 15:23:12', '2025-04-24 15:23:12');
INSERT INTO `user_opt_log` VALUES ('0e960126-bc0f-44c1-85ce-d8ec96fd3f0d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":5,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:33:08', '2025-04-15 09:33:08');
INSERT INTO `user_opt_log` VALUES ('0ea259f3-2a84-4aa2-8f1e-d915fb228c75', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:55:36', '2023-11-23 03:55:36');
INSERT INTO `user_opt_log` VALUES ('0fc10601-d308-4dfa-94ad-78457f1c8e04', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":222,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-18 09:48:11', '2025-04-18 09:48:11');
INSERT INTO `user_opt_log` VALUES ('1455803b-124e-49f0-aece-2e4e387a2b0e', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":413,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 10:21:55', '2025-04-15 10:21:55');
INSERT INTO `user_opt_log` VALUES ('1477e7a5-2ee0-442f-9927-db1fc00318ca', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":560,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-13 22:29:19', '2025-04-13 22:29:19');
INSERT INTO `user_opt_log` VALUES ('15bb80de-2590-4299-b442-4df38c019fc2', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":421,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 10:50:45', '2025-04-09 10:50:45');
INSERT INTO `user_opt_log` VALUES ('18a692e2-342d-4de4-ac4b-e05624761fa1', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":7,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:01:51', '2025-04-15 10:01:51');
INSERT INTO `user_opt_log` VALUES ('18cef4c5-15fe-4f37-8dbd-5855ac4a7283', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":4,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码错误\\\"}\"}', '2025-04-11 15:00:58', '2025-04-11 15:00:58');
INSERT INTO `user_opt_log` VALUES ('19320d37-ec0c-442a-a684-65173f87c9f6', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:24:57', '2023-11-18 07:24:57');
INSERT INTO `user_opt_log` VALUES ('198fddf1-3356-4073-855b-bc95dc26260e', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":375,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 15:11:59', '2025-04-11 15:11:59');
INSERT INTO `user_opt_log` VALUES ('19f44121-5cfa-4495-9e0c-dc24372276f2', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":3,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:04:02', '2025-04-15 10:04:02');
INSERT INTO `user_opt_log` VALUES ('1b22b795-faf0-4d0a-ba0b-0273aa6a3071', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":181,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-23 14:49:47', '2025-04-23 14:49:47');
INSERT INTO `user_opt_log` VALUES ('1edc503c-a00d-444c-8e18-fd331d593361', '未知', '-', '用户查询', '未知', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/user', '2023-11-17 06:55:29', '2023-11-17 06:55:29');
INSERT INTO `user_opt_log` VALUES ('1f6c7542-c700-4ebc-b0ec-7d97bcbef326', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:54:52', '2023-11-23 03:54:52');
INSERT INTO `user_opt_log` VALUES ('2219c050-76a5-431b-865c-1e9ae08743d2', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:25:14', '2023-11-18 07:25:14');
INSERT INTO `user_opt_log` VALUES ('22a316fa-067d-4f20-a44a-a88af1dfb173', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":380,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 10:13:54', '2025-04-24 10:13:54');
INSERT INTO `user_opt_log` VALUES ('24c5e7f1-cd11-4692-bf50-d5bfae4e4602', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":605,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 07:06:00', '2025-04-11 07:06:00');
INSERT INTO `user_opt_log` VALUES ('24cf0553-2390-497c-b10f-7ac833cbc227', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":100,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 15:23:19', '2025-04-24 15:23:19');
INSERT INTO `user_opt_log` VALUES ('254dfae9-547c-496f-980d-0abc549bb80a', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":7,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:03:37', '2025-04-15 10:03:37');
INSERT INTO `user_opt_log` VALUES ('28fd4e00-b66f-43fb-b514-d821003abb97', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":6,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:05:26', '2025-04-15 10:05:26');
INSERT INTO `user_opt_log` VALUES ('2956ce2d-f559-4452-a60d-b6420b91171f', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":2,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:41:37', '2025-04-15 08:41:37');
INSERT INTO `user_opt_log` VALUES ('2995ab30-adf6-4f09-8c44-123025220c49', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":417,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 17:05:22', '2025-04-09 17:05:22');
INSERT INTO `user_opt_log` VALUES ('2c4c6574-9030-4d39-8c41-27a526438c4d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":21,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-24 15:04:24', '2025-04-24 15:04:24');
INSERT INTO `user_opt_log` VALUES ('2cc0549f-f597-4824-b0a4-6cb7d9b8ac0c', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":11,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:57:23', '2025-04-15 08:57:23');
INSERT INTO `user_opt_log` VALUES ('2fdb0a7a-f622-49e1-ac46-aa30622615c9', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":92,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 15:06:16', '2025-04-24 15:06:16');
INSERT INTO `user_opt_log` VALUES ('321c0335-3daf-4f44-9671-5100d5a2d07a', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":484,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 13:53:32', '2025-04-09 13:53:32');
INSERT INTO `user_opt_log` VALUES ('33513c4f-eb43-4571-8fee-3bcfb1132ad3', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":10,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:34:54', '2025-04-15 09:34:54');
INSERT INTO `user_opt_log` VALUES ('33eb3189-992e-4fb3-805e-3cbfdb24bb43', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\",\"remember\":true},\"responseStatus\":400,\"success\":false,\"duration\":2,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:41:14', '2025-04-15 08:41:14');
INSERT INTO `user_opt_log` VALUES ('344b7ce9-fe9f-4a2d-8a1f-c94bb676718f', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-20 10:54:04', '2023-11-20 10:54:04');
INSERT INTO `user_opt_log` VALUES ('359045e6-f720-4981-a4cb-e43685f86aba', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":8,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:04:27', '2025-04-15 10:04:27');
INSERT INTO `user_opt_log` VALUES ('37103707-0fb3-4aa4-ab9c-78df548da4a7', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":432,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 10:29:04', '2025-04-15 10:29:04');
INSERT INTO `user_opt_log` VALUES ('3977341e-d672-4892-b18d-4b7b44621540', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:55:32', '2023-11-23 03:55:32');
INSERT INTO `user_opt_log` VALUES ('3ada737e-2e2e-460d-b5cb-5ae79a9dac7d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":5,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:06:54', '2025-04-15 10:06:54');
INSERT INTO `user_opt_log` VALUES ('3c2d6ab6-3b6b-4f47-b9a4-0762fc569e8a', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":106,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 09:10:28', '2025-04-15 09:10:28');
INSERT INTO `user_opt_log` VALUES ('3dd539ff-bd03-4104-8479-ed29c04bd294', '未知', '-', '用户查询', '未知', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/user', '2023-11-17 08:01:30', '2023-11-17 08:01:30');
INSERT INTO `user_opt_log` VALUES ('3e65b8c9-fa21-4010-8192-5a3d27f097a4', '未知', '-', '用户查询', '未知', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/user', '2023-11-17 08:01:45', '2023-11-17 08:01:45');
INSERT INTO `user_opt_log` VALUES ('4291f52b-f397-4c70-b068-004d29960188', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":109,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-21 14:29:01', '2025-04-21 14:29:01');
INSERT INTO `user_opt_log` VALUES ('4691388b-de4e-4b06-ae0c-3b00af299e60', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:21:57', '2023-11-23 03:21:57');
INSERT INTO `user_opt_log` VALUES ('475032c4-3e20-49b7-90c6-502ff79bd5a6', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-21 03:24:23', '2023-11-21 03:24:23');
INSERT INTO `user_opt_log` VALUES ('4a8d9345-9950-4939-b715-229f81de388c', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-20 10:53:55', '2023-11-20 10:53:55');
INSERT INTO `user_opt_log` VALUES ('4adc3fed-cdcd-44d1-a691-dc5c275e90d9', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":386,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 10:49:08', '2025-04-09 10:49:08');
INSERT INTO `user_opt_log` VALUES ('4b7bccd7-2ed0-4e94-be49-05d501250eeb', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":14,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:09:31', '2025-04-15 10:09:31');
INSERT INTO `user_opt_log` VALUES ('4bde31d2-848d-405c-8e3a-98cd6d563721', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\",\"remember\":true},\"responseStatus\":200,\"success\":true,\"duration\":154,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-22 09:00:43', '2025-04-22 09:00:43');
INSERT INTO `user_opt_log` VALUES ('4c09ef74-8a45-422a-abd8-afe8058c5806', 'John Doe', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '权限管理', '未知', '0.0.0.0', 0, 0, '保留地址', '获取所有权限管理', '2023-11-18 05:48:56', '2023-11-18 05:48:56');
INSERT INTO `user_opt_log` VALUES ('4c53a8e9-ca18-4cf6-aac2-f49abd9f0be8', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":500,\"success\":false,\"duration\":7,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":{}}\"}', '2025-04-15 09:41:53', '2025-04-15 09:41:53');
INSERT INTO `user_opt_log` VALUES ('4cb94b1f-9d9c-4496-b942-808c3c1197c0', '未知', '-', '用户查询', '未知', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/user', '2023-11-17 07:55:44', '2023-11-17 07:55:44');
INSERT INTO `user_opt_log` VALUES ('4d06e791-c4af-4463-8cd7-e3c4a8170d76', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":392,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 10:46:06', '2025-04-09 10:46:06');
INSERT INTO `user_opt_log` VALUES ('4d539fce-e8be-49a5-a33b-2052770cc5e6', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\",\"remember\":true},\"responseStatus\":200,\"success\":true,\"duration\":113,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-14 14:43:44', '2025-04-14 14:43:44');
INSERT INTO `user_opt_log` VALUES ('4ff482c4-7e0f-483a-ad68-1add36c24b6d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":3,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码错误\\\"}\"}', '2025-04-11 14:58:15', '2025-04-11 14:58:15');
INSERT INTO `user_opt_log` VALUES ('5207c859-c892-4280-9fc8-942e6e18932d', 'John Doe', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '权限管理', '未知', '0.0.0.0', 0, 0, '保留地址', '获取所有权限管理', '2023-11-18 05:39:30', '2023-11-18 05:39:30');
INSERT INTO `user_opt_log` VALUES ('52623308-9bbc-4296-b639-e3b869123db2', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":1,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:41:47', '2025-04-15 08:41:47');
INSERT INTO `user_opt_log` VALUES ('533f1038-7015-409a-a7e6-b48d6d2ce106', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":15,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-09 10:45:32', '2025-04-09 10:45:32');
INSERT INTO `user_opt_log` VALUES ('53f2496f-724d-4d35-a05f-b2929c9a0050', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":7,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:06:54', '2025-04-15 10:06:54');
INSERT INTO `user_opt_log` VALUES ('563da652-605b-4b0e-9730-5f9aa764cef7', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":74,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 09:19:08', '2025-04-15 09:19:08');
INSERT INTO `user_opt_log` VALUES ('5647d012-6bd4-444d-aa26-6d16c2aced6f', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":12,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:55:27', '2025-04-15 08:55:27');
INSERT INTO `user_opt_log` VALUES ('57f5a94e-16b0-41e5-a447-46cafd95068e', 'John Doe', 'df29278b-6a85-43fb-b969-0f6b589c5628', '权限管理', '未知', '0.0.0.0', 0, 0, '保留地址', '获取所有权限管理', '2023-11-18 06:07:06', '2023-11-18 06:07:06');
INSERT INTO `user_opt_log` VALUES ('58ea4725-8489-4bc3-85ea-3a934e9adae9', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":422,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 14:19:30', '2025-04-09 14:19:30');
INSERT INTO `user_opt_log` VALUES ('59130242-222b-4442-99bd-93ab2d73bc19', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":22,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码错误\\\"}\"}', '2025-04-10 08:10:01', '2025-04-10 08:10:01');
INSERT INTO `user_opt_log` VALUES ('5ac00e95-c227-42a7-8bab-eb02954d39f6', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:22:44', '2023-11-23 03:22:44');
INSERT INTO `user_opt_log` VALUES ('5ad0f681-28c9-4847-b8cf-12031fb7a8fe', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":449,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 16:12:37', '2025-04-09 16:12:37');
INSERT INTO `user_opt_log` VALUES ('5c4b41fa-cc97-4f88-abe9-e7c8a94c5ab0', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":394,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 15:06:45', '2025-04-24 15:06:45');
INSERT INTO `user_opt_log` VALUES ('5f4f3963-b6c0-413b-b0dc-32877f3d853a', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":83,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 09:11:53', '2025-04-15 09:11:53');
INSERT INTO `user_opt_log` VALUES ('5fa36cb0-1b0b-4d31-8bab-645fc3344181', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":4,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:32:57', '2025-04-15 09:32:57');
INSERT INTO `user_opt_log` VALUES ('6406f40a-9a1c-4303-8b3b-ba2f593a060e', '1q', NULL, '1', '1', '1', NULL, NULL, '1', '1', '2025-04-10 08:38:28', '2025-04-10 08:38:28');
INSERT INTO `user_opt_log` VALUES ('6528bc97-0c1c-4cb3-a04f-5820ed0970a5', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":93,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 15:19:47', '2025-04-11 15:19:47');
INSERT INTO `user_opt_log` VALUES ('666f0b7e-fdbb-420b-a656-b57be988a325', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":7,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:54:18', '2025-04-15 08:54:18');
INSERT INTO `user_opt_log` VALUES ('6b8b1166-e694-4374-a520-c237178ed38b', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":108,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 14:02:39', '2025-04-09 14:02:39');
INSERT INTO `user_opt_log` VALUES ('6c838d54-cc5b-4b58-b36d-3fdae2f04c92', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":37,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 09:15:50', '2025-04-15 09:15:50');
INSERT INTO `user_opt_log` VALUES ('6cf59c11-12d7-4061-b2f7-2e3431092848', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":13,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:09:41', '2025-04-15 10:09:41');
INSERT INTO `user_opt_log` VALUES ('6f623f2e-8f14-4f7e-8088-e2ee2a6159f1', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":3,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:07:58', '2025-04-15 10:07:58');
INSERT INTO `user_opt_log` VALUES ('6fb1a2fa-d4d7-406b-8b42-d178c61983f0', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":452,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-10 08:14:31', '2025-04-10 08:14:31');
INSERT INTO `user_opt_log` VALUES ('70747ca2-9c69-49c2-b9e0-206357c6296f', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":566,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-13 22:51:20', '2025-04-13 22:51:20');
INSERT INTO `user_opt_log` VALUES ('7274987b-79ee-403d-ba48-540601c4f846', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 08:25:57', '2023-11-18 08:25:57');
INSERT INTO `user_opt_log` VALUES ('73409754-f06f-4696-b5b9-c4281077d744', 'admin', '-', '登录', '未知', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 05:34:53', '2023-11-18 05:34:53');
INSERT INTO `user_opt_log` VALUES ('7561e0d5-08e9-46c2-a639-871caf36b64b', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:53:22', '2023-11-23 03:53:22');
INSERT INTO `user_opt_log` VALUES ('7a8cce03-5631-41c8-acc2-bd55b93568eb', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":500,\"success\":false,\"duration\":17,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":{}}\"}', '2025-04-15 09:53:03', '2025-04-15 09:53:03');
INSERT INTO `user_opt_log` VALUES ('7aff7a5a-ef3c-462f-8270-b9d2c666b2cc', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":5,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:03:48', '2025-04-15 10:03:48');
INSERT INTO `user_opt_log` VALUES ('7b036c06-2e3b-4f54-b4c3-43ccd1b1f63e', 'admin', '-', '登录', 'Chrome.v134', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2025-04-08 03:00:30', '2025-04-08 03:00:30');
INSERT INTO `user_opt_log` VALUES ('7c91abfa-80d8-44ba-b758-01a9da48a550', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":268,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-12 00:13:04', '2025-04-12 00:13:04');
INSERT INTO `user_opt_log` VALUES ('7c9b576f-7a63-478f-9959-28d83c04d76c', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":4,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:54:19', '2025-04-15 08:54:19');
INSERT INTO `user_opt_log` VALUES ('7cb89882-c231-42e6-8794-60ec70efff49', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":6,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:15:02', '2025-04-15 09:15:02');
INSERT INTO `user_opt_log` VALUES ('8019cc02-bd85-4569-8f92-c7cbf5fdf223', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":25,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:55:42', '2025-04-15 08:55:42');
INSERT INTO `user_opt_log` VALUES ('8089e2be-c090-4b31-871a-82ab5b0a5a33', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":500,\"success\":false,\"duration\":15,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":{}}\"}', '2025-04-15 09:52:01', '2025-04-15 09:52:01');
INSERT INTO `user_opt_log` VALUES ('829b9228-aacf-40f6-993e-b458df59749b', 'admin', '-', '登录', '未知', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 05:49:21', '2023-11-18 05:49:21');
INSERT INTO `user_opt_log` VALUES ('82aec0b7-c646-46e9-8c7b-75b6d8a4c222', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":9,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:33:05', '2025-04-15 09:33:05');
INSERT INTO `user_opt_log` VALUES ('848af30d-bc75-41ab-aaef-21c220a80ec4', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"test\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":366,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"61ec01da-fd50-4b5a-a377-de346150b655\\\",\\\"username\\\":\\\"test\\\",\\\"nickname\\\":\\\"书中枫叶\\\",\\\"roleId\\\":\\\"f222bed7-fcc6-4d42-b6ad-885caf4232fc\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-24 16:49:16', '2025-04-24 16:49:16');
INSERT INTO `user_opt_log` VALUES ('8602d1ca-3f69-47f3-9bbb-77eda554145f', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:13:54', '2023-11-18 07:13:54');
INSERT INTO `user_opt_log` VALUES ('881362e8-6e1f-48fd-871e-8c400e1887a9', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:17:41', '2023-11-18 07:17:41');
INSERT INTO `user_opt_log` VALUES ('88551b34-2d9f-4574-abba-0ee0211da3ae', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":405,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 15:15:07', '2025-04-11 15:15:07');
INSERT INTO `user_opt_log` VALUES ('88b2b5e8-bccd-49aa-83c8-7dc71f35b9fc', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":581,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-13 22:55:33', '2025-04-13 22:55:33');
INSERT INTO `user_opt_log` VALUES ('88b8ff31-8909-46a5-8870-8657e30bcc73', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":398,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 08:12:50', '2025-04-11 08:12:50');
INSERT INTO `user_opt_log` VALUES ('88eb37ca-385f-4284-98b7-73346263906d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":434,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-13 21:18:05', '2025-04-13 21:18:05');
INSERT INTO `user_opt_log` VALUES ('897a5583-cb63-4d02-bb23-53b0573433a3', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:21:48', '2023-11-23 03:21:48');
INSERT INTO `user_opt_log` VALUES ('89ac9f9d-c533-41aa-9751-f9a4d1afad05', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:34:40', '2023-11-18 07:34:40');
INSERT INTO `user_opt_log` VALUES ('8b31348e-6363-4b99-aa6b-69777c6a95c0', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\",\"remember\":true},\"responseStatus\":200,\"success\":true,\"duration\":457,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 10:46:44', '2025-04-09 10:46:44');
INSERT INTO `user_opt_log` VALUES ('8c1cff8c-2274-4f11-9f7f-e4732368b637', '未知', '-', '用户查询', '未知', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/user', '2023-11-17 06:55:40', '2023-11-17 06:55:40');
INSERT INTO `user_opt_log` VALUES ('8ebd6ece-5490-459d-9750-9ef1f6422e06', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":232,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-10 23:55:21', '2025-04-10 23:55:21');
INSERT INTO `user_opt_log` VALUES ('8f0156e6-e3f2-4297-b93a-88c4ff6b766c', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":9,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:31:22', '2025-04-15 09:31:22');
INSERT INTO `user_opt_log` VALUES ('8f2d9bba-736a-4803-8cba-97e6646e9d27', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:58:08', '2023-11-23 03:58:08');
INSERT INTO `user_opt_log` VALUES ('8f61203b-b6e5-4ef2-88fa-2128eb55d02a', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":4,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:36:25', '2025-04-15 09:36:25');
INSERT INTO `user_opt_log` VALUES ('8fb58e16-aa7e-431f-b72b-7184c7f6bf91', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":425,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 14:04:37', '2025-04-09 14:04:37');
INSERT INTO `user_opt_log` VALUES ('90197c2a-b84d-4615-97bd-f3b9d119be8f', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-20 10:53:59', '2023-11-20 10:53:59');
INSERT INTO `user_opt_log` VALUES ('902c9524-89de-471d-a053-fec1d98c395a', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":452,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 14:10:01', '2025-04-09 14:10:01');
INSERT INTO `user_opt_log` VALUES ('9032ebdf-6ac1-4e32-838b-0bc0819a1250', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":5,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:45:43', '2025-04-15 08:45:43');
INSERT INTO `user_opt_log` VALUES ('907fddd8-88ba-4c6f-9a7a-45012ee72142', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":1,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:44:33', '2025-04-15 08:44:33');
INSERT INTO `user_opt_log` VALUES ('92c2629a-1f19-4748-9b0b-e72319c980fc', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login?username=admin&password=123456&code=8\",\"requestParams\":{\"username\":\"admin\",\"password\":\"123456\",\"code\":\"7\"},\"response\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImYzNWYzOWMzLTk1YzUtNDQ5Ni04MGZiLTc4NzdiZjcxNWFhNyIsInVzZXJuYW1lIjoiYWRtaW4iLCJuaWNrbmFtZSI6IkpvaG4gRG9lIiwicm9sZUlkIjoiNDc5MTRjN2ItMGZhNS00ODVjLWJjNDQtYjVkNTcxZTFlODllIiwic3RhdHVzIjoiMSIsImlhdCI6MTc0NDE2NTc0NSwiZXhwIjoxNzQ0NDI0OTQ1fQ.migAL6I-CYUYTH51rnE24RG21c6Idx4nKH4QHuxGRcc\\\"}}\",\"status\":200,\"success\":true,\"duration\":556}', '2025-04-09 10:29:05', '2025-04-09 10:29:05');
INSERT INTO `user_opt_log` VALUES ('9308ec99-e777-4576-96ec-e9d40a5e14c3', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":16,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码错误\\\"}\"}', '2025-04-24 15:11:16', '2025-04-24 15:11:16');
INSERT INTO `user_opt_log` VALUES ('96565c4c-857d-4179-8aab-990ee92b85e4', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 04:00:39', '2023-11-23 04:00:39');
INSERT INTO `user_opt_log` VALUES ('9e5a74f6-af28-49f6-93a8-2f59b8811fa7', 'admin', '-', '登录', '未知', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 06:27:16', '2023-11-18 06:27:16');
INSERT INTO `user_opt_log` VALUES ('a0b91a37-3b37-4e6b-b40f-474c4a885f67', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":500,\"success\":false,\"duration\":15,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":{}}\"}', '2025-04-15 09:41:48', '2025-04-15 09:41:48');
INSERT INTO `user_opt_log` VALUES ('a165599e-3ab3-4cd2-9eb8-3c4524ac83c3', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 04:04:27', '2023-11-23 04:04:27');
INSERT INTO `user_opt_log` VALUES ('a206ff2a-9013-431d-bc97-be4e9b5a482f', 'John Doe', 'df29278b-6a85-43fb-b969-0f6b589c5628', '权限管理', '未知', '0.0.0.0', 0, 0, '保留地址', '获取所有权限管理', '2023-11-18 05:49:44', '2023-11-18 05:49:44');
INSERT INTO `user_opt_log` VALUES ('a2370c69-26c3-451e-b695-dd67c5100156', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":463,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 13:57:37', '2025-04-09 13:57:37');
INSERT INTO `user_opt_log` VALUES ('a62bd747-8b10-421f-95df-88175e27900f', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":6,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码错误\\\"}\"}', '2025-04-11 14:58:10', '2025-04-11 14:58:10');
INSERT INTO `user_opt_log` VALUES ('aae428b9-7fd7-4006-88f1-5a55be4c0959', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":8,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:07:51', '2025-04-15 10:07:51');
INSERT INTO `user_opt_log` VALUES ('ab4c639f-857e-41df-9d69-b2499d01087b', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":3,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:49:52', '2025-04-15 08:49:52');
INSERT INTO `user_opt_log` VALUES ('ac76950d-97f3-4306-b9aa-6d124671c46c', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 04:02:55', '2023-11-23 04:02:55');
INSERT INTO `user_opt_log` VALUES ('ae3cce36-afb4-4830-bad2-283fedeb62e2', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":433,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-22 08:16:07', '2025-04-22 08:16:07');
INSERT INTO `user_opt_log` VALUES ('af89d205-9543-4418-9711-2790403ece2e', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":14,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:13:46', '2025-04-15 10:13:46');
INSERT INTO `user_opt_log` VALUES ('aff0a4de-5a71-41ab-9623-14065b747687', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":435,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 16:13:02', '2025-04-09 16:13:02');
INSERT INTO `user_opt_log` VALUES ('b067dc2a-bc4b-4eea-8a13-b19913b898c1', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":426,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 10:45:47', '2025-04-09 10:45:47');
INSERT INTO `user_opt_log` VALUES ('b33c7413-c2f9-498d-8410-c0201978b152', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-23 15:41:34', '2023-11-23 15:41:34');
INSERT INTO `user_opt_log` VALUES ('b6027188-6a34-47f9-8f2d-a39b60d47054', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":376,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 14:55:58', '2025-04-24 14:55:58');
INSERT INTO `user_opt_log` VALUES ('b76454c5-13e8-4413-9dd5-5c293c41bd2c', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":8,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:06:01', '2025-04-15 10:06:01');
INSERT INTO `user_opt_log` VALUES ('b83de2cf-66f8-4782-8b01-8253eca29307', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":99,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 15:11:22', '2025-04-24 15:11:22');
INSERT INTO `user_opt_log` VALUES ('b9cd4b43-6cdf-415a-ac06-27efa95f4900', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":440,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-09 14:08:23', '2025-04-09 14:08:23');
INSERT INTO `user_opt_log` VALUES ('bc112671-2abc-4bb0-b2e1-629ef90bf145', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:54:11', '2023-11-23 03:54:11');
INSERT INTO `user_opt_log` VALUES ('bda02aee-4a03-4d7a-b95f-769ae81b2fe3', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":11,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:37:15', '2025-04-15 09:37:15');
INSERT INTO `user_opt_log` VALUES ('be6eec37-dcb1-4d6c-8ad7-5a51edd565bb', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":84,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 09:19:56', '2025-04-15 09:19:56');
INSERT INTO `user_opt_log` VALUES ('bea44f38-9799-4bc1-8928-870ca14d0651', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":11,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:53:35', '2025-04-15 08:53:35');
INSERT INTO `user_opt_log` VALUES ('c021febe-4ff3-46da-a141-9014928255fe', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"teacher_001\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":556,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6\\\",\\\"username\\\":\\\"teacher_001\\\",\\\"nickname\\\":\\\"张三\\\",\\\"roleId\\\":\\\"f222bed7-fcc6-4d42-b6ad-885caf4232fc\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\"}', '2025-04-13 23:03:09', '2025-04-13 23:03:09');
INSERT INTO `user_opt_log` VALUES ('c0da6845-77d8-4c39-91f8-07b0a84df87f', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":139,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-15 09:12:18', '2025-04-15 09:12:18');
INSERT INTO `user_opt_log` VALUES ('c3327d7e-3b94-4c20-a471-66cadb5f2fac', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:24:09', '2023-11-23 03:24:09');
INSERT INTO `user_opt_log` VALUES ('c507f998-d401-406c-a41a-ff2489f7a59b', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":4,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:44:17', '2025-04-15 08:44:17');
INSERT INTO `user_opt_log` VALUES ('c6170334-bd80-4a7a-bcfb-b499c4aa1b8d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":21,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:09:02', '2025-04-15 09:09:02');
INSERT INTO `user_opt_log` VALUES ('c62c97a6-e79f-483f-9a7d-381a457250b1', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\",\"remember\":true},\"responseStatus\":400,\"success\":false,\"duration\":17,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:41:05', '2025-04-15 08:41:05');
INSERT INTO `user_opt_log` VALUES ('cb246b93-0648-4361-bc8e-7661f484712e', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":444,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-10 14:42:19', '2025-04-10 14:42:19');
INSERT INTO `user_opt_log` VALUES ('cc051024-00c3-4fe9-baab-2c8206131776', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":6,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:48:36', '2025-04-15 08:48:36');
INSERT INTO `user_opt_log` VALUES ('ccf1d4d6-1b18-4a3f-8087-f0a7ffc98f5d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":10,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:32:53', '2025-04-15 09:32:53');
INSERT INTO `user_opt_log` VALUES ('cd357524-88ab-4d25-953e-7136aae89d7f', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":192,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 15:49:57', '2025-04-11 15:49:57');
INSERT INTO `user_opt_log` VALUES ('cd43eec8-f393-40bd-ab88-9f36aa718c20', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":4,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:06:47', '2025-04-15 10:06:47');
INSERT INTO `user_opt_log` VALUES ('cf5dd60e-a714-4298-ba60-2520abc34e5a', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":108,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 14:55:28', '2025-04-24 14:55:28');
INSERT INTO `user_opt_log` VALUES ('d0c97d85-8f2f-40b8-93be-9e2cee0ee97f', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":477,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"token\\\":\\\"Bearer eyJhbGciOiJIU...\\\"}}\"}', '2025-04-09 10:42:46', '2025-04-09 10:42:46');
INSERT INTO `user_opt_log` VALUES ('d1cdab3a-c581-4a99-84f0-23c33519f3e8', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":19,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码错误\\\"}\"}', '2025-04-18 09:48:02', '2025-04-18 09:48:02');
INSERT INTO `user_opt_log` VALUES ('d222966f-ecc0-446e-900d-bd18bb163711', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{},\"responseStatus\":400,\"success\":false,\"duration\":3,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"用户名不能为空.\\\"}\"}', '2025-04-13 22:29:02', '2025-04-13 22:29:02');
INSERT INTO `user_opt_log` VALUES ('d2810c3e-ba3a-4951-8110-509cb7745aae', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":385,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 15:01:19', '2025-04-11 15:01:19');
INSERT INTO `user_opt_log` VALUES ('d3ec886f-039b-441f-83af-0d59923240f7', 'admin', '-', '登录', 'Chrome.v134', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2025-04-08 03:08:48', '2025-04-08 03:08:48');
INSERT INTO `user_opt_log` VALUES ('d478febc-8d4b-4ddb-b25c-88012d65f960', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:13:37', '2023-11-18 07:13:37');
INSERT INTO `user_opt_log` VALUES ('d765a523-07df-4501-9c33-b61aac4bf2d9', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{},\"responseStatus\":400,\"success\":false,\"duration\":8,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"用户名不能为空.\\\"}\"}', '2025-04-13 22:32:18', '2025-04-13 22:32:18');
INSERT INTO `user_opt_log` VALUES ('d78b6aa4-1f2b-41dc-acc4-29c63d52c3b1', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":6,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:11:10', '2025-04-15 10:11:10');
INSERT INTO `user_opt_log` VALUES ('d933e2c7-5649-483d-915b-b309882f9b42', 'admin', '-', '登录', 'Chrome.v134', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2025-04-08 03:08:50', '2025-04-08 03:08:50');
INSERT INTO `user_opt_log` VALUES ('dc90d186-9b21-4241-a6cb-afe6a12abb54', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":374,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 15:13:29', '2025-04-11 15:13:29');
INSERT INTO `user_opt_log` VALUES ('ddc0d3e4-80df-4710-8e6b-f964c588ea4d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":9,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:45:23', '2025-04-15 08:45:23');
INSERT INTO `user_opt_log` VALUES ('e04b06f1-7a5e-448e-bba2-365a7ae4c4a8', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":6,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:02:49', '2025-04-15 09:02:49');
INSERT INTO `user_opt_log` VALUES ('e21018e2-3fc8-40a4-a454-bfca98b056b2', 'John Doe', 'df29278b-6a85-43fb-b969-0f6b589c5628', '权限管理', '未知', '0.0.0.0', 0, 0, '保留地址', '获取所有权限管理', '2023-11-18 05:58:31', '2023-11-18 05:58:31');
INSERT INTO `user_opt_log` VALUES ('e2dc815f-4e67-4cbc-ab7c-08c24eb7591c', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":413,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 16:57:49', '2025-04-24 16:57:49');
INSERT INTO `user_opt_log` VALUES ('e416327e-47f2-43fa-879a-bcbc1cf80c8f', 'John Doe', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '权限管理', '未知', '0.0.0.0', 0, 0, '保留地址', '获取所有权限管理', '2023-11-18 05:37:09', '2023-11-18 05:37:09');
INSERT INTO `user_opt_log` VALUES ('e5897bce-aaaf-455f-9b68-fa2da255992d', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:22:19', '2023-11-23 03:22:19');
INSERT INTO `user_opt_log` VALUES ('e6466f2d-7191-45e2-b834-02fdf31e3fc9', 'John Doe', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '权限管理', '未知', '0.0.0.0', 0, 0, '保留地址', '获取所有权限管理', '2023-11-18 05:48:28', '2023-11-18 05:48:28');
INSERT INTO `user_opt_log` VALUES ('e7a0d3b5-65f1-42e7-a47f-a376c3bfa952', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":16,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:39:09', '2025-04-15 09:39:09');
INSERT INTO `user_opt_log` VALUES ('e894a49f-3ff5-483c-bdd1-03ee7fc98ce5', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":13,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:55:29', '2025-04-15 09:55:29');
INSERT INTO `user_opt_log` VALUES ('e9db74c7-21c9-4d0b-97c7-f39b0aa67327', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":5,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:06:52', '2025-04-15 10:06:52');
INSERT INTO `user_opt_log` VALUES ('ed6d71ee-2cc8-4343-935b-8268c2973c87', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":2,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 08:44:47', '2025-04-15 08:44:47');
INSERT INTO `user_opt_log` VALUES ('ee3d3675-c18c-4013-9944-3b4375cc4443', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":15,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 09:40:37', '2025-04-15 09:40:37');
INSERT INTO `user_opt_log` VALUES ('f0840fa2-e3f2-4c4f-9e1d-fdb5601472b0', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:20:07', '2023-11-18 07:20:07');
INSERT INTO `user_opt_log` VALUES ('f0c236f0-f753-4b4d-a37e-5389bb91ceea', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::ffff:127.0.0.1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":14,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-13 22:38:13', '2025-04-13 22:38:13');
INSERT INTO `user_opt_log` VALUES ('f1701bcf-7a47-4c26-a870-201c7467a780', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":9,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:08:44', '2025-04-15 10:08:44');
INSERT INTO `user_opt_log` VALUES ('f192ddfa-d533-4b4f-9147-9d4f4af20be1', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:34:50', '2023-11-18 07:34:50');
INSERT INTO `user_opt_log` VALUES ('f25f02d8-0ec4-4415-a853-cb49b30d723e', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::ffff:127.0.0.1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\",\"remember\":true},\"responseStatus\":200,\"success\":true,\"duration\":557,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-10 16:33:52', '2025-04-10 16:33:52');
INSERT INTO `user_opt_log` VALUES ('f438e675-ab8f-44df-aca3-d5555ba6f11d', '未登录用户', NULL, '登录', 'PostmanRuntime/7.43.3', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":559,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-13 22:32:44', '2025-04-13 22:32:44');
INSERT INTO `user_opt_log` VALUES ('f440b808-7378-4254-bbeb-73e3726e888a', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::ffff:127.0.0.1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\",\"remember\":true},\"responseStatus\":200,\"success\":true,\"duration\":567,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-13 09:46:55', '2025-04-13 09:46:55');
INSERT INTO `user_opt_log` VALUES ('f440fd2e-ebdd-4cc0-9a6d-e1a098ba5511', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 04:01:00', '2023-11-23 04:01:00');
INSERT INTO `user_opt_log` VALUES ('f7958c34-2ea4-4339-a3c5-9365df204d06', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":500,\"success\":false,\"duration\":9,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":{}}\"}', '2025-04-15 09:43:53', '2025-04-15 09:43:53');
INSERT INTO `user_opt_log` VALUES ('f8597464-4ef3-44f4-99d0-a7b35ae676aa', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":345,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-11 15:21:13', '2025-04-11 15:21:13');
INSERT INTO `user_opt_log` VALUES ('f91856eb-2392-408d-ad51-0559cce0becf', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:20:59', '2023-11-18 07:20:59');
INSERT INTO `user_opt_log` VALUES ('f9c57454-60dd-4588-ab2b-b963fe7d431d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":11,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:00:24', '2025-04-15 10:00:24');
INSERT INTO `user_opt_log` VALUES ('fad128f5-1b7e-4cba-8345-bdb12b46d292', 'test', '-', '登录', 'Chrome.v119', '0.0.0.0', NULL, NULL, '保留地址', '/v1/sys/auth/login', '2023-11-23 03:53:15', '2023-11-23 03:53:15');
INSERT INTO `user_opt_log` VALUES ('faf14211-1082-4896-ba21-7a6886f4d04d', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::ffff:127.0.0.1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":560,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-13 22:25:22', '2025-04-13 22:25:22');
INSERT INTO `user_opt_log` VALUES ('faf96c1a-7122-4673-be58-acabed6a5a43', 'John Doe', 'f35f39c3-95c5-4496-80fb-7877bf715aa7', '权限管理', '未知', '0.0.0.0', 0, 0, '保留地址', '获取所有权限管理', '2023-11-18 05:49:01', '2023-11-18 05:49:01');
INSERT INTO `user_opt_log` VALUES ('fd68b95c-700c-443b-ae60-2d1c278ed3aa', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":200,\"success\":true,\"duration\":92,\"responsePreview\":\"{\\\"status\\\":1,\\\"message\\\":\\\"登录成功.\\\",\\\"data\\\":{\\\"id\\\":\\\"f35f39c3-95c5-4496-80fb-7877bf715aa7\\\",\\\"username\\\":\\\"admin\\\",\\\"nickname\\\":\\\"John Doe\\\",\\\"roleId\\\":\\\"47914c7b-0fa5-485c-bc44-b5d571e1e89e\\\",\\\"status\\\":\\\"1\\\",\\\"avatar\\\":\\\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQ\"}', '2025-04-24 15:04:29', '2025-04-24 15:04:29');
INSERT INTO `user_opt_log` VALUES ('fd8f93f0-e68d-4244-b8b1-e8998b18d5c4', 'admin', '-', '登录', 'Chrome.v119', '0.0.0.0', 0, 0, '保留地址', '/v1/sys/auth/login', '2023-11-18 07:23:27', '2023-11-18 07:23:27');
INSERT INTO `user_opt_log` VALUES ('fdc3e1d9-8258-4d0d-a691-f6bcf50c4b57', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":9,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码错误\\\"}\"}', '2025-04-18 09:48:03', '2025-04-18 09:48:03');
INSERT INTO `user_opt_log` VALUES ('ff1566a9-cdea-4578-9fd0-1502f33d6d42', '未登录用户', NULL, '登录', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa', '::1', NULL, NULL, NULL, '{\"method\":\"POST\",\"url\":\"/v1/sys/auth/login\",\"requestParams\":{\"username\":\"admin\",\"password\":\"******\",\"code\":\"******\"},\"responseStatus\":400,\"success\":false,\"duration\":9,\"responsePreview\":\"{\\\"status\\\":0,\\\"message\\\":\\\"验证码已失效\\\"}\"}', '2025-04-15 10:03:57', '2025-04-15 10:03:57');

-- ----------------------------
-- Table structure for user_record
-- ----------------------------
DROP TABLE IF EXISTS `user_record`;
CREATE TABLE `user_record`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名称',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职称',
  `department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '所属院系',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(1:已注册的USER 0:临时USER)',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_record
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
