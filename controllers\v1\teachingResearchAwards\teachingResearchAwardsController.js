const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const multer = require('multer');
const jwt = require('jsonwebtoken');
const { JWT_SECRET } = require('../../../config');
const teachingResearchAwardsModel = require('../../../models/v1/mapping/teachingResearchAwardsModel');
const teachingResearchAwardParticipantsModel = require('../../../models/v1/mapping/teachingResearchAwardParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const roleModel = require('../../../models/v1/mapping/roleModel');
const fileModel = require('../../../models/v1/mapping/fileModel');
const teachingResearchAwardLevelsModel = require('../../../models/v1/mapping/teachingResearchAwardLevelsModel');
const { Op, Sequelize } = require('sequelize');
const fileController = require('../common/fileController');
const { getTimeIntervalByName, getUserInfoFromRequest, getSequelizeInstance } = require('../../../utils/others');
const upload = multer({ dest: 'uploads/' });
const { updateUserRankings, RANKING_TYPE_MAPPINGS } = require('../../../utils/rankingUtils');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');

// 添加与参与者的关联
teachingResearchAwardsModel.hasMany(teachingResearchAwardParticipantsModel, {
  foreignKey: 'awardId',
  as: 'participants'
});

/**
 * 将存储过程结果转换为数组格式
 * @param {Array} result - 存储过程返回的结果
 * @param {boolean} [removeMetadata=true] - 是否移除元数据字段
 * @param {boolean} [convertNumericStrings=true] - 是否将数字字符串转换为数字
 * @returns {Array} 处理后的数组
 */
const convertStoredProcResultToArray = (result, removeMetadata = true, convertNumericStrings = true) => {
  if (!Array.isArray(result)) {
    console.warn('存储过程结果不是数组:', result);
    return [];
  }

  // 处理MySQL存储过程返回的特殊格式
  let dataArray = [];

  // 如果结果是嵌套数组，取第一个元素
  if (Array.isArray(result[0])) {
    dataArray = result[0];
  } else if (typeof result[0] === 'object') {
    dataArray = result;
  }

  // 移除可能存在的元数据字段
  if (removeMetadata) {
    dataArray = dataArray.filter(item => {
      return !item.hasOwnProperty('meta') &&
             !item.hasOwnProperty('_meta') &&
             !item.hasOwnProperty('fieldCount');
    });
  }

  // 将数字字符串转换为数字
  if (convertNumericStrings) {
    dataArray = dataArray.map(item => {
      const newItem = {...item};

      for (const [key, value] of Object.entries(newItem)) {
        if (typeof value === 'string' && !isNaN(value) && value.trim() !== '') {
          if (value.includes('.')) {
            newItem[key] = parseFloat(value);
          } else {
            newItem[key] = parseInt(value, 10);
          }
        }
      }

      return newItem;
    });
  }

  return dataArray;
};

/**
 * 获取教学科研奖励列表（统一接口）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwards = async (req, res) => {
  try {
    const {
      awardName,
      level, // 前端传递的是level而不是levelId
      levelId, // 保留levelId以兼容其他代码
      awardLevelId, // 奖励级别ID
      leader, // leader参数现在用于firstResponsibleId查询
      firstResponsibleId, // 直接支持firstResponsibleId参数
      startDate,
      endDate,
      awardStartDate,
      awardEndDate,
      userId,
      page = 1,
      pageSize = 10,
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      ifReviewer = 'all', // 审核状态筛选: 'all'(全部), 'reviewed'(已审核), 'rejected'(拒绝), 'pending'(待审核)
      isExport = false // 是否是导出操作
    } = req.body;
  
    // 构建查询条件
    const where = {};

    // 根据审核状态过滤
    if (ifReviewer === 'reviewed') {
      where.ifReviewer = 1;
    } else if (ifReviewer === 'rejected') {
      where.ifReviewer = 0;
    } else if (ifReviewer === 'pending') {
      where.ifReviewer = null;
    }

    if (awardName) {
      where.awardName = { [Op.like]: `%${awardName}%` };
    }

    // 使用level、levelId或awardLevelId参数，优先使用level（与前端匹配）
    if (level) {
      where.awardLevelId = level;
    } else if (levelId) {
      where.awardLevelId = levelId;
    } else if (awardLevelId) {
      where.awardLevelId = awardLevelId;
    }

    // 初始化include配置
    const includeConfig = [
      {
        model: userModel,
        as: 'firstResponsible',
        attributes: ['id', 'nickname', 'username', 'studentNumber'],
        required: false,
      },
      {
        model: teachingResearchAwardParticipantsModel,
        as: 'participants',
        include: [
          {
            model: userModel,
            as: 'participant',
            attributes: ['id', 'nickname', 'username', 'studentNumber'],
            required: false,
          }
        ],
        required: false,
      },
      {
        model: teachingResearchAwardLevelsModel,
        as: 'awardLevel',
        attributes: ['id', 'levelName', 'score', 'description'],
        required: false,
      }
    ];

    // 处理负责人查询 - 两种情况：ID查询或名称模糊查询
    if (firstResponsibleId) {
      // 如果提供了确切的firstResponsibleId，直接精确查询
      where.firstResponsibleId = firstResponsibleId;
    } else if (leader) {
      // 检查leader是否是UUID格式
      if (leader.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        // 是UUID，直接用于精确查询
        where.firstResponsibleId = leader;
      } else {
        // 不是UUID，进行名称模糊查询
        // 修改firstResponsible的include设置，添加名称过滤条件
        includeConfig[0] = {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: true, // 重要：需要inner join才能正确过滤
          where: {
            [Op.or]: [
              { nickname: { [Op.like]: `%${leader}%` } },
              { username: { [Op.like]: `%${leader}%` } },
              { studentNumber: { [Op.like]: `%${leader}%` } }
            ]
          }
        };
      }
    }

    if (startDate && endDate) {
      where.awardTime = {
        [Op.gte]: startDate,
        [Op.lte]: endDate
      };
    } else if (startDate) {
      where.awardTime = { [Op.gte]: startDate };
    } else if (endDate) {
      where.awardTime = { [Op.lte]: endDate };
    }

    if (awardStartDate && awardEndDate) {
      where.awardTime = {
        [Op.gte]: awardStartDate,
        [Op.lte]: awardEndDate
      };
    } else if (awardStartDate) {
      where.awardTime = { [Op.gte]: awardStartDate };
    } else if (awardEndDate) {
      where.awardTime = { [Op.lte]: awardEndDate };
    }

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");

    // 将range过滤条件添加到数据库查询中
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        where.awardTime = {
          ...where.awardTime, // 保留原有的awardTime条件
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        where[Op.or] = [
          { awardTime: { [Op.lt]: intervalStartTime } },
          { awardTime: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    // 如果提供了userId，添加用户参与关系的查询条件
    if (userId) {
      // 修复查询方式，使用两个独立的查询然后合并结果
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userId },
        attributes: ['awardId'],
        raw: true
      });

      const awardIds = participantAwards.map(p => p.awardId);

      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userId },
        { id: { [Op.in]: awardIds } }
      ];

      // 如果之前已经设置了Or条件（针对range=out的情况），则需要合并条件
      if (where[Op.or]) {
        // 需要使用AND将range条件和userId条件组合
        where[Op.and] = [
          { [Op.or]: where[Op.or] },  // 之前的条件
          { [Op.or]: userCondition }   // userId条件
        ];
        delete where[Op.or];  // 删除原来的条件，因为已经移到AND中了
      } else {
        where[Op.or] = userCondition;
      }
    }

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;

    console.log('查询条件:', where);
    console.log('分页参数:', { page: pageNum, pageSize: pageSizeNum, offset, limit, isExport });
    console.log('负责人查询:', leader ? (typeof leader === 'string' ? `模糊搜索: ${leader}` : `ID搜索: ${leader}`) : '无');

    // 设置查询选项，根据是否导出决定是否应用分页
    const queryOptions = {
      where,
      order: [['awardTime', 'DESC']], // 按获奖时间降序排序
      include: includeConfig,
      distinct: true, // 使用distinct确保正确计数（特别是有多个关联时）
    };

    // 只有在不是导出操作时才应用分页限制
    if (!isExport) {
      queryOptions.offset = offset;
      queryOptions.limit = limit;
      queryOptions.order = [['createdAt', 'DESC']];
    }

    // 查询数据
    let { count, rows } = await teachingResearchAwardsModel.findAndCountAll(queryOptions);

    // 处理每个奖励的成员信息
    const processedAwards = [];

    for (const award of rows) {
      const awardJson = award.toJSON();

      // 添加是否在统计时间范围内的标记（用于前端展示，但不再用于筛选）
      let isInTimeRange = false;
      if (timeInterval && awardJson.awardTime) {
        const awardDate = new Date(awardJson.awardTime);
        const intervalStartTime = new Date(timeInterval.startTime);
        const intervalEndTime = new Date(timeInterval.endTime);

        isInTimeRange = (awardDate >= intervalStartTime && awardDate <= intervalEndTime);
      }

      awardJson.isInTimeRange = isInTimeRange;

      // 处理奖励参与者信息
      if (awardJson.participants && awardJson.participants.length > 0) {
        // 找出奖励负责人
        const leader = awardJson.participants.find(p => p.isLeader);
        if (leader) {
          awardJson.leader = leader.participant;
          awardJson.leaderId = leader.participantId;
        }

        // 处理所有参与者信息
        awardJson.memberDetails = awardJson.participants.map(p => ({
          id: p.participantId,
          name: p.participant ? (p.participant.nickname || p.participant.username) : '未知用户',
          allocationRatio: p.allocationRatio,
          isLeader: p.isLeader
        }));
      }

      // 不再根据range参数过滤，因为已经在数据库查询中处理了
      processedAwards.push(awardJson);
    }

    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: processedAwards,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count, // 使用数据库返回的总记录数
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取教学科研奖励列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教学科研奖励列表失败',
      error: error.message
    });
  }
};

/**
 * 获取奖励详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardDetail = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少奖励ID',
        data: null
      });
    }

    // 查询奖励详情
    const award = await teachingResearchAwardsModel.findByPk(id, {
      include: [
        {
          model: teachingResearchAwardParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'participant',
              attributes: ['id', 'username', 'nickname', 'studentNumber'],
              required: false,
            }
          ],
          required: false,
        },
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'username', 'nickname', 'studentNumber'],
          required: false,
        },
        {
          model: teachingResearchAwardLevelsModel,
          as: 'awardLevel',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        }
      ]
    });

    if (!award) {
      return res.status(404).json({
        code: 404,
        message: '未找到教学科研奖励',
        data: null
      });
    }

    // 将奖励数据转换为JSON对象，方便后续处理
    const awardData = award.toJSON();

    // 处理奖励参与者信息
    if (awardData.participants && awardData.participants.length > 0) {
      // 找出奖励负责人
      const leader = awardData.participants.find(p => p.isLeader);
      if (leader) {
        awardData.leader = leader.participant;
      }

      // 处理所有参与者信息
      awardData.memberDetails = awardData.participants.map(p => ({
        id: p.id,
        participantId: p.participantId,
        participant: p.participant,
        allocationRatio: p.allocationRatio,
        isLeader: p.isLeader
      }));

      // 创建ID到用户信息的映射，方便前端使用
      const memberMap = {};
      awardData.participants.forEach(p => {
        if (p.participant) {
          memberMap[p.participantId] = p.participant;
        }
      });
      awardData.memberMap = memberMap;
    }

    // 添加可读的审核状态描述
    awardData.reviewStatusDesc = getReviewStatusDesc(awardData.ifReviewer);

    // 查询奖励关联的文件列表
    const files = await fileModel.findAll({
      where: {
        projectId: id,
        relatedId: id
      },
      attributes: [
        'id',
        'fileName',
        'originalName',
        'filePath',
        'fileSize',
        'mimeType',
        'extension',
        'uploaderId',
        'relatedId',
        'relatedType',
        'createdAt',
        'updatedAt'
      ],
      order: [['createdAt', 'DESC']]
    });

    // 处理文件信息，添加URL和其他前端需要的信息
    awardData.attachments = files.map(file => {
      const fileData = file.toJSON();
      // 构造文件URL
      const filePath = fileData.filePath;
      const url = filePath.startsWith('/') ? filePath : `/${filePath}`;

      return {
        id: fileData.id,
        name: fileData.originalName,
        fileName: fileData.fileName,
        size: fileData.fileSize,
        type: fileData.mimeType,
        extension: fileData.extension,
        url: url,
        filePath: fileData.filePath,
        uploadTime: fileData.createdAt
      };
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: awardData
    });
  } catch (error) {
    console.error('获取教学科研奖励详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教学科研奖励详情失败',
      error: error.message
    });
  }
};

/**
 * 获取审核状态描述
 * @param {number|null} ifReviewer - 审核状态
 * @returns {string} 审核状态描述
 */
function getReviewStatusDesc(ifReviewer) {
  if (ifReviewer === null || ifReviewer === undefined) {
    return '待审核';
  } else if (ifReviewer === 1) {
    return '已通过';
  } else if (ifReviewer === 0) {
    return '已拒绝';
  } else {
    return '未知状态';
  }
}

/**
 * 创建教学科研奖励
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAward = async (req, res) => {
  const sequelize = getSequelizeInstance(teachingResearchAwardsModel);
  const transaction = await sequelize.transaction();

  try {
    const {
      awardName,
      awardTime,
      awardLevelId,
      firstResponsibleId,
      department,
      remark,
      attachmentUrl,
      participants = [],
      fileIds
    } = req.body;

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 验证必要字段
    if (!awardName || !awardTime || !awardLevelId || !firstResponsibleId) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '获奖名称、获奖时间、奖励级别和第一负责人为必填项',
        data: null
      });
    }

    // 验证分配比例总和
    if (participants.length > 0) {
      const totalRatio = participants.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
      if (Math.abs(totalRatio - 1) > 0.0001) {
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '参与者分配比例总和必须等于1',
          data: null
        });
      }
    }

    // 检查奖励级别是否存在
    const level = await teachingResearchAwardLevelsModel.findByPk(awardLevelId);
    if (!level) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '奖励级别不存在',
        data: null
      });
    }

    // 检查第一负责人是否存在
    const firstResponsible = await userModel.findByPk(firstResponsibleId);
    if (!firstResponsible) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '第一负责人不存在',
        data: null
      });
    }

    const awardId = uuidv4();

    // 创建奖励记录
    const award = await teachingResearchAwardsModel.create({
      id: awardId,
      awardName,
      awardTime,
      awardLevelId,
      firstResponsibleId,
      department,
      remark,
      attachmentUrl: attachmentUrl || `uploads/teaching_research_awards/${awardId}/`,
      status: 1
    }, { transaction });

    // 创建参与者记录
    if (participants && participants.length > 0) {
      const participantRecords = participants.map(participant => ({
        id: uuidv4(),
        awardId,
        participantId: participant.participantId,
        employeeNumber: participant.employeeNumber,
        allocationRatio: participant.allocationRatio,
        isLeader: participant.participantId === firstResponsibleId ? 1 : 0
      }));

      await teachingResearchAwardParticipantsModel.bulkCreate(participantRecords, { transaction });
    }

    // 处理文件关联
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];

      // 解析文件ID数组，可能以JSON字符串形式传递
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }

      // 如果有文件ID，更新关联关系
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (const fileId of fileIdArray) {
          const updateData = {
            projectId: awardId,
            relatedId: awardId,
            relatedType: 'teaching_research_awards'
          };

          await fileModel.update(
            updateData,
            {
              where: { id: fileId },
              transaction
            }
          );

          processedFileIds.push(fileId);
        }
      }
    }

    await transaction.commit();

    // 事务提交成功后，异步移动文件到指定的奖励目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'teaching_research_awards'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${awardId}/`;

        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }

        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });

          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }

          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);

          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }

              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: awardId,
                relatedId: awardId,
                relatedType: storagePath
              });
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响奖励创建的返回结果，仅记录错误
        console.error('移动文件到奖励目录失败:', moveError);
      }
    }

    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: { id: awardId }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('创建教学科研奖励失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建教学科研奖励失败',
      error: error.message
    });
  }
};

/**
 * 更新教学科研奖励
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateAward = async (req, res) => {
  const sequelize = getSequelizeInstance(teachingResearchAwardsModel);;
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const {
      awardName,
      awardTime,
      awardLevelId,
      firstResponsibleId,
      department,
      remark,
      attachmentUrl,
      participants = [],
      fileIds
    } = req.body;

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少奖励ID',
        data: null
      });
    }

    // 查询奖励是否存在
    const award = await teachingResearchAwardsModel.findByPk(id);

    if (!award) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到教学科研奖励',
        data: null
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 检查权限：只有管理员、奖励提交者或参与者可以修改
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      // 检查是否为负责人
      if (award.firstResponsibleId !== userInfo.id) {
        // 检查是否为参与者
        const participants = await teachingResearchAwardParticipantsModel.findAll({
          where: { awardId: id }
        });
        const isParticipant = participants.some(p => p.participantId === userInfo.id);
        
        // 如果既不是负责人也不是参与者，则拒绝访问
        if (!isParticipant) {
          await transaction.rollback();
          return res.status(403).json({
            code: 403,
            message: '您没有权限修改该奖励',
            data: null
          });
        }
      }
    }

    // 验证分配比例总和
    if (participants.length > 0) {
      const totalRatio = participants.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
      if (Math.abs(totalRatio - 1) > 0.0001) {
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '参与者分配比例总和必须等于1',
          data: null
        });
      }
    }

    // 如果更新了奖励级别，检查是否存在
    if (awardLevelId && awardLevelId !== award.awardLevelId) {
      const level = await teachingResearchAwardLevelsModel.findByPk(awardLevelId);
      if (!level) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '奖励级别不存在',
          data: null
        });
      }
    }

    // 如果更新了第一负责人，检查是否存在
    if (firstResponsibleId && firstResponsibleId !== award.firstResponsibleId) {
      const firstResponsible = await userModel.findByPk(firstResponsibleId);
      if (!firstResponsible) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '第一负责人不存在',
          data: null
        });
      }
    }

    // 更新奖励记录
    const updateData = {};
    if (awardName !== undefined) updateData.awardName = awardName;
    if (awardTime !== undefined) updateData.awardTime = awardTime;
    if (awardLevelId !== undefined) updateData.awardLevelId = awardLevelId;
    if (firstResponsibleId !== undefined) updateData.firstResponsibleId = firstResponsibleId;
    if (department !== undefined) updateData.department = department;
    if (remark !== undefined) updateData.remark = remark;
    if (attachmentUrl !== undefined) updateData.attachmentUrl = attachmentUrl;

    await award.update(updateData, { transaction });

    // 如果提供了参与者信息，先删除原有参与者，再创建新的
    if (participants && participants.length > 0) {
      // 删除原有参与者
      await teachingResearchAwardParticipantsModel.destroy({
        where: { awardId: id },
        transaction
      });

      // 创建新的参与者记录
      const participantRecords = participants.map(participant => ({
        id: uuidv4(),
        awardId: id,
        participantId: participant.participantId,
        employeeNumber: participant.employeeNumber,
        allocationRatio: participant.allocationRatio,
        isLeader: participant.participantId === (firstResponsibleId || award.firstResponsibleId) ? 1 : 0
      }));

      await teachingResearchAwardParticipantsModel.bulkCreate(participantRecords, { transaction });
    }

    // 处理文件关联
    if (fileIds) {
      let fileIdArray = [];

      // 解析文件ID数组
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds];
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }

      // 更新文件关联关系
      if (fileIdArray.length > 0) {
        for (const fileId of fileIdArray) {
          await fileModel.update(
            {
              projectId: id,
              relatedId: id,
              relatedType: 'teaching_research_awards'
            },
            {
              where: { id: fileId },
              transaction
            }
          );
        }
      }
    }

    await transaction.commit();

    // 如果奖励已审核，需要更新用户排名
    if (award.ifReviewer === 1) {
      try {
        console.log('开始更新用户排名数据，奖励ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("teachingResearchAwards");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断奖励变更前后是否在时间区间内（使用奖励时间判断）
        const oldAwardTime = award.awardTime;
        const newAwardTime = awardTime || award.awardTime;
        console.log('奖励时间 - 原始:', oldAwardTime, '新:', newAwardTime);
        
        const wasInTimeRange = timeInterval ? 
          isProjectInTimeRange(oldAwardTime, timeInterval.startTime, timeInterval.endTime) : 
          false;
          
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(newAwardTime, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('奖励时间范围状态 - 原始:', wasInTimeRange ? '在范围内' : '不在范围内', 
                   '变更后:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表（针对减分操作）
        let oldRankingTables = [];
        if (wasInTimeRange) {
          oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        // 确定需要更新的排名表（针对加分操作）
        let newRankingTables = [];
        if (isInTimeRange) {
          newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('排名表 - 原始:', oldRankingTables, '新:', newRankingTables);
        
        // 获取旧的级别分数
        const oldAwardLevel = await teachingResearchAwardLevelsModel.findByPk(award.awardLevelId);
        const oldBaseScore = oldAwardLevel ? oldAwardLevel.score : 0;
        console.log('原始级别分数:', oldBaseScore);
        
        // 获取新的级别分数
        let newBaseScore = oldBaseScore;
        if (awardLevelId && awardLevelId !== award.awardLevelId) {
          const newLevel = await teachingResearchAwardLevelsModel.findByPk(awardLevelId);
          if (newLevel) {
            newBaseScore = newLevel.score || 0;
          }
        }
        console.log('新级别分数:', newBaseScore);
        
        // 获取旧的参与者名单
        const oldParticipants = await teachingResearchAwardParticipantsModel.findAll({
          where: { awardId: id }
        });
        console.log('原始参与者数量:', oldParticipants.length);
        
        // 准备新的参与者列表
        let newParticipants = [];
        
        // 如果提供了新的参与者信息
        if (participants && Array.isArray(participants) && participants.length > 0) {
          newParticipants = participants.map(participant => ({
            userId: participant.participantId,
            allocationRatio: parseFloat(participant.allocationRatio) || 0,
            isLeader: participant.participantId === (firstResponsibleId || award.firstResponsibleId)
          }));
        }
        console.log('新参与者数量:', newParticipants.length);
        
        // 从原始参与者中找出要删除的参与者 - 他们在旧列表中但不在新列表中
        const oldUserIds = oldParticipants.map(p => p.participantId);
        const newUserIds = newParticipants.map(p => p.userId);
        
        // 找出要删除的参与者
        const deletedUserIds = oldUserIds.filter(id => !newUserIds.includes(id));
        console.log('要删除的参与者:', deletedUserIds);
        
        // 找出保留的参与者
        const keptUserIds = oldUserIds.filter(id => newUserIds.includes(id));
        console.log('保留的参与者:', keptUserIds);
        
        // 找出新增的参与者
        const addedUserIds = newUserIds.filter(id => !oldUserIds.includes(id));
        console.log('新增的参与者:', addedUserIds);
        
        // 1. 处理要删除的参与者 - 减少奖励数量和分数
        if (deletedUserIds.length > 0) {
          const deletedParticipants = oldParticipants.filter(p => deletedUserIds.includes(p.participantId));
          console.log('被删除的参与者完整数据:', JSON.stringify(deletedParticipants));
          
          const deletedIds = [];
          const deletedRatios = [];
          const deletedScores = [];
          const countDeltas = []; // 固定值为1的数组表示每人减少1个奖励
          
          for (const participant of deletedParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            deletedIds.push(userId);
            deletedRatios.push(ratio);
            deletedScores.push(participantScore);
            countDeltas.push(1); // 每个被删除的参与者奖励数-1
          }
          
          console.log('被删除参与者的排名更新数据:', {
            userIds: deletedIds,
            countDeltas: countDeltas,
            scores: deletedScores
          });
          
          // 对每个排名表执行减分操作
          for (const table of oldRankingTables) {
            console.log(`为被删除的参与者更新排名表 ${table}`);
            await updateUserRankings(
              deletedIds,
              table,
              'teachingResearchAwards',
              countDeltas, // 使用固定值1表示奖励计数-1
              deletedScores,
              null, // 事务已提交
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去被删除参与者的分数和奖励数');
        }
        
        // 2. 处理保留但分配比例变化的参与者 - 先减去原有分数，后面再加上新分数
        if (keptUserIds.length > 0) {
          const keptOldParticipants = oldParticipants.filter(p => keptUserIds.includes(p.participantId));
          console.log('保留的参与者原始数据:', JSON.stringify(keptOldParticipants));
          
          const keptIds = [];
          const keptRatios = [];
          const keptScores = [];
          
          for (const participant of keptOldParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            keptIds.push(userId);
            keptRatios.push(ratio);
            keptScores.push(participantScore);
          }
          
          console.log('保留参与者的减分数据:', {
            userIds: keptIds,
            scores: keptScores
          });
          
          // 减去原有分数（但不减少奖励计数）
          for (const table of oldRankingTables) {
            console.log(`为保留的参与者减去原有分数：${table}`);
            // 传递0表示不改变奖励计数，只改变分数
            const zeroCounts = Array(keptIds.length).fill(0);
            await updateUserRankings(
              keptIds,
              table,
              'teachingResearchAwards',
              zeroCounts, // 奖励计数不变
              keptScores,
              null, // 事务已提交
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去保留参与者的原有分数');
        }
        
        // 3. 处理所有新参与者名单（包括保留的和新增的）- 增加分数，对新增的也增加奖励数
        if (newParticipants.length > 0) {
          console.log('新参与者完整数据:', JSON.stringify(newParticipants));
          
          const allNewIds = [];
          const allNewRatios = [];
          const allNewScores = [];
          const allCountDeltas = []; // 对新增参与者设为1，对保留的参与者设为0
          
          for (const participant of newParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = newBaseScore * ratio;
            
            allNewIds.push(userId);
            allNewRatios.push(ratio);
            allNewScores.push(participantScore);
            
            // 对新增参与者奖励数+1，对保留的参与者奖励数不变
            if (addedUserIds.includes(userId)) {
              allCountDeltas.push(1);
            } else {
              allCountDeltas.push(0);
            }
          }
          
          console.log('所有新参与者的加分数据:', {
            userIds: allNewIds,
            countDeltas: allCountDeltas,
            scores: allNewScores
          });
          
          // 为所有参与者添加分数，但只为新增参与者增加奖励计数
          for (const table of newRankingTables) {
            console.log(`为所有参与者更新排名表：${table}`);
            await updateUserRankings(
              allNewIds,
              table,
              'teachingResearchAwards',
              allCountDeltas, // 使用差异化的计数更新：新增的+1，保留的不变
              allNewScores,
              null, // 事务已提交
              "add" // 加分操作
            );
          }
          console.log('成功更新所有参与者的分数和新增参与者的奖励数');
        }
        
        console.log('成功完成教学科研奖励参与者的排名数据更新');
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        // 事务已提交，只能记录错误
      }
    }

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: { id }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('更新教学科研奖励失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新教学科研奖励失败',
      error: error.message
    });
  }
};

/**
 * 删除教学科研奖励
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAward = async (req, res) => {
  const sequelize = getSequelizeInstance(teachingResearchAwardsModel);;
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const userInfo = await getUserInfoFromRequest(req);

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少奖励ID',
        data: null
      });
    }

    // 查询奖励是否存在
    const award = await teachingResearchAwardsModel.findByPk(id);

    if (!award) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到教学科研奖励',
        data: null
      });
    }

    // 检查权限：只有管理员或奖励提交者可以删除
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      // 检查是否为负责人
      if (award.firstResponsibleId !== userInfo.id) {
        // 检查是否为参与者
        const participants = await teachingResearchAwardParticipantsModel.findAll({
          where: { awardId: id }
        });
        const isParticipant = participants.some(p => p.participantId === userInfo.id);
        
        // 如果既不是负责人也不是参与者，则拒绝访问
        if (!isParticipant) {
          await transaction.rollback();
          return res.status(403).json({
            code: 403,
            message: '您没有权限删除该奖励',
            data: null
          });
        }
      }
    }

    // 如果奖励已审核通过，需要更新用户排名数据（减分）
    if (award.ifReviewer === 1) {
      try {
        // 获取奖励级别对应的分数
        const awardLevel = await teachingResearchAwardLevelsModel.findByPk(award.awardLevelId, { transaction });
        if (!awardLevel) {
          console.error(`未找到奖励级别信息，levelId: ${award.awardLevelId}`);
          throw new Error('未找到奖励级别信息');
        }
        
        const baseScore = awardLevel.score || 0;
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("teachingResearchAwards");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断奖励是否在时间区间内（使用奖励时间判断）
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(award.awardTime, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('奖励时间范围状态:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('需要更新的排名表:', rankingTables);
        
        // 获取奖励所有参与者
        const participants = await teachingResearchAwardParticipantsModel.findAll({
          where: { awardId: id },
          transaction
        });
        
        // 如果有参与者，批量处理减分操作
        if (participants && participants.length > 0) {
          // 准备批量更新的数据
          const userIds = [];
          const scores = [];
          
          // 收集所有参与者数据
          for (const participant of participants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = baseScore * ratio;
            
            userIds.push(userId);
            scores.push(participantScore);
          }
          
          console.log('删除奖励的参与者数据:', {
            userIds: userIds,
            scores: scores
          });
          
          // 创建固定值为1的计数数组，表示每个用户奖励数-1
          const countDeltas = Array(userIds.length).fill(1);
          
          // 对每个排名表执行减分操作（一次性批量处理所有参与者）
          for (const table of rankingTables) {
            console.log(`为被删除奖励的所有参与者更新排名表 ${table}`);
            await updateUserRankings(
              userIds,
              table,
              'teachingResearchAwards',
              countDeltas, // 使用固定值1表示奖励计数-1
              scores, // 使用参与者分数数组
              transaction,
              "subtract" // 减分操作
            );
          }
          
          console.log('成功从排名表中减去被删除奖励参与者的分数和奖励数');
        }
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }

    // 删除相关的参与者记录
    await teachingResearchAwardParticipantsModel.destroy({
      where: { awardId: id },
      transaction
    });

    // 删除奖励记录
    await award.destroy({ transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    await transaction.rollback();
    console.error('删除教学科研奖励失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除教学科研奖励失败',
      error: error.message
    });
  }
};

/**
 * 审核教学科研奖励
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewAward = async (req, res) => {
  const sequelize = getSequelizeInstance(teachingResearchAwardsModel);;
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { ifReviewer, reviewComment } = req.body;
    const userInfo = await getUserInfoFromRequest(req);

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少奖励ID',
        data: null
      });
    }

    if (ifReviewer === undefined || ifReviewer === null) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少审核结果',
        data: null
      });
    }

    // 查询奖励是否存在
    const award = await teachingResearchAwardsModel.findByPk(id);

    if (!award) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到教学科研奖励',
        data: null
      });
    }

    // 检查权限：只有管理员可以审核
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核该奖励',
        data: null
      });
    }

    // 更新审核信息
    await award.update({
      ifReviewer: ifReviewer,
      reviewComment: reviewComment || null,
      reviewerId: userInfo.id
    }, { transaction });

    await transaction.commit();

    // 如果审核通过，更新用户排名数据
    if (ifReviewer === 1) {
      try {
        // 获取奖励级别对应的分数
        const awardLevel = await teachingResearchAwardLevelsModel.findByPk(award.awardLevelId);
        if (!awardLevel) {
          console.error(`未找到奖励级别信息，levelId: ${award.awardLevelId}`);
          throw new Error('未找到奖励级别信息');
        }
        
        const baseScore = awardLevel.score || 0;
        
        // 获取奖励所有参与者及其分配比例
        const participants = await teachingResearchAwardParticipantsModel.findAll({
          where: { awardId: id }
        });
        
        if (participants && participants.length > 0) {
          // 准备用户ID数组和得分数组
          const participantUserIds = [];
          const participantScores = [];
          
          // 计算每个参与者的得分
          for (const participant of participants) {
            const userId = participant.participantId;
            const allocationRatio = parseFloat(participant.allocationRatio) || 0;
            
            // 计算该参与者应得的分数 = 奖励基础分 * 分配比例
            const userScore = baseScore * allocationRatio;
            participantUserIds.push(userId);
            participantScores.push(userScore);
          }
          
          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("teachingResearchAwards");
          
          // 判断奖励是否在时间区间内
          const isInTimeRange = timeInterval && award.awardTime ? 
            isProjectInTimeRange(award.awardTime, timeInterval.startTime, timeInterval.endTime) : 
            false;
          
          // 根据奖励是否在时间区间内，更新不同的排名表
          let rankingTables = [];
          
          if (isInTimeRange) {
            // 在区间内：更新范围内表和全部表
            rankingTables = [
              'user_ranking_reviewed_in', 
              'user_ranking_reviewed_all'
            ];
          } else {
            // 不在区间内：更新范围外表和全部表
            rankingTables = [
              'user_ranking_reviewed_out', 
              'user_ranking_reviewed_all'
            ];
          }
          
          try {
            for (const table of rankingTables) {
              // 更新所有参与者的排名数据：每人计数+1，分数增加各自的得分
              await updateUserRankings(
                participantUserIds,          // 所有参与者的用户ID数组
                table,                       // 表名
                'teachingResearchAwards',    // 类型名
                Array(participantUserIds.length).fill(1), // 每个参与者计数+1
                participantScores,           // 每个参与者的得分数组
                null,                        // 不使用事务（因为前面已提交）
                "add"                        // 操作类型：加分
              );
            }
          } catch (rankingError) {
            console.error('更新排名表失败:', rankingError);
            // 已提交事务，只能记录错误
            console.warn(`排名更新出现错误: ${rankingError.message}`);
          }
        } else {
          console.log(`奖励ID ${id} 没有参与者，无需更新排名`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 已提交事务，只能记录错误
      }
    }

    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: {
        id,
        ifReviewer,
        reviewComment,
        reviewerId: userInfo.id
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('审核教学科研奖励失败:', error);
    return res.status(500).json({
      code: 500,
      message: '审核教学科研奖励失败',
      error: error.message
    });
  }
};

/**
 * 重新提交审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapplyAward = async (req, res) => {
  const sequelize = getSequelizeInstance(teachingResearchAwardsModel);;
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const userInfo = await getUserInfoFromRequest(req);

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少奖励ID',
        data: null
      });
    }

    // 查询奖励是否存在
    const award = await teachingResearchAwardsModel.findByPk(id);

    if (!award) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到教学科研奖励',
        data: null
      });
    }

    // 检查权限：只有管理员或奖励提交者可以重新提交
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth) && award.firstResponsibleId !== userInfo.id) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限重新提交该奖励',
        data: null
      });
    }

    // 重置审核状态
    await award.update({
      ifReviewer: null,
      reviewComment: null,
      reviewerId: null
    }, { transaction });

    await transaction.commit();

    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: { id }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('重新提交教学科研奖励审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '重新提交教学科研奖励审核失败',
      error: error.message
    });
  }
};

/**
 * 导出教学科研奖励数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportAwards = async (req, res) => {
  try {
    const {
      awardName,
      awardLevelId,
      department,
      startDate,
      endDate,
      ifReviewer,
      userId
    } = req.body;

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const where = {};

    if (awardName) {
      where.awardName = { [Op.like]: `%${awardName}%` };
    }

    if (awardLevelId) {
      where.awardLevelId = awardLevelId;
    }

    if (department) {
      where.department = { [Op.like]: `%${department}%` };
    }

    if (startDate && endDate) {
      where.awardTime = {
        [Op.gte]: startDate,
        [Op.lte]: endDate
      };
    } else if (startDate) {
      where.awardTime = { [Op.gte]: startDate };
    } else if (endDate) {
      where.awardTime = { [Op.lte]: endDate };
    }

    if (ifReviewer !== undefined && ifReviewer !== null && ifReviewer !== '') {
      where.ifReviewer = ifReviewer;
    }

    // 权限控制：TEACHER-LV1角色只能导出自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {
      where.firstResponsibleId = userInfo.id;
    }

    // 如果指定了userId，则只导出该用户的数据
    if (userId) {
      where.firstResponsibleId = userId;
    }

    // 查询数据
    const awards = await teachingResearchAwardsModel.findAll({
      where,
      include: [
        {
          model: userModel,
          as: 'firstResponsible',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        },
        {
          model: teachingResearchAwardLevelsModel,
          as: 'awardLevel',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: teachingResearchAwardParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'participant',
              attributes: ['id', 'nickname', 'username', 'studentNumber'],
              required: false,
            }
          ],
          required: false,
        }
      ],
      order: [['awardTime', 'DESC']]
    });

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('教学科研奖励');

    // 设置列标题
    worksheet.columns = [
      { header: '序号', key: 'index', width: 8 },
      { header: '获奖名称', key: 'awardName', width: 30 },
      { header: '获奖时间', key: 'awardTime', width: 15 },
      { header: '奖励级别', key: 'levelName', width: 20 },
      { header: '第一负责人', key: 'firstResponsible', width: 15 },
      { header: '系/教研室', key: 'department', width: 20 },
      { header: '参与者', key: 'participants', width: 30 },
      { header: '审核状态', key: 'ifReviewer', width: 12 },
      { header: '备注', key: 'remark', width: 30 }
    ];

    // 添加数据行
    awards.forEach((award, index) => {
      const awardJson = award.toJSON();

      // 处理参与者信息
      let participantsStr = '';
      if (awardJson.participants && awardJson.participants.length > 0) {
        participantsStr = awardJson.participants.map(p => {
          const name = p.participant ? (p.participant.nickname || p.participant.username) : '未知用户';
          const ratio = (p.allocationRatio * 100).toFixed(1) + '%';
          const leader = p.isLeader ? '(负责人)' : '';
          return `${name}${leader}:${ratio}`;
        }).join('; ');
      }

      worksheet.addRow({
        index: index + 1,
        awardName: awardJson.awardName,
        awardTime: awardJson.awardTime ? new Date(awardJson.awardTime).toLocaleDateString() : '',
        levelName: awardJson.awardLevel ? awardJson.awardLevel.levelName : '',
        firstResponsible: awardJson.firstResponsible ? (awardJson.firstResponsible.nickname || awardJson.firstResponsible.username) : '',
        department: awardJson.department || '',
        participants: participantsStr,
        ifReviewer: getReviewStatusDesc(awardJson.ifReviewer),
        remark: awardJson.remark || ''
      });
    });

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=teaching_research_awards_${new Date().getTime()}.xlsx`);

    // 写入响应
    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('导出教学科研奖励失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出教学科研奖励失败',
      error: error.message
    });
  }
};

/**
 * 导入教学科研奖励数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importAwards = async (req, res) => {
  const sequelize = getSequelizeInstance(teachingResearchAwardsModel);;
  const transaction = await sequelize.transaction();

  try {
    if (!req.file) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '请上传Excel文件',
        data: null
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 检查权限：只有管理员可以导入
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限导入数据',
        data: null
      });
    }

    const filePath = req.file.path;
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: 'Excel文件格式错误',
        data: null
      });
    }

    const results = {
      total: 0,
      success: 0,
      failed: 0,
      errors: []
    };

    // 跳过标题行，从第2行开始读取数据
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // 跳过空行
      if (!row.hasValues) continue;

      results.total++;

      try {
        const awardName = row.getCell(1).value?.toString()?.trim();
        const awardTime = row.getCell(2).value;
        const levelName = row.getCell(3).value?.toString()?.trim();
        const firstResponsibleName = row.getCell(4).value?.toString()?.trim();
        const department = row.getCell(5).value?.toString()?.trim();
        const remark = row.getCell(6).value?.toString()?.trim();

        // 验证必填字段
        if (!awardName || !awardTime || !levelName || !firstResponsibleName) {
          results.failed++;
          results.errors.push(`第${rowNumber}行：缺少必填字段`);
          continue;
        }

        // 查找奖励级别
        const awardLevel = await teachingResearchAwardLevelsModel.findOne({
          where: { levelName: levelName }
        });

        if (!awardLevel) {
          results.failed++;
          results.errors.push(`第${rowNumber}行：未找到奖励级别"${levelName}"`);
          continue;
        }

        // 查找第一负责人
        const firstResponsible = await userModel.findOne({
          where: {
            [Op.or]: [
              { nickname: firstResponsibleName },
              { username: firstResponsibleName },
              { studentNumber: firstResponsibleName }
            ]
          }
        });

        if (!firstResponsible) {
          results.failed++;
          results.errors.push(`第${rowNumber}行：未找到用户"${firstResponsibleName}"`);
          continue;
        }

        // 处理日期
        let formattedDate;
        if (awardTime instanceof Date) {
          formattedDate = awardTime;
        } else if (typeof awardTime === 'string') {
          formattedDate = new Date(awardTime);
        } else if (typeof awardTime === 'number') {
          // Excel日期序列号
          formattedDate = new Date((awardTime - 25569) * 86400 * 1000);
        } else {
          results.failed++;
          results.errors.push(`第${rowNumber}行：日期格式错误`);
          continue;
        }

        if (isNaN(formattedDate.getTime())) {
          results.failed++;
          results.errors.push(`第${rowNumber}行：日期格式错误`);
          continue;
        }

        // 创建奖励记录
        const awardId = uuidv4();
        await teachingResearchAwardsModel.create({
          id: awardId,
          awardName,
          awardTime: formattedDate,
          awardLevelId: awardLevel.id,
          firstResponsibleId: firstResponsible.id,
          department: department || '',
          remark: remark || '',
          attachmentUrl: `uploads/teaching_research_awards/${awardId}/`,
          status: 1
        }, { transaction });

        // 创建参与者记录（第一负责人作为参与者）
        await teachingResearchAwardParticipantsModel.create({
          id: uuidv4(),
          awardId,
          participantId: firstResponsible.id,
          employeeNumber: firstResponsible.studentNumber,
          allocationRatio: 1.0,
          isLeader: 1
        }, { transaction });

        results.success++;

      } catch (error) {
        results.failed++;
        results.errors.push(`第${rowNumber}行：${error.message}`);
        console.error(`处理第${rowNumber}行数据失败:`, error);
      }
    }

    await transaction.commit();

    // 删除临时文件
    try {
      fs.unlinkSync(filePath);
    } catch (error) {
      console.error('删除临时文件失败:', error);
    }

    return res.status(200).json({
      code: 200,
      message: '导入完成',
      data: results
    });

  } catch (error) {
    await transaction.rollback();
    console.error('导入教学科研奖励失败:', error);

    // 删除临时文件
    if (req.file && req.file.path) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (error) {
        console.error('删除临时文件失败:', error);
      }
    }

    return res.status(500).json({
      code: 500,
      message: '导入教学科研奖励失败',
      error: error.message
    });
  }
};

/**
 * 获取教学科研奖励统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardsStatistics = async (req, res) => {
  try {
    const { range = 'all', userId } = req.body;

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const where = {};

    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {
      where.firstResponsibleId = userInfo.id;
    }

    // 如果指定了userId，则只统计该用户的数据
    if (userId) {
      where.firstResponsibleId = userId;
    }

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");

    // 根据range参数添加时间筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        where.awardTime = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        where[Op.or] = [
          { awardTime: { [Op.lt]: intervalStartTime } },
          { awardTime: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    // 统计总数
    const totalCount = await teachingResearchAwardsModel.count({ where });

    // 按审核状态统计
    const reviewedCount = await teachingResearchAwardsModel.count({
      where: { ...where, ifReviewer: 1 }
    });

    const rejectedCount = await teachingResearchAwardsModel.count({
      where: { ...where, ifReviewer: 0 }
    });

    const pendingCount = await teachingResearchAwardsModel.count({
      where: { ...where, ifReviewer: null }
    });

    // 按级别统计
    const levelStats = await teachingResearchAwardsModel.findAll({
      where,
      include: [
        {
          model: teachingResearchAwardLevelsModel,
          as: 'awardLevel',
          attributes: ['id', 'levelName', 'score'],
          required: false
        }
      ],
      attributes: [
        'awardLevelId',
        [Sequelize.fn('COUNT', Sequelize.col('teaching_research_awards.id')), 'count']
      ],
      group: ['awardLevelId', 'awardLevel.id'],
      raw: false
    });

    // 按年份统计
    const yearStats = await teachingResearchAwardsModel.findAll({
      where,
      attributes: [
        [Sequelize.fn('YEAR', Sequelize.col('awardTime')), 'year'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      group: [Sequelize.fn('YEAR', Sequelize.col('awardTime'))],
      order: [[Sequelize.fn('YEAR', Sequelize.col('awardTime')), 'DESC']],
      raw: true
    });

    // 按部门统计
    const departmentStats = await teachingResearchAwardsModel.findAll({
      where,
      attributes: [
        'department',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      group: ['department'],
      order: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'DESC']],
      raw: true
    });

    const statistics = {
      overview: {
        total: totalCount,
        reviewed: reviewedCount,
        rejected: rejectedCount,
        pending: pendingCount
      },
      levelDistribution: levelStats.map(item => ({
        levelId: item.awardLevelId,
        levelName: item.awardLevel ? item.awardLevel.levelName : '未知级别',
        count: parseInt(item.get('count'))
      })),
      yearDistribution: yearStats.map(item => ({
        year: item.year,
        count: parseInt(item.count)
      })),
      departmentDistribution: departmentStats.map(item => ({
        department: item.department || '未指定',
        count: parseInt(item.count)
      }))
    };

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: statistics
    });

  } catch (error) {
    console.error('获取教学科研奖励统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教学科研奖励统计数据失败',
      error: error.message
    });
  }
};

/**
 * 获取部门教学科研奖励排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDepartmentAwardsRanking = async (req, res) => {
  try {
    const { range = 'all', limit = 10 } = req.body;

    // 构建查询条件
    const where = { ifReviewer: 1 }; // 只统计已审核通过的奖励

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");

    // 根据range参数添加时间筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        where.awardTime = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        where[Op.or] = [
          { awardTime: { [Op.lt]: intervalStartTime } },
          { awardTime: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    // 按部门统计奖励数量和总分
    const departmentRanking = await teachingResearchAwardsModel.findAll({
      where,
      include: [
        {
          model: teachingResearchAwardLevelsModel,
          as: 'awardLevel',
          attributes: ['score'],
          required: false
        }
      ],
      attributes: [
        'department',
        [Sequelize.fn('COUNT', Sequelize.col('teaching_research_awards.id')), 'awardCount'],
        [Sequelize.fn('SUM', Sequelize.col('awardLevel.score')), 'totalScore']
      ],
      group: ['department'],
      order: [
        [Sequelize.fn('SUM', Sequelize.col('awardLevel.score')), 'DESC'],
        [Sequelize.fn('COUNT', Sequelize.col('teaching_research_awards.id')), 'DESC']
      ],
      limit: parseInt(limit),
      raw: true
    });

    // 格式化结果
    const ranking = departmentRanking.map((item, index) => ({
      rank: index + 1,
      department: item.department || '未指定部门',
      awardCount: parseInt(item.awardCount) || 0,
      totalScore: parseFloat(item.totalScore) || 0
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: ranking
    });

  } catch (error) {
    console.error('获取部门教学科研奖励排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取部门教学科研奖励排名失败',
      error: error.message
    });
  }
};

/**
 * 获取教师教学科研奖励排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherAwardRanking = async (req, res) => {
  try {
    console.log('获取教师教学科研奖励排名，参数:', req.body);
    const {
      range = 'all',
      ifReviewer = 'all',
      page = 1,
      pageSize,
      isExport = false
    } = req.body;
    const limit = pageSize != null ? pageSize : 10;

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(limit);
    const offset = (pageNum - 1) * pageSizeNum;

    // 根据range选择对应的排名表模型
    let RankingModel;
    switch(range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }

    // 查询条件：按教学科研奖励总分降序排序
    const queryOptions = {
      order: [['teachingResearchAwardScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'teachingResearchAwardCount',
        'teachingResearchAwardScore'
      ]
    };

    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }

    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);

    // 格式化返回数据
    const formattedResults = rows.map((item, index) => ({
      rank: item.rank || index + 1 + offset,
      userId: item.userId,
      userName: item.nickName,
      studentNumber: item.studentNumber,
      totalAwards: item.teachingResearchAwardCount || 0,
      totalScore: parseFloat(item.teachingResearchAwardScore || 0).toFixed(2)
    }));

    // 构建分页信息
    const pagination = {
      total: count,
      page: pageNum,
      pageSize: pageSizeNum,
      totalPages: Math.ceil(count / pageSizeNum)
    };

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: formattedResults,
        pagination: pagination
      }
    });

  } catch (error) {
    console.error('获取教师教学科研奖励排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教师教学科研奖励排名失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取用户教学科研奖励汇总
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserAwardsSummary = async (req, res) => {
  try {
    const { userId } = req.params;
    const { range = 'all' } = req.body;

    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1' && userInfo.id !== userId) {
      return res.status(403).json({
        code: 403,
        message: '您只能查看自己的奖励汇总',
        data: null
      });
    }

    // 构建查询条件
    const where = { ifReviewer: 1 }; // 只统计已审核通过的奖励

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");

    // 根据range参数添加时间筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        where.awardTime = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        where[Op.or] = [
          { awardTime: { [Op.lt]: intervalStartTime } },
          { awardTime: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    // 查询用户作为第一负责人的奖励
    const leaderAwards = await teachingResearchAwardsModel.findAll({
      where: {
        ...where,
        firstResponsibleId: userId
      },
      include: [
        {
          model: teachingResearchAwardLevelsModel,
          as: 'awardLevel',
          attributes: ['id', 'levelName', 'score'],
          required: false
        }
      ]
    });

    // 查询用户作为参与者的奖励
    const participantAwards = await teachingResearchAwardsModel.findAll({
      where,
      include: [
        {
          model: teachingResearchAwardParticipantsModel,
          as: 'participants',
          where: { participantId: userId },
          required: true
        },
        {
          model: teachingResearchAwardLevelsModel,
          as: 'awardLevel',
          attributes: ['id', 'levelName', 'score'],
          required: false
        }
      ]
    });

    // 计算统计数据
    let totalCount = 0;
    let totalScore = 0;
    let leaderCount = 0;
    let leaderScore = 0;
    let participantCount = 0;
    let participantScore = 0;

    const levelStats = {};

    // 统计作为负责人的奖励
    leaderAwards.forEach(award => {
      const awardJson = award.toJSON();
      leaderCount++;
      totalCount++;

      if (awardJson.awardLevel && awardJson.awardLevel.score) {
        const score = parseFloat(awardJson.awardLevel.score);
        leaderScore += score;
        totalScore += score;

        // 按级别统计
        const levelName = awardJson.awardLevel.levelName;
        if (!levelStats[levelName]) {
          levelStats[levelName] = { count: 0, score: 0 };
        }
        levelStats[levelName].count++;
        levelStats[levelName].score += score;
      }
    });

    // 统计作为参与者的奖励
    participantAwards.forEach(award => {
      const awardJson = award.toJSON();

      // 查找该用户的参与记录
      const userParticipation = awardJson.participants.find(p => p.participantId === userId);
      if (userParticipation && !userParticipation.isLeader) { // 排除已经作为负责人统计的
        participantCount++;
        totalCount++;

        if (awardJson.awardLevel && awardJson.awardLevel.score) {
          const baseScore = parseFloat(awardJson.awardLevel.score);
          const ratio = parseFloat(userParticipation.allocationRatio) || 0;
          const score = baseScore * ratio;

          participantScore += score;
          totalScore += score;

          // 按级别统计
          const levelName = awardJson.awardLevel.levelName;
          if (!levelStats[levelName]) {
            levelStats[levelName] = { count: 0, score: 0 };
          }
          levelStats[levelName].count++;
          levelStats[levelName].score += score;
        }
      }
    });

    // 格式化级别统计
    const levelDistribution = Object.entries(levelStats).map(([levelName, stats]) => ({
      levelName,
      count: stats.count,
      score: parseFloat(stats.score.toFixed(2))
    }));

    const summary = {
      userId,
      total: {
        count: totalCount,
        score: parseFloat(totalScore.toFixed(2))
      },
      asLeader: {
        count: leaderCount,
        score: parseFloat(leaderScore.toFixed(2))
      },
      asParticipant: {
        count: participantCount,
        score: parseFloat(participantScore.toFixed(2))
      },
      levelDistribution
    };

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: summary
    });

  } catch (error) {
    console.error('获取用户教学科研奖励汇总失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户教学科研奖励汇总失败',
      error: error.message
    });
  }
};

// 辅助函数：获取审核状态描述
function getReviewStatusDesc(ifReviewer) {
  if (ifReviewer === 1) return '已通过';
  if (ifReviewer === 0) return '已拒绝';
  return '待审核';
}

/**
 * 判断项目是否在时间范围内
 * @param {string} projectDate - 项目日期
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isProjectInTimeRange(projectDate, startDate, endDate) {
  if (!projectDate || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const projectDateObj = new Date(projectDate);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 项目日期必须在开始日期和结束日期之间
  return projectDateObj >= startDateObj && projectDateObj <= endDateObj;
}

/**
 * 获取教学科研奖励总分统计（按级别和总体）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeachingResearchAwardsTotalScore = async (req, res) => {
  try {
    // 获取参数
    const { range = 'in', ifReviewer = 'reviewed' } = req.body;
    
    // 获取数据库连接实例
    let sequelize;
    if (teachingResearchAwardsModel.sequelize) {
      sequelize = teachingResearchAwardsModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }
    
    console.log('调用存储过程参数:', {
      range,
      ifReviewer
    });
    
    let userId = null; 
    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    // 调用存储过程
    const results = await sequelize.query(
      'CALL get_teaching_research_awards_total_score(?, ?, ?)',
      {
        replacements: [range, ifReviewer, userId],
        type: sequelize.QueryTypes.RAW,
        raw: true
      }
    );
    
    console.log('存储过程原始返回结果:', JSON.stringify(results));
    
    // 初始化返回值
    const responseData = {
      levelStats: [], // 按级别的统计
      overallStats: { totalAwards: 0, totalScore: 0 }, // 总体统计
      timeInterval: null // 时间区间信息
    };
    
    // 根据实际返回结果格式处理数据
    if (Array.isArray(results) && results.length > 0) {
      // 检查返回的是单层数组(直接就是结果数组)还是嵌套数组(多个结果集)
      const isNestedArray = Array.isArray(results[0]) && !results[0].hasOwnProperty('levelId') && !results[0].hasOwnProperty('levelName');
      
      if (isNestedArray) {
        // 处理嵌套数组格式 (传统格式)
        // 第一个结果集可能是按级别统计的数据
        if (results[0] && Array.isArray(results[0])) {
          responseData.levelStats = results[0].map(item => ({
            levelId: item.levelId,
            levelName: item.levelName || '未知级别',
            count: parseInt(item.count || 0, 10),
            totalScore: parseFloat(item.totalScore || 0)
          }));
        }
        
        // 第二个结果集可能是总体统计
        if (results.length > 1 && Array.isArray(results[1]) && results[1].length > 0) {
          responseData.overallStats = {
            totalAwards: parseInt(results[1][0].totalAwards || 0, 10),
            totalScore: parseFloat(results[1][0].totalScore || 0)
          };
        }
        
        // 第三个结果集可能是时间区间信息
        if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
          responseData.timeInterval = results[2][0];
        }
      } else {
        // 处理单层数组格式 (当前情况)
        // 将results直接作为levelStats使用
        responseData.levelStats = results.map(item => ({
          levelId: item.levelId,
          levelName: item.levelName || '未知级别',
          count: parseInt(item.count || 0, 10),
          totalScore: parseFloat(item.totalScore || 0)
        }));
        
        // 根据levelStats计算总体统计
        responseData.overallStats = {
          totalAwards: responseData.levelStats.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0),
          totalScore: parseFloat(
            responseData.levelStats.reduce((sum, item) => sum + (parseFloat(item.totalScore) || 0), 0).toFixed(2)
          )
        };
      }
    }
    
    // 如果没有获取到时间区间信息，则尝试使用工具函数获取
    if (!responseData.timeInterval) {
      try {
        const timeInterval = await getTimeIntervalByName('teachingResearchAwards');
        if (timeInterval) {
          responseData.timeInterval = timeInterval;
        }
      } catch (timeError) {
        console.error('获取时间区间信息失败:', timeError);
      }
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取教学科研奖励总分统计成功',
      data: responseData
    });
  } catch (error) {
    console.error('获取教学科研奖励总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教学科研奖励总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户教学科研奖励详情列表及得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserAwardsDetail = async (req, res) => {
  try {
    const { 
      userId,                // 用户ID - 必填
      range = 'all',         // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all',  // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      pageSize = 10,         // 每页记录数
      pageNum = 1            // 当前页码
    } = req.body;
    
    console.log("getUserAwardsDetail被调用，参数:", JSON.stringify({userId, range, reviewStatus, pageSize, pageNum}));
    
    // 验证必填参数
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：userId',
        data: null
      });
    }
    
    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    
    // 验证用户权限 - TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1' && userId !== userInfo.id) {
      return res.status(403).json({
        code: 403,
        message: '权限不足，您只能查看自己的奖励详情',
        data: null
      });
    }
    
    // 分页参数
    const pageNumberInt = parseInt(pageNum);
    const pageSizeInt = parseInt(pageSize);
    const offset = (pageNumberInt - 1) * pageSizeInt;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");
    console.log('获取到时间区间:', timeInterval ? `${timeInterval.startTime} 至 ${timeInterval.endTime}` : '无');
    
    // 构建查询条件
    const whereCondition = {};
    
    // 添加时间范围过滤条件
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        whereCondition.awardTime = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { awardTime: { [Op.lt]: intervalStartTime } },
          { awardTime: { [Op.gt]: intervalEndTime } }
        ];
      }
    }
    
    // 审核状态筛选
    if (reviewStatus !== 'all') {
      if (reviewStatus === 'reviewed') {
        whereCondition.ifReviewer = 1;
      } else if (reviewStatus === 'rejected') {
        whereCondition.ifReviewer = 0;
      } else if (reviewStatus === 'pending') {
        whereCondition.ifReviewer = null;
      }
    }
    
    console.log('查询条件:', JSON.stringify(whereCondition));
    
    // 首先查询用户参与的奖励ID列表
    const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
      where: { participantId: userId },
      attributes: ['awardId', 'allocationRatio', 'isLeader'],
      raw: true
    });
    
    const awardIds = participantAwards.map(p => p.awardId);
    console.log(`找到用户参与的奖励数量: ${awardIds.length}`);
    
    if (awardIds.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          totalCount: 0,
          pageSize: pageSizeInt,
          pageNum: pageNumberInt,
          list: [],
          stats: {
            totalAwards: 0,
            leaderCount: 0,
            participantCount: 0,
            totalScore: 0
          },
          timeInterval: timeInterval ? {
            startTime: timeInterval.startTime,
            endTime: timeInterval.endTime,
            name: timeInterval.name
          } : null,
          user: {
            id: userId,
            name: userInfo.nickname || userInfo.username,
            employeeNumber: userInfo.studentNumber
          },
          pagination: {
            total: 0,
            current: pageNumberInt,
            pageSize: pageSizeInt
          }
        }
      });
    }
    
    // 添加奖励ID筛选条件，确保只查询用户参与的奖励
    whereCondition.id = { [Op.in]: awardIds };
    
    // 查询用户参与的奖励
    const userAwards = await teachingResearchAwardsModel.findAll({
      where: whereCondition,
      include: [
        {
          model: teachingResearchAwardLevelsModel,
          as: 'awardLevel',
          attributes: ['id', 'levelName', 'score'],
          required: false
        }
      ]
    });
    
    console.log(`根据条件查询到的奖励数量: ${userAwards.length}`);
    
    // 处理查询结果
    const awardsList = [];
    let leaderCount = 0;
    let participantCount = 0;
    let totalScore = 0;
    
    for (const award of userAwards) {
      const awardJson = award.toJSON();
      
      // 查找用户在该奖励中的参与信息
      const participation = participantAwards.find(p => p.awardId === award.id);
      
      if (participation) {
        const isLeader = participation.isLeader === 1;
        const allocationRatio = parseFloat(participation.allocationRatio || 0);
        
        // 计算奖励总分数（使用奖励级别的分数）
        const awardScore = parseFloat(awardJson.awardLevel?.score || 0);
        
        // 计算用户在奖励中的得分
        const userScore = awardScore * allocationRatio;
        
        // 统计负责人和参与者数量
        if (isLeader) {
          leaderCount++;
        } else {
          participantCount++;
        }
        
        // 累计总分
        totalScore += userScore;
        
        // 创建奖励详情对象
        const awardDetail = {
          id: awardJson.id,
          awardName: awardJson.awardName,
          levelName: awardJson.awardLevel?.levelName || '未知级别',
          awardTime: awardJson.awardTime,
          department: awardJson.department,
          baseScore: awardScore,
          allocationRatio: allocationRatio,
          isLeader: isLeader,
          userScore: parseFloat(userScore.toFixed(2)),
          reviewStatus: getReviewStatusDesc(awardJson.ifReviewer),
          reviewComment: awardJson.reviewComment,
          createdAt: awardJson.createdAt,
          updatedAt: awardJson.updatedAt
        };
        
        awardsList.push(awardDetail);
      }
    }
    
    // 按用户得分降序排序
    awardsList.sort((a, b) => b.userScore - a.userScore);
    
    // 计算总数
    const totalCount = awardsList.length;
    
    // 应用分页
    const pagedAwards = awardsList.slice(offset, offset + pageSizeInt);
    
    // 查询用户信息
    const user = await userModel.findByPk(userId);
    const userData = user ? {
      id: user.id,
      name: user.nickname || user.username,
      employeeNumber: user.studentNumber
    } : { id: userId, name: '未知用户', employeeNumber: '' };
    
    // 准备返回数据
    const responseData = {
      totalCount,
      pageSize: pageSizeInt,
      pageNum: pageNumberInt,
      list: pagedAwards,
      stats: {
        totalAwards: totalCount,
        leaderCount,
        participantCount,
        totalScore: parseFloat(totalScore.toFixed(2))
      },
      timeInterval: timeInterval ? {
        startTime: timeInterval.startTime,
        endTime: timeInterval.endTime,
        name: timeInterval.name
      } : null,
      user: userData,
      pagination: {
        total: totalCount,
        current: pageNumberInt,
        pageSize: pageSizeInt
      }
    };
    
    console.log("返回数据结构:", JSON.stringify({
      totalCount: responseData.totalCount,
      listLength: responseData.list.length,
      statsKeys: Object.keys(responseData.stats),
      hasPagination: !!responseData.pagination
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: responseData
    });
  } catch (error) {
    console.error('获取用户教学科研奖励详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户教学科研奖励详情失败: ' + error.message,
      data: null
    });
  }
};

// ==================== 图表统计相关函数 ====================

/**
 * 获取审核状态分布统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReviewStatusStats = async (req, res) => {
  try {
    const { 
      range = 'all',         // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all',  // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      userId                 // 用户ID，用于筛选特定用户的数据
    } = req.body;
    
    console.log("getReviewStatusStats被调用，参数:", JSON.stringify({range, reviewStatus, userId}));
    
    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    
    // 构建查询条件
    const whereCondition = {};
    
    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {
      // 强制使用自己的ID
      const userIdToUse = userInfo.id;
      
      // 查询用户参与的奖励ID列表
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userIdToUse },
        attributes: ['awardId'],
        raw: true
      });
      
      const awardIds = participantAwards.map(p => p.awardId);
      
      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userIdToUse },
        { id: { [Op.in]: awardIds } }
      ];
      
      whereCondition[Op.or] = userCondition;
    } 
    // 如果指定了userId且不是TEACHER-LV1角色，则只统计该用户的数据
    else if (userId) {
      // 查询用户参与的奖励ID列表
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userId },
        attributes: ['awardId'],
        raw: true
      });
      
      const awardIds = participantAwards.map(p => p.awardId);
      
      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userId },
        { id: { [Op.in]: awardIds } }
      ];
      
      whereCondition[Op.or] = userCondition;
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");
    
    // 添加时间范围过滤条件
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        const timeCondition = {
          awardTime: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 如果已经有用户条件，需要使用AND组合
        if (whereCondition[Op.or]) {
          whereCondition[Op.and] = [
            { [Op.or]: whereCondition[Op.or] },
            timeCondition
          ];
          delete whereCondition[Op.or];
        } else {
          Object.assign(whereCondition, timeCondition);
        }
      } else if (range === 'out') {
        // 在时间范围外
        const timeCondition = {
          [Op.or]: [
            { awardTime: { [Op.lt]: intervalStartTime } },
            { awardTime: { [Op.gt]: intervalEndTime } }
          ]
        };
        
        // 如果已经有用户条件，需要使用AND组合
        if (whereCondition[Op.or]) {
          whereCondition[Op.and] = [
            { [Op.or]: whereCondition[Op.or] },
            timeCondition
          ];
          delete whereCondition[Op.or];
        } else {
          Object.assign(whereCondition, timeCondition);
        }
      }
    }
    
    // 获取各状态的数量
    const reviewedCount = await teachingResearchAwardsModel.count({
      where: {
        ...whereCondition,
        ifReviewer: 1
      }
    });
    
    const rejectedCount = await teachingResearchAwardsModel.count({
      where: {
        ...whereCondition,
        ifReviewer: 0
      }
    });
    
    const pendingCount = await teachingResearchAwardsModel.count({
      where: {
        ...whereCondition,
        ifReviewer: null
      }
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取审核状态分布统计成功',
      data: {
        reviewed: reviewedCount,
        rejected: rejectedCount,
        pending: pendingCount
      }
    });
  } catch (error) {
    console.error('获取审核状态分布统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取审核状态分布统计失败',
      error: error.message
    });
  }
};

/**
 * 获取奖励级别分布统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelStats = async (req, res) => {
  try {
    const { 
      range = 'all',         // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'reviewed',   // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      userId                 // 用户ID，用于筛选特定用户的数据
    } = req.body;
    
    console.log("getLevelStats被调用，参数:", JSON.stringify({range, reviewStatus, userId}));
    
    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    
    // 构建查询条件
    const where = {};
    
    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {
      // 强制使用自己的ID
      const userIdToUse = userInfo.id;
      
      // 查询用户参与的奖励ID列表
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userIdToUse },
        attributes: ['awardId'],
        raw: true
      });
      
      const awardIds = participantAwards.map(p => p.awardId);
      
      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userIdToUse },
        { id: { [Op.in]: awardIds } }
      ];
      
      where[Op.or] = userCondition;
    } 
    // 如果指定了userId且不是TEACHER-LV1角色，则只统计该用户的数据
    else if (userId) {
      // 查询用户参与的奖励ID列表
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userId },
        attributes: ['awardId'],
        raw: true
      });
      
      const awardIds = participantAwards.map(p => p.awardId);
      
      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userId },
        { id: { [Op.in]: awardIds } }
      ];
      
      where[Op.or] = userCondition;
    }
    
    // 根据审核状态过滤
    if (reviewStatus !== 'all') {
      if (reviewStatus === 'reviewed') {
        if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            { ifReviewer: 1 }
          ];
          delete where[Op.or];
        } else {
          where.ifReviewer = 1;
        }
      } else if (reviewStatus === 'rejected') {
        if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            { ifReviewer: 0 }
          ];
          delete where[Op.or];
        } else {
          where.ifReviewer = 0;
        }
      } else if (reviewStatus === 'pending') {
        if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            { ifReviewer: null }
          ];
          delete where[Op.or];
        } else {
          where.ifReviewer = null;
        }
      }
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");
    
    // 添加时间范围过滤条件
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        const timeCondition = {
          awardTime: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 如果已经有AND条件，添加到AND数组中
        if (where[Op.and]) {
          where[Op.and].push(timeCondition);
        } 
        // 如果已经有OR条件，转换为AND+OR结构
        else if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            timeCondition
          ];
          delete where[Op.or];
        } 
        // 否则直接添加时间条件
        else {
          Object.assign(where, timeCondition);
        }
      } else if (range === 'out') {
        // 在时间范围外
        const timeCondition = {
          [Op.or]: [
            { awardTime: { [Op.lt]: intervalStartTime } },
            { awardTime: { [Op.gt]: intervalEndTime } }
          ]
        };
        
        // 如果已经有AND条件，需要特殊处理
        if (where[Op.and]) {
          where[Op.and].push(timeCondition);
        } 
        // 如果已经有OR条件，转换为AND+OR结构
        else if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            timeCondition
          ];
          delete where[Op.or];
        } 
        // 否则直接添加时间条件
        else {
          Object.assign(where, timeCondition);
        }
      }
    }
    
    // 使用ORM方式查询，按级别分组统计
    const levelStats = await teachingResearchAwardsModel.findAll({
      where,
      include: [
        {
          model: teachingResearchAwardLevelsModel,
          as: 'awardLevel',
          attributes: ['id', 'levelName'],
          required: true
        }
      ],
      attributes: [
        'awardLevelId',
        [Sequelize.fn('COUNT', Sequelize.col('teaching_research_awards.id')), 'count']
      ],
      group: ['awardLevelId', 'awardLevel.id', 'awardLevel.levelName'],
      order: [[Sequelize.literal('count'), 'DESC']]
    });
    
    // 处理结果
    const formattedStats = levelStats.map(item => {
      const itemData = item.toJSON();
      return {
        levelId: itemData.awardLevelId,
        levelName: itemData.awardLevel.levelName || '未知级别',
        count: parseInt(itemData.count || 0)
      };
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取奖励级别分布统计成功',
      data: formattedStats
    });
  } catch (error) {
    console.error('获取奖励级别分布统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取奖励级别分布统计失败',
      error: error.message
    });
  }
};

/**
 * 获取年度奖励分布统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getYearStats = async (req, res) => {
  try {
    const { 
      range = 'all',         // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all',  // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      userId                 // 用户ID，用于筛选特定用户的数据
    } = req.body;
    
    console.log("getYearStats被调用，参数:", JSON.stringify({range, reviewStatus, userId}));
    
    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    
    // 构建查询条件
    const where = {};
    
    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {
      // 强制使用自己的ID
      const userIdToUse = userInfo.id;
      
      // 查询用户参与的奖励ID列表
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userIdToUse },
        attributes: ['awardId'],
        raw: true
      });
      
      const awardIds = participantAwards.map(p => p.awardId);
      
      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userIdToUse },
        { id: { [Op.in]: awardIds } }
      ];
      
      where[Op.or] = userCondition;
    } 
    // 如果指定了userId且不是TEACHER-LV1角色，则只统计该用户的数据
    else if (userId) {
      // 查询用户参与的奖励ID列表
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userId },
        attributes: ['awardId'],
        raw: true
      });
      
      const awardIds = participantAwards.map(p => p.awardId);
      
      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userId },
        { id: { [Op.in]: awardIds } }
      ];
      
      where[Op.or] = userCondition;
    }
    
    // 根据审核状态过滤
    if (reviewStatus !== 'all') {
      if (reviewStatus === 'reviewed') {
        if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            { ifReviewer: 1 }
          ];
          delete where[Op.or];
        } else {
          where.ifReviewer = 1;
        }
      } else if (reviewStatus === 'rejected') {
        if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            { ifReviewer: 0 }
          ];
          delete where[Op.or];
        } else {
          where.ifReviewer = 0;
        }
      } else if (reviewStatus === 'pending') {
        if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            { ifReviewer: null }
          ];
          delete where[Op.or];
        } else {
          where.ifReviewer = null;
        }
      }
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");
    
    // 添加时间范围过滤条件
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        const timeCondition = {
          awardTime: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 如果已经有AND条件，添加到AND数组中
        if (where[Op.and]) {
          where[Op.and].push(timeCondition);
        } 
        // 如果已经有OR条件，转换为AND+OR结构
        else if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            timeCondition
          ];
          delete where[Op.or];
        } 
        // 否则直接添加时间条件
        else {
          Object.assign(where, timeCondition);
        }
      } else if (range === 'out') {
        // 在时间范围外
        const timeCondition = {
          [Op.or]: [
            { awardTime: { [Op.lt]: intervalStartTime } },
            { awardTime: { [Op.gt]: intervalEndTime } }
          ]
        };
        
        // 如果已经有AND条件，需要特殊处理
        if (where[Op.and]) {
          where[Op.and].push(timeCondition);
        } 
        // 如果已经有OR条件，转换为AND+OR结构
        else if (where[Op.or]) {
          where[Op.and] = [
            { [Op.or]: where[Op.or] },
            timeCondition
          ];
          delete where[Op.or];
        } 
        // 否则直接添加时间条件
        else {
          Object.assign(where, timeCondition);
        }
      }
    }
    
    // 使用ORM方式查询，按年份分组统计
    const yearStats = await teachingResearchAwardsModel.findAll({
      where,
      attributes: [
        [Sequelize.fn('YEAR', Sequelize.col('awardTime')), 'year'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      group: [Sequelize.fn('YEAR', Sequelize.col('awardTime'))],
      order: [[Sequelize.fn('YEAR', Sequelize.col('awardTime')), 'DESC']],
      raw: true
    });
    
    // 处理结果
    const formattedStats = yearStats.map(item => ({
      year: item.year.toString(),
      count: parseInt(item.count || 0)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取年度奖励分布统计成功',
      data: formattedStats
    });
  } catch (error) {
    console.error('获取年度奖励分布统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取年度奖励分布统计失败',
      error: error.message
    });
  }
};

/**
 * 获取系/教研室分布统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDepartmentStats = async (req, res) => {
  try {
    const { 
      range = 'all',         // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all',  // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      userId                 // 用户ID，用于筛选特定用户的数据
    } = req.body;
    
    console.log("getDepartmentStats被调用，参数:", JSON.stringify({range, reviewStatus, userId}));
    
    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    
    // 构建查询条件
    const where = {
      department: {
        [Op.ne]: null,
        [Op.ne]: ''
      }
    };
    
    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {
      // 强制使用自己的ID
      const userIdToUse = userInfo.id;
      
      // 查询用户参与的奖励ID列表
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userIdToUse },
        attributes: ['awardId'],
        raw: true
      });
      
      const awardIds = participantAwards.map(p => p.awardId);
      
      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userIdToUse },
        { id: { [Op.in]: awardIds } }
      ];
      
      // 由于已经有department条件，需要使用AND组合
      where[Op.and] = [
        { department: where.department },
        { [Op.or]: userCondition }
      ];
      delete where.department;
    } 
    // 如果指定了userId且不是TEACHER-LV1角色，则只统计该用户的数据
    else if (userId) {
      // 查询用户参与的奖励ID列表
      const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
        where: { participantId: userId },
        attributes: ['awardId'],
        raw: true
      });
      
      const awardIds = participantAwards.map(p => p.awardId);
      
      // 修改查询条件为：是第一负责人或奖励ID在参与者列表中
      const userCondition = [
        { firstResponsibleId: userId },
        { id: { [Op.in]: awardIds } }
      ];
      
      // 由于已经有department条件，需要使用AND组合
      where[Op.and] = [
        { department: where.department },
        { [Op.or]: userCondition }
      ];
      delete where.department;
    }
    
    // 根据审核状态过滤
    if (reviewStatus !== 'all') {
      const reviewCondition = reviewStatus === 'reviewed' ? 
        { ifReviewer: 1 } : 
        (reviewStatus === 'rejected' ? 
          { ifReviewer: 0 } : 
          { ifReviewer: null }
        );
      
      // 如果已经有AND条件，添加到AND数组中
      if (where[Op.and]) {
        where[Op.and].push(reviewCondition);
      } else {
        // 如果已经有department条件但没有AND
        where[Op.and] = [
          { department: where.department },
          reviewCondition
        ];
        delete where.department;
      }
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingResearchAwards");
    
    // 添加时间范围过滤条件
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);
      
      if (range === 'in') {
        // 在时间范围内
        const timeCondition = {
          awardTime: {
            [Op.gte]: intervalStartTime,
            [Op.lte]: intervalEndTime
          }
        };
        
        // 如果已经有AND条件，添加到AND数组中
        if (where[Op.and]) {
          where[Op.and].push(timeCondition);
        } else {
          // 如果已经有department条件但没有AND
          where[Op.and] = [
            { department: where.department },
            timeCondition
          ];
          delete where.department;
        }
      } else if (range === 'out') {
        // 在时间范围外
        const timeCondition = {
          [Op.or]: [
            { awardTime: { [Op.lt]: intervalStartTime } },
            { awardTime: { [Op.gt]: intervalEndTime } }
          ]
        };
        
        // 如果已经有AND条件，添加到AND数组中
        if (where[Op.and]) {
          where[Op.and].push(timeCondition);
        } else {
          // 如果已经有department条件但没有AND
          where[Op.and] = [
            { department: where.department },
            timeCondition
          ];
          delete where.department;
        }
      }
    }
    
    // 使用ORM方式查询，按部门分组统计
    const departmentStats = await teachingResearchAwardsModel.findAll({
      where,
      attributes: [
        'department',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      group: ['department'],
      order: [[Sequelize.literal('count'), 'DESC']],
      raw: true
    });
    
    // 处理结果
    const formattedStats = departmentStats.map(item => ({
      department: item.department || '未知部门',
      count: parseInt(item.count || 0)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取系/教研室分布统计成功',
      data: formattedStats
    });
  } catch (error) {
    console.error('获取系/教研室分布统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取系/教研室分布统计失败',
      error: error.message
    });
  }
};
