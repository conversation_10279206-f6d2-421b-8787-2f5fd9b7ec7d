import { utils, read } from 'xlsx';
import dayjs from 'dayjs';
/**
 * 文件处理工具函数
 */

/**
 * 将数据和文件转换为FormData
 * @param {Object} data - 普通数据对象
 * @param {Array|FileList|File} files - 文件数组、FileList对象或单个文件
 * @returns {FormData} 包含数据和文件的FormData对象
 */
export const fileToFormData = (data = {}, files = null) => {
  const formData = new FormData();

  // 处理普通数据字段
  if (data && typeof data === 'object') {
    Object.keys(data).forEach(key => {
      const value = data[key];

      // 如果值是数组或对象，则转换为JSON字符串
      if (value !== null && value !== undefined) {
        if (typeof value === 'object' && !(value instanceof File)) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value);
        }
      }
    });
  }

  // 处理文件
  if (files) {
    // 如果是单个文件
    if (files instanceof File) {
      formData.append('files', files);
    }
    // 如果是FileList对象
    else if (files instanceof FileList) {
      Array.from(files).forEach(file => {
        formData.append('files', file);
      });
    }
    // 如果是数组
    else if (Array.isArray(files)) {
      files.forEach((file, index) => {
        if (file instanceof File) {
          formData.append('files', file);
        }
      });
    }
    // 如果是对象，包含了多个文件字段
    else if (typeof files === 'object') {
      Object.keys(files).forEach(fieldName => {
        const fieldFiles = files[fieldName];

        if (fieldFiles instanceof File) {
          formData.append(fieldName, fieldFiles);
        } else if (fieldFiles instanceof FileList || Array.isArray(fieldFiles)) {
          Array.from(fieldFiles).forEach(file => {
            formData.append(fieldName, file);
          });
        }
      });
    }
  }

  return formData;
};

/**
 * 获取文件扩展名
 * @param {string} fileName - 文件名
 * @returns {string} 文件扩展名（包含点，如 .jpg）
 */
export const getFileExtension = (fileName) => {
  if (!fileName) return '';
  const lastDotIndex = fileName.lastIndexOf('.');
  return lastDotIndex === -1 ? '' : fileName.substring(lastDotIndex);
};

/**
 * 获取文件名（不包含扩展名）
 * @param {string} fileName - 文件名
 * @returns {string} 文件名（不包含扩展名）
 */
export const getFileNameWithoutExtension = (fileName) => {
  if (!fileName) return '';
  const lastDotIndex = fileName.lastIndexOf('.');
  return lastDotIndex === -1 ? fileName : fileName.substring(0, lastDotIndex);
};

/**
 * 根据文件类型返回对应的图标
 * @param {string} fileName - 文件名或文件类型
 * @returns {string} 对应的图标名称或类型名称
 */
export const getFileIcon = (fileName) => {
  if (!fileName) return 'file';

  const extension = getFileExtension(fileName).toLowerCase();

  // 图片文件
  if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'].includes(extension)) {
    return 'image';
  }

  // 文档文件
  if (['.doc', '.docx', '.rtf'].includes(extension)) {
    return 'word';
  }

  // 表格文件
  if (['.xls', '.xlsx', '.csv'].includes(extension)) {
    return 'excel';
  }

  // 演示文件
  if (['.ppt', '.pptx'].includes(extension)) {
    return 'ppt';
  }

  // PDF文件
  if (extension === '.pdf') {
    return 'pdf';
  }

  // 压缩文件
  if (['.zip', '.rar', '.7z', '.tar', '.gz'].includes(extension)) {
    return 'archive';
  }

  // 音频文件
  if (['.mp3', '.wav', '.ogg', '.m4a', '.flac'].includes(extension)) {
    return 'audio';
  }

  // 视频文件
  if (['.mp4', '.avi', '.mov', '.flv', '.wmv', '.mkv'].includes(extension)) {
    return 'video';
  }

  // 代码文件
  if (['.js', '.ts', '.html', '.css', '.java', '.py', '.php', '.c', '.cpp', '.h'].includes(extension)) {
    return 'code';
  }

  // 默认类型
  return 'file';
};

/**
 * 格式化文件大小
 * @param {number} size - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (size) => {
  if (size === null || size === undefined) return '0 Bytes';

  const units = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  let i = 0;

  while (size >= 1024 && i < units.length - 1) {
    size /= 1024;
    i++;
  }

  return `${Math.round(size * 100) / 100} ${units[i]}`;
};

/**
 * 下载JSON数据为文件
 * @param {Object|Array} data - 要下载的数据对象
 * @param {string} filename - 文件名（不包括扩展名）
 * @returns {Promise<boolean>} 下载是否成功的Promise
 */
export const downloadJson = (data, filename = 'export') => {
  return new Promise((resolve, reject) => {
    try {
      console.log(`开始准备下载文件: ${filename}.json，数据条数: ${Array.isArray(data) ? data.length : 1}`);

      // 将对象转换为漂亮格式的JSON字符串
      const json = JSON.stringify(data, null, 2);
      console.log(`JSON数据已格式化，长度: ${json.length} 字符`);

      // 创建Blob对象
      const blob = new Blob([json], { type: 'application/json' });
      console.log(`已创建Blob对象，大小: ${blob.size} 字节`);

      // 尝试使用浏览器下载API（如果支持）
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // 针对IE/Edge的特殊处理
        window.navigator.msSaveOrOpenBlob(blob, `${filename}.json`);
        console.log('使用msSaveOrOpenBlob API下载');
        resolve(true);
        return;
      }

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}.json`;
      console.log(`已创建下载链接: ${link.download}`);

      // 设置链接样式，防止它显示在页面上
      link.style.display = 'none';

      // 触发下载
      document.body.appendChild(link);
      console.log('链接已添加到DOM');

      // 使用setTimeout确保DOM操作完成
      setTimeout(() => {
        try {
          console.log('开始触发点击事件');
          link.click();
          console.log('点击事件已触发');

          // 短暂延迟后再清理，确保下载已开始
          setTimeout(() => {
            try {
              // 清理
              if (document.body.contains(link)) {
                document.body.removeChild(link);
                console.log('链接已从DOM移除');
              }
              URL.revokeObjectURL(url);
              console.log('URL对象已释放');
              console.log('下载流程已完成');
              resolve(true);
            } catch (cleanupError) {
              console.error('清理下载链接时出错:', cleanupError);
              // 即使清理出错，下载可能已经成功触发
              resolve(true);
            }
          }, 200); // 增加延迟时间，确保下载开始
        } catch (clickError) {
          console.error('触发下载点击时出错:', clickError);
          reject(clickError);
        }
      }, 200); // 增加延迟时间，确保DOM更新
    } catch (error) {
      console.error('下载JSON数据出错:', error);
      reject(error);
    }
  });
};

// 自定义日期解析函数，处理各种复杂格式
const parseCustomDate = (dateStr) => {
  if (!dateStr) return null;

  // 转为字符串并去除空格
  let str = dateStr;
  if (typeof dateStr !== 'string') {
    str = String(dateStr).trim();
  } else {
    str = dateStr.trim();
  }

  console.log(`parseCustomDate 解析日期: "${str}", 类型: ${typeof dateStr}`);

  // 如果是标准日期对象，直接格式化
  if (dateStr instanceof Date && !isNaN(dateStr)) {
    const year = dateStr.getFullYear();
    const month = (dateStr.getMonth() + 1).toString().padStart(2, '0');
    const day = dateStr.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  try {
    // 处理数字类型日期
    if (typeof dateStr === 'number') {
      const numStr = dateStr.toString();

      // 如果是四位整数，可能是纯年份
      if (Number.isInteger(dateStr) && dateStr >= 1900 && dateStr <= 2100) {
        return `${dateStr}-01-01`;
      }

      // 如果数字形式是YYYY.MM格式
      if (numStr.includes('.')) {
        const parts = numStr.split('.');
        const year = parseInt(parts[0]);

        // 将小数部分转换为月份
        let month;
        if (parts[1].length === 1) {
          month = parseInt(parts[1]);
        } else if (parts[1].length === 2) {
          month = parseInt(parts[1]);
        } else {
          const decimalMonth = Math.round(parseFloat('0.' + parts[1]) * 100);
          if (decimalMonth >= 1 && decimalMonth <= 12) {
            month = decimalMonth;
          } else {
            const firstTwoDigits = parseInt(parts[1].substring(0, 2));
            const firstDigit = parseInt(parts[1].substring(0, 1));
            month = (firstTwoDigits >= 1 && firstTwoDigits <= 12) ? firstTwoDigits :
                    (firstDigit >= 1 && firstDigit <= 12) ? firstDigit : 1;
          }
        }

        // 验证是否合理的年月
        if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
          return `${year}-${month.toString().padStart(2, '0')}-01`;
        }
      }

      // 如果前面的识别都失败，则作为Excel序列号处理
      let date;
      if (dateStr < 60) {
        date = new Date(Date.UTC(1899, 11, 30 + dateStr + 1));
      } else {
        date = new Date(Date.UTC(1899, 11, 30 + dateStr));
      }

      // 避免时区问题，使用UTC日期格式
      const year = date.getUTCFullYear();
      const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
      const day = date.getUTCDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }

    // 处理字符串类型日期
    if (str) {
      // 检查是否是浮点年月格式（如2023.03）
      const yearMonthMatch = str.match(/^(\d{4})\.(\d{1,2})$/);
      if (yearMonthMatch) {
        const year = parseInt(yearMonthMatch[1]);
        const month = parseInt(yearMonthMatch[2]);
        if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
          return `${year}-${month.toString().padStart(2, '0')}-01`;
        }
      }

      // 处理中文"日月年"格式（如"1日8月2021年"）
      if (/^\d{1,2}日\d{1,2}月\d{4}年$/.test(str)) {
        try {
          const day = str.match(/^(\d{1,2})日/)[1].padStart(2, '0');
          const month = str.match(/(\d{1,2})月/)[1].padStart(2, '0');
          const year = str.match(/(\d{4})年$/)[1];
          return `${year}-${month}-${day}`;
        } catch (e) {
          console.error(`处理日月年格式失败: "${str}"`, e);
        }
      }

      // 处理中文年月日格式（如"2023年1月1日"）
      if (/^\d{4}年\d{1,2}月\d{1,2}日$/.test(str)) {
        try {
          const year = str.match(/(\d{4})年/)[1];
          const month = str.match(/(\d{1,2})月/)[1].padStart(2, '0');
          const day = str.match(/(\d{1,2})日/)[1].padStart(2, '0');
          return `${year}-${month}-${day}`;
        } catch (e) {
          console.error(`处理中文年月日格式失败: "${str}"`, e);
        }
      }

      // 处理中文年月格式（如"2023年1月"）
      if (/^\d{4}年\d{1,2}月$/.test(str)) {
        try {
          const year = str.match(/(\d{4})年/)[1];
          const month = str.match(/(\d{1,2})月/)[1].padStart(2, '0');
          return `${year}-${month}-01`;
        } catch (e) {
          console.error(`处理中文年月格式失败: "${str}"`, e);
        }
      }

      // 处理纯年份格式（如"2023年"或"2023"）
      if (/^\d{4}年?$/.test(str)) {
        try {
          const year = str.replace('年', '');
          return `${year}-01-01`;
        } catch (e) {
          console.error(`处理纯年份格式失败: "${str}"`, e);
        }
      }

      // 处理ISO标准日期格式
      const isoDateRegex = /^(\d{4})[-\/\.](\d{1,2})[-\/\.](\d{1,2})$/;
      const match = str.match(isoDateRegex);
      if (match) {
        const year = parseInt(match[1]);
        const month = parseInt(match[2]);
        const day = parseInt(match[3]);

        if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
          // 直接构造标准格式字符串，避免Date对象的时区问题
          return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        }
      }

      // 处理8位数字格式，如"20210801"
      if (/^\d{8}$/.test(str)) {
        const year = str.substring(0, 4);
        const month = str.substring(4, 6);
        const day = str.substring(6, 8);

        // 验证年月日是否合理
        if (parseInt(year) >= 1900 && parseInt(year) <= 2100 &&
            parseInt(month) >= 1 && parseInt(month) <= 12 &&
            parseInt(day) >= 1 && parseInt(day) <= 31) {
          return `${year}-${month}-${day}`;
        }
      }

      // 最后尝试，使用Date对象解析
      try {
        // 特殊处理类似2019.07这种格式
        if (/^\d{4}\.\d{1,2}$/.test(str)) {
          const parts = str.split('.');
          const year = parseInt(parts[0]);
          const month = parseInt(parts[1]);
          if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
            // 直接构造字符串，避免Date对象的时区问题
            return `${year}-${month.toString().padStart(2, '0')}-01`;
          }
        }
        else if (/^\d{4}-\d{2}-\d{2}$/.test(str)) {
          return str;
        } else {
          // 创建日期对象时尝试处理时区问题
          let date;

          // 如果日期字符串包含时区信息，使用原始字符串
          if (str.includes('T') || str.includes('Z') || str.includes('+')) {
            date = new Date(str);
          } else {
            // 否则添加T00:00:00确保使用当天开始时间而不发生偏移
            date = new Date(`${str}T00:00:00`);
          }

          if (!isNaN(date.getTime())) {
            // 使用UTC日期方法避免时区问题
            const year = date.getUTCFullYear();
            const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
            const day = date.getUTCDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
          }
        }
      } catch (parseError) {
        console.error(`Date解析失败: "${str}"`, parseError);
      }
    }

    // 无法解析时返回null
    console.warn(`无法解析日期格式: "${str}"`);
    return null;

  } catch (error) {
    console.error('日期解析错误:', error, '原始值:', dateStr);
    return null;
  }
};

/**
 * 将Excel文件转换为论文JSON格式，参照importSql.py实现
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为2（第3行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Array>} 解析后的论文数据数组
 */
export const excelToHighLevelPapersJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项，参考excelToHighLevelPapersJson的默认值
  const headerRow = options.headerRow ?? 3; // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表
  const rawValues = options.rawValues ?? true;
  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行，与pandas的read_excel方法相似
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 处理数据，与importSql.py的process_row函数处理逻辑一致
    const papers = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];

      // 跳过空行（如果论文题目为空）
      if (!row['论文题目'] || row['论文题目'] === null) {
        continue;
      }

      // 创建论文对象
      const paper = {};

      // 基本字段
      if (row['论文题目']) paper.title = String(row['论文题目']).trim();
      if (row['期刊名称']) paper.journal = String(row['期刊名称']).trim();

      // 处理日期字段 - 参照importSql.py的parse_date函数
      if (row['出版时间（写到月）'] || row['出版时间\n（写到月）'] || row['出版时间\r\n（写到月）']) {
        const dateValue = row['出版时间（写到月）'] || row['出版时间\n（写到月）'] || row['出版时间\r\n（写到月）'];
        try {
          // 记录原始日期值以便调试
          console.log(`处理日期: "${dateValue}", 类型: ${typeof dateValue}`);

          let formattedDate = null;

          // 1.1 数字类型可能是Excel序列号或YYYY.MM格式
          if ((typeof dateValue === 'number' || !isNaN(parseFloat(dateValue))) && !formattedDate) {
            const numStr = String(dateValue);
            // 检测是否类似于YYYY.MM格式
            if (numStr.includes('.')) {
              // 提取年月部分
              const parts = numStr.split('.');
              if (parts.length === 2 && parts[0].length === 4) {
                const year = parseInt(parts[0]);
                let month = null;
                if (parts[1].length >= 1) {
                  if (parts[1].length <= 2) {
                    month = parseInt(parts[1]);
                  } else {
                    const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                    if (firstTwoDigits >= 1 && firstTwoDigits <= 12) {
                      month = firstTwoDigits;
                    } else {
                      const firstDigit = parseInt(parts[1].substring(0, 1));
                      if (firstDigit >= 1 && firstDigit <= 12) {
                        month = firstDigit;
                      }
                    }
                  }
                }

                // 验证年月是否在合理范围内
                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                  // 直接构造标准格式字符串，避免Date对象的时区问题
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                  console.log(`解析为年月格式: "${numStr}" => "${formattedDate}"`);
                }
              }
            }
          }

          // 如果特殊处理没有结果，再进行标准处理
          if (!formattedDate) {
            // 1. 处理数字类型日期
        if (typeof dateValue === 'number') {
              const numStr = dateValue.toString();
              console.log(`数字日期字符串表示: "${numStr}"`);

              // 1.1 如果是四位整数，可能是纯年份（如2023）
              if (Number.isInteger(dateValue) && dateValue >= 1900 && dateValue <= 2100) {
                formattedDate = `${dateValue}-01-01`;
                console.log(`识别数字为纯年份 ${dateValue}，设置为 "${formattedDate}"`);
              }
              // 1.2 如果数字形式是YYYY.MM格式（如2023.03）
              else if (numStr.includes('.')) {
                const parts = numStr.split('.');
                const year = parseInt(parts[0]);

                // 将小数部分转换为月份
                let month;
                // 处理小数部分
                if (parts[1].length === 1) {
                  // 如 2023.3 表示3月
                  month = parseInt(parts[1]);
                } else if (parts[1].length === 2) {
                  // 如 2023.03 表示3月
                  month = parseInt(parts[1]);
                } else {
                  // 尝试从小数部分提取合理的月份
                  // 如果小数部分能转换为1-12之间的数
                  const decimalMonth = Math.round(parseFloat('0.' + parts[1]) * 100);
                  if (decimalMonth >= 1 && decimalMonth <= 12) {
                    month = decimalMonth;
                  } else {
                    // 尝试取前两位或前一位
                    const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                    const firstDigit = parseInt(parts[1].substring(0, 1));
                    month = (firstTwoDigits >= 1 && firstTwoDigits <= 12) ? firstTwoDigits :
                            (firstDigit >= 1 && firstDigit <= 12) ? firstDigit : null;
                  }
                }

                console.log(`从数字 ${dateValue} 提取: 年=${year}, 月=${month}, 小数部分="${parts[1]}"`);

                // 验证是否合理的年月
                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                  console.log(`识别为数字年月格式 ${dateValue} => "${formattedDate}"`);
                } else {
                  console.log(`数字 ${dateValue} 形式似乎是年月格式，但年(${year})或月(${month})超出合理范围`);
                }
              }

              // 1.3 如果前面的识别都失败，则作为Excel序列号处理
              if (!formattedDate) {
          // 修复Excel日期序列号计算，考虑Excel错误认为1900是闰年的问题
          let date;
          if (dateValue < 60) {
                  // 1900年1月1日之前的日期，添加1天修复日期偏移问题
                  date = new Date(Date.UTC(1899, 11, 30 + dateValue + 1));
          } else {
                  // 1900年3月1日及之后的日期，需要减去1天的闰年偏差，再加上1天修复日期偏移
                  // 原来是 -1，现在不减了，相当于 -1+1=0
                  date = new Date(Date.UTC(1899, 11, 30 + dateValue));
                }

                // 避免时区问题，使用UTC日期格式
                const year = date.getUTCFullYear();
                const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
                const day = date.getUTCDate().toString().padStart(2, '0');
                formattedDate = `${year}-${month}-${day}`;

                console.log(`处理Excel序列号 ${dateValue} 为 ${formattedDate} (UTC，已修复日期偏移)`);
              }
            }
            // 2. 处理字符串类型日期
            else if (dateValue) {
            const dateStr = String(dateValue).trim();
              console.log(`处理日期字符串: "${dateStr}"`);

              // 2.1 检查是否是浮点年月格式（如2023.03）
              if (!formattedDate) {
                // 匹配类似2023.03这样的年月格式
                const yearMonthMatch = dateStr.match(/^(\d{4})\.(\d{1,2})$/);
                if (yearMonthMatch) {
                  const year = parseInt(yearMonthMatch[1]);
                  const month = parseInt(yearMonthMatch[2]);
                  if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                    formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                    console.log(`解析年月点格式 "${dateStr}" 为 "${formattedDate}"`);
                  }
                }
              }

              // 2.2 检查浮点年月格式的另一种情况，即使有隐藏字符
              if (!formattedDate && dateStr.includes('.')) {
                // 尝试提取数字部分
                const yearMatch = dateStr.match(/(\d{4})/);
                const monthMatch = dateStr.match(/\.(\d{1,2})/);

                if (yearMatch && monthMatch) {
                  const year = parseInt(yearMatch[1]);
                  const month = parseInt(monthMatch[1]);

                  if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                    console.log(`处理点分隔日期 "${dateStr}" 为 "${formattedDate}"`);
                  }
                }
              }

              // 2.3 处理英文月份格式，如Oct-17, Jan-23
              if (!formattedDate) {
                const englishMonths = {
                  'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
                  'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
                };

                if (dateStr.includes('-')) {
                  const parts = dateStr.split('-');
                  if (parts.length === 2) {
                    const monthPart = parts[0].toLowerCase();
                    const yearOrDayPart = parts[1];

                    // 检查是否是英文月份缩写
                    for (const [monthName, monthNum] of Object.entries(englishMonths)) {
                      if (monthPart.startsWith(monthName)) {
                        let year;

                        // 判断第二部分是年份还是日期
                        if (yearOrDayPart.length === 2 && /^\d{2}$/.test(yearOrDayPart)) {
                          // 两位数年份 (如Oct-17)
                          const twoDigitYear = parseInt(yearOrDayPart);
                          year = twoDigitYear >= 50 ? 1900 + twoDigitYear : 2000 + twoDigitYear;
                          formattedDate = `${year}-${monthNum.toString().padStart(2, '0')}-01`;
                        } else if (yearOrDayPart.length === 4 && /^\d{4}$/.test(yearOrDayPart)) {
                          // 四位数年份 (如Oct-2017)
                          year = parseInt(yearOrDayPart);
                          formattedDate = `${year}-${monthNum.toString().padStart(2, '0')}-01`;
                        } else if (/^\d{1,2}$/.test(yearOrDayPart) && parseInt(yearOrDayPart) >= 1 && parseInt(yearOrDayPart) <= 31) {
                          // 日期 (如Oct-20)
                          const day = parseInt(yearOrDayPart);
                          const currentYear = new Date().getFullYear();
                          formattedDate = `${currentYear}-${monthNum.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                        }

                        if (formattedDate) {
                          console.log(`处理英文月份日期 "${dateStr}" 为 "${formattedDate}"`);
                          break;
                        }
                      }
                    }
                  }
                }
              }

              // 2.4 处理中文"日月年"格式（如"1日1月2023年"）
              if (!formattedDate && /^\d{1,2}日\d{1,2}月\d{4}年$/.test(dateStr)) {
                try {
                  const day = dateStr.match(/^(\d{1,2})日/)[1].padStart(2, '0');
              const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                  const year = dateStr.match(/(\d{4})年$/)[1];
                  formattedDate = `${year}-${month}-${day}`;
                  console.log(`处理日月年格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理日月年格式失败: "${dateStr}"`, e);
                }
              }

              // 2.5 处理中文年月日格式（如"2023年1月1日"）
              if (!formattedDate && /^\d{4}年\d{1,2}月\d{1,2}日$/.test(dateStr)) {
                try {
              const year = dateStr.match(/(\d{4})年/)[1];
              const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
              const day = dateStr.match(/(\d{1,2})日/)[1].padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
                  console.log(`处理中文年月日格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理中文年月日格式失败: "${dateStr}"`, e);
                }
              }

              // 2.6 处理中文年月格式（如"2023年1月"）
              if (!formattedDate && /^\d{4}年\d{1,2}月$/.test(dateStr)) {
                try {
                  const year = dateStr.match(/(\d{4})年/)[1];
              const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                  formattedDate = `${year}-${month}-01`;
                  console.log(`处理中文年月格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理中文年月格式失败: "${dateStr}"`, e);
                }
              }

              // 2.7 处理月-年格式 (如"06-2018")
              if (!formattedDate && /^\d{1,2}-\d{4}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('-');
                  const month = parseInt(parts[0]);
                  const year = parseInt(parts[1]);
                  if (month >= 1 && month <= 12 && year >= 1900) {
                    formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                    console.log(`处理月-年格式 "${dateStr}" 为 "${formattedDate}"`);
                  }
                } catch (e) {
                  console.error(`处理月-年格式失败: "${dateStr}"`, e);
                }
              }

              // 2.8 处理年-月格式 (如"2018-06")
              if (!formattedDate && /^\d{4}-\d{1,2}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('-');
                  const year = parseInt(parts[0]);
                  const month = parseInt(parts[1]);
                  if (month >= 1 && month <= 12 && year >= 1900) {
                    formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                    console.log(`处理年-月格式 "${dateStr}" 为 "${formattedDate}"`);
                  }
                } catch (e) {
                  console.error(`处理年-月格式失败: "${dateStr}"`, e);
                }
              }

              // 2.9 处理日/月/年格式 (如"1/6/2022")
              if (!formattedDate && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('/');
                  // 假设为日/月/年格式
                  formattedDate = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
                  console.log(`处理日/月/年格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理日/月/年格式失败: "${dateStr}"`, e);
                }
              }

              // 2.10 处理月/日/年格式 (如"6/1/2022")
              if (!formattedDate && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('/');
                  // 假设为月/日/年格式
                  formattedDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
                  console.log(`处理月/日/年格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理月/日/年格式失败: "${dateStr}"`, e);
                }
              }

              // 2.11 处理年/月格式 (如"2022/06")
              if (!formattedDate && /^\d{4}\/\d{1,2}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('/');
                  formattedDate = `${parts[0]}-${parts[1].padStart(2, '0')}-01`;
                  console.log(`处理年/月格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理年/月格式失败: "${dateStr}"`, e);
                }
              }

              // 2.12 处理月/年格式 (如"06/2022")
              if (!formattedDate && /^\d{1,2}\/\d{4}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('/');
                  formattedDate = `${parts[1]}-${parts[0].padStart(2, '0')}-01`;
                  console.log(`处理月/年格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理月/年格式失败: "${dateStr}"`, e);
                }
              }

              // 添加ISO标准日期格式处理
              if (!formattedDate) {
                const isoDateRegex = /^(\d{4})[-\/\.](\d{1,2})[-\/\.](\d{1,2})$/;
                const match = dateStr.match(isoDateRegex);
                if (match) {
                  const year = parseInt(match[1]);
                  const month = parseInt(match[2]);
                  const day = parseInt(match[3]);

                  if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                    // 直接构造标准格式字符串，避免Date对象的时区问题
                    formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                    console.log(`解析ISO标准日期格式: "${dateStr}" => "${formattedDate}"`);
                  }
                }
              }

              // 2.13 最后尝试，使用Date对象解析
              if (!formattedDate) {
                try {
                  // 添加处理类似2019.07这种格式的特殊检查
                  if (/^\d{4}\.\d{1,2}$/.test(dateStr)) {
                    const parts = dateStr.split('.');
                    const year = parseInt(parts[0]);
                    const month = parseInt(parts[1]);
                    if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                      // 直接构造字符串，避免Date对象的时区问题
                      formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                      console.log(`特殊处理年月点格式 "${dateStr}" 为 "${formattedDate}"`);
                    }
                  }
                  else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                    formattedDate = dateStr;
                    console.log(`直接使用ISO格式日期: "${dateStr}"`);
                  } else {
                    // 创建日期对象时尝试处理时区问题
                    let date;

                    // 如果日期字符串包含时区信息，使用原始字符串
                    if (dateStr.includes('T') || dateStr.includes('Z') || dateStr.includes('+')) {
                      date = new Date(dateStr);
                    } else {
                      // 否则添加T00:00:00确保使用当天开始时间而不发生偏移
                      date = new Date(`${dateStr}T00:00:00`);
                    }

              if (!isNaN(date.getTime())) {
                      // 使用UTC日期方法避免时区问题
                      const year = date.getUTCFullYear();
                      const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
                      const day = date.getUTCDate().toString().padStart(2, '0');
                      formattedDate = `${year}-${month}-${day}`;
                      console.log(`使用默认Date解析 "${dateStr}" 为 "${formattedDate}" (UTC)`);
              } else {
                      console.log(`无法使用默认Date解析: "${dateStr}"`);
                    }
                  }
                } catch (parseError) {
                  console.error(`Date解析失败: "${dateStr}"`, parseError);
                }
              }

              // 2.14 如果所有方法都失败，保留原始字符串
              if (!formattedDate) {
                formattedDate = dateStr;
                console.log(`无法解析日期，保留原始值: "${dateStr}"`);
              }
              }
            }

          // 在所有日期处理之后统一设置paper.publishDate
          if (formattedDate) {
            paper.publishDate = formattedDate;
            console.log(`最终设置论文日期值: "${paper.publishDate}"`);
          } else if (dateValue) {
            // 如果所有尝试都失败但有原始值，则保留原始值
            paper.publishDate = String(dateValue).trim();
            console.warn(`无法解析日期，使用原始值: "${paper.publishDate}"`);
          }
          } catch (e) {
            console.error(`日期解析失败: ${dateValue}`, e);
          if (dateValue) {
            paper.publishDate = String(dateValue).trim();
          }
        }
      }

      // 论文类型（收录）- 更健壮的处理方式
      // 使用多种可能的列名查找paperLevel
      const paperLevelKeys = ['收录', '收录类型', 'JCR', '论文级别'];
      let paperLevelFound = false;

      // 先尝试直接匹配
      for (const key of paperLevelKeys) {
        if (row[key] && !paperLevelFound) {
          paper.paperLevel = String(row[key]).trim();
          paperLevelFound = true;
          console.log(`找到论文级别，使用列名: "${key}", 值: "${paper.paperLevel}"`);
          break;
        }
      }

      // 如果没找到，尝试模糊匹配（部分包含）
      if (!paperLevelFound) {
        for (const colName of Object.keys(row)) {
          if (colName.includes('收录') || colName.includes('JCR') || colName.includes('级别')) {
            if (row[colName]) {
              paper.paperLevel = String(row[colName]).trim();
              paperLevelFound = true;
              console.log(`使用模糊匹配找到论文级别，列名: "${colName}", 值: "${paper.paperLevel}"`);
              break;
            }
          }
        }
      }

      // 最后，如果在常规位置（第6列）有值，可能是paperLevel
      if (!paperLevelFound) {
        const cols = Object.keys(row);
        if (cols.length >= 6) {
          const sixthColName = cols[5]; // 第6列（索引为5）
          if (row[sixthColName] && typeof row[sixthColName] === 'string' &&
              (row[sixthColName].includes('JCR') || row[sixthColName].includes('区'))) {
            paper.paperLevel = String(row[sixthColName]).trim();
            paperLevelFound = true;
            console.log(`根据位置找到论文级别，列名: "${sixthColName}", 值: "${paper.paperLevel}"`);
          }
        }
      }

      // 如果row中任何一个值包含"JCR"或"区"，可能是paperLevel
      if (!paperLevelFound) {
        for (const [key, value] of Object.entries(row)) {
          if (value && typeof value === 'string' &&
              (value.includes('JCR') || value.includes('区'))) {
            paper.paperLevel = String(value).trim();
            paperLevelFound = true;
            console.log(`在字段 "${key}" 中找到可能的论文级别: "${paper.paperLevel}"`);
            break;
          }
        }
      }

      // 记录每条数据的收录情况
      console.log(`论文 #${i+1}: "${paper.title}" - 收录情况: ${paper.paperLevel || '未找到'}`);

      // 提交人
      if (row['提交人']) paper.submitter = String(row['提交人']).trim();
      if (row['提交人排位']) paper.submitterRanking = String(row['提交人排位']).trim();

      // 通讯作者相关
      if (row['本学院通讯作者人数'] !== undefined && row['本学院通讯作者人数'] !== null) {
        paper.collegeCorrespondentAuthorNumber = Number(row['本学院通讯作者人数']) || 0;
      }
      if (row['通讯作者总人数'] !== undefined && row['通讯作者总人数'] !== null) {
        paper.correspondentAuthorNumber = Number(row['通讯作者总人数']) || 0;
      }

      // 分配比例 - 四舍五入保留两位小数
      if (row['分配比例基数（四舍五入，保留两位小数）'] !== undefined &&
          row['分配比例基数（四舍五入，保留两位小数）'] !== null) {
        const value = Number(row['分配比例基数（四舍五入，保留两位小数）']);
        paper.allocationProportionBase = value ? parseFloat(value.toFixed(2)) : 1;
      } else {
        paper.allocationProportionBase = 1;
      }

      if (row['总分配比例'] !== undefined && row['总分配比例'] !== null) {
        const value = Number(row['总分配比例']);
        paper.totalAllocationProportion = value ? parseFloat(value.toFixed(2)) : 1;
      } else {
        paper.totalAllocationProportion = 1;
      }

      // 设置状态字段，与importSql.py一致
      paper.status = 1; // 默认状态为1

      // 备注
      if (row['备注']) paper.remark = String(row['备注']).trim();

      // 影响因子
      if (row['影响因子'] !== undefined && row['影响因子'] !== null) {
        paper.impactFactor = Number(row['影响因子']) || 0;
      }

      // 引用次数
      if (row['引用次数'] !== undefined && row['引用次数'] !== null) {
        paper.citations = Number(row['引用次数']) || 0;
      }

      // 提取并整合所有作者及其分配比例，最多支持15个作者，与importSql.py一致
      const authors = [];
      const allocationRatios = [];

      // 首先获取所有作者和对应的分配比例
      for (let j = 1; j <= 15; j++) {
        const authorKey = `作者${j}`;

        if (authorKey in row && row[authorKey] !== null && row[authorKey] !== undefined) {
          authors.push(String(row[authorKey]).trim());

          // 直接查找每个作者后面的分配比例列
          // 根据提供的Excel结构，作者列之后是人事编号列，再之后是分配比例列
          const ratioKey = `分配比例`;
          const ratioIndex = Object.keys(row).indexOf(authorKey) + 2; // +2 跳过作者和人事编号列

          if (ratioIndex < Object.keys(row).length) {
            const ratioColumn = Object.keys(row)[ratioIndex];
            if (ratioColumn.includes('分配比例') && row[ratioColumn] !== null && row[ratioColumn] !== undefined) {
              const value = Number(row[ratioColumn]);
            allocationRatios.push(parseFloat(value.toFixed(2)));
            } else {
              allocationRatios.push(null);
            }
          } else {
            allocationRatios.push(null);
          }
        }
      }

      // 添加作者和分配比例数组
      if (authors.length > 0) {
        paper.authors = authors;
        paper.allocationRatios = allocationRatios;

        // 创建participants数组，与importSql.py一致
        const participants = [];
        for (let j = 0; j < authors.length; j++) {
          if (!authors[j]) continue;

          // 每个作者的参与信息
          const participant = {
            name: authors[j],
            authorRank: j + 1,
            allocationRatio: allocationRatios[j] !== undefined && allocationRatios[j] !== null
              ? allocationRatios[j]
              : (j === 0 ? 1 : 0), // 如果是第一作者且没有设置分配比例，默认为1
            isFirstAuthor: j === 0, // 第一个作者为第一作者
            isCorrespondingAuthor: authors[j] === row['提交人'] &&
              row['提交人排位'] === '通讯作者'
          };

          participants.push(participant);
        }

        paper.participants = participants;
      }

      // 检查必须字段
      if (!paper.title || !paper.journal) {
        console.log(`跳过缺少必要字段的记录: 标题=${paper.title}, 期刊=${paper.journal}`);
        continue;
      }

      // 设置默认值
      paper.isFirstAffiliationOurs = paper.isFirstAffiliationOurs !== undefined ? paper.isFirstAffiliationOurs : 1;
      paper.firstAuthorType = paper.firstAuthorType !== undefined ? paper.firstAuthorType : 1;

      // 添加到论文列表
      papers.push(paper);
    }

    console.log(`处理完成，共解析出 ${papers.length} 条论文记录`);

    return papers;

  } catch (error) {
    console.error('转换Excel到JSON出错:', error);
    throw error;
  }
};

/**
 * 将Excel文件转换为科研项目JSON数据
 * @param {File} file - Excel文件对象
 * @param {Object} options - 配置选项
 * @returns {Promise<Array>} 科研项目数据数组
 */
export const excelToResearchProjectsJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项，参考excelToHighLevelPapersJson的默认值
  const headerRow = options.headerRow ?? 3; // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表
  const rawValues = options.rawValues ?? true;

  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 处理数据，将Excel数据转换为科研项目数据格式
    const projects = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];

      console.log(`处理第 ${i+1} 行数据:`, row);

      // 检查是否使用了__EMPTY_X格式的列名
      const isEmptyFormat = Object.keys(row).some(key => key.startsWith('__EMPTY'));
      let mappedRow = row;

      // 如果使用了__EMPTY_X格式的列名，进行映射
      if (isEmptyFormat) {
        console.log(`检测到__EMPTY格式的列名，开始映射字段`);
        mappedRow = {};

        // 映射表，根据Excel文件的实际结构调整
        const fieldMapping = {
          '__EMPTY_1': '主持人',
          '__EMPTY_2': '项目编号',
          '__EMPTY_3': '项目下达部门',
          '__EMPTY_4': '项目类别',
          '__EMPTY_5': '项目名称',
          '__EMPTY_6': '获批时间（写到月）',
          '__EMPTY_7': '批准经费（万）',
          '__EMPTY_8': '级别',
          '__EMPTY_9': '执行起始年',
          '__EMPTY_10': '执行结束年',
          '__EMPTY_11': '主持人人事编号',
          '__EMPTY_12': '主持人分配比例',
          '__EMPTY_13': '承担者2',
          '__EMPTY_14': '人事编号',
          '__EMPTY_15': '分配比例',
          '__EMPTY_16': '承担者3',
          '__EMPTY_17': '人事编号_1',
          '__EMPTY_18': '分配比例_1',
          '__EMPTY_19': '承担者4',
          '__EMPTY_20': '人事编号_2',
          '__EMPTY_21': '分配比例_2',
          '__EMPTY_22': '承担者5',
          '__EMPTY_23': '人事编号_3',
          '__EMPTY_24': '分配比例_3',
          '__EMPTY_25': '承担者6',
          '__EMPTY_26': '人事编号_4',
          '__EMPTY_27': '分配比例_4',
          '__EMPTY_28': '承担者7',
          '__EMPTY_29': '人事编号_5',
          '__EMPTY_30': '分配比例_5',
          '__EMPTY_31': '承担者8',
          '__EMPTY_32': '人事编号_6',
          '__EMPTY_33': '分配比例_6',
          '__EMPTY_34': '承担者9',
          '__EMPTY_35': '人事编号_7',
          '__EMPTY_36': '分配比例_7',
          '__EMPTY_37': '承担者10',
          '__EMPTY_38': '人事编号_8',
          '__EMPTY_39': '分配比例_8',
          '__EMPTY_40': '备注',
          '__EMPTY_41': '审核人'
        };

        // 复制原始行的所有值
        Object.assign(mappedRow, row);

        // 应用映射
        for (const [emptyKey, standardKey] of Object.entries(fieldMapping)) {
          if (row[emptyKey] !== undefined) {
            mappedRow[standardKey] = row[emptyKey];
          }
        }

        console.log(`映射后的行数据:`, mappedRow);
      }

      // 跳过空行（检查多个关键字段）
      if ((!mappedRow['项目名称'] || mappedRow['项目名称'] === null) &&
          (!mappedRow['项目编号'] || mappedRow['项目编号'] === null) &&
          (!mappedRow['主持人'] || mappedRow['主持人'] === null) &&
          (!mappedRow['项目类别'] || mappedRow['项目类别'] === null)) {
        console.log(`跳过第 ${i+1} 行，关键字段为空:`, mappedRow);
        continue;
      }

      // 创建项目对象，按照数据库模型要求的字段名
      const project = {};
      console.log(`开始处理第 ${i+1} 行数据为项目`);

      // 基本字段
      if (mappedRow['项目名称']) project.name = String(mappedRow['项目名称']).trim();
      if (mappedRow['项目编号']) project.projectId = String(mappedRow['项目编号']).trim();
      if (mappedRow['项目下达部门']) project.projectIssuingDepartment = String(mappedRow['项目下达部门']).trim();
      if (mappedRow['项目类别']) project.type = String(mappedRow['项目类别']).trim();
      if (mappedRow['级别']) project.levelName = String(mappedRow['级别']).trim();
      if (mappedRow['备注']) project.remark = String(mappedRow['备注']).trim();

      // 主持人信息 - 用于前端显示
      if (mappedRow['主持人']) project.leaderName = String(mappedRow['主持人']).trim();

      // 处理获批时间 - 参考日期处理函数
      if (mappedRow['获批时间（写到月）'] || mappedRow['获批时间\n（写到月）'] || mappedRow['获批时间\r\n（写到月）']) {
        const dateValue = mappedRow['获批时间（写到月）'] || mappedRow['获批时间\n（写到月）'] || mappedRow['获批时间\r\n（写到月）'];
        try {
          console.log(`处理获批日期: "${dateValue}", 类型: ${typeof dateValue}`);

          // 使用parseCustomDate函数解析日期
          const formattedDate = parseCustomDate(dateValue);

          // 设置项目批准日期
          if (formattedDate) {
            project.approvalDate = formattedDate;
            console.log(`设置项目批准日期: "${project.approvalDate}"`);
          } else if (dateValue) {
            project.approvalDate = String(dateValue).trim();
            console.warn(`无法解析批准日期，使用原始值: "${project.approvalDate}"`);
          }
        } catch (e) {
          console.error(`日期解析失败: ${dateValue}`, e);
          if (dateValue) {
            project.approvalDate = String(dateValue).trim();
          }
        }
      }

      // 处理执行起始年和结束年
      if (mappedRow['执行起始年']) {
        const startYear = mappedRow['执行起始年'];
        project.startDate = parseCustomDate(startYear) || String(startYear).trim();
      }

      if (mappedRow['执行结束年']) {
        const endYear = mappedRow['执行结束年'];
        // 对于结束年，应该设置为该年的最后一天
        if (typeof endYear === 'number' || !isNaN(parseInt(endYear))) {
          const year = parseInt(endYear);
          project.endDate = `${year}-12-31`;
        } else {
          project.endDate = String(endYear).trim();
        }
      }

      // 处理批准经费
      if (mappedRow['批准经费（万）']) {
        const fundingValue = mappedRow['批准经费（万）'];
        if (typeof fundingValue === 'number' || !isNaN(parseFloat(fundingValue))) {
          project.fundingAmount = parseFloat(fundingValue);
        } else {
          // 尝试从字符串中提取数字
          const match = String(fundingValue).match(/(\d+(\.\d+)?)/);
          project.fundingAmount = match ? parseFloat(match[1]) : 0;
        }
        console.log(`设置项目经费: ${project.fundingAmount}万元`);
      }

      // 初始化参与者数组
      project.participants = [];

      // 添加主持人到参与者列表中
      if (mappedRow['主持人']) {
        const leaderAllocationRatio = mappedRow['主持人分配比例']
          ? parseFloat(parseFloat(mappedRow['主持人分配比例']).toFixed(2))
          : 100;

        // 添加主持人作为第一位参与者
        const leader = {
          name: String(mappedRow['主持人']).trim(),
          studentNumber: mappedRow['主持人人事编号'] ? String(mappedRow['主持人人事编号']).trim() : '',
          allocationRatio: leaderAllocationRatio,
          participantRank: 1,
          isLeader: true
        };
        project.participants.push(leader);
        console.log(`设置项目主持人: "${project.leaderName}", 分配比例: ${leaderAllocationRatio}%`);
      }

      // 处理其他承担者（从承担者2开始）
      for (let j = 2; j <= 10; j++) {
        const nameKey = `承担者${j}`;
        const idKey = `人事编号`;
        const ratioKey = `分配比例`;

        // 判断该承担者的姓名是否存在
        if (mappedRow[nameKey] && mappedRow[nameKey] !== null) {
          // 查找对应的人事编号和分配比例列
          let studentNumber = '';
          let allocationRatio = 0;

          // 尝试找到对应的人事编号列
          const idKeyIndex = Object.keys(mappedRow).indexOf(nameKey) + 1;
          if (idKeyIndex < Object.keys(mappedRow).length) {
            const idColumnKey = Object.keys(mappedRow)[idKeyIndex];
            if (idColumnKey.includes(idKey) && mappedRow[idColumnKey]) {
              studentNumber = String(mappedRow[idColumnKey]).trim();
            }
          }

          // 尝试找到对应的分配比例列
          const ratioKeyIndex = Object.keys(mappedRow).indexOf(nameKey) + 2;
          if (ratioKeyIndex < Object.keys(mappedRow).length) {
            const ratioColumnKey = Object.keys(mappedRow)[ratioKeyIndex];
            if (ratioColumnKey.includes(ratioKey) && mappedRow[ratioColumnKey]) {
              // 保留原始百分比值，只保留两位小数
              allocationRatio = parseFloat(parseFloat(mappedRow[ratioColumnKey]).toFixed(2));
            }
          }

          // 添加承担者
          project.participants.push({
            name: String(mappedRow[nameKey]).trim(),
            studentNumber: studentNumber,
            allocationRatio: allocationRatio,
            participantRank: j,
            isLeader: false
          });

          console.log(`添加项目参与者: "${mappedRow[nameKey]}", 排名: ${j}, 分配比例: ${allocationRatio}%`);
        }
      }

      // 设置默认值
      project.isUniversityFirstUnit = true; // 默认为第一单位是我们大学
      project.isCollegeFirstUnit = true; // 默认为学院是第一单位
      project.status = 1; // 默认状态为1(进行中)

      // 默认审批部门（如果Excel中没有这个字段）
      project.approvalDepartment = '暨南大学';

      // 添加到项目列表
      projects.push(project);
    }

    console.log(`处理完成，共解析出 ${projects.length} 条项目记录`);

    // 返回格式调整为与后端API匹配
    return {
      data: projects,
      total: data.length,
      success: projects.length,
      failed: data.length - projects.length,
      ignored: data.length - projects.length
    };
  } catch (error) {
    console.error('转换Excel到科研项目JSON出错:', error);
    throw error;
  }
};

/**
 * 将Excel文件转换为专利JSON格式
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为2（第3行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Array>} 解析后的专利数据数组
 */
export const excelToPatentsJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项
  const headerRow = options.headerRow ?? 2; // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表

  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 处理数据
    const patents = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];

      // 跳过空行（如果专利名称为空）
      if (!row['专利名称'] || row['专利名称'] === null) {
        continue;
      }

      // 创建专利对象
      const patent = {};

      // 基本字段
      if (row['专利名称']) patent.patentName = String(row['专利名称']).trim();
      if (row['专利分类']) patent.categoryName = String(row['专利分类']).trim();

      // 处理授权日期
      if (row['授权时间（写到月份）'] || row['授权时间\n（写到月份）'] || row['授权时间\r\n（写到月份）']) {
        const dateValue = row['授权时间（写到月份）'] || row['授权时间\n（写到月份）'] || row['授权时间\r\n（写到月份）'];
        try {
          console.log(`处理授权日期: "${dateValue}", 类型: ${typeof dateValue}`);

          let formattedDate = null;

          // 处理数字类型日期（年月格式如2023.03或Excel序列号）
          if ((typeof dateValue === 'number' || !isNaN(parseFloat(dateValue))) && !formattedDate) {
            const numStr = String(dateValue);
      if (numStr.includes('.')) {
        const parts = numStr.split('.');
              if (parts.length === 2 && parts[0].length === 4) {
                const year = parseInt(parts[0]);
                let month = null;
                if (parts[1].length >= 1) {
                  if (parts[1].length <= 2) {
                    month = parseInt(parts[1]);
                  } else {
                    const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                    if (firstTwoDigits >= 1 && firstTwoDigits <= 12) {
                      month = firstTwoDigits;
                    } else {
                      const firstDigit = parseInt(parts[1].substring(0, 1));
                      if (firstDigit >= 1 && firstDigit <= 9) {
                        month = firstDigit;
                      }
                    }
                  }
                }

                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                  console.log(`解析为年月格式: "${numStr}" => "${formattedDate}"`);
                }
              }
            }
          }

          // 如果特殊处理没有结果，再进行标准处理
          if (!formattedDate) {
    // 处理数字类型日期
            if (typeof dateValue === 'number') {
              const numStr = dateValue.toString();
              console.log(`数字日期字符串表示: "${numStr}"`);

              // 如果是四位整数，可能是纯年份（如2023）
              if (Number.isInteger(dateValue) && dateValue >= 1900 && dateValue <= 2100) {
                formattedDate = `${dateValue}-01-01`;
                console.log(`识别数字为纯年份 ${dateValue}，设置为 "${formattedDate}"`);
              }
              // 如果数字形式是YYYY.MM格式（如2023.03）
              else if (numStr.includes('.')) {
                const parts = numStr.split('.');
        const year = parseInt(parts[0]);

        // 将小数部分转换为月份
        let month;
                // 处理小数部分
        if (parts[1].length === 1) {
                  // 如 2023.3 表示3月
          month = parseInt(parts[1]);
        } else if (parts[1].length === 2) {
                  // 如 2023.03 表示3月
          month = parseInt(parts[1]);
        } else {
                  // 尝试从小数部分提取合理的月份
          const decimalMonth = Math.round(parseFloat('0.' + parts[1]) * 100);
          if (decimalMonth >= 1 && decimalMonth <= 12) {
            month = decimalMonth;
          } else {
                    // 尝试取前两位或前一位
            const firstTwoDigits = parseInt(parts[1].substring(0, 2));
            const firstDigit = parseInt(parts[1].substring(0, 1));
            month = (firstTwoDigits >= 1 && firstTwoDigits <= 12) ? firstTwoDigits :
                            (firstDigit >= 1 && firstDigit <= 12) ? firstDigit : null;
          }
        }

                console.log(`从数字 ${dateValue} 提取: 年=${year}, 月=${month}, 小数部分="${parts[1]}"`);

        // 验证是否合理的年月
        if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                  console.log(`识别为数字年月格式 ${dateValue} => "${formattedDate}"`);
                } else {
                  console.log(`数字 ${dateValue} 形式似乎是年月格式，但年(${year})或月(${month})超出合理范围`);
        }
      }

      // 如果前面的识别都失败，则作为Excel序列号处理
              if (!formattedDate) {
                // 修复Excel日期序列号计算，考虑Excel错误认为1900是闰年的问题
      let date;
                if (dateValue < 60) {
                  // 1900年1月1日之前的日期，添加1天修复日期偏移问题
                  date = new Date(Date.UTC(1899, 11, 30 + dateValue + 1));
      } else {
                  // 1900年3月1日及之后的日期，需要减去1天的闰年偏差，再加上1天修复日期偏移
                  date = new Date(Date.UTC(1899, 11, 30 + dateValue));
      }

      // 避免时区问题，使用UTC日期格式
      const year = date.getUTCFullYear();
      const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
      const day = date.getUTCDate().toString().padStart(2, '0');
                formattedDate = `${year}-${month}-${day}`;

                console.log(`处理Excel序列号 ${dateValue} 为 ${formattedDate} (UTC，已修复日期偏移)`);
              }
            }
    // 处理字符串类型日期
            else if (dateValue) {
              const dateStr = String(dateValue).trim();
              console.log(`处理日期字符串: "${dateStr}"`);

      // 检查是否是浮点年月格式（如2023.03）
              if (!formattedDate) {
                // 匹配类似2023.03这样的年月格式
                const yearMonthMatch = dateStr.match(/^(\d{4})\.(\d{1,2})$/);
      if (yearMonthMatch) {
        const year = parseInt(yearMonthMatch[1]);
        const month = parseInt(yearMonthMatch[2]);
        if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                    formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                    console.log(`解析年月点格式 "${dateStr}" 为 "${formattedDate}"`);
                  }
                }
              }

              // 2.2 检查浮点年月格式的另一种情况，即使有隐藏字符
              if (!formattedDate && dateStr.includes('.')) {
                // 尝试提取数字部分
                const yearMatch = dateStr.match(/(\d{4})/);
                const monthMatch = dateStr.match(/\.(\d{1,2})/);

                if (yearMatch && monthMatch) {
                  const year = parseInt(yearMatch[1]);
                  const month = parseInt(monthMatch[1]);

                  if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                    console.log(`处理点分隔日期 "${dateStr}" 为 "${formattedDate}"`);
                  }
                }
              }

              // 2.3 处理英文月份格式，如Oct-17, Jan-23
              if (!formattedDate) {
                const englishMonths = {
                  'jan': 1, 'feb': 2, 'mar': 3, 'apr': 4, 'may': 5, 'jun': 6,
                  'jul': 7, 'aug': 8, 'sep': 9, 'oct': 10, 'nov': 11, 'dec': 12
                };

                if (dateStr.includes('-')) {
                  const parts = dateStr.split('-');
                  if (parts.length === 2) {
                    const monthPart = parts[0].toLowerCase();
                    const yearOrDayPart = parts[1];

                    // 检查是否是英文月份缩写
                    for (const [monthName, monthNum] of Object.entries(englishMonths)) {
                      if (monthPart.startsWith(monthName)) {
                        let year;

                        // 判断第二部分是年份还是日期
                        if (yearOrDayPart.length === 2 && /^\d{2}$/.test(yearOrDayPart)) {
                          // 两位数年份 (如Oct-17)
                          const twoDigitYear = parseInt(yearOrDayPart);
                          year = twoDigitYear >= 50 ? 1900 + twoDigitYear : 2000 + twoDigitYear;
                          formattedDate = `${year}-${monthNum.toString().padStart(2, '0')}-01`;
                        } else if (yearOrDayPart.length === 4 && /^\d{4}$/.test(yearOrDayPart)) {
                          // 四位数年份 (如Oct-2017)
                          year = parseInt(yearOrDayPart);
                          formattedDate = `${year}-${monthNum.toString().padStart(2, '0')}-01`;
                        } else if (/^\d{1,2}$/.test(yearOrDayPart) && parseInt(yearOrDayPart) >= 1 && parseInt(yearOrDayPart) <= 31) {
                          // 日期 (如Oct-20)
                          const day = parseInt(yearOrDayPart);
                          const currentYear = new Date().getFullYear();
                          formattedDate = `${currentYear}-${monthNum.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                        }

                        if (formattedDate) {
                          console.log(`处理英文月份日期 "${dateStr}" 为 "${formattedDate}"`);
                          break;
                        }
                      }
                    }
                  }
                }
              }

              // 2.4 处理中文"日月年"格式（如"1日1月2023年"）
              if (!formattedDate && /^\d{1,2}日\d{1,2}月\d{4}年$/.test(dateStr)) {
                try {
                  const day = dateStr.match(/^(\d{1,2})日/)[1].padStart(2, '0');
              const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                  const year = dateStr.match(/(\d{4})年$/)[1];
                  formattedDate = `${year}-${month}-${day}`;
                  console.log(`处理日月年格式 "${dateStr}" 为 "${formattedDate}"`);
        } catch (e) {
                  console.error(`处理日月年格式失败: "${dateStr}"`, e);
                }
              }

              // 2.5 处理中文年月日格式（如"2023年1月1日"）
              if (!formattedDate && /^\d{4}年\d{1,2}月\d{1,2}日$/.test(dateStr)) {
                try {
              const year = dateStr.match(/(\d{4})年/)[1];
              const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
              const day = dateStr.match(/(\d{1,2})日/)[1].padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
                  console.log(`处理中文年月日格式 "${dateStr}" 为 "${formattedDate}"`);
        } catch (e) {
                  console.error(`处理中文年月日格式失败: "${dateStr}"`, e);
                }
              }

              // 2.6 处理中文年月格式（如"2023年1月"）
              if (!formattedDate && /^\d{4}年\d{1,2}月$/.test(dateStr)) {
                try {
                  const year = dateStr.match(/(\d{4})年/)[1];
              const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                  formattedDate = `${year}-${month}-01`;
                  console.log(`处理中文年月格式 "${dateStr}" 为 "${formattedDate}"`);
        } catch (e) {
                  console.error(`处理中文年月格式失败: "${dateStr}"`, e);
                }
              }

              // 2.7 处理月-年格式 (如"06-2018")
              if (!formattedDate && /^\d{1,2}-\d{4}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('-');
                  const month = parseInt(parts[0]);
                  const year = parseInt(parts[1]);
                  if (month >= 1 && month <= 12 && year >= 1900) {
                    formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                    console.log(`处理月-年格式 "${dateStr}" 为 "${formattedDate}"`);
                  }
        } catch (e) {
                  console.error(`处理月-年格式失败: "${dateStr}"`, e);
                }
              }

              // 2.8 处理年-月格式 (如"2018-06")
              if (!formattedDate && /^\d{4}-\d{1,2}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('-');
                  const year = parseInt(parts[0]);
                  const month = parseInt(parts[1]);
                  if (month >= 1 && month <= 12 && year >= 1900) {
                    formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                    console.log(`处理年-月格式 "${dateStr}" 为 "${formattedDate}"`);
                  }
                } catch (e) {
                  console.error(`处理年-月格式失败: "${dateStr}"`, e);
                }
              }

              // 2.9 处理日/月/年格式 (如"1/6/2022")
              if (!formattedDate && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('/');
                  // 假设为日/月/年格式
                  formattedDate = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
                  console.log(`处理日/月/年格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理日/月/年格式失败: "${dateStr}"`, e);
                }
              }

              // 2.10 处理月/日/年格式 (如"6/1/2022")
              if (!formattedDate && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('/');
                  // 假设为月/日/年格式
                  formattedDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
                  console.log(`处理月/日/年格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理月/日/年格式失败: "${dateStr}"`, e);
                }
              }

              // 2.11 处理年/月格式 (如"2022/06")
              if (!formattedDate && /^\d{4}\/\d{1,2}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('/');
                  formattedDate = `${parts[0]}-${parts[1].padStart(2, '0')}-01`;
                  console.log(`处理年/月格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理年/月格式失败: "${dateStr}"`, e);
                }
              }

              // 2.12 处理月/年格式 (如"06/2022")
              if (!formattedDate && /^\d{1,2}\/\d{4}$/.test(dateStr)) {
                try {
                  const parts = dateStr.split('/');
                  formattedDate = `${parts[1]}-${parts[0].padStart(2, '0')}-01`;
                  console.log(`处理月/年格式 "${dateStr}" 为 "${formattedDate}"`);
                } catch (e) {
                  console.error(`处理月/年格式失败: "${dateStr}"`, e);
                }
              }

              // 添加ISO标准日期格式处理
              if (!formattedDate) {
      const isoDateRegex = /^(\d{4})[-\/\.](\d{1,2})[-\/\.](\d{1,2})$/;
                const match = dateStr.match(isoDateRegex);
      if (match) {
        const year = parseInt(match[1]);
        const month = parseInt(match[2]);
        const day = parseInt(match[3]);

        if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                    // 直接构造标准格式字符串，避免Date对象的时区问题
                    formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                    console.log(`解析ISO标准日期格式: "${dateStr}" => "${formattedDate}"`);
                  }
                }
              }

              // 2.13 最后尝试，使用Date对象解析
              if (!formattedDate) {
                try {
                  // 添加处理类似2019.07这种格式的特殊检查
                  if (/^\d{4}\.\d{1,2}$/.test(dateStr)) {
                    const parts = dateStr.split('.');
                    const year = parseInt(parts[0]);
                    const month = parseInt(parts[1]);
                    if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                      // 直接构造字符串，避免Date对象的时区问题
                      formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                      console.log(`特殊处理年月点格式 "${dateStr}" 为 "${formattedDate}"`);
                    }
                  }
                  else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                    formattedDate = dateStr;
                    console.log(`直接使用ISO格式日期: "${dateStr}"`);
                  } else {
                    // 创建日期对象时尝试处理时区问题
                    let date;

                    // 如果日期字符串包含时区信息，使用原始字符串
                    if (dateStr.includes('T') || dateStr.includes('Z') || dateStr.includes('+')) {
                      date = new Date(dateStr);
                    } else {
                      // 否则添加T00:00:00确保使用当天开始时间而不发生偏移
                      date = new Date(`${dateStr}T00:00:00`);
                    }

              if (!isNaN(date.getTime())) {
                      // 使用UTC日期方法避免时区问题
                      const year = date.getUTCFullYear();
                      const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
                      const day = date.getUTCDate().toString().padStart(2, '0');
                      formattedDate = `${year}-${month}-${day}`;
                      console.log(`使用默认Date解析 "${dateStr}" 为 "${formattedDate}" (UTC)`);
              } else {
                      console.log(`无法使用默认Date解析: "${dateStr}"`);
                    }
                  }
                } catch (parseError) {
                  console.error(`Date解析失败: "${dateStr}"`, parseError);
                }
              }

              // 2.14 如果所有方法都失败，保留原始字符串
              if (!formattedDate) {
                formattedDate = dateStr;
                console.log(`无法解析日期，保留原始值: "${dateStr}"`);
              }
              }
            }

          // 在所有日期处理之后统一设置patent.authorizationDate
          if (formattedDate) {
            patent.authorizationDate = formattedDate;
            console.log(`最终设置专利日期值: "${patent.authorizationDate}"`);
          } else if (dateValue) {
            // 如果所有尝试都失败但有原始值，则保留原始值
            patent.authorizationDate = String(dateValue).trim();
            console.warn(`无法解析日期，使用原始值: "${patent.authorizationDate}"`);
          }
          } catch (e) {
            console.error(`日期解析失败: ${dateValue}`, e);
          if (dateValue) {
            patent.authorizationDate = String(dateValue).trim();
          }
        }
      }

      // 处理转化日期
      if (row['转化时间（写到月份）'] || row['转化时间\n（写到月份）'] || row['转化时间\r\n（写到月份）'] || row['转化时间↵（写到月份）']) {
        const dateValue = row['转化时间（写到月份）'] || row['转化时间\n（写到月份）'] || row['转化时间\r\n（写到月份）'] || row['转化时间↵（写到月份）'];
        try {
          console.log(`处理转化日期: "${dateValue}", 类型: ${typeof dateValue}`);

          let formattedDate = null;

          // 处理数字类型日期（年月格式如2023.03或Excel序列号）
          if ((typeof dateValue === 'number' || !isNaN(parseFloat(dateValue))) && !formattedDate) {
            const numStr = String(dateValue);
            if (numStr.includes('.')) {
              const parts = numStr.split('.');
              if (parts.length === 2 && parts[0].length === 4) {
                const year = parseInt(parts[0]);
                let month = null;
                if (parts[1].length >= 1) {
                  if (parts[1].length <= 2) {
                    month = parseInt(parts[1]);
                  } else {
                    const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                    if (firstTwoDigits >= 1 && firstTwoDigits <= 12) {
                      month = firstTwoDigits;
                    } else {
                      const firstDigit = parseInt(parts[1].substring(0, 1));
                      if (firstDigit >= 1 && firstDigit <= 9) {
                        month = firstDigit;
                      }
                    }
                  }
                }

                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                  console.log(`解析为年月格式: "${numStr}" => "${formattedDate}"`);
                }
              }
            }
          }

          // 如果特殊处理没有结果，再进行标准处理
          if (!formattedDate) {
            // 处理数字类型日期
            if (typeof dateValue === 'number') {
              // ... using the same approach as for authorization date ...
              // 使用Excel序列号处理
              let date;
              if (dateValue < 60) {
                // 1900年1月1日之前的日期，添加1天修复日期偏移问题
                date = new Date(Date.UTC(1899, 11, 30 + dateValue + 1));
              } else {
                // 1900年3月1日及之后的日期，需要减去1天的闰年偏差，再加上1天修复日期偏移
                date = new Date(Date.UTC(1899, 11, 30 + dateValue));
              }

              // 避免时区问题，使用UTC日期格式
              const year = date.getUTCFullYear();
              const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
              const day = date.getUTCDate().toString().padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;

              console.log(`处理Excel序列号 ${dateValue} 为 ${formattedDate} (UTC，已修复日期偏移)`);
            }
          }

          // 在所有日期处理之后统一设置patent.conversionDate
          if (formattedDate) {
            patent.conversionDate = formattedDate;
            console.log(`最终设置转化日期值: "${patent.conversionDate}"`);
          } else if (dateValue) {
            // 如果所有尝试都失败但有原始值，则保留原始值
            patent.conversionDate = String(dateValue).trim();
            console.warn(`无法解析转化日期，使用原始值: "${patent.conversionDate}"`);
          }
        } catch (e) {
          console.error(`转化日期解析失败: ${dateValue}`, e);
          if (dateValue) {
            patent.conversionDate = String(dateValue).trim();
          }
        }
      }

      // 论文类型（收录）- 更健壮的处理方式
      // 使用多种可能的列名查找paperLevel
      const paperLevelKeys = ['收录', '收录类型', 'JCR', '论文级别'];
      let paperLevelFound = false;

      // 先尝试直接匹配
      for (const key of paperLevelKeys) {
        if (row[key] && !paperLevelFound) {
          patent.paperLevel = String(row[key]).trim();
          paperLevelFound = true;
          console.log(`找到论文级别，使用列名: "${key}", 值: "${patent.paperLevel}"`);
          break;
        }
      }

      // 如果没找到，尝试模糊匹配（部分包含）
      if (!paperLevelFound) {
        for (const colName of Object.keys(row)) {
          if (colName.includes('收录') || colName.includes('JCR') || colName.includes('级别')) {
            if (row[colName]) {
              patent.paperLevel = String(row[colName]).trim();
              paperLevelFound = true;
              console.log(`使用模糊匹配找到论文级别，列名: "${colName}", 值: "${patent.paperLevel}"`);
              break;
            }
          }
        }
      }

      // 最后，如果在常规位置（第6列）有值，可能是paperLevel
      if (!paperLevelFound) {
        const cols = Object.keys(row);
        if (cols.length >= 6) {
          const sixthColName = cols[5]; // 第6列（索引为5）
          if (row[sixthColName] && typeof row[sixthColName] === 'string' &&
              (row[sixthColName].includes('JCR') || row[sixthColName].includes('区'))) {
            patent.paperLevel = String(row[sixthColName]).trim();
            paperLevelFound = true;
            console.log(`根据位置找到论文级别，列名: "${sixthColName}", 值: "${patent.paperLevel}"`);
          }
        }
      }

      // 如果row中任何一个值包含"JCR"或"区"，可能是paperLevel
      if (!paperLevelFound) {
        for (const [key, value] of Object.entries(row)) {
          if (value && typeof value === 'string' &&
              (value.includes('JCR') || value.includes('区'))) {
            patent.paperLevel = String(value).trim();
            paperLevelFound = true;
            console.log(`在字段 "${key}" 中找到可能的论文级别: "${patent.paperLevel}"`);
            break;
          }
        }
      }

      // 记录每条数据的收录情况
      console.log(`论文 #${i+1}: "${patent.patentName}" - 收录情况: ${patent.paperLevel || '未找到'}`);

      // 提交人
      if (row['提交人']) patent.submitter = String(row['提交人']).trim();
      if (row['提交人排位']) patent.submitterRanking = String(row['提交人排位']).trim();

      // 通讯作者相关
      if (row['本学院通讯作者人数'] !== undefined && row['本学院通讯作者人数'] !== null) {
        patent.collegeCorrespondentAuthorNumber = Number(row['本学院通讯作者人数']) || 0;
      }
      if (row['通讯作者总人数'] !== undefined && row['通讯作者总人数'] !== null) {
        patent.correspondentAuthorNumber = Number(row['通讯作者总人数']) || 0;
      }

      // 分配比例 - 四舍五入保留两位小数
      if (row['分配比例基数（四舍五入，保留两位小数）'] !== undefined &&
          row['分配比例基数（四舍五入，保留两位小数）'] !== null) {
        const value = Number(row['分配比例基数（四舍五入，保留两位小数）']);
        patent.allocationProportionBase = value ? parseFloat(value.toFixed(2)) : 1;
      } else {
        patent.allocationProportionBase = 1;
      }

      if (row['总分配比例'] !== undefined && row['总分配比例'] !== null) {
        const value = Number(row['总分配比例']);
        patent.totalAllocationProportion = value ? parseFloat(value.toFixed(2)) : 1;
      } else {
        patent.totalAllocationProportion = 1;
      }

      // 设置状态字段，与importSql.py一致
      patent.status = 1; // 默认状态为1

      // 备注
      if (row['备注']) patent.remark = String(row['备注']).trim();

      // 影响因子
      if (row['影响因子'] !== undefined && row['影响因子'] !== null) {
        patent.impactFactor = Number(row['影响因子']) || 0;
      }

      // 引用次数
      if (row['引用次数'] !== undefined && row['引用次数'] !== null) {
        patent.citations = Number(row['引用次数']) || 0;
      }

      // 提取并整合所有作者及其分配比例，最多支持15个作者，与importSql.py一致
      const authors = [];
      const allocationRatios = [];

      // 获取第一负责人信息
      let firstResponsibleKey = '第一负责人';
      // 尝试寻找可能的第一负责人列名变体
      if (!row[firstResponsibleKey]) {
        // 检查列名中包含"第一负责人"的列
        firstResponsibleKey = Object.keys(row).find(key =>
          key === '第一负责人' ||
          key === '第一负责人\n' ||
          key === '第一负责人\r\n' ||
          key === '第一负责人↵' ||
          key.includes('第一负责人')
        ) || '第一负责人';
      }

      const firstResponsible = row[firstResponsibleKey] ? String(row[firstResponsibleKey]).trim() : null;
      console.log(`获取到第一负责人 (列名: "${firstResponsibleKey}"): "${firstResponsible}"`);

      // 首先获取所有参与者和对应的分配比例
      for (let j = 1; j <= 15; j++) {
        const participantKey = `参与者${j}`;

        if (participantKey in row && row[participantKey] !== null && row[participantKey] !== undefined) {
          const participantName = String(row[participantKey]).trim();
          authors.push(participantName);
          console.log(`找到参与者${j}: "${participantName}"`);

          // 直接查找每个参与者后面的分配比例列
          // 根据提供的Excel结构，参与者列之后是人事编号列，再之后是分配比例列
          const ratioKey = `分配比例`;
          const ratioIndex = Object.keys(row).indexOf(participantKey) + 2; // +2 跳过参与者和人事编号列

          if (ratioIndex < Object.keys(row).length) {
            const ratioColumn = Object.keys(row)[ratioIndex];
            if (ratioColumn.includes('分配比例') && row[ratioColumn] !== null && row[ratioColumn] !== undefined) {
              const value = Number(row[ratioColumn]);
              const formattedValue = parseFloat(value.toFixed(2));
              allocationRatios.push(formattedValue);
              console.log(`参与者${j}的分配比例: ${formattedValue}`);
            } else {
              allocationRatios.push(null);
              console.log(`参与者${j}的分配比例未找到，设为null`);
            }
          } else {
            allocationRatios.push(null);
            console.log(`参与者${j}的分配比例列不存在，设为null`);
          }
        }
      }

      // 添加作者和分配比例数组
      if (authors.length > 0 || firstResponsible) {
        console.log(`共找到${authors.length}个参与者，第一负责人: ${firstResponsible || '无'}`);
        patent.authors = authors;
        patent.allocationRatios = allocationRatios;

        // 创建participants数组
        const participants = [];
        for (let j = 0; j < authors.length; j++) {
          if (!authors[j]) continue;

          // 检查是否是第一负责人
          const isLeader = firstResponsible && authors[j] === firstResponsible;
          if (isLeader) {
            console.log(`参与者 "${authors[j]}" 与第一负责人匹配，标记为负责人`);
          }

          // 每个作者的参与信息
          const participant = {
            name: authors[j],
            authorRank: j + 1,
            allocationRatio: allocationRatios[j] !== undefined && allocationRatios[j] !== null
              ? allocationRatios[j]
              : (j === 0 ? 1 : 0), // 如果是第一作者且没有设置分配比例，默认为1
            isLeader: isLeader, // 如果与第一负责人同名，则设置为负责人
            isFirstAuthor: j === 0 // 第一个作者为第一作者
          };

          participants.push(participant);
          console.log(`添加参与者: ${participant.name}, 排名: ${participant.authorRank}, 分配比例: ${participant.allocationRatio}, 是否负责人: ${participant.isLeader}`);
        }

        // 如果第一负责人不在参与者列表中，且存在第一负责人，则添加到参与者列表中
        if (firstResponsible) {
          const firstResponsibleExists = participants.some(p => p.name === firstResponsible);

          if (!firstResponsibleExists) {
            console.log(`第一负责人 "${firstResponsible}" 不在参与者列表中，将其添加为单独参与者`);
            participants.push({
              name: firstResponsible,
              authorRank: participants.length + 1,
              allocationRatio: null, // 保留原始分配比例
              isLeader: true,
              isFirstAuthor: participants.length === 0 // 如果没有其他参与者，则同时是第一作者
            });
          } else {
            console.log(`第一负责人 "${firstResponsible}" 已在参与者列表中，不需要重复添加`);
          }
        }

        patent.participants = participants;
        console.log(`最终参与者列表包含 ${participants.length} 人`);

        // 保存第一负责人信息到专利对象中（如果有）
        if (firstResponsible) {
          patent.firstResponsible = firstResponsible;
          console.log(`将第一负责人 "${firstResponsible}" 设置到专利对象中`);
        }
      }

      // 检查必须字段
      if (!patent.patentName || !patent.categoryName) {
        console.log(`跳过缺少必要字段的记录: 专利名称=${patent.patentName}, 专利分类=${patent.categoryName}`);
        continue;
      }

      // 设置默认值
      patent.isFirstAffiliationOurs = patent.isFirstAffiliationOurs !== undefined ? patent.isFirstAffiliationOurs : 1;
      patent.firstAuthorType = patent.firstAuthorType !== undefined ? patent.firstAuthorType : 1;

      // 添加到专利列表
      patents.push(patent);
    }

    console.log(`处理完成，共解析出 ${patents.length} 条专利记录`);

    return patents;

  } catch (error) {
    console.error('转换Excel到JSON出错:', error);
    throw error;
  }
};

/**
 * 将Excel文件转换为教学改革项目JSON格式
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为3（第4行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Array>} 解析后的教学改革项目数据数组
 */
export const excelToTeachingReformProjectsJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项
  const headerRow = options.headerRow ?? 3; // 默认第4行为表头（索引为3）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表

  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 跳过第四行（索引为0的那一行，因为从headerRow开始读取的）
    const projectsData = data.slice(1);

    // 处理数据
    const projects = [];

    for (let i = 0; i < projectsData.length; i++) {
      const row = projectsData[i];

      // 跳过空行（如果项目名称为空）
      if (!row['项目名称'] || row['项目名称'] === null) {
        continue;
      }

      // 创建项目对象
      const project = {};

      // 基本字段
      if (row['项目编号']) project.projectNumber = String(row['项目编号']).trim();
      if (row['项目名称']) project.projectName = String(row['项目名称']).trim();
      if (row['项目下达部门']) project.approvalDepartment = String(row['项目下达部门']).trim();
      if (row['级别']) project.categoryName = String(row['级别']).trim();

      // 处理批准经费
      if (row['批准经费（万）'] !== undefined && row['批准经费（万）'] !== null) {
        const fundValue = Number(row['批准经费（万）']);
        project.approvalFund = isNaN(fundValue) ? 0 : fundValue;
      }

      // 处理获批时间 - 增强处理不同列名的能力
      const approvalDateKeys = [
        '获批时间（写到月）',
        '获批时间\n（写到月）',
        '获批时间\r\n（写到月）',
        '获批日期',
        '批准日期'
      ];

      // 尝试所有可能的列名
      let approvalDateValue = null;
      for (const key of approvalDateKeys) {
        if (row[key] !== undefined && row[key] !== null) {
          approvalDateValue = row[key];
          console.log(`找到获批时间字段: ${key} = ${approvalDateValue}`);
          break;
        }
      }

      if (approvalDateValue) {
        try {
          console.log(`处理获批日期: "${approvalDateValue}", 类型: ${typeof approvalDateValue}`);

          let formattedDate = null;

          // 处理数字类型日期
          if (typeof approvalDateValue === 'number') {
            const numStr = approvalDateValue.toString();

            // 如果是四位整数，可能是纯年份（如2023）
            if (Number.isInteger(approvalDateValue) && approvalDateValue >= 1900 && approvalDateValue <= 2100) {
              formattedDate = `${approvalDateValue}-01-01`;
            }
            // 如果数字形式是YYYY.MM格式（如2023.03）
            else if (numStr.includes('.')) {
              const parts = numStr.split('.');
              const year = parseInt(parts[0]);

              // 将小数部分转换为月份
              let month;
              if (parts[1].length === 1) {
                month = parseInt(parts[1]);
              } else if (parts[1].length === 2) {
                month = parseInt(parts[1]);
              } else {
                const decimalMonth = Math.round(parseFloat('0.' + parts[1]) * 100);
                if (decimalMonth >= 1 && decimalMonth <= 12) {
                  month = decimalMonth;
                } else {
                  const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                  const firstDigit = parseInt(parts[1].substring(0, 1));
                  month = (firstTwoDigits >= 1 && firstTwoDigits <= 12) ? firstTwoDigits :
                          (firstDigit >= 1 && firstDigit <= 12) ? firstDigit : 1;
                }
              }

              if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
              }
            }

            // 如果前面的识别都失败，则作为Excel序列号处理
            if (!formattedDate) {
              let date;
              if (approvalDateValue < 60) {
                date = new Date(Date.UTC(1899, 11, 30 + approvalDateValue + 1));
              } else {
                date = new Date(Date.UTC(1899, 11, 30 + approvalDateValue));
              }

              const year = date.getUTCFullYear();
              const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
              const day = date.getUTCDate().toString().padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
            }
          }
          // 处理字符串类型日期
          else if (approvalDateValue) {
            const dateStr = String(approvalDateValue).trim();

            // 处理中文年月格式（如"2023年1月"）
            if (/^\d{4}年\d{1,2}月$/.test(dateStr)) {
              try {
                const year = dateStr.match(/(\d{4})年/)[1];
                const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                formattedDate = `${year}-${month}-01`;
              } catch (e) {
                console.error(`处理中文年月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年-月格式 (如"2018-06")
            else if (/^\d{4}-\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('-');
          const year = parseInt(parts[0]);
          const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年-月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年.月格式 (如"2018.06")
            else if (/^\d{4}\.\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('.');
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年.月格式失败: "${dateStr}"`, e);
              }
            }
            // ISO标准日期格式处理
            else {
              const isoDateRegex = /^(\d{4})[-\/\.](\d{1,2})[-\/\.](\d{1,2})$/;
              const match = dateStr.match(isoDateRegex);
              if (match) {
                const year = parseInt(match[1]);
                const month = parseInt(match[2]);
                const day = parseInt(match[3]);

                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                }
              }
            }

            // 如果所有方法都失败，保留原始字符串
            if (!formattedDate) {
              formattedDate = dateStr;
            }
          }

          // 设置项目批准日期
          if (formattedDate) {
            project.approvalDate = formattedDate;
            console.log(`设置项目批准日期: ${project.approvalDate}`);
          }
        } catch (e) {
          console.error(`日期解析失败: ${approvalDateValue}`, e);
          if (approvalDateValue) {
            project.approvalDate = String(approvalDateValue).trim();
          }
        }
      } else {
        console.warn('未找到获批时间字段');
      }

      // 处理执行起始年和结束年
      if (row['执行起始年']) {
        const startYear = String(row['执行起始年']).trim();
        if (/^\d{4}$/.test(startYear)) {
          project.startYear = startYear;
        }
      }

      if (row['执行结束年']) {
        const endYear = String(row['执行结束年']).trim();
        if (/^\d{4}$/.test(endYear)) {
          project.endYear = endYear;
        }
      }

      // 如果没有读取到起始年或结束年，设置为当前年份
      const currentYear = new Date().getFullYear().toString();
      if (!project.startYear) {
        console.log(`未找到起始年，使用当前年份: ${currentYear}`);
        project.startYear = currentYear;
      }
      if (!project.endYear) {
        console.log(`未找到结束年，使用当前年份: ${currentYear}`);
        project.endYear = currentYear;
      }

      // 备注
      if (row['备注']) project.remark = String(row['备注']).trim();

      // 提取并整合所有参与者及其分配比例，支持最多3个承担者
      const participants = [];


      // 处理承担者1
      if (row['承担者1']) {
        let ratio = null;
        const allKeys = Object.keys(row);

        // 找到承担者1后的分配比例字段
        const ratio1Index = allKeys.findIndex(k => k.includes('分配比例') && allKeys.indexOf(k) > allKeys.indexOf('承担者1'));
        if (ratio1Index !== -1) {
          const ratioKey = allKeys[ratio1Index];
          const ratioStr = String(row[ratioKey] || '').trim();

          // 直接使用Excel中的数据，只转换百分比格式为小数格式
          if (ratioStr.endsWith('%')) {
            // 百分比转小数，保留两位小数
            ratio = parseFloat(ratioStr.replace('%', '')) / 100;
            ratio = parseFloat(ratio.toFixed(2));
          } else if (ratioStr) {
            // 直接使用数值，保留两位小数
            ratio = parseFloat(parseFloat(ratioStr).toFixed(2));
          }
        }

        participants.push({
          name: String(row['承担者1']).trim(),
          isLeader: true,
          allocationRatio: ratio // 直接使用Excel中读取的值
        });
      }

      // 处理承担者2
      if (row['承担者2']) {
        let ratio = null;
        const allKeys = Object.keys(row);

        // 找到承担者2后的分配比例字段
        const ratio2Index = allKeys.findIndex(k => k.includes('分配比例') && allKeys.indexOf(k) > allKeys.indexOf('承担者2'));
        if (ratio2Index !== -1) {
          const ratioKey = allKeys[ratio2Index];
          const ratioStr = String(row[ratioKey] || '').trim();

          // 直接使用Excel中的数据，只转换百分比格式为小数格式
          if (ratioStr.endsWith('%')) {
            // 百分比转小数，保留两位小数
            ratio = parseFloat(ratioStr.replace('%', '')) / 100;
            ratio = parseFloat(ratio.toFixed(2));
          } else if (ratioStr) {
            // 直接使用数值，保留两位小数
            ratio = parseFloat(parseFloat(ratioStr).toFixed(2));
          }
        }

        participants.push({
          name: String(row['承担者2']).trim(),
          isLeader: false,
          allocationRatio: ratio // 直接使用Excel中读取的值
        });
      }

      // 处理承担者3
      if (row['承担者3']) {
        let ratio = null;
        const allKeys = Object.keys(row);

        // 找到承担者3后的分配比例字段
        const ratio3Index = allKeys.findIndex(k => k.includes('分配比例') && allKeys.indexOf(k) > allKeys.indexOf('承担者3'));
        if (ratio3Index !== -1) {
          const ratioKey = allKeys[ratio3Index];
          const ratioStr = String(row[ratioKey] || '').trim();

          // 直接使用Excel中的数据，只转换百分比格式为小数格式
          if (ratioStr.endsWith('%')) {
            // 百分比转小数，保留两位小数
            ratio = parseFloat(ratioStr.replace('%', '')) / 100;
            ratio = parseFloat(ratio.toFixed(2));
          } else if (ratioStr) {
            // 直接使用数值，保留两位小数
            ratio = parseFloat(parseFloat(ratioStr).toFixed(2));
          }
        }

        participants.push({
          name: String(row['承担者3']).trim(),
          isLeader: false,
          allocationRatio: ratio // 直接使用Excel中读取的值
        });
      }

      // 删除所有计算和校验逻辑，直接使用Excel中读取的原始数据
      project.participants = participants;

      // 检查必须字段
      if (!project.projectName || !project.categoryName || participants.length === 0) {
        console.log(`跳过缺少必要字段的记录: 项目名称=${project.projectName}, 级别=${project.categoryName}, 参与人数=${participants.length}`);
        continue;
      }

      // 添加到项目列表
      projects.push(project);
    }

    console.log(`处理完成，共解析出 ${projects.length} 条教学改革项目记录`);

    return projects;

  } catch (error) {
    console.error('转换Excel到JSON出错:', error);
    throw error;
  }
};

/**
 * 将Excel文件转换为学生项目指导JSON格式
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为2（第3行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Array>} 解析后的学生项目指导数据数组
 */
export const excelToStudentProjectGuidanceProjectsJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项
  const headerRow = options.headerRow ?? 2; // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表

  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 跳过第四行（索引为1的那一行，因为从headerRow开始读取的）
    const projectsData = data.slice(1);

    // 处理数据
    const projects = [];

    for (let i = 0; i < projectsData.length; i++) {
      const row = projectsData[i];

      // 跳过空行（如果项目名称为空）
      if (!row['项目名称'] || row['项目名称'] === null) {
        continue;
      }

      // 创建项目对象
      const project = {};

      // 基本字段
      if (row['项目名称']) project.projectName = String(row['项目名称']).trim();
      if (row['项目级别']) project.levelName = String(row['项目级别']).trim();

      // 处理获批时间
      if (row['获批时间（写到月）'] || row['获批时间\n（写到月）'] || row['获批时间\r\n（写到月）']) {
        const dateValue = row['获批时间（写到月）'] || row['获批时间\n（写到月）'] || row['获批时间\r\n（写到月）'];
        try {
          console.log(`处理获批日期: "${dateValue}", 类型: ${typeof dateValue}`);

          let formattedDate = null;

          // 处理数字类型日期
          if (typeof dateValue === 'number') {
            const numStr = dateValue.toString();

            // 如果是四位整数，可能是纯年份（如2023）
            if (Number.isInteger(dateValue) && dateValue >= 1900 && dateValue <= 2100) {
              formattedDate = `${dateValue}-01-01`;
            }
            // 如果数字形式是YYYY.MM格式（如2023.03）
            else if (numStr.includes('.')) {
              const parts = numStr.split('.');
              const year = parseInt(parts[0]);

              // 将小数部分转换为月份
              let month;
              if (parts[1].length === 1) {
                month = parseInt(parts[1]);
              } else if (parts[1].length === 2) {
                month = parseInt(parts[1]);
              } else {
                const decimalMonth = Math.round(parseFloat('0.' + parts[1]) * 100);
                if (decimalMonth >= 1 && decimalMonth <= 12) {
                  month = decimalMonth;
                } else {
                  const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                  const firstDigit = parseInt(parts[1].substring(0, 1));
                  month = (firstTwoDigits >= 1 && firstTwoDigits <= 12) ? firstTwoDigits :
                          (firstDigit >= 1 && firstDigit <= 12) ? firstDigit : 1;
                }
              }

          if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
              }
            }

            // 如果前面的识别都失败，则作为Excel序列号处理
            if (!formattedDate) {
              let date;
              if (dateValue < 60) {
                date = new Date(Date.UTC(1899, 11, 30 + dateValue + 1));
        } else {
                date = new Date(Date.UTC(1899, 11, 30 + dateValue));
              }

              const year = date.getUTCFullYear();
              const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
              const day = date.getUTCDate().toString().padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
            }
          }
          // 处理字符串类型日期
          else if (dateValue) {
            const dateStr = String(dateValue).trim();

            // 处理中文年月格式（如"2023年1月"）
            if (/^\d{4}年\d{1,2}月$/.test(dateStr)) {
              try {
                const year = dateStr.match(/(\d{4})年/)[1];
                const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                formattedDate = `${year}-${month}-01`;
              } catch (e) {
                console.error(`处理中文年月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年-月格式 (如"2018-06")
            else if (/^\d{4}-\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('-');
          const year = parseInt(parts[0]);
          const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年-月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年.月格式 (如"2018.06")
            else if (/^\d{4}\.\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('.');
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年.月格式失败: "${dateStr}"`, e);
              }
            }
            // ISO标准日期格式处理
            else {
              const isoDateRegex = /^(\d{4})[-\/\.](\d{1,2})[-\/\.](\d{1,2})$/;
              const match = dateStr.match(isoDateRegex);
              if (match) {
                const year = parseInt(match[1]);
                const month = parseInt(match[2]);
                const day = parseInt(match[3]);

                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                }
              }
            }

            // 如果所有方法都失败，保留原始字符串
            if (!formattedDate) {
              formattedDate = dateStr;
            }
          }

          // 设置项目批准日期
          if (formattedDate) {
            project.approvalDate = formattedDate;
          }
        } catch (e) {
          console.error(`日期解析失败: ${row['获批时间（写到月）']}`, e);
          if (row['获批时间（写到月）']) {
            project.approvalDate = String(row['获批时间（写到月）']).trim();
          }
        }
      }

      // 处理执行起始年和结束年
      if (row['执行起始年']) {
        const startYear = String(row['执行起始年']).trim();
        if (/^\d{4}$/.test(startYear)) {
          project.startYear = startYear;
        }
      }

      if (row['执行结束年']) {
        const endYear = String(row['执行结束年']).trim();
        if (/^\d{4}$/.test(endYear)) {
          project.endYear = endYear;
        }
      }

      // 如果没有读取到起始年或结束年，设置为当前年份
      const currentYear = new Date().getFullYear().toString();
      if (!project.startYear) {
        console.log(`未找到起始年，使用当前年份: ${currentYear}`);
        project.startYear = currentYear;
      }
      if (!project.endYear) {
        console.log(`未找到结束年，使用当前年份: ${currentYear}`);
        project.endYear = currentYear;
      }

      // 默认审批部门（如果Excel中没有这个字段）
      project.approvalDepartment = '暨南大学';

      // 备注
      if (row['备注']) project.remark = String(row['备注']).trim();

      // 提取并整合所有参与者及其分配比例
      const participants = [];

      // 获取主要指导老师和指导教师1的名称
      const mainSupervisorName = row['主要指导老师'] ? String(row['主要指导老师']).trim() : null;
      const instructor1Name = row['指导教师1'] ? String(row['指导教师1']).trim() : null;

      // 判断是否为同一人
      const isSamePerson = mainSupervisorName && instructor1Name && mainSupervisorName === instructor1Name;

      // 处理指导教师1
      if (instructor1Name) {
        // 获取指导教师1的分配比例
        let ratio = null;
        if ('分配比例' in row) {
          const ratioValue = row['分配比例'];
          // 保持原始分配比例值，不做计算
          ratio = typeof ratioValue === 'number' ? ratioValue :
                  typeof ratioValue === 'string' ? parseFloat(ratioValue) : null;
        }

        participants.push({
          name: instructor1Name,
          studentNumber: row['人事编号'] ? String(row['人事编号']).trim() : '',
          // 如果是同一人或者没有主要指导老师，则设为负责人
          isLeader: isSamePerson || !mainSupervisorName,
          allocationRatio: ratio // 直接使用Excel中的比例值，不做计算
        });
      }

      // 如果主要指导老师和指导教师1不是同一人，且主要指导老师存在，则添加主要指导老师
      if (mainSupervisorName && !isSamePerson) {
        participants.push({
          name: mainSupervisorName,
          isLeader: true,
          // 直接使用默认值，不做计算
          allocationRatio: row['主导师分配比例'] ? parseFloat(row['主导师分配比例']) : null
        });
      }

      project.participants = participants;

      // 检查必须字段
      if (!project.projectName || !project.levelName || participants.length === 0) {
        console.log(`跳过缺少必要字段的记录: 项目名称=${project.projectName}, 级别=${project.levelName}, 参与人数=${participants.length}`);
        continue;
      }

      // 添加到项目列表
      projects.push(project);
    }

    console.log(`处理完成，共解析出 ${projects.length} 条学生项目指导记录`);

    return projects;

  } catch (error) {
    console.error('转换Excel到JSON出错:', error);
    throw error;
  }
};

/**
 * 将Excel文件转换为学生获奖指导JSON格式
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为2（第3行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Array>} 解析后的学生获奖指导数据数组
 */
export const excelToStudentAwardGuidanceAwardsJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项
  const headerRow = options.headerRow ?? 2; // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表

  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 处理数据
    const awards = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];

      // 跳过空行（如果获奖名称为空）
      if (!row['获奖名称/出国研究生姓名'] || row['获奖名称/出国研究生姓名'] === null) {
        continue;
      }

      // 创建获奖对象
      const award = {};

      // 基本字段
      if (row['获奖名称/出国研究生姓名']) award.awardName = String(row['获奖名称/出国研究生姓名']).trim();
      if (row['系/教研室']) award.department = String(row['系/教研室']).trim();
      if (row['获奖等级/国际交流']) award.levelName = String(row['获奖等级/国际交流']).trim();

      // 处理获奖时间
      if (row['获奖时间/出国交流时间（写到月）'] || row['获奖时间/出国交流时间\n（写到月）'] || row['获奖时间/出国交流时间\r\n（写到月）']) {
        const dateValue = row['获奖时间/出国交流时间（写到月）'] || row['获奖时间/出国交流时间\n（写到月）'] || row['获奖时间/出国交流时间\r\n（写到月）'];
        try {
          console.log(`处理获奖日期: "${dateValue}", 类型: ${typeof dateValue}`);

          let formattedDate = null;

          // 处理数字类型日期
          if (typeof dateValue === 'number') {
            const numStr = dateValue.toString();

            // 如果是四位整数，可能是纯年份（如2023）
            if (Number.isInteger(dateValue) && dateValue >= 1900 && dateValue <= 2100) {
              formattedDate = `${dateValue}-01-01`;
            }
            // 如果数字形式是YYYY.MM格式（如2023.03）
            else if (numStr.includes('.')) {
              const parts = numStr.split('.');
              const year = parseInt(parts[0]);

              // 将小数部分转换为月份
              let month;
              if (parts[1].length === 1) {
                month = parseInt(parts[1]);
              } else if (parts[1].length === 2) {
                month = parseInt(parts[1]);
              } else {
                const decimalMonth = Math.round(parseFloat('0.' + parts[1]) * 100);
                if (decimalMonth >= 1 && decimalMonth <= 12) {
                  month = decimalMonth;
                } else {
                  const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                  const firstDigit = parseInt(parts[1].substring(0, 1));
                  month = (firstTwoDigits >= 1 && firstTwoDigits <= 12) ? firstTwoDigits :
                          (firstDigit >= 1 && firstDigit <= 12) ? firstDigit : 1;
                }
              }

          if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
              }
            }

            // 如果前面的识别都失败，则作为Excel序列号处理
            if (!formattedDate) {
          let date;
              if (dateValue < 60) {
                date = new Date(Date.UTC(1899, 11, 30 + dateValue + 1));
          } else {
                date = new Date(Date.UTC(1899, 11, 30 + dateValue));
          }

            const year = date.getUTCFullYear();
            const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
            const day = date.getUTCDate().toString().padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
            }
          }
          // 处理字符串类型日期
          else if (dateValue) {
            const dateStr = String(dateValue).trim();

            // 处理中文年月格式（如"2023年1月"）
            if (/^\d{4}年\d{1,2}月$/.test(dateStr)) {
              try {
                const year = dateStr.match(/(\d{4})年/)[1];
                const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                formattedDate = `${year}-${month}-01`;
              } catch (e) {
                console.error(`处理中文年月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年-月格式 (如"2018-06")
            else if (/^\d{4}-\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('-');
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年-月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年.月格式 (如"2018.06")
            else if (/^\d{4}\.\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('.');
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年.月格式失败: "${dateStr}"`, e);
              }
            }
            // ISO标准日期格式处理
            else {
              const isoDateRegex = /^(\d{4})[-\/\.](\d{1,2})[-\/\.](\d{1,2})$/;
              const match = dateStr.match(isoDateRegex);
              if (match) {
                const year = parseInt(match[1]);
                const month = parseInt(match[2]);
                const day = parseInt(match[3]);

                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                }
              }
            }

            // 如果所有方法都失败，保留原始字符串
            if (!formattedDate) {
              formattedDate = dateStr;
            }
          }

          // 设置获奖日期
          if (formattedDate) {
            award.awardDate = formattedDate;
          }
        } catch (e) {
          console.error(`日期解析失败: ${row['获奖时间/出国交流时间（写到月）']}`, e);
          if (row['获奖时间/出国交流时间（写到月）']) {
            award.awardDate = String(row['获奖时间/出国交流时间（写到月）']).trim();
          }
        }
      }

      // 备注
      if (row['备注']) award.remark = String(row['备注']).trim();

      // 提取并整合所有参与者及其分配比例
      const participants = [];

      // 获取主要指导教师的名称（如果有）
      const mainInstructor = row['主要指导教师'] ? String(row['主要指导教师']).trim() : null;

      // 处理指导教师1
      if (row['指导教师1']) {
        const instructorName = String(row['指导教师1']).trim();
        let ratio = null;

        // 获取指导教师1的分配比例
        if ('分配比例' in row) {
          ratio = parseFloat(row['分配比例']);
          if (isNaN(ratio)) ratio = null;
        }

        // 检查是否是主要指导教师
        const isMainInstructor = mainInstructor && instructorName === mainInstructor;

        participants.push({
          name: instructorName,
          studentNumber: row['人事编号'] ? String(row['人事编号']).trim() : '',
          isLeader: isMainInstructor, // 如果和主要指导教师相同，则设为主导师
          allocationRatio: ratio // 可能是null，后面会处理
        });
      }
      // 如果指导教师1没有，但有主要指导教师，则添加主要指导教师
      else if (mainInstructor) {
        participants.push({
          name: mainInstructor,
          isLeader: true,
          allocationRatio: 1 // 只有一个人时，分配比例为1
        });
      }

      // 规范化分配比例
      if (participants.length > 0) {
        // 计算已有的比例总和
        let totalRatio = 0;
        let validRatioCount = 0;

        for (const p of participants) {
          if (p.allocationRatio !== null && !isNaN(p.allocationRatio)) {
            totalRatio += p.allocationRatio;
            validRatioCount++;
          }
        }

        // 如果没有任何有效的分配比例，或总和不为1，则重新分配
        if (validRatioCount === 0 || Math.abs(totalRatio - 1) > 0.01) {
          // 如果只有主导师一个人，则分配比例为1
          if (participants.length === 1) {
            participants[0].allocationRatio = 1;
          } else {
            // 主导师分配更高比例，其他人平分剩余比例
            const leaderRatio = 0.6; // 主导师占60%
            const otherRatio = parseFloat(((1 - leaderRatio) / (participants.length - 1)).toFixed(2)); // 其他人平分剩余40%，保留两位小数

            for (let j = 0; j < participants.length; j++) {
              if (participants[j].isLeader) {
                participants[j].allocationRatio = leaderRatio;
              } else {
                participants[j].allocationRatio = otherRatio;
              }
            }
          }
        }
      }

      award.participants = participants;

      // 检查必须字段
      if (!award.awardName || !award.department || !award.awardDate || !award.levelName || participants.length === 0) {
        console.log(`跳过缺少必要字段的记录: 获奖名称=${award.awardName}, 部门=${award.department}, 获奖日期=${award.awardDate}, 获奖等级=${award.levelName}, 参与人数=${participants.length}`);
        continue;
      }

      // 添加到获奖列表
      awards.push(award);
    }

    console.log(`处理完成，共解析出 ${awards.length} 条学生获奖指导记录`);

    return awards;

    } catch (error) {
    console.error('转换Excel到JSON出错:', error);
    throw error;
  }
};

/**
 * 将Excel文件转换为会议JSON格式
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为2（第3行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Array>} 解析后的会议数据数组
 */
export const excelToConferencesJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项
  const headerRow = options.headerRow ?? 2; // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表

  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 从第4行开始处理数据（跳过第3行表头）
    const conferencesData = data;

    // 处理数据
    const conferences = [];

    for (let i = 0; i < conferencesData.length; i++) {
      const row = conferencesData[i];

      // 跳过空行（如果会议名称为空）
      if (!row['会议名称'] || row['会议名称'] === null) {
        continue;
      }

      // 创建会议对象
      const conference = {};

      // 基本字段
      if (row['会议名称']) conference.conferenceName = String(row['会议名称']).trim();
      if (row['级别']) conference.levelName = String(row['级别']).trim();

      // 处理举办时间
      if (row['举办时间（写到月）'] || row['举办时间\n（写到月）'] || row['举办时间\r\n（写到月）'] || row['举办时间↵（写到月）']) {
        const dateValue = row['举办时间（写到月）'] || row['举办时间\n（写到月）'] || row['举办时间\r\n（写到月）'] || row['举办时间↵（写到月）'];
        try {
          console.log(`处理举办时间: "${dateValue}", 类型: ${typeof dateValue}`);

          let formattedDate = null;

          // 处理数字类型日期
          if (typeof dateValue === 'number') {
            const numStr = dateValue.toString();

            // 如果是四位整数，可能是纯年份（如2023）
            if (Number.isInteger(dateValue) && dateValue >= 1900 && dateValue <= 2100) {
              formattedDate = `${dateValue}-01-01`;
            }
            // 如果数字形式是YYYY.MM格式（如2023.03）
            else if (numStr.includes('.')) {
              const parts = numStr.split('.');
              const year = parseInt(parts[0]);

              // 将小数部分转换为月份
              let month;
              if (parts[1].length === 1) {
                month = parseInt(parts[1]);
              } else if (parts[1].length === 2) {
                month = parseInt(parts[1]);
              } else {
                const decimalMonth = Math.round(parseFloat('0.' + parts[1]) * 100);
                if (decimalMonth >= 1 && decimalMonth <= 12) {
                  month = decimalMonth;
                } else {
                  const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                  const firstDigit = parseInt(parts[1].substring(0, 1));
                  month = (firstTwoDigits >= 1 && firstTwoDigits <= 12) ? firstTwoDigits :
                          (firstDigit >= 1 && firstDigit <= 12) ? firstDigit : 1;
                }
              }

              if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
              }
            }

            // 如果前面的识别都失败，则作为Excel序列号处理
            if (!formattedDate) {
          let date;
              if (dateValue < 60) {
                date = new Date(Date.UTC(1899, 11, 30 + dateValue + 1));
          } else {
                date = new Date(Date.UTC(1899, 11, 30 + dateValue));
          }

            const year = date.getUTCFullYear();
            const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
            const day = date.getUTCDate().toString().padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
            }
          }
          // 处理字符串类型日期
          else if (dateValue) {
            const dateStr = String(dateValue).trim();

            // 处理中文年月格式（如"2023年1月"）
            if (/^\d{4}年\d{1,2}月$/.test(dateStr)) {
              try {
                const year = dateStr.match(/(\d{4})年/)[1];
                const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                formattedDate = `${year}-${month}-01`;
              } catch (e) {
                console.error(`处理中文年月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年-月格式 (如"2018-06")
            else if (/^\d{4}-\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('-');
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年-月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年.月格式 (如"2018.06")
            else if (/^\d{4}\.\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('.');
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年.月格式失败: "${dateStr}"`, e);
              }
            }
            // ISO标准日期格式处理
            else {
              const isoDateRegex = /^(\d{4})[-\/\.](\d{1,2})[-\/\.](\d{1,2})$/;
              const match = dateStr.match(isoDateRegex);
              if (match) {
                const year = parseInt(match[1]);
                const month = parseInt(match[2]);
                const day = parseInt(match[3]);

                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                }
              }
            }

            // 如果所有方法都失败，保留原始字符串
            if (!formattedDate) {
              formattedDate = dateStr;
            }
          }

          // 设置举办时间
          if (formattedDate) {
            conference.holdTime = formattedDate;
          }
        } catch (e) {
          console.error(`日期解析失败: ${dateValue}`, e);
          if (dateValue) {
            conference.holdTime = String(dateValue).trim();
          }
        }
      }

      // 处理备注
      if (row['备注']) conference.remark = String(row['备注']).trim();

      // 处理参与者
      const participants = [];
      const firstResponsible = row['第一负责人'] ? String(row['第一负责人']).trim() : null;

      // 处理参与者（最多10个）
      for (let j = 1; j <= 10; j++) {
        const participantKey = `参与者${j}`;

        if (row[participantKey] && row[participantKey] !== null) {
          // 获取参与者名称
          const name = String(row[participantKey]).trim();

          // 获取人事编号
          const personIdKey = Object.keys(row).find(key => {
            // 寻找在参与者列名后的"人事编号"列
            const participantKeyIndex = Object.keys(row).indexOf(participantKey);
            return key.includes('人事编号') && Object.keys(row).indexOf(key) === participantKeyIndex + 1;
          });

          let personId = '';
          if (personIdKey && row[personIdKey]) {
            personId = String(row[personIdKey]).trim();
          }

          // 获取分配比例
          const ratioKey = Object.keys(row).find(key => {
            // 寻找在参与者列名后的"分配比例"列
            const participantKeyIndex = Object.keys(row).indexOf(participantKey);
            return key.includes('分配比例') && Object.keys(row).indexOf(key) === participantKeyIndex + 2;
          });

          let ratio = null;
          if (ratioKey && row[ratioKey] !== null && row[ratioKey] !== undefined) {
            ratio = parseFloat(row[ratioKey]);
            if (isNaN(ratio)) ratio = null;
          }

          // 检查是否是第一负责人
          const isLeader = firstResponsible && name === firstResponsible;

          // 添加参与者
          participants.push({
            name: name,
            personId: personId,
            isLeader: isLeader, // 如果与第一负责人同名，则设置为负责人
            allocationRatio: ratio // 保留原始分配比例
          });
        }
      }

      // 如果第一负责人不在参与者列表中，且存在第一负责人，则添加到参与者列表中
      if (firstResponsible) {
        const firstResponsibleExists = participants.some(p => p.name === firstResponsible);

        if (!firstResponsibleExists) {
          participants.push({
            name: firstResponsible,
            personId: '',
            isLeader: true,
            allocationRatio: null // 保留原始分配比例
          });
        }
      }

      // 保存第一负责人信息到会议对象中（如果有）
      if (firstResponsible) {
        conference.firstResponsible = firstResponsible;
      }

      conference.participants = participants;

      // Debug log to see what values are being checked
      console.log(`必要字段检查前: 会议名称=${conference.conferenceName}, 级别=${conference.levelName}, 举办时间=${conference.holdTime}, 参与人数=${participants.length}`);

      // 检查必须字段 - 改为更宽松的验证，只要有会议名称就添加
      if (!conference.conferenceName) {
        console.log(`跳过缺少会议名称的记录`);
        continue;
      }

      // 如果没有举办时间，设置默认值为当前日期
      if (!conference.holdTime) {
        console.log(`会议时间为空，使用当前日期作为默认值`);
        conference.holdTime = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      }

      // 如果没有级别，设置默认值
      if (!conference.levelName) {
        console.log(`会议级别为空，使用"其他"作为默认值`);
        conference.levelName = "其他";
      }

      // 添加到会议列表，即使没有参与者也添加
      conferences.push(conference);
    }

    console.log(`处理完成，共解析出 ${conferences.length} 条会议记录`);

    return conferences;

    } catch (error) {
    console.error('转换Excel到会议JSON出错:', error);
    throw error;
    }
};

/**
 * 将Excel文件转换为教材JSON格式
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为2（第3行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Array>} 解析后的教材数据数组
 */
export const excelToTextbooksJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项
  const headerRow = options.headerRow ?? 2; // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表

  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 处理数据
    const textbooks = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];

      // 跳过空行（如果教材名称为空）
      if (!row['教材或著作名称'] || row['教材或著作名称'] === null) {
        continue;
      }

      // 创建教材对象
      const textbook = {};

      // 基本字段
      if (row['教材或著作名称']) textbook.materialName = String(row['教材或著作名称']).trim();
      if (row['类别及任职']) textbook.categoryName = String(row['类别及任职']).trim();
      if (row['备注']) textbook.remark = String(row['备注']).trim();

      // 处理作者信息
      if (row['作者']) {
        textbook.author = String(row['作者']).trim();
      }

      // 处理人事编号
      if (row['人事编号']) {
        textbook.personnelId = String(row['人事编号']).trim();
      }

      // 处理出版时间
      if (row['出版时间（写到月）'] || row['出版时间\n（写到月）'] || row['出版时间\r\n（写到月）'] || row['出版时间↵（写到月）']) {
        const dateValue = row['出版时间（写到月）'] || row['出版时间\n（写到月）'] || row['出版时间\r\n（写到月）'] || row['出版时间↵（写到月）'];
        try {
          console.log(`处理出版时间: "${dateValue}", 类型: ${typeof dateValue}`);

          let formattedDate = null;

          // 处理数字类型日期
          if (typeof dateValue === 'number') {
            const numStr = dateValue.toString();

            // 如果是四位整数，可能是纯年份（如2023）
            if (Number.isInteger(dateValue) && dateValue >= 1900 && dateValue <= 2100) {
              formattedDate = `${dateValue}-01-01`;
            }
            // 如果数字形式是YYYY.MM格式（如2023.03）
            else if (numStr.includes('.')) {
              const parts = numStr.split('.');
              const year = parseInt(parts[0]);

              // 将小数部分转换为月份
              let month;
              if (parts[1].length === 1) {
                month = parseInt(parts[1]);
              } else if (parts[1].length === 2) {
                month = parseInt(parts[1]);
              } else {
                const decimalMonth = Math.round(parseFloat('0.' + parts[1]) * 100);
                if (decimalMonth >= 1 && decimalMonth <= 12) {
                  month = decimalMonth;
                } else {
                  const firstTwoDigits = parseInt(parts[1].substring(0, 2));
                  const firstDigit = parseInt(parts[1].substring(0, 1));
                  month = (firstTwoDigits >= 1 && firstTwoDigits <= 12) ? firstTwoDigits :
                          (firstDigit >= 1 && firstDigit <= 12) ? firstDigit : 1;
                }
              }

              if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
              }
            }

            // 如果前面的识别都失败，则作为Excel序列号处理
            if (!formattedDate) {
              let date;
              if (dateValue < 60) {
                date = new Date(Date.UTC(1899, 11, 30 + dateValue + 1));
              } else {
                date = new Date(Date.UTC(1899, 11, 30 + dateValue));
              }

              const year = date.getUTCFullYear();
              const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
              const day = date.getUTCDate().toString().padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
            }
          }
          // 处理字符串类型日期
          else if (dateValue) {
            const dateStr = String(dateValue).trim();

            // 处理中文年月格式（如"2023年1月"）
            if (/^\d{4}年\d{1,2}月$/.test(dateStr)) {
              try {
                const year = dateStr.match(/(\d{4})年/)[1];
                const month = dateStr.match(/(\d{1,2})月/)[1].padStart(2, '0');
                formattedDate = `${year}-${month}-01`;
              } catch (e) {
                console.error(`处理中文年月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年-月格式 (如"2018-06")
            else if (/^\d{4}-\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('-');
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年-月格式失败: "${dateStr}"`, e);
              }
            }
            // 处理年.月格式 (如"2018.06")
            else if (/^\d{4}\.\d{1,2}$/.test(dateStr)) {
              try {
                const parts = dateStr.split('.');
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]);
                if (month >= 1 && month <= 12 && year >= 1900) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-01`;
                }
              } catch (e) {
                console.error(`处理年.月格式失败: "${dateStr}"`, e);
              }
            }
            // ISO标准日期格式处理
            else {
              const isoDateRegex = /^(\d{4})[-\/\.](\d{1,2})[-\/\.](\d{1,2})$/;
              const match = dateStr.match(isoDateRegex);
              if (match) {
                const year = parseInt(match[1]);
                const month = parseInt(match[2]);
                const day = parseInt(match[3]);

                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                  formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                }
              }
            }

            // 如果所有方法都失败，保留原始字符串
            if (!formattedDate) {
              formattedDate = dateStr;
            }
          }

          // 设置出版时间
          if (formattedDate) {
            textbook.publishDate = formattedDate;
          }
        } catch (e) {
          console.error(`日期解析失败: ${dateValue}`, e);
          if (dateValue) {
            textbook.publishDate = String(dateValue).trim();
          }
        }
      }

      // Debug log to see what values are being checked
      console.log(`必要字段检查前: 教材名称=${textbook.materialName}, 类别=${textbook.categoryName}, 出版时间=${textbook.publishDate}, 作者=${textbook.author}`);

      // 检查必须字段 - 只需要教材名称
      if (!textbook.materialName) {
        console.log(`跳过缺少教材名称的记录`);
        continue;
      }

      // 如果没有出版时间，设置默认值为当前日期
      if (!textbook.publishDate) {
        console.log(`出版时间为空，使用当前日期作为默认值`);
        textbook.publishDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      }

      // 如果没有类别，设置默认值
      if (!textbook.categoryName) {
        console.log(`类别为空，使用"其他"作为默认值`);
        textbook.categoryName = "其他";
      }

      // 添加到教材列表
      textbooks.push(textbook);
    }

    console.log(`处理完成，共解析出 ${textbooks.length} 条教材记录`);

    return textbooks;

  } catch (error) {
    console.error('转换Excel到教材JSON出错:', error);
    throw error;
  }
};

/**
 * 将Excel文件转换为学术任职JSON格式
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为2（第3行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Array>} 解析后的学术任职数据数组
 */
export const excelToAcademicAppointmentsJson = async (file, options = {}) => {
  // 检查是否提供了文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  // 检查文件类型
  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  // 配置选项
  const headerRow = options.headerRow ?? 2; // 默认第3行为表头（索引为2）
  const sheetName = options.sheetName || null; // 默认使用第一个工作表

  try {
    // 动态导入xlsx库，避免全局加载
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          // 选择工作表
          const wsname = sheetName || wb.SheetNames[0];
          const ws = wb.Sheets[wsname];

          console.log(`正在从第 ${headerRow + 1} 行读取表头和数据`);

          // 使用header参数直接指定表头行
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow, // 从headerRow开始读取（包括表头行）
            defval: null, // 使用null代替空值
            raw: true,    // 获取原始值
            cellText: false, // 不使用显示文本
            cellDates: true, // 将日期转换为JS日期对象
            dateNF: 'yyyy-mm-dd' // 设置日期格式
          });

          console.log(`读取到 ${jsonData.length} 条数据记录`);

          // 输出第一条记录的所有列名，帮助调试
          if (jsonData.length > 0) {
            console.log('Excel列名:', Object.keys(jsonData[0]));
          }

          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => reject(new Error('读取文件失败'));
      reader.readAsArrayBuffer(file);
    });

    console.log("原始Excel数据:", data);

    // 检查数据是否为空
    if (!data || data.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    // 跳过第四行（即原始数据的第二行，因为headerRow=2表示从第3行开始读取）
    const appointmentsData = data.slice(1);

    // 处理数据
    const appointments = [];

    for (let i = 0; i < appointmentsData.length; i++) {
      const row = appointmentsData[i];

      // 跳过空行（如果学术协会/期刊为空）
      if (!row['任职学术协会/期刊'] || row['任职学术协会/期刊'] === null) {
        continue;
      }

      // 创建学术任职对象
      const appointment = {};

      // 基本字段
      if (row['姓名']) appointment.name = String(row['姓名']).trim();
      if (row['人事编号']) appointment.personnelId = String(row['人事编号']).trim();
      if (row['任职学术协会/期刊']) appointment.associationName = String(row['任职学术协会/期刊']).trim();
      if (row['职务']) appointment.position = String(row['职务']).trim();
      if (row['任职级别']) appointment.levelName = String(row['任职级别']).trim();

      // 处理任职起始年
      if (row['任职起始年'] !== undefined && row['任职起始年'] !== null) {
        const startYearValue = row['任职起始年'];

        // 处理数字类型
        if (typeof startYearValue === 'number') {
          if (Number.isInteger(startYearValue) && startYearValue >= 1900 && startYearValue <= 2100) {
            appointment.startYear = String(startYearValue);
          }
        }
        // 处理字符串类型
        else if (typeof startYearValue === 'string') {
          const yearStr = startYearValue.trim();
          // 提取4位数年份
          const yearMatch = yearStr.match(/\d{4}/);
          if (yearMatch) {
            const year = parseInt(yearMatch[0]);
            if (year >= 1900 && year <= 2100) {
              appointment.startYear = String(year);
            }
          } else {
            appointment.startYear = yearStr;
          }
        }
      }

      // 处理任职结束年
      if (row['任职结束年'] !== undefined && row['任职结束年'] !== null) {
        const endYearValue = row['任职结束年'];

        // 处理数字类型
        if (typeof endYearValue === 'number') {
          if (Number.isInteger(endYearValue) && endYearValue >= 1900 && endYearValue <= 2100) {
            appointment.endYear = String(endYearValue);
          }
        }
        // 处理字符串类型
        else if (typeof endYearValue === 'string') {
          const yearStr = endYearValue.trim();
          // 处理"至今"或类似表示当前的文本
          if (yearStr === '至今' || yearStr === '现在' || yearStr.toLowerCase() === 'present') {
            const currentYear = new Date().getFullYear();
            appointment.endYear = String(currentYear);
          } else {
            // 提取4位数年份
            const yearMatch = yearStr.match(/\d{4}/);
            if (yearMatch) {
              const year = parseInt(yearMatch[0]);
              if (year >= 1900 && year <= 2100) {
                appointment.endYear = String(year);
              }
            } else {
              appointment.endYear = yearStr;
            }
          }
        }
      }

      // 处理备注
      if (row['备注']) appointment.remark = String(row['备注']).trim();

      // Debug log to see what values are being checked
      console.log(`检查记录: 姓名=${appointment.name}, 学术协会/期刊=${appointment.associationName}, 职务=${appointment.position}, 级别=${appointment.levelName}, 起始年=${appointment.startYear}`);

      // 检查必须字段
      if (!appointment.associationName || !appointment.position || !appointment.levelName) {
        console.log(`跳过缺少必要字段的记录: 学术协会/期刊=${appointment.associationName}, 职务=${appointment.position}, 级别=${appointment.levelName}`);
        continue;
      }

      // 如果没有起始年，使用当前年份
      if (!appointment.startYear) {
        appointment.startYear = String(new Date().getFullYear());
        console.log(`起始年未提供，使用当前年份: ${appointment.startYear}`);
      }

      // 添加到学术任职列表
      appointments.push(appointment);
    }

    console.log(`处理完成，共解析出 ${appointments.length} 条学术任职记录`);

    return appointments;

  } catch (error) {
    console.error('转换Excel到学术任职JSON出错:', error);
    throw error;
    }
};

/**
 * 将Excel文件转换为教学科技奖励JSON格式
 * @param {File} file - Excel文件
 * @param {Object} options - 配置选项
 * @param {number} options.headerRow - 表头行索引，默认为0（第1行）
 * @param {string} options.sheetName - 工作表名称，默认为null（第一个工作表）
 * @returns {Promise<Object>} 解析后的奖励数据对象
 */
export const excelToTeachingResearchAwardsJson = async (file, options = {}) => {
  // 检查文件
  if (!file || !(file instanceof File)) {
    throw new Error('请提供有效的Excel文件');
  }

  const extension = getFileExtension(file.name).toLowerCase();
  if (!['.xlsx', '.xls', '.csv'].includes(extension)) {
    throw new Error('请上传有效的Excel文件 (.xlsx, .xls, .csv)');
  }

  const headerRow = options.headerRow ?? 0; // 默认第1行为表头
  const sheetName = options.sheetName || null;

  try {
    // 动态导入xlsx库
    const XLSX = await import('xlsx');

    // 读取文件
    const data = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          console.log('开始解析Excel文件...');
          const ab = e.target.result;
          const wb = XLSX.read(ab, { type: 'array' });

          console.log('Excel工作簿信息:', {
            sheetNames: wb.SheetNames,
            totalSheets: wb.SheetNames.length
          });

          const wsname = sheetName || wb.SheetNames[0];
          console.log(`选择工作表: ${wsname}`);

          const ws = wb.Sheets[wsname];

          // 获取工作表范围信息
          const range = XLSX.utils.decode_range(ws['!ref']);
          console.log('工作表范围:', {
            startRow: range.s.r,
            endRow: range.e.r,
            startCol: range.s.c,
            endCol: range.e.c,
            totalRows: range.e.r - range.s.r + 1,
            totalCols: range.e.c - range.s.c + 1
          });

          // 先读取所有数据查看原始内容
          const allData = XLSX.utils.sheet_to_json(ws, {
            raw: false,
            defval: null,
            header: 1 // 使用数组格式，便于查看原始数据
          });

          console.log('原始数据预览（前5行）:', allData.slice(0, 5));
          console.log(`总数据行数: ${allData.length}`);

          // 从指定行开始读取数据
          const jsonData = XLSX.utils.sheet_to_json(ws, {
            range: headerRow,
            raw: false,
            defval: null
          });

          console.log(`从第${headerRow + 1}行开始读取，解析出${jsonData.length}条记录`);
          console.log('解析后的数据结构（前3条）:', jsonData.slice(0, 3));

          // 检查表头信息
          if (jsonData.length > 0) {
            const headers = Object.keys(jsonData[0]);
            console.log('检测到的表头字段:', headers);
            console.log('表头字段数量:', headers.length);
          }

          resolve(jsonData);
        } catch (error) {
          console.error('解析Excel文件时出错:', error);
          reject(new Error(`解析Excel文件失败: ${error.message}`));
        }
      };
      reader.onerror = () => {
        console.error('FileReader读取文件失败');
        reject(new Error('读取文件失败'));
      };
      reader.readAsArrayBuffer(file);
    });

    // 处理数据，转换为教学科技奖励数据格式
    const awards = [];
    console.log(`开始处理${data.length}条原始数据...`);

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      console.log(`处理第${i + 1}行数据:`, row);

      // 检查关键字段
      const awardName = row['获奖名称'];
      console.log(`第${i + 1}行 - 获奖名称: "${awardName}", 类型: ${typeof awardName}`);

      // 跳过空行
      if (!awardName || awardName === null || String(awardName).trim() === '') {
        console.log(`第${i + 1}行 - 跳过空行（获奖名称为空）`);
        continue;
      }

      // 创建奖励对象
      const award = {
        awardName: String(awardName).trim(),
        awardLevelName: row['奖励名称/等级'] ? String(row['奖励名称/等级']).trim() : '',
        awardTime: parseCustomDate(row['获奖时间（写到月）']),
        department: row['系/教研室'] ? String(row['系/教研室']).trim() : '',
        responsiblePerson: row['负责人'] ? String(row['负责人']).trim() : '',
        participants: []
      };

      console.log(`第${i + 1}行 - 创建奖励对象:`, {
        awardName: award.awardName,
        awardLevelName: award.awardLevelName,
        awardTime: award.awardTime,
        department: award.department,
        responsiblePerson: award.responsiblePerson,
        rawAwardTime: row['获奖时间（写到月）']
      });

      // 处理获奖者信息 - 可以有多个获奖者
      const awardees = [];
      console.log(`第${i + 1}行 - 开始处理获奖者信息...`);

      // 处理获奖者1（负责人）
      const awardee1Name = row['获奖者1'];
      console.log(`第${i + 1}行 - 获奖者1: "${awardee1Name}"`);

      if (awardee1Name) {
        const personnelId = row['人事编号'];
        const allocationRatioRaw = row['分配比例'];
        console.log(`第${i + 1}行 - 获奖者1详细信息:`, {
          name: awardee1Name,
          personnelId: personnelId,
          allocationRatioRaw: allocationRatioRaw
        });

        const awardee1 = {
          name: String(awardee1Name).trim(),
          personnelId: personnelId ? String(personnelId).trim() : '',
          allocationRatio: parseFloat(String(allocationRatioRaw || '100').replace('%', '')) / 100,
          isLeader: true // 获奖者1为负责人
        };

        console.log(`第${i + 1}行 - 获奖者1处理结果:`, awardee1);
        awardees.push(awardee1);
      } else {
        console.log(`第${i + 1}行 - 未找到获奖者1信息`);
      }

      // 处理其他获奖者（获奖者2, 获奖者3等）
      let awardeeIndex = 2;
      while (row[`获奖者${awardeeIndex}`]) {
        const awardeeName = row[`获奖者${awardeeIndex}`];
        const personnelId = row[`人事编号${awardeeIndex}`];
        const allocationRatioRaw = row[`分配比例${awardeeIndex}`];

        console.log(`第${i + 1}行 - 获奖者${awardeeIndex}:`, {
          name: awardeeName,
          personnelId: personnelId,
          allocationRatioRaw: allocationRatioRaw
        });

        const awardee = {
          name: String(awardeeName).trim(),
          personnelId: personnelId ? String(personnelId).trim() : '',
          allocationRatio: parseFloat(String(allocationRatioRaw || '0').replace('%', '')) / 100,
          isLeader: false
        };

        awardees.push(awardee);
        awardeeIndex++;
      }

      console.log(`第${i + 1}行 - 共找到${awardees.length}个获奖者`);

      // 检查是否有其他可能的获奖者字段
      const allKeys = Object.keys(row);
      const awardeeKeys = allKeys.filter(key => key.includes('获奖者'));
      console.log(`第${i + 1}行 - 所有包含"获奖者"的字段:`, awardeeKeys);

      // 如果没有找到获奖者信息，但有负责人信息，使用负责人作为获奖者
      if (awardees.length === 0 && award.responsiblePerson) {
        console.log(`第${i + 1}行 - 未找到获奖者信息，使用负责人作为获奖者: ${award.responsiblePerson}`);
        awardees.push({
          name: award.responsiblePerson,
          personnelId: '',
          allocationRatio: 1.0,
          isLeader: true
        });
      }

      // 验证分配比例总和
      const totalRatio = awardees.reduce((sum, p) => sum + p.allocationRatio, 0);
      console.log(`第${i + 1}行 - 分配比例总和: ${totalRatio}`);

      if (Math.abs(totalRatio - 1) > 0.01 && awardees.length > 1) {
        console.log(`第${i + 1}行 - 分配比例总和不等于1，进行平均分配`);
        // 如果分配比例总和不等于1，平均分配
        const avgRatio = 1 / awardees.length;
        awardees.forEach(p => {
          p.allocationRatio = avgRatio;
        });
        console.log(`第${i + 1}行 - 平均分配后每人比例: ${avgRatio}`);
      }

      award.participants = awardees;

      // 处理审核人信息
      if (row['审核人']) {
        award.reviewer = String(row['审核人']).trim();
        console.log(`第${i + 1}行 - 审核人: ${award.reviewer}`);
      }

      console.log(`第${i + 1}行 - 最终奖励对象:`, award);
      awards.push(award);
    }

    console.log(`数据处理完成，共解析出${awards.length}条有效奖励记录`);
    console.log('所有奖励记录:', awards);

    const result = {
      data: awards,
      total: data.length,
      success: awards.length,
      failed: data.length - awards.length
    };

    console.log('最终转换结果:', result);
    console.log(`转换统计: 总行数=${result.total}, 成功=${result.success}, 失败=${result.failed}`);

    return result;

  } catch (error) {
    console.error('转换Excel到教学科技奖励JSON出错:', error);
    console.error('错误堆栈:', error.stack);
    throw error;
  }
};