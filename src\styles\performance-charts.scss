// 性能管理页面图表样式
// 用于统一所有性能页面的图表显示样式

// 图表容器样式
.chart-container {
  .ant-card-body {
    padding: 16px;
    min-height: 300px;
  }
  
  .chart-wrapper {
    width: 100%;
    height: 280px;
    min-height: 280px;
    position: relative;
    
    // 确保图表容器有足够的空间
    &.echarts-container {
      height: 320px;
      min-height: 320px;
    }
    
    // 影响因子图表特殊样式
    &.impact-factor-chart {
      height: 350px;
      min-height: 350px;
    }
  }
  
  // 图表加载状态
  .chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 280px;
    color: #999;
  }
  
  // 图表错误状态
  .chart-error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 280px;
    color: #ff4d4f;
    
    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    .error-message {
      font-size: 14px;
    }
  }
  
  // 图表无数据状态
  .chart-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 280px;
    color: #999;
    
    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    .empty-message {
      font-size: 14px;
    }
  }
}

// 统计卡片样式
.stats-card {
  .ant-statistic {
    .ant-statistic-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .ant-statistic-content {
      .ant-statistic-content-value {
        font-weight: 600;
      }
      
      .ant-statistic-content-suffix {
        font-size: 16px;
        margin-left: 4px;
      }
    }
  }
  
  // 不同类型统计的颜色
  &.primary-stat {
    .ant-statistic-content-value {
      color: #1890ff;
    }
  }
  
  &.success-stat {
    .ant-statistic-content-value {
      color: #52c41a;
    }
  }
  
  &.warning-stat {
    .ant-statistic-content-value {
      color: #faad14;
    }
  }
  
  &.danger-stat {
    .ant-statistic-content-value {
      color: #ff4d4f;
    }
  }
}

// 图表控制器样式
.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  
  .ant-select {
    min-width: 100px;
  }
  
  .control-label {
    font-size: 12px;
    color: #666;
    margin-right: 4px;
  }
}

// 图例样式调整
.chart-legend {
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 10;
  
  .legend-item {
    display: inline-flex;
    align-items: center;
    margin-right: 16px;
    font-size: 12px;
    
    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: 6px;
    }
    
    .legend-text {
      color: #666;
    }
  }
}

// 绩效分析模块专用样式
.performance-analysis {
  margin-bottom: 32px;

  // 分析控制面板
  .analysis-controls {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .analysis-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .ant-space {
        flex-wrap: wrap;
      }

      .ant-btn {
        border-radius: 6px;
        font-weight: 500;

        &.ant-btn-primary {
          background: linear-gradient(135deg, #1890ff, #40a9ff);
          border: none;
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);

          &:hover {
            background: linear-gradient(135deg, #40a9ff, #1890ff);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
          }
        }

        &:not(.ant-btn-primary) {
          border-color: #d9d9d9;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  // 绩效卡片样式
  .performance-card {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        font-weight: 600;
        font-size: 16px;
        color: #262626;
      }

      .ant-card-extra {
        .anticon {
          font-size: 16px;
          color: #999;
          cursor: help;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }

    .ant-card-body {
      padding: 20px;
    }

    // 图表容器
    .chart-wrapper {
      height: 400px;
      width: 100%;
      position: relative;

      &.echarts-container {
        canvas {
          border-radius: 4px;
        }
      }
    }

    // 加载状态
    .chart-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      color: #999;

      .ant-spin {
        margin-bottom: 16px;
      }

      div {
        font-size: 14px;
        color: #666;
      }
    }

    // 空状态
    .chart-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      color: #999;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
        color: #d9d9d9;
      }

      .empty-message {
        font-size: 14px;
        color: #999;
      }
    }

    // 图表底部信息
    .chart-info {
      margin-top: 16px;
      text-align: center;
      color: #666;
      font-size: 12px;
      padding: 8px;
      background: #fafafa;
      border-radius: 4px;
      border-top: 1px solid #f0f0f0;
    }
  }

  // 分析报告样式
  .analysis-report {
    .report-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #262626;
        border-left: 4px solid #1890ff;
        padding-left: 12px;
        background: linear-gradient(90deg, rgba(24, 144, 255, 0.1), transparent);
        padding: 8px 12px;
        border-radius: 4px;
      }

      .ant-descriptions {
        .ant-descriptions-item-label {
          font-weight: 500;
          color: #666;
        }

        .ant-descriptions-item-content {
          font-weight: 500;
        }
      }

      .ant-list {
        .ant-list-item {
          padding: 8px 0;
          border-bottom: 1px solid #f5f5f5;

          &:last-child {
            border-bottom: none;
          }

          .ant-list-item-meta {
            .ant-list-item-meta-title {
              margin-bottom: 4px;
              font-weight: 500;
            }

            .ant-list-item-meta-description {
              color: #666;
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}

// 响应式图表样式
@media (max-width: 1200px) {
  .chart-container {
    .chart-wrapper {
      height: 250px;
      min-height: 250px;

      &.echarts-container {
        height: 280px;
        min-height: 280px;
      }

      &.impact-factor-chart {
        height: 300px;
        min-height: 300px;
      }
    }

    .chart-loading,
    .chart-error,
    .chart-empty {
      height: 250px;
    }
  }

  .chart-controls {
    flex-wrap: wrap;
    gap: 6px;

    .ant-select {
      min-width: 80px;
    }
  }

  // 绩效分析响应式
  .performance-analysis {
    .performance-card {
      .chart-wrapper {
        height: 320px;
      }

      .chart-loading,
      .chart-empty {
        height: 320px;
      }
    }
  }
}

@media (max-width: 768px) {
  .chart-container {
    .ant-card-body {
      padding: 12px;
    }

    .chart-wrapper {
      height: 220px;
      min-height: 220px;

      &.echarts-container {
        height: 250px;
        min-height: 250px;
      }

      &.impact-factor-chart {
        height: 270px;
        min-height: 270px;
      }
    }

    .chart-loading,
    .chart-error,
    .chart-empty {
      height: 220px;
    }
  }

  .stats-card {
    .ant-statistic {
      .ant-statistic-title {
        font-size: 12px;
      }

      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 20px;
        }
      }
    }
  }

  .chart-legend {
    position: static;
    margin-top: 12px;
    text-align: center;

    .legend-item {
      margin: 4px 8px;
      font-size: 11px;
    }
  }

  // 绩效分析移动端优化
  .performance-analysis {
    .analysis-controls {
      .ant-row {
        flex-direction: column;
        gap: 16px;

        .ant-col {
          width: 100% !important;
          flex: none !important;
        }
      }

      .analysis-actions {
        justify-content: center;

        .ant-space {
          flex-wrap: wrap;
          justify-content: center;

          .ant-btn {
            min-width: 80px;
            font-size: 12px;
          }
        }
      }
    }

    .performance-card {
      margin-bottom: 16px;

      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
        }
      }

      .ant-card-body {
        padding: 16px;
      }

      .chart-wrapper {
        height: 280px;
      }

      .chart-loading,
      .chart-empty {
        height: 280px;

        .empty-icon {
          font-size: 36px;
        }

        .empty-message {
          font-size: 12px;
        }
      }

      .chart-info {
        font-size: 11px;
        padding: 6px;
      }
    }

    .analysis-report {
      .report-section {
        margin-bottom: 16px;

        .section-title {
          font-size: 14px;
          padding: 6px 10px;
        }

        .ant-descriptions {
          .ant-descriptions-item {
            padding-bottom: 8px;
          }
        }

        .ant-list {
          .ant-list-item {
            padding: 6px 0;

            .ant-list-item-meta {
              .ant-list-item-meta-title {
                font-size: 13px;
              }

              .ant-list-item-meta-description {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .chart-container {
    .chart-wrapper {
      height: 200px;
      min-height: 200px;
      
      &.echarts-container {
        height: 220px;
        min-height: 220px;
      }
      
      &.impact-factor-chart {
        height: 240px;
        min-height: 240px;
      }
    }
    
    .chart-loading,
    .chart-error,
    .chart-empty {
      height: 200px;
    }
  }
  
  .chart-controls {
    justify-content: center;
    
    .ant-select {
      min-width: 70px;
      font-size: 12px;
    }
    
    .control-label {
      font-size: 11px;
    }
  }
}
