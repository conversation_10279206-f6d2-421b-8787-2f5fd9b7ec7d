/**
 * 在主框架内显示
 * 路由配置说明
 * {
      path: '/dir-demo-info',    // 页面地址（唯一）
      name: 'dir-demo-info',     // 页面名称（唯一）
      hidden: false,              // 隐藏（不展示在侧边栏菜单） b
      meta: {
          title: '用户管理',       // 页面标题
          cache: true,          // 页面是否进行缓存 默认true
          link: false,           // 页面是否是外链 默认false
          frameSrc: false,       // 页面是否是内嵌 默认false
          requiresAuth: true,   // 页面是否是需要登录 默认true
          perms: [               // 页面的操作的权限列表
              'sys:user:list',   // 查询
              'sys:user:create', // 增加
              'sys:user:update', // 更新
              'sys:user:delete', // 删除
          ],
      },
      component: () => import('@/views/sys/users/dir-users-info.vue'),// 懒加载页面组件
   }
 *
 */
const frameIn = [
    {
        path: '/',
        redirect: { name: 'index' },
        component: () => import('@/layout/index.vue'),
        /*************************************************************************************/
        /********************children 建议最多 再加一级children  否则侧边栏体验不好*********************/
        /*************************************************************************************/
        children: [
            {
                path: '/index',
                name: 'index',
                meta: {
                    cache: true,
                    title: '首页',
                    requiresAuth: true,
                },
                component: () => import('@/views/home/<USER>'),
            },
            // 数据大屏菜单项
            // {
            //     path: '/data-screen',
            //     name: 'dataScreen',
            //     meta: {
            //         cache: false,
            //         title: '数据大屏',
            //         requiresAuth: true,
            //         isDataScreen: true,
            //     },
            //     component: () => import('@/views/demo/dataCenter/data-button.vue'),
            // },
            {
                path: '/sys',
                name: 'sys',
                meta: {
                    title: '系统管理',
                    requiresAuth: true,
                },
                children: [
                    {
                        path: '/dir-users-info',
                        name: 'dir-users-info',
                        meta: {
                            title: '用户管理',
                            requiresAuth: true,
                            perms: [
                                'sys:user:list',
                                'sys:user:create',
                                'sys:user:update',
                                'sys:user:delete',
                                'sys:user:reset',
                            ],
                        },
                        component: () => import('@/views/sys/users/dir-users-info.vue'),
                    },
                    {
                        path: '/dir-roles-info',
                        name: 'dir-roles-info',
                        meta: {
                            title: '角色管理',
                            requiresAuth: true,
                            perms: [ //页面的操作的权限
                                'sys:role:list',
                                'sys:role:create',
                                'sys:role:update',
                                'sys:role:delete',
                            ],
                        },
                        component: () => import('@/views/sys/roles/dir-roles-info.vue'),
                    },
                    {
                        path: '/dir-permissions-info',
                        name: 'dir-permissions-info',
                        meta: {
                            title: '权限管理',
                            requiresAuth: true,
                            perms: [
                                'sys:permissions:list',
                                'sys:permissions:create',
                                'sys:permissions:update',
                                'sys:permissions:delete',
                                'sys:permissions:reset',
                            ],
                        },
                        component: () => import('@/views/sys/permission/dir-permissions-info.vue'),
                    },
                    {
                        path: '/dir-users_opt_logs-info',
                        name: 'dir-users_opt_logs-info',
                        meta: {
                            title: '操作日志',
                            requiresAuth: true,
                            perms: [
                                'sys:optLog:list',
                                'sys:optLog:create',
                                'sys:optLog:update',
                                'sys:optLog:delete',
                                'sys:optLog:deleteAll',
                                'sys:optLog:export',
                            ],
                        },
                        component: () => import('@/views/sys/optLogs/dir-users_opt_logs-info.vue'),
                    },
                    {
                        path: '/dir-resources-info',
                        name: 'dir-resources-info',
                        meta: {
                            title: '资源管理',
                            requiresAuth: true,
                            perms: [
                                'sys:resources:list',
                                'sys:resources:create',
                                'sys:resources:update',
                                'sys:resources:delete'
                            ],
                        },
                        component: () => import('@/views/sys/resources/dir-resources-info.vue'),
                    },
                    {
                        path: '/dir-sql-ranking',
                        name: 'dir-sql-ranking',
                        meta: {
                            title: '排名管理',
                            requiresAuth: true,
                            perms: [
                                'sys:sql:updateRankings',
                                'sys:sql:updateUserRankings',
                                'sys:sql:getUserRankings'
                            ],
                        },
                        component: () => import('@/views/sys/sql/sql.vue'),
                    },
                    {
                        path: '/dir-departments-info',
                        name: 'dir-departments-info',
                        meta: {
                            title: '部门管理',
                            requiresAuth: true,
                            perms: [
                                'sys:departments:list',
                                'sys:departments:create',
                                'sys:departments:update',
                                'sys:departments:delete'
                            ],
                        },
                        component: () => import('@/views/sys/departments/departments.vue'),
                    },
                    {
                        path: '/dir-user-levels-info',
                        name: 'dir-user-levels-info',
                        meta: {
                            title: '用户级别管理',
                            requiresAuth: true,
                            perms: [
                                'sys:userLevels:list',
                                'sys:userLevels:create',
                                'sys:userLevels:update',
                                'sys:userLevels:delete'
                            ],
                        },
                        component: () => import('@/views/sys/userLevels/userLevels.vue'),
                    }
                ]
            },
            // 添加通知模块路由
            {
                path: '/notification',
                name: 'notification',
                meta: {
                    cache: true,
                    title: '通知管理',
                    requiresAuth: true,
                },
                children: [
                    {
                        path: '/notification/list',
                        name: 'notification-list',
                        meta: {
                            cache: true,
                            title: '通知列表',
                            requiresAuth: true,
                            perms: [
                                'notification:list:list',
                            ],
                        },
                        component: () => import('@/views/notification/list.vue'),
                    },
                    {
                        path: '/notification/send',
                        name: 'notification-send',
                        meta: {
                            cache: true,
                            title: '发送通知',
                            requiresAuth: true,
                            perms: [
                                'notification:send:list',
                            ],
                        },
                        component: () => import('@/views/notification/send.vue'),
                    },
                    // {
                    //     path: '/notification/settings',
                    //     name: 'notification-settings',
                    //     meta: {
                    //         cache: true,
                    //         title: '通知设置',
                    //         icon: 'shezhi',
                    //         requiresAuth: true,
                    //         perms: [
                    //             'notification:setting:list',
                    //         ],
                    //     },
                    //     component: () => import('@/views/notification/settings.vue'),
                    // },
                    // {
                    //     path: '/notification/template',
                    //     name: 'notification-template',
                    //     meta: {
                    //         cache: true,
                    //         title: '通知模板',
                    //         icon: 'yewumoban',
                    //         requiresAuth: true,
                    //         perms: [
                    //             'notification:template:list',
                    //         ],
                    //     },
                    //     component: () => import('@/views/notification/template.vue'),
                    // }
                ]
            },
            // 重定向页面 必须保留
            {
                path: '/redirect/:path(.*)/:_origin_params(.*)?',
                name: 'Redirect',
                hidden: true,//不展示在侧边栏菜单
                meta: {
                    title: '重定向',
                },
                component: () => import('@/views/sys/function/redirect'),
            },
            {
                path: '/performance',
                name: 'performance',
                meta: {
                    title: '绩效评分管理',
                    requiresAuth: true
                },
                children: [
                    {
                        path: '/performance/research',
                        name: 'research',
                        meta: {
                            title: '科研',
                            requiresAuth: true
                        },
                        children: [
                            {
                                path: '/performance/research-projects',
                                name: 'research-projects',
                                meta: {
                                    title: '>科研项目',
                                    requiresAuth: true,
                                    perms: [
                                        'score:researchProjects:self:list',
                                    ],
                                },
                                component: () => import('@/views/performance/research-projects.vue')
                            },
                            // {
                            //     path: '/performance/research-funds',
                            //     name: 'research-funds',
                            //     meta: {
                            //         title: 'B科研经费',
                            //         requiresAuth: true,
                            //         perms: [
                            //             'score:B:self:list',
                            //             'score:B:admin:list'
                            //         ]
                            //     },
                            //     component: () => import('@/views/performance/research-funds.vue')
                            // },
                            {
                                path: '/performance/high-level-papers',
                                name: 'high-level-papers',
                                meta: {
                                    title: '高水平论文',
                                    requiresAuth: true
                                },
                                component: () => import('@/views/performance/high-level-papers.vue')
                            },
                            {
                                path: '/performance/patents',
                                name: 'patents',
                                component: () => import('@/views/performance/patents.vue'),
                                meta: {
                                    title: '专利',
                                    requiresAuth: true,
                                    perms: [
                                        'score:patents:self:list',
                                    ], 
                                }
                            }
                        ]
                    },
                    {
                        path: '/performance/teaching-management',
                        name: 'teaching-management',
                        meta: {
                            title: '教学',
                            requiresAuth: true
                        },
                        children: [
                            {
                                path: '/performance/textbooks',
                                name: 'textbooks',
                                component: () => import('@/views/performance/textbooks.vue'),
                                meta: {
                                    title: '教材与著作',
                                    requiresAuth: true,
                                    perms: [
                                        'score:textbooks:self:list',
                                    ],
                                }
                            },
                            {
                                path: '/performance/teachingReformProjects',
                                name: 'teachingReformProjects',
                                component: () => import('@/views/performance/teachingReformProjects.vue'),
                                meta: {
                                    title: '教学改革项目',
                                    requiresAuth: true,
                                    perms: [
                                        'score:teachingReformProjects:self:list',
                                    ],
                                }
                            },
                            {
                                path: '/performance/studentProjectGuidanceProjects',
                                name: 'studentProjectGuidanceProjects',
                                component: () => import('@/views/performance/studentProjectGuidanceProjects.vue'),
                                meta: {
                                    title: '学生项目立项',
                                    requiresAuth: true,
                                    perms: [
                                        'score:studentProjectGuidanceProjects:self:list',
                                    ],
                                }
                            },
                            {
                                path: '/performance/studentAwardGuidanceAwards',
                                name: 'studentAwardGuidanceAwards',
                                component: () => import('@/views/performance/studentAwardGuidanceAwards.vue'),
                                meta: {
                                    title: '学生项目获奖',
                                    requiresAuth: true,
                                    perms: [
                                        'score:studentAwardGuidanceAwards:self:list',
                                    ],
                                }
                            },
                            {
                                path: '/performance/teaching-research-awards',
                                name: 'teaching-research-awards',
                                component: () => import('@/views/performance/teachingResearchAwards.vue'),
                                meta: {
                                    title: '教学科研奖励',
                                    requiresAuth: true,
                                    perms: [
                                        'score:teachingResearchAwards:self:list',
                                    ],
                                }
                            },
                            {
                                path: '/performance/teaching-workloads',
                                name: 'teaching-workloads',
                                component: () => import('@/views/performance/teaching-workloads.vue'),
                                meta: {
                                    title: '教学工作量',
                                    requiresAuth: true,
                                    perms: [
                                        'score:teachingWorkloads:self:list',
                                    ],
                                }
                            }
                        ]
                    },
                    {
                        path: '/performance/social-services',
                        name: 'social-services',
                        meta: {
                            title: '社会服务',
                            requiresAuth: true,
                            perms: [
                                'score:socialServices:self:list',
                            ],
                        },
                        children: [
                            {
                                path: '/performance/conferences',
                                name: 'conferences',
                                component: () => import('@/views/performance/conferences.vue'),
                                meta: {
                                    title: '会议',
                                    requiresAuth: true,
                                    perms: [
                                        'score:conferences:self:list',
                                    ],
                                }
                            },
                            {
                                path: '/performance/academicAppointments',
                                name: 'academicAppointments',
                                component: () => import('@/views/performance/academicAppointments.vue'),
                                meta: {
                                    title: '学术任职',
                                    requiresAuth: true,
                                    perms: [
                                        'score:academicAppointments:self:list',
                                    ],
                                }
                            }
                        ]
                    },
                    // {
                    //     path: '/performance/international-affairs',
                    //     name: 'international-affairs',
                    //     meta: {
                    //         title: '国际交流',
                    //         requiresAuth: true,
                    //         perms: [
                    //             'score:internationalAffairs:self:list',
                    //         ],
                    //     },
                    //     children: [
                    //         {
                    //             path: '/performance/international-exchange',
                    //             name: 'international-exchange',
                    //             meta: {
                    //                 title: 'E国际交流',
                    //                 requiresAuth: true,
                    //                 perms: [
                    //                     'score:internationalExchanges:self:list',
                    //                 ],
                    //             },
                    //             component: () => import('@/views/performance/international-exchange.vue')
                    //         }
                    //     ]
                    // },
                    // {
                    //     path: '/performance/others',
                    //     name: 'others',
                    //     meta: {
                    //         title: '其他',
                    //         requiresAuth: true,
                    //         perms: [
                    //             'score:others:self:list',
                    //         ],
                    //     },
                    //     children: [
                    //         {
                    //             path: '/performance/employment-quality',
                    //             name: 'employment-quality',
                    //             meta: {
                    //                 title: 'G就业质量',
                    //                 requiresAuth: true,
                    //                 perms: [
                    //                     'score:employmentQuality:self:list',
                    //                 ],
                    //             },
                    //             component: () => import('@/views/performance/employment-quality.vue')
                    //         },
                    //         {
                    //             path: '/performance/deductions',
                    //             name: 'deductions',
                    //             meta: {
                    //                 title: 'H扣分',
                    //                 requiresAuth: true,
                    //                 perms: [
                    //                     'score:deductions:self:list',
                    //                 ],
                    //             },
                    //             component: () => import('@/views/performance/deductions.vue')
                    //         }
                    //     ]
                    // },
                ]
            },
            {
                path: '/rules',
                name: 'rules',
                meta: {
                    title: '绩效评分规则管理',
                    requiresAuth: true
                },
                children: [
                    {
                        path: '/rules/research',
                        name: 'rules-research',
                        meta: {
                            title: '科研规则',
                            requiresAuth: true
                        },
                        children: [
                            {
                                path: '/rules/researchProjectsLevels',
                                name: 'researchProjectsLevels',
                                component: () => import('@/views/rules/researchProjectsLevels.vue'),
                                meta: {
                                    title: '科研项目级别',
                                    requiresAuth: true,
                                    perms: ['rule:A:list']
                                }
                            },
                            // {
                            //     path: '/rules/researchFundsRules',
                            //     name: 'researchFundsRules',
                            //     component: () => import('@/views/rules/researchFundsRules.vue'),
                            //     meta: {
                            //         title: 'B科研经费规则',
                            //         requiresAuth: true,
                            //         perms: ['rule:B:list']
                            //     }
                            // },
                            {
                                path: '/rules/highLevelPapersRules',
                                name: 'highLevelPapersRules',
                                component: () => import('@/views/rules/highLevelPapersRules.vue'),
                                meta: {
                                    title: '高水平论文',
                                    requiresAuth: true,
                                    perms: ['rule:C:list']
                                }
                            },
                            {
                                path: '/rules/patentCategories',
                                name: 'patentCategories',
                                component: () => import('@/views/rules/patentCategories.vue'),
                                meta: {
                                    title: '专利分类',
                                    requiresAuth: true,
                                    perms: ['rule:patent:category:list']
                                }
                            }
                        ]
                    },
                    {
                        path: '/rules/teaching-management',
                        name: 'rules-teaching-management',
                        meta: {
                            title: '教学规则',
                            requiresAuth: true
                        },
                        children: [
                            {
                                path: '/rules/textbookCategories',
                                name: 'textbookCategories',
                                component: () => import('@/views/rules/textbookCategories.vue'),
                                meta: {
                                    title: '教材类别',
                                    requiresAuth: true,
                                    perms: ['rule:textbook:category:list']
                                }
                            },
                            {
                                path: '/rules/teachingReformProjectLevels',
                                name: 'teachingReformProjectLevels',
                                component: () => import('@/views/rules/teachingReformProjectLevels.vue'),
                                meta: {
                                    title: '教改项目级别',
                                    requiresAuth: true,
                                    perms: ['rule:teachingReformProjectLevel:list']
                                }
                            },
                            {
                                path: '/rules/studentProjectGuidanceProjectLevels',
                                name: 'studentProjectGuidanceProjectLevels',
                                component: () => import('@/views/rules/studentProjectGuidanceProjectLevels.vue'),
                                meta: {
                                    title: '学生立项级别',
                                    requiresAuth: true,
                                    perms: ['rule:studentProjectGuidanceProjectLevel:list']
                                }
                            },
                            {
                                path: '/rules/studentAwardGuidanceAwardLevels',
                                name: 'studentAwardGuidanceAwardLevels',
                                component: () => import('@/views/rules/studentAwardGuidanceAwardLevels.vue'),
                                meta: {
                                    title: '学生获奖级别',
                                    requiresAuth: true,
                                    perms: ['rule:studentAwardGuidanceAwardLevel:list']
                                }
                            },
                            {
                                path: '/rules/teachingResearchAwardLevels',
                                name: 'teachingResearchAwardLevels',
                                component: () => import('@/views/rules/teachingResearchAwardLevels.vue'),
                                meta: {
                                    title: '教学科技奖励级别',
                                    requiresAuth: true,
                                    perms: ['rule:teachingResearchAwards:list']
                                }
                            },
                            {
                                path: '/rules/teaching-workload-levels',
                                name: 'teaching-workload-levels',
                                component: () => import('@/views/rules/teaching-workload-levels.vue'),
                                meta: {
                                    title: '教学工作量级别',
                                    requiresAuth: true,
                                    perms: ['rule:teachingWorkloads:list']
                                }
                            }
                        ]
                    },
                    {
                        path: '/rules/social-services',
                        name: 'rules-social-services',
                        meta: {
                            title: '社会服务',
                            requiresAuth: true
                        },
                        children: [
                            {
                                path: '/rules/conferencesLevels',
                                name: 'conferencesLevels',
                                component: () => import('@/views/rules/conferencesLevels.vue'),
                                meta: {
                                    title: '会议级别',
                                    requiresAuth: true,
                                    perms: ['rule:conference:level:list']
                                }
                            },
                            {
                                path: '/rules/associationLevels',
                                name: 'associationLevels',
                                component: () => import('@/views/rules/associationLevels.vue'),
                                meta: {
                                    title: '学术任职',
                                    requiresAuth: true,
                                    perms: ['rule:academic:level:list']
                                }
                            }
                        ]
                    },
                    // {
                    //     path: '/rules/international-affairs',
                    //     name: 'rules-international-affairs',
                    //     meta: {
                    //         title: '国际交流',
                    //         requiresAuth: true
                    //     },
                    //     children: [
                    //         {
                    //             path: '/rules/internationalExchangesRules',
                    //             name: 'internationalExchangesRules',
                    //             component: () => import('@/views/rules/internationalExchangesRules.vue'),
                    //             meta: {
                    //                 title: '国际交流',
                    //                 requiresAuth: true,
                    //                 perms: ['rule:E:list']
                    //             }
                    //         }
                    //     ]
                    // },
                    {
                        path: '/rules/others',
                        name: 'rules-others',
                        meta: {
                            title: '其他',
                            requiresAuth: true
                        },
                        children: [
                            // {
                            //     path: '/rules/deductionsRules',
                            //     name: 'deductionsRules',
                            //     component: () => import('@/views/rules/deductionsRules.vue'),
                            //     meta: {
                            //         title: '扣分规则',
                            //         requiresAuth: true,
                            //         perms: ['rule:H:list']
                            //     }
                            // },
                            {
                                path: '/time-interval',
                                name: 'time-interval',
                                meta: {
                                    title: '时间区间管理',
                                    requiresAuth: true,
                                    perms: [
                                        'rule:timeInterval:list',
                                        'rule:timeInterval:create',
                                        'rule:timeInterval:update',
                                        'rule:timeInterval:delete'
                                    ],
                                },
                                component: () => import('@/views/sys/TimeInterval.vue'),
                            },
                            {
                                path: '/score-weights',
                                name: 'score-weights',
                                meta: {
                                    title: '评分权重管理',
                                    requiresAuth: true,
                                    perms: [
                                        'rule:scoreWeight:list',
                                        'rule:scoreWeight:create',
                                        'rule:scoreWeight:update',
                                        'rule:scoreWeight:delete'
                                    ]
                                },
                                component: () => import('@/views/rules/scoreWeights.vue'),
                            }
                        ]
                    }
                ]
            },
        ]
    },
]

/**
 * 在主框架之外显示
 */
const frameOut = [
    // 登录
    {
        path: '/login',
        name: 'login',
        meta: {
            title: '登录',
        },
        component: () => import('@/views/sys/login/dir-login-info.vue'),
    },
    // 数据大屏 - 独立窗口
    // {
    //     path: '/dataCenter',
    //     name: 'dataCenter',
    //     meta: {
    //         title: '数据大屏',
    //         requiresAuth: true,
    //         fullscreen: true,
    //     },
    //     component: () => import('@/views/demo/dataCenter/index.vue'),
    // }
]

/**
 * 错误页面
 */
const errorPage = [
    {
        path: '/401',
        name: '401',
        component: () => import('@/views/error/401.vue'),
        meta: {
            title: '401',
        },
    },
    {
        path: '/:pathMatch(.*)*',
        name: '404',
        component: () => import('@/views/error/404.vue'),
        meta: {
            title: '404',
        },
    }
]

// 导出需要显示菜单的
export const frameInRoutes = frameIn

// 重新组织后导出
export default [...frameIn, ...frameOut, ...errorPage]
