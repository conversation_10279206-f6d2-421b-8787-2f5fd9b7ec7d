const express = require('express');
const router = express.Router();
const patentController = require('../../../controllers/v1/patents/patentsController');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建专利权限中间件函数
const patentsPermission = (action) => createModulePermission('patents', action);

/**
 * 获取专利列表（统一接口）
 * @route POST /v1/sys/patents/list
 * @group 专利管理 - 专利相关接口
 * @param {string} patentName - 专利名称（模糊搜索）
 * @param {string} categoryId - 专利分类ID
 * @param {string} authorizationDateStart - 授权开始日期
 * @param {string} authorizationDateEnd - 授权结束日期
 * @param {string} userId - 用户ID（可选，如果提供则只返回该用户参与的专利）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)，默认'all'
 * @param {string} reviewStatus - 审核状态筛选: 'all'(全部), 'reviewed'(已通过), 'reject'(已拒绝), 'pending'(待审核)，默认'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, page: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', 
  authMiddleware, 
  patentsPermission('list'), 
  patentController.getPatents
);


/**
 * 导入专利数据
 * @route POST /v1/sys/patents/import
 * @group 专利管理 - 专利相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', 
  authMiddleware, 
  patentsPermission('import'), 
  upload.single('file'), 
  patentController.importPatents
);

/**
 * 导出专利数据
 * @route GET /v1/sys/patents/export
 * @group 专利管理 - 专利相关接口
 * @param {string} patentName.query - 专利名称（模糊搜索）
 * @param {string} categoryId.query - 专利分类ID
 * @param {string} authorizationDateStart.query - 授权开始日期
 * @param {string} authorizationDateEnd.query - 授权结束日期
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/export', 
  authMiddleware, 
  patentsPermission('export'), 
  patentController.exportPatents
);

/**
 * 创建专利（前端兼容接口）
 * @route POST /v1/sys/patents/create
 * @group 专利管理 - 专利相关接口
 * @param {string} patentName.body.required - 专利名称
 * @param {string} categoryId.body.required - 专利分类ID
 * @param {string} authorizationDate.body.required - 授权日期
 * @param {string} conversionDate.body - 转化日期
 * @param {string} remark.body - 备注
 * @param {Array} participants.body.required - 参与者数组，包含participantId, allocationRatio, isLeader
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', 
  authMiddleware, 
  patentsPermission('create'), 
  upload.array('files', 5), 
  patentController.createPatent
);

/**
 * 更新专利（前端兼容接口）
 * @route POST /v1/sys/patents/update
 * @group 专利管理 - 专利相关接口
 * @param {string} id.body.required - 专利ID
 * @param {string} patentName.body - 专利名称
 * @param {string} categoryId.body - 专利分类ID
 * @param {string} authorizationDate.body - 授权日期
 * @param {string} conversionDate.body - 转化日期
 * @param {string} remark.body - 备注
 * @param {Array} participants.body - 参与者数组，包含participantId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @param {Array} deletedFileIds.body - 要删除的文件ID数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update', 
  authMiddleware, 
  patentsPermission('update'), 
  upload.array('files', 5), 
  async (req, res) => {
    const { id, ...updateData } = req.body;
    req.params = { id };
    req.body = updateData;
    await patentController.updatePatent(req, res);
  }
);

/**
 * 删除专利（前端兼容接口）
 * @route POST /v1/sys/patents/delete
 * @group 专利管理 - 专利相关接口
 * @param {string} id.body.required - 专利ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', 
  authMiddleware, 
  patentsPermission('delete'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await patentController.deletePatent(req, res);
  }
);

/**
 * 获取专利详情（GET方法）
 * @route GET /v1/sys/patents/detail
 * @group 专利管理 - 专利相关接口
 * @param {string} id.query.required - 专利ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail', 
  authMiddleware, 
  patentsPermission('detail'), 
  async (req, res) => {
    const { id } = req.query;
    console.log("req.query",req.query);
    
    if (!id) {
      return res.status(400).json({ code: 400, message: "专利ID不能为空", data: null });
    }
    req.params = { id };
    await patentController.getPatentDetail(req, res);
  }
);

/**
 * 获取专利详情（POST方法）
 * @route POST /v1/sys/patents/detail
 * @group 专利管理 - 专利相关接口
 * @param {string} id.body.required - 专利ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/detail', 
  authMiddleware, 
  patentsPermission('detail'), 
  async (req, res) => {
    const { id } = req.body;
    if (!id) {
      return res.status(400).json({ code: 400, message: "专利ID不能为空", data: null });
    }
    req.params = { id };
    await patentController.getPatentDetail(req, res);
  }
);

/**
 * 获取专利时间分布数据
 * @route POST /v1/sys/patents/patent-statistics/time-distribution
 * @group 专利统计 - 专利统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的专利
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {months: ["YYYY-MM",...], data: [数量,...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/patent-statistics/time-distribution', 
  authMiddleware, 
  patentsPermission('timeDistribution'), 
  patentController.getTimeDistribution
);

/**
 * 审核专利
 * @route POST /v1/sys/patents/:id/review
 * @group 专利管理 - 专利相关接口
 * @param {string} id.path.required - 专利ID
 * @param {number} reviewStatus.body - 审核状态(1:通过 0:拒绝)
 * @param {string} reviewComment.body - 审核意见
 * @param {string} reviewer.body - 审核人ID
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/:id/review', 
  authMiddleware, 
  patentsPermission('review'), 
  patentController.reviewPatent
);

/**
 * 获取所有用户专利总得分统计
 * @route POST /v1/sys/patents/patent-statistics/teacher-ranking
 * @group 专利统计 - 专利统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {object} timeRange.body - 可选的自定义时间范围，包含startDate和endDate字段
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @param {string} sortField.body - 排序字段，可选值：'totalScore'(总分),'userScore'(用户得分)，默认'totalScore'
 * @param {string} sortOrder.body - 排序方向，可选值：'asc'(升序),'desc'(降序)，默认'desc'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{userId: "用户ID", totalScore: 总分},...]}
 * @returns {Error} default - Unexpected error  
 * @security JWT
 */
router.post('/patent-statistics/teacher-ranking', 
  authMiddleware, 
  patentsPermission('getAllUsersTotalScore'), 
  patentController.getAllUsersTotalScore
);

/**
 * 获取用户专利详情
 * @route POST /v1/sys/patents/patent-statistics/user-project-details
 * @group 专利统计 - 专利统计相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值：'reviewed'(已通过), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部)，默认'all'
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{id, name, level, type, approvalDate, startDate, endDate, totalScore, userScore, role, userAllocationRatio, isLeader}], totalScore: 总分, user: {id, name, employeeNumber}, pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/patent-statistics/user-patent-details', 
  authMiddleware, 
  patentsPermission('getUserPatentDetails'), 
  patentController.getUserPatentDetails
);

/**
 * 获取专利总分统计
 * @route POST /v1/sys/patents/statistics/patents-total-score
 * @group 专利统计 - 专利统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值：'reviewed'(已通过), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部)，默认'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {typeStats: [{categoryId, categoryName, count, totalScore},...], overallStats: {totalPatents, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/patents-total-score', 
  authMiddleware, 
  patentsPermission('getPatentsTotalScore'), 
  patentController.getPatentsTotalScore
);

/**
 * 获取用户专利详情
 * @route POST /v1/sys/patents/user/details
 * @group 专利统计 - 专利统计相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值：'reviewed'(已通过), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部)，默认'all'
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{patentId, patentName, categoryName, authorizationDate, baseScore, actualScore, reviewStatus...}], stats: {totalPatents, leaderPatentCount, participantPatentCount, totalScore}, user: {id, name, employeeNumber}, pagination: {total, page, pageSize, totalPages}, timeInterval}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/details', 
  authMiddleware, 
  patentsPermission('getUserPatentsDetail'), 
  patentController.getUserPatentsDetail
);

/**
 * 重新提交专利审核
 * @route POST /v1/sys/patents/reapply
 * @group 专利管理 - 专利相关接口
 * @param {string} id.body.required - 专利ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/reapply',
  authMiddleware,
  patentsPermission('reapply'),
  patentController.reapply
);

/**
 * 获取审核状态概览
 * @route POST /v1/sys/patents/statistics/review-status-overview
 * @group 专利统计 - 专利统计相关接口
 * @param {string} range.body - 查询范围：'in'|'out'|'all'，默认'all'
 * @param {string} userId.body - 用户ID，可选
 * @returns {object} 200 - 审核状态统计数据
 * @security JWT
 */
router.post('/statistics/review-status-overview',
  authMiddleware,
  patentsPermission('reviewStatusOverview'),
  patentController.getReviewStatusOverview
);

module.exports = router;