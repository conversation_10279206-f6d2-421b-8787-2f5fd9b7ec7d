<template>
  <a-modal
    :visible="visible"
    title="审核教学科技奖励"
    width="700px"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div v-if="loadingDetails" class="loading-container">
      <a-spin tip="加载详情中..." />
    </div>
    <div v-else>
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="奖励名称">
          <span>{{ awardDetails?.awardName || '-' }}</span>
        </a-form-item>

        <a-form-item label="奖励级别">
          <span>{{ awardDetails?.awardLevel?.levelName || '-' }}</span>
        </a-form-item>

        <a-form-item label="审核结果" name="ifReviewer">
          <a-radio-group v-model:value="form.ifReviewer">
            <a-radio :value="1">
              <CheckCircleOutlined style="color: #52c41a" /> 通过审核
            </a-radio>
            <a-radio :value="0">
              <CloseCircleOutlined style="color: #ff4d4f" /> 拒绝审核
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="审核意见" name="reviewComment">
          <a-textarea
            v-model:value="form.reviewComment"
            placeholder="请输入审核意见（选填）"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>

        <!-- 附件列表 -->
        <a-form-item v-if="attachments.length > 0" label="相关附件">
          <div class="attachment-list">
            <a-table
              :columns="attachmentColumns"
              :data-source="attachments"
              :pagination="false"
              size="small"
              rowKey="id"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fileName'">
                  {{ record.originalName || record.fileName }}
                </template>
                <template v-else-if="column.key === 'fileSize'">
                  {{ formatFileSize(record.size) }}
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="previewFile(record)">
                      <template #icon><EyeOutlined /></template>
                      预览
                    </a-button>
                    <a-button type="link" size="small" @click="downloadFile(record)">
                      <template #icon><DownloadOutlined /></template>
                      下载
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </div>
        </a-form-item>
        <a-form-item v-else label="相关附件">
          <span class="no-attachments">无附件</span>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { reviewTeachingResearchAward, getTeachingResearchAwardDetail } from '@/api/modules/api.teachingResearchAwards'
import { previewFileById, downloadFileById } from '@/utils/others'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  awardId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const loadingDetails = ref(false)
const formRef = ref()
const awardDetails = ref(null)
const attachments = ref([])

// 附件表格列定义
const attachmentColumns = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    key: 'fileName',
    ellipsis: true
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center'
  }
]

// 表单数据
const form = reactive({
  ifReviewer: 1,
  reviewComment: ''
})

// 表单验证规则
const rules = {
  ifReviewer: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ]
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.awardId) {
    resetForm()
    fetchAwardDetails(props.awardId)
  }
})

// 获取奖励详情
const fetchAwardDetails = async (id) => {
  if (!id) return
  loadingDetails.value = true
  try {
    const response = await getTeachingResearchAwardDetail({ id })
    if (response.code === 200) {
      awardDetails.value = response.data
      // 处理附件数据
      if (response.data.attachments && Array.isArray(response.data.attachments)) {
        attachments.value = response.data.attachments.map((file, index) => ({
          ...file,
          id: file.id || `file-${index}`,
          fileName: file.originalName || file.name || `附件${index + 1}`
        }))
      } else {
        attachments.value = []
      }
    } else {
      message.error(response?.message || '获取奖励详情失败')
    }
  } catch (error) {
    console.error('获取奖励详情失败:', error)
    message.error('获取奖励详情失败: ' + (error.message || '未知错误'))
  } finally {
    loadingDetails.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    ifReviewer: 1,
    reviewComment: ''
  })
  awardDetails.value = null
  attachments.value = []
}

// 提交审核
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    if (!props.awardId) {
      message.error('奖励ID不存在')
      loading.value = false
      return
    }

    // 调用审核API
    const response = await reviewTeachingResearchAward({
      id: props.awardId,
      ifReviewer: form.ifReviewer,
      reviewComment: form.reviewComment
    })

    if (response && response.code === 200) {
      message.success('审核成功')
      emit('success')
      handleCancel()
    } else {
      message.error(response?.message || '审核失败')
    }
  } catch (error) {
    console.error('审核失败:', error)
    message.error('审核失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 预览文件
const previewFile = (file) => {
  if (file.id) {
    previewFileById(file.id)
  } else {
    message.warning('无法预览文件，文件ID不存在')
  }
}

// 下载文件
const downloadFile = (file) => {
  if (file.id) {
    downloadFileById(file.id, file.fileName || file.originalName || '下载文件')
  } else {
    message.warning('无法下载文件，文件ID不存在')
  }
}

// 文件大小格式化
const formatFileSize = (bytes) => {
  if (!bytes || isNaN(bytes)) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.attachment-list {
  max-height: 250px;
  overflow-y: auto;
}

.no-attachments {
  color: #999;
  font-style: italic;
}
</style>
