const jwt = require("jsonwebtoken");
const {body, query, validationResult} = require('express-validator');
const userModel = require('../../../models/v1/mapping/userModel');
const apiResponse = require('@utils/apiResponse')
const crypto = require('../../../utils/crypto');
const { aes } = crypto;
const logger = require('@utils/logger')
const svgCaptcha = require('svg-captcha');
const {actionRecords} = require("@middleware/actionLogMiddleware");
const UAParser = require("ua-parser-js");
const axios = require("axios");
const querystring = require('querystring');
const {randomNumber, parseIP, getPublicIP, getEmailAvatar, encryption} = require('@utils/others');
const {sendMail} = require('@utils/mailer');
const { JWT_SECRET } = require('@config');
const chalk = require('chalk');;
const {JWT_EXPIRES_IN, SERVER_CONFIG, USER_CONFIG} = require('../../../config');
const moment = require('moment');
/**
 * TODO:
 *   express-validator : https://express-validator.github.io/docs/
 *   参数校验方法查询（基于validator.js库）: https://github.com/validatorjs/validator.js#Validators
 *   eg:isLength isEmail trim ...
 * */
/******************************************************************************************/

/**
 * 验证码
 * @route GET /v1/sys/auth/captcha
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200
 * @returns {Error}  default - Unexpected error
 */
exports.captcha = [
    async (req, res) => {
        try {
            //验证码配置api
            let options = {
                //线条数
                noise: 1,
                color: true,
                fontSize: 55,
                width: 90,
                height: 38,
                size: 4,
                ignoreChars: '0o1i', // 忽略容易混淆的字符
                background: '#f0f0f0',
                charPreset: '0123456789' // 只使用数字
            }
            let captcha = svgCaptcha.create(options)
            
            // 打印原始session信息
            console.log('\n===========================================');
            console.log('生成验证码前 - Session信息:');
            console.log('Session ID:', req.sessionID);
            console.log('Session内容:', req.session);
            console.log('Cookie:', req.headers.cookie);
            
            // 确保会话对象存在
            if (!req.session) {
                console.error('会话对象不存在，请检查session中间件');
                return apiResponse.errorResponse(res, "会话初始化失败");
            }
            
            //存储到session
            req.session.code = captcha.text;
            console.log('验证码已设置到会话:', captcha.text);
            
            // 强制保存会话
            req.session.save((err) => {
                if (err) {
                    console.error('会话保存失败:', err);
                    return apiResponse.errorResponse(res, "会话保存失败");
                }
                
                // 会话保存成功后的日志
                console.log('会话保存成功:');
                console.log('Session ID:', req.sessionID);
                console.log('Session内容:', req.session);
                console.log('验证码数据大小:', captcha.data.length, '字节');
                console.log('请求头Cookie:', req.headers.cookie);
                console.log('请求IP:', getPublicIP(req));
                console.log('===========================================\n');
                
                // 设置响应头，禁止缓存
                res.setHeader('Content-Type', 'image/svg+xml');
                res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
                res.setHeader('Pragma', 'no-cache');
                res.setHeader('Expires', '0');
                
                // 返回验证码图片
                apiResponse.successResponseWithData(res, "成功", captcha.data);
            });
        } catch (err) {
            console.error('生成验证码错误:', err);
            return apiResponse.errorResponse(res, err);
        }
    }
]

/**
 * 登录
 * @route POST /v1/sys/auth/login
 * @group 权限验证 - 登录注册相关
 * @param {string} username 用户名或昵称
 * @param {string} password 密码
 * @param {string} code 验证码
 * @returns {object} 200 - {"status": 1,"message": "登录成功.","data": {...},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */
exports.login = [
    actionRecords({module: '登录'}),
    //参数验证
    [
        body("username").notEmpty().withMessage('用户名或昵称不能为空.'),
        body("password").notEmpty().withMessage('密码不能为空.'),
        body("code").notEmpty().withMessage('验证码不能为空.'),
    ],

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                console.log('参数验证错误：', errors.array());
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            console.log('\n===========================================');
            console.log('登录请求信息：');
            console.log('用户名或昵称：', req.body.username);
            console.log('Session ID：', req.sessionID);
            console.log('Session状态：', req.session);
            console.log('请求头：', req.headers);
            console.log('请求体：', req.body);
            //console.log('Cookie：', req.headers.cookie);
            
            // 直接检查session中的验证码，不进行reload
            if (!req.session.code) {
                console.log('验证码已失效，session中无验证码');
                console.log('当前session内容：', req.session);
                console.log('Session ID：', req.sessionID);
                //console.log('Cookie：', req.headers.cookie);
                return apiResponse.validationErrorResponse(res, "验证码已失效");
            }

            // 添加调试日志
            console.log('验证码验证：');
            console.log('Session中的验证码:', req.session.code, typeof req.session.code);
            console.log('用户输入的验证码:', req.body.code, typeof req.body.code);
            console.log('Session完整内容:', req.session);
            
            // 确保类型一致，将两者都转为字符串再比较
            if (String(req.session.code).trim() !== String(req.body.code).trim()) {
                console.log('验证码不匹配');
                console.log('Session验证码(处理后):', String(req.session.code).trim());
                console.log('用户输入(处理后):', String(req.body.code).trim());
                console.log('Session ID:', req.sessionID);
                console.log('Cookie:', req.headers.cookie);
                return apiResponse.validationErrorResponse(res, "验证码错误");
            }
            console.log('验证码验证通过');
            console.log('===========================================\n');

            // 首先尝试通过用户名查找用户
            let userWithData = await userModel.findOne({ where: {username: req.body.username} });
            
            // 如果通过用户名没找到，则尝试通过昵称查找
            if (!userWithData) {
                userWithData = await userModel.findOne({ where: {nickname: req.body.username} });
                if (!userWithData) {
                    return apiResponse.validationErrorResponse(res, "用户名或昵称不存在");
                }
            }

            // 密码和数据库密码匹配
            let isPass = userWithData.password === aes.en(req.body.password);
            if (!isPass) {
                return apiResponse.validationErrorResponse(res, "用户名或密码错误");
            }

            if (!userWithData.status) {
                return apiResponse.unauthorizedResponse(res, "当前账户已被禁用,请联系管理员.");
            }

            // 确保用户头像存在
            if (!userWithData.avatar) {
                await userWithData.update({ avatar: USER_CONFIG.defaultAvatar });
            }

            // 构建响应给前端的数据
            const userData = {
                id: userWithData.id,
                username: userWithData.username,
                nickname: userWithData.nickname || userWithData.username,
                roleId: userWithData.roleId,
                status: userWithData.status,
                avatar: userWithData.avatar || USER_CONFIG.defaultAvatar,
            };

            // 使用统一的JWT密钥配置
            const jwtSecret = JWT_SECRET || 'your-jwt-secret-key';
            
            // 打印角色ID信息用于调试
            console.log('===========================================');
            console.log('登录成功 - 用户信息:', {
                id: userWithData.id,
                username: userWithData.username,
                nickname: userWithData.nickname,
                roleId: userWithData.roleId,
                status: userWithData.status
            });
            console.log('登录成功 - 使用的JWT密钥:', jwtSecret);
            
            // 创建JWT payload
            const payload = {
                id: userWithData.id,
                username: userWithData.username,
                nickname: userWithData.nickname,
                roleId: userWithData.roleId,
                status: userWithData.status
            };
            
            console.log('登录成功 - JWT payload:', JSON.stringify(payload));
            
            // 生成token
            const token = jwt.sign(payload, jwtSecret, { expiresIn: 3600 * 24 * 3 });
            console.log('登录成功 - Token前20字符:', token.substring(0, 20) + '...');
            
            // 验证生成的token是否有效（仅自检，不影响响应）
            try {
                const decoded = jwt.verify(token, jwtSecret);
                console.log('登录成功 - Token自检通过，解码数据:', JSON.stringify(decoded));
            } catch (verifyError) {
                console.error('登录成功 - Token自检失败:', verifyError.message);
                // 仅记录错误，不影响登录流程
            }
            
            userData.token = 'Bearer ' + token;
            console.log('===========================================');

            logger.info(`*** 昵称: ${userWithData.nickname} 登录成功`);
            return apiResponse.successResponseWithData(res, "登录成功.", userData);
        } catch (err) {
            console.error(err);
            logger.error(`*** ${req.body.username} 登录失败 ** 错误信息 : ${JSON.stringify(err)}`);
            return apiResponse.errorResponse(res, err);
        }
    }
]

/**
 * User login 码云单点登录.
 * @param {string}  code 码云授权码
 * @returns {Object}
 */

exports.giteeLogin = [
    // 参数验证
    [query("code").notEmpty().withMessage('码云授权码不能为空.')],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                // 处理验证错误并返回适当的响应
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            } else {
                // 码云 OAuth 应用的客户端ID、客户端秘钥和回调URL
                const CLIENT_ID = 'b1297cb3d4b6ebffeb0c90080b0211e2fe15d81e8e4cd31f95f2316a4de2ffe5';
                const CLIENT_SECRET = 'd0918f6ea72c87055e2b9b841e08f3b57e0638409d60a99ac500da623a89b889';
                const REDIRECT_URI = 'http://localhost:3000/v1/sys/auth/giteeLogin';

                // 从请求中获取授权码
                const code = req.query.code;

                // 使用授权码获取访问令牌
                const tokenResponse = await axios.post('https://gitee.com/oauth/token', querystring.stringify({
                    code,
                    client_id: CLIENT_ID,
                    client_secret: CLIENT_SECRET,
                    redirect_uri: REDIRECT_URI,
                    grant_type: 'authorization_code',
                }));

                const accessToken = tokenResponse.data.access_token;

                // 使用访问令牌访问码云API获取用户信息
                const userResponse = await axios.get('https://gitee.com/api/v5/user', {
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                    },
                });

                const user = userResponse.data;

                // 检查用户是否已存在
                const userWithData = await userModel.findOne({username: user.name});

                if (!userWithData) {
                    // 如果用户不存在，则注册新用户
                    const clientIP = getPublicIP(req);
                    const u = new UAParser(req.headers['user-agent']);
                    const address = await parseIP(clientIP);
                    const equipment = u.getBrowser().name ? `${u.getBrowser().name}.v${u.getBrowser().major}` : '未知';

                    const enPassword = await encryption(user.name); // 密码加密，默认用户名和密码相同
                    const avatar = await getEmailAvatar(user.name);

                    // 创建新用户
                    const newUser = {
                        type: 'admin', // 默认管理端用户
                        avatar: user.avatar_url || avatar,
                        nickname: user.name,
                        username: user.name,
                        password: enPassword,
                        userIp: clientIP,
                        website: user.blog || '',
                        email: user.email || null,
                        address,
                        remark: 'Gitee',
                        platform: equipment,
                        roleId: '64a7aa20a971facd04696242',
                    };

                    // 添加调试日志
                    console.log('===========================================');
                    console.log('提交的注册数据:', {
                        username: newUser.username,
                        nickname: newUser.nickname,
                        email: newUser.email,
                        type: newUser.type
                    });
                    console.log('创建用户对象:', {
                        type: newUser.type,
                        username: newUser.username,
                        nickname: newUser.nickname,
                        email: newUser.email,
                        roleId: newUser.roleId
                    });
                    console.log('===========================================');
                    
                    const addInfo = await userModel.create(newUser);

                    if (addInfo) {
                        // 构建响应给前端的数据
                        const userData = {
                            _id: addInfo._id,
                            username: addInfo.username,
                            nickname: addInfo.nickname,
                            roleId: addInfo.roleId,
                            status: addInfo.status,
                        };
                        userData.token = 'Bearer ' + jwt.sign(
                            userData,
                            JWT_SECRET || 'your-jwt-secret-key',
                            {
                                expiresIn: 3600 * 24 * 3, // token 3天有效期
                            }
                        );

                        // 重定向到前端登录页面并传递用户数据
                        return res.redirect('http://localhost:3000/#/login?userData=' + JSON.stringify(userData));
                    }
                } else {
                    if (!userWithData.status) return apiResponse.unauthorizedResponse(res, "当前账户已被禁用,请联系管理员.");

                    // 构建响应给前端的数据
                    const userData = {
                        _id: userWithData._id,
                        username: userWithData.username,
                        nickname: userWithData.nickname,
                        roleId: userWithData.roleId,
                        status: userWithData.status,
                    };
                    userData.token = 'Bearer ' + jwt.sign(
                        userData,
                        JWT_SECRET || 'your-jwt-secret-key',
                        {
                            expiresIn: 3600 * 24 * 3, // token 3天有效期
                        }
                    );
                    // 重定向到前端登录页面并传递用户数据
                    return res.redirect('http://localhost:3000/#/login?userData=' + JSON.stringify(userData));
                }
            }
        } catch (err) {
            logger.error(`*** 码云授权登录失败请重新登录 ** 错误信息 : ${JSON.stringify(err)}`);
            return apiResponse.errorResponse(res, err);
        }
    }
];

/**
 * User login github单点登录.
 * @param {string}  code github授权码
 * @returns {Object}
 */

exports.githubLogin = [
    // 参数验证
    [query("code").notEmpty().withMessage('github授权码不能为空.')],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                // 处理验证错误并返回适当的响应
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            } else {
                // github OAuth 应用的客户端ID、客户端秘钥和回调URL   https://github.com/settings/applications/2361469
                const CLIENT_ID = 'ed16b155150aab848cff';
                const CLIENT_SECRET = '48755367f3481521114ca9a78359f9017cd9b043';
                const REDIRECT_URI = 'http://localhost:3000/v1/sys/auth/githubLogin';

                // 从请求中获取授权码
                const code = req.query.code;

                // 使用授权码获取访问令牌
                const tokenResponse = await axios.post('https://github.com/login/oauth/access_token', {
                        code,
                        client_id: CLIENT_ID,
                        client_secret: CLIENT_SECRET,
                        redirect_uri: REDIRECT_URI,
                    },
                    {
                        headers: {
                            'Accept': 'application/json',
                        },
                    });

                const accessToken = tokenResponse.data.access_token;

                // 使用访问令牌访问码云API获取用户信息
                const userResponse = await axios.get('https://api.github.com/user', {
                    headers: {
                        'Accept': 'application/json',
                        Authorization: `Bearer ${accessToken}`,
                    },
                });
                //****************************************
                const user = userResponse.data;

                console.log("github账号信息", user)
                logger.info(JSON.stringify(user))

                // 检查用户是否已存在
                const userWithData = await userModel.findOne({username: user.name});

                if (!userWithData) {
                    // 如果用户不存在，则注册新用户
                    const clientIP = getPublicIP(req);
                    const u = new UAParser(req.headers['user-agent']);
                    const address = await parseIP(clientIP);
                    const equipment = u.getBrowser().name ? `${u.getBrowser().name}.v${u.getBrowser().major}` : '未知';

                    const enPassword = await encryption(user.name); // 密码加密，默认用户名和密码相同
                    const avatar = user.avatar_url || USER_CONFIG.defaultAvatar;

                    // 创建新用户
                    const newUser = {
                        type: 'admin', // 默认管理端用户
                        avatar: avatar,
                        nickname: user.name,
                        username: user.name,
                        password: enPassword,
                        userIp: clientIP,
                        website: user.blog || '',
                        email: user.email || null,
                        address,
                        remark: 'Github',
                        platform: equipment,
                        roleId: '64a7aa20a971facd04696242',
                    };

                    // 添加调试日志
                    console.log('===========================================');
                    console.log('提交的注册数据:', {
                        username: newUser.username,
                        nickname: newUser.nickname,
                        email: newUser.email,
                        type: newUser.type
                    });
                    console.log('创建用户对象:', {
                        type: newUser.type,
                        username: newUser.username,
                        nickname: newUser.nickname,
                        email: newUser.email,
                        roleId: newUser.roleId
                    });
                    console.log('===========================================');
                    
                    const addInfo = await userModel.create(newUser);

                    if (addInfo) {
                        // 构建响应给前端的数据
                        const userData = {
                            _id: addInfo._id,
                            username: addInfo.username,
                            nickname: addInfo.nickname,
                            roleId: addInfo.roleId,
                            status: addInfo.status,
                        };
                        userData.token = 'Bearer ' + jwt.sign(
                            userData,
                            JWT_SECRET || 'your-jwt-secret-key',
                            {
                                expiresIn: 3600 * 24 * 3, // token 3天有效期
                            }
                        );

                        // 重定向到前端登录页面并传递用户数据
                        return res.redirect('http://localhost:3000/#/login?userData=' + JSON.stringify(userData));
                    }
                } else {
                    if (!userWithData.status) return apiResponse.unauthorizedResponse(res, "当前账户已被禁用,请联系管理员.");

                    // 构建响应给前端的数据
                    const userData = {
                        _id: userWithData._id,
                        username: userWithData.username,
                        nickname: userWithData.nickname,
                        roleId: userWithData.roleId,
                        status: userWithData.status,
                    };
                    userData.token = 'Bearer ' + jwt.sign(
                        userData,
                        JWT_SECRET || 'your-jwt-secret-key',
                        {
                            expiresIn: 3600 * 24 * 3, // token 3天有效期
                        }
                    );

                    // 重定向到前端登录页面并传递用户数据
                    return res.redirect('http://localhost:3000/#/login?userData=' + JSON.stringify(userData));
                }
            }
        } catch (err) {
            logger.error(`*** github授权登录失败请重新登录 ** 错误信息 : ${JSON.stringify(err)}`);
            return apiResponse.errorResponse(res, err);
        }
    }
];

/**
 * 注册
 * @route POST /v1/sys/auth/register
 * @group 权限验证 - 登录注册相关
 * @param {string} email 邮箱
 * @param {string} password 密码
 * @param {string} username 昵称
 * @returns {object} 200 - {"status": 1,"message": "...","data": {...},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */
exports.register = [
    // actionRecords({module: '注册'}),
    //必填参数验证
    [
        body("nickname").notEmpty().withMessage('昵称不能为空.'),
        body("username").notEmpty().withMessage('用户名不能为空.').custom((value, {req}) => {
            return userModel.findOne({username: value}).then(user => {
                if (user) {
                    return Promise.reject(`用户名:${user.username}已经注册,请更换其他.`);
                }
            });
        }),
        body("password").notEmpty().withMessage('密码不能为空.').isLength({min: 6}).trim().withMessage('密码不能小于6位.'),
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                // console.error('****validationError*****: '+errors.array()[0].msg)
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            } else {
                const clientIP = getPublicIP(req);
                //识别常见的浏览器、操作系统和设备等信息
                const u = new UAParser(req.headers['user-agent']);
                const address = await parseIP(clientIP);
                const equipment = u.getBrowser().name ? `${u.getBrowser().name}.v${u.getBrowser().major}` : '未知'

                // 密码加密
                let enPassword = await encryption(req.body.password)
                // 使用默认头像或通过邮箱获取头像
                let avatar = req.body.avatar || USER_CONFIG.defaultAvatar
                if (!avatar && req.body.email) {
                    avatar = await getEmailAvatar(req.body.email)
                }
                //Save users.
                let newUser = {
                    type: req.body.type || 'user',//使用请求中的type或默认为普通用户
                    avatar: avatar,
                    nickname: req.body.nickname,
                    username: req.body.username,
                    password: enPassword,
                    userIp: clientIP,
                    email: req.body.email,
                    address,
                    platform: equipment,
                    roleId: 'f222bed7fcc64d42b6ad885caf4232fc',
                };
                
                // 添加调试日志
                console.log('===========================================');
                console.log('提交的注册数据:', {
                    username: req.body.username,
                    nickname: req.body.nickname,
                    email: req.body.email,
                    type: req.body.type
                });
                console.log('创建用户对象:', {
                    type: newUser.type,
                    username: newUser.username,
                    nickname: newUser.nickname,
                    email: newUser.email,
                    roleId: newUser.roleId
                });
                console.log('===========================================');
                
                const addInfo = await userModel.create(newUser)
                if (addInfo) {
                    //发送邮件
                    addInfo.email && await sendMail(req.body.email,
                        '✨✨注册成功通知',
                        `
                        <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
        }
        .logo {
            width: 100px;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            margin-top: 20px;
        }
        .content-text {
          font-weight: bold;
          color: #2f1e2e;
        }
        .bold {
            font-weight: bold;
        }
        .indent {
            text-indent: 2em;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="http://localhost:3000/assets/logo.png" alt="Logo" class="logo">
        </div>
        <div class="content">
            <p class="indent content-text">恭喜您已注册成功,感谢您的使用暨南大学基础医学与公共卫生学院教师绩效评定与管理平台！</p>
            <p class="bold">✨您的账号： ${req.body.username || '-'}</p>
            <p class="bold">✨您的密码： ${req.body.password || '-'}</p>
            <p class="bold">✨前台： http://localhost:3000</p>
            <p class="bold">✨管理端： http://localhost:3000/admin</p>
             <p class="bold">🎉🎉🎉 <a href="http://localhost:3000/#/contact">留言板</a></p>
            <p class="indent">祝您工作顺利，心想事成!</p>
        </div>
    </div>
</body>
</html>
                        `)
                    logger.info(`+++ 新用户: ${req.body.username} 注册成功`)
                    return apiResponse.successResponse(res, "注册成功");
                }
            }
        } catch (err) {
            console.log(err)
            return apiResponse.errorResponse(res, err);
        }
    }
]


/**
 * User Verify Confirm code 用户账号邮件验证码确认
 * @param {string}  email  邮箱
 * @param {string}  opt    验证码
 * @returns {Object}
 */
exports.verifyConfirm = [
    //必填参数验证
    [
        query("email").notEmpty().withMessage('邮箱不能为空.').isEmail().normalizeEmail().withMessage("邮箱格式不正确."),
        query("code").notEmpty().withMessage('验证码不能为空.').withMessage("验证码不能为空."),
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                // console.error('****validationError*****: '+errors.array()[0].msg)
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            } else {
                let {code} = req.session
                if (!code) return apiResponse.unauthorizedResponse(res, "验证码已失效,请重新获取.");
                //
                let query = {email: req.query.email};
                const userInfo = await userModel.findOne(query)
                if (!userInfo) return apiResponse.unauthorizedResponse(res, "邮箱号码不存在.");

                // if (userInfo.confirmOTP === req.query.code) {
                if (code === Number(req.query.code)) {
                    userModel.findOneAndUpdate(query, {
                        isConfirmed: 1,
                    }).catch(err => {
                        return apiResponse.errorResponse(res, err);
                    });
                    return apiResponse.successResponse(res, "账户验证成功！可进行登录.");
                } else {
                    return apiResponse.unauthorizedResponse(res, "验证码错误");
                }
            }
        } catch (err) {
            return apiResponse.errorResponse(res, err);
        }
    }
]


/**
 * Resend Confirm code. 重发验证码
 * @param {string}  email  邮箱
 * @returns {Object}
 */
exports.resendConfirmCode = [
    //必填参数验证
    query("email").notEmpty().withMessage('邮箱不能为空.').isEmail().normalizeEmail().withMessage("邮箱格式不正确."),
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                // console.error('****validationError*****: '+errors.array()[0].msg)
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            } else {
                //
                let query = {email: req.query.email};
                const userInfo = await userModel.findOne(query)
                if (!userInfo) return apiResponse.unauthorizedResponse(res, "邮箱号码不存在.");
                if (userInfo.isConfirmed) return apiResponse.unauthorizedResponse(res, "账户已经验证.");

                // 生成新验证码
                let newCode = randomNumber(4);

                // 更新用户验证状态 验证码
                await userModel.findOneAndUpdate(query, {isConfirmed: 0}).catch(err => {
                    return apiResponse.errorResponse(res, err);
                })
                // 发送验证码
                await sendMail(req.query.email, '注册成功', `
        <img src="http://localhost:3000/assets/logo.png" alt=""  style="height:auto;display:block;" />
        <p >🎉🎉🎉 <a href="http://localhost:3000/#/">Welcome to Admin System</a></p>
        <p style="font-weight: bold">✨您的验证码：${newCode}  有效期5分钟</p>
        <p style="text-indent: 2em;">祝您工作顺利，心想事成</p>`)
                //session存储验证码
                req.session.code = newCode
                return apiResponse.successResponse(res, "验证码发送成功！请在5分钟内进行验证.");

            }
        } catch (err) {
            console.log(err)
            return apiResponse.errorResponse(res, JSON.stringify(err));
        }
    }
]

/**
 * 退出登录
 * @route POST /v1/sys/auth/logout
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "退出成功","data": null,"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.logout = async (req, res) => {
  try {
    // 清除用户的token
    res.clearCookie('token');
    return apiResponse.successResponse(res, '登出成功');
  } catch (error) {
    logger.error('登出失败:', error);
    return apiResponse.errorResponse(res, '登出失败');
  }
};

/**
 * 获取当前用户信息
 * @route GET /v1/sys/auth/currentUser
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "获取成功","data": {用户信息},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await userModel.findById(req.user.id).select('-password');
    if (!user) {
      return apiResponse.errorResponse(res, '用户不存在');
    }
    return apiResponse.successResponse(res, '获取成功', user);
  } catch (error) {
    logger.error('获取用户信息失败:', error);
    return apiResponse.errorResponse(res, '获取用户信息失败');
  }
};

/**
 * 刷新token
 * @route POST /v1/sys/auth/refreshToken
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "刷新成功","data": {token: "新token"},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.refreshToken = async (req, res) => {
  try {
    const refreshToken = req.body.refreshToken;
    if (!refreshToken) {
      return apiResponse.errorResponse(res, '刷新令牌不能为空');
    }

    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    const user = await userModel.findById(decoded.id);
    if (!user) {
      return apiResponse.errorResponse(res, '用户不存在');
    }

    const token = jwt.sign(
      { id: user._id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );

    const newRefreshToken = jwt.sign(
      { id: user._id },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: '7d' }
    );

    return apiResponse.successResponse(res, '刷新成功', {
      token,
      refreshToken: newRefreshToken
    });
  } catch (error) {
    logger.error('刷新令牌失败:', error);
    return apiResponse.errorResponse(res, '刷新令牌失败');
  }
};

/**
 * 修改密码
 * @route POST /v1/sys/auth/changePassword
 * @group 权限验证 - 登录注册相关
 * @param {string} oldPassword.body.required - 旧密码
 * @param {string} newPassword.body.required - 新密码
 * @returns {object} 200 - {"status": 1,"message": "修改成功","data": null,"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.changePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const user = await userModel.findById(req.user.id);
    
    if (!user) {
      return apiResponse.errorResponse(res, '用户不存在');
    }

    if (aes.en(oldPassword) !== user.password) {
      return apiResponse.errorResponse(res, '原密码错误');
    }

    user.password = aes.en(newPassword);
    await user.save();

    return apiResponse.successResponse(res, '密码修改成功');
  } catch (error) {
    logger.error('修改密码失败:', error);
    return apiResponse.errorResponse(res, '修改密码失败');
  }
};

/**
 * 重置密码
 * @route POST /v1/sys/auth/resetPassword
 * @group 权限验证 - 登录注册相关
 * @param {string} email.body.required - 邮箱
 * @returns {object} 200 - {"status": 1,"message": "重置成功","data": null,"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */
exports.resetPassword = async (req, res) => {
  try {
    const { email, code, newPassword } = req.body;
    
    if (!req.session.code || req.session.code !== code) {
      return apiResponse.errorResponse(res, '验证码错误或已过期');
    }

    const user = await userModel.findOne({ email });
    if (!user) {
      return apiResponse.errorResponse(res, '用户不存在');
    }

    user.password = aes.en(newPassword);
    await user.save();

    return apiResponse.successResponse(res, '密码重置成功');
  } catch (error) {
    logger.error('重置密码失败:', error);
    return apiResponse.errorResponse(res, '重置密码失败');
  }
};

/**
 * 获取用户权限
 * @route GET /v1/sys/auth/permissions
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "获取成功","data": {权限列表},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.getUserPermissions = async (req, res) => {
  try {
    const roleModel = require('../../../models/v1/mapping/roleModel');
    // 从请求对象中获取用户ID
    const userId = req.user?.id;
    if (!userId) {
      return apiResponse.unauthorizedResponse(res, '未登录或授权已过期');
    }

    // 查找用户
    const user = await userModel.findOne({ where: { id: userId } });
    if (!user) {
      return apiResponse.errorResponse(res, '用户不存在');
    }

    // 获取角色ID
    const roleId = user.roleId;
    if (!roleId) {
      return apiResponse.errorResponse(res, '用户没有分配角色');
    }

    // 查找角色
    const role = await roleModel.findOne({ where: { id: roleId } });
    if (!role) {
      return apiResponse.errorResponse(res, '角色不存在');
    }

    // 返回角色的权限列表
    console.log('===========================================');
    console.log('用户ID:', userId);
    console.log('角色ID:', roleId);
    console.log('角色权限:', role.permissions || []);
    console.log('===========================================');

    return apiResponse.successResponseWithData(res, '获取成功', role.permissions || []);
  } catch (error) {
    logger.error('获取用户权限失败:', error);
    return apiResponse.errorResponse(res, '获取用户权限失败: ' + error.message);
  }
};

/**
 * 获取用户菜单
 * @route GET /v1/sys/auth/menus
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "获取成功","data": {菜单列表},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.getUserMenus = async (req, res) => {
  try {
    const roleModel = require('../../../models/v1/mapping/roleModel');
    // 从请求对象中获取用户ID
    const userId = req.user?.id;
    if (!userId) {
      return apiResponse.unauthorizedResponse(res, '未登录或授权已过期');
    }

    // 查找用户
    const user = await userModel.findOne({ where: { id: userId } });
    if (!user) {
      return apiResponse.errorResponse(res, '用户不存在');
    }

    // 获取角色ID
    const roleId = user.roleId;
    if (!roleId) {
      return apiResponse.errorResponse(res, '用户没有分配角色');
    }

    // 查找角色
    const role = await roleModel.findOne({ where: { id: roleId } });
    if (!role) {
      return apiResponse.errorResponse(res, '角色不存在');
    }

    // 返回角色的菜单列表
    console.log('===========================================');
    console.log('用户ID:', userId);
    console.log('角色ID:', roleId);
    console.log('角色菜单:', role.menus || []);
    console.log('===========================================');

    return apiResponse.successResponseWithData(res, '获取成功', role.menus || []);
  } catch (error) {
    logger.error('获取用户菜单失败:', error);
    return apiResponse.errorResponse(res, '获取用户菜单失败: ' + error.message);
  }
};

/**
 * 获取用户信息和权限
 * @route GET /v1/sys/auth/userInfo
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"code": 200,"message": "获取成功","data": {用户信息和权限}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getUserInfoAndPermissions = async (req, res) => {
  try {
    console.log('===========================================');
    console.log('获取用户信息和权限 - 请求路径:', req.path);
    console.log('Authorization Header:', req.headers.authorization ? `${req.headers.authorization.substring(0, 20)}...` : '无');
    
    // 从请求对象中获取用户ID (通过authMiddleware设置)
    const userId = req.user?.id;
    if (!userId) {
      console.log('获取用户信息失败 - 未找到用户ID');
      console.log('===========================================');
      return apiResponse.unauthorizedResponse(res, '未登录或授权已过期');
    }
    
    console.log('用户ID:', userId);
    
    // 查找用户信息(不包括密码)
    const user = await userModel.findOne({ 
      where: { id: userId },
      attributes: { 
        exclude: ['password'] 
      }
    });
    
    if (!user) {
      console.log('获取用户信息失败 - 未找到用户');
      console.log('===========================================');
      return apiResponse.errorResponse(res, '用户不存在');
    }
    
    console.log('用户信息:', {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      roleId: user.roleId
    });
    
    // 查找角色信息
    const roleModel = require('../../../models/v1/mapping/roleModel');
    const permissionsModel = require('../../../models/v1/mapping/permissionsModel');
    
    const role = await roleModel.findOne({ 
      where: { id: user.roleId },
      include: [{
        model: permissionsModel,
        attributes: ['id', 'key', 'name', 'status'],
        through: { attributes: [] }
      }]
    });
    
    if (!role) {
      console.log('获取用户角色失败 - 未找到角色');
      console.log('===========================================');
      return apiResponse.errorResponse(res, '角色不存在');
    }
    
    console.log('角色信息:', {
      id: role.id,
      name: role.name,
      status: role.status,
      permissionsCount: role.permissions ? role.permissions.length : 0
    });
    
    // 构建返回数据
    const userData = user.toJSON();
    
    // 添加角色和权限信息
    userData.role = {
      id: role.id,
      name: role.name,
      status: role.status,
      roleAuth: role.roleAuth
    };
    
    // 添加权限列表
    userData.permissions = role.permissions ? role.permissions.map(p => p.key) : [];
    
    // 过滤活跃的权限
    userData.activePermissions = role.permissions ? 
      role.permissions.filter(p => p.status).map(p => p.key) : [];
    
    console.log('返回数据包含权限数量:', userData.permissions.length);
    console.log('===========================================');
    
    return apiResponse.successResponseWithData(res, "获取成功", userData);
  } catch (error) {
    console.error('获取用户信息和权限失败:', error);
    logger.error(`获取用户信息和权限失败，错误信息: ${error.message}`);
    return apiResponse.errorResponse(res, '获取用户信息和权限失败: ' + error.message);
  }
};

/**
 * 获取用户角色
 * @route GET /v1/auth/role
 * @group 权限验证 - 登录注册相关
 * @param {string} userId.query - 用户ID（可选，不传则获取当前用户角色）
 * @returns {object} 200 - {"status": 1,"message": "获取成功","data": {角色信息},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.getUserRole = async (req, res) => {
  try {
    const roleModel = require('../../../models/v1/mapping/roleModel');

    // 优先使用查询参数中的userId，如果没有则从token中获取
    let userId = req.query.userId || req.user?.id;

    // 构建请求URL
    const requestUrl = `/auth/role?userId=${userId}`;
    console.log('请求角色信息URL:', requestUrl);
    console.log('请求参数: userId =', userId);
    console.log('请求头: Authorization =', req.headers.authorization ? `${req.headers.authorization.substring(0, 15)}...` : '无token');

    if (!userId) {
      return apiResponse.unauthorizedResponse(res, '未提供用户ID且未登录或授权已过期');
    }

    // 查找用户
    const user = await userModel.findOne({ where: { id: userId } });
    if (!user) {
      return apiResponse.errorResponse(res, '用户不存在');
    }

    // 获取角色ID
    const roleId = user.roleId;
    if (!roleId) {
      return apiResponse.errorResponse(res, '用户没有分配角色');
    }

    // 查找角色
    const role = await roleModel.findOne({ where: { id: roleId } });
    if (!role) {
      return apiResponse.errorResponse(res, '角色不存在');
    }

    // 构建响应数据
    const responseData = {
      id: role.id,
      roleName: role.roleName,
      roleAuth: role.roleAuth,
      description: role.description
    };

    return apiResponse.successResponseWithData(res, '获取成功', responseData);
  } catch (error) {
    logger.error('获取用户角色失败:', error);
    return apiResponse.errorResponse(res, '获取用户角色失败: ' + error.message);
  }
};





