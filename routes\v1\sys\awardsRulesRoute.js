const express = require('express');
const router = express.Router();
const awardsRulesController = require('../../../controllers/v1/rules/awardsRulesController');

/**
 * 获取奖项规则列表
 * @route GET /v1/sys/awards-rules/list
 * @group 奖项规则管理 - 奖项规则相关接口
 * @param {string} award_name.query - 奖项名称（模糊搜索）
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 0, page: 1, pageSize: 10, list: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list',  awardsRulesController.getAwardsRules);

/**
 * 获取奖项规则详情
 * @route GET /v1/sys/awards-rules/detail
 * @group 奖项规则管理 - 奖项规则相关接口
 * @param {string} id.query - 规则ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {规则详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail',  awardsRulesController.getAwardsRuleDetail);

/**
 * 创建奖项规则
 * @route POST /v1/sys/awards-rules/create
 * @group 奖项规则管理 - 奖项规则相关接口
 * @param {string} award_name.body.required - 奖项名称
 * @param {number} score.body.required - 分数
 * @param {string} description.body - 描述
 * @param {string} additional_info.body - 附加信息
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {创建的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create',  awardsRulesController.createAwardsRule);

/**
 * 更新奖项规则
 * @route PUT /v1/sys/awards-rules/update
 * @group 奖项规则管理 - 奖项规则相关接口
 * @param {string} id.body.required - 规则ID
 * @param {string} award_name.body - 奖项名称
 * @param {number} score.body - 分数
 * @param {string} description.body - 描述
 * @param {string} additional_info.body - 附加信息
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {更新后的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/update',  awardsRulesController.updateAwardsRule);

/**
 * 删除奖项规则
 * @route DELETE /v1/sys/awards-rules/delete
 * @group 奖项规则管理 - 奖项规则相关接口
 * @param {string} id.body.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete',  awardsRulesController.deleteAwardsRule);

/**
 * 批量删除奖项规则
 * @route DELETE /v1/sys/awards-rules/batch-delete
 * @group 奖项规则管理 - 奖项规则相关接口
 * @param {Array} ids.body.required - 规则ID列表
 * @returns {object} 200 - {code: 200, message: "批量删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/batch-delete',  awardsRulesController.batchDeleteAwardsRules);

module.exports = router; 