import request from '../server'
import { getProjectParticipants, getProjectParticipantDetail, createProjectParticipant, updateProjectParticipant, deleteProjectParticipant, batchAddProjectParticipants } from './api.researchProjectParticipants'
import axios from 'axios'
import config from '../../config'
import dbUtils from '../../libs/util.strotage'

// Export the participants API for convenience
export { 
  getProjectParticipants, 
  getProjectParticipantDetail, 
  createProjectParticipant, 
  updateProjectParticipant, 
  deleteProjectParticipant, 
  batchAddProjectParticipants 
}

// 获取科研项目详情
export function getResearchProjectDetail(id) {
  return request.post('/researchProjects/research-project/detail', { id: id })
}

// 添加科研项目
export function addResearchProject(data) {
  // 使用原始axios实例直接发送FormData数据
  return request.post('/researchProjects/research-project/create', data)
}

// 更新科研项目
export function updateResearchProject(data) {
  return request.post('/researchProjects/research-project/update/' + data.get('id'), data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除科研项目
export function deleteResearchProject(params) {
  return request.post('/researchProjects/research-project/delete', params)
}

// 审核科研项目
export function reviewProject(data) {
  return request.post('/researchProjects/research-project/review', data)
}

// 导入科研项目数据
export function importResearchProjects(file) {
  if (!file) {
    return Promise.reject(new Error('文件不能为空'))
  }
  const formData = new FormData()
  formData.append('file', file)
  return request.post('/researchProjects/research-projects/import', formData, null, 'multipart/form-data')
}

// 导出科研项目数据
export function exportResearchProjects(data) {
  return request.post('/researchProjects/research-projects/export', data || {}, null, 'blob')
}

/**
 * 获取科研项目列表（统一接口）
 * @param {Object} params - 查询参数
 * @param {String} params.name - 项目名称（模糊搜索）
 * @param {String} params.level - 项目级别
 * @param {String} params.type - 项目类型
 * @param {String} params.leader - 项目负责人（模糊搜索）
 * @param {String} params.startDate - 开始日期
 * @param {String} params.endDate - 结束日期
 * @param {String} params.userId - 用户ID（可选，如果提供则只返回该用户参与的项目）
 * @param {Number} params.page - 页码，默认1
 * @param {Number} params.pageSize - 每页条数，默认10
 * @param {String} params.range - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)，默认'all'
 * @param {String} params.reviewStatus - 审核状态筛选: 'all'(全部), 'reviewed'(已审核), 'unreviewed'(未审核)，默认'all'
 * @returns {Promise} - 请求结果
 */
export function getProjects(params) {
  return request.post('/researchProjects/projects', params || {})
}

/**
 * 获取项目级别分布数据
 * @param {Object} data - 请求参数
 * @param {String} data.range - 数据范围: 'in', 'out', 'all'
 * @param {String} data.userId - 用户ID，可选，用于过滤特定用户的数据
 * @returns {Promise}
 */
export function getLevelDistribution(data) {
  return request.post('/researchProjects/research-statistics/level-distribution', data)
}

/**
 * 获取项目类型分布数据
 * @param {Object} data - 请求参数
 * @param {String} data.range - 数据范围: 'in', 'out', 'all'
 * @param {String} data.userId - 用户ID，可选，用于过滤特定用户的数据
 * @returns {Promise}
 */
export function getTypeDistribution(data) {
  return request.post('/researchProjects/research-statistics/type-distribution', data)
}

/**
 * 获取项目时间分布数据
 * @param {Object} data - 请求参数
 * @param {String} data.range - 数据范围: 'in', 'out', 'all'
 * @param {String} data.userId - 用户ID，可选，用于过滤特定用户的数据
 * @returns {Promise}
 */
export function getTimeDistribution(data) {
  return request.post('/researchProjects/research-statistics/time-distribution', data)
}

/**
 * 获取项目得分分布数据
 * @param {Object} data - 请求参数
 * @param {String} data.range - 数据范围: 'in', 'out', 'all'
 * @param {String} data.userId - 用户ID，可选，用于过滤特定用户的数据
 * @returns {Promise}
 */
export function getScoreDistribution(data) {
  return request.post('/researchProjects/research-statistics/score-distribution', data)
}

/**
 * 获取项目负责人得分排名数据
 * @param {Object} data - 请求参数
 * @param {String} data.range - 数据范围: 'in', 'out', 'all'
 * @param {Number} data.limit - 返回数量限制
 * @param {String} data.userId - 用户ID，可选，用于过滤特定用户的数据
 * @returns {Promise}
 */
export function getLeaderRanking(data) {
  return request.post('/researchProjects/research-statistics/leader-ranking', data)
}

/**
 * 获取用户科研项目总分
 * @param {Object} params - 请求参数
 * @param {string} params.userId - 用户ID
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {Object} [params.timeRange] - 可选，自定义时间范围
 * @param {string} [params.timeRange.startDate] - 开始日期，格式 YYYY-MM-DD
 * @param {string} [params.timeRange.endDate] - 结束日期，格式 YYYY-MM-DD
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @returns {Promise} - 请求Promise对象
 */
export function getUserTotalScore(params) {
  return request.post('/researchProjects/research-statistics/user-total-score', params)
}

/**
 * 获取所有用户科研项目总分统计
 * @param {Object} params - 请求参数
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {Object} [params.timeRange] - 可选，自定义时间范围
 * @param {string} [params.timeRange.startDate] - 开始日期，格式 YYYY-MM-DD
 * @param {string} [params.timeRange.endDate] - 结束日期，格式 YYYY-MM-DD
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @param {string} [params.sortField] - 排序字段
 * @param {string} [params.sortOrder] - 排序方向，'asc' 或 'desc'
 * @param {string} [params.projectId] - 可选，项目编号，支持模糊搜索
 * @param {string} [params.nickname] - 可选，用户昵称，支持模糊搜索
 * @returns {Promise} - 请求Promise对象
 */
export function getAllUsersTotalScore(params) {
  return request.post('/researchProjects/research-statistics/teacher-ranking', params)
}

/**
 * 获取用户科研项目详情
 * @param {Object} params - 请求参数
 * @param {string} params.userId - 用户ID，必填
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {string} [params.reviewStatus='all'] - 可选，审核状态筛选，可选值：'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 'all'(全部)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @returns {Promise} - 请求Promise对象
 */
export function getUserProjectDetails(params) {
  return request.post('/researchProjects/research-statistics/user-project-details', params)
}

/**
 * 获取科研项目总分统计
 * @param {Object} params - 请求参数
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {string} [params.reviewStatus='all'] - 可选，审核状态筛选，可选值 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 'all'(全部)
 * @returns {Promise} - 请求Promise对象
 */
export function getProjectsTotalScore(params) {
  return request.post('/researchProjects/research-statistics/projects-total-score', params)
}

/**重新提交审核 */
export function reapplyReview(params) {
  return request.post('/researchProjects/research-project/reapply', params)
}

/**
 * 获取审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getReviewStatusOverview(params) {
  return request.post('/researchProjects/statistics/review-status-overview', params)
}
