const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const conferenceModel = require('./conferencesModel');
const userModel = require('./userModel');

// 定义会议参与者模型
const ConferenceParticipant = sequelize.define('conferences_participants', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    conferenceId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '会议ID'
    },
    participantId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '参与者ID'
    },
    allocationRatio: {
        type: DataTypes.DECIMAL(5, 4),
        allowNull: false,
        defaultValue: 0,
        comment: '分配比例'
    },
    isLeader: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 0,
        comment: '是否是负责人：0-不是，1-是'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'conferences_participants',
    timestamps: true,
    indexes: [
        {
            name: 'idx_conference_participant_conference',
            fields: ['conferenceId']
        },
        {
            name: 'idx_conference_participant_user',
            fields: ['participantId']
        },
        {
            name: 'idx_conference_participant_leader',
            fields: ['isLeader']
        }
    ]
});

// 建立与会议的关联关系
ConferenceParticipant.belongsTo(conferenceModel, {
    foreignKey: 'conferenceId',
    as: 'conference'
});

// 建立与用户的关联关系
ConferenceParticipant.belongsTo(userModel, {
    foreignKey: 'participantId',
    as: 'participant'
});

module.exports = ConferenceParticipant; 