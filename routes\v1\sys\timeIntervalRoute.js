const express = require('express');
const router = express.Router();
const timeIntervalController = require('../../../controllers/v1/sys/timeIntervalController');

/**
 * 获取时间区间列表
 * @route GET /v1/sys/time-interval/list
 * @group 时间区间管理 - 时间区间相关接口
 * @param {string} category.query - 类型（模糊搜索）
 * @param {string} nameC.query - 名称（模糊搜索）
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 0, page: 1, pageSize: 10, list: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list', timeIntervalController.getTimeIntervals);

/**
 * 获取时间区间详情
 * @route GET /v1/sys/time-interval/detail
 * @group 时间区间管理 - 时间区间相关接口
 * @param {string} id.query - 记录ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {记录详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail', timeIntervalController.getTimeIntervalDetail);

/**
 * 创建时间区间
 * @route POST /v1/sys/time-interval/create
 * @group 时间区间管理 - 时间区间相关接口
 * @param {string} category.body.required - 类型
 * @param {string} nameC.body.required - 名称
 * @param {date} startTime.body.required - 开始时间
 * @param {date} endTime.body.required - 结束时间
 * @param {string} description.body - 描述
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {创建的记录}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', timeIntervalController.createTimeInterval);

/**
 * 更新时间区间
 * @route PUT /v1/sys/time-interval/update
 * @group 时间区间管理 - 时间区间相关接口
 * @param {string} id.body.required - 记录ID
 * @param {string} category.body - 类型
 * @param {string} nameC.body - 名称
 * @param {date} startTime.body - 开始时间
 * @param {date} endTime.body - 结束时间
 * @param {string} description.body - 描述
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {更新后的记录}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/update', timeIntervalController.updateTimeInterval);

/**
 * 删除时间区间
 * @route DELETE /v1/sys/time-interval/delete
 * @group 时间区间管理 - 时间区间相关接口
 * @param {string} id.query - 记录ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete', timeIntervalController.deleteTimeInterval);

/**
 * 批量删除时间区间
 * @route DELETE /v1/sys/time-interval/batch-delete
 * @group 时间区间管理 - 时间区间相关接口
 * @param {Array} ids.body.required - 记录ID列表
 * @returns {object} 200 - {code: 200, message: "批量删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/batch-delete', timeIntervalController.batchDeleteTimeIntervals);

module.exports = router; 