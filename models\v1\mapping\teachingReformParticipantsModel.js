const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const userModel = require('./userModel');

// 定义教学改革项目参与者模型
const TeachingReformParticipant = sequelize.define('teaching_reform_participants', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '记录ID'
    },
    projectId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '项目ID'
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '用户ID'
    },
    allocationRatio: {
        type: DataTypes.DECIMAL(4, 2),
        allowNull: false,
        comment: '分配比例（0.00-1.00）'
    },
    isLeader: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否主持人'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'teaching_reform_participants',
    timestamps: true,
    indexes: [
        {
            name: 'uk_participant',
            unique: true,
            fields: ['projectId', 'userId']
        }
    ]
});

// 建立与用户的关联关系
TeachingReformParticipant.belongsTo(userModel, {
    foreignKey: 'userId',
    as: 'user'
});

module.exports = TeachingReformParticipant; 