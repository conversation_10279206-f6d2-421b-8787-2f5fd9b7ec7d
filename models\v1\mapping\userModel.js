// models/userModel.js
const {DataTypes} = require('sequelize');
const sequelize = require('@config/dbConfig');
const {aes} = require('../../../utils/crypto');

/**
 * 用户模型
 * 表名使用下划线命名法: user
 * 字段名使用小驼峰命名法: roleId, createdAt 等
 */
const userModel = sequelize.define('user', // 明确指定表名为下划线形式
    {
        id: {
            type: DataTypes.CHAR(36),
            primaryKey: true,
            allowNull: false,
            defaultValue: DataTypes.UUIDV4,
            comment: '用户ID，使用UUID'
        },
        roleId: {
            type: DataTypes.CHAR(36),
            allowNull: false,
            comment: '角色ID，关联role表'
        },
        studentNumber: {
            type: DataTypes.STRING(36),
            allowNull: false,
            comment: '学号/工号'
        },
        avatar: {
            type: DataTypes.STRING(200),
            allowNull: true,
            comment: '头像地址'
        },
        username: {
            type: DataTypes.STRING(50),
            allowNull: false,
            unique: true,
            comment: '用户名'
        },
        password: {
            type: DataTypes.STRING(100),
            allowNull: false,
            set(value) {
                this.setDataValue('password', aes.en(value));
            },
            get() {
                const rawValue = this.getDataValue('password');
                return rawValue;
            },
            comment: '密码'
        },
        email: {
            type: DataTypes.STRING(50),
            allowNull: true,
            comment: '邮箱'
        },
        nickname: {
            type: DataTypes.STRING(50),
            allowNull: true,
            comment: '昵称'
        },
        status: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
            comment: '状态: 1-启用, 0-禁用'
        },
        remark: {
            type: DataTypes.STRING(200),
            allowNull: true,
            comment: '备注'
        },
        userLevelId: {
            type: DataTypes.CHAR(36),
            allowNull: true,
            comment: '用户级别ID，关联user_levels表'
        },
        departmentId: {
            type: DataTypes.CHAR(36),
            allowNull: true,
            comment: '部门ID，关联departments表'
        }
    },
    {
        // 明确指定表名
        tableName: 'user',
        // 不使用自动复数形式
        freezeTableName: true,
        // 添加表注释
        comment: '用户信息表'
    });

// 设置模型关联
const setupAssociations = () => {
    const departmentsModel = require('./departmentsModel');

    // 用户属于一个部门
    userModel.belongsTo(departmentsModel, {
        foreignKey: 'departmentId',
        as: 'department'
    });
};

// 延迟设置关联以避免循环依赖
setTimeout(setupAssociations, 0);

module.exports = userModel;
