const { Op } = require('sequelize');
const highLevelPapersRulesModel = require('../../../models/v1/mapping/highLevelPapersRulesModel');
const { v4: uuidv4 } = require('uuid');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

/**
 * 获取高水平论文规则列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getHighLevelPapersRules = async (req, res) => {
  try {
    
    // 获取查询参数
    const { page = 1, pageSize = 10, paperLevel } = req.query;
    
    // 确保 page 和 pageSize 是有效的数字，否则使用默认值
    const pageNum = page ? parseInt(page) : 1;
    const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

    // 如果 page 或 pageSize 是无效数字，返回默认值
    const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
    const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;
    

    // 构建查询条件
    const where = {};
    if (paperLevel) {
      where.paperLevel = { [Op.like]: `%${paperLevel}%` };
    }
    

    // 分页查询
    const offset = (validPage - 1) * validPageSize;
    try {
      const { count, rows } = await highLevelPapersRulesModel.findAndCountAll({
        where,
        offset,
        limit: validPageSize,
        order: [['createdAt', 'DESC']] // 默认按时间倒序
      });
      
      
      const totalPages = Math.ceil(count / validPageSize);
      
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          total: count,
          page: validPage,
          pageSize: validPageSize,
          totalPages,
          list: rows
        }
      });
    } catch (dbError) {
      console.error('❌ 数据库查询失败:', dbError);
      return res.status(500).json({
        code: 500,
        message: `数据库查询失败: ${dbError.message}`,
        data: null
      });
    }
  } catch (error) {
    console.error('❌ 获取高水平论文规则列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取高水平论文规则列表失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取高水平论文规则详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getHighLevelPapersRuleDetail = async (req, res) => {
  try {
    const { id } = req.query;
    console.log('🔍 获取高水平论文规则详情 - ID:', id);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    const rule = await highLevelPapersRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }
    
    console.log(`✅ 查询成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 获取高水平论文规则详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取高水平论文规则详情失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 创建高水平论文规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createHighLevelPapersRule = async (req, res) => {
  try {
    const { paperLevel, score, description, nonDepartmentAuthorCoefficient, coFirstAuthorRankCoefficient } = req.body;
    console.log('📝 创建高水平论文规则 - 请求数据:', { paperLevel, score });
    
    // 校验必填参数
    if (!paperLevel || score === undefined) {
      return res.status(400).json({
        code: 400,
        message: '论文级别和分数不能为空',
        data: null
      });
    }
    
    // 检查论文级别是否已存在
    const existingRule = await highLevelPapersRulesModel.findOne({
      where: { paperLevel }
    });

    if (existingRule) {
      return res.status(400).json({
        code: 400,
        message: '该论文级别已存在',
        data: null
      });
    }
    
    // 创建规则
    const rule = await highLevelPapersRulesModel.create({
      id: uuidv4(),
      paperLevel,
      score: Number(score),
      description: description || null,
      nonDepartmentAuthorCoefficient: nonDepartmentAuthorCoefficient || 0.9,
      coFirstAuthorRankCoefficient: coFirstAuthorRankCoefficient || null,
      createdBy: req.user.id
    });
    
    console.log(`✅ 创建成功: 规则ID ${rule.id}`);
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 创建高水平论文规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `创建高水平论文规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 更新高水平论文规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateHighLevelPapersRule = async (req, res) => {
  try {
    const { id, paperLevel, score, description, nonDepartmentAuthorCoefficient, coFirstAuthorRankCoefficient } = req.body;
    console.log('📝 更新高水平论文规则 - 请求数据:', { id, paperLevel, score });

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    // 查找规则
    const rule = await highLevelPapersRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }

    // 如果更新了论文级别，检查是否与其他记录重复
    if (paperLevel && paperLevel !== rule.paperLevel) {
      const existingRule = await highLevelPapersRulesModel.findOne({
        where: {
          id: { [Op.ne]: id },
          paperLevel
        }
      });

      if (existingRule) {
        return res.status(400).json({
          code: 400,
          message: '该论文级别已存在',
          data: null
        });
      }
    }

    // 更新规则
    await rule.update({
      paperLevel: paperLevel || rule.paperLevel,
      score: score !== undefined ? Number(score) : rule.score,
      description: description !== undefined ? description : rule.description,
      nonDepartmentAuthorCoefficient: nonDepartmentAuthorCoefficient !== undefined ? nonDepartmentAuthorCoefficient : rule.nonDepartmentAuthorCoefficient,
      coFirstAuthorRankCoefficient: coFirstAuthorRankCoefficient !== undefined ? coFirstAuthorRankCoefficient : rule.coFirstAuthorRankCoefficient
    });
    
    console.log(`✅ 更新成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 更新高水平论文规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `更新高水平论文规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 删除高水平论文规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteHighLevelPapersRule = async (req, res) => {
  try {
    const { id } = req.query;
    console.log('🗑️ 删除高水平论文规则 - ID:', id);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    const rule = await highLevelPapersRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }

    await rule.destroy();
    
    console.log(`✅ 删除成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('❌ 删除高水平论文规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `删除高水平论文规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 批量删除高水平论文规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteHighLevelPapersRules = async (req, res) => {
  try {
    const { ids } = req.body;
    console.log('🗑️ 批量删除高水平论文规则 - IDs:', ids);

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: 'ID列表不能为空',
        data: null
      });
    }

    await highLevelPapersRulesModel.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });
    
    console.log(`✅ 批量删除成功: 共${ids.length}条记录`);
    
    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: null
    });
  } catch (error) {
    console.error('❌ 批量删除高水平论文规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `批量删除高水平论文规则失败: ${error.message}`,
      data: null
    });
  }
}; 