const express = require('express');
const router = express.Router();
const studentProjectGuidanceProjectLevelController = require('../../../controllers/v1/studentProjectGuidanceProjects/studentProjectGuidanceProjectLevelsController');

/**
 * 获取立项级别列表
 * @route GET /v1/sys/student-project-guidance-levels/levels
 * @group 指导学生立项级别管理 - 指导学生立项级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels', studentProjectGuidanceProjectLevelController.getProjectLevels);

/**
 * 获取所有立项级别（不分页）
 * @route GET /v1/sys/student-project-guidance-levels/allLevels
 * @group 指导学生立项级别管理 - 指导学生立项级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/allLevels', studentProjectGuidanceProjectLevelController.getAllProjectLevels);

/**
 * 获取所有级别及其立项数量
 * @route GET /v1/sys/student-project-guidance-levels/levels-with-count
 * @group 指导学生立项级别管理 - 指导学生立项级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id, levelName, description, projectCount},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels-with-count', studentProjectGuidanceProjectLevelController.getLevelsWithCount);

/**
 * 获取立项级别详情
 * @route GET /v1/sys/student-project-guidance-levels/level/:id
 * @group 指导学生立项级别管理 - 指导学生立项级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/:id', studentProjectGuidanceProjectLevelController.getLevelDetail);

/**
 * 创建立项级别
 * @route POST /v1/sys/student-project-guidance-levels/level/create
 * @group 指导学生立项级别管理 - 指导学生立项级别相关接口
 * @param {string} levelName.body.required - 级别名称
 * @param {number} score.body.required - 级别分数
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/create', studentProjectGuidanceProjectLevelController.createLevel);

/**
 * 更新立项级别
 * @route POST /v1/sys/student-project-guidance-levels/level/update
 * @group 指导学生立项级别管理 - 指导学生立项级别相关接口
 * @param {string} id.body.required - 级别ID
 * @param {string} levelName.body - 级别名称
 * @param {number} score.body - 级别分数
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/update', async (req, res) => {
  const { id, ...updateData } = req.body;
  req.params = { id };
  req.body = updateData;
  await studentProjectGuidanceProjectLevelController.updateLevel(req, res);
});

/**
 * 获取立项级别分布数据
 * @route POST /v1/sys/student-project-guidance-levels/statistics/distribution
 * @group 指导学生立项级别统计 - 指导学生立项级别统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的立项
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "级别名称", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/distribution', studentProjectGuidanceProjectLevelController.getLevelDistribution);

/**
 * 删除立项级别
 * @route POST /v1/sys/student-project-guidance-levels/level/delete
 * @group 指导学生立项级别管理 - 指导学生立项级别相关接口
 * @param {string} id.body.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/delete', async (req, res) => {
  const { id } = req.body;
  req.params = { id };
  await studentProjectGuidanceProjectLevelController.deleteLevel(req, res);
});


module.exports = router; 