const express = require('express');
const router = express.Router();
const deductionController = require('../../../controllers/v1/rules/deductionController');
const deductionMiddleware = require('../../../middleware/deductionMiddleware');
const multer = require('multer');
const path = require('path');

// 配置 multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../../uploads'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// 应用扣分权限中间件
router.use(deductionMiddleware.attachTeacherInfo);

// 个人扣分相关路由 - 无需额外权限验证
router.get('/personal/list', deductionController.getPersonalDeductions);
router.get('/personal/stats', deductionController.getPersonalDeductionStats);

/**
 * 获取扣分列表
 * @route GET /v1/sys/deduction/list
 * @group 扣分管理 - 扣分相关接口
 * @param {string} teacherName.query - 教师姓名
 * @param {string} deductionType.query - 扣分类型
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @param {string} userId.query - 用户ID（筛选个人记录）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0, page: 1, pageSize: 10, totalPages: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list', deductionController.getDeductions);

// 以下路由需要权限验证
router.use(deductionMiddleware.checkDeductionPermission);

/**
 * 获取扣分详情
 * @route GET /v1/sys/deduction/:id
 * @group 扣分管理 - 扣分相关接口
 * @param {string} id.path.required - 扣分记录ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {扣分详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/:id', deductionController.getDeductionById);

/**
 * 创建扣分记录
 * @route POST /v1/sys/deduction
 * @group 扣分管理 - 扣分相关接口
 * @param {string} username.body.required - 用户名
 * @param {string} deductionType.body.required - 扣分类型
 * @param {string} deductionDate.body.required - 扣分时间
 * @param {number} deductionScore.body.required - 扣分分值
 * @param {string} deductionReason.body.required - 扣分原因
 * @param {string} handleResult.body - 处理结果
 * @param {string} remark.body - 备注
 * @param {string} userId.query - 用户ID
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "扣分记录ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/', deductionMiddleware.validateCreateDeduction, deductionController.createDeduction);

/**
 * 更新扣分记录
 * @route PUT /v1/sys/deduction/:id
 * @group 扣分管理 - 扣分相关接口
 * @param {string} id.path.required - 扣分记录ID
 * @param {string} username.body.required - 用户名
 * @param {string} deductionType.body.required - 扣分类型
 * @param {string} deductionDate.body.required - 扣分时间
 * @param {number} deductionScore.body.required - 扣分分值
 * @param {string} deductionReason.body.required - 扣分原因
 * @param {string} handleResult.body - 处理结果
 * @param {string} remark.body - 备注
 * @param {string} userId.query - 用户ID
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/:id', deductionMiddleware.checkRecordExists, deductionMiddleware.validateUpdateDeduction, deductionController.updateDeduction);

/**
 * 删除扣分记录
 * @route DELETE /v1/sys/deduction/:id
 * @group 扣分管理 - 扣分相关接口
 * @param {string} id.path.required - 扣分记录ID
 * @param {string} userId.query - 用户ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/:id', deductionMiddleware.checkRecordExists, deductionController.deleteDeduction);

/**
 * 导入扣分数据
 * @route POST /v1/sys/deduction/import
 * @group 扣分管理 - 扣分相关接口
 * @param {file} file.body.required - Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', upload.single('file'), deductionController.importDeductions);

/**
 * 导出扣分数据
 * @route GET /v1/sys/deduction/export
 * @group 扣分管理 - 扣分相关接口
 * @param {string} teacherName.query - 教师姓名
 * @param {string} deductionType.query - 扣分类型
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/export', deductionController.exportDeductions);

/**
 * 获取扣分类型分布
 * @route GET /v1/sys/deduction/stats/type-distribution
 * @group 扣分管理 - 扣分统计接口
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{type: "师德师风", count: 10, totalScore: "50.00"}]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/type-distribution', deductionController.getDeductionTypeDistribution);

/**
 * 获取扣分时间分布
 * @route GET /v1/sys/deduction/stats/time-distribution
 * @group 扣分管理 - 扣分统计接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{month: "2023-01", count: 5, totalScore: "25.00"},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/time-distribution', deductionController.getDeductionTimeDistribution);

/**
 * 获取教师扣分排名
 * @route GET /v1/sys/deduction/stats/teacher-ranking
 * @group 扣分管理 - 扣分统计接口
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @param {number} limit.query - 限制数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{teacherName: "张三", count: 5, totalScore: "25.00"},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/teacher-ranking', deductionController.getTeacherDeductionRanking);

/**
 * 获取扣分原因分析
 * @route GET /v1/sys/deduction/stats/reason-analysis
 * @group 扣分管理 - 扣分统计接口
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @param {number} limit.query - 限制数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{reason: "未按时毕业", count: 8, totalScore: "40.00"},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/reason-analysis', deductionController.getDeductionReasonAnalysis);

module.exports = router; 