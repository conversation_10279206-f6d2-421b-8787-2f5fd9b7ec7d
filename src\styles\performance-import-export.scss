// 性能管理页面导入导出样式
// 用于统一所有性能页面的导入导出功能样式

// 导入预览模态框样式
.import-preview-modal {
  .ant-modal-body {
    padding: 16px;
  }
  
  .import-summary {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    
    .summary-title {
      font-weight: 600;
      color: #389e0d;
      margin-bottom: 8px;
    }
    
    .summary-stats {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .stat-label {
          color: #666;
          font-size: 14px;
        }
        
        .stat-value {
          font-weight: 600;
          color: #262626;
          
          &.success {
            color: #52c41a;
          }
          
          &.warning {
            color: #faad14;
          }
          
          &.error {
            color: #ff4d4f;
          }
        }
      }
    }
  }
  
  .import-warnings {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    
    .warning-title {
      font-weight: 600;
      color: #d48806;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    
    .warning-list {
      color: #666;
      font-size: 14px;
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 4px;
        }
      }
    }
  }
}

// 导入结果表格样式
.import-result-table {
  .ant-table-tbody > tr.ant-table-row:hover > td {
    background-color: rgba(24, 144, 255, 0.1);
  }
  
  // 错误行样式
  .import-row-error {
    background-color: rgba(245, 34, 45, 0.05) !important;
    
    &:hover > td {
      background-color: rgba(245, 34, 45, 0.1) !important;
    }
  }
  
  // 成功行样式
  .import-row-success {
    background-color: rgba(82, 196, 26, 0.05) !important;
    
    &:hover > td {
      background-color: rgba(82, 196, 26, 0.1) !important;
    }
  }
  
  // 警告行样式
  .import-row-warning {
    background-color: rgba(250, 173, 20, 0.05) !important;
    
    &:hover > td {
      background-color: rgba(250, 173, 20, 0.1) !important;
    }
  }
}

// 导入进度样式
.import-progress {
  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .progress-text {
      font-size: 14px;
      color: #666;
    }
    
    .progress-percentage {
      font-weight: 600;
      color: #1890ff;
    }
  }
  
  .progress-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    padding: 8px 12px;
    background-color: #fafafa;
    border-radius: 4px;
    
    .stat-item {
      text-align: center;
      
      .stat-label {
        font-size: 12px;
        color: #999;
        margin-bottom: 2px;
      }
      
      .stat-value {
        font-size: 16px;
        font-weight: 600;
        
        &.total {
          color: #666;
        }
        
        &.current {
          color: #1890ff;
        }
        
        &.success {
          color: #52c41a;
        }
        
        &.failed {
          color: #ff4d4f;
        }
      }
    }
  }
}

// 导出配置样式
.export-config {
  .config-section {
    margin-bottom: 20px;
    
    .section-title {
      font-weight: 600;
      color: #262626;
      margin-bottom: 12px;
      padding-bottom: 6px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .config-options {
      .ant-checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .ant-checkbox-wrapper {
          padding: 6px 0;
        }
      }
      
      .ant-radio-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .ant-radio-wrapper {
          padding: 6px 0;
        }
      }
    }
  }
  
  .export-preview {
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 12px;
    margin-top: 16px;
    
    .preview-title {
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }
    
    .preview-info {
      font-size: 14px;
      color: #666;
      
      .info-item {
        margin-bottom: 4px;
        
        .info-label {
          font-weight: 500;
          margin-right: 8px;
        }
        
        .info-value {
          color: #1890ff;
        }
      }
    }
  }
}

// 文件上传拖拽样式
.upload-dragger {
  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
    transition: all 0.3s;
    
    &:hover {
      border-color: #1890ff;
    }
    
    &.ant-upload-drag-hover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
    
    .ant-upload-drag-container {
      padding: 20px;
      
      .upload-icon {
        font-size: 48px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }
      
      .upload-text {
        color: #666;
        font-size: 16px;
        margin-bottom: 8px;
      }
      
      .upload-hint {
        color: #999;
        font-size: 14px;
      }
    }
  }
}

// 批量操作样式
.batch-operations {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  margin-bottom: 16px;
  
  .batch-info {
    flex: 1;
    color: #0050b3;
    font-size: 14px;
    
    .selected-count {
      font-weight: 600;
    }
  }
  
  .batch-actions {
    display: flex;
    gap: 8px;
    
    .ant-btn {
      height: 32px;
      font-size: 14px;
    }
  }
}

// 模板下载样式
.template-download {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  
  .template-title {
    font-weight: 600;
    color: #389e0d;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .template-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 12px;
  }
  
  .template-actions {
    display: flex;
    gap: 8px;
    
    .ant-btn {
      height: 32px;
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .import-summary {
    .summary-stats {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  .import-progress {
    .progress-stats {
      flex-direction: column;
      gap: 8px;
      
      .stat-item {
        display: flex;
        justify-content: space-between;
        text-align: left;
      }
    }
  }
  
  .batch-operations {
    flex-direction: column;
    align-items: stretch;
    
    .batch-info {
      text-align: center;
      margin-bottom: 8px;
    }
    
    .batch-actions {
      justify-content: center;
    }
  }
  
  .template-download {
    .template-actions {
      flex-direction: column;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .upload-dragger {
    .ant-upload-drag {
      .ant-upload-drag-container {
        padding: 16px;
        
        .upload-icon {
          font-size: 36px;
          margin-bottom: 12px;
        }
        
        .upload-text {
          font-size: 14px;
        }
        
        .upload-hint {
          font-size: 12px;
        }
      }
    }
  }
  
  .export-config {
    .config-section {
      .config-options {
        .ant-checkbox-group,
        .ant-radio-group {
          gap: 6px;
        }
      }
    }
  }
}
