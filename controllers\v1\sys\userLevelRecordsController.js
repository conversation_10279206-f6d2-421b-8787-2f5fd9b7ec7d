const userLevelRecordsModel = require('../../../models/v1/mapping/userLevelRecordsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const userLevelsModel = require('../../../models/v1/mapping/userLevelsModel');
const { body, param, validationResult } = require('express-validator');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取用户职称记录列表
 * @route GET /v1/sys/user-level-records
 * @group 用户职称记录管理
 * @param {string} userId.query - 用户ID
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @returns {object} 200 - 成功返回职称记录列表
 */
exports.getUserLevelRecords = async (req, res) => {
    try {
        const { userId, page = 1, pageSize = 10 } = req.query;
        const where = {};
        
        if (userId) {
            where.userId = userId;
        }

        const offset = (page - 1) * pageSize;
        const limit = parseInt(pageSize);

        const { count, rows } = await userLevelRecordsModel.findAndCountAll({
            where,
            include: [
                {
                    model: userModel,
                    as: 'user',
                    attributes: ['id', 'nickname', 'username', 'studentNumber']
                },
                {
                    model: userLevelsModel,
                    as: 'level',
                    attributes: ['id', 'levelName', 'description', 'sort']
                },
                {
                    model: userModel,
                    as: 'creator',
                    attributes: ['id', 'nickname', 'username']
                }
            ],
            order: [['obtainedAt', 'DESC'], ['createdAt', 'DESC']],
            offset,
            limit
        });

        return res.status(200).json({
            code: 200,
            message: '获取用户职称记录成功',
            data: {
                list: rows,
                pagination: {
                    current: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total: count
                }
            }
        });
    } catch (error) {
        console.error('获取用户职称记录失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取用户职称记录失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 创建用户职称记录
 * @route POST /v1/sys/user-level-records
 * @group 用户职称记录管理
 * @param {string} userId.body.required - 用户ID
 * @param {string} levelId.body.required - 职称级别ID
 * @param {string} obtainedAt.body.required - 获得职称时间
 * @param {string} remarks.body - 备注
 * @returns {object} 200 - 成功创建职称记录
 */
exports.createUserLevelRecord = [
    body('userId').notEmpty().withMessage('用户ID不能为空'),
    body('levelId').notEmpty().withMessage('职称级别ID不能为空'),
    body('obtainedAt').notEmpty().withMessage('获得职称时间不能为空'),
    
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const { userId, levelId, obtainedAt, remarks } = req.body;
            const createdBy = req.user.id;

            // 验证用户是否存在
            const user = await userModel.findByPk(userId);
            if (!user) {
                return res.status(404).json({
                    code: 404,
                    message: '用户不存在',
                    data: null
                });
            }

            // 验证职称级别是否存在
            const level = await userLevelsModel.findByPk(levelId);
            if (!level) {
                return res.status(404).json({
                    code: 404,
                    message: '职称级别不存在',
                    data: null
                });
            }

            // 业务逻辑：检查用户当前职称级别，不能添加比当前级别更高的职称记录
            if (user.userLevelId) {
                const currentLevel = await userLevelsModel.findByPk(user.userLevelId);
                if (currentLevel && level.sort < currentLevel.sort) {
                    return res.status(400).json({
                        code: 400,
                        message: `不能添加比当前职称(${currentLevel.levelName})更高级别的职称记录`,
                        data: null
                    });
                }
            }

            // 创建职称记录
            const record = await userLevelRecordsModel.create({
                id: uuidv4(),
                userId,
                levelId,
                obtainedAt: new Date(obtainedAt),
                remarks,
                createdBy
            });

            // 更新用户的当前职称为最高级别（sort值最小）
            await updateUserCurrentLevel(userId);

            return res.status(201).json({
                code: 201,
                message: '创建用户职称记录成功',
                data: record
            });
        } catch (error) {
            console.error('创建用户职称记录失败:', error);
            return res.status(500).json({
                code: 500,
                message: '创建用户职称记录失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 更新用户职称记录
 * @route PUT /v1/sys/user-level-records/:id
 * @group 用户职称记录管理
 * @param {string} id.path.required - 记录ID
 * @param {string} levelId.body - 职称级别ID
 * @param {string} obtainedAt.body - 获得职称时间
 * @param {string} remarks.body - 备注
 * @returns {object} 200 - 成功更新职称记录
 */
exports.updateUserLevelRecord = [
    param('id').notEmpty().withMessage('记录ID不能为空'),
    
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const recordId = req.params.id;
            const { levelId, obtainedAt, remarks } = req.body;

            const record = await userLevelRecordsModel.findByPk(recordId);
            if (!record) {
                return res.status(404).json({
                    code: 404,
                    message: '职称记录不存在',
                    data: null
                });
            }

            // 如果更新了职称级别，需要验证
            if (levelId && levelId !== record.levelId) {
                const level = await userLevelsModel.findByPk(levelId);
                if (!level) {
                    return res.status(404).json({
                        code: 404,
                        message: '职称级别不存在',
                        data: null
                    });
                }
            }

            // 更新记录
            const updateData = {};
            if (levelId) updateData.levelId = levelId;
            if (obtainedAt) updateData.obtainedAt = new Date(obtainedAt);
            if (remarks !== undefined) updateData.remarks = remarks;

            await record.update(updateData);

            // 重新计算用户的当前职称
            await updateUserCurrentLevel(record.userId);

            return res.status(200).json({
                code: 200,
                message: '更新用户职称记录成功',
                data: record
            });
        } catch (error) {
            console.error('更新用户职称记录失败:', error);
            return res.status(500).json({
                code: 500,
                message: '更新用户职称记录失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 删除用户职称记录
 * @route DELETE /v1/sys/user-level-records/:id
 * @group 用户职称记录管理
 * @param {string} id.path.required - 记录ID
 * @returns {object} 200 - 成功删除职称记录
 */
exports.deleteUserLevelRecord = [
    param('id').notEmpty().withMessage('记录ID不能为空'),
    
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const recordId = req.params.id;
            const record = await userLevelRecordsModel.findByPk(recordId);
            
            if (!record) {
                return res.status(404).json({
                    code: 404,
                    message: '职称记录不存在',
                    data: null
                });
            }

            const userId = record.userId;
            await record.destroy();

            // 重新计算用户的当前职称
            await updateUserCurrentLevel(userId);

            return res.status(200).json({
                code: 200,
                message: '删除用户职称记录成功',
                data: null
            });
        } catch (error) {
            console.error('删除用户职称记录失败:', error);
            return res.status(500).json({
                code: 500,
                message: '删除用户职称记录失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 更新用户当前职称为最高级别（sort值最小）
 * @param {string} userId - 用户ID
 */
async function updateUserCurrentLevel(userId) {
    try {
        // 查找用户所有职称记录中sort值最小的（级别最高的）
        const highestRecord = await userLevelRecordsModel.findOne({
            where: { userId },
            include: [{
                model: userLevelsModel,
                as: 'level',
                attributes: ['id', 'levelName', 'sort']
            }],
            order: [
                [{ model: userLevelsModel, as: 'level' }, 'sort', 'ASC'],
                ['obtainedAt', 'DESC']
            ]
        });

        // 更新用户的当前职称
        const user = await userModel.findByPk(userId);
        if (user) {
            await user.update({
                userLevelId: highestRecord ? highestRecord.levelId : null
            });
        }
    } catch (error) {
        console.error('更新用户当前职称失败:', error);
        throw error;
    }
}

module.exports = {
    getUserLevelRecords: exports.getUserLevelRecords,
    createUserLevelRecord: exports.createUserLevelRecord,
    updateUserLevelRecord: exports.updateUserLevelRecord,
    deleteUserLevelRecord: exports.deleteUserLevelRecord,
    updateUserCurrentLevel
};
