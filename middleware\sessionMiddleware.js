const session = require('express-session');

/**
 * 创建会话中间件
 */
const createSession = () => {
  return session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 24小时
    }
  });
};

module.exports = {
  createSession
}; 