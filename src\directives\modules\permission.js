import dbUtils from "../../libs/util.strotage";

/**
 *<AUTHOR>
 *@date 2023/6/28 22:36
 *@Description:权限验证自定义指令
 * 使用: v-permission='sys'
 */
const permissionDirective = {
    mounted(el, binding) {
        // 从 binding.value 中获取所需的权限信息
        const permission = binding.value  // 'sys'
        if (!permission.length) return
        // 这里可以根据你的权限逻辑进行验证
        // 以下只是一个示例，你需要根据你的实际需求进行修改
        const hasPermission = checkPermission(permission)

        if (!hasPermission) {
            // 如果没有权限，从 DOM 中移除元素
            el.parentNode.removeChild(el)
        }
    }
}

// 进行权限验证的函数
function checkPermission(permission) {
    // 在这里实现你的权限验证逻辑
    // 这里只是一个示例，你需要根据你的实际需求进行修改  先去获取用户的所有权限再进行验证
    const permissionList = dbUtils.get('perms');
    
    // 超级权限判断
    if (permissionList.includes('*')) return true;
    
    // 精确匹配
    if (permissionList.includes(permission)) return true;
    
    // 如果是多级权限（包含多个冒号），尝试前缀匹配
    // 例如 score:researchProjects:self:reapply 可以匹配 score:researchProjects:self
    if (permission.includes(':')) {
        const parts = permission.split(':');
        // 如果权限包含多级，尝试检查其父级权限
        if (parts.length > 2) {
            // 移除最后一部分，尝试匹配父级权限
            const parentPerm = parts.slice(0, -1).join(':');
            if (permissionList.includes(parentPerm)) {
                return true;
            }
        }
    }
    
    return false;
}

export default permissionDirective
