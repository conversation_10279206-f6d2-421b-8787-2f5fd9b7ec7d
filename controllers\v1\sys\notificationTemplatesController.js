const { body, param, query, validationResult } = require('express-validator');
const { Op } = require('sequelize');
const apiResponse = require('@utils/apiResponse');
const { getUserInfoFromRequest } = require('@utils/others');
const { v4: uuidv4 } = require('uuid');

// 模拟模板数据（实际项目中应该创建专门的数据表）
let templates = [
    {
        id: uuidv4(),
        name: '系统维护通知',
        type: 'system',
        title: '系统维护通知',
        content: '尊敬的用户：\n\n系统将于{date}进行维护升级，维护时间预计{hours}小时，维护期间系统将暂停服务。\n\n给您带来的不便，敬请谅解！',
        description: '用于系统维护时发送通知，需要填写维护日期和预计时长。',
        status: 1,
        created_at: new Date('2023-12-01 10:00:00'),
        updated_at: new Date('2023-12-01 10:00:00'),
        created_by: 'admin'
    },
    {
        id: uuidv4(),
        name: '评分开始提醒',
        type: 'reminder',
        title: '{year}年度绩效评分工作开始通知',
        content: '各位老师：\n\n{year}年度绩效评分工作已经开始，请于{deadline}前完成相关数据填报和自评工作。\n\n评分系统访问地址：http://performance.jnu.edu.cn',
        description: '用于通知年度绩效评分工作开始，需要填写年份和截止日期。',
        status: 1,
        created_at: new Date('2023-12-05 14:30:00'),
        updated_at: new Date('2023-12-05 14:30:00'),
        created_by: 'admin'
    },
    {
        id: uuidv4(),
        name: '评分结果公示',
        type: 'result',
        title: '{year}年度绩效评分结果公示',
        content: '各位老师：\n\n{year}年度绩效评分工作已经结束，评分结果已经公示。请登录系统查看您的评分结果，如有异议，请于{deadline}前提出申诉。',
        description: '用于通知评分结果公示，需要填写年份和申诉截止日期。',
        status: 1,
        created_at: new Date('2023-12-10 09:15:00'),
        updated_at: new Date('2023-12-10 09:15:00'),
        created_by: 'admin'
    }
];

/**
 * 获取通知模板列表（分页）
 * @route GET /v1/sys/notifications/templates
 * @group 通知模板管理
 * @param {number} page.query - 页码
 * @param {number} pageSize.query - 每页条数
 * @param {string} type.query - 模板类型
 * @returns {object} 200 - 成功返回模板列表
 */
exports.getNotificationTemplates = [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页条数必须是1-100之间的整数'),
    query('type').optional().isString().withMessage('模板类型必须是字符串'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            const { page = 1, pageSize = 10, type } = req.query;
            const offset = (page - 1) * pageSize;
            const limit = parseInt(pageSize);

            // 过滤模板
            let filteredTemplates = templates.filter(template => template.status === 1);
            if (type) {
                filteredTemplates = filteredTemplates.filter(template => template.type === type);
            }

            // 分页
            const total = filteredTemplates.length;
            const list = filteredTemplates.slice(offset, offset + limit);

            return apiResponse.successResponseWithData(res, '获取模板列表成功', {
                list,
                total,
                page: parseInt(page),
                pageSize: limit
            });
        } catch (error) {
            console.error('获取模板列表失败:', error);
            return apiResponse.errorResponse(res, '获取模板列表失败');
        }
    }
];

/**
 * 获取通知模板详情
 * @route GET /v1/sys/notifications/templates/:id
 * @group 通知模板管理
 * @param {string} id.path - 模板ID
 * @returns {object} 200 - 成功返回模板详情
 */
exports.getNotificationTemplate = [
    param('id').notEmpty().withMessage('模板ID不能为空'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            const template = templates.find(t => t.id === req.params.id && t.status === 1);
            if (!template) {
                return apiResponse.notFoundResponse(res, '模板不存在');
            }

            return apiResponse.successResponseWithData(res, '获取模板详情成功', template);
        } catch (error) {
            console.error('获取模板详情失败:', error);
            return apiResponse.errorResponse(res, '获取模板详情失败');
        }
    }
];

/**
 * 创建通知模板
 * @route POST /v1/sys/notifications/templates
 * @group 通知模板管理
 * @param {string} name.body - 模板名称
 * @param {string} type.body - 通知类型
 * @param {string} title.body - 模板标题
 * @param {string} content.body - 模板内容
 * @param {string} description.body - 使用说明
 * @returns {object} 200 - 成功创建模板
 */
exports.createNotificationTemplate = [
    body('name').notEmpty().withMessage('模板名称不能为空').isLength({ max: 100 }).withMessage('模板名称不能超过100个字符'),
    body('type').notEmpty().withMessage('通知类型不能为空').isIn(['system', 'important', 'reminder', 'normal', 'result']).withMessage('无效的通知类型'),
    body('title').notEmpty().withMessage('模板标题不能为空').isLength({ max: 200 }).withMessage('模板标题不能超过200个字符'),
    body('content').notEmpty().withMessage('模板内容不能为空'),
    body('description').optional().isLength({ max: 500 }).withMessage('使用说明不能超过500个字符'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            const { name, type, title, content, description = '' } = req.body;
            const currentUser = await getUserInfoFromRequest(req);

            // 检查模板名称是否已存在
            const existingTemplate = templates.find(t => t.name === name && t.status === 1);
            if (existingTemplate) {
                return apiResponse.validationErrorResponse(res, '该模板名称已存在');
            }

            const newTemplate = {
                id: uuidv4(),
                name,
                type,
                title,
                content,
                description,
                status: 1,
                created_at: new Date(),
                updated_at: new Date(),
                created_by: currentUser.id
            };

            templates.push(newTemplate);

            return apiResponse.successResponseWithData(res, '创建模板成功', newTemplate);
        } catch (error) {
            console.error('创建模板失败:', error);
            return apiResponse.errorResponse(res, '创建模板失败');
        }
    }
];

/**
 * 更新通知模板
 * @route PUT /v1/sys/notifications/templates/:id
 * @group 通知模板管理
 * @param {string} id.path - 模板ID
 * @param {string} name.body - 模板名称
 * @param {string} type.body - 通知类型
 * @param {string} title.body - 模板标题
 * @param {string} content.body - 模板内容
 * @param {string} description.body - 使用说明
 * @returns {object} 200 - 成功更新模板
 */
exports.updateNotificationTemplate = [
    param('id').notEmpty().withMessage('模板ID不能为空'),
    body('name').optional().isLength({ max: 100 }).withMessage('模板名称不能超过100个字符'),
    body('type').optional().isIn(['system', 'important', 'reminder', 'normal', 'result']).withMessage('无效的通知类型'),
    body('title').optional().isLength({ max: 200 }).withMessage('模板标题不能超过200个字符'),
    body('description').optional().isLength({ max: 500 }).withMessage('使用说明不能超过500个字符'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            const templateIndex = templates.findIndex(t => t.id === req.params.id && t.status === 1);
            if (templateIndex === -1) {
                return apiResponse.notFoundResponse(res, '模板不存在');
            }

            // 如果更新名称，检查是否重复
            if (req.body.name && req.body.name !== templates[templateIndex].name) {
                const existingTemplate = templates.find(t => t.name === req.body.name && t.status === 1 && t.id !== req.params.id);
                if (existingTemplate) {
                    return apiResponse.validationErrorResponse(res, '该模板名称已存在');
                }
            }

            // 更新模板
            templates[templateIndex] = {
                ...templates[templateIndex],
                ...req.body,
                updated_at: new Date()
            };

            return apiResponse.successResponseWithData(res, '更新模板成功', templates[templateIndex]);
        } catch (error) {
            console.error('更新模板失败:', error);
            return apiResponse.errorResponse(res, '更新模板失败');
        }
    }
];

/**
 * 删除通知模板
 * @route DELETE /v1/sys/notifications/templates/:id
 * @group 通知模板管理
 * @param {string} id.path - 模板ID
 * @returns {object} 200 - 成功删除模板
 */
exports.deleteNotificationTemplate = [
    param('id').notEmpty().withMessage('模板ID不能为空'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            const templateIndex = templates.findIndex(t => t.id === req.params.id && t.status === 1);
            if (templateIndex === -1) {
                return apiResponse.notFoundResponse(res, '模板不存在');
            }

            // 软删除
            templates[templateIndex].status = 0;
            templates[templateIndex].updated_at = new Date();

            return apiResponse.successResponse(res, '删除模板成功');
        } catch (error) {
            console.error('删除模板失败:', error);
            return apiResponse.errorResponse(res, '删除模板失败');
        }
    }
];
