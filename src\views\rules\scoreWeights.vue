<template>
    <div class="score-weights">
      <!-- 错误信息展示区域 -->
      <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable style="margin-bottom: 16px;" />
      
      <!-- 总体得分饼状图统计 -->
      <a-card title="总体得分统计" :bordered="false" style="margin-bottom: 16px;">
        <!-- 先显示总分统计 -->
        <div class="total-score-header" style="margin-bottom: 20px;">
          <a-row :gutter="24">
            <!-- 原始得分 -->
            <a-col :xs="24" :md="12">
              <h3 style="text-align: center; margin-bottom: 16px;">
                原始总得分: <span style="color: #52c41a; font-weight: bold;">{{ totalScoreSummary.totalScore.toFixed(2) }}</span>
              </h3>
              <a-list size="small" :dataSource="totalScoreSummary.data" :bordered="false">
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <div style="display: flex; justify-content: space-between; width: 100%;">
                      <span style="font-weight: 500;">{{ item.name }}</span>
                      <span style="font-weight: 500; color: #1890ff;">{{ item.value.toFixed(2) }}</span>
                    </div>
                  </a-list-item>
        </template>
              </a-list>
            </a-col>
            
            <!-- 加权得分 -->
            <a-col :xs="24" :md="12">
              <h3 style="text-align: center; margin-bottom: 16px;">
                加权总得分: <span style="color: #722ed1; font-weight: bold;">{{ totalScoreSummary.weightedTotalScore.toFixed(2) }}</span>
              </h3>
              <a-list size="small" :dataSource="totalScoreSummary.weightedData" :bordered="false">
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <div style="display: flex; justify-content: space-between; width: 100%; align-items: center;">
                      <div style="display: flex; align-items: center; flex: 2;">
                        <span style="font-weight: 500;">{{ item.name }}</span>
                      </div>
                      <div style="display: flex; flex-direction: column; align-items: center; margin: 0 8px; flex: 1;">
                        <a-input-number 
                          v-model:value="customWeights[item.key]" 
                          :min="0" 
                          :max="10" 
                          :step="0.1" 
                          :precision="2"
                          style="width: 80px;"
                          @change="() => recalculateWeightedScores()"
                        />
                        <span style="font-size: 12px; color: #8c8c8c; margin-top: 4px;">
                          系统权重: {{ getSystemWeight(item.key) }}
                        </span>
                      </div>
                      <span style="font-weight: 500; color: #722ed1; flex: 1; text-align: right;">
                        {{ (item.value * customWeights[item.key]).toFixed(2) }}
                      </span>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
              <div style="text-align: right; margin-top: 8px;">
                <a-button type="primary" size="small" @click="resetCustomWeights">
                  重置权重
                </a-button>
              </div>
            </a-col>
          </a-row>
        </div>
        
        <!-- 饼图展示 -->
        <a-row :gutter="24">
                <a-col :span="12">
            <h4 style="text-align: center; margin-bottom: 8px;">原始得分分布</h4>
            <div ref="totalScoreChartContainer" style="height: 320px;"></div>
                </a-col>
                <a-col :span="12">
            <h4 style="text-align: center; margin-bottom: 8px;">加权得分分布</h4>
            <div ref="weightedScoreChartContainer" style="height: 320px;"></div>
                </a-col>
              </a-row>
            </a-card>
      
      <!-- 使用标签页组织不同类型的统计数据 -->
      <a-tabs v-model:activeKey="activeTabKey" type="card" @change="handleTabChange">
        <a-tab-pane key="research" tab="科研规则统计">
          <a-row :gutter="[16, 16]">
            <!-- 科研项目统计 -->
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="projectsCard"
                title="科研项目总分统计"
                countLabel="项目总数"
                :fetchApi="getProjectsTotalScore"
            :columns="projectTypeColumns"
            rowKey="type"
                totalCountKey="totalProjects"
                :getStatsFromResponse="response => response.data.typeStats || []"
                :style="{ display: activeTabKey === 'research' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('projects', data)"
              />
            </a-col>
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="papersCard"
                title="高水平论文总分统计"
                countLabel="论文总数"
                :fetchApi="getHighLevelPapersTotalScore"
                :columns="papersLevelColumns"
                rowKey="levelId"
                totalCountKey="totalPapers"
                :style="{ display: activeTabKey === 'research' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('papers', data)"
                  />
                </a-col>
            <!-- 第二行 -->
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="patentsCard"
                title="专利总分统计"
                countLabel="专利总数"
                :fetchApi="getPatentsTotalScore"
                :columns="patentsTypeColumns"
                rowKey="typeId"
                totalCountKey="totalPatents"
                :getStatsFromResponse="response => response.data.typeStats || []"
                :style="{ display: activeTabKey === 'research' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('patents', data)"
                  />
                </a-col>
              </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="teaching" tab="教学规则统计">
          <a-row :gutter="[16, 16]">
            <!-- 教学规则统计 - 第一行 -->
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="textbooksCard"
                title="教材与著作总分统计"
                countLabel="教材总数"
                :fetchApi="getTextbooksTotalScore"
            :columns="textbooksCategoryColumns"
            rowKey="categoryId"
                totalCountKey="totalTextbooks"
                :getStatsFromResponse="response => response.data.categoryStats || []"
                :style="{ display: activeTabKey === 'teaching' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('textbooks', data)"
              />
            </a-col>
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="teachingProjectsCard"
                title="教学改革项目总分统计"
                countLabel="项目总数"
                :fetchApi="getTeachingReformProjectTotalScore"
                :columns="teachingReformProjectsLevelColumns"
                rowKey="levelId"
                totalCountKey="totalProjects"
                :style="{ display: activeTabKey === 'teaching' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('teachingProjects', data)"
              />
            </a-col>
            <!-- 第二行 -->
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="awardsCard"
                title="学生获奖指导奖项总分统计"
                countLabel="奖项总数"
                :fetchApi="getAwardTotalScore"
                :columns="awardsLevelColumns"
                rowKey="levelId"
                totalCountKey="totalAwards"
                :style="{ display: activeTabKey === 'teaching' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('awards', data)"
              />
            </a-col>
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="studentProjectsCard"
                title="学生项目指导总分统计"
                countLabel="项目总数"
                :fetchApi="getStudentProjectTotalScore"
                :columns="studentProjectLevelColumns"
                rowKey="levelId"
                totalCountKey="totalProjects"
                :style="{ display: activeTabKey === 'teaching' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('studentProjects', data)"
              />
            </a-col>
            <!-- 第三行 - 新增教学工作量和教学科研奖励 -->
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="teachingWorkloadsCard"
                title="教学工作量总分统计"
                countLabel="工作量总数"
                :fetchApi="getTeachingWorkloadsTotalScore"
                :columns="teachingWorkloadsLevelColumns"
                rowKey="levelId"
                totalCountKey="totalWorkloads"
                :style="{ display: activeTabKey === 'teaching' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('teachingWorkloads', data)"
              />
            </a-col>
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="teachingResearchAwardsCard"
                title="教学科研奖励总分统计"
                countLabel="奖励总数"
                :fetchApi="getTeachingResearchAwardsTotalScore"
                :columns="teachingResearchAwardsLevelColumns"
                rowKey="levelId"
                totalCountKey="totalAwards"
                :style="{ display: activeTabKey === 'teaching' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('teachingResearchAwards', data)"
              />
            </a-col>
          </a-row>
        </a-tab-pane>
        
        <a-tab-pane key="social" tab="社会服务统计">
          <a-row :gutter="[16, 16]">
            <!-- 社会服务统计 -->
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="conferencesCard"
                title="会议总分统计"
                countLabel="会议总数"
                :fetchApi="getConferencesTotalScore"
                :columns="conferencesLevelColumns"
                rowKey="levelId"
                totalCountKey="totalConferences"
                :style="{ display: activeTabKey === 'social' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('conferences', data)"
              />
            </a-col>
            <a-col :xs="24" :lg="12">
              <StatsCard
                ref="appointmentsCard"
                title="学术任职总分统计"
                countLabel="任职总数"
                :fetchApi="getAcademicAppointmentsTotalScore"
                :columns="appointmentsLevelColumns"
                rowKey="levelId"
                totalCountKey="totalAppointments"
                :style="{ display: activeTabKey === 'social' ? 'block' : 'none' }"
                @data-loaded="(data) => updateChartData('appointments', data)"
              />
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
      
      <!-- 评分权重管理部分，固定在下方 -->
      <a-card title="评分权重管理" :bordered="false" style="margin-top: 24px;">
        <template #extra>
          <a-space>
            <!-- <a-button type="primary" @click="showAddModal">
              <template #icon><PlusOutlined /></template>
              添加权重
            </a-button> -->
          </a-space>
        </template>
  
        <a-alert
          message="权重说明"
          description="评分权重系数用于调整各类评分项目的权重，影响最终得分计算。不同的权重系数对应不同的得分贡献度。"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
  
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="isLoading"
          rowKey="id"
          @change="handleTableChange"
          :scroll="{ x: 800 }"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 1 ? 'green' : 'red'">
                {{ record.status === 1 ? '启用' : '禁用' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'weight'">
              <span style="font-weight: bold; color: '#1890ff';">{{ record.weight }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a @click="handleEdit(record)">编辑</a>
                <!-- <a-divider type="vertical" />
                <a-popconfirm
                  title="确定要删除这个权重吗？"
                  @confirm="handleDelete(record)"
                >
                  <a class="text-danger">删除</a>
                </a-popconfirm> -->
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
  
        <!-- 使用ZyModal替代a-modal -->
        <ZyModal
          :show="modalVisible"
          :title="isEdit ? '编辑评分权重' : '添加评分权重'"
          :min-width="500"
          :min-height="300"
          @close="handleModalCancel"
        >
          <div class="weight-form">
            <a-form
              :model="formState"
              :rules="rules"
              ref="formRef"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-form-item label="类别名称" name="categoryName">
                <a-input v-model:value="formState.categoryName" placeholder="请输入类别名称" />
              </a-form-item>
              
              <!-- <a-form-item label="类别代码" name="categoryCode" :disabled="isEdit">
                <a-input v-model:value="formState.categoryCode" placeholder="请输入类别代码" :disabled="isEdit" />
              </a-form-item> -->
              
              <a-form-item label="权重系数" name="weight">
                <a-input-number
                  v-model:value="formState.weight"
                  :min="0"
                  :max="10"
                  :step="0.01"
                  :precision="2"
                  style="width: 100%"
                />
              </a-form-item>
              
              <!-- <a-form-item label="状态" name="status">
                <a-switch
                  v-model:checked="formState.status"
                  checked-children="启用"
                  un-checked-children="禁用"
                />
              </a-form-item> -->
  
              <a-form-item label="描述" name="description">
                <a-textarea
                  v-model:value="formState.description"
                  :rows="4"
                  placeholder="请输入描述"
                />
              </a-form-item>
              
              <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
                <a-space>
                  <a-button type="primary" @click="handleSubmit" :loading="confirmLoading">
                    提交
                  </a-button>
                  <a-button @click="handleModalCancel">
                    取消
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>
        </ZyModal>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import ZyModal from '@/components/common/ZyModal.vue'
  import StatsCard from '@/components/statistics/StatsCard.vue'
  import * as echarts from 'echarts'
  
  import { 
    getAllScoreWeights,
    getScoreWeightDetail,
    createScoreWeight,
    updateScoreWeight,
    deleteScoreWeight
  } from '@/api/rules/scoreWeights'
  
  // 导入API函数
  import { getProjectsTotalScore } from '@/api/modules/api.researchProjects'
  import { getTextbooksTotalScore } from '@/api/modules/api.textbooks'
  import { getHighLevelPapersTotalScore } from '@/api/modules/api.high-level-papers'
  import { getPatentsTotalScore } from '@/api/modules/api.patents'
  import { getAwardTotalScore } from '@/api/modules/api.studentAwardGuidanceAwards'
  import { getProjectTotalScore as getStudentProjectTotalScore } from '@/api/modules/api.studentProjectGuidanceProjects'
  import { getProjectTotalScore as getTeachingReformProjectTotalScore } from '@/api/modules/api.teachingReformProjects'
  import { getAcademicAppointmentsTotalScore } from '@/api/modules/api.academicAppointments'
  import { getConferencesTotalScore } from '@/api/modules/api.conferences'
  import { getTeachingWorkloadsTotalScore } from '@/api/modules/api.teachingWorkloads'
  import { getTeachingResearchAwardsTotalScore } from '@/api/modules/api.teachingResearchAwards'
  
  // 当前活动标签页
  const activeTabKey = ref('research')
  
  // StatsCard组件引用
  const projectsCard = ref(null)
  const papersCard = ref(null)
  const patentsCard = ref(null)
  const textbooksCard = ref(null)
  const teachingProjectsCard = ref(null)
  const awardsCard = ref(null)
  const studentProjectsCard = ref(null)
  const conferencesCard = ref(null)
  const appointmentsCard = ref(null)
  const teachingWorkloadsCard = ref(null)
  const teachingResearchAwardsCard = ref(null)
  
  // 处理标签页切换
  const handleTabChange = (key) => {
    console.log(`标签切换到: ${key}`)
    activeTabKey.value = key
    
    // 根据当前选中的标签页，重新加载相应的数据
    nextTick(() => {
      if (key === 'research') {
        projectsCard.value?.reloadData()
        papersCard.value?.reloadData()
        patentsCard.value?.reloadData()
      } else if (key === 'teaching') {
        textbooksCard.value?.reloadData()
        teachingProjectsCard.value?.reloadData()
        awardsCard.value?.reloadData()
        studentProjectsCard.value?.reloadData()
        teachingWorkloadsCard.value?.reloadData()
        teachingResearchAwardsCard.value?.reloadData()
      } else if (key === 'social') {
        conferencesCard.value?.reloadData()
        appointmentsCard.value?.reloadData()
      }
    })
  }

  // 总体得分数据
  const totalScoreSummary = reactive({
    totalScore: 0,
    weightedTotalScore: 0,
    data: [],
    weightedData: []
  })
  
  // 各模块数据存储
  const moduleData = reactive({
    projects: { totalScore: 0, stats: [] },
    papers: { totalScore: 0, stats: [] },
    patents: { totalScore: 0, stats: [] },
    textbooks: { totalScore: 0, stats: [] },
    teachingProjects: { totalScore: 0, stats: [] },
    awards: { totalScore: 0, stats: [] },
    studentProjects: { totalScore: 0, stats: [] },
    conferences: { totalScore: 0, stats: [] },
    appointments: { totalScore: 0, stats: [] },
    teachingWorkloads: { totalScore: 0, stats: [] },
    teachingResearchAwards: { totalScore: 0, stats: [] }
  })

  // 饼图颜色
  const chartColors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', 
    '#722ed1', '#13c2c2', '#fa8c16', '#eb2f96', '#a0d911'
  ]

  // 图表容器引用
  const totalScoreChartContainer = ref(null)
  let totalChart = null

  // 添加加权饼图容器引用
  const weightedScoreChartContainer = ref(null)
  let weightedChart = null

  // 添加自定义权重对象
  const customWeights = reactive({})
  
  // 模块名称映射
  const moduleNames = {
    projects: '科研项目总分统计',
    papers: '高水平论文总分统计',
    patents: '专利总分统计',
    textbooks: '教材与著作总分统计',
    teachingProjects: '教学改革项目总分统计',
    awards: '学生获奖指导奖项总分统计',
    studentProjects: '学生项目指导总分统计',
    conferences: '会议总分统计',
    appointments: '学术任职总分统计',
    teachingWorkloads: '教学工作量总分统计',
    teachingResearchAwards: '教学科研奖励总分统计'
  }

  // 重置自定义权重
  const resetCustomWeights = () => {
    Object.keys(customWeights).forEach(key => {
      const categoryCode = moduleCodeMap[key]
      const weightItem = dataSource.value.find(item => item.categoryCode === categoryCode)
      customWeights[key] = weightItem ? parseFloat(weightItem.weight) : 1.0
    })
    recalculateWeightedScores()
  }

  // 重新计算加权得分
  const recalculateWeightedScores = () => {
    let weightedTotalScore = 0
    
    const weightedDetailData = totalScoreSummary.data.map(item => {
      const moduleKey = Object.keys(moduleNames).find(key => moduleNames[key] === item.name)
      const weight = customWeights[moduleKey] || 1.0
      const weightedValue = item.value * weight
      
      weightedTotalScore += weightedValue
      
      return {
        name: item.name,
        key: moduleKey,
        value: item.value,
        weight: weight,
        weightedValue: weightedValue
      }
    })
    
    totalScoreSummary.weightedTotalScore = weightedTotalScore
    totalScoreSummary.weightedData = weightedDetailData
    
    nextTick(() => {
      updateWeightedScoreChart()
    })
  }

  // 更新图表数据
  const updateChartData = (moduleKey, data, skipTotalUpdate = false) => {
    console.log(`${moduleKey} 数据更新:`, data);
    
    // 更新模块数据
    if (data) {
      moduleData[moduleKey].totalScore = parseFloat(data.totalScore || 0)
      moduleData[moduleKey].stats = data.stats || []
      
      // 记录数据来源，确保后续能找到对应数据
      console.log(`模块 ${moduleKey} 数据已更新，总分: ${moduleData[moduleKey].totalScore}`);
    }
    
    // 如果需要跳过总体统计更新，直接返回
    if (skipTotalUpdate) {
      return;
    }
    
    // 立即计算总得分
    calculateTotalScore();
  }

  // 更新总得分饼图 - 修改标题
  const updateTotalScoreChart = () => {
    if (!totalScoreChartContainer.value) {
      console.warn('总分饼图容器不存在，无法渲染');
      return;
    }

    if (totalScoreSummary.data.length === 0) {
      console.warn('总分数据为空，无法渲染饼图');
      return;
    }

    console.log('更新总分饼图，数据:', totalScoreSummary.data);

    if (!totalChart) {
      totalChart = echarts.init(totalScoreChartContainer.value);
      console.log('初始化总分饼图');
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        left: 10,
        bottom: 10,
        itemGap: 10,
        itemWidth: 15,
        itemHeight: 10,
        textStyle: {
          fontSize: 12
        },
        pageButtonItemGap: 5,
        pageButtonPosition: 'end'
      },
      series: [
        {
          name: '原始得分',
          type: 'pie',
          radius: ['20%', '55%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 1
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{c}分',
            color: '#333',
            fontSize: 12
          },
          labelLine: {
            show: true,
            length: 12,
            length2: 8,
            smooth: true
          },
          data: totalScoreSummary.data,
          color: chartColors
        }
      ]
    }

    // 使用try-catch捕获图表渲染过程中可能发生的错误
    try {
      totalChart.setOption(option);
      console.log('总分饼图更新成功');
    } catch (error) {
      console.error('更新总分饼图失败:', error);
    }
  }

  // 添加加权得分饼图
  const updateWeightedScoreChart = () => {
    if (!weightedScoreChartContainer.value) {
      console.warn('加权饼图容器不存在，无法渲染');
      return;
    }

    if (totalScoreSummary.weightedData.length === 0) {
      console.warn('加权数据为空，无法渲染饼图');
      return;
    }

    console.log('更新加权饼图，数据:', totalScoreSummary.weightedData);

    if (!weightedChart) {
      weightedChart = echarts.init(weightedScoreChartContainer.value);
      console.log('初始化加权饼图');
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        left: 10,
        bottom: 10,
        itemGap: 10,
        itemWidth: 15,
        itemHeight: 10,
        textStyle: {
          fontSize: 12
        },
        pageButtonItemGap: 5,
        pageButtonPosition: 'end'
      },
      series: [
        {
          name: '加权得分',
          type: 'pie',
          radius: ['20%', '55%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 1
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{c}分',
            color: '#333',
            fontSize: 12
          },
          labelLine: {
            show: true,
            length: 12,
            length2: 8,
            smooth: true
          },
          data: totalScoreSummary.weightedData.map(item => ({
            name: item.name,
            value: item.weightedValue
          })),
          color: chartColors
        }
      ]
    }

    // 使用try-catch捕获图表渲染过程中可能发生的错误
    try {
      weightedChart.setOption(option);
      console.log('加权饼图更新成功');
    } catch (error) {
      console.error('更新加权饼图失败:', error);
    }
  }
  
  // 窗口大小调整时重新绘制图表
  const handleResize = () => {
    if (totalChart) {
      totalChart.resize()
    }
    if (weightedChart) {
      weightedChart.resize()
    }
  }
  
  // 定义所有统计表格的列
  // 项目类型表格列定义
  const projectTypeColumns = [
    {
      title: '项目类型',
      dataIndex: 'typeName',
      key: 'typeName',
    },
    {
      title: '项目数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 教材类别表格列定义
  const textbooksCategoryColumns = [
    {
      title: '教材类别',
      dataIndex: 'categoryName',
      key: 'categoryName',
    },
    {
      title: '教材数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 论文级别表格列定义
  const papersLevelColumns = [
    {
      title: '论文级别',
      dataIndex: 'levelName',
      key: 'levelName',
    },
    {
      title: '论文数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]
  
  // 专利类型表格列定义
  const patentsTypeColumns = [
    {
      title: '专利类型',
      dataIndex: 'typeName',
      key: 'typeName',
    },
    {
      title: '专利数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 奖项级别表格列定义
  const awardsLevelColumns = [
    {
      title: '奖项级别',
      dataIndex: 'levelName',
      key: 'levelName',
    },
    {
      title: '奖项数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 学生项目级别表格列定义
  const studentProjectLevelColumns = [
    {
      title: '项目级别',
      dataIndex: 'levelName',
      key: 'levelName',
    },
    {
      title: '项目数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 教学改革项目级别表格列定义
  const teachingReformProjectsLevelColumns = [
    {
      title: '项目级别',
      dataIndex: 'levelName',
      key: 'levelName',
    },
    {
      title: '项目数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 学术任职级别表格列定义
  const appointmentsLevelColumns = [
    {
      title: '任职级别',
      dataIndex: 'levelName',
      key: 'levelName',
    },
    {
      title: '任职数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 会议级别表格列定义
  const conferencesLevelColumns = [
    {
      title: '会议级别',
      dataIndex: 'levelName',
      key: 'levelName',
    },
    {
      title: '会议数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]
  
  // 教学工作量级别表格列定义
  const teachingWorkloadsLevelColumns = [
    {
      title: '工作量级别',
      dataIndex: 'levelName',
      key: 'levelName',
    },
    {
      title: '工作量数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 教学科研奖励级别表格列定义
  const teachingResearchAwardsLevelColumns = [
    {
      title: '奖励级别',
      dataIndex: 'levelName',
      key: 'levelName',
    },
    {
      title: '奖励数量',
      dataIndex: 'count',
      key: 'count',
      width: '150px',
    },
    {
      title: '总得分',
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: '150px',
      sorter: (a, b) => a.totalScore - b.totalScore,
      defaultSortOrder: 'descend'
    }
  ]

  // 权重管理相关代码 - 保持不变
  // 表格列定义
  const columns = [
    {
      title: '类别名称',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: '180px',
    },
    // {
    //   title: '类别代码',
    //   dataIndex: 'categoryCode',
    //   key: 'categoryCode',
    //   width: '150px',
    // },
    {
      title: '权重系数',
      dataIndex: 'weight',
      key: 'weight',
      width: '100px',
    },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   key: 'status',
    //   width: '100px',
    // },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: '150px',
      fixed: 'right',
    },
  ]
  
  // 数据源
  const dataSource = ref([])
  const isLoading = ref(false)
  
  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  })
  
  // 模态框相关
  const modalVisible = ref(false)
  const confirmLoading = ref(false)
  const isEdit = ref(false)
  const currentRecord = ref(null)
  
  // 表单引用
  const formRef = ref(null)
  
  // 表单数据
  const formState = reactive({
    categoryName: '',
    categoryCode: '',
    weight: 1.00,
    status: true,
    description: ''
  })
  
  // 表单验证规则
  const rules = {
    categoryName: [
      { required: true, message: '请输入类别名称', trigger: 'blur' },
      { min: 2, max: 100, message: '类别名称长度在2-100个字符之间', trigger: 'blur' }
    ],
    categoryCode: [
      { required: true, message: '请输入类别代码', trigger: 'blur' },
      { min: 2, max: 50, message: '类别代码长度在2-50个字符之间', trigger: 'blur' }
    ],
    weight: [
      { required: true, message: '请输入权重系数', trigger: 'change' },
      { type: 'number', message: '权重系数必须为数字', trigger: 'change' }
    ]
  }
  
  // 添加错误状态
  const errorMessage = ref('')
  
  // 获取列表数据
  const fetchData = async () => {
    isLoading.value = true
    errorMessage.value = ''
    
    try {
      const response = await getAllScoreWeights()
      
      if (response && response.code === 200) {
        dataSource.value = response.data || []
        pagination.total = dataSource.value.length
        
        // 初始化自定义权重，从权重管理数据中获取默认值
        initCustomWeightsFromSystem()
        
      } else {
        message.error(response?.message || '获取数据失败')
        errorMessage.value = '获取权重列表失败：' + (response?.message || '未知错误')
      }
    } catch (error) {
      console.error('获取评分权重列表失败:', error)
      message.error('获取评分权重列表失败: ' + (error.message || '未知错误'))
      errorMessage.value = '获取权重列表失败：' + (error.message || '未知错误')
    } finally {
      isLoading.value = false
    }
  }
  
  // 添加初始化权重的函数
  const initCustomWeightsFromSystem = () => {
    // 输出信息到控制台，方便调试
    console.log('权重数据:', dataSource.value)
    
    // 遍历所有模块，从系统权重设置中获取默认值
    Object.keys(moduleCodeMap).forEach(key => {
      const categoryCode = moduleCodeMap[key]
      const weightItem = dataSource.value.find(item => 
        item.categoryCode.toLowerCase() === categoryCode.toLowerCase())
      
      if (weightItem) {
        // 使用系统设置的权重作为初始值
        customWeights[key] = parseFloat(weightItem.weight)
        console.log(`模块 ${key} (${categoryCode}) 的权重: ${weightItem.weight}`)
      } else {
        // 如果系统中没有设置，使用1.0作为默认值
        customWeights[key] = 1.0
        console.log(`警告: 模块 ${key} (${categoryCode}) 在权重配置中不存在，使用默认值1.0`)
      }
    })
    
    // 如果已经有统计数据，重新计算加权得分
    if (totalScoreSummary.data.length > 0) {
      recalculateWeightedScores()
    }
  }
  
  // 处理表格变化
  const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
  }
  
  // 添加新权重
  const showAddModal = () => {
    isEdit.value = false
    currentRecord.value = null
    resetForm()
    modalVisible.value = true
  }
  
  // 编辑权重
  const handleEdit = async (record) => {
    isEdit.value = true
    currentRecord.value = record
    resetForm()
    
    try {
      const response = await getScoreWeightDetail(record.id)
      
      if (response && response.code === 200) {
        const detail = response.data
        
        formState.categoryName = detail.categoryName
        formState.categoryCode = detail.categoryCode
        formState.weight = detail.weight
        formState.status = detail.status === 1
        formState.description = detail.description
        
        modalVisible.value = true
      } else {
        message.error(response?.message || '获取详情失败')
      }
    } catch (error) {
      console.error('获取权重详情失败:', error)
      message.error('获取权重详情失败: ' + (error.message || '未知错误'))
    }
  }
  
  // 处理删除
  const handleDelete = async (record) => {
    try {
      const response = await deleteScoreWeight(record.id)
      
      if (response && response.code === 200) {
        message.success('删除成功')
        fetchData()
      } else {
        message.error(response?.message || '删除失败')
      }
    } catch (error) {
      console.error('删除评分权重失败:', error)
      message.error('删除评分权重失败: ' + (error.message || '未知错误'))
    }
  }
  
  // 提交表单
  const handleSubmit = () => {
    formRef.value.validate().then(async () => {
      try {
        confirmLoading.value = true
        
        const formData = {
          categoryName: formState.categoryName,
          categoryCode: formState.categoryCode,
          weight: formState.weight,
          status: formState.status ? 1 : 0,
          description: formState.description
        }
        
        let response
        if (isEdit.value) {
          response = await updateScoreWeight(currentRecord.value.id, formData)
        } else {
          response = await createScoreWeight(formData)
        }
        
        if (response && response.code === 200) {
          message.success(isEdit.value ? '更新成功' : '添加成功')
          modalVisible.value = false
          fetchData()
        } else {
          message.error(response?.message || (isEdit.value ? '更新失败' : '添加失败'))
        }
      } catch (error) {
        console.error(isEdit.value ? '更新评分权重失败:' : '添加评分权重失败:', error)
        message.error(isEdit.value ? '更新评分权重失败: ' + (error.message || '未知错误') : '添加评分权重失败: ' + (error.message || '未知错误'))
      } finally {
        confirmLoading.value = false
      }
    }).catch(errors => {
      console.log('表单验证失败', errors)
      const errorFields = errors.errorFields || []
      errorFields.forEach(field => {
        message.error(`${field.name.join('.')}：${field.errors.join(', ')}`)
      })
    })
  }
  
  // 模态框取消
  const handleModalCancel = () => {
    modalVisible.value = false
  }
  
  // 重置表单
  const resetForm = () => {
    formState.categoryName = ''
    formState.categoryCode = ''
    formState.weight = 1.00
    formState.status = true
    formState.description = ''
    
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }
  
  // 模块代码映射
  const moduleCodeMap = {
    projects: 'research_projects',
    papers: 'high_level_papers',
    patents: 'patents',
    textbooks: 'textbooks',
    teachingProjects: 'teaching_reform_projects',
    awards: 'student_award_guidance',
    studentProjects: 'student_project_guidance',
    conferences: 'conferences',
    appointments: 'academicAppointments',
    teachingWorkloads: 'teaching_workloads',
    teachingResearchAwards: 'teaching_research_awards'
  }
  
  // 主动加载所有统计数据
  const loadAllStatsData = async () => {
    try {
      console.log('开始加载所有统计数据...')
      
      // 并行请求所有数据，等待全部完成
      const promises = [
        getProjectsTotalScore({ range: 'all', reviewStatus: 'all' }),
        getHighLevelPapersTotalScore({ range: 'all', reviewStatus: 'all' }),
        getPatentsTotalScore({ range: 'all', reviewStatus: 'all' }),
        getTextbooksTotalScore({ range: 'all', reviewStatus: 'all' }),
        getTeachingReformProjectTotalScore({ range: 'all', reviewStatus: 'all' }),
        getAwardTotalScore({ range: 'all', reviewStatus: 'all' }),
        getStudentProjectTotalScore({ range: 'all', reviewStatus: 'all' }),
        getConferencesTotalScore({ range: 'all', reviewStatus: 'all' }),
        getAcademicAppointmentsTotalScore({ range: 'all', reviewStatus: 'all' }),
        getTeachingWorkloadsTotalScore({ range: 'all', reviewStatus: 'all' }),
        getTeachingResearchAwardsTotalScore({ range: 'all', reviewStatus: 'all' })
      ];
      
      const results = await Promise.all(promises);
      
      console.log('所有API请求完成，正在处理数据...');
      
      // 处理各模块数据，但不更新总体统计图表
      const processResponse = (moduleKey, response) => {
        console.log(`处理${moduleKey}模块数据:`, response);
        
        if (response && response.code === 200 && response.data) {
          let totalScore = 0;
          let stats = [];
          
          // 根据不同模块的数据结构提取数据
          if (moduleKey === 'projects' || moduleKey === 'patents') {
            totalScore = response.data.totalStats?.totalScore || 0;
            stats = response.data.typeStats || [];
          } else if (moduleKey === 'textbooks') {
            totalScore = response.data.overallStats?.totalScore || 0;
            stats = response.data.categoryStats || [];
          } else if (['teachingProjects', 'awards', 'studentProjects', 'conferences', 'appointments', 'papers'].includes(moduleKey)) {
        if (response.data.overallStats) {
              totalScore = response.data.overallStats.totalScore || 0;
            } else if (response.data.totalStats) {
              totalScore = response.data.totalStats.totalScore || 0;
            }
            stats = response.data.levelStats || [];
          } else if (moduleKey === 'teachingWorkloads' || moduleKey === 'teachingResearchAwards') {
            totalScore = response.data.overallStats?.totalScore || 0;
            stats = response.data.levelStats || [];
          }
          
          console.log(`${moduleKey} 解析结果: 总分=${totalScore}, 详细数据:`, stats);
          
          // 更新模块数据
          moduleData[moduleKey].totalScore = parseFloat(totalScore);
          moduleData[moduleKey].stats = stats;
          
          return { totalScore, stats };
        } else {
          console.warn(`获取${moduleNames[moduleKey]}数据失败`, response?.message || '未知错误');
          return { totalScore: 0, stats: [] };
        }
      };
      
      // 依次处理每个模块的数据
      processResponse('projects', results[0]);
      processResponse('papers', results[1]);
      processResponse('patents', results[2]);
      processResponse('textbooks', results[3]);
      processResponse('teachingProjects', results[4]);
      processResponse('awards', results[5]);
      processResponse('studentProjects', results[6]);
      processResponse('conferences', results[7]);
      processResponse('appointments', results[8]);
      processResponse('teachingWorkloads', results[9]);
      processResponse('teachingResearchAwards', results[10]);
      
      // 打印处理后的moduleData，用于调试
      console.log('处理后的模块数据:', JSON.stringify(moduleData));
      
      console.log('所有统计数据加载完成，开始计算总体得分...');
      
      // 计算总体得分并更新图表
      calculateTotalScore();
      
    } catch (error) {
      console.error('加载统计数据失败:', error);
      message.error('加载统计数据失败：' + (error.message || '未知错误'));
    }
  }
  
  // 添加一个新方法，专门用于计算总得分并更新图表
  const calculateTotalScore = () => {
    console.log('计算总体得分...');
    
    // 计算总得分
    let totalScore = 0;
    let weightedTotalScore = 0;
    
    // 详细数据列表
    const detailData = [];
    const weightedDetailData = [];
    
    // 计算每个具体模块的总分和总体得分
    Object.entries(moduleData).forEach(([key, module]) => {
      console.log(`模块 ${key}: 总分=${module.totalScore}`, module);
      
      if (module.totalScore > 0) {
        totalScore += module.totalScore;
        
        // 获取对应的权重系数
        const categoryCode = moduleCodeMap[key];
        const weightItem = dataSource.value.find(item => 
          item.categoryCode.toLowerCase() === categoryCode.toLowerCase());
        const weight = weightItem ? parseFloat(weightItem.weight) : 1.0;
        
        // 确保自定义权重中有这个键
        if (customWeights[key] === undefined) {
          customWeights[key] = weight;
        }
        
        const weightedValue = module.totalScore * customWeights[key];
        
        // 累加加权总分
        weightedTotalScore += weightedValue;
        
        // 添加到详细数据列表
        detailData.push({
          name: moduleNames[key] || key,
          key: key,
          value: module.totalScore
        });
        
        // 添加到加权数据列表
        weightedDetailData.push({
          name: moduleNames[key] || key,
          key: key,
          value: module.totalScore,
          weight: customWeights[key],
          weightedValue: weightedValue
        });
      }
    });
    
    // 更新总得分摘要
    totalScoreSummary.totalScore = totalScore;
    totalScoreSummary.weightedTotalScore = weightedTotalScore;
    
    // 使用详细数据作为图表数据
    totalScoreSummary.data = detailData;
    totalScoreSummary.weightedData = weightedDetailData;
    
    console.log('总得分计算完成，更新图表...');
    console.log('详细数据:', detailData);
    console.log('加权数据:', weightedDetailData);
    
    // 更新总得分饼图
    nextTick(() => {
      updateTotalScoreChart();
      updateWeightedScoreChart();
    });
  }
  
  // 添加获取系统权重的辅助函数
  const getSystemWeight = (moduleKey) => {
    const categoryCode = moduleCodeMap[moduleKey]
    const weightItem = dataSource.value.find(item => 
      item.categoryCode.toLowerCase() === categoryCode.toLowerCase())
    return weightItem ? weightItem.weight : '1.00'
  }
  
  // 加载数据和初始化图表
  onMounted(() => {
    console.log('组件已挂载，开始初始化...');
    
    // 首先获取权重数据
    fetchData();
    
    // 等待DOM渲染完成后再加载数据和初始化图表
    nextTick(() => {
      // 检查容器是否已渲染
      console.log('总分图表容器:', totalScoreChartContainer.value);
      console.log('加权图表容器:', weightedScoreChartContainer.value);
      
      // 主动加载所有数据
      loadAllStatsData();
      
      // 监听窗口大小变化，重绘图表
      window.addEventListener('resize', handleResize);
      
      console.log('监听器已添加，初始化完成');
    });
  })
  </script>
  
  <style scoped>
  .score-weights {
    margin: 24px;
    border: 1px solid #f0f0f0;
  }
  
  .text-danger {
    color: #ff4d4f;
  }
  
  .weight-form {
    padding: 20px;
  }
  
  /* 标签页样式优化 */
  :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
    padding: 8px 16px;
    margin-right: 4px;
  }

  :deep(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
    font-weight: 500;
    color: #1890ff;
  }

  :deep(.ant-card-head) {
    min-height: 48px;
  }
  
  .total-score-summary {
    padding: 16px;
    background: #f5f5f5;
    border-radius: 4px;
    height: 100%;
    overflow-y: auto;
  }
  
  .total-score-summary h3 {
    margin-bottom: 16px;
    font-weight: bold;
    text-align: center;
  }
  </style>