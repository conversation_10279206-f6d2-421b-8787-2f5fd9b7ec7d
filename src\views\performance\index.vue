<template>
  <div class="performance-dashboard">
    <a-row :gutter="16">
      <a-col :span="24">
        <a-card title="绩效评分统计概览" :bordered="false">
          <a-alert
            message="欢迎使用绩效评分管理系统"
            description="本系统用于管理教师绩效评分，包括科研项目、科研经费、高水平论文、教学与科研获奖、国际交流、社会服务、就业质量等维度的评分管理。"
            type="info"
            show-icon
          />
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="12">
        <a-card title="各维度评分分布" :bordered="false">
          <div ref="pieChart" style="height: 400px"></div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="月度评分趋势" :bordered="false">
          <div ref="barChart" style="height: 400px"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" style="margin-top: 16px">
      <a-col :span="24">
        <a-card title="评分详情" :bordered="false">
          <a-table :columns="columns" :data-source="data" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'score'">
                <a-progress
                  :percent="record.score"
                  :status="record.score >= 80 ? 'success' : record.score >= 60 ? 'normal' : 'exception'"
                />
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const pieChart = ref(null)
const barChart = ref(null)

// 表格列定义
const columns = [
  {
    title: '评分维度',
    dataIndex: 'dimension',
    key: 'dimension',
  },
  {
    title: '平均分',
    dataIndex: 'score',
    key: 'score',
  },
  {
    title: '参与人数',
    dataIndex: 'participants',
    key: 'participants',
  },
  {
    title: '最高分',
    dataIndex: 'maxScore',
    key: 'maxScore',
  },
  {
    title: '最低分',
    dataIndex: 'minScore',
    key: 'minScore',
  },
]

// 模拟数据
const data = [
  {
    key: '1',
    dimension: '科研项目',
    score: 85,
    participants: 120,
    maxScore: 100,
    minScore: 60,
  },
  {
    key: '2',
    dimension: '科研经费',
    score: 78,
    participants: 115,
    maxScore: 95,
    minScore: 55,
  },
  {
    key: '3',
    dimension: '高水平论文',
    score: 82,
    participants: 125,
    maxScore: 98,
    minScore: 58,
  },
  {
    key: '4',
    dimension: '教学与科研获奖',
    score: 75,
    participants: 110,
    maxScore: 92,
    minScore: 52,
  },
  {
    key: '5',
    dimension: '国际交流',
    score: 70,
    participants: 105,
    maxScore: 90,
    minScore: 50,
  },
  {
    key: '6',
    dimension: '社会服务',
    score: 88,
    participants: 130,
    maxScore: 100,
    minScore: 65,
  },
  {
    key: '7',
    dimension: '就业质量',
    score: 92,
    participants: 135,
    maxScore: 100,
    minScore: 70,
  },
]

onMounted(() => {
  // 初始化饼图
  const pie = echarts.init(pieChart.value)
  pie.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: ['科研项目', '科研经费', '高水平论文', '教学与科研获奖', '国际交流', '社会服务', '就业质量']
    },
    series: [
      {
        name: '评分分布',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 85, name: '科研项目' },
          { value: 78, name: '科研经费' },
          { value: 82, name: '高水平论文' },
          { value: 75, name: '教学与科研获奖' },
          { value: 70, name: '国际交流' },
          { value: 88, name: '社会服务' },
          { value: 92, name: '就业质量' }
        ]
      }
    ]
  })

  // 初始化柱状图
  const bar = echarts.init(barChart.value)
  bar.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['平均分']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '平均分',
        type: 'bar',
        data: [82, 85, 88, 86, 90, 92]
      }
    ]
  })

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    pie.resize()
    bar.resize()
  })
})
</script>

<style scoped>
.performance-dashboard {
  padding: 24px;
}
</style> 