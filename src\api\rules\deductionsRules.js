import request from '../server'

// API 路径配置
const api = {
  list: '/deductionsRules/list',
  detail: '/deductionsRules/detail',
  create: '/deductionsRules/create',
  update: '/deductionsRules/update',
  delete: '/deductionsRules/delete',
  batchDelete: '/deductionsRules/batch-delete',
  import: '/deductionsRules/import',
  export: '/deductionsRules/export'
}

/**
 * 获取扣减规则列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getDeductionsRules(params) {
  const { page = 1, pageSize = 10 } = params || {};
  return request.get(api.list, { page, pageSize });
}

/**
 * 获取扣减规则详情
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function getDeductionRuleDetail(id) {
  return request.get(api.detail, { id });
}

/**
 * 创建扣减规则
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function addDeductionRule(data) {
  return request.post(api.create, data);
}

/**
 * 更新扣减规则
 * @param {string} id - 规则ID
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateDeductionRule(id, data) {
  return request.put(api.update, { id, ...data });
}

/**
 * 删除扣减规则
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteDeductionRule(id) {
  return request.delete(api.delete, { id });
}

/**
 * 批量删除扣分规则
 * @param {Array} ids - 规则ID数组
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteDeductionRules(ids) {
  return request.delete(api.batchDelete, { ids });
}

/**
 * 导入扣分规则数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回Promise对象
 */
export function importDeductionRules(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request.post(api.import, formData, null, 'multipart/form-data');
}

/**
 * 导出扣分规则数据
 * @param {Object} params - 过滤参数
 * @returns {Promise} - 返回Promise对象
 */
export function exportDeductionRules(params) {
  return request.get(api.export, params, { responseType: 'blob' });
} 