/**
 * 教学科技奖励路由
 * 遵循项目RESTful API设计规范和权限控制规范
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');

// 导入控制器
const teachingResearchAwardsController = require('../../../controllers/v1/teachingResearchAwards/teachingResearchAwardsController');
const teachingResearchAwardLevelsController = require('../../../controllers/v1/teachingResearchAwards/teachingResearchAwardLevelsController');

// 导入中间件
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建教学科技奖励权限中间件函数
const awardsPermission = (action) => createModulePermission('teachingResearchAwards', action);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/teaching_research_awards/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'teaching-research-awards-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

// ==================== 教学科技奖励主表路由 ====================

/**
 * 获取教学科技奖励列表
 * @route POST /v1/teaching-research-awards/list
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {string} awardName - 获奖名称（模糊搜索）
 * @param {string} awardLevelId - 奖励级别ID
 * @param {string} department - 系/教研室
 * @param {string} startDate - 获奖开始日期
 * @param {string} endDate - 获奖结束日期
 * @param {string} ifReviewer - 审核状态
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {items: [], total: 0, page: 1, pageSize: 10, totalPages: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list',
  authMiddleware,
  awardsPermission('list'),
  teachingResearchAwardsController.getAwards
);

/**
 * 获取教学科技奖励详情
 * @route POST /v1/teaching-research-awards/detail
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {string} id.body.required - 奖励ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/detail',
  authMiddleware,
  awardsPermission('detail'),
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await teachingResearchAwardsController.getAwardDetail(req, res);
  }
);

/**
 * 创建教学科技奖励
 * @route POST /v1/teaching-research-awards/create
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {string} awardName.body.required - 获奖名称
 * @param {string} awardTime.body.required - 获奖时间
 * @param {string} awardLevelId.body.required - 奖励级别ID
 * @param {string} firstResponsibleId.body.required - 第一负责人ID
 * @param {string} department.body - 系/教研室
 * @param {string} remark.body - 备注
 * @param {Array} participants.body.required - 参与者数组，包含participantId, employeeNumber, allocationRatio
 * @param {Array} files.body - 附件文件
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create',
  authMiddleware,
  awardsPermission('create'),
  upload.array('files', 5),
  teachingResearchAwardsController.createAward
);

/**
 * 更新教学科技奖励
 * @route POST /v1/teaching-research-awards/update/:id
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {string} id.path.required - 奖励ID
 * @param {string} awardName.body - 获奖名称
 * @param {string} awardTime.body - 获奖时间
 * @param {string} awardLevelId.body - 奖励级别ID
 * @param {string} firstResponsibleId.body - 第一负责人ID
 * @param {string} department.body - 系/教研室
 * @param {string} remark.body - 备注
 * @param {Array} participants.body - 参与者数组
 * @param {Array} files.body - 附件文件
 * @param {Array} deletedFileIds.body - 要删除的文件ID数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update/:id',
  authMiddleware,
  awardsPermission('update'),
  upload.array('files', 5),
  teachingResearchAwardsController.updateAward
);

/**
 * 删除教学科技奖励
 * @route DELETE /v1/teaching-research-awards/delete/:id
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {string} id.path.required - 奖励ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete/:id',
  authMiddleware,
  awardsPermission('delete'),
  teachingResearchAwardsController.deleteAward
);

/**
 * 审核教学科技奖励
 * @route POST /v1/teaching-research-awards/review
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {string} id.body.required - 奖励ID
 * @param {number} ifReviewer.body.required - 审核结果（1-通过，0-拒绝）
 * @param {string} reviewComment.body - 审核意见
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/review',
  authMiddleware,
  awardsPermission('review'),
  async (req, res) => {
    const { id, ...reviewData } = req.body;
    req.params = { id };
    req.body = reviewData;
    await teachingResearchAwardsController.reviewAward(req, res);
  }
);

/**
 * 重新提交审核
 * @route POST /v1/teaching-research-awards/reapply/:id
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {string} id.path.required - 奖励ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/reapply/:id',
  authMiddleware,
  awardsPermission('reapply'),
  teachingResearchAwardsController.reapplyAward
);

// ==================== 教学科技奖励级别路由 ====================

/**
 * 获取奖励级别列表（分页）
 * @route POST /v1/teaching-research-awards/levels/list
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} levelName - 级别名称（模糊搜索）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {items: [], total: 0, page: 1, pageSize: 10, totalPages: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/levels/list',
  authMiddleware,
  awardsPermission('levelsList'),
  teachingResearchAwardLevelsController.getTeachingResearchAwardLevels
);

/**
 * 获取所有启用的奖励级别（用于下拉选择）
 * @route GET /v1/teaching-research-awards/levels/all
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels/all',
  authMiddleware,
  awardsPermission('list'),
  teachingResearchAwardLevelsController.getAllTeachingResearchAwardLevels
);

/**
 * 获取奖励级别详情
 * @route GET /v1/teaching-research-awards/levels/:id
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels/:id',
  authMiddleware,
  awardsPermission('levelsDetail'),
  teachingResearchAwardLevelsController.getTeachingResearchAwardLevelDetail
);

/**
 * 创建奖励级别
 * @route POST /v1/teaching-research-awards/levels/create
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} levelName.body.required - 级别名称
 * @param {number} score.body.required - 基础分数
 * @param {string} description.body - 级别描述
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/levels/create',
  authMiddleware,
  awardsPermission('levelsCreate'),
  teachingResearchAwardLevelsController.createTeachingResearchAwardLevel
);

/**
 * 更新奖励级别
 * @route POST /v1/teaching-research-awards/levels/update
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.body.required - 级别ID
 * @param {string} levelName.body - 级别名称
 * @param {number} score.body - 基础分数
 * @param {string} description.body - 级别描述
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/levels/update',
  authMiddleware,
  awardsPermission('levelsUpdate'),
  async (req, res) => {
    const { id, ...updateData } = req.body;
    req.params = { id };
    req.body = updateData;
    await teachingResearchAwardLevelsController.updateTeachingResearchAwardLevel(req, res);
  }
);

/**
 * 删除奖励级别
 * @route POST /v1/teaching-research-awards/levels/delete
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.body.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/levels/delete',
  authMiddleware,
  awardsPermission('levelsDelete'),
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await teachingResearchAwardLevelsController.deleteTeachingResearchAwardLevel(req, res);
  }
);

// ==================== 导出功能路由 ====================

/**
 * 导出教学科技奖励数据
 * @route POST /v1/teaching-research-awards/export
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {string} awardName - 获奖名称（模糊搜索）
 * @param {string} awardLevelId - 奖励级别ID
 * @param {string} department - 系/教研室
 * @param {string} startDate - 获奖开始日期
 * @param {string} endDate - 获奖结束日期
 * @param {string} ifReviewer - 审核状态
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/export',
  authMiddleware,
  awardsPermission('export'),
  teachingResearchAwardsController.exportAwards
);

/**
 * 导入教学科技奖励数据
 * @route POST /v1/teaching-research-awards/import
 * @group 教学科技奖励管理 - 教学科技奖励相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import',
  authMiddleware,
  awardsPermission('import'),
  upload.single('file'),
  teachingResearchAwardsController.importAwards
);

// ==================== 统计分析路由 ====================

/**
 * 获取教学科技奖励统计数据
 * @route POST /v1/teaching-research-awards/statistics
 * @group 教学科技奖励统计 - 教学科技奖励统计相关接口
 * @param {string} range - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId - 用户ID，如果提供则只统计该用户的奖励
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics',
  authMiddleware,
  awardsPermission('statistics'),
  teachingResearchAwardsController.getAwardsStatistics
);

/**
 * 获取用户教学科技奖励汇总
 * @route POST /v1/teaching-research-awards/user/summary
 * @group 教学科技奖励统计 - 教学科技奖励统计相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range - 查询范围
 * @param {string} reviewStatus - 审核状态
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/summary',
  authMiddleware,
  awardsPermission('userSummary'),
  async (req, res) => {
    const { userId, ...params } = req.body;
    req.params = { userId };
    req.body = params;
    await teachingResearchAwardsController.getUserAwardsSummary(req, res);
  }
);

/**
 * 获取部门教学科技奖励排名
 * @route POST /v1/teaching-research-awards/department/ranking
 * @group 教学科技奖励统计 - 教学科技奖励统计相关接口
 * @param {string} range - 查询范围
 * @param {string} reviewStatus - 审核状态
 * @param {number} page - 页码
 * @param {number} pageSize - 每页条数
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/department/ranking',
  authMiddleware,
  awardsPermission('departmentRanking'),
  teachingResearchAwardsController.getDepartmentAwardsRanking
);

/**
 * 获取教师教学科研奖励排名
 * @route POST /v1/teaching-research-awards/teacher-ranking
 * @group 教学科技奖励统计 - 教学科技奖励统计相关接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @param {boolean} isExport.body - 是否导出，默认false
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/teacher-ranking',
  authMiddleware,
  awardsPermission('statistics'),
  teachingResearchAwardsController.getTeacherAwardRanking
);

/**
 * 获取教学科研奖励总分统计（按级别和总体）
 * @route POST /v1/teaching-research-awards/stats/total-score
 * @group 教学科技奖励统计 - 教学科技奖励统计相关接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {levelStats: [], totalStats: {}, timeInterval: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/total-score',
  authMiddleware,
  awardsPermission('statistics'),
  teachingResearchAwardsController.getTeachingResearchAwardsTotalScore
);

/**
 * 获取用户教学科研奖励详情列表及得分
 * @route POST /v1/teaching-research-awards/user-awards-detail
 * @group 教学科技奖励统计 - 用户详情接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @param {number} pageSize.body - 每页记录数，默认10
 * @param {number} pageNum.body - 当前页码，默认1
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalCount: 0, pageSize: 10, pageNum: 1, awards: [], stats: {}, timeInterval: {}, user: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user-awards-detail',
  authMiddleware,
  awardsPermission('statistics'),
  teachingResearchAwardsController.getUserAwardsDetail
);

/**
 * 获取审核状态分布统计
 * @route POST /v1/teaching-research-awards/stats/review-status
 * @group 教学科技奖励统计 - 图表统计接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {reviewed: 0, rejected: 0, pending: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/review-status',
  authMiddleware,
  awardsPermission('statistics'),
  teachingResearchAwardsController.getReviewStatusStats
);

/**
 * 获取奖励级别分布统计
 * @route POST /v1/teaching-research-awards/stats/level
 * @group 教学科技奖励统计 - 图表统计接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{levelName: "国家级", count: 0}, {levelName: "省部级", count: 0}, ...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/level',
  authMiddleware,
  awardsPermission('statistics'),
  teachingResearchAwardsController.getLevelStats
);

/**
 * 获取年度奖励分布统计
 * @route POST /v1/teaching-research-awards/stats/year
 * @group 教学科技奖励统计 - 图表统计接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{year: "2023", count: 0}, {year: "2022", count: 0}, ...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/year',
  authMiddleware,
  awardsPermission('statistics'),
  teachingResearchAwardsController.getYearStats
);

/**
 * 获取系/教研室分布统计
 * @route POST /v1/teaching-research-awards/stats/department
 * @group 教学科技奖励统计 - 图表统计接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{department: "计算机系", count: 0}, {department: "数学系", count: 0}, ...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/department',
  authMiddleware,
  awardsPermission('statistics'),
  teachingResearchAwardsController.getDepartmentStats
);

module.exports = router;
