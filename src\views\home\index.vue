<template>
  <section class="zy-container">
    <div class="img-box jnu-header">
      <img src="https://jnumed.jnu.edu.cn/_upload/tpl/02/6a/618/template618/images/logo.png" width="160" class="jnu-logo"/>
      <!-- <h1 class="jnu-title">暨南大学基础医学与公共卫生学院教师绩效评定与管理平台</h1> -->
    </div>
    <hr class="jnu-divider"/>
    <section class="one">
      <header class="major">
        <h1>暨南大学基础医学与公共卫生学院绩效评定管理系统</h1>
        <div class="sub-title">
          <a href="https://www.jnu.edu.cn" target="_blank">暨南大学</a> · 基础医学与公共卫生学院 · 教师绩效评定平台
        </div>
        <p>
          基于现代化信息技术构建的智能绩效评定平台，致力于为学院教师提供科学、公正、透明的绩效评价体系。
          系统涵盖科研项目、学术论文、专利成果、教学改革、学生指导等多个维度，
          通过数据驱动的方式全面评估教师的学术贡献与教学成效。
        </p>
        <div class="system-features">
          <div class="feature-grid">
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <div class="feature-text">
                <strong>多维度评价</strong><br>
                科研、教学、服务全覆盖
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔍</div>
              <div class="feature-text">
                <strong>数据可视化</strong><br>
                直观展示绩效趋势分析
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">⚡</div>
              <div class="feature-text">
                <strong>智能化管理</strong><br>
                自动计算评分排名
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🛡️</div>
              <div class="feature-text">
                <strong>权限管控</strong><br>
                分级审核确保公正性
              </div>
            </div>
          </div>
        </div>
      </header>
    </section>
    
    <!-- 最新通知展示区域 -->
    <section class="overview">
      <ZySectionHeader title="最新通知" titleNum="01"/>
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card class="recent-notifications-card">
        <template #title>
          <div class="card-title">
            <BellOutlined style="font-size: 18px; margin-right: 8px; color: #1890ff;" />
            <span>系统通知</span>
          </div>
        </template>
        <!-- <template #extra>
          <a-button type="link" size="small" @click="viewAllNotifications">
            查看全部
            <RightOutlined />
          </a-button>
        </template> -->
        <a-list
          :data-source="recentNotifications"
          :loading="notificationsLoading"
          size="default"
          :pagination="false"
        >
          <template #renderItem="{ item }">
            <a-list-item
              @click="handleNotificationClick(item)"
              style="cursor: pointer;"
            >
              <a-list-item-meta>
                <template #avatar>
                  <component :is="getNotificationIcon(item.type)"
                             :style="{ color: getNotificationColor(item.type), fontSize: '18px' }" />
                </template>
                <template #title>
                  <div class="notification-title">
                    <span :class="{ 'unread-title': !item.isRead }">{{ item.title || '无标题' }}</span>
                    <div class="notification-tags">
                      <a-tag v-if="item.type" size="small" :color="getNotificationColor(item.type)">
                        {{ getTypeText(item.type) }}
                      </a-tag>
                      <a-tag v-if="item.sendMode" size="small" :color="getSendModeColor(item.sendMode)">
                        {{ getSendModeText(item.sendMode) }}
                      </a-tag>
                    </div>
                  </div>
                </template>
                <template #description>
                  <div class="notification-content">
                    <div class="content-text">{{ item.abstract || item.content || '暂无内容' }}</div>
                    <div class="notification-meta">
                      <span class="notification-time">{{ formatNotificationTime(item.createdAt) }}</span>
                      <span v-if="item.initiator" class="notification-author">
                        发布者: {{ item.initiator.nickname || item.initiator.username }}
                      </span>
                    </div>
                  </div>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
          <template #loadMore>
            <div v-if="recentNotifications.length === 0 && !notificationsLoading" class="empty-notifications">
              <a-empty description="暂无通知消息" />
            </div>
          </template>
        </a-list>
          </a-card>
        </a-col>
      </a-row>
    </section>

    <!-- 添加系统概览数据展示 -->
    <section class="overview" v-if="overviewData">
      <ZySectionHeader title="系统概览" titleNum="02"/>
      <a-row :gutter="16" v-permission="'score:teachingReformProjects:admin'">
        <a-col :span="12">
          <a-card>
            <a-statistic
              title="教师总数"
              :value="userStats.teachers || 0"
              :value-style="{ color: '#3f8600' }"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card>
            <a-statistic
              title="总条目数"
              :value="userStats.totalItems || 0"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <ProfileOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
      
      <!-- 替换为饼图 -->
      <a-row class="mt-16">
        <a-col :span="24">
          <a-card title="各类条目统计分布" :bordered="false">
            <div ref="overviewPieChartRef" style="height: 400px;"></div>
          </a-card>
        </a-col>
      </a-row>
    </section>
    
    <!-- 添加绩效排名和通知展示区域 -->
    <section class="performance-overview" v-if="overviewData">
      <ZySectionHeader title="绩效排名" titleNum="03"/>
      <a-row :gutter="[16, 16]">
        <!-- 绩效排名 -->
        <a-col :span="24">
          <a-card title="绩效排名前茅" :bordered="false">
            <!-- 统一筛选条件 -->
            <div class="filter-container">
              <a-space>
                <a-select v-model:value="rankFilters.range" style="width: 120px" @change="handleRankFiltersChange">
                  <a-select-option value="all">全部</a-select-option>
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                </a-select>
                <!-- 移除reviewStatus下拉框 -->
                <a-button type="primary" @click="exportRankingData" :loading="exportLoading" v-permission="'score:teachingReformProjects:admin'">
                  <template #icon><DownloadOutlined /></template>
                  导出数据
                </a-button>
              </a-space>
            </div>
            <!-- 添加提示信息 -->
            <div style="margin-top: 8px; margin-bottom: 16px;">
              <a-alert 
                type="info" 
                show-icon
                message="提示：绩效排名仅显示已审核通过的数据，拒绝或待审核的数据不会被计算在内。"
              />
            </div>
            
            <a-table
              :columns="topPerformerColumns"
              :data-source="topPerformers"
              :pagination="false"
              size="middle"
              :loading="rankingLoading"
              :scroll="{ x: 100 }"
              bordered
              rowKey="userId"
              class="performance-table no-hover-table ranking-table"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'rank'">
                  <a-tag :color="getRankColor(record.index)">第 {{ record.index }} 名</a-tag>
                </template>
                <template v-if="column.key === 'score'">
                  <div style="width: 100%;">
                    <div style="font-weight: bold; font-size: 16px; margin-bottom: 6px;">
                      {{ record.total_score }} 分
                    </div>
                    <div class="score-bars">
                      <div v-if="record.papers_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">论文</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.papers_score, record.total_score), backgroundColor: '#722ed1'}"></div>
                          <span class="score-bar-text">{{ record.papers_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.patents_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">专利</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.patents_score, record.total_score), backgroundColor: '#eb2f96'}"></div>
                          <span class="score-bar-text">{{ record.patents_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.textbooks_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">教材与著作</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.textbooks_score, record.total_score), backgroundColor: '#a0d911'}"></div>
                          <span class="score-bar-text">{{ record.textbooks_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.teaching_projects_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">教学改革</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.teaching_projects_score, record.total_score), backgroundColor: '#13c2c2'}"></div>
                          <span class="score-bar-text">{{ record.teaching_projects_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.awards_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">学生获奖</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.awards_score, record.total_score), backgroundColor: '#faad14'}"></div>
                          <span class="score-bar-text">{{ record.awards_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.student_projects_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">学生立项</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.student_projects_score, record.total_score), backgroundColor: '#eb2f96'}"></div>
                          <span class="score-bar-text">{{ record.student_projects_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.conferences_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">会议</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.conferences_score, record.total_score), backgroundColor: '#faad14'}"></div>
                          <span class="score-bar-text">{{ record.conferences_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.appointments_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">学术任职</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.appointments_score, record.total_score), backgroundColor: '#1890ff'}"></div>
                          <span class="score-bar-text">{{ record.appointments_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.research_projects_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">科研项目</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.research_projects_score, record.total_score), backgroundColor: '#1890ff'}"></div>
                          <span class="score-bar-text">{{ record.research_projects_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.teaching_workloads_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">教学工作量</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.teaching_workloads_score, record.total_score), backgroundColor: '#1890ff'}"></div>
                          <span class="score-bar-text">{{ record.teaching_workloads_score }}</span>
                        </div>
                      </div>
                      <div v-if="record.teaching_research_awards_score > 0" class="score-bar-wrapper">
                        <div class="score-bar-label">教学科研奖励</div>
                        <div class="score-bar-container">
                          <div class="score-bar" :style="{width: getItemScoreWidth(record.teaching_research_awards_score, record.total_score), backgroundColor: '#1890ff'}"></div>
                          <span class="score-bar-text">{{ record.teaching_research_awards_score }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-if="column.key === 'detail'">
                  <a-tooltip>
                    <template #title>
                      <div>论文({{ record.papers_count || 0 }}篇): {{ record.papers_score || 0 }}分</div>
                      <div>专利({{ record.patents_count || 0 }}项): {{ record.patents_score || 0 }}分</div>
                      <div>教材与著作({{ record.textbooks_count || 0 }}本): {{ record.textbooks_score || 0 }}分</div>
                      <div>教学改革({{ record.teaching_projects_count || 0 }}个): {{ record.teaching_projects_score || 0 }}分</div>
                      <div>学生获奖({{ record.awards_count || 0 }}项): {{ record.awards_score || 0 }}分</div>
                      <div>学生立项({{ record.student_projects_count || 0 }}个): {{ record.student_projects_score || 0 }}分</div>
                      <div>会议({{ record.conferences_count || 0 }}次): {{ record.conferences_score || 0 }}分</div>
                      <div>学术任职({{ record.appointments_count || 0 }}个): {{ record.appointments_score || 0 }}分</div>
                      <div>科研项目({{ record.research_projects_count || 0 }}个): {{ record.research_projects_score || 0 }}分</div>
                      <div style="margin-top: 5px; font-weight: bold;">总计: {{ record.total_count || 0 }}项 {{ record.total_score || 0 }}分</div>
                    </template>
                    <InfoCircleOutlined style="cursor: pointer" />
                  </a-tooltip>
                </template>
              </template>
            </a-table>
            <!-- 添加自定义分页组件 -->
            <div class="custom-pagination-wrapper" v-permission="'score:teachingReformProjects:admin'">
              <a-pagination
                v-model:current="currentPage"
                :total="rankingTotal"
                :pageSize="pageSize"
                :showTotal="(total) => `共 ${total} 条数据`"
                @change="handlePageChange"
                @showSizeChange="handleSizeChange"
                size="default"
                :showSizeChanger="true"
                :pageSizeOptions="['10', '20', '50', '100']"
                :showQuickJumper="true"
                :hideOnSinglePage="rankingTotal <= pageSize"
                :responsive="true"
                position="['bottomCenter']"
              />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 最近通知 - 单独一行 -->
      <!-- <div class="section-spacing"></div>
      <ZySectionHeader title="最近通知" titleNum="02" />
      <a-row :gutter="[16, 16]">
        <a-col :span="24">
          <a-card title="通知公告" :bordered="false">
            <a-tabs v-model:activeKey="activeNoticeTab">
              <a-tab-pane key="all" tab="全部">
                <a-list
                  :data-source="overviewData.recent_activities"
                  size="small"
                  :pagination="false"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <component :is="getNoticeIcon(item.type)" :style="{ color: getNoticeColor(item.type) }" />
                        </template>
                        <template #title>
                          <span :style="{ color: getNoticeColor(item.type) }">{{ item.title }}</span>
                        </template>
                        <template #description>
                          <div>{{ item.content }}</div>
                          <div class="notice-time">{{ formatDateTime(item.created_at) }}</div>
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </a-tab-pane>
              <a-tab-pane key="research" tab="科研">
                <a-list
                  :data-source="filterNotices('research_project')"
                  size="small"
                  :pagination="false"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <FundProjectionScreenOutlined :style="{ color: '#1890ff' }" />
                        </template>
                        <template #title>
                          <span style="color: #1890ff">{{ item.title }}</span>
                        </template>
                        <template #description>
                          <div>{{ item.content }}</div>
                          <div class="notice-time">{{ formatDateTime(item.created_at) }}</div>
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </a-tab-pane>
              <a-tab-pane key="paper" tab="论文">
                <a-list
                  :data-source="filterNotices('paper')"
                  size="small"
                  :pagination="false"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <FileTextOutlined :style="{ color: '#722ed1' }" />
                        </template>
                        <template #title>
                          <span style="color: #722ed1">{{ item.title }}</span>
                        </template>
                        <template #description>
                          <div>{{ item.content }}</div>
                          <div class="notice-time">{{ formatDateTime(item.created_at) }}</div>
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </a-tab-pane>
              <a-tab-pane key="award" tab="获奖">
                <a-list
                  :data-source="filterNotices('award')"
                  size="small"
                  :pagination="false"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <TrophyOutlined :style="{ color: '#faad14' }" />
                        </template>
                        <template #title>
                          <span style="color: #faad14">{{ item.title }}</span>
                        </template>
                        <template #description>
                          <div>{{ item.content }}</div>
                          <div class="notice-time">{{ formatDateTime(item.created_at) }}</div>
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </a-col>
      </a-row> -->
    </section>
    
    <!-- 删除系统模块部分，改为我的评分模块 -->
    <section class="my-score">
      <ZySectionHeader title="我的评分" titleNum="04">
        <template #extra>
          <a-button
            @click="toggleMyChartsDisplay"
            type="dashed"
            size="small"
          >
            <template #icon>
              <DownOutlined v-if="!showAllMyCharts" />
              <UpOutlined v-else />
            </template>
            {{ showAllMyCharts ? '收起图表' : '展开图表' }}
          </a-button>
        </template>
      </ZySectionHeader>
      <a-row :gutter="[16, 16]">
        <!-- 个人评分卡片 -->
        <a-col :span="8">
          <a-card class="score-overview-card" :loading="myScoreLoading">
            <template #title>
              <div class="card-title">
                <TrophyOutlined style="font-size: 20px; margin-right: 8px; color: #faad14;" />
                <span>评分概览</span>
              </div>
            </template>
            <div class="total-score">
              <div class="score-number">{{ formatScore(myScoreData?.basic_info?.total_score) }}</div>
              <div class="score-text">本次评分</div>
            </div>
            <a-divider />
            <div class="score-rank">
              <div class="rank-item">
                <div class="rank-label">本次统计排名</div>
                <div class="rank-value">
                  <a-tag color="#f50">第 {{ myScoreData?.rankings?.in_range_rank || '-' }} 名</a-tag>
                </div>
              </div>
              <div class="rank-item">
                <div class="rank-label">历史总排名</div>
                <div class="rank-value">
                  <a-tag color="#2db7f5">第 {{ myScoreData?.rankings?.out_range_rank || '-' }} 名</a-tag>
                </div>
              </div>
              <div class="rank-item">
                <div class="rank-label">总排名</div>
                <div class="rank-value">第 {{ myScoreData?.rankings?.total_rank || '-' }} 名</div>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <!-- 各维度评分 -->
        <a-col :span="16">
          <a-card title="各维度评分" class="dimension-score-card" :loading="myScoreLoading">
            <a-row :gutter="[16, 16]">
              <a-col :span="8" v-for="(item, index) in myScoreData?.dimension_scores" :key="index">
                <div class="dimension-item">
                  <div class="dimension-icon">
                    <component :is="getIconComponent(item.icon)" :style="{color: item.color, fontSize: '24px'}" />
                  </div>
                  <div class="dimension-info">
                    <div class="dimension-name">{{ item.name }}</div>
                    <div class="dimension-score">{{ formatScore(item.score) }}分</div>
                    <div class="dimension-count">{{ item.count || 0 }}项</div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
        
        <!-- 评分趋势和提升建议面板 - 可折叠 -->
        <a-col :span="12" v-show="showAllMyCharts">
          <a-card title="个人评分趋势" class="trend-card">
            <div ref="personalScoreTrendRef" style="height: 300px;"></div>
          </a-card>
        </a-col>

        <!-- 提升建议 - 可折叠 -->
        <a-col :span="12" v-show="showAllMyCharts">
          <a-card title="提升建议" class="suggestion-card">
            <a-list size="small">
              <a-list-item>
                <div class="suggestion-item">
                  <a-tag color="#1890ff">科研项目</a-tag>
                  <span>建议申报国家级研究项目，提升此项得分</span>
                </div>
              </a-list-item>
              <a-list-item>
                <div class="suggestion-item">
                  <a-tag color="#722ed1">论文发表</a-tag>
                  <span>建议在SCI一区期刊发表论文，提高论文质量</span>
                </div>
              </a-list-item>
              <a-list-item>
                <div class="suggestion-item">
                  <a-tag color="#13c2c2">国际交流</a-tag>
                  <span>参与国际学术会议或访问学者项目</span>
                </div>
              </a-list-item>
              <a-list-item>
                <div class="suggestion-item">
                  <a-tag color="#fa8c16">社会服务</a-tag>
                  <span>积极参与社会服务项目，提高影响力</span>
                </div>
              </a-list-item>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </section>

    <!-- 添加图表展示区域 -->
    <section class="charts" v-if="chartsData">
      <ZySectionHeader title="数据分析" titleNum="05"/>
      <!-- 统一筛选条件 -->
      <div class="filter-container">
        <a-row :gutter="16" style="margin-bottom: 16px;">
          <a-col :span="12">
            <a-card :bordered="false">
              <a-space>
                <a-select v-model:value="statsFilters.range" style="width: 120px" @change="handleFiltersChange">
                  <a-select-option value="all">全部</a-select-option>
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                </a-select>
                <!-- 移除reviewStatus下拉框 -->
              </a-space>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card :bordered="false">
              <div style="text-align: right;">
                <a-button
                  @click="toggleChartsDisplay"
                  type="dashed"
                  size="small"
                >
                  <template #icon>
                    <DownOutlined v-if="!showAllCharts" />
                    <UpOutlined v-else />
                  </template>
                  {{ showAllCharts ? '收起图表' : '展开图表' }}
                </a-button>
              </div>
            </a-card>
          </a-col>
        </a-row>
        <!-- 添加提示信息 -->
        <div style="margin-top: -8px; margin-bottom: 16px;">
          <a-alert
            type="info"
            show-icon
            message="提示：数据分析仅显示已审核通过的数据，拒绝或待审核的数据不会被计算在内。"
          />
        </div>
      </div>

      <!-- 所有统计图表直接展示，不使用标签页 -->
      <a-row :gutter="[24, 24]">
        <!-- 科研项目统计 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12">
          <StatsCard
            ref="projectsCard"
            title="科研项目总分统计"
            countLabel="项目总数"
            :fetchApi="getProjectsTotalScore"
            :columns="projectTypeColumns"
            rowKey="type"
            totalCountKey="totalProjects"
            :getStatsFromResponse="response => response.data.typeStats || []"
            @data-loaded="(data) => updateModuleData('projects', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>

        <!-- 高水平论文统计 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12">
          <StatsCard
            ref="papersCard"
            title="高水平论文总分统计"
            countLabel="论文总数"
            :fetchApi="getHighLevelPapersTotalScore"
            :columns="papersLevelColumns"
            rowKey="levelId"
            totalCountKey="totalPapers"
            @data-loaded="(data) => updateModuleData('papers', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>

        <!-- 专利总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="patentsCard"
            title="专利总分统计"
            countLabel="专利总数"
            :fetchApi="getPatentsTotalScore"
            :columns="patentsTypeColumns"
            rowKey="typeId"
            totalCountKey="totalPatents"
            :getStatsFromResponse="response => response.data.typeStats || []"
            @data-loaded="(data) => updateModuleData('patents', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>

        <!-- 教材与著作总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="textbooksCard"
            title="教材与著作总分统计"
            countLabel="教材总数"
            :fetchApi="getTextbooksTotalScore"
            :columns="textbooksCategoryColumns"
            rowKey="categoryId"
            totalCountKey="totalTextbooks"
            :getStatsFromResponse="response => response.data.categoryStats || []"
            @data-loaded="(data) => updateModuleData('textbooks', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>

        <!-- 教学改革项目总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="teachingProjectsCard"
            title="教学改革项目总分统计"
            countLabel="项目总数"
            :fetchApi="getTeachingReformProjectTotalScore"
            :columns="teachingReformProjectsLevelColumns"
            rowKey="levelId"
            totalCountKey="totalProjects"
            @data-loaded="(data) => updateModuleData('teachingProjects', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>

        <!-- 学生获奖指导奖项总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="awardsCard"
            title="学生获奖指导奖项总分统计"
            countLabel="奖项总数"
            :fetchApi="getAwardTotalScore"
            :columns="awardsLevelColumns"
            rowKey="levelId"
            totalCountKey="totalAwards"
            @data-loaded="(data) => updateModuleData('awards', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>

        <!-- 学生项目指导总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="studentProjectsCard"
            title="学生项目指导总分统计"
            countLabel="项目总数"
            :fetchApi="getStudentProjectTotalScore"
            :columns="studentProjectLevelColumns"
            rowKey="levelId"
            totalCountKey="totalProjects"
            @data-loaded="(data) => updateModuleData('studentProjects', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>

        <!-- 会议总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="conferencesCard"
            title="会议总分统计"
            countLabel="会议总数"
            :fetchApi="getConferencesTotalScore"
            :columns="conferencesLevelColumns"
            rowKey="levelId"
            totalCountKey="totalConferences"
            @data-loaded="(data) => updateModuleData('conferences', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>

        <!-- 学术任职总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="appointmentsCard"
            title="学术任职总分统计"
            countLabel="任职总数"
            :fetchApi="getAcademicAppointmentsTotalScore"
            :columns="appointmentsLevelColumns"
            rowKey="levelId"
            totalCountKey="totalAppointments"
            @data-loaded="(data) => updateModuleData('appointments', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>
        
        <!-- 教学工作量总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="teachingWorkloadsCard"
            title="教学工作量总分统计"
            countLabel="工作量总数"
            :fetchApi="getTeachingWorkloadsTotalScore"
            :columns="teachingWorkloadsLevelColumns"
            rowKey="levelId"
            totalCountKey="totalWorkloads"
            @data-loaded="(data) => updateModuleData('teachingWorkloads', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>
        
        <!-- 教学科研奖励总分统计 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12" v-show="showAllCharts">
          <StatsCard
            ref="teachingResearchAwardsCard"
            title="教学科研奖励总分统计"
            countLabel="奖励总数"
            :fetchApi="getTeachingResearchAwardsTotalScore"
            :columns="teachingResearchAwardsLevelColumns"
            rowKey="levelId"
            totalCountKey="totalAwards"
            @data-loaded="(data) => updateModuleData('teachingResearchAwards', data)"
            :customParams="statsFilters"
            :shouldAutoLoad="false"
          />
        </a-col>
      </a-row>
    </section>

    <!-- 添加绩效分析模块 -->
    <section class="performance-analysis" v-if="showPerformanceAnalysis">
      <ZySectionHeader title="绩效分析" titleNum="06"/>

      <!-- 分析控制面板 -->
      <div class="analysis-controls mb-24">
        <a-card :bordered="false">
          <a-row :gutter="24" align="middle">
            <a-col :span="5">
              <a-form-item label="统计范围" style="margin-bottom: 0;">
                <a-select
                  v-model:value="analysisFilters.range"
                  style="width: 100%"
                  @change="handleAnalysisFiltersChange"
                >
                  <a-select-option value="all">全部数据</a-select-option>
                  <a-select-option value="in">统计范围内</a-select-option>
                  <a-select-option value="out">统计范围外</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="9" v-permission="'score:teachingReformProjects:admin'">
              <a-form-item label="分析用户" style="margin-bottom: 0;">
                <a-select
                  v-model:value="analysisFilters.userId"
                  style="width: 100%"
                  placeholder="搜索并选择用户（输入姓名或工号）"
                  show-search
                  :filter-option="false"
                  :loading="userSearchLoading"
                  @search="handleUserSearch"
                  @change="handleUserChange"
                  allow-clear
                  option-label-prop="label"
                >
                  <a-select-option
                    v-for="user in currentUserOptions"
                    :key="user.userId"
                    :value="user.userId"
                    :label="user.label"
                  >
                    {{ user.nickname }} ({{ user.studentNumber }})
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <div class="analysis-actions">
                <a-space wrap>
                  <a-button
                    type="primary"
                    @click="refreshAnalysisData"
                    :loading="analysisLoading"
                    size="small"
                  >
                    <template #icon><ReloadOutlined /></template>
                    刷新数据
                  </a-button>
                  <!-- <a-button
                    @click="exportAnalysisReport"
                    :loading="exportAnalysisLoading"
                    size="small"
                  >
                    <template #icon><DownloadOutlined /></template>
                    导出报告
                  </a-button>
                  <a-button
                    @click="showDepartmentComparison"
                    size="small"
                  >
                    <template #icon><BarChartOutlined /></template>
                    部门对比
                  </a-button> -->
                </a-space>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </div>

      <!-- 绩效分析图表区域 -->
      <a-row :gutter="[24, 24]">
        <!-- 雷达图 -->
        <!-- <a-col :xs="24" :sm="24" :md="12" :lg="12">
          <a-card
            title="综合绩效雷达图"
            class="performance-card chart-container"
            :loading="analysisLoading"
          >
            <template #extra>
              <a-tooltip title="显示用户在九个维度的绩效表现">
                <InfoCircleOutlined style="color: #999;" />
              </a-tooltip>
            </template>
            <div
              ref="radarChartRef"
              class="chart-wrapper echarts-container radar-chart-container"
              v-show="!analysisLoading && radarChartData"
              style="width: 100%; height: 400px;"
            ></div>
            <div v-if="analysisLoading" class="chart-loading">
              <a-spin size="large" />
              <div style="margin-top: 16px;">加载雷达图数据...</div>
            </div>
            <div v-if="!analysisLoading && !radarChartData" class="chart-empty">
              <FileSearchOutlined class="empty-icon" />
              <div class="empty-message">暂无雷达图数据</div>
            </div>
            <div v-if="!analysisLoading && radarChartData" class="chart-info">
              用户: {{ radarChartData.userInfo?.nickname }} | 总分: {{ radarChartData.summary?.totalScore }} | 排名: 第{{ radarChartData.summary?.rank }}名
            </div>
          </a-card>
        </a-col> -->

        <!-- 分布图 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12">
          <a-card
            title="绩效分布分析"
            class="performance-card chart-container"
            :loading="analysisLoading"
          >
            <template #extra>
              <a-tooltip title="显示用户在各分数段的分布情况">
                <InfoCircleOutlined style="color: #999;" />
              </a-tooltip>
            </template>
            <div
              ref="distributionChartRef"
              class="chart-wrapper echarts-container distribution-chart-container"
              v-show="!analysisLoading && distributionChartData"
              style="width: 100%; height: 400px;"
            ></div>
            <div v-if="analysisLoading" class="chart-loading">
              <a-spin size="large" />
              <div style="margin-top: 16px;">加载分布图数据...</div>
            </div>
            <div v-if="!analysisLoading && !distributionChartData" class="chart-empty">
              <PieChartOutlined class="empty-icon" />
              <div class="empty-message">暂无分布图数据</div>
            </div>
            <div v-if="!analysisLoading && distributionChartData" class="chart-info">
              全校排名: 第{{ distributionChartData.userInfo?.globalRank }}名 | 部门排名: 第{{ distributionChartData.userInfo?.departmentRank }}名
            </div>
          </a-card>
        </a-col>

        <!-- 趋势对比图 - 可折叠 -->
        <!-- <a-col :xs="24" :sm="24" :md="12" :lg="12">
          <a-card
            title="排名趋势对比"
            class="performance-card chart-container"
            :loading="analysisLoading"
          >
            <template #extra>
              <a-tooltip title="显示用户在不同统计范围的排名趋势">
                <InfoCircleOutlined style="color: #999;" />
              </a-tooltip>
            </template>
            <div
              ref="trendChartRef"
              class="chart-wrapper echarts-container trend-chart-container"
              v-show="!analysisLoading && trendChartData"
              style="width: 100%; height: 400px;"
            ></div>
            <div v-if="analysisLoading" class="chart-loading">
              <a-spin size="large" />
              <div style="margin-top: 16px;">加载趋势图数据...</div>
            </div>
            <div v-if="!analysisLoading && !trendChartData" class="chart-empty">
              <LineChartOutlined class="empty-icon" />
              <div class="empty-message">暂无趋势图数据</div>
            </div>
            <div v-if="!analysisLoading && trendChartData" class="chart-info">
              最佳表现: {{ getRangeLabel(trendChartData.analysis?.bestRange?.range) }} ({{ trendChartData.analysis?.bestRange?.score }}分)
            </div>
          </a-card>
        </a-col> -->

        <!-- 分析报告 - 可折叠 -->
        <a-col :xs="24" :sm="24" :md="12" :lg="12">
          <a-card
            title="绩效分析报告"
            class="performance-card"
            :loading="analysisLoading"
          >
            <template #extra>
              <a-tooltip title="基于数据生成的智能分析报告">
                <InfoCircleOutlined style="color: #999;" />
              </a-tooltip>
            </template>
            <div v-if="!analysisLoading && analysisReport" class="analysis-report">
              <!-- 基本信息 -->
              <div class="report-section">
                <h4 class="section-title">基本信息</h4>
                <a-descriptions :column="2" size="small">
                  <a-descriptions-item label="总分">
                    <span class="text-primary">{{ analysisReport.summary?.totalScore || 0 }}分</span>
                  </a-descriptions-item>
                  <a-descriptions-item label="排名">
                    <a-tag color="blue">第{{ analysisReport.summary?.rank || 0 }}名</a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="绩效等级">
                    <a-tag :color="getPerformanceLevelColor(analysisReport.summary?.level)">
                      {{ analysisReport.summary?.level || '未评级' }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="统计范围">
                    {{ getRangeLabel(analysisReport.range) }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>

              <!-- 强项分析 -->
              <div class="report-section" v-if="analysisReport.strengths?.length">
                <h4 class="section-title">优势维度</h4>
                <a-list size="small" :data-source="analysisReport.strengths">
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #title>
                          <span>{{ item.dimension }}</span>
                          <a-tag color="green" style="margin-left: 8px;">{{ item.level }}</a-tag>
                        </template>
                        <template #description>
                          得分：{{ item.score }}分
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </div>

              <!-- 改进建议 -->
              <div class="report-section" v-if="analysisReport.suggestions?.length">
                <h4 class="section-title">改进建议</h4>
                <a-list size="small" :data-source="analysisReport.suggestions">
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <BulbOutlined style="color: #faad14;" />
                        </template>
                        <template #title>
                          <a-tag :color="item.type === 'improvement' ? 'orange' : 'blue'">
                            {{ item.type === 'improvement' ? '能力提升' : '策略调整' }}
                          </a-tag>
                        </template>
                        <template #description>
                          {{ item.suggestion }}
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
            <div v-if="!analysisLoading && !analysisReport" class="chart-empty">
              <FileTextOutlined class="empty-icon" />
              <div class="empty-message">暂无分析报告</div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </section>

    <!-- 绩效评定填写说明区域 -->
    <section class="score-calculation">
      <ZySectionHeader title="绩效评定填写说明" titleNum="07"/>
      <a-alert
        class="mt-16 mb-16"
        message="绩效评定统计时间范围"
        :description="`统计时间：${performanceTimeRange || '2017年8月1日至2023年7月31日'}`"
        type="info"
        show-icon
      />
      <a-row :gutter="[16, 16]">
        <!-- 一、科研项目 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <ExperimentOutlined style="color: #1890ff; font-size: 20px; margin-right: 8px;" />
                <span>一、科研项目</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>科研项目获批年在统计时间范围内</li>
                <li>项目以批文为准</li>
                <li>此表由主持人为我院的教职工填写，各承担人比例由主持人分配</li>
                <li>此表中出现的姓名全部为我院教职工</li>
                <li>分配比例请填写小数，不要填写百分数</li>
                <li>所有承担人的总分配比例加总应为1</li>
                <li>本表仅做工作量统计，绩效计算等文件制定后根据统计结果进行计算</li>
              </ol>
            </div>
          </a-card>
        </a-col>

        <!-- 二、论文 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <FileTextOutlined style="color: #722ed1; font-size: 20px; margin-right: 8px;" />
                <span>二、论文</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>出版时间范围在统计时间内，以图书馆检索证明为准</li>
                <li>收录情况必须出具图书馆的检索证明和论文首页</li>
                <li>参与分配者按照贡献多少，由多到少依次填写</li>
                <li>"分配比例基数"和"分配比例"填写小数，不要填写百分数</li>
                <li>中文论文必须属于北图核心目录</li>
                <li>该表由通讯作者填写，如第一作者单位我院而通讯作者不是，由第一作者填写</li>
                <li>出现在本表中的人必须为我院教职工</li>
                <li>分配比例基数根据通讯作者情况计算</li>
                <li>所有作者分配比例"总分配比例"应为1</li>
              </ol>
            </div>
          </a-card>
        </a-col>

        <!-- 三、专利 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <BulbOutlined style="color: #faad14; font-size: 20px; margin-right: 8px;" />
                <span>三、专利</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>专利授权时间和转化时间范围在统计时间内</li>
                <li>本表中出现的所有人名均为我院教职工</li>
                <li>审核需要提供专利授权证明和转化证明</li>
                <li>本表由第一负责人填写</li>
                <li>总分配比例应为1</li>
                <li>"分配比例"填写小数，不要填写百分数</li>
              </ol>
            </div>
          </a-card>
        </a-col>

        <!-- 四、学术会议 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <TeamOutlined style="color: #52c41a; font-size: 20px; margin-right: 8px;" />
                <span>四、学术会议</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>承办学术会议指中华医学会或各专业学会主办的学术会议</li>
                <li>时间范围在统计时间内</li>
                <li>本表由学术会议的第一负责人填写</li>
                <li>表中的所有姓名均为我院教职工</li>
                <li>证明材料为有科技处盖章的申报表、海报和证明会议级别的材料</li>
                <li>总分配比例应为1</li>
                <li>分配比例请填写小数，不要填写百分数</li>
              </ol>
            </div>
          </a-card>
        </a-col>

        <!-- 五、学术任职 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <CrownOutlined style="color: #fa8c16; font-size: 20px; margin-right: 8px;" />
                <span>五、学术任职</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>学术协会仅按照国家级和省自治区的医学会和医学各专业的学会计算</li>
                <li>理事长和副理事长相对应的主任委员和副主任委员、会长和副会长按相同标准对待</li>
                <li>学会任职以最高级别仅计算一项</li>
                <li>按任职年度计算</li>
                <li>需提供带有聘任日期和聘任期限的聘书复印件供审核</li>
                <li>任职年限区间为统计时间范围</li>
              </ol>
            </div>
          </a-card>
        </a-col>

        <!-- 六、教材著作 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <BookOutlined style="color: #13c2c2; font-size: 20px; margin-right: 8px;" />
                <span>六、教材著作</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>出版时间范围在统计时间内</li>
                <li>审核需要提供教材和专著的封面、扉页等证明材料</li>
                <li>丛书主编不重复计分</li>
                <li>学术著作与本人岗位所从事的专业对口的本专业著作才能计分</li>
                <li>"类别及任职"已内置下拉菜单，选择即可</li>
              </ol>
            </div>
          </a-card>
        </a-col>

        <!-- 七、教学改革项目 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <EditOutlined style="color: #eb2f96; font-size: 20px; margin-right: 8px;" />
                <span>七、教学改革项目</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>教改项目的获批时间需在统计时间内</li>
                <li>项目以批件为准</li>
                <li>此表由主持人为我院的教职工填写，各承担人比例由主持人分配</li>
                <li>此表中出现的姓名全部为我院教职工</li>
                <li>分配比例请填写小数，不要填写百分数</li>
                <li>"级别"、"执行年限区间"已设置下拉菜单</li>
                <li>总分配比例应为1</li>
              </ol>
            </div>
          </a-card>
        </a-col>

        <!-- 八、学生立项 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <ProjectOutlined style="color: #2f54eb; font-size: 20px; margin-right: 8px;" />
                <span>八、学生立项</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>双创类项目的获批时间需在统计时间内</li>
                <li>执行年（非获批年）范围在统计年度间</li>
                <li>项目计分按照在研年度计分，以批件、计划书和合同书为准</li>
                <li>此表由主要指导老师为我院的教职工填写</li>
                <li>此表中出现的姓名全部为我院教职工</li>
                <li>分配比例请填写小数，不要填写百分数</li>
                <li>总分配比例应为1</li>
              </ol>
            </div>
          </a-card>
        </a-col>

        <!-- 九、学生获奖 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <TrophyOutlined style="color: #f5222d; font-size: 20px; margin-right: 8px;" />
                <span>九、学生获奖</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>双创类项目（大创、挑战杯）获奖和研究生国际交流时间范围在统计时间内</li>
                <li>此表由主要指导教师为我院的教职工填写</li>
                <li>各指导教师比例由第一负责人分配</li>
                <li>此表中出现的姓名全部为我院教职工</li>
                <li>分配比例请填写小数，不要填写百分数</li>
                <li>"获奖等级/国际交流"已设置下拉菜单</li>
                <li>总分配比例应为1，若不是请反查数据</li>
                <li>需提供写有获奖时间的获奖证书和研究生院开具的出国交流证明文件备查</li>
              </ol>
            </div>
          </a-card>
        </a-col>
        
        <!-- 十、教学和科技奖励 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <TrophyOutlined style="color: #9254de; font-size: 20px; margin-right: 8px;" />
                <span>十、教学和科技奖励</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>教学和科技奖励获奖时间范围在2017年8月1日至2023年7月31日</li>
                <li>此表由第一负责人为我院的教职工填写，各获奖者比例由第一负责人分配，第一负责人非我院教职工不填写</li>
                <li>此表中出现的姓名全部为我院教职工</li>
                <li>分配比例请填写小数，不要填写百分数</li>
                <li>"奖励名称/等级"已设置下拉菜单，选择即可</li>
                <li>"对应分数"、"总分配比例"已内置公式，请勿更改</li>
                <li>总分配比例应为1</li>
                <li>需提供写有获奖时间的获奖证书备查</li>
              </ol>
            </div>
          </a-card>
        </a-col>
        
        <!-- 十一、教师上课学时 -->
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="score-card">
            <template #title>
              <div class="score-card-title">
                <ScheduleOutlined style="color: #2f54eb; font-size: 20px; margin-right: 8px;" />
                <span>十一、教师上课学时</span>
              </div>
            </template>
            <a-divider />
            <div class="rule-content">
              <p><strong>填写说明：</strong></p>
              <ol class="detail-list">
                <li>教学工作量统计范围为每学年度内完成的教学任务</li>
                <li>系统将自动从教务系统导入教学工作量数据</li>
                <li>包括理论课、实验课、实践课等各类教学活动</li>
                <li>教学工作量按照学校最新标准折算成标准学时</li>
                <li>教师可在系统中查看自己的教学工作量明细</li>
                <li>如有疑问请与教务处确认</li>
                <li>分配比例已按照实际授课情况自动计算</li>
                <li>最终数据以教务处审核为准</li>
              </ol>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </section>
    
    <!-- 通知弹窗组件 -->
    <NotificationPopup :notification-id="notificationId" />
  </section>
</template>

<script setup>
import {watchEffect, reactive, ref, onMounted, nextTick, onUnmounted, computed, watch} from 'vue'
import {getHomeOverview, getHomeCharts, getUserStats, getCombinedRanking, getMyScore} from "../../api/modules/api.home";
import {usersSearch} from "../../api/modules/api.users";
import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole } = useUserRole();
import {ZyMessage} from "../../libs/util.toast";
import ZySectionHeader from "../../components/common/ZySectionHeader.vue";
import NotificationPopup from "../../components/NotificationPopup/index.vue";
import useUserId from '@/composables/useUserId'
import * as XLSX from 'xlsx';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import {
  getUserNotifications,
  markNotificationAsRead,
  getUnreadNotificationCount
} from '@/api/modules/api.notifications.js';
import {
  UserOutlined,
  FundProjectionScreenOutlined,
  FileTextOutlined,
  TrophyOutlined,
  NotificationOutlined,
  ProfileOutlined,
  SafetyCertificateOutlined,
  BookOutlined,
  HighlightOutlined,
  ExperimentOutlined,
  TeamOutlined,
  CrownOutlined,
  EditOutlined,
  ProjectOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  ReloadOutlined,
  BarChartOutlined,
  FileSearchOutlined,
  PieChartOutlined,
  LineChartOutlined,
  BulbOutlined,
  MoneyCollectOutlined,
  GlobalOutlined,
  DownOutlined,
  UpOutlined,
  BellOutlined,
  RightOutlined,
  MessageOutlined,
  MailOutlined,
  SoundOutlined,
  ScheduleOutlined
} from '@ant-design/icons-vue';

// 引入echarts
import * as echarts from 'echarts';
import StatsCard from '@/components/statistics/StatsCard.vue';

// 导入API函数
import { getProjectsTotalScore } from '@/api/modules/api.researchProjects'
import { getTextbooksTotalScore } from '@/api/modules/api.textbooks'
import { getHighLevelPapersTotalScore } from '@/api/modules/api.high-level-papers'
import { getPatentsTotalScore } from '@/api/modules/api.patents'
import { getAwardTotalScore } from '@/api/modules/api.studentAwardGuidanceAwards'
import { getProjectTotalScore as getStudentProjectTotalScore } from '@/api/modules/api.studentProjectGuidanceProjects'
import { getProjectTotalScore as getTeachingReformProjectTotalScore } from '@/api/modules/api.teachingReformProjects'
import { getAcademicAppointmentsTotalScore } from '@/api/modules/api.academicAppointments'
import { getConferencesTotalScore } from '@/api/modules/api.conferences'
import { getTeachingWorkloadsTotalScore } from '@/api/modules/api.teachingWorkloads'
import { getTeachingResearchAwardsTotalScore } from '@/api/modules/api.teachingResearchAwards'

// 导入绩效分析API
import {
  getRadarData,
  getDistributionData,
  getRankingTrend,
  getAnalysisReport,
  getDepartmentComparison,
  getAllRankings
} from '@/api/modules/api.userRanking'

// 导出数据功能
const exportLoading = ref(false);

// 导出排名数据
const exportRankingData = async () => {
  try {
    exportLoading.value = true;
    // 显示加载中提示
    const hide = message.loading('正在导出排行榜数据...', 0);
    
    // 请求所有数据（isExportAll设为true，获取所有记录而不是分页）
    const response = await getCombinedRanking({
      page: 1,
      limit: 1000, // 设置较大的值
      range: rankFilters.range,
      reviewStatus: 'reviewed', // 固定使用已审核状态
      isExportAll: true
    });
    
    hide(); // 关闭加载提示
    
    if (response && response.code === 200 && response.result.records) {
      const data = response.result.records;
      
      // 导出数据处理
      const exportData = data.map((item, index) => {
        // 确保所有数值型字段使用前进行类型检查和转换
        const formatNumber = (value) => {
          const num = parseFloat(value);
          return !isNaN(num) ? num.toFixed(2) : '0.00';
        };
        
        return {
          '排名': index + 1,
          '姓名': item.nickName || '未知',
          '工号': item.studentNumber || '未知',
          '总得分': formatNumber(item.total_score),
          '论文数量': item.papers_count || 0,
          '论文得分': formatNumber(item.papers_score),
          '专利数量': item.patents_count || 0,
          '专利得分': formatNumber(item.patents_score),
          '教材数量': item.textbooks_count || 0,
          '教材得分': formatNumber(item.textbooks_score),
          '教学项目数量': item.teaching_projects_count || 0,
          '教学项目得分': formatNumber(item.teaching_projects_score),
          '奖项数量': item.awards_count || 0,
          '奖项得分': formatNumber(item.awards_score),
          '学生项目数量': item.student_projects_count || 0,
          '学生项目得分': formatNumber(item.student_projects_score),
          '会议数量': item.conferences_count || 0,
          '会议得分': formatNumber(item.conferences_score),
          '学术任职数量': item.appointments_count || 0,
          '学术任职得分': formatNumber(item.appointments_score),
          '科研项目数量': item.research_projects_count || 0,
          '科研项目得分': formatNumber(item.research_projects_score),
          '教学工作量': item.teaching_workloads_count || 0, 
          '教学工作量得分': formatNumber(item.teaching_workloads_score),
          '教学和科研奖励': item.teaching_research_awards_count || 0,
          '教学和科研奖励得分': formatNumber(item.teaching_research_awards_score),
          '条目总数': item.total_count || 0
        };
      });
      
      // 使用xlsx导出
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '教师绩效综合排行榜');
      
      // 生成文件名
      const fileName = `教师绩效综合排行榜_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      
      // 导出文件
      XLSX.writeFile(workbook, fileName);
      
      message.success('导出成功');
    } else {
      message.error(response?.message || '导出失败：未获取到有效数据');
    }
  } catch (error) {
    console.error('导出排名数据失败:', error);
    message.error('导出失败: ' + (error.message || '未知错误'));
  } finally {
    exportLoading.value = false;
  }
};

const notificationId = ref('');
const overviewData = ref(null);
const chartsData = ref(null);
const worksList = ref([]);
const charts = ref([]);
const topPerformers = ref([
  // 提供一些默认数据以避免null引用错误
  {
    rank: 1,
    nickName: '加载中...',
    studentNumber: '------',
    total_score: 0,
    papers_count: 0,
    papers_score: 0,
    patents_count: 0,
    textbooks_count: 0,
    textbooks_score: 0,
    teaching_projects_count: 0,
    teaching_projects_score: 0,
    awards_count: 0,
    awards_score: 0,
    student_projects_count: 0,
    student_projects_score: 0,
    conferences_count: 0,
    conferences_score: 0,
    appointments_count: 0,
    appointments_score: 0,
    research_projects_count: 0,
    research_projects_score: 0,
    teaching_workloads_count: 0,  
    teaching_workloads_score: 0,
    teaching_research_awards_count: 0,
    teaching_research_awards_score: 0,
    total_count: 0
  }
]);

// 饼图DOM引用
const performanceDistributionChartRef = ref(null)
const monthlyScoreTrendChartRef = ref(null)
const scoreTrendChartRef = ref(null);
const dimensionChartRef = ref(null);
// 新增9个分类统计饼图的引用
const projectsChartRef = ref(null);
const papersChartRef = ref(null);
const patentsChartRef = ref(null);
const textbooksChartRef = ref(null);
const teachingProjectsChartRef = ref(null);
const awardsChartRef = ref(null);
const studentProjectsChartRef = ref(null);
const conferencesChartRef = ref(null);
const appointmentsChartRef = ref(null);

// 统一筛选条件
const statsFilters = reactive({
  range: 'in',
  reviewStatus: 'reviewed' // 固定为已审核状态
});

// 统计数据状态
const statsData = reactive({
  projects: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  papers: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  patents: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  textbooks: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  teachingProjects: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  awards: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  studentProjects: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  conferences: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  appointments: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  teachingWorkloads: { totalScore: 0, totalCount: 0, items: [], timeInterval: null },
  teachingResearchAwards: { totalScore: 0, totalCount: 0, items: [], timeInterval: null }
});

// 加载状态
const statsLoading = reactive({
  projects: false,
  papers: false,
  patents: false,
  textbooks: false,
  teachingProjects: false,
  awards: false,
  studentProjects: false,
  conferences: false,
  appointments: false,
  teachingWorkloads: false,
  teachingResearchAwards: false
});

// 页面状态
const loading = ref(false);
const userStats = ref({
  teachers: 0,
  totalItems: 0,
  researchProjects: 0,
  highLevelPapers: 0,
  studentAwards: 0,
  patents: 0,
  textbooks: 0,
  teachingProjects: 0,
  conferences: 0,
  studentProjects: 0,
  academicAppointments: 0,
  teachingWorkloads: 0,
  teachingResearchAwards: 0
});
const notifications = ref([]);
const firstUnreadImportant = ref(null);

// 使用用户ID相关的composable
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId();

// 当前用户角色
const currentRole = ref(null);
const currentUserId = ref(null);

// 我的评分数据
const myScoreData = ref(null);
const myScoreLoading = ref(false);

// ==================== 绩效分析相关状态 ====================

// 绩效分析控制状态
const showPerformanceAnalysis = ref(true);
const analysisLoading = ref(false);
const exportAnalysisLoading = ref(false);

// "我的"部分折叠状态（默认只显示第一行的两个图表）
const showAllMyCharts = ref(false);


// 数据分析图表显示控制（默认只显示第一行的两个图表）
const showAllCharts = ref(false);

// 绩效分析筛选条件
const analysisFilters = reactive({
  range: 'all',
  userId: null // 显示在输入框中的用户ID
});

// 内部实际用于分析的用户ID（不显示在输入框中）
const internalAnalysisUserId = ref('');

// 用户列表（用于选择分析对象）
const userList = ref([]);

// 用户搜索相关状态
const userSearchLoading = ref(false);
const userSearchTimeout = ref(null);

// 绩效评定时间范围
const performanceTimeRange = ref('');

// 通知相关数据
const notificationStats = ref({
  unreadCount: 0,
  totalCount: 0
})
const recentNotifications = ref([])
const notificationsLoading = ref(false)
const currentUserInfo = ref(null)

// 获取绩效评定时间范围
const fetchPerformanceTimeRange = async () => {
  try {
    // TODO: 这里应该调用API获取时间范围
    // const response = await getPerformanceTimeRange();
    // if (response && response.code === 200) {
    //   performanceTimeRange.value = response.data.timeRange;
    // }

    // 暂时使用默认值
    performanceTimeRange.value = '2017年8月1日至2023年7月31日';
  } catch (error) {
    console.error('获取绩效评定时间范围失败:', error);
    performanceTimeRange.value = '2017年8月1日至2023年7月31日';
  }
};

// 获取用户通知数据
const fetchUserNotifications = async () => {
  try {
    notificationsLoading.value = true

    // 获取用户信息
    const userRole = await getUserRole()
    if (!userRole) {
      console.warn('无法获取用户信息')
      return
    }

    currentUserInfo.value = userRole

    // 获取未读通知数量
    try {
      const unreadCountResponse = await getUnreadNotificationCount()
      if (unreadCountResponse.status === 1) {
        notificationStats.value.unreadCount = unreadCountResponse.data.count || 0
      }
    } catch (error) {
      console.warn('获取未读通知数量失败:', error)
    }

    // 获取最新通知列表（前5条）
    const notificationsResponse = await getUserNotifications({
      page: 1,
      pageSize: 5
    })

    if (notificationsResponse.status === 1) {
      const notifications = notificationsResponse.data.list || []

      // 直接使用API返回的数据，无需前端过滤
      recentNotifications.value = notifications
      notificationStats.value.totalCount = notificationsResponse.data.pagination?.total || notifications.length

      console.log('通知数据加载成功:', {
        count: notifications.length,
        total: notificationStats.value.totalCount,
        unread: notificationStats.value.unreadCount
      })
    } else {
      console.error('获取通知失败:', notificationsResponse)
      message.error('获取通知信息失败: ' + (notificationsResponse.message || '未知错误'))
    }

  } catch (error) {
    console.error('获取用户通知失败:', error)
    message.error('获取通知信息失败: ' + error.message)
  } finally {
    notificationsLoading.value = false
  }
}

// 处理通知点击
const handleNotificationClick = async (notification) => {
  // try {
  //   // 如果是未读通知，标记为已读
  //   if (!notification.isRead) {
  //     const response = await markNotificationAsRead(notification.id)

  //     if (response.status === 1) {
  //       notification.isRead = true
  //       notificationStats.value.unreadCount = Math.max(0, notificationStats.value.unreadCount - 1)
  //       message.success('通知已标记为已读')
  //     } else {
  //       message.warning('标记已读失败')
  //     }
  //   }

  //   // 这里可以添加跳转到通知详情页面的逻辑
  //   // router.push(`/notification/detail/${notification.id}`)
  // } catch (error) {
  //   console.error('处理通知点击失败:', error)
  //   message.error('操作失败')
  // }
}

// 查看全部通知
const viewAllNotifications = () => {
  // 跳转到通知列表页面
  window.open('/notification/list', '_blank')
}

// 获取通知图标
const getNotificationIcon = (type) => {
  const iconMap = {
    'system': BellOutlined,
    'announcement': SoundOutlined,
    'message': MessageOutlined,
    'email': MailOutlined,
    'normal': BellOutlined
  }
  return iconMap[type] || BellOutlined
}

// 获取通知颜色
const getNotificationColor = (type) => {
  const colorMap = {
    'system': '#1890ff',
    'announcement': '#fa8c16',
    'message': '#52c41a',
    'email': '#722ed1',
    'normal': '#666666'
  }
  return colorMap[type] || '#666666'
}

// 获取发送模式颜色
const getSendModeColor = (sendMode) => {
  const colorMap = {
    'all': 'red',
    'department': 'blue',
    'user_level': 'green',
    'single': 'orange'
  }
  return colorMap[sendMode] || 'default'
}

// 获取发送模式文本
const getSendModeText = (sendMode) => {
  const textMap = {
    'all': '全员',
    'department': '部门',
    'user_level': '职称',
    'single': '个人'
  }
  return textMap[sendMode] || sendMode
}

// 获取通知类型文本
const getTypeText = (type) => {
  const textMap = {
    'system': '系统通知',
    'announcement': '公告',
    'reminder': '提醒',
    'result': '结果公示',
    'message': '消息',
    'email': '邮件',
    'normal': '普通通知'
  }
  return textMap[type] || type
}

// 格式化通知时间
const formatNotificationTime = (time) => {
  if (!time) return '未知时间'

  try {
    const date = dayjs(time)
    if (!date.isValid()) return '时间格式错误'

    const now = dayjs()
    const diffMinutes = now.diff(date, 'minute')
    const diffHours = now.diff(date, 'hour')
    const diffDays = now.diff(date, 'day')

    // 小于1小时显示分钟
    if (diffMinutes < 60) {
      return diffMinutes <= 0 ? '刚刚' : `${diffMinutes}分钟前`
    }

    // 小于24小时显示小时
    if (diffHours < 24) {
      return `${diffHours}小时前`
    }

    // 小于7天显示天数
    if (diffDays < 7) {
      return `${diffDays}天前`
    }

    // 大于7天显示具体日期
    return date.format('MM-DD HH:mm')
  } catch (error) {
    return '时间解析失败'
  }
}

// 计算当前选中用户的显示名称
const selectedUserLabel = computed(() => {
  if (!analysisFilters.userId || !userList.value.length) {
    return '';
  }
  const selectedUser = userList.value.find(user => user.userId === analysisFilters.userId);
  return selectedUser ? selectedUser.label : analysisFilters.userId;
});

// 直接使用用户列表作为选项数据
const currentUserOptions = computed(() => {
  return userList.value;
});

// 监听用户列表变化（仅用于调试）
watch([userList, currentUserId], ([newUserList, newCurrentUserId]) => {
  console.log('用户列表或当前用户ID发生变化:', {
    userListLength: newUserList.length,
    currentUserId: newCurrentUserId,
    analysisUserId: analysisFilters.userId
  });
}, { immediate: true });

// 图表DOM引用
const radarChartRef = ref(null);
const distributionChartRef = ref(null);
const trendChartRef = ref(null);

// 图表实例
const radarChart = ref(null);
const distributionChart = ref(null);
const trendChart = ref(null);

// 图表数据
const radarChartData = ref(null);
const distributionChartData = ref(null);
const trendChartData = ref(null);
const analysisReport = ref(null);

// 获取我的评分数据
const fetchMyScoreData = async () => {
  try {
    myScoreLoading.value = true;

    // 获取当前用户ID
    const currentUserId = await getUserId(true);
    console.log('当前用户ID:', currentUserId);
    if (!currentUserId) {
      message.error('获取用户ID失败');
      return;
    }

    const response = await getMyScore(currentUserId);

    if (response && response.code === 200) {
      myScoreData.value = response.data;
      console.log('我的评分数据:', myScoreData.value);
    } else {
      message.error(response?.message || '获取我的评分数据失败');
    }
  } catch (error) {
    console.error('获取我的评分数据失败:', error);
    message.error('获取我的评分数据失败: ' + (error.message || '未知错误'));
  } finally {
    myScoreLoading.value = false;
  }
};

// ==================== 绩效分析相关方法 ====================

// 用户搜索处理（参考research-projects.vue的实现）
const handleUserSearch = (value) => {
  // 清除之前的定时器
  if (userSearchTimeout.value) {
    clearTimeout(userSearchTimeout.value);
  }

  // 如果搜索值为空，清空用户列表并自动填充当前用户
  if (!value || value.trim() === '') {
    userList.value = []; // 清空搜索结果
    // 获取当前用户并自动填充
    getUserId(true).then(currentUserId => {
      if (currentUserId) {
        analysisFilters.userId = currentUserId;
        console.log('搜索为空，自动选择当前用户');
      }
    });
    return;
  }

  userSearchLoading.value = true;

  // 延时搜索，避免频繁请求
  userSearchTimeout.value = setTimeout(() => {
    // 调用用户搜索API，确保keyword不为空
    const keyword = value.trim();
    if (!keyword) {
      userSearchLoading.value = false;
      return;
    }

    usersSearch({ keyword })
      .then((res) => {
        console.log('用户搜索API响应:', res);
        // 处理API返回结果
        if (res && res.code === 200) {
          // 将返回的用户数据转换为下拉选项格式
          if (Array.isArray(res.data)) {
            userList.value = res.data.map(item => ({
              userId: item.id, // 用户ID作为选项值
              nickname: item.nickname || item.username || '未知',
              studentNumber: item.studentNumber || '无工号',
              label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`, // 显示名称和用户名
              ...item // 保留原始用户数据，方便后续使用
            }));
            console.log('用户搜索成功，找到', userList.value.length, '个用户');
          } else if (res.data && Array.isArray(res.data.list)) {
            // 如果返回的是包含list属性的对象
            userList.value = res.data.list.map(item => ({
              userId: item.id,
              nickname: item.nickname || item.username || '未知',
              studentNumber: item.studentNumber || '无工号',
              label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`,
              ...item
            }));
            console.log('用户搜索成功（list格式），找到', userList.value.length, '个用户');
          } else {
            // 如果数据结构不是预期的格式
            console.error('搜索用户返回的数据结构异常:', res.data);
            userList.value = [];
          }
        } else {
          userList.value = [];
          console.warn('用户搜索返回数据格式不正确:', res);
          if (res && res.message) {
            message.error('搜索失败: ' + res.message);
          }
        }
      })
      .catch((error) => {
        console.error('搜索用户失败:', error);
        message.error('搜索用户失败: ' + (error.message || '网络错误'));
        userList.value = [];
      })
      .finally(() => {
        userSearchLoading.value = false;
      });
  }, 500); // 500ms的防抖延迟
};

// 用户选择变化处理
const handleUserChange = (userId) => {
  console.log('用户选择变化:', userId);
  analysisFilters.userId = userId;
  if (userId) {
    // 触发数据重新加载
    fetchAnalysisData();
  }
};

// 获取用户列表（不自动设置当前用户，由调用方控制）
const fetchUserList = async () => {
  try {
    // 直接使用排名API获取用户列表，避免空关键词问题
    console.log('使用排名API获取用户列表');
    const response = await getAllRankings({
      page: 1,
      limit: 1000,
      range: 'all'
    });

    if (response && response.code === 200 && response.result?.records) {
      userList.value = response.result.records.map(user => ({
        userId: user.userId,
        nickname: user.nickName || '未知',
        studentNumber: user.studentNumber || '未知',
        label: `${user.nickName || '未知'} (${user.studentNumber || '无工号'})`
      }));
      console.log('通过排名API获取用户列表成功:', userList.value.length, '个用户');
    } else {
      console.warn('排名API返回数据格式异常:', response);
      userList.value = [];
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    userList.value = [];
  }
};

// 筛选条件变化处理
const handleAnalysisFiltersChange = () => {
  if (analysisFilters.userId) {
    fetchAnalysisData();
  }
};



// 用户选项过滤
const filterUserOption = (input, option) => {
  const text = option.children?.toLowerCase() || '';
  return text.includes(input.toLowerCase());
};

// 刷新分析数据
const refreshAnalysisData = () => {
  if (internalAnalysisUserId.value) {
    // 如果有内部分析用户ID，使用内部函数
    fetchAnalysisDataInternal();
  } else if (analysisFilters.userId) {
    // 如果用户已选择，使用普通函数
    fetchAnalysisData();
  } else {
    message.warning('请先选择要分析的用户');
  }
};

// 获取绩效分析数据（内部使用，用于初始加载）
const fetchAnalysisDataInternal = async () => {
  if (!internalAnalysisUserId.value) {
    console.log('没有内部分析用户ID，跳过数据获取');
    return;
  }

  try {
    analysisLoading.value = true;
    console.log('开始获取绩效分析数据（内部），用户ID:', internalAnalysisUserId.value, '范围:', analysisFilters.range);

    // 逐个获取分析数据，便于调试
    let radarResponse, distributionResponse, trendResponse, reportResponse;

    // try {
    //   console.log('开始获取雷达图数据...');
    //   radarResponse = await getRadarData(internalAnalysisUserId.value, analysisFilters.range);
    //   console.log('雷达图数据获取结果:', radarResponse);
    // } catch (error) {
    //   console.error('雷达图数据获取失败:', error);
    //   radarResponse = null;
    // }

    try {
      console.log('开始获取分布图数据...');
      distributionResponse = await getDistributionData(internalAnalysisUserId.value, analysisFilters.range);
      console.log('分布图数据获取结果:', distributionResponse);
    } catch (error) {
      console.error('分布图数据获取失败:', error);
      distributionResponse = null;
    }

    // try {
    //   console.log('开始获取趋势图数据...');
    //   trendResponse = await getRankingTrend(internalAnalysisUserId.value);
    //   console.log('趋势图数据获取结果:', trendResponse);
    // } catch (error) {
    //   console.error('趋势图数据获取失败:', error);
    //   trendResponse = null;
    // }

    try {
      console.log('开始获取分析报告数据...');
      reportResponse = await getAnalysisReport(internalAnalysisUserId.value, analysisFilters.range);
      console.log('分析报告数据获取结果:', reportResponse);
    } catch (error) {
      console.error('分析报告数据获取失败:', error);
      reportResponse = null;
    }

    // 处理图表数据
    handleAnalysisDataResponse(radarResponse, distributionResponse, trendResponse, reportResponse);

  } catch (error) {
    console.error('获取绩效分析数据失败:', error);
    message.error('获取绩效分析数据失败: ' + (error.message || '未知错误'));
  } finally {
    analysisLoading.value = false;
  }
};

// 获取绩效分析数据（用户选择后调用）
const fetchAnalysisData = async () => {
  if (!analysisFilters.userId) {
    console.log('没有选择用户，跳过数据获取');
    return;
  }

  try {
    analysisLoading.value = true;
    console.log('开始获取绩效分析数据，用户ID:', analysisFilters.userId, '范围:', analysisFilters.range);

    // 更新内部分析用户ID
    internalAnalysisUserId.value = analysisFilters.userId;

    // 逐个获取分析数据，便于调试
    let radarResponse, distributionResponse, trendResponse, reportResponse;

    // try {
    //   console.log('开始获取雷达图数据...');
    //   radarResponse = await getRadarData(analysisFilters.userId, analysisFilters.range);
    //   console.log('雷达图数据获取结果:', radarResponse);
    // } catch (error) {
    //   console.error('雷达图数据获取失败:', error);
    //   radarResponse = null;
    // }

    try {
      console.log('开始获取分布图数据...');
      distributionResponse = await getDistributionData(analysisFilters.userId, analysisFilters.range);
      console.log('分布图数据获取结果:', distributionResponse);
    } catch (error) {
      console.error('分布图数据获取失败:', error);
      distributionResponse = null;
    }

    // try {
    //   console.log('开始获取趋势图数据...');
    //   trendResponse = await getRankingTrend(analysisFilters.userId);
    //   console.log('趋势图数据获取结果:', trendResponse);
    // } catch (error) {
    //   console.error('趋势图数据获取失败:', error);
    //   trendResponse = null;
    // }

    try {
      console.log('开始获取分析报告数据...');
      reportResponse = await getAnalysisReport(analysisFilters.userId, analysisFilters.range);
      console.log('分析报告数据获取结果:', reportResponse);
    } catch (error) {
      console.error('分析报告数据获取失败:', error);
      reportResponse = null;
    }

    // 处理图表数据
    handleAnalysisDataResponse(radarResponse, distributionResponse, trendResponse, reportResponse);

  } catch (error) {
    console.error('获取绩效分析数据失败:', error);
    message.error('获取绩效分析数据失败: ' + (error.message || '未知错误'));
  } finally {
    analysisLoading.value = false;
  }
};

// 处理分析数据响应的公共函数
const handleAnalysisDataResponse = (radarResponse, distributionResponse, trendResponse, reportResponse) => {
  // 处理雷达图数据
  // if (radarResponse && radarResponse.status === 1) {
  //   radarChartData.value = radarResponse.data;
  //   console.log('雷达图数据设置完成:', radarChartData.value);
  //   setTimeout(() => {
  //     renderRadarChart();
  //   }, 100);
  // } else {
  //   console.error('雷达图数据获取失败:', radarResponse);
  //   radarChartData.value = null;
  // }

  // 处理分布图数据
  if (distributionResponse && distributionResponse.status === 1) {
    distributionChartData.value = distributionResponse.data;
    console.log('分布图数据设置完成:', distributionChartData.value);
    setTimeout(() => {
      renderDistributionChart();
    }, 100);
  } else {
    console.error('分布图数据获取失败:', distributionResponse);
    distributionChartData.value = null;
  }

  // 处理趋势图数据
  // if (trendResponse && trendResponse.status === 1) {
  //   trendChartData.value = trendResponse.data;
  //   console.log('趋势图数据设置完成:', trendChartData.value);
  //   setTimeout(() => {
  //     renderTrendChart();
  //   }, 100);
  // } else {
  //   console.error('趋势图数据获取失败:', trendResponse);
  //   trendChartData.value = null;
  // }

  // 处理分析报告数据
  if (reportResponse && reportResponse.status === 1) {
    analysisReport.value = reportResponse.data;
    console.log('分析报告数据设置完成:', analysisReport.value);
  } else {
    console.error('分析报告数据获取失败:', reportResponse);
    analysisReport.value = null;
  }
};

// 渲染雷达图
const renderRadarChart = () => {
  console.log('renderRadarChart 被调用');
  console.log('radarChartRef.value:', radarChartRef.value);
  console.log('radarChartData.value:', radarChartData.value);

  if (!radarChartRef.value || !radarChartData.value) {
    console.log('雷达图渲染条件不满足:', {
      hasRef: !!radarChartRef.value,
      hasData: !!radarChartData.value
    });
    return;
  }

  console.log('开始渲染雷达图:', radarChartData.value);

  // 销毁已存在的图表实例
  if (radarChart.value) {
    radarChart.value.dispose();
  }

  radarChart.value = echarts.init(radarChartRef.value);

  // 确保数据是数字格式
  const values = radarChartData.value.values.map(v => parseFloat(v) || 0);
  const maxValue = Math.max(...values, 100); // 至少100作为最大值

  const option = {
    title: {
      text: '综合绩效雷达图',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const index = radarChartData.value.dimensions.findIndex(dim =>
          params.name.includes(dim) || dim.includes(params.name)
        );
        const value = index >= 0 ? values[index] : params.value;
        return `${params.name}: ${value}分`;
      }
    },
    legend: {
      data: ['绩效得分'],
      bottom: 10,
      left: 'left'
    },
    radar: {
      indicator: radarChartData.value.dimensions.map((dim, index) => ({
        name: dim,
        max: maxValue * 1.2
      })),
      center: ['50%', '50%'],
      radius: '60%',
      splitNumber: 5,
      axisName: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      splitArea: {
        show: false
      }
    },
    series: [{
      name: '绩效得分',
      type: 'radar',
      data: [{
        value: values,
        name: '绩效得分',
        areaStyle: {
          opacity: 0.3,
          color: '#1890ff'
        },
        lineStyle: {
          color: '#1890ff',
          width: 2
        },
        itemStyle: {
          color: '#1890ff'
        }
      }]
    }]
  };

  radarChart.value.setOption(option);
  console.log('雷达图渲染完成');
};

// 渲染分布图
const renderDistributionChart = () => {
  if (!distributionChartRef.value || !distributionChartData.value) {
    console.log('分布图渲染条件不满足:', {
      hasRef: !!distributionChartRef.value,
      hasData: !!distributionChartData.value
    });
    return;
  }

  console.log('开始渲染分布图:', distributionChartData.value);

  if (distributionChart.value) {
    distributionChart.value.dispose();
  }

  distributionChart.value = echarts.init(distributionChartRef.value);

  const scoreDistribution = distributionChartData.value.scoreDistribution || [];

  const option = {
    title: {
      text: '绩效分布分析',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `${params.seriesName}<br/>${params.name}: ${params.value}人 (${params.percent}%)`;
      }
    },
    legend: {
      bottom: 10,
      left: 'left',
      orient: 'vertical',
      data: scoreDistribution.map(item => item.label)
    },
    series: [{
      name: '分数分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      data: scoreDistribution.map(item => ({
        value: parseInt(item.count) || 0,
        name: item.label,
        itemStyle: {
          color: item.isUserRange ? '#ff4d4f' : undefined
        }
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        formatter: '{b}: {c}人'
      }
    }]
  };

  distributionChart.value.setOption(option);
  console.log('分布图渲染完成');
};

// 渲染趋势图
const renderTrendChart = () => {
  if (!trendChartRef.value || !trendChartData.value) {
    console.log('趋势图渲染条件不满足:', {
      hasRef: !!trendChartRef.value,
      hasData: !!trendChartData.value
    });
    return;
  }

  console.log('开始渲染趋势图:', trendChartData.value);

  if (trendChart.value) {
    trendChart.value.dispose();
  }

  trendChart.value = echarts.init(trendChartRef.value);

  // 确保数据是数字格式
  const scoreData = (trendChartData.value.series?.[0]?.data || []).map(v => parseFloat(v) || 0);
  const rankData = (trendChartData.value.series?.[1]?.data || []).map(v => parseInt(v) || 0);

  // 将后端返回的类别名称转换为更清晰的业务含义
  const originalCategories = trendChartData.value.categories || [];
  const displayCategories = originalCategories.map(category => {
    switch(category) {
      case '统计范围内':
        return '本次统计排名';
      case '统计范围外':
        return '历史排名';
      case '全部数据':
        return '总统计排名';
      default:
        return category;
    }
  });

  const option = {
    title: {
      text: '排名趋势对比',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        let result = params[0].axisValue + '<br/>';
        params.forEach(param => {
          const unit = param.seriesName === '总分' ? '分' : '名';
          result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`;
        });
        return result;
      }
    },
    legend: {
      bottom: 10,
      left: 'left',
      data: ['总分', '排名']
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '15%'
    },
    xAxis: {
      type: 'category',
      data: displayCategories, // 使用转换后的类别名称
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '总分',
        position: 'left',
        axisLabel: {
          formatter: '{value}分'
        }
      },
      {
        type: 'value',
        name: '排名',
        position: 'right',
        inverse: true,
        axisLabel: {
          formatter: '第{value}名'
        }
      }
    ],
    series: [
      {
        name: '总分',
        type: 'line',
        yAxisIndex: 0,
        data: scoreData,
        lineStyle: {
          color: '#1890ff',
          width: 3
        },
        itemStyle: {
          color: '#1890ff'
        },
        symbol: 'circle',
        symbolSize: 8
      },
      {
        name: '排名',
        type: 'line',
        yAxisIndex: 1,
        data: rankData,
        lineStyle: {
          color: '#52c41a',
          width: 3
        },
        itemStyle: {
          color: '#52c41a'
        },
        symbol: 'circle',
        symbolSize: 8
      }
    ]
  };

  trendChart.value.setOption(option);
  console.log('趋势图渲染完成');
};

// 导出分析报告
const exportAnalysisReport = async () => {
  if (!analysisReport.value) {
    message.warning('暂无分析报告数据');
    return;
  }

  try {
    exportAnalysisLoading.value = true;

    // 这里可以实现导出功能，比如生成PDF或Excel
    // 暂时使用简单的JSON下载
    const reportData = {
      ...analysisReport.value,
      exportTime: new Date().toISOString(),
      exportUser: analysisFilters.userId
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `绩效分析报告_${analysisFilters.userId}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    message.success('报告导出成功');
  } catch (error) {
    console.error('导出报告失败:', error);
    message.error('导出报告失败');
  } finally {
    exportAnalysisLoading.value = false;
  }
};

// 显示部门对比
const showDepartmentComparison = async () => {
  try {
    const response = await getDepartmentComparison(analysisFilters.range);

    if (response && response.status === 'success') {
      // 这里可以打开一个模态框显示部门对比数据
      // 或者跳转到专门的部门对比页面
      console.log('部门对比数据:', response.data);
      message.info('部门对比功能开发中...');
    }
  } catch (error) {
    console.error('获取部门对比数据失败:', error);
    message.error('获取部门对比数据失败');
  }
};

// 获取绩效等级颜色
const getPerformanceLevelColor = (level) => {
  const colorMap = {
    '优秀': 'green',
    '良好': 'blue',
    '中等': 'orange',
    '一般': 'yellow',
    '待提升': 'red'
  };
  return colorMap[level] || 'default';
};

// 获取范围标签
const getRangeLabel = (range) => {
  const labelMap = {
    'all': '总统计排名',
    'in': '本次统计排名',
    'out': '历史排名'
  };
  return labelMap[range] || '未知';
};

// 切换"我的"图表显示状态
const toggleMyChartsDisplay = () => {
  showAllMyCharts.value = !showAllMyCharts.value;

  // 如果展开了图表，需要等待DOM更新后重新渲染图表
  if (showAllMyCharts.value) {
    setTimeout(() => {
      // 重新渲染"我的"部分的图表
      if (charts.value && charts.value.length > 0) {
        charts.value.forEach(chart => {
          if (chart && chart.resize) {
            chart.resize();
          }
        });
      }
    }, 100);
  }
};

// 切换数据分析图表显示状态
const toggleChartsDisplay = () => {
  showAllCharts.value = !showAllCharts.value;

  // 如果展开了图表，需要等待DOM更新后重新调整图表大小
  if (showAllCharts.value) {
    setTimeout(() => {
      // 重新调整所有StatsCard组件中的图表大小
      try {
        if (patentsCard.value && patentsCard.value.resizeChart) {
          patentsCard.value.resizeChart();
        }
        if (textbooksCard.value && textbooksCard.value.resizeChart) {
          textbooksCard.value.resizeChart();
        }
        if (teachingProjectsCard.value && teachingProjectsCard.value.resizeChart) {
          teachingProjectsCard.value.resizeChart();
        }
        if (awardsCard.value && awardsCard.value.resizeChart) {
          awardsCard.value.resizeChart();
        }
        if (studentProjectsCard.value && studentProjectsCard.value.resizeChart) {
          studentProjectsCard.value.resizeChart();
        }
        if (conferencesCard.value && conferencesCard.value.resizeChart) {
          conferencesCard.value.resizeChart();
        }
        if (appointmentsCard.value && appointmentsCard.value.resizeChart) {
          appointmentsCard.value.resizeChart();
        }
        if (teachingWorkloadsCard.value && teachingWorkloadsCard.value.resizeChart) {
          teachingWorkloadsCard.value.resizeChart();
        }
        if (teachingResearchAwardsCard.value && teachingResearchAwardsCard.value.resizeChart) {
          teachingResearchAwardsCard.value.resizeChart();
        }
        console.log('图表大小调整完成');
      } catch (error) {
        console.error('调整图表大小时出错:', error);
      }
    }, 150);
  }
};


// 初始化绩效评分分布图表

// 初始化绩效评分分布图表
const initPerformanceDistributionChart = () => {
  if (!performanceDistributionChartRef.value) return;
  
  const chart = echarts.init(performanceDistributionChartRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: ['科研项目', '科研经费', '高水平论文', '教学与科研获奖', '国际交流', '社会服务', '就业质量']
    },
    series: [
      {
        name: '评分分布',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 85, name: '科研项目' },
          { value: 78, name: '科研经费' },
          { value: 82, name: '高水平论文' },
          { value: 75, name: '教学与科研获奖' },
          { value: 70, name: '国际交流' },
          { value: 88, name: '社会服务' },
          { value: 92, name: '就业质量' }
        ]
      }
    ]
  });
  charts.value.push(chart);
}

// 初始化月度评分趋势图表
const initMonthlyScoreTrendChart = () => {
  if (!monthlyScoreTrendChartRef.value) return;
  
  const chart = echarts.init(monthlyScoreTrendChartRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['平均分']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '平均分',
        type: 'bar',
        data: [82, 85, 88, 86, 90, 92]
      }
    ]
  });
  charts.value.push(chart);
}

// 修复initCharts函数
const initCharts = () => {
  if (!chartsData.value) return;

  // 初始化评分趋势图表
  if (scoreTrendChartRef.value && chartsData.value.score_trend && 
      chartsData.value.score_trend.series && 
      chartsData.value.score_trend.x_axis) {
    
    const scoreTrendChart = echarts.init(scoreTrendChartRef.value);
    scoreTrendChart.setOption({
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br/>{a}: {c}分'
      },
      legend: {
        data: chartsData.value.score_trend.series.map(item => item.name || '')
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartsData.value.score_trend.x_axis,
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      yAxis: {
        type: 'value',
        name: '分数',
        min: 0,
        max: 1000
      },
      series: chartsData.value.score_trend.series.map(item => ({
        name: item.name || '',
        type: 'line',
        data: item.data || [],
        smooth: true,
        showSymbol: true,
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#1890ff'
        },
        itemStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(24,144,255,0.3)'
            },
            {
              offset: 1,
              color: 'rgba(24,144,255,0.1)'
            }
          ])
        }
      }))
    });
    charts.value.push(scoreTrendChart);
  }

  // 初始化维度分布图表
  if (dimensionChartRef.value && chartsData.value.dimension_distribution &&
      chartsData.value.dimension_distribution.dimensions && 
      chartsData.value.dimension_distribution.data) {
    
    const dimensions = chartsData.value.dimension_distribution.dimensions || [];
    const data = chartsData.value.dimension_distribution.data || [];
    
    if (dimensions.length > 0 && data.length > 0) {
      const dimensionChart = echarts.init(dimensionChartRef.value);
      dimensionChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}分 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: dimensions
        },
        series: [
          {
            name: '评分分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: dimensions.map((name, index) => ({
              value: data[index] || 0,
              name: name,
              itemStyle: {
                color: getDimensionColor(name)
              }
            }))
          }
        ]
      });
      charts.value.push(dimensionChart);
    }
  }
  
  // 初始化新的9个饼图
  initStatisticsCharts();
};

// 初始化统计图表
const initStatisticsCharts = () => {
  // 初始化所有饼图
  if (projectsChartRef.value) {
    charts.value.projects = echarts.init(projectsChartRef.value);
  }
  if (papersChartRef.value) {
    charts.value.papers = echarts.init(papersChartRef.value);
  }
  if (patentsChartRef.value) {
    charts.value.patents = echarts.init(patentsChartRef.value);
  }
  if (textbooksChartRef.value) {
    charts.value.textbooks = echarts.init(textbooksChartRef.value);
  }
  if (teachingProjectsChartRef.value) {
    charts.value.teachingProjects = echarts.init(teachingProjectsChartRef.value);
  }
  if (awardsChartRef.value) {
    charts.value.awards = echarts.init(awardsChartRef.value);
  }
  if (studentProjectsChartRef.value) {
    charts.value.studentProjects = echarts.init(studentProjectsChartRef.value);
  }
  if (conferencesChartRef.value) {
    charts.value.conferences = echarts.init(conferencesChartRef.value);
  }
  if (appointmentsChartRef.value) {
    charts.value.appointments = echarts.init(appointmentsChartRef.value);
  }
  if (teachingWorkloadsCard.value && teachingWorkloadsCard.value.$el.querySelector('.chart-container')) {
    charts.value.teachingWorkloads = echarts.init(teachingWorkloadsCard.value.$el.querySelector('.chart-container'));
  }
  if (teachingResearchAwardsCard.value && teachingResearchAwardsCard.value.$el.querySelector('.chart-container')) {
    charts.value.teachingResearchAwards = echarts.init(teachingResearchAwardsCard.value.$el.querySelector('.chart-container'));
  }
  
  // 加载所有统计数据
  loadAllStatsData();
};

// 获取维度颜色
const getDimensionColor = (name) => {
  const colorMap = {
    '科研项目': '#1890ff',
    '科研经费': '#52c41a',
    '论文': '#722ed1',
    '获奖': '#faad14',
    '国际交流': '#13c2c2',
    '社会服务': '#fa8c16',
    '就业质量': '#eb2f96'
  };
  return colorMap[name] || '#1890ff';
};

// 监听chartsData变化，初始化图表
watchEffect(() => {
  if (chartsData.value) {
    initCharts();
  }
});

// 页面加载时获取数据
onMounted(async () => {
  try {
    // 获取绩效评定时间范围
    await fetchPerformanceTimeRange();

    // 获取用户通知数据
    await fetchUserNotifications();

    // 获取当前用户角色
    currentRole.value = await getUserRole();
    console.log("当前用户角色:", currentRole.value);
    console.log("roleAuth===", currentRole.value ? currentRole.value.roleAuth : 'undefined');

    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true);
      console.log("当前用户ID:", currentUserId.value);

      // 设置内部分析用户ID，但不显示在输入框中
      internalAnalysisUserId.value = currentUserId.value;
      console.log("当前用户ID已获取，设置为默认分析对象:", currentUserId.value);
    } catch (error) {
      console.error("获取用户ID失败:", error);
    }

    // 获取首页概览数据
    const overviewRes = await getHomeOverview();
    if (overviewRes && overviewRes.code === 200) {
      overviewData.value = overviewRes.data;
      console.log('首页概览数据:', overviewData.value);
    }

    // 获取绩效排名前茅数据
    await loadTopPerformers();

    // 获取用户统计数据
    await loadUserStats();
    
    // 获取首页图表数据
    const chartsRes = await getHomeCharts();
    if (chartsRes && chartsRes.code === 200) {
      chartsData.value = chartsRes.data;
      console.log('首页图表数据:', chartsData.value);
    }
    
    // 确保所有数据加载和DOM更新完成后初始化图表
    await nextTick();
    
    try {
      // 初始化概览饼图
      initOverviewPieChart();
        
      // 确保图表DOM元素都存在后再初始化
      if (personalScoreTrendRef.value) {
        initPersonalScoreTrend();
      }
      
      if (performanceDistributionChartRef.value) {
        initPerformanceDistributionChart();
      }
      
      if (monthlyScoreTrendChartRef.value) {
        initMonthlyScoreTrendChart();
      }
      
      // 初始化其他图表
      initCharts();
      
      // 统一加载StatsCard组件的数据，避免重复请求
      await loadAllStatsData();
      
      // 设置窗口大小改变事件监听
      window.addEventListener('resize', handleWindowResize);
    } catch (chartError) {
      console.error('初始化图表失败:', chartError);
    }

    // 获取我的评分数据
    await fetchMyScoreData();

    // 初始化绩效分析功能
    console.log('开始初始化绩效分析功能...');

    // 如果已经有内部分析用户ID，立即加载绩效分析数据
    if (internalAnalysisUserId.value) {
      console.log('开始加载绩效分析数据，用户ID:', internalAnalysisUserId.value);

      // 如果是教师角色，强调这是个人数据
      if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
        console.log('用户为教师角色，默认显示个人数据');
      }

      // 等待DOM更新后加载绩效分析数据
      await nextTick();
      await fetchAnalysisDataInternal();
      console.log('绩效分析数据加载完成');
    } else {
      console.warn('未获取到当前用户ID，无法加载默认绩效分析数据');
    }

    // 异步加载用户列表（用于用户搜索功能）
    fetchUserList().then(() => {
      console.log('用户列表加载完成，用户数量:', userList.value.length);
    }).catch(error => {
      console.error('用户列表加载失败:', error);
    });
  } catch (error) {
    console.error('获取首页数据失败:', error);
    ZyMessage.error('获取首页数据失败: ' + (error.message || '未知错误'));
  }
});

// 修复onUnmounted函数，确保所有图表实例都被正确销毁
onUnmounted(() => {
  // 移除窗口大小改变事件监听
  window.removeEventListener('resize', handleWindowResize);
  
  // 销毁所有图表实例
  if (charts.value) {
    // 如果charts.value是数组
    if (Array.isArray(charts.value)) {
      charts.value.forEach(chart => {
        if (chart && chart.dispose) {
          chart.dispose();
        }
      });
    }

    // 如果charts.value是对象，包含命名的图表实例
    else if (typeof charts.value === 'object') {
      Object.keys(charts.value).forEach(key => {
        const chart = charts.value[key];
        if (chart && chart.dispose) {
          chart.dispose();
        }
      });
    }
  }
  
  // 销毁概览饼图
  if (overviewPieChart) {
    overviewPieChart.dispose();
    overviewPieChart = null;
  }

  // 销毁绩效分析图表实例
  if (radarChart.value) {
    radarChart.value.dispose();
    radarChart.value = null;
  }
  if (distributionChart.value) {
    distributionChart.value.dispose();
    distributionChart.value = null;
  }
  if (trendChart.value) {
    trendChart.value.dispose();
    trendChart.value = null;
  }
});

// 监听窗口大小变化，重新调整图表大小
const handleResize = () => {
  for (const key in charts.value) {
    if (charts.value[key]) {
      charts.value[key].resize();
    }
  }
  
  charts.value.forEach(chart => chart.resize());
};

// 窗口大小变化处理
const handleWindowResize = () => {
  if (charts.value && charts.value.length > 0) {
    charts.value.forEach(chart => chart.resize());
  }
  
  // 调整概览饼图大小
  if (overviewPieChart) {
    overviewPieChart.resize();
  }

  // 调整绩效分析图表大小
  if (radarChart.value) {
    radarChart.value.resize();
  }
  if (distributionChart.value) {
    distributionChart.value.resize();
  }
  if (trendChart.value) {
    trendChart.value.resize();
  }
};

const topPerformerColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    align: 'center'
  },
  {
    title: '姓名',
    dataIndex: 'nickName',
    key: 'nickName',
    width: 100,
  },
  {
    title: '工号',
    dataIndex: 'studentNumber',
    key: 'studentNumber',
    width: 100,
  },
  {
    title: '得分',
    dataIndex: 'total_score',
    key: 'score',
    width: 'auto',
  }
];

const activeNoticeTab = ref('all');

// 根据排名获取颜色
const getRankColor = (rank) => {
  if (rank === '-') return 'default';
  
  const rankNum = parseInt(rank);
  if (rankNum === 1) return '#f5222d'; // 红色，第一名
  if (rankNum === 2) return '#fa8c16'; // 橙色，第二名
  if (rankNum === 3) return '#faad14'; // 黄色，第三名
  if (rankNum <= 10) return '#52c41a'; // 绿色，前十
  if (rankNum <= 20) return '#1890ff'; // 蓝色，前二十
  return '#d9d9d9'; // 灰色，其他
};

// 获取通知图标
const getNoticeIcon = (type) => {
  switch (type) {
    case 'research_project':
      return FundProjectionScreenOutlined;
    case 'paper':
      return FileTextOutlined;
    case 'award':
      return TrophyOutlined;
    default:
      return NotificationOutlined;
  }
};

// 获取通知颜色
const getNoticeColor = (type) => {
  switch (type) {
    case 'research_project':
      return '#1890ff';
    case 'paper':
      return '#722ed1';
    case 'award':
      return '#faad14';
    default:
      return '#52c41a';
  }
};

// 过滤通知
const filterNotices = (type) => {
  if (!overviewData.value || !overviewData.value.recent_activities) {
    return [];
  }
  return overviewData.value.recent_activities.filter(item => item.type === type);
};

// 各维度评分数据
const dimensionScores = ref([
  {
    name: '科研项目',
    score: 250,
    max: 300,
    color: '#1890ff',
    icon: FundProjectionScreenOutlined
  },
  {
    name: '科研经费',
    score: 18,
    max: 20,
    color: '#52c41a',
    icon: MoneyCollectOutlined
  },
  {
    name: '论文发表',
    score: 150,
    max: 200,
    color: '#722ed1',
    icon: FileTextOutlined
  },
  {
    name: '教学获奖',
    score: 100,
    max: 150,
    color: '#faad14',
    icon: TrophyOutlined
  },
  {
    name: '国际交流',
    score: 50,
    max: 100,
    color: '#13c2c2',
    icon: GlobalOutlined
  },
  {
    name: '社会服务',
    score: 20,
    max: 50,
    color: '#fa8c16',
    icon: TeamOutlined
  }
]);

// 个人评分趋势图表引用
const personalScoreTrendRef = ref(null);

// 初始化个人评分趋势图表
const initPersonalScoreTrend = () => {
  if (!personalScoreTrendRef.value) return;
  
  try {
    const chart = echarts.init(personalScoreTrendRef.value);
    chart.setOption({
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br/>{a0}: {c0}分<br/>{a1}: {c1}分'
      },
      legend: {
        data: ['我的评分', '院系平均'],
        right: 10,
        top: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['2023-01', '2023-02', '2023-03', '2023-04', '2023-05', '2023-06']
      },
      yAxis: {
        type: 'value',
        name: '分数',
        min: 700,
        max: 1000
      },
      series: [
        {
          name: '我的评分',
          type: 'line',
          data: [850, 865, 875, 880, 885, 890],
          smooth: true,
          showSymbol: true,
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#f5222d'
          },
          itemStyle: {
            color: '#f5222d'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(245,34,45,0.3)'
              },
              {
                offset: 1,
                color: 'rgba(245,34,45,0.1)'
              }
            ])
          }
        },
        {
          name: '院系平均',
          type: 'line',
          data: [820, 830, 835, 840, 850, 855],
          smooth: true,
          lineStyle: {
            width: 2,
            type: 'dashed',
            color: '#1890ff'
          },
          itemStyle: {
            color: '#1890ff'
          }
        }
      ]
    });
    
    // 将图表实例添加到charts数组中，确保能够在组件卸载时正确销毁
    if (Array.isArray(charts.value)) {
      charts.value.push(chart);
    } else if (!charts.value) {
      charts.value = [chart];
    } else {
      charts.value.personalTrend = chart;
    }
  } catch (error) {
    console.error('初始化个人评分趋势图表失败:', error);
  }
};

// 加载所有统计数据
const loadAllStatsData = async () => {
  console.log('开始统一加载所有统计数据...');
  
  try {
    const loadPromises = [];
    const components = {
      projectsCard, papersCard, patentsCard, textbooksCard,
      teachingProjectsCard, awardsCard, studentProjectsCard,
      conferencesCard, appointmentsCard, teachingWorkloadsCard,
      teachingResearchAwardsCard
    };

    // 检查每个组件是否需要加载数据
    Object.entries(components).forEach(([key, component]) => {
      if (!component.value) return;
      
      const componentRef = component.value;
      let needsReload = false;

      // 当组件设置了isDataLoaded但为false时，或者没有设置isDataLoaded属性时，需要加载
      if (componentRef.isDataLoaded !== undefined) {
        needsReload = !componentRef.isDataLoaded;
        if (!needsReload) {
          console.log(`${key}: 已加载数据，跳过请求`);
        }
      } else {
        needsReload = true; // 旧组件不支持isDataLoaded，总是重新加载
      }

      if (needsReload) {
        console.log(`${key}: 需要加载数据`);
        // 前两个组件总是显示，剩下的只在展开时加载
        if (key === 'projectsCard' || key === 'papersCard' || showAllCharts.value) {
          loadPromises.push(componentRef.reloadData());
        }
      }
    });

    // 等待所有需要加载的数据完成
    if (loadPromises.length > 0) {
      console.log(`共有${loadPromises.length}个组件需要加载数据`);
      await Promise.all(loadPromises);
      console.log('所有需要的统计数据加载完成');
    } else {
      console.log('没有组件需要重新加载数据');
    }
  } catch (error) {
    console.error('加载统计数据时发生错误:', error);
  }
};

// 加载科研项目统计
const loadProjectsStats = async () => {
  if (!charts.value.projects) return;
  
  statsLoading.projects = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getProjectsTotalScore(params);
    statsData.projects.totalScore = data.totalScore || 0;
    statsData.projects.totalCount = data.totalCount || 0;
    statsData.projects.items = data.items || [];
    statsData.projects.timeInterval = data.timeInterval || null;
    
    updateProjectsChart();
  } catch (error) {
    console.error('Failed to load projects statistics:', error);
  } finally {
    statsLoading.projects = false;
  }
};

// 更新科研项目统计图表
const updateProjectsChart = () => {
  if (!charts.value.projects) return;
  
  const items = statsData.projects.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '项目得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.projects.setOption(option);
};

// 加载高水平论文统计
const loadPapersStats = async () => {
  if (!charts.value.papers) return;
  
  statsLoading.papers = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getHighLevelPapersTotalScore(params);
    statsData.papers.totalScore = data.totalScore || 0;
    statsData.papers.totalCount = data.totalCount || 0;
    statsData.papers.items = data.items || [];
    statsData.papers.timeInterval = data.timeInterval || null;
    
    updatePapersChart();
  } catch (error) {
    console.error('Failed to load papers statistics:', error);
  } finally {
    statsLoading.papers = false;
  }
};

// 更新高水平论文统计图表
const updatePapersChart = () => {
  if (!charts.value.papers) return;
  
  const items = statsData.papers.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '论文得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.papers.setOption(option);
};

// 加载专利统计
const loadPatentsStats = async () => {
  if (!charts.value.patents) return;
  
  statsLoading.patents = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getPatentsTotalScore(params);
    statsData.patents.totalScore = data.totalScore || 0;
    statsData.patents.totalCount = data.totalCount || 0;
    statsData.patents.items = data.items || [];
    statsData.patents.timeInterval = data.timeInterval || null;
    
    updatePatentsChart();
  } catch (error) {
    console.error('Failed to load patents statistics:', error);
  } finally {
    statsLoading.patents = false;
  }
};

// 更新专利统计图表
const updatePatentsChart = () => {
  if (!charts.value.patents) return;
  
  const items = statsData.patents.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '专利得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.patents.setOption(option);
};

// 加载教材与著作统计
const loadTextbooksStats = async () => {
  if (!charts.value.textbooks) return;
  
  statsLoading.textbooks = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getTextbooksTotalScore(params);
    statsData.textbooks.totalScore = data.totalScore || 0;
    statsData.textbooks.totalCount = data.totalCount || 0;
    statsData.textbooks.items = data.items || [];
    statsData.textbooks.timeInterval = data.timeInterval || null;
    
    updateTextbooksChart();
  } catch (error) {
    console.error('Failed to load textbooks statistics:', error);
  } finally {
    statsLoading.textbooks = false;
  }
};

// 更新教材与著作统计图表
const updateTextbooksChart = () => {
  if (!charts.value.textbooks) return;
  
  const items = statsData.textbooks.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '教材得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.textbooks.setOption(option);
};

// 加载教学改革项目统计
const loadTeachingProjectsStats = async () => {
  if (!charts.value.teachingProjects) return;
  
  statsLoading.teachingProjects = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getTeachingReformProjectTotalScore(params);
    statsData.teachingProjects.totalScore = data.totalScore || 0;
    statsData.teachingProjects.totalCount = data.totalCount || 0;
    statsData.teachingProjects.items = data.items || [];
    statsData.teachingProjects.timeInterval = data.timeInterval || null;
    
    updateTeachingProjectsChart();
  } catch (error) {
    console.error('Failed to load teaching projects statistics:', error);
  } finally {
    statsLoading.teachingProjects = false;
  }
};

// 更新教学改革项目统计图表
const updateTeachingProjectsChart = () => {
  if (!charts.value.teachingProjects) return;
  
  const items = statsData.teachingProjects.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '教学项目得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.teachingProjects.setOption(option);
};

// 加载学生获奖指导奖项统计
const loadAwardsStats = async () => {
  if (!charts.value.awards) return;
  
  statsLoading.awards = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getAwardTotalScore(params);
    statsData.awards.totalScore = data.totalScore || 0;
    statsData.awards.totalCount = data.totalCount || 0;
    statsData.awards.items = data.items || [];
    statsData.awards.timeInterval = data.timeInterval || null;
    
    updateAwardsChart();
  } catch (error) {
    console.error('Failed to load awards statistics:', error);
  } finally {
    statsLoading.awards = false;
  }
};

// 更新学生获奖指导奖项统计图表
const updateAwardsChart = () => {
  if (!charts.value.awards) return;
  
  const items = statsData.awards.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '奖项得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.awards.setOption(option);
};

// 加载学生项目指导统计
const loadStudentProjectsStats = async () => {
  if (!charts.value.studentProjects) return;
  
  statsLoading.studentProjects = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getStudentProjectTotalScore(params);
    statsData.studentProjects.totalScore = data.totalScore || 0;
    statsData.studentProjects.totalCount = data.totalCount || 0;
    statsData.studentProjects.items = data.items || [];
    statsData.studentProjects.timeInterval = data.timeInterval || null;
    
    updateStudentProjectsChart();
  } catch (error) {
    console.error('Failed to load student projects statistics:', error);
  } finally {
    statsLoading.studentProjects = false;
  }
};

// 更新学生项目指导统计图表
const updateStudentProjectsChart = () => {
  if (!charts.value.studentProjects) return;
  
  const items = statsData.studentProjects.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '学生项目得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.studentProjects.setOption(option);
};

// 加载会议总分统计
const loadConferencesStats = async () => {
  if (!charts.value.conferences) return;
  
  statsLoading.conferences = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getConferencesTotalScore(params);
    statsData.conferences.totalScore = data.totalScore || 0;
    statsData.conferences.totalCount = data.totalCount || 0;
    statsData.conferences.items = data.items || [];
    statsData.conferences.timeInterval = data.timeInterval || null;
    
    updateConferencesChart();
  } catch (error) {
    console.error('Failed to load conferences statistics:', error);
  } finally {
    statsLoading.conferences = false;
  }
};

// 更新会议总分统计图表
const updateConferencesChart = () => {
  if (!charts.value.conferences) return;
  
  const items = statsData.conferences.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '会议得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.conferences.setOption(option);
};

// 加载学术任职总分统计
const loadAppointmentsStats = async () => {
  if (!charts.value.appointments) return;
  
  statsLoading.appointments = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getAcademicAppointmentsTotalScore(params);
    statsData.appointments.totalScore = data.totalScore || 0;
    statsData.appointments.totalCount = data.totalCount || 0;
    statsData.appointments.items = data.items || [];
    statsData.appointments.timeInterval = data.timeInterval || null;
    
    updateAppointmentsChart();
  } catch (error) {
    console.error('Failed to load appointments statistics:', error);
  } finally {
    statsLoading.appointments = false;
  }
};

// 更新学术任职总分统计图表
const updateAppointmentsChart = () => {
  if (!charts.value.appointments) return;
  
  const items = statsData.appointments.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '学术任职得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.appointments.setOption(option);
};

// 处理筛选条件变化
const handleFiltersChange = () => {
  console.log('筛选条件变化:', statsFilters);
  
  // 使用nextTick确保DOM更新后再加载数据
  nextTick(async () => {
    try {
      // 重置所有组件的isDataLoaded状态
      const components = {
        projectsCard, papersCard, patentsCard, textbooksCard,
        teachingProjectsCard, awardsCard, studentProjectsCard,
        conferencesCard, appointmentsCard, teachingWorkloadsCard,
        teachingResearchAwardsCard
      };
      
      // 对每个组件，如果支持isDataLoaded属性，则设置为false来强制重新加载
      Object.entries(components).forEach(([key, component]) => {
        if (component.value && component.value.isDataLoaded !== undefined) {
          console.log(`重置${key}的数据加载状态`);
          component.value.isDataLoaded = false;
        }
      });
      
      // 统一加载所有统计数据
      await loadAllStatsData();
    } catch (error) {
      console.error('处理筛选条件变化出错:', error);
    }
  });
};

// 用于从StatsCard组件接收数据更新
const updateModuleData = (moduleKey, data) => {
  console.log(`${moduleKey} 数据更新:`, data);
  
  if (data) {
    // 更新统计状态数据
    statsData[moduleKey].totalScore = parseFloat(data.totalScore || 0);
    statsData[moduleKey].totalCount = parseInt(data.totalCount || 0);
    statsData[moduleKey].items = data.items || [];
    statsData[moduleKey].timeInterval = data.timeInterval || null;
    
    console.log(`模块 ${moduleKey} 数据已更新，总分: ${statsData[moduleKey].totalScore}`);
  }
};

// 手动刷新所有统计数据
const reloadAllStats = () => {
  // 刷新所有统计卡片的数据
  projectsCard.value?.reloadData();
  papersCard.value?.reloadData();
  patentsCard.value?.reloadData();
  textbooksCard.value?.reloadData();
  teachingProjectsCard.value?.reloadData();
  awardsCard.value?.reloadData();
  studentProjectsCard.value?.reloadData();
  conferencesCard.value?.reloadData();
  appointmentsCard.value?.reloadData();
};

// Variables are automatically available to the template in <script setup>
// No return statement needed
// Everything below this line is already exposed to the template

// 统计表格列配置
const projectTypeColumns = [
  { title: '项目类型', dataIndex: 'typeName', key: 'typeName' },
  { title: '项目数量', dataIndex: 'count', key: 'count', width: '150px' },
  { title: '总得分', dataIndex: 'totalScore', key: 'totalScore', width: '150px' }
];

const papersLevelColumns = [
  { title: '论文级别', dataIndex: 'levelName', key: 'levelName' },
  { title: '论文数量', dataIndex: 'count', key: 'count', width: '150px' },
  { title: '总得分', dataIndex: 'totalScore', key: 'totalScore', width: '150px' }
];

const patentsTypeColumns = [
  { title: '专利类型', dataIndex: 'typeName', key: 'typeName' },
  { title: '专利数量', dataIndex: 'count', key: 'count', width: '150px' },
  { title: '总得分', dataIndex: 'totalScore', key: 'totalScore', width: '150px' }
];

const textbooksCategoryColumns = [
  { title: '教材类别', dataIndex: 'categoryName', key: 'categoryName' },
  { title: '教材数量', dataIndex: 'count', key: 'count', width: '150px' },
  { title: '总得分', dataIndex: 'totalScore', key: 'totalScore', width: '150px' }
];

const teachingReformProjectsLevelColumns = [
  { title: '项目级别', dataIndex: 'levelName', key: 'levelName' },
  { title: '项目数量', dataIndex: 'count', key: 'count', width: '150px' },
  { title: '总得分', dataIndex: 'totalScore', key: 'totalScore', width: '150px' }
];

const awardsLevelColumns = [
  { title: '奖项级别', dataIndex: 'levelName', key: 'levelName' },
  { title: '奖项数量', dataIndex: 'count', key: 'count', width: '150px' },
  { title: '总得分', dataIndex: 'totalScore', key: 'totalScore', width: '150px' }
];

const studentProjectLevelColumns = [
  { title: '项目级别', dataIndex: 'levelName', key: 'levelName' },
  { title: '项目数量', dataIndex: 'count', key: 'count', width: '150px' },
  { title: '总得分', dataIndex: 'totalScore', key: 'totalScore', width: '150px' }
];

const conferencesLevelColumns = [
  { title: '会议级别', dataIndex: 'levelName', key: 'levelName' },
  { title: '会议数量', dataIndex: 'count', key: 'count', width: '150px' },
  { title: '总得分', dataIndex: 'totalScore', key: 'totalScore', width: '150px' }
];

const appointmentsLevelColumns = [
  {
    title: '任职级别',
    dataIndex: 'levelName',
    key: 'levelName',
  },
  {
    title: '任职数量',
    dataIndex: 'count',
    key: 'count',
    width: '100px',
  },
  {
    title: '总得分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: '100px',
    sorter: (a, b) => a.totalScore - b.totalScore,
    defaultSortOrder: 'descend'
  }
];

// 教学工作量级别表格列定义
const teachingWorkloadsLevelColumns = [
  {
    title: '工作量级别',
    dataIndex: 'levelName',
    key: 'levelName',
  },
  {
    title: '工作量数量',
    dataIndex: 'count',
    key: 'count',
    width: '100px',
  },
  {
    title: '总得分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: '100px',
    sorter: (a, b) => a.totalScore - b.totalScore,
    defaultSortOrder: 'descend'
  }
];

// 教学科研奖励级别表格列定义
const teachingResearchAwardsLevelColumns = [
  {
    title: '奖励级别',
    dataIndex: 'levelName',
    key: 'levelName',
  },
  {
    title: '奖励数量',
    dataIndex: 'count',
    key: 'count',
    width: '100px',
  },
  {
    title: '总得分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: '100px',
    sorter: (a, b) => a.totalScore - b.totalScore,
    defaultSortOrder: 'descend'
  }
];

// 卡片组件引用
const projectsCard = ref(null);
const papersCard = ref(null);
const patentsCard = ref(null);
const textbooksCard = ref(null);
const teachingProjectsCard = ref(null);
const awardsCard = ref(null);
const studentProjectsCard = ref(null);
const conferencesCard = ref(null);
const appointmentsCard = ref(null);
const teachingWorkloadsCard = ref(null);
const teachingResearchAwardsCard = ref(null);

// 获取用户统计数据
const loadUserStats = async () => {
  try {
    const res = await getUserStats();
    if (res && res.code === 200) {
      userStats.value = res.data || {
        teachers: 0,
        totalItems: 0,
        researchProjects: 0,
        highLevelPapers: 0,
        patents: 0,
        textbooks: 0,
        teachingProjects: 0,
        studentAwards: 0,
        studentProjects: 0,
        conferences: 0,
        academicAppointments: 0,
        teachingWorkloads: 0,
        teachingResearchAwards: 0
      };
      console.log('用户统计数据:', userStats.value);
    }
  } catch (error) {
    console.error('获取用户统计数据失败:', error);
    userStats.value = {
      teachers: 0,
      totalItems: 0,
      researchProjects: 0,
      highLevelPapers: 0,
      patents: 0,
      textbooks: 0,
      teachingProjects: 0,
      studentAwards: 0,
      studentProjects: 0,
      conferences: 0,
      academicAppointments: 0,
      teachingWorkloads: 0,
      teachingResearchAwards: 0
    };
  }
};

// 分数转换为百分比
const getScorePercent = (score) => {
  // 假设最高分为1000分
  const maxScore = 1000; 
  return Math.min(100, (score / maxScore) * 100);
};

// 分页状态变量
const currentPage = ref(1);
const pageSize = ref(10);
const rankingTotal = ref(0); // 总记录数
const rankingLoading = ref(false); // 加载状态

// 筛选条件
const rankFilters = reactive({
  range: 'in',
  reviewStatus: 'reviewed' // 固定为已审核状态
});

// 处理页码变化的函数
const handlePageChange = (page, size) => {
  console.log(`页码变化：从第${currentPage.value}页变为第${page}页，每页${size}条`);
  currentPage.value = page;
  if (size !== undefined) {
    pageSize.value = size;
  }
  fetchRankingData();
};

// 处理筛选条件变化
const handleRankFiltersChange = () => {
  console.log('筛选条件变化:', rankFilters);
  currentPage.value = 1; // 重置到第一页
  fetchRankingData();
};

// 重写获取排名数据的函数
const fetchRankingData = async () => {
  if (!currentPage.value) {
    currentPage.value = 1; // 确保页码有效
  }
  
  rankingLoading.value = true;
  console.log('开始获取数据，页码:', currentPage.value, '每页条数:', pageSize.value);
  
  try {
    // 构建请求参数
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      range: rankFilters.range,
      reviewStatus: 'reviewed', // 固定使用已审核状态
      isExportAll: false
    };
    
    console.log('请求参数:', params);
    
    // 调用API获取数据
    const response = await getCombinedRanking(params);
    console.log('API响应:', response);
    
    if (response && response.code === 200) {
      const result = response.result || {};
      const records = result.records || [];
      
      // 计算总记录数 - 处理total为null的情况
      if (result.total === null) {
        // 如果total为null，使用pages和size计算
        if (result.pages && result.size) {
          rankingTotal.value = result.pages * result.size;
          console.log('总记录数(通过pages*size计算):', rankingTotal.value);
        } else {
          // 如果pages或size也不可用，至少考虑当前页的记录数
          rankingTotal.value = Math.max(records.length, 
                                      (currentPage.value - 1) * pageSize.value + records.length);
          console.log('总记录数(通过记录数估算):', rankingTotal.value);
        }
      } else {
        rankingTotal.value = result.total;
        console.log('总记录数(API返回):', rankingTotal.value);
      }
      
      if (records.length > 0) {
        // 计算当前页起始序号
        const startIndex = (currentPage.value - 1) * pageSize.value + 1;
        
        // 处理数据
        topPerformers.value = records.map((item, index) => ({
          ...item,
          // 根据当前页码和每页条数计算实际排名
          index: startIndex + index
        }));
        
        console.log(`成功获取第${currentPage.value}页数据，共${records.length}条记录，起始排名: ${startIndex}`);
      } else {
        // 无数据时显示提示
        topPerformers.value = [{
          rank: '-',
          index: '-',
          nickName: '暂无数据',
          studentNumber: '-',
          total_score: 0
        }];
        console.log('未找到记录');
      }
    } else {
      console.error('API请求失败:', response?.message || '未知错误');
      // 显示错误提示
      topPerformers.value = [{
        rank: '-',
        index: '-',
        nickName: '请求失败',
        studentNumber: '-',
        total_score: 0
      }];
    }
  } catch (error) {
    console.error('请求异常:', error);
    // 显示异常提示
    topPerformers.value = [{
      rank: '-',
      index: '-',
      nickName: '加载异常',
      studentNumber: '-',
      total_score: 0
    }];
  } finally {
    rankingLoading.value = false;
  }
};

// 加载初始数据的函数
const loadTopPerformers = async () => {
  currentPage.value = 1;
  await fetchRankingData();
};

// 计算每项分数占总分的宽度比例
const getItemScoreWidth = (itemScore, totalScore) => {
  if (!itemScore || !totalScore || totalScore === 0) return '0%';
  const percentage = (itemScore / totalScore) * 100;
  return `${Math.min(100, percentage.toFixed(0))}%`;
};

// 处理每页显示数量变化的函数
const handleSizeChange = (current, size) => {
  console.log(`每页显示数量变化：从${pageSize.value}条变为${size}条，当前页：${current}`);
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
  fetchRankingData();
};

// 根据图标名称获取对应的组件
const getIconComponent = (iconName) => {
  const iconMap = {
    'FileTextOutlined': FileTextOutlined,
    'SafetyCertificateOutlined': SafetyCertificateOutlined,
    'BookOutlined': BookOutlined,
    'HighlightOutlined': HighlightOutlined,
    'TrophyOutlined': TrophyOutlined,
    'FundProjectionScreenOutlined': FundProjectionScreenOutlined,
    'GlobalOutlined': GlobalOutlined,
    'TeamOutlined': TeamOutlined
  };
  
  return iconMap[iconName] || FileTextOutlined; // 默认返回FileTextOutlined
};

// 格式化分数显示
const formatScore = (score) => {
  if (score === undefined || score === null) return '0';
  return parseFloat(score).toFixed(2);
};

// 加载教学工作量统计
const loadTeachingWorkloadsStats = async () => {
  if (!charts.value.teachingWorkloads) return;
  
  statsLoading.teachingWorkloads = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getTeachingWorkloadsTotalScore(params);
    statsData.teachingWorkloads.totalScore = data.totalScore || 0;
    statsData.teachingWorkloads.totalCount = data.totalCount || 0;
    statsData.teachingWorkloads.items = data.items || [];
    statsData.teachingWorkloads.timeInterval = data.timeInterval || null;
    
    updateTeachingWorkloadsChart();
  } catch (error) {
    console.error('Failed to load teaching workloads statistics:', error);
  } finally {
    statsLoading.teachingWorkloads = false;
  }
};

// 更新教学工作量统计图表
const updateTeachingWorkloadsChart = () => {
  if (!charts.value.teachingWorkloads) return;
  
  const items = statsData.teachingWorkloads.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '教学工作量得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.teachingWorkloads.setOption(option);
};

// 加载教学科研奖励统计
const loadTeachingResearchAwardsStats = async () => {
  if (!charts.value.teachingResearchAwards) return;
  
  statsLoading.teachingResearchAwards = true;
  try {
    const params = {
      range: statsFilters.range,
      reviewStatus: statsFilters.reviewStatus
    };
    const { data } = await getTeachingResearchAwardsTotalScore(params);
    statsData.teachingResearchAwards.totalScore = data.totalScore || 0;
    statsData.teachingResearchAwards.totalCount = data.totalCount || 0;
    statsData.teachingResearchAwards.items = data.items || [];
    statsData.teachingResearchAwards.timeInterval = data.timeInterval || null;
    
    updateTeachingResearchAwardsChart();
  } catch (error) {
    console.error('Failed to load teaching research awards statistics:', error);
  } finally {
    statsLoading.teachingResearchAwards = false;
  }
};

// 更新教学科研奖励统计图表
const updateTeachingResearchAwardsChart = () => {
  if (!charts.value.teachingResearchAwards) return;
  
  const items = statsData.teachingResearchAwards.items || [];
  const chartData = items.map(item => ({
    name: item.name,
    value: item.score
  }));
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '教学科研奖励得分',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: chartData
      }
    ]
  };
  
  charts.value.teachingResearchAwards.setOption(option);
};

// 概览饼图DOM引用
const overviewPieChartRef = ref(null);
let overviewPieChart = null;

// 初始化概览饼图
const initOverviewPieChart = () => {
  if (!overviewPieChartRef.value || !userStats.value) return;
  
  // 如果图表已存在，先销毁
  if (overviewPieChart) {
    overviewPieChart.dispose();
  }
  
  // 创建新图表
  overviewPieChart = echarts.init(overviewPieChartRef.value);
  
  // 准备饼图数据
  const pieData = [
    { name: '科研项目', value: userStats.value.researchProjects || 0, color: '#1890ff' },
    { name: '发表论文', value: userStats.value.highLevelPapers || 0, color: '#722ed1' },
    { name: '指导学生获奖', value: userStats.value.studentAwards || 0, color: '#cf1322' },
    { name: '专利数量', value: userStats.value.patents || 0, color: '#fa8c16' },
    { name: '教材著作', value: userStats.value.textbooks || 0, color: '#a0d911' },
    { name: '教学改革', value: userStats.value.teachingProjects || 0, color: '#13c2c2' },
    { name: '学术会议', value: userStats.value.conferences || 0, color: '#faad14' },
    { name: '学生立项', value: userStats.value.studentProjects || 0, color: '#eb2f96' },
    { name: '学术任职', value: userStats.value.academicAppointments || 0, color: '#52c41a' },
    { name: '教学工作量', value: userStats.value.teachingWorkloads || 0, color: '#2f54eb' },
    { name: '教学科研奖励', value: userStats.value.teachingResearchAwards || 0, color: '#9254de' }
  ];
  
  // 筛选出数量大于0的项目
  const validPieData = pieData.filter(item => item.value > 0);
  
  // 设置图表配置
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      bottom: 10,
      left: 'center',
      formatter: function (name) {
        const data = validPieData.find(item => item.name === name);
        return `${name}: ${data ? data.value : 0}`;
      },
      textStyle: {
        width: 100,
        overflow: 'break',
        lineHeight: 16
      },
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 20
    },
    series: [
      {
        name: '条目数量',
        type: 'pie',
        radius: ['35%', '65%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c}\n{d}%'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10
        },
        data: validPieData.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  };
  
  // 渲染图表
  overviewPieChart.setOption(option);
  
  // 添加到charts数组以便统一管理
  charts.value.push(overviewPieChart);
};

// 监听userStats变化，更新概览饼图
watch(() => userStats.value, (newStats) => {
  if (newStats) {
    nextTick(() => {
      initOverviewPieChart();
    });
  }
}, { deep: true });

</script>

<style lang="scss" scoped>
/* 分数条样式 */
.score-bars {
  max-width: 100%;
}

.score-bar-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.score-bar-label {
  width: 60px;
  font-size: 12px;
  color: #666;
  text-align: right;
  padding-right: 8px;
  flex-shrink: 0;
}

.score-bar-container {
  flex: 1;
  height: 16px;
  background-color: #f0f0f0;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.score-bar {
  height: 100%;
  border-radius: 10px;
  transition: width 0.5s;
}

.score-bar-text {
  position: absolute;
  right: 8px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  font-size: 13px;
  font-weight: bold;
  color: #333;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  padding: 0 6px;
  margin-right: 2px;
}
.img-box {
  width: auto;
  height: auto;
  margin: 1rem auto 0;
}

.intro {
  max-width: 1200px;
  margin: 0 auto;

  .social-list {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2.5rem 0;

    .iconfont {
      font-size: 2rem;
      margin: 5px;
    }
  }

  .zs-list {
    display: flex;

    .zs {
      display: inline-block;
      width: 120px;
      margin: 1rem;
    }

  }
}

.one {
  max-width: 1200px;
  margin: 2rem auto;

  .major {
    text-align: center;
    margin-bottom: 2rem;

    h1 {
      margin-bottom: 1rem;
    }

    p:nth-child(3) {
      margin-bottom: 1rem;
    }

    a {
      margin: .5rem;
    }
  }
}

/* 统一所有区域的宽度样式 */
.overview,
.notifications-overview,
.performance-overview,
.my-score,
.performance-analysis,
.charts,
.score-calculation {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 16px; /* 添加左右内边距确保在小屏幕上有适当间距 */
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.score-calculation {
  .score-card {
    height: 100%;
    min-height: 350px;
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-5px);
    }
    
    .score-card-title {
      display: flex;
      align-items: center;
      font-weight: bold;
      font-size: 16px;
    }
    
    p {
      margin-bottom: 8px;
      line-height: 1.5;
    }
    
    .time-period {
      margin-top: 12px;
      font-size: 0.85rem;
      color: #909399;
      font-style: italic;
      border-top: 1px dashed #e8e8e8;
      padding-top: 8px;
    }
    
    b {
      color: #333;
    }
    
    .deduction-note {
      background-color: #fff7e6;
      border-left: 3px solid #faad14;
      padding: 8px 10px;
      margin: 10px 0;
      border-radius: 0 4px 4px 0;
      font-size: 0.9rem;
      
      .highlight {
        color: #fa8c16;
        font-weight: bold;
      }
    }
    
    .table-title {
      font-weight: 500;
      color: #555;
      margin-top: 10px;
      margin-bottom: 5px;
      font-size: 0.95rem;
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .score-calculation {
    .score-card {
      margin-bottom: 16px;
    }
  }
}

.performance-overview {
  .notice-time {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }
  
  .section-spacing {
    margin-top: 24px;
    margin-bottom: 16px;
  }
}

// 通知相关样式
.notifications-overview {
  .recent-notifications-card {
    width: 100%; /* 确保卡片占满容器宽度 */

    .card-title {
      display: flex;
      align-items: center;
      font-weight: bold;
    }

    .ant-list-item {
      &:hover {
        background-color: #f5f5f5;
      }
    }

    .notification-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;

      .unread-title {
        font-weight: bold;
        color: #262626;
      }

      .notification-tags {
        display: flex;
        gap: 4px;
      }
    }

    .notification-content {
      .content-text {
        color: #666;
        margin-bottom: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.4;
        max-height: 2.8em;
      }

      .notification-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .notification-time {
          color: #999;
          font-size: 12px;
        }

        .notification-author {
          color: #666;
          font-size: 11px;
        }
      }
    }

    .empty-notifications {
      text-align: center;
      padding: 40px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .overview,
  .notifications-overview,
  .performance-overview,
  .my-score,
  .performance-analysis,
  .charts,
  .score-calculation {
    margin: 1rem;
    padding: 0 8px;
  }

  .notifications-overview {
    .notification-stats-card {
      .stat-item {
        .stat-number {
          font-size: 24px;
        }
      }
    }

    /* 响应式通知卡片样式已移除宽度限制 */
  }
}

/* my-score 样式已在统一样式中定义 */

.score-overview-card {
  height: 100%;
  
  .card-title {
    display: flex;
    align-items: center;
    font-weight: bold;
  }
  
  .total-score {
    text-align: center;
    padding: 16px 0;
    
    .score-number {
      font-size: 48px;
      font-weight: bold;
      color: #f5222d;
      line-height: 1.2;
    }
    
    .score-text {
      font-size: 16px;
      color: #666;
    }
  }
  
  .score-rank {
    .rank-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      .rank-label {
        color: #666;
      }
      
      .rank-value {
        font-weight: bold;
      }
    }
  }
}

.dimension-score-card {
  height: 100%;
  
  .dimension-item {
    display: flex;
    padding: 8px 0;
    
    .dimension-icon {
      margin-right: 12px;
      display: flex;
      align-items: center;
    }
    
    .dimension-info {
      flex: 1;
      
      .dimension-name {
        font-size: 14px;
        margin-bottom: 4px;
      }
      
      .dimension-score {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 4px;
      }
    }
  }
}

.trend-card, .suggestion-card {
  height: 100%;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  
  .ant-tag {
    margin-right: 8px;
    margin-top: 3px;
  }
}

/* H-扣分折叠面板样式 */
:deep(.ant-collapse) {
  background-color: transparent;
  border: none;
}

:deep(.ant-collapse-item) {
  border-radius: 6px;
  margin-bottom: 10px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

:deep(.ant-collapse-header) {
  background-color: #f9f9f9;
  padding: 12px 16px !important;
  transition: all 0.3s;
}

:deep(.ant-collapse-header:hover) {
  background-color: #f0f5ff;
}

:deep(.ant-collapse-content) {
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-collapse-content-box) {
  padding: 16px 20px !important;
}

.rule-content, .standard-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  color: #333;
  line-height: 1.6;
}

.rule-content p, .standard-content p {
  margin-bottom: 12px;
}

.rule-content ol, .standard-content ol {
  padding-left: 20px;
  margin-bottom: 12px;
}

.rule-content ol li, .standard-content ol li {
  margin-bottom: 8px;
}

.deduction-note {
  background-color: #fffbe6;
  border-left: 3px solid #faad14;
  padding: 10px 16px;
  margin: 12px 0;
}

.deduction-note .highlight {
  color: #fa8c16;
  font-weight: bold;
}

.time-period {
  color: #666;
  font-style: italic;
}

.table-title {
  margin: 8px 0 5px 0;
  color: #555;
  font-weight: 500;
  font-size: 14px;
}

.cancel-qualification-list {
  background-color: #fff1f0;
  border-radius: 4px;
  padding: 8px 16px 8px 30px;
  margin: 0;
  list-style-type: disc;
}

.cancel-qualification-list li {
  margin-bottom: 6px;
  line-height: 1.5;
  color: #333;
}

.deduction-detail {
  margin-top: 8px;
  color: #555;
  font-size: 0.9rem;
  border-top: 1px dashed #eee;
  padding-top: 8px;
}

.detail-list {
  padding-left: 20px;
  margin: 5px 0;
}

.detail-list li {
  margin-bottom: 3px;
  line-height: 1.4;
}

/* 为表格设置固定高度，防止内容过多导致卡片高度不一 */
.standard-content .ant-table-wrapper {
  margin-bottom: 12px;
  height: 120px;
  overflow-y: auto;
}

/* ==================== 绩效分析模块样式 ==================== */

// 导入公共样式
@import '@/styles/performance-common.scss';
</style>
