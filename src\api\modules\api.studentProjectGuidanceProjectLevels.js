import request from '../server'

/**
 * 获取立项级别列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getProjectLevels(params) {
  return request.get('/studentProjectGuidanceProjectLevels/levels', { params })
}

/**
 * 获取所有立项级别（不分页）
 * @returns {Promise} - 请求结果
 */
export function getAllProjectLevels() {
  return request.get('/studentProjectGuidanceProjectLevels/allLevels')
}

/**
 * 获取所有级别及其立项数量
 * @returns {Promise} - 请求结果
 */
export function getLevelsWithCount() {
  return request.get('/studentProjectGuidanceProjectLevels/levels-with-count')
}

/**
 * 获取立项级别详情
 * @param {string} id - 级别ID
 * @returns {Promise} - 请求结果
 */
export function getProjectLevelDetail(id) {
  return request.get(`/studentProjectGuidanceProjectLevels/level/${id}`)
}

/**
 * 创建立项级别
 * @param {Object} data - 级别数据
 * @returns {Promise} - 请求结果
 */
export function createProjectLevel(data) {
  return request.post('/studentProjectGuidanceProjectLevels/level/create', data)
}

/**
 * 更新立项级别
 * @param {string} id - 级别ID
 * @param {Object} data - 更新数据
 * @returns {Promise} - 请求结果
 */
export function updateProjectLevel(id, data) {
  return request.post('/studentProjectGuidanceProjectLevels/level/update', { id, ...data })
}

/**
 * 删除立项级别
 * @param {string} id - 级别ID
 * @returns {Promise} - 请求结果
 */
export function deleteProjectLevel(id) {
  return request.post('/studentProjectGuidanceProjectLevels/level/delete', { id })
}

/**
 * 获取立项级别分布数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getLevelDistribution(params = {}) {
  return request.post('/studentProjectGuidanceProjectLevels/statistics/distribution', params)
}