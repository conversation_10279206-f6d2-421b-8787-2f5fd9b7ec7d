<template>
  <a-modal
    :visible="visible"
    title="审核项目"
    @ok="handleSubmit"
    @cancel="$emit('cancel')"
    :confirmLoading="loading"
    :width="600"
  >
    <div>
      <!-- <p style="margin-bottom: 10px">您正在审核"{{ record.projectName }}"项目</p> -->
      
      <div style="margin-bottom: 10px">
        <span style="display: inline-block; width: 80px">审核结果：</span>
        <a-radio-group v-model:value="reviewForm.reviewStatus">
          <a-radio :value="1">通过</a-radio>
          <a-radio :value="0">拒绝</a-radio>
        </a-radio-group>
      </div>
      
      <div style="margin-bottom: 15px">
        <span style="display: inline-block; width: 80px; vertical-align: top">审核意见：</span>
        <a-textarea
          v-model:value="reviewForm.reviewComment"
          :rows="3"
          placeholder="请输入审核意见"
        />
      </div>
      
      <!-- 附件列表 -->
      <div v-if="attachments && attachments.length > 0">
        <div style="font-weight: bold; margin-bottom: 10px">项目附件:</div>
        <div style="max-height: 200px; overflow: auto">
          <a-table
            :dataSource="attachments"
            :columns="fileColumns"
            :pagination="false"
            size="small"
            rowKey="uid"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'fileName'">
                <span :title="record.name">{{ record.name }}</span>
              </template>
              <template v-if="column.key === 'fileSize'">
                {{ formatFileSize(record.data?.size || 0) }}
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="previewFileById(record.data.id)">
                    <template #icon><eye-outlined /></template>
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="downloadFileById(record.data.id)">
                    <template #icon><download-outlined /></template>
                    下载
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <div v-else style="color: #999; margin-top: 10px">该项目没有附件</div>
    </div>
  </a-modal>
</template>

<script>
import { ref, defineComponent, watch } from 'vue';
import { message } from 'ant-design-vue';
import { EyeOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import {previewFileById,downloadFileById } from '@/utils/others';

export default defineComponent({
  name: 'ProjectReviewModal',
  components: {
    EyeOutlined,
    DownloadOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    },
    attachments: {
      type: Array,
      default: () => []
    }
  },
  emits: ['cancel', 'submit'],
  setup(props, { emit }) {
    const loading = ref(false);
    
    const reviewForm = ref({
      reviewStatus: 1, // 默认通过
      reviewComment: ''
    });
    
    watch(() => props.visible, (newVisible) => {
      if(newVisible) {
        reviewForm.value = {
          reviewStatus: 1,
          reviewComment: ''
        };
        loading.value = false;
      }
    });
    
    const fileColumns = [
      {
        title: '文件名',
        dataIndex: 'name',
        key: 'fileName',
        ellipsis: true
      },
      {
        title: '大小',
        key: 'fileSize',
        width: 120
      },
      {
        title: '操作',
        key: 'action',
        width: 150
      }
    ];
    
    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '0 B';
      
      const units = ['B', 'KB', 'MB', 'GB', 'TB'];
      let unitIndex = 0;
      
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }
      
      return `${size.toFixed(2)} ${units[unitIndex]}`;
    };
    
    // 提交审核
    const handleSubmit = () => {
      loading.value = true;
      // 输出调试信息，查看表单数据
      console.log('提交审核数据:', {
        id: props.record.id,
        reviewStatus: reviewForm.value.reviewStatus,
        reviewComment: reviewForm.value.reviewComment,
      });
      
      emit('submit', {
        id: props.record.id,
        reviewStatus: reviewForm.value.reviewStatus,
        reviewComment: reviewForm.value.reviewComment
      });
    };
    
    return {
      loading,
      reviewForm,
      fileColumns,
      formatFileSize,
      previewFileById,
      downloadFileById,
      handleSubmit
    };
  }
});
</script> 