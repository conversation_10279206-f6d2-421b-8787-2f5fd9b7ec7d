import request from '../server'

/**
 * 获取项目详情
 * @param {string} id - 项目ID
 * @returns {Promise} - 请求结果
 */
export function getProjectDetail(id) {
  return request.post('/teachingReformProjects/project/detail', { id: id })
}

/**
 * 添加项目（支持文件上传）
 * @param {FormData} data - 项目数据，包含文件
 * @returns {Promise} - 请求结果
 */
export function addProject(data) {
  return request.post('/teachingReformProjects/project/create', data)
}

/**
 * 更新项目（支持文件上传）
 * @param {string} id - 项目ID
 * @param {FormData} data - 包含id和文件的FormData对象
 * @returns {Promise} - 请求结果
 */
export function updateProject(id, data) {
  return request.post(`/teachingReformProjects/project/update/${id}`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 删除项目
 * @param {string} id - 项目ID
 * @returns {Promise} - 请求结果
 */
export function deleteProject(id) {
  return request.delete(`/teachingReformProjects/project/delete/${id}`)
}

/**
 * 审核项目
 * @param {string} id - 项目ID
 * @param {Object} data - 额外数据，包含审核人信息
 * @returns {Promise} - 请求结果
 */
export function reviewProject(data) {
  return request.post('/teachingReformProjects/project/review', data)
}

/**
 * 重新提交审核
 * @param {Object} params - 包含id的参数对象
 * @returns {Promise} - 请求结果
 */
export function reapplyReview(params) {
  return request.post('/teachingReformProjects/project/reapply', params)
}

/**
 * 导入项目数据
 * @param {File} file - 文件对象
 * @returns {Promise} - 请求结果
 */
export function importProjects(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/teachingReformProjects/projects/import', formData, null, 'multipart/form-data')
}

/**
 * 导出项目数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function exportProjects(params) {
  return request.get('/teachingReformProjects/projects/export', {
    params,
    responseType: 'blob'
  })
}

/**
 * 获取教学改革项目列表
 * @param {Object} params - 查询参数
 * @param {string} [params.projectName] - 项目名称（模糊搜索）
 * @param {string} [params.levelId] - 项目级别ID
 * @param {string} [params.approvalDateStart] - 获批开始日期
 * @param {string} [params.approvalDateEnd] - 获批结束日期
 * @param {string} [params.userId] - 用户ID（可选，传入则获取用户参与的项目）
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @param {string} [params.range='all'] - 范围筛选，可选值：all, in, out
 * @param {string} [params.reviewStatus='all'] - 审核状态筛选，可选值：all, reviewed, unreviewed
 * @param {string} [params.sortField='approvalDate'] - 排序字段
 * @param {string} [params.sortOrder='desc'] - 排序方向，'asc' 或 'desc'
 * @returns {Promise} - 请求结果
 */
export function getProjects(params) {
  return request.post('/teachingReformProjects/list', params)
}

/**
 * 获取项目时间分布数据
 * @param {Object} data - 请求参数
 * @param {string} data.range - 数据范围: 'in', 'out', 'all'
 * @param {string} [data.userId] - 用户ID，用于过滤特定用户的数据
 * @param {Object} [data.timeRange] - 自定义时间范围
 * @param {string} [data.timeRange.startDate] - 开始日期，格式 YYYY-MM-DD
 * @param {string} [data.timeRange.endDate] - 结束日期，格式 YYYY-MM-DD
 * @returns {Promise} - 请求结果
 */
export function getProjectTimeDistribution(data) {
  return request.post('/teachingReformProjects/statistics/time-distribution', data)
}

/**
 * 获取项目时间分布数据（别名，与getProjectTimeDistribution功能相同）
 * @param {Object} data - 请求参数
 * @param {string} data.range - 数据范围: 'in', 'out', 'all'
 * @param {string} [data.userId] - 用户ID，用于过滤特定用户的数据
 * @param {Object} [data.timeRange] - 自定义时间范围
 * @returns {Promise} - 请求结果
 */
export function getTimeDistribution(data) {
  return getProjectTimeDistribution(data)
}

/**
 * 获取项目级别分布数据
 * @param {Object} data - 请求参数
 * @param {string} data.range - 数据范围: 'in', 'out', 'all'
 * @param {string} [data.userId] - 用户ID，用于过滤特定用户的数据
 * @param {boolean|string} [data.reviewStatus] - 审核状态筛选
 * @param {Object} [data.timeRange] - 自定义时间范围
 * @returns {Promise} - 请求结果
 */
export function getLevelDistribution(data) {
  return request.post('/teachingReformProjects/statistics/level-distribution', data)
}

/**
 * 获取教师项目排名数据
 * @param {Object} params 参数对象
 * @param {string} params.range 统计范围 'all', 'in', 'out'
 * @param {string} params.reviewStatus 审核状态 'all', 'reviewed', 'unreviewed'
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页条数
 * @param {boolean} params.isExport 是否导出，导出时不应用分页
 * @returns {Promise} 请求结果
 */
export function getTeacherProjectRanking(params) {
  return request.post('/teachingReformProjects/statistics/teacher-ranking', params);
}

/**
 * 获取教师项目详情
 * @param {Object} params - 请求参数
 * @param {string} params.userId - 用户ID
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页记录数
 * @param {string} params.range - 数据范围: 'in', 'out', 'all'
 * @param {boolean|string} [params.reviewStatus] - 审核状态
 * @param {Object} [params.timeRange] - 自定义时间范围
 * @returns {Promise} - 请求结果
 */
export function getTeacherProjectDetails(params) {
  return request.post('/teachingReformProjects/statistics/teacher-projects', params)
}

/**
 * 获取项目统计数据
 * @param {Object} params - 请求参数
 * @param {string} [params.userId] - 特定用户的ID
 * @param {string} [params.range] - 数据范围: 'in', 'out', 'all'
 * @param {Object} [params.timeRange] - 自定义时间范围
 * @returns {Promise} - 请求结果
 */
export function getProjectStatistics(params = {}) {
  return request.post('/teachingReformProjects/statistics/overview', params)
}

// 为了兼容之前的代码，保留旧的函数名，但内部调用新的getProjects函数
/**
 * 获取当前用户参与的项目列表（兼容旧代码，请使用getProjects）
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getUserProjects(params) {
  return getProjects(params)
}

/**
 * 获取所有项目列表（兼容旧代码，请使用getProjects）
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getAllProjects(params) {
  return getProjects(params)
}

/**
 * 获取项目总分统计
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getProjectTotalScore(params) {
  return request.post('/teachingReformProjects/statistics/projects-total-score', params);
}

/**
 * 获取用户项目详情
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getUserProjectDetails(params) {
  return request.post('/teachingReformProjects/user/details', params);
}



/**
 * 获取审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getReviewStatusOverview(params) {
  return request.post('/teachingReformProjects/statistics/review-status-overview', params)
}
