import request from '../server'

// API 路径配置
const api = {
  list: '/internationalExchangesRules/list',
  detail: '/internationalExchangesRules/detail',
  create: '/internationalExchangesRules/create',
  update: '/internationalExchangesRules/update',
  delete: '/internationalExchangesRules/delete',
  batchDelete: '/internationalExchangesRules/batch-delete',
  import: '/internationalExchangesRules/import',
  export: '/internationalExchangesRules/export'
}

/**
 * 获取国际交流规则列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getInternationalExchangesRules(params) {
  const { page = 1, pageSize = 10, project } = params || {};
  
  // 构造参数对象
  const queryParams = {
    page,
    pageSize,
    project
  };
  console.log("queryParams:",queryParams);
  return request.get(api.list, queryParams);
}

/**
 * 获取国际交流规则详情
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function getInternationalExchangeRuleDetail(id) {
  return request.get(api.detail, { id });
}

/**
 * 创建国际交流规则
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function addInternationalExchangeRule(data) {
  return request.post(api.create, data);
}

/**
 * 更新国际交流规则
 * @param {string} id - 规则ID
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateInternationalExchangeRule(id, data) {
  return request.put(api.update, { id, ...data });
}

/**
 * 删除国际交流规则
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteInternationalExchangeRule(id) {
  return request.delete(api.delete, { id });
}

/**
 * 批量删除国际交流规则
 * @param {Array} ids - 规则ID数组
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteInternationalExchangeRules(ids) {
  return request.delete(api.batchDelete, { ids });
}

/**
 * 导入国际交流规则数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回Promise对象
 */
export function importInternationalExchangeRules(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request.post(api.import, formData, null, 'multipart/form-data');
}

/**
 * 导出国际交流规则数据
 * @param {Object} params - 过滤参数
 * @returns {Promise} - 返回Promise对象
 */
export function exportInternationalExchangeRules(params) {
  return request.get(api.export, params, { responseType: 'blob' });
} 