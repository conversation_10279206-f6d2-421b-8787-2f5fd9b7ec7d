const express = require('express');
const router = express.Router();
const projectLevelController = require('../../../controllers/v1/teachingReformProjects/teachingReformProjectLevelsController');

/**
 * 获取项目级别列表
 * @route GET /v1/sys/teaching-reform-levels/levels
 * @group 教学改革项目级别管理 - 教学改革项目级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels', projectLevelController.getProjectLevels);

/**
 * 获取所有级别及其项目数量
 * @route GET /v1/sys/teaching-reform-levels/levels-with-count
 * @group 教学改革项目级别管理 - 教学改革项目级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id, levelName, score, remark, projectCount},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels-with-count', projectLevelController.getLevelsWithCount);

/**
 * 获取项目级别详情
 * @route GET /v1/sys/teaching-reform-levels/level/:id
 * @group 教学改革项目级别管理 - 教学改革项目级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/:id', projectLevelController.getLevelDetail);

/**
 * 创建项目级别
 * @route POST /v1/sys/teaching-reform-levels/level/create
 * @group 教学改革项目级别管理 - 教学改革项目级别相关接口
 * @param {string} levelName.body.required - 级别名称
 * @param {number} score.body.required - 级别分数
 * @param {string} remark.body - 备注
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/create', projectLevelController.createLevel);

/**
 * 更新项目级别
 * @route POST /v1/sys/teaching-reform-levels/level/update
 * @group 教学改革项目级别管理 - 教学改革项目级别相关接口
 * @param {string} id.body.required - 级别ID
 * @param {string} levelName.body - 级别名称
 * @param {number} score.body - 级别分数
 * @param {string} remark.body - 备注
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/update', async (req, res) => {
  const { id, ...updateData } = req.body;
  req.params = { id };
  req.body = updateData;
  await projectLevelController.updateLevel(req, res);
});

/**
 * 删除项目级别
 * @route POST /v1/sys/teaching-reform-levels/level/delete
 * @group 教学改革项目级别管理 - 教学改革项目级别相关接口
 * @param {string} id.body.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/delete', async (req, res) => {
  const { id } = req.body;
  req.params = { id };
  await projectLevelController.deleteLevel(req, res);
});

/**
 * 获取项目级别分布数据
 * @route POST /v1/sys/teaching-reform-levels/statistics/distribution
 * @group 教学改革项目级别统计 - 教学改革项目级别统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的项目
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "级别名称", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/distribution', projectLevelController.getLevelDistribution);

/**
 * 获取级别下的项目列表
 * @route GET /v1/sys/teaching-reform-levels/level/:id/projects
 * @group 教学改革项目级别管理 - 教学改革项目级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/:id/projects', projectLevelController.getProjectsByLevel);

module.exports = router; 