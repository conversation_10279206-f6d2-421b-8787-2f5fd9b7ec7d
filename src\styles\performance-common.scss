// 绩效管理页面公共样式
// 用于高水平论文、科研项目等页面的样式复用

// 引入其他样式模块
@import './performance-layout.scss';
@import './performance-charts.scss';
@import './performance-forms.scss';
@import './performance-import-export.scss';
@import './performance-analysis.scss';
@import './home-page.scss';
@import './ranking-table.scss';

// 基础变量定义
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --text-color: #262626;
  --text-color-secondary: #666;
  --border-color: #f0f0f0;
  --background-color: #fafafa;
}

// 页面容器
.performance-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

// 卡片样式
.performance-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-weight: 600;
      font-size: 16px;
    }
  }
}

// 统计卡片
.stats-card {
  text-align: center;
  
  .stats-number {
    font-size: 32px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 8px;
  }
  
  .stats-label {
    font-size: 14px;
    color: #666;
  }
  
  &.success {
    .stats-number {
      color: #52c41a;
    }
  }
  
  &.warning {
    .stats-number {
      color: #faad14;
    }
  }
  
  &.error {
    .stats-number {
      color: #f5222d;
    }
  }
}

// 图表容器
.chart-container {
  .chart-wrapper {
    height: 350px;
    width: 100%;
    position: relative;
    padding-bottom: 20px; // 为横坐标文字留出空间
  }

  .chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 350px;
    color: #999;
  }

  .chart-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 350px;
    color: #999;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
  }
}

// 影响因子图表特殊样式
.impact-factor-chart {
  .chart-wrapper {
    height: 380px !important; // 增加高度以容纳横坐标文字
    padding-bottom: 30px; // 增加底部间距
  }
}

// ECharts 图表通用样式优化
.echarts-container {
  canvas {
    // 确保画布不会超出容器
    max-width: 100% !important;
    max-height: 100% !important;
  }
}

// 搜索表单
.search-form {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;

  .ant-form-item {
    margin-bottom: 12px;
  }

  .ant-form-item-label {
    padding-bottom: 2px;

    > label {
      font-size: 13px;
      font-weight: 500;
    }
  }
  
  .search-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 16px;

    .ant-btn {
      min-width: 80px;
    }
  }

  .search-actions-inline {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;

    .ant-btn {
      min-width: 70px;
      height: 32px;
    }
  }
}

// 表格样式
.performance-table {
  background: #fff;
  border-radius: 8px;
  
  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      border-bottom: 2px solid #f0f0f0;
    }
    
    .ant-table-tbody > tr:hover > td {
      background: #f5f5f5;
    }
  }
  
  .table-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-info {
      color: #666;
      font-size: 14px;
    }
  }

  // 表格单元格样式
  :deep(.ant-table-cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // 错误行样式
  :deep(.error-row) {
    background-color: rgba(245, 34, 45, 0.05);
  }

  :deep(.error-row:hover > td) {
    background-color: rgba(245, 34, 45, 0.1) !important;
  }

  // 导入错误行样式
  .import-row-error {
    background-color: rgba(245, 34, 45, 0.05);
  }

  .import-row-error:hover > td {
    background-color: rgba(245, 34, 45, 0.1) !important;
  }

  // 禁用表格悬浮效果
  &.no-hover-table {
    .ant-table {
      .ant-table-tbody > tr:hover > td {
        background-color: transparent !important;
        background: transparent !important;
      }
    }

    :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
      background-color: transparent !important;
      background: transparent !important;
    }

    :deep(.ant-table-tbody > tr.ant-table-row:hover) {
      background-color: transparent !important;
      background: transparent !important;
    }
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  gap: 8px;

  .ant-btn {
    border-radius: 4px;

    &.primary {
      background: #1890ff;
      border-color: #1890ff;
    }

    &.success {
      background: #52c41a;
      border-color: #52c41a;
      color: #fff;
    }

    &.warning {
      background: #faad14;
      border-color: #faad14;
      color: #fff;
    }

    &.danger {
      background: #f5222d;
      border-color: #f5222d;
      color: #fff;
    }
  }
}

// 表格操作下拉菜单
.action-dropdown-menu {
  min-width: 160px !important;
  max-width: 200px !important;

  .ant-dropdown-menu-item {
    padding: 8px 12px !important;
    line-height: 1.5 !important;
    min-height: 32px !important;

    .action-menu-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #333;
      text-decoration: none;
      font-size: 13px;

      &:hover {
        color: #1890ff;
      }

      &.text-danger {
        color: #f5222d;

        &:hover {
          color: #ff7875;
        }
      }

      .anticon {
        font-size: 13px;
        width: 13px;
        display: inline-flex;
        justify-content: center;
        flex-shrink: 0;
      }

      span {
        white-space: nowrap;
        flex: 1;
      }
    }
  }
}

// 操作触发按钮
.action-trigger-btn {
  padding: 4px 12px !important;
  height: auto !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  min-width: 80px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: space-between !important;

  .anticon {
    font-size: 10px;
    margin-left: 8px;
    flex-shrink: 0;
  }
}

// 状态标签
.status-tag {
  &.pending {
    background: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
  }
  
  &.approved {
    background: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  
  &.rejected {
    background: #fff2f0;
    border-color: #ffccc7;
    color: #f5222d;
  }
}

// 模态框样式
.performance-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-modal-title {
      font-weight: 600;
      font-size: 16px;
    }
  }
  
  .ant-modal-body {
    padding: 24px;
  }
  
  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    
    .ant-btn {
      margin-left: 8px;
    }
  }
}

// 表单样式
.performance-form {
  .ant-form-item-label > label {
    font-weight: 500;
  }

  .ant-form-item-explain {
    font-size: 12px;
  }

  .form-section {
    margin-bottom: 32px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }
  }

  .form-actions {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;

    .ant-btn {
      margin: 0 8px;
      min-width: 100px;
    }
  }
}

// 自定义表单区域样式
.form-section {
  display: flex;
  margin-bottom: 24px;
}

.form-section-label {
  flex: 0 0 20.83%;
  max-width: 20.83%;
  text-align: right;
  padding-right: 12px;
  line-height: 32px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.85);
  box-sizing: border-box;
}

.form-section-content {
  flex: 1;
  max-width: 75%;
}

// 作者管理相关样式
.authors-container {
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;

  .ant-list {
    max-height: 200px;
    overflow-y: auto;
  }
}

.author-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.author-actions {
  display: flex;
  align-items: center;
  height: 32px;

  .ant-btn {
    margin-right: 16px;
  }
}

.author-roles {
  display: flex;
  gap: 16px;
  align-items: center;
}

// 文件上传
.file-upload {
  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    
    &:hover {
      border-color: #1890ff;
    }
  }
  
  .upload-hint {
    color: #999;
    font-size: 12px;
    margin-top: 8px;
  }
}

// 统计卡片样式
.ant-statistic-title {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

// 图表卡片样式
.ant-card-small .ant-card-head {
  min-height: 48px;
  padding: 0 16px;
}

.ant-card-small .ant-card-body {
  padding: 16px;
}

// 表格总分显示
.total-score {
  text-align: right;
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
}

// 期刊名称换行显示
.journal-cell {
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.4;
  max-width: 180px;
}

// 页面容器样式
.performance-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 搜索卡片样式
.search-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 6px;

  :deep(.ant-card-body) {
    padding: 16px 20px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 12px;
  }

  :deep(.ant-form-item-label) {
    padding-bottom: 4px;
    font-weight: 500;
  }
}

// 表格底部信息
.table-footer {
  margin-top: 16px;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 作者角色复选框样式
.ant-table .author-roles {
  display: flex;
  flex-direction: row;
  gap: 8px;
  white-space: nowrap;
}

.author-actions .author-roles {
  display: flex;
  flex-direction: row;
  gap: 8px;
  white-space: nowrap;
  flex-wrap: nowrap;
}

// 通用文本样式
.text-danger {
  color: #ff4d4f;
}

// 响应式设计
@media (max-width: 1200px) {
  .search-actions-inline {
    .ant-btn {
      min-width: 60px;
      font-size: 12px;
    }
  }
}

@media (max-width: 992px) {
  .search-form {
    .ant-row {
      .ant-col {
        margin-bottom: 8px;
      }
    }
  }

  .search-actions-inline {
    justify-content: flex-start;

    .ant-btn {
      min-width: 65px;
    }
  }
}

@media (max-width: 768px) {
  .performance-container {
    padding: 8px;
  }

  .search-form {
    padding: 16px;
  }

  .chart-container .chart-wrapper {
    height: 250px;
  }

  // 移动端影响因子图表优化
  .impact-factor-chart .chart-wrapper {
    height: 280px !important;
    padding-bottom: 25px;
  }

  .search-actions {
    flex-direction: column;

    .ant-btn {
      width: 100%;
    }
  }

  .search-actions-inline {
    flex-direction: column;
    gap: 8px;

    .ant-btn {
      width: 100%;
      min-width: auto;
    }
  }

  .action-buttons {
    flex-direction: column;

    .ant-btn {
      width: 100%;
    }
  }

  // 表单区域响应式
  .form-section {
    flex-direction: column;
  }

  .form-section-label {
    width: 100%;
    text-align: left;
    padding-right: 0;
    margin-bottom: 8px;
  }

  .form-section-content {
    width: 100%;
  }

  .author-actions {
    flex-wrap: wrap;
    gap: 8px;
  }

  .author-roles {
    gap: 8px;
  }

  // 列布局响应式
  .ant-col {
    margin-bottom: 16px;
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: #1890ff; }
.text-success { color: #52c41a; }
.text-warning { color: #faad14; }
.text-danger { color: #f5222d; }
.text-muted { color: #999; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }
.mb-32 { margin-bottom: 32px; }

.mt-8 { margin-top: 8px; }
.mt-16 { margin-top: 16px; }
.mt-24 { margin-top: 24px; }
.mt-32 { margin-top: 32px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }
.p-24 { padding: 24px; }

.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }
