const { checkAccessAndData } = require('./permissionMiddleware');
const { getUserInfoFromRequest } = require('../utils/others');
// 导入整个权限配置目录
const permissionsConfig = require('../permissions');

/**
 * 创建模块权限中间件，包含数据访问控制
 * @param {string} module - 模块名称 (如 'researchProjects')
 * @param {string} action - 操作名称 (如 'list', 'create', 'update' 等)
 * @returns {Function} Express中间件函数
 */
const createModulePermission = (module, action) => {
  
  // 检查模块和操作的配置是否存在
  if (!permissionsConfig[module] || !permissionsConfig[module][action]) {
    console.warn(`警告: 未找到 ${module}.${action} 的权限配置`);
    // 返回一个默认的权限检查中间件
    return async (req, res, next) => {
      try {
        console.log(`执行默认权限检查，模块: ${module}, 操作: ${action}`);
        // 仍然验证用户，确保请求是经过身份验证的
        const userInfo = await getUserInfoFromRequest(req);
        if (!userInfo) {
          return res.status(401).json({
            code: 401,
            message: '用户未认证',
            data: null
          });
        }
        req.verifiedUser = userInfo;
        next();
      } catch (error) {
        next(error);
      }
    };
  }
  
  return checkAccessAndData(permissionsConfig[module][action]);
};

module.exports = createModulePermission;
