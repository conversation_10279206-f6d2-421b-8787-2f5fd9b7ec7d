/**
 * 权限检查钩子
 * 用于在Vue组件中检查用户权限
 */

import { computed } from 'vue'
import dbUtils from '@/libs/util.strotage'

/**
 * 权限检查钩子
 * @returns {Object} 包含权限检查方法的对象
 */
export function usePermission() {
  
  /**
   * 检查用户是否具有指定权限
   * @param {string} permission - 权限标识，如 'teaching_research_awards:create'
   * @returns {boolean} 是否具有权限
   */
  const hasPermission = (permission) => {
    // 获取用户权限列表
    const permissionList = dbUtils.get('perms') || []
    
    // 如果权限列表为空，返回false
    if (!Array.isArray(permissionList) || permissionList.length === 0) {
      return false
    }
    
    // 超级权限判断
    if (permissionList.includes('*')) {
      return true
    }
    
    // 精确匹配
    if (permissionList.includes(permission)) {
      return true
    }
    
    // 如果是多级权限（包含冒号），尝试前缀匹配
    // 例如 teaching_research_awards:create 可以匹配 teaching_research_awards
    if (permission.includes(':')) {
      const parts = permission.split(':')
      // 检查父级权限
      for (let i = parts.length - 1; i > 0; i--) {
        const parentPerm = parts.slice(0, i).join(':')
        if (permissionList.includes(parentPerm)) {
          return true
        }
      }
    }
    
    return false
  }
  
  /**
   * 检查用户是否具有任意一个权限
   * @param {Array<string>} permissions - 权限标识数组
   * @returns {boolean} 是否具有任意一个权限
   */
  const hasAnyPermission = (permissions) => {
    if (!Array.isArray(permissions) || permissions.length === 0) {
      return false
    }
    
    return permissions.some(permission => hasPermission(permission))
  }
  
  /**
   * 检查用户是否具有所有权限
   * @param {Array<string>} permissions - 权限标识数组
   * @returns {boolean} 是否具有所有权限
   */
  const hasAllPermissions = (permissions) => {
    if (!Array.isArray(permissions) || permissions.length === 0) {
      return false
    }
    
    return permissions.every(permission => hasPermission(permission))
  }
  
  /**
   * 获取当前用户的所有权限
   * @returns {Array<string>} 权限列表
   */
  const getUserPermissions = () => {
    return dbUtils.get('perms') || []
  }
  
  /**
   * 响应式的权限列表
   */
  const permissions = computed(() => getUserPermissions())
  
  /**
   * 检查是否为超级管理员
   * @returns {boolean} 是否为超级管理员
   */
  const isSuperAdmin = computed(() => {
    const permissionList = getUserPermissions()
    return permissionList.includes('*')
  })
  
  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getUserPermissions,
    permissions,
    isSuperAdmin
  }
}
