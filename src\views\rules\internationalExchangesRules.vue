<template>
  <div class="app-container">
    <!-- 错误信息展示 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
    
    <a-card title="国际交流规则管理">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><plus-outlined /></template>
            新增规则
          </a-button>
        </a-space>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form-wrapper">
        <a-form layout="inline">
          <a-row :gutter="16" style="width: 100%">
            <a-col :span="8">
              <a-form-item label="交流类型">
                <a-input v-model:value="searchForm.exchangeType" placeholder="请输入交流类型" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item>
                <a-button type="primary" @click="handleSearch">搜索</a-button>
                <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="showEditModal(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这条规则吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
          <template v-else-if="column.key === 'description'">
            <a-tooltip v-if="record.description && record.description.length > 20">
              <template #title>{{ record.description }}</template>
              <span>{{ record.description.substring(0, 20) }}...</span>
            </a-tooltip>
            <span v-else>{{ record.description || '--' }}</span>
          </template>
          <template v-else-if="column.key === 'maxPeople'">
            <a-tooltip v-if="record.maxPeople && record.maxPeople.length > 20">
              <template #title>{{ record.maxPeople }}</template>
              <span>{{ record.maxPeople.substring(0, 20) }}...</span>
            </a-tooltip>
            <span v-else>{{ record.maxPeople || '--' }}</span>
          </template>
          <template v-else-if="column.key === 'createdAt'">
            <span>{{ formatDate(record.createdAt) }}</span>
          </template>
        </template>
      </a-table>

      <!-- 新增/编辑模态框 -->
      <a-modal
        :title="modalTitle"
        :visible="modalVisible"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="confirmLoading"
        width="800px"
      >
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="交流类型" name="exchangeType">
                <a-input 
                  v-model:value="formState.exchangeType" 
                  :maxLength="255"
                  placeholder="请输入交流类型" 
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="基础分数" name="baseScore">
                <a-input-number
                  v-model:value="formState.baseScore"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%"
                  placeholder="请输入基础分数"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="详细描述" name="description">
            <a-textarea
              v-model:value="formState.description"
              :rows="4"
              placeholder="请输入交流类型的详细描述"
            />
          </a-form-item>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="合作单位数目" name="unitCount">
                <a-input-number
                  v-model:value="formState.unitCount"
                  :min="1"
                  :precision="0"
                  style="width: 100%"
                  placeholder="请输入合作单位数目"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { 
  PlusOutlined, 
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { 
  getInternationalExchangesRules, 
  getInternationalExchangeRuleDetail, 
  addInternationalExchangeRule, 
  updateInternationalExchangeRule, 
  deleteInternationalExchangeRule 
} from "@/api/rules/internationalExchangesRules";

// 错误信息
const errorMessage = ref('');

// 表格列定义
const columns = [
  {
    title: '项目名称',
    dataIndex: 'project',
    key: 'project',
    width: 200,
  },
  {
    title: '核算分数',
    dataIndex: 'score',
    key: 'score',
    width: 100,
  },
  {
    title: '详细描述',
    key: 'description',
    width: 200,
  },
  {
    title: '最大记录人数',
    key: 'maxPeople',
    width: 10,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
];

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 搜索表单
const searchForm = reactive({
  exchangeType: '',
});

// 表单状态
const formState = reactive({
  id: '',
  project: '',
  score: undefined,
  description: '',
  maxPeople: '',
  exchangeType: '',
  baseScore: undefined,
  unitCount: '',
});

// 表单验证规则
const rules = {
  project: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  score: [{ required: true, message: '请输入核算分数', trigger: 'blur' }],
};

// 模态框状态
const modalVisible = ref(false);
const modalTitle = ref('新增规则');
const confirmLoading = ref(false);
const formRef = ref(null);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  errorMessage.value = '';
  
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      exchangeType: searchForm.exchangeType,
    };
    
    const response = await getInternationalExchangesRules(params);
    
    if (response && response.code === 200) {
      dataSource.value = response.data.list || [];
      pagination.total = response.data.total || 0;
    } else {
      message.error(response?.message || '获取数据失败');
      errorMessage.value = response?.message || '获取数据失败，请稍后重试';
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败：' + (error.message || '未知错误'));
    errorMessage.value = '获取数据失败：' + (error.message || '未知错误');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.exchangeType = '';
  handleSearch();
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 显示新增模态框
const showAddModal = () => {
  modalTitle.value = '新增规则';
  Object.keys(formState).forEach(key => {
    formState[key] = key === 'id' ? '' : (typeof formState[key] === 'string' ? '' : undefined);
  });
  modalVisible.value = true;
};

// 显示编辑模态框
const showEditModal = async (record) => {
  modalTitle.value = '编辑规则';
  try {
    const response = await getInternationalExchangeRuleDetail(record.id);
    if (response.code === 200) {
      formState.id = record.id;
      formState.project = response.data.project;
      formState.score = response.data.score;
      formState.description = response.data.description || '';
      formState.maxPeople = response.data.maxPeople || '';
      formState.exchangeType = response.data.exchangeType || '';
      formState.baseScore = response.data.baseScore || undefined;
      formState.unitCount = response.data.unitCount || '';
      modalVisible.value = true;
    } else {
      message.error(response.message || '获取规则详情失败');
    }
  } catch (error) {
    console.error('获取规则详情失败:', error);
    message.error('获取规则详情失败');
  }
};

// 模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate();
    confirmLoading.value = true;
    
    const formData = {
      project: formState.project,
      score: formState.score,
      description: formState.description,
      maxPeople: formState.maxPeople,
      exchangeType: formState.exchangeType,
      baseScore: formState.baseScore,
      unitCount: formState.unitCount,
    };
    
    let response;
    if (formState.id) {
      response = await updateInternationalExchangeRule(formState.id, formData);
    } else {
      response = await addInternationalExchangeRule(formData);
    }
    
    if (response.code === 200) {
      message.success('保存成功');
      modalVisible.value = false;
      fetchData();
    } else {
      message.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    confirmLoading.value = false;
  }
};

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 删除记录
const handleDelete = async (record) => {
  try {
    const response = await deleteInternationalExchangeRule(record.id);
    if (response.code === 200) {
      message.success('删除成功');
      fetchData();
    } else {
      message.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '--';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 页面加载时初始化
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.app-container {
  padding: 24px;
}
.search-form-wrapper {
  background-color: #f8f8f8;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
}
</style> 