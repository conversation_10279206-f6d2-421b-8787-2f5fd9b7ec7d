const express = require('express');
const router = express.Router();
const studentAwardGuidanceAwardLevelController = require('../../../controllers/v1/studentAwardGuidanceAwards/studentAwardGuidanceAwardLevelsController');

/**
 * 获取获奖级别列表
 * @route GET /v1/sys/student-award-guidance-levels/levels
 * @group 指导学生获奖级别管理 - 指导学生获奖级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels', studentAwardGuidanceAwardLevelController.getAwardLevels);

/**
 * 获取所有级别
 * @route GET /v1/sys/student-award-guidance-levels/all-levels
 * @group 指导学生获奖级别管理 - 指导学生获奖级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/all-levels', studentAwardGuidanceAwardLevelController.getAllAwardLevels);

/**
 * 获取所有级别及其获奖数量
 * @route GET /v1/sys/student-award-guidance-levels/levels-with-count
 * @group 指导学生获奖级别管理 - 指导学生获奖级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id, levelName, description, awardCount},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels-with-count', studentAwardGuidanceAwardLevelController.getLevelsWithCount);

/**
 * 获取获奖级别详情
 * @route GET /v1/sys/student-award-guidance-levels/level/:id
 * @group 指导学生获奖级别管理 - 指导学生获奖级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/:id', studentAwardGuidanceAwardLevelController.getLevelDetail);

/**
 * 创建获奖级别
 * @route POST /v1/sys/student-award-guidance-levels/level/create
 * @group 指导学生获奖级别管理 - 指导学生获奖级别相关接口
 * @param {string} levelName.body.required - 级别名称
 * @param {number} score.body.required - 级别分数
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/create', studentAwardGuidanceAwardLevelController.createLevel);

/**
 * 更新获奖级别
 * @route POST /v1/sys/student-award-guidance-levels/level/update
 * @group 指导学生获奖级别管理 - 指导学生获奖级别相关接口
 * @param {string} id.body.required - 级别ID
 * @param {string} levelName.body - 级别名称
 * @param {number} score.body - 级别分数
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/update', async (req, res) => {
  const { id, ...updateData } = req.body;
  req.params = { id };
  req.body = updateData;
  await studentAwardGuidanceAwardLevelController.updateLevel(req, res);
});

/**
 * 获取获奖级别分布数据
 * @route POST /v1/sys/student-award-guidance-levels/statistics/distribution
 * @group 指导学生获奖级别统计 - 指导学生获奖级别统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的获奖
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "级别名称", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/distribution', studentAwardGuidanceAwardLevelController.getLevelDistribution);

/**
 * 删除获奖级别
 * @route POST /v1/sys/student-award-guidance-levels/level/delete
 * @group 指导学生获奖级别管理 - 指导学生获奖级别相关接口
 * @param {string} id.body.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/delete', async (req, res) => {
  const { id } = req.body;
  req.params = { id };
  await studentAwardGuidanceAwardLevelController.deleteLevel(req, res);
});


module.exports = router; 