/**
 *<AUTHOR>
 *@date 2023/11/16 23:10
 *@status: 1 操作成功 0 操作失败
 */

// 成功响应
exports.successResponse = function (res, msg) {
    var data = {
        status: 1,
        message: msg
    };
    return res.status(200).json(data);
};

// 成功响应携带数据
exports.successResponseWithData = function (res, msg, data) {
    var resData = {
        status: 1,
        message: msg,
        data: data
    };
    return res.status(200).json(resData);
};

// 服务器内部错误
exports.errorResponse = function (res, msg) {
    var data = {
        status: 0,
        message: msg,
    };
    return res.status(500).json(data);
};

// 接口不存在
exports.notFoundResponse = function (res, msg) {
    var data = {
        status: 0,
        message: msg,
    };
    return res.status(404).json(data);
};

// 参数校验失败
exports.validationErrorResponse = function (res, msg) {
    var data = {
        status: 0,
        message: msg,
    };
    return res.status(400).json(data);
};

// 参数校验失败携带数据
exports.validationErrorWithData = function (res, msg, data) {
    var resData = {
        status: 0,
        message: msg,
        data: data
    };
    return res.status(400).json(resData);
};

// token未授权或已过期错误响应
exports.unauthorizedResponse = function (res, msg) {
    var data = {
        status: 0,
        message: msg,
    };
    return res.status(401).json(data);
};

// 用户权限不足响应
exports.forbiddenResponse = function (res, msg) {
    var data = {
        status: 0,
        message: msg || "Forbidden"
    };
    return res.status(403).json(data);
};