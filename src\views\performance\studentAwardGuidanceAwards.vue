<template>
  <div class="performance-container awards-container">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />

    <a-card title="学生获奖指导管理" :bordered="false" class="performance-card">
      <template #extra>
        <a-space>
          <!-- <a-upload
            :customRequest="handleImport"
            :show-upload-list="false"
            :before-upload="beforeUpload"
          >
            <a-button type="primary">
              <template #icon><UploadOutlined /></template>
              导入数据
            </a-button>
          </a-upload> -->
          <a-upload
            :customRequest="handleExcelToJsonConvert"
            :show-upload-list="false"
            :before-upload="beforeExcelUpload"
            accept=".xlsx,.xls,.csv"
          >
            <a-button type="primary" v-permission="'score:studentAwardGuidanceAwards:admin:update'">
              <template #icon><FileExcelOutlined /></template>
              Excel数据导入
            </a-button>
          </a-upload>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            添加获奖
          </a-button>
          <a-button :type="showPersonalAwards ? 'default' : 'primary'" @click="togglePersonalAwards" v-permission="'score:studentAwardGuidanceAwards:admin'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalAwards ? '查看全部获奖' : '查看我的获奖' }}
          </a-button>
        </a-space>
      </template>
        <!-- 学生获奖填写说明区域 -->
        <a-card title="学生获奖填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
          <a-alert
            class="mb-16"
            message="学生获奖统计时间范围"
            :description="`统计时间：${timeRangeText || '加载中...'}`"
            type="info"
            show-icon
          />
          <div class="rule-content">
            <p><strong>填写说明：</strong></p>
            <ol class="detail-list">
              <li>双创类项目（大创、挑战杯）获奖和研究生国际交流时间范围在统计时间内</li>
              <li>此表由主要指导教师为我院的教职工填写</li>
              <li>各指导教师比例由第一负责人分配</li>
              <li>此表中出现的姓名全部为我院教职工</li>
              <li>分配比例请填写百分数，如：30%、50%等</li>
              <li>"获奖等级/国际交流"已设置下拉菜单</li>
              <li>总分配比例应为100%，若不是请反查数据</li>
              <li>需提供写有获奖时间的获奖证书和研究生院开具的出国交流证明文件备查</li>
            </ol>
          </div>
        </a-card>

        <!-- 图表区域 -->
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="审核状态分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="reviewStatusChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('reviewStatus', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="reviewStatusChartRef" id="reviewStatusChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="获奖级别分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="categoryChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('category', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="categoryChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-bottom: 10px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="获奖时间分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="timeChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('time', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="timeChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

      <!-- 教师获奖数量排行 -->
      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="24">
          <a-card title="教师获奖得分排行" :bordered="false">
            <template #extra>
                <a-space>
                <a-input-search
                  v-model:value="authorRankingPagination.nickname"
                  v-permission="'score:textbooks:admin:list'"
                  placeholder="用户名称"
                  style="width: 150px;"
                  @search="fetchTeacherRankData"
                  @pressEnter="fetchTeacherRankData"
                />
                <a-select
                  v-model:value="teacherRankChartRange"
                  style="width: 150px;"
                  @change="(value) => changeChartRange('teacherRank', value)"
                >
                  <a-select-option value="in">统计范围内</a-select-option>
                  <a-select-option value="out">统计范围外</a-select-option>
                  <a-select-option value="all">全部获奖</a-select-option>
                </a-select>
                <a-button type="primary" @click="exportTeacherRanking" :loading="teacherRankExportLoading" v-permission="'score:studentAwardGuidanceAwards:admin:list'">
                    <template #icon><DownloadOutlined /></template>
                  导出
                  </a-button>
                </a-space>
            </template>
            <a-table
              :columns="teacherRankColumns"
              :data-source="teacherRankData"
              :pagination="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' ? teacherRankPagination : false"
              :loading="teacherRankLoading"
              rowKey="userId"
              @change="handleTeacherRankTableChange"
              :scroll="{ x: 800 }"
              :bordered="true"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'rank'">
                  <a-tag :color="getRankColor(index + 1)">{{ index + 1 }}</a-tag>
                </template>
                <template v-else-if="column.key === 'totalAwards'">
                  <a-badge :count="record.totalAwards" :number-style="{ backgroundColor: '#52c41a' }" />
                </template>
                <template v-else-if="column.key === 'totalScore'">
                  <span style="font-weight: bold; color: #1890ff;">{{ (record.totalScore || 0).toFixed(2) }}分</span>
                </template>
                <template v-else-if="column.key === 'details'">
                  <a-button type="link" @click="showTeacherAwardDetails(record)"
                  v-if="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId"
                  >
                    查看详情
                  </a-button>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>

      <!-- 教师获奖详情弹窗 -->
      <a-modal
        v-model:visible="teacherDetailsVisible"
        :title="`${selectedTeacherDetailName || '教师'}的获奖详情`"
        width="1200px"
        :footer="null"
      >
        <a-table
          :columns="teacherAwardDetailColumns"
          :data-source="teacherAwardDetails"
          :pagination="{ pageSize: 10 }"
          :loading="teacherDetailsLoading"
          rowKey="id"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'levelName'">
              <a-tag color="blue">{{ record.level?.levelName || '-' }}</a-tag>
            </template>
            <template v-else-if="column.key === 'score'">
              <span style="font-weight: bold; color: #1890ff;">
                {{ record.level?.score || 0 }}分
              </span>
            </template>
            <template v-else-if="column.key === 'awardDate'">
              {{ record.awardDate ? record.awardDate.substring(0, 10) : '-' }}
            </template>
            <template v-else-if="column.key === 'allocationRatio'">
              {{ (record.allocationRatio * 100).toFixed(2) }}%
            </template>
          </template>
        </a-table>
        <div style="margin-top: 16px; text-align: right; font-weight: bold;">
          总得分: {{ (teacherDetailsTotalScore || 0).toFixed(2) }}分
        </div>
      </a-modal>

        <!-- 搜索表单 -->
        <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
          <a-form :model="searchForm" @finish="handleSearch" layout="vertical" class="performance-form">
            <a-row :gutter="[12, 8]">
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="获奖名称" name="awardName">
                  <a-input
                    v-model:value="searchForm.awardName"
                    placeholder="请输入获奖名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="获奖级别" name="levelId">
                  <a-select
                    v-model:value="searchForm.levelId"
                    placeholder="请选择获奖级别"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="level in levelOptions" :key="level.id" :value="level.id">
                      {{ level.levelName }} ({{ level.score }}分)
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="获奖日期" name="dateRange">
                  <a-range-picker
                    v-model:value="searchForm.dateRange"
                    :format="'YYYY-MM-DD'"
                    style="width: 100%"
                    :placeholder="['开始日期', '结束日期']"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="审核状态" name="reviewStatus">
                  <a-select
                    v-model:value="searchForm.reviewStatus"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部状态</a-select-option>
                    <a-select-option value="rejected">已拒绝</a-select-option>
                    <a-select-option value="pending">待审核</a-select-option>
                    <a-select-option value="reviewed">已审核</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="统计范围" name="range">
                  <a-select
                    v-model:value="searchForm.range"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部范围</a-select-option>
                    <a-select-option value="in">统计范围内</a-select-option>
                    <a-select-option value="out">统计范围外</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
                <a-form-item label=" " style="margin-bottom: 0;">
                  <div class="search-actions-inline">
                    <a-button type="primary" html-type="submit" size="default">
                      <template #icon><SearchOutlined /></template>
                      搜索
                    </a-button>
                    <a-button @click="resetSearch" size="default">
                      <template #icon><ReloadOutlined /></template>
                      重置
                    </a-button>
                    <a-button type="default" @click="exportData" :loading="exportLoading" size="default">
                      <template #icon><DownloadOutlined /></template>
                      导出
                    </a-button>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 数据表格 -->
        <div class="performance-table">
          <a-table
            :columns="columns"
            :data-source="dataSource"
            :pagination="pagination"
            :loading="isLoading"
            rowKey="id"
            @change="handleTableChange"
            :scroll="{ x: 1400 }"
            :bordered="true"
          >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'participants'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ formatParticipantsWithAllocation(record.participants, record) }}</span>
          </template>
          <template v-else-if="column.key === 'awardName'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ record.awardName }}</span>
          </template>
          <template v-else-if="column.key === 'levelName'">
            <a-tag color="blue">{{ record.level?.levelName || '-' }}</a-tag>
          </template>
          <template v-else-if="column.key === 'awardDate'">
            <span>{{ formatDate(record.awardDate) }}</span>
          </template>
          <template v-else-if="column.key === 'department'">
            <span>{{ record.department || '-' }}</span>
          </template>
          <template v-else-if="column.key === 'remark'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ record.remark || '-' }}</span>
          </template>
          <template v-else-if="column.key === 'score'">
            <span v-if="record.isInTimeRange" style="font-weight: bold; color: #1890ff;">
              {{ record.level?.score || 0 }}分
            </span>
            <span v-else style="color: #999999;">0分 (统计范围外)</span>
          </template>
          <template v-else-if="column.key == 'ifReviewer'">
            <a-tag :color="record.ifReviewer == true ? 'success' : (record.ifReviewer == false ? 'error' : 'warning')">
              {{ record.ifReviewer == 1 ? '已审核' : (record.ifReviewer == false ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'reviewComment'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ record.reviewComment || '-' }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown placement="bottomRight" :trigger="['click']">
              <template #overlay>
                <a-menu class="action-dropdown-menu">
                  <!-- 编辑选项 -->
                  <a-menu-item
                    key="edit"
                    v-if="record.ifReviewer != 1 && hasPerms(showPersonalAwards ? 'score:studentAwardGuidanceAwards:self:update' : 'score:studentAwardGuidanceAwards:admin:update')"
                  >
                    <a @click="handleEdit(record)" class="action-menu-item">
                      <EditOutlined />
                      <span>编辑</span>
                    </a>
                  </a-menu-item>

                  <!-- 重新提交审核选项 -->
                  <a-menu-item
                    key="resubmit"
                    v-if="record.ifReviewer === false && hasPerms('score:studentAwardGuidanceAwards:self:reapply')"
                  >
                    <a @click="handleResubmit(record)" class="action-menu-item">
                      <ReloadOutlined />
                      <span>重新提交审核</span>
                    </a>
                  </a-menu-item>

                  <!-- 审核选项 - 仅管理员视图显示 -->
                  <a-menu-item
                    key="review"
                    v-if="!showPersonalAwards && !record.ifReviewer && hasPerms('score:studentAwardGuidanceAwards:admin:review')"
                  >
                    <a @click="handleReview(record)" class="action-menu-item">
                      <AuditOutlined />
                      <span>审核</span>
                    </a>
                  </a-menu-item>

                  <a-menu-divider v-if="record.ifReviewer != 1 || (!showPersonalAwards && !record.ifReviewer)" />

                  <!-- 删除选项 -->
                  <a-menu-item
                    key="delete"
                    v-if="hasPerms(showPersonalAwards ? 'score:studentAwardGuidanceAwards:self:delete' : 'score:studentAwardGuidanceAwards:admin:delete')"
                  >
                    <a @click="confirmDelete(record)" class="action-menu-item text-danger">
                      <DeleteOutlined />
                      <span>删除</span>
                    </a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small" class="action-trigger-btn">
                操作
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
          </a-table>
        </div>
      </a-card>

    <!-- 添加获奖模态框 -->
    <a-modal
      :visible="modalVisible"
      :title="isEdit ? '编辑获奖' : '添加获奖'"
      :width="700"
      @cancel="handleModalCancel"
      :footer="null"
    >
      <div class="award-form">
        <a-form 
          ref="formRef" 
          :model="formState" 
          :rules="rules" 
          :label-col="{ span: 6 }" 
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item name="awardName" label="获奖名称">
            <a-input v-model:value="formState.awardName" placeholder="请输入获奖名称" />
          </a-form-item>
          
          <a-form-item name="levelId" label="获奖级别">
            <a-select
              v-model:value="formState.levelId"
              placeholder="请选择获奖级别"
              style="width: 100%;"
            >
              <a-select-option v-for="level in levelOptions" :key="level.id" :value="level.id">
                {{ level.levelName }} ({{ level.score }}分)
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item name="department" label="所属系/教研室">
            <a-input v-model:value="formState.department" placeholder="请输入所属系/教研室" />
          </a-form-item>

          <a-form-item name="awardDate" label="获奖日期">
            <a-date-picker 
              v-model:value="formState.awardDate" 
              style="width: 100%;" 
              format="YYYY-MM-DD" 
              placeholder="请选择获奖日期"
            />
          </a-form-item>

          <a-form-item name="remark" label="备注">
            <a-textarea 
              v-model:value="formState.remark" 
              placeholder="请输入备注信息" 
              :rows="3" 
            />
          </a-form-item>

          <a-form-item name="participants" label="参与人员" required>
            <div class="members-container">
              <!-- 成员添加区域 -->
              <a-row :gutter="8" style="margin-bottom: 8px;">
                <a-col :span="12">
                  <a-select
                    v-model:value="currentMember.displayName"
                    placeholder="请选择参与人员"
                    :filter-option="false"
                    show-search
                    allow-clear
                    :loading="membersSearchLoading"
                    @search="handleMembersSearch"
                    :not-found-content="membersSearchLoading ? undefined : '未找到匹配结果'"
                    @change="handleCurrentMemberChange"
                  >
                    <a-select-option v-for="(option, index) in membersOptions" :key="option.id || index" :value="option.nickname || option.username" :data="option">
                      {{ option.nickname || option.username }} ({{ option.studentNumber || '无工号' }})
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="8">
                  <a-input-number
                    v-model:value="currentMember.allocationRatio"
                    placeholder="分配比例(%)"
                    :min="0"
                    :max="100"
                    :step="5"
                    :precision="1"
                    style="width: 100%"
                    addon-after="%"
                    @change="() => validateTotalAllocation()"
                  />
                </a-col>
                <a-col :span="4">
                  <a-button type="primary" @click="addParticipant">
                    <template #icon><PlusOutlined /></template>
                    添加
                  </a-button>
                </a-col>
              </a-row>
              
              <!-- 已添加成员列表 -->
              <a-divider v-if="formState.participants && formState.participants.length > 0" style="margin: 8px 0">已添加参与人员</a-divider>
              <div v-if="formState.participants && formState.participants.length > 0">
                <p style="color: #666; font-size: 12px; margin-bottom: 8px;">已添加 {{ formState.participants.length }} 位参与人员</p>
                <a-list 
                  :data-source="formState.participants" 
                  size="small"
                  bordered
                >
                  <template #renderItem="{ item, index }">
                    <a-list-item>
                      <a-row style="width: 100%">
                        <a-col :span="8">
                          {{ item.nickname || item.username || item.participantId }}
                        </a-col>
                        <a-col :span="4">
                          <a-switch 
                            :checked="item.isLeader" 
                            @change="(checked) => toggleLeader(index, checked)"
                            checkedChildren="负责人" 
                            unCheckedChildren="参与者"
                          />
                        </a-col>
                        <a-col :span="8">
                          <a-input-number
                            v-model:value="item.allocationRatio"
                            :min="0"
                            :max="100"
                            :step="5"
                            :precision="1"
                            style="width: 90%"
                            addon-after="%"
                            @change="validateTotalAllocation"
                          />
                        </a-col>
                        <a-col :span="4" style="text-align: right">
                          <a-button type="link" danger @click="() => removeParticipant(index)">删除</a-button>
                        </a-col>
                      </a-row>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
              <div v-else style="color: #999; text-align: center; padding: 10px; border: 1px dashed #ddd; border-radius: 4px;">
                还没有添加参与人员，请先选择参与人员并点击"添加"按钮
              </div>
            </div>

            <div v-if="formState.participants.length > 0" style="margin-top: 8px; color: #ff4d4f;">
              {{ allocationMsg }}
            </div>
            
            <div style="margin-top: 8px;">
              <a-button type="dashed" @click="adjustParticipantRatios" style="width: 100%">
                <template #icon><PartitionOutlined /></template>
                平均分配比例
              </a-button>
            </div>
          </a-form-item>
          
          <!-- 文件上传 -->
          <a-form-item name="files" label="相关附件">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
              <a-upload
                v-model:file-list="fileList"
                :customRequest="handleFileUpload"
                :before-upload="beforeUpload"
                multiple
                :show-upload-list="false"
              >
                <a-button type="primary">
                  <template #icon><UploadOutlined /></template>
                  选择文件
                </a-button>
              </a-upload>
              <span style="margin-left: 16px; color: #666; font-size: 12px;">
                支持上传文档、图片或压缩文件，单个文件不超过10MB
              </span>
            </div>
            
            <!-- 已上传的文件列表 -->
            <a-table
              :columns="fileColumns"
              :data-source="fileList"
              :pagination="false"
              size="small"
              style="margin-top: 8px;"
              rowKey="uid"
              v-if="fileList.length > 0"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fileName'">
                  <span :title="record.name">{{ record.name }}</span>
                </template>
                <template v-if="column.key === 'fileSize'">
                  {{ formatFileSize(record.size) }}
                </template>
                <template v-if="column.key === 'status'">
                  <div v-if="record.status === 'uploading'">
                    <a-progress :percent="record.percent || 0" size="small" />
                  </div>
                  <a-tag v-else :color="record.status === 'done' ? 'success' : (record.status === 'error' ? 'error' : 'processing')">
                    {{ record.status === 'done' ? '已上传' : (record.status === 'error' ? '上传失败' : '上传中') }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="previewFile(record)" v-if="record.status === 'done'">
                      <template #icon><EyeOutlined /></template>
                      预览
                    </a-button>
                    <a-button type="link" size="small" @click="downloadFile(record)" v-if="record.status === 'done'">
                      <template #icon><DownloadOutlined /></template>
                      下载
                    </a-button>
                    <a-popconfirm
                      title="确定要删除该文件吗？"
                      @confirm="confirmDeleteFile(record)"
                      okText="确认"
                      cancelText="取消"
                    >
                      <a-button type="link" danger size="small">
                        <template #icon><DeleteOutlined /></template>
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
            
            <!-- 显示已有的文件 -->
            <a-divider v-if="fileList.length > 0 && existingFileList.length > 0" style="margin: 12px 0" />
            
            <a-table
              :columns="fileColumns"
              :data-source="existingFileList"
              :pagination="false"
              size="small"
              style="margin-top: 8px;"
              rowKey="id"
              v-if="existingFileList.length > 0"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fileName'">
                  <span :title="record.originalName">{{ record.originalName || record.fileName }}</span>
                </template>
                <template v-if="column.key === 'fileSize'">
                  {{ formatFileSize(record.size) }}
                </template>
                <template v-if="column.key === 'status'">
                  <a-tag color="success">已上传</a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="previewFile(record)">
                      <template #icon><EyeOutlined /></template>
                      预览
                    </a-button>
                    <a-button type="link" size="small" @click="downloadFile(record)">
                      <template #icon><DownloadOutlined /></template>
                      下载
                    </a-button>
                    <a-popconfirm
                      title="确定要删除该文件吗？"
                      @confirm="() => removeExistingFile(record.id)"
                      okText="确认"
                      cancelText="取消"
                    >
                      <a-button type="link" danger size="small">
                        <template #icon><DeleteOutlined /></template>
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-form-item>
          
          <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
            <a-space>
              <a-button type="primary" @click="handleModalOk" :loading="confirmLoading">
                提交
              </a-button>
              <a-button @click="handleModalCancel">
                取消
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 审核模态框 -->
    <ProjectReviewModal
      :visible="reviewModalVisible"
      :record="currentReviewRecord"
      :attachments="reviewAttachments"
      @cancel="reviewModalVisible = false"
      @submit="handleReviewSubmit"
    />

    <!-- 导入预览弹窗 -->
    <a-modal
      :visible="importPreviewVisible"
      title="Excel导入预览"
      width="90%"
      :footer="null"
      @cancel="handleCancelImportPreview"
    >
      <template v-if="importPreviewLoading">
        <div style="text-align: center; padding: 40px;">
          <a-spin size="large" />
          <p style="margin-top: 20px;">正在解析数据，请稍候...</p>
        </div>
      </template>
      <template v-else>
        <div style="margin-bottom: 16px;">
          <a-alert
            :type="userIdCheckResults.notFound > 0 ? 'warning' : 'success'"
            :message="userIdCheckResults.notFound > 0 ? 
              `存在${userIdCheckResults.notFound}个用户ID未找到，这些记录可能导入失败` : 
              '所有用户ID均已找到'"
            show-icon
          />
          <div style="margin-top: 8px;">
            <a-space>
              <span>共找到 <b>{{ importPreviewData.length }}</b> 条记录</span>
              <a-button type="primary" @click="handleStartImport" :loading="importInProgress">
                开始导入
              </a-button>
              <a-button @click="handleCancelImportPreview">
                取消
              </a-button>
              <a-button type="primary" @click="handleDownloadJson">
                <template #icon><DownloadOutlined /></template>
                下载JSON
              </a-button>
            </a-space>
          </div>
        </div>
        
        <a-table
          :dataSource="importPreviewData"
          :columns="previewColumns"
          rowKey="index"
          :pagination="{ pageSize: 10 }"
          :rowClassName="(record) => record.userIdCheckStatus === 'notFound' ? 'import-row-error' : ''"
          :scroll="{ x: 1200 }"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'userIdCheckStatus'">
              <a-tag v-if="text === 'checking'" color="blue">检查中</a-tag>
              <a-tag v-else-if="text === 'found'" color="green">已找到</a-tag>
              <a-tag v-else color="red">未找到</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'participants'">
              <div>
                <!-- 处理参与者列的显示 -->
                <div v-if="Array.isArray(text)">
                  <div v-for="(participant, pIndex) in text" :key="pIndex">
                    {{ participant.name || participant.nickname || participant.username || '未知' }}
                    <a-tag v-if="participant.found !== undefined" :color="participant.found ? 'green' : 'red'">
                      {{ participant.found ? '已找到' : '未找到' }}
                    </a-tag>
                  </div>
                </div>
                <span v-else>{{ text || '-' }}</span>
              </div>
            </template>
          </template>
        </a-table>
      </template>
    </a-modal>
  
    <!-- 导入结果弹窗 -->
    <a-modal
      :visible="importResultVisible"
      title="导入结果"
      width="800px"
      :footer="null"
      :maskClosable="false"
      :closable="!importInProgress"
    >
      <div style="margin-bottom: 16px;">
        <a-progress 
          :percent="importResults.total > 0 ? Math.floor((importResults.current / importResults.total) * 100) : 0" 
          :status="importInProgress ? 'active' : (importResults.failed > 0 ? 'exception' : 'success')" 
        />
        <div style="margin-top: 16px; display: flex; justify-content: space-between;">
          <span>总记录数: <b>{{ importResults.total }}</b></span>
          <span>已处理: <b>{{ importResults.current }}</b></span>
          <span>成功: <b style="color: #52c41a">{{ importResults.success }}</b></span>
          <span>失败: <b style="color: #ff4d4f">{{ importResults.failed }}</b></span>
        </div>
      </div>
      
      <a-table
        :dataSource="importResults.details"
        :columns="resultColumns"
        rowKey="index"
        :pagination="{ pageSize: 10 }"
        :rowClassName="(record) => record.status === 'error' ? 'import-row-error' : ''"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag v-if="text === 'success'" color="success">成功</a-tag>
            <a-tag v-else color="error">失败</a-tag>
          </template>
        </template>
      </a-table>
      
      <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
        <a-space>
          <a-button 
            @click="handleExportFailedRecords" 
            :disabled="importInProgress || importResults.failed === 0"
            danger
          >
            <template #icon><DownloadOutlined /></template>
            导出失败记录
          </a-button>
          <a-button 
            type="primary" 
            @click="handleCloseImportResult"
            :disabled="importInProgress"
          >
            完成
          </a-button>
        </a-space>
      </div>
    </a-modal>
  </div>
</template> 

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { UploadOutlined, DownloadOutlined, PlusOutlined, UserOutlined, SearchOutlined, ReloadOutlined, EyeOutlined, DeleteOutlined, PartitionOutlined, FileExcelOutlined, EditOutlined, AuditOutlined, DownOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import useUserId from '@/composables/useUserId'
import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole } = useUserRole();
import { uploadFile as utilUploadFile, previewFileById, downloadFileById, deleteFileById } from '@/utils/others'
import { uploadFiles } from '@/api/modules/api.file'
import ProjectReviewModal from '@/components/review/ProjectReviewModal.vue'
import { usersSearch } from '@/api/modules/api.users'
import * as XLSX from 'xlsx'
import {
  getAwardDetail,
  createAward,
  updateAward,
  importAwards,
  getAwards,
  getTimeDistribution,
  reviewAward,
  getTeacherAwardRanking,
  getTeacherAwardDetails,
  deleteAward,
  reapplyReview,
  getReviewStatusOverview
} from '@/api/modules/api.studentAwardGuidanceAwards'
import { getAwardLevels, getLevelDistribution, getAllLevels } from '@/api/modules/api.studentAwardGuidanceAwardLevels'
import { excelToStudentAwardGuidanceAwardsJson, downloadJson } from '@/utils/fileUtils'
import { hasPerms } from '@/libs/util.common';
import { getScoreTimeRange } from '@/api/modules/api.home';

// 图表引用
const categoryChartRef = ref(null)
const reviewStatusChartRef = ref(null)
const timeChartRef = ref(null)

// 图表实例
let categoryChart = null
let reviewStatusChart = null
let timeChart = null

// 图表数据范围状态
const categoryChartRange = ref('in')
const reviewStatusChartRange = ref('in')
const timeChartRange = ref('in')
const teacherRankChartRange = ref('in')
const teacherRankExportLoading = ref(false)
const exportLoading = ref(false)
const timeRangeText = ref('');

// 切换图表数据范围
const changeChartRange = async (type, range) => {
  if (type === 'teacherRank') {
    console.log('切换教师排行图表范围:', teacherRankChartRange.value, '->', range)
    // 删除相同值检查，确保每次切换都执行刷新
    teacherRankChartRange.value = range
    teacherRankPagination.current = 1
    fetchTeacherRankData()
  } else if (type === 'category') {
    console.log('切换获奖级别图表范围:', categoryChartRange.value, '->', range)
    // 删除相同值检查，确保每次切换都执行刷新
    categoryChartRange.value = range
    initCategoryChart(range)
  } else if (type === 'reviewStatus') {
    console.log('切换审核状态图表范围:', reviewStatusChartRange.value, '->', range)
    // 删除相同值检查，确保每次切换都执行刷新
    reviewStatusChartRange.value = range
    initReviewStatusChart(range)
  } else if (type === 'time') {
    console.log('切换获奖时间图表范围:', timeChartRange.value, '->', range)
    // 删除相同值检查，确保每次切换都执行刷新
    timeChartRange.value = range
    initTimeChart(range)
  }
}

// 表格列定义
const columns = [
  {
    title: '获奖名称',
    dataIndex: 'awardName',
    key: 'awardName',
    width: '20%',
    ellipsis: true,
  },
  {
    title: '获奖级别',
    dataIndex: 'levelName',
    key: 'levelName',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '获奖日期',
    dataIndex: 'awardDate',
    key: 'awardDate',
    width: '10%',
    sorter: true,
  },
  {
    title: '参与人员及分配比例',
    dataIndex: 'participants',
    key: 'participants',
    width: '20%',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: '20%',
    ellipsis: true,
  },
  {
    title: '分数',
    key: 'score',
    width: '10%',
  },
  {
    title: '审核状态',
    key: 'ifReviewer',
    width: '10%',
  },
  {
    title: '审核建议',
    dataIndex: 'reviewComment',
    key: 'reviewComment',
    width: '15%',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
    align: 'center'
  },
]

// 数据源
const dataSource = ref([])
const isLoading = ref(false)
const levelOptions = ref([])

// 搜索表单
const searchForm = reactive({
  awardName: '',
  levelId: undefined,
  dateRange: [],
  reviewStatus: 'reviewed',
  range: 'in',
})

// 重置搜索条件
const resetSearch = () => {
  searchForm.awardName = ''
  searchForm.levelId = undefined
  searchForm.dateRange = []
  searchForm.reviewStatus = 'reviewed'
  searchForm.range = 'in'
  handleSearch()
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)

// 表单引用
const formRef = ref(null)

// 表单数据
const formState = reactive({
  awardName: '',
  levelId: '',
  department: '',
  awardDate: null,
  remark: '',
  participants: [], // 存储参与者数组
})

// 添加在其他状态变量附近
const currentRole = ref('');
const currentUserId = ref('');

// 是否显示个人获奖
const showPersonalAwards = ref(false)

// 使用useUserId composable
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()

// 添加错误状态
const errorMessage = ref('')

// 审核状态筛选
const reviewStatus = ref('reviewed')

// 分配比例信息
const allocationMsg = ref('')

// 校验分配比例总和是否为100%
const validateTotalAllocation = () => {
  if (!formState.participants || formState.participants.length === 0) {
    allocationMsg.value = ''
    return true
  }

  // 确保所有分配比例都是有效的数字
  const validParticipants = formState.participants.filter(p => {
    const ratio = parseFloat(p.allocationRatio)
    return !isNaN(ratio) && ratio >= 0 && ratio <= 100
  })

  if (validParticipants.length !== formState.participants.length) {
    allocationMsg.value = '存在无效的分配比例，请检查'
    return false
  }

  // 计算总比例
  const total = validParticipants.reduce((sum, p) => {
    const ratio = parseFloat(p.allocationRatio) || 0
    return sum + ratio
  }, 0)

  // 允许1%的误差
  const isValid = Math.abs(total - 100) < 1

  // 格式化显示百分比
  const totalPercentage = total.toFixed(0)
  allocationMsg.value = isValid
    ? '分配比例总和: 100%'
    : `分配比例总和: ${totalPercentage}%，需要调整为100%`

  return isValid
}

// 调整参与人员分配比例
const adjustParticipantRatios = () => {
  const participantsCount = formState.participants.length

  if (participantsCount > 0) {
    // 计算每人平均分配比例，使用100%而不是1，因为现在使用的是百分比
    const averageRatio = 100 / participantsCount

    // 为每个参与人设置均等分配比例
    formState.participants.forEach(p => {
      p.allocationRatio = parseFloat(averageRatio.toFixed(1))
    })

    // 重新验证总比例
    validateTotalAllocation()
  }
}

// 新增成员相关参数
const currentMember = reactive({
  displayName: '',  // 用于显示的名称
  participantId: '',  // 用户ID，用于提交到后端
  id: '',
  nickname: '',
  username: '',
  studentNumber: '',
  allocationRatio: 10,  // 默认分配比例设为10%
  isLeader: false
})

// 成员搜索相关
const membersOptions = ref([])
const membersSearchLoading = ref(false)
const membersSearchTimeout = ref(null)

// 参与人员搜索
const handleMembersSearch = (value) => {
  // 清除之前的定时器
  if (membersSearchTimeout.value) {
    clearTimeout(membersSearchTimeout.value)
  }
  
  // 如果输入为空，清空选项
  if (!value || value.trim() === '') {
    membersOptions.value = []
    return
  }
  
  // 设置500ms延迟，避免频繁请求
  membersSearchTimeout.value = setTimeout(async () => {
    membersSearchLoading.value = true
    try {
      // 调用搜索接口
      const response = await usersSearch({ keyword: value })
      
      if (response && response.code === 200) {
        // 检查返回的数据结构
        if (Array.isArray(response.data)) {
          membersOptions.value = response.data
        } else if (response.data && Array.isArray(response.data.list)) {
          // 如果返回的是包含list属性的对象
          membersOptions.value = response.data.list
        } else {
          // 如果数据结构不是预期的格式
          console.error('搜索成员返回的数据结构异常:', response.data)
          
          // 尝试转换数据结构
          const data = response.data
          if (data && typeof data === 'object') {
            try {
              // 尝试将对象转换为数组
              const tempArray = Object.values(data)
              if (tempArray.length > 0 && typeof tempArray[0] === 'object') {
                membersOptions.value = tempArray
              } else {
                membersOptions.value = [data] // 如果只有一个对象，创建包含它的数组
              }
            } catch (e) {
              console.error('转换成员数据结构失败:', e)
              membersOptions.value = []
            }
          } else {
            membersOptions.value = []
          }
        }
        
      } else {
        membersOptions.value = []
        console.error('搜索成员失败:', response?.message || '未知错误')
      }
    } catch (error) {
      console.error('搜索成员出错:', error)
      membersOptions.value = []
    } finally {
      membersSearchLoading.value = false
    }
  }, 500)
}

// 当选择成员时更新当前成员信息
const handleCurrentMemberChange = (value, option) => {
  // 在vue3的a-select中，option参数是一个包含data属性的对象
  const selectedOption = option ? 
    (option.data ? option.data : 
      (option.option ? option.option.data : null)) : null
  
  if (selectedOption) {
    // 直接使用选项数据
    currentMember.id = selectedOption.id || ''           // 用户ID
    currentMember.participantId = selectedOption.id      // 设置participantId为用户ID
    currentMember.displayName = selectedOption.nickname || selectedOption.username || value
    currentMember.nickname = selectedOption.nickname || selectedOption.username || value
    currentMember.username = selectedOption.username || ''
    currentMember.studentNumber = selectedOption.studentNumber || ''
    
    // 默认分配比例设为10%
    if (!currentMember.allocationRatio) {
      currentMember.allocationRatio = 10
    }
  } else if (value) {
    // 尝试从选项列表中找到匹配的选项
    const found = membersOptions.value.find(opt => 
      (opt.nickname || opt.username) === value
    )
    
    if (found) {
      currentMember.id = found.id || ''                // 用户ID
      currentMember.participantId = found.id           // 设置participantId为用户ID
      currentMember.displayName = found.nickname || found.username || value
      currentMember.nickname = found.nickname || found.username || value
      currentMember.username = found.username || ''
      currentMember.studentNumber = found.studentNumber || ''
      
      // 默认分配比例设为10%
      if (!currentMember.allocationRatio) {
        currentMember.allocationRatio = 10
      }
    } else {
      // 如果没有找到，直接使用输入的值
      currentMember.id = ''
      currentMember.participantId = ''  // 没有用户ID，设为空
      currentMember.displayName = value
      currentMember.nickname = value
      currentMember.username = ''
      currentMember.studentNumber = ''
      
      // 默认分配比例设为10%
      if (!currentMember.allocationRatio) {
        currentMember.allocationRatio = 10
      }
    }
  }
}

// 添加参与人
const addParticipant = () => {
  // 验证成员数据
  if (!currentMember.participantId) {
    message.error('请选择参与人员')
    return
  }
  
  // 检查分配比例
  if (currentMember.allocationRatio === undefined || 
      currentMember.allocationRatio === null || 
      isNaN(parseFloat(currentMember.allocationRatio))) {
    message.error('请输入有效的成员分配比例')
    return
  }
  
  // 检查是否已存在该成员
  const exists = formState.participants.some(
    p => p.id === currentMember.id || p.participantId === currentMember.participantId
  )
  
  if (exists) {
    message.warning('该参与人员已添加')
    return
  }
  
  // 创建新成员对象
  const newMember = {
    id: currentMember.id, // 用户ID
    participantId: currentMember.participantId, // 用户ID，用于提交到后端
    displayName: currentMember.displayName, // 显示名称
    nickname: currentMember.nickname,
    username: currentMember.username || '',
    studentNumber: currentMember.studentNumber || '',
    allocationRatio: parseFloat(currentMember.allocationRatio) || 0,
    isLeader: formState.participants.length === 0 // 第一个添加的成员默认为负责人
  }
  
  try {
    // 添加到参与人员列表
    formState.participants.push(newMember)
    
    // 重置当前成员数据
    currentMember.id = ''
    currentMember.participantId = ''
    currentMember.displayName = ''
    currentMember.nickname = ''
    currentMember.username = ''
    currentMember.studentNumber = ''
    currentMember.allocationRatio = 10
    
    // 检查总比例，不自动调整
    validateTotalAllocation()
    
    message.success('参与人员添加成功')
  } catch (err) {
    console.error('添加成员时发生错误:', err)
    message.error('参与人员添加失败，请查看控制台错误信息')
  }
}

// 修改toggleLeader函数，确保只有一个负责人
const toggleLeader = (index, isLeader) => {
  // 如果设置为负责人，先将其他所有人设为非负责人
  if (isLeader) {
    formState.participants.forEach((p, i) => {
      if (i !== index) {
        p.isLeader = false
      }
    })
  }
  
  // 修改指定参与人的负责人状态
  formState.participants[index].isLeader = isLeader
}

// 修改移除参与人函数
const removeParticipant = (index) => {
  formState.participants.splice(index, 1)
  
  // 如果只剩下一个参与人员，自动设置为100%
  if (formState.participants.length === 1) {
    formState.participants[0].allocationRatio = 100
  }
  
  // 验证总比例，但不自动调整
  validateTotalAllocation()
}

// 记录图表是否已初始化的状态
const chartsInitialized = ref(false)

// 教师排行表格列定义
const teacherRankColumns = [
  {
    title: '排名',
    key: 'rank',
    width: '80px',
    align: 'center',
  },
  {
    title: '教师',
    dataIndex: 'name',
    key: 'name',
    width: '150px',
  },
  {
    title: '工号',
    dataIndex: 'employeeNumber',
    key: 'employeeNumber',
    width: '120px',
  },
  {
    title: '获奖总数',
    dataIndex: 'awardCount',
    key: 'awardCount',
    width: '120px',
    align: 'center'
  },
  {
    title: '总得分',
    dataIndex: 'awardScore',
    key: 'awardScore',
    width: '120px',
    align: 'center',
    sorter: (a, b) => a.awardScore - b.awardScore,
  },
  {
    title: '操作',
    key: 'details',
    width: '100px',
    align: 'center',
    fixed: 'right',
  },
]

const teacherRankData = ref([])
const teacherRankLoading = ref(false)

// 教师排名分页
const teacherRankPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条`
})

// 处理教师排名表格变化
const handleTeacherRankTableChange = (pagination) => {
  // 只更新必要的分页属性，保持其他配置不变
  teacherRankPagination.current = pagination.current
  teacherRankPagination.pageSize = pagination.pageSize
  // 确保其他配置属性保持不变
  teacherRankPagination.showSizeChanger = true
  teacherRankPagination.pageSizeOptions = ['10', '20', '50']
  teacherRankPagination.showTotal = (total) => `共 ${total} 条`
  fetchTeacherRankData()
}

// 获取排名颜色
const getRankColor = (rank) => {
  switch (rank) {
    case 1:
      return 'gold'
    case 2:
      return 'silver'
    case 3:
      return '#cd7f32' // bronze
    default:
      return 'blue'
  }
}

const authorRankingPagination = reactive({
  nickname: ''
});

// 获取教师排名数据
const fetchTeacherRankData = async () => {
  teacherRankLoading.value = true
  try {
    const response = await getTeacherAwardRanking({
      range: teacherRankChartRange.value,
      reviewStatus: searchForm.reviewStatus,
      limit: teacherRankPagination.pageSize,
      page: teacherRankPagination.current,
      nickname: authorRankingPagination.nickname,
      isExport: false
    })

    if (response && response.code === 200) {
      teacherRankData.value = response.data.list || []
      
      // 更新分页信息
      if (response.data.pagination) {
        teacherRankPagination.total = response.data.pagination.total || 0
      }
    } else {
      message.error(response?.message || '获取教师排名数据失败')
    }
  } catch (error) {
    console.error('获取教师排名数据失败:', error)
    message.error('获取教师排名数据失败: ' + (error.message || '未知错误'))
  } finally {
    teacherRankLoading.value = false
  }
}

// 导出教师排名
const exportTeacherRanking = async () => {
  try {
    // 显示加载中提示
    const hide = message.loading('正在导出排行榜数据...', 0)
    
    const response = await getTeacherAwardRanking({
      range: teacherRankChartRange.value,
      reviewStatus: searchForm.reviewStatus,
      isExport: true
    })
    
    hide() // 关闭加载提示
    
    if (response && response.code === 200 && response.data.list) {
      const data = response.data.list
      
      // 导出数据处理
      const exportData = data.map((item, index) => {
        return {
          '排名': index + 1,
          '教师': item.userName,
          '工号': item.employeeNumber || item.studentNumber,
          '获奖总数': item.totalAwards,
          '总得分': (item.totalScore || 0).toFixed(2)
        }
      })
      
      // 使用xlsx导出
      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '教师获奖排行榜')
      
      // 生成文件名
      const fileName = `教师获奖排行榜_${dayjs().format('YYYY-MM-DD')}.xlsx`
      
      // 导出文件
      XLSX.writeFile(workbook, fileName)
      
      message.success('导出成功')
    } else {
      message.error(response?.message || '导出失败')
    }
  } catch (error) {
    console.error('导出教师排名失败:', error)
    message.error('导出失败: ' + (error.message || '未知错误'))
  }
}

// 页面生命周期钩子
onMounted(async () => {
  try {
    // 获取当前用户角色
    try {
      currentRole.value = await getUserRole();
      console.log("当前用户角色:", currentRole.value);
      console.log("roleAuth===", currentRole.value ? currentRole.value.roleAuth : 'undefined');
    } catch (error) {
      console.error("获取用户角色失败:", error);
      message.error("获取用户角色信息失败，某些功能可能受限");
    }

    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true);
      console.log("当前用户ID:", currentUserId.value);
    } catch (error) {
      console.error("获取用户ID失败:", error);
    }

    // 如果是教师角色，默认显示个人获奖
    if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
      showPersonalAwards.value = true;
      console.log('用户为教师角色，默认显示个人获奖');
    }

    // 初始化表格数据
    fetchData()
    // 获取所有获奖级别
    fetchLevels()
    // 初始化各类统计图表
    nextTick(() => {
      initCategoryChart('in')
      initReviewStatusChart('in')
      initTimeChart('in')
      fetchTeacherRankData()
    })



    // 获取时间范围
    const getTimeRange = () => {
      // 调用API获取时间范围
      getScoreTimeRange('studentAwardGuidance').then(res => {
        if (res.code === 200 && res.data) {
          timeRangeText.value = res.data.timeRange || '';
        } else {
          timeRangeText.value = '暂无时间范围数据';
        }
      }).catch(error => {
        console.error('获取时间范围失败:', error);
        timeRangeText.value = '获取时间范围失败';
      });
    };

    // 在onMounted中调用
    getTimeRange();
  } catch (error) {
    console.error('组件初始化错误:', error)
    message.error('初始化失败，请刷新页面重试')
  }
})

// 自定义导入处理
const handleImport = async ({ file, onSuccess, onError }) => {
  try {
    message.loading('正在导入数据...')
    const res = await importAwards(file)
    
    if (res && res.code === 200) {
      message.success('导入成功')
      onSuccess()
      fetchData() // 重新获取数据
    } else {
      message.error(res?.message || '导入失败')
      onError()
    }
  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败: ' + (error.message || '未知错误'))
    onError()
  }
}

// 处理导出
const handleExport = async () => {
  try {
    const fileName = `获奖数据_${dayjs().format('YYYY-MM-DD')}.xlsx`
    await exportAwards({
      fileName: fileName,
      awardName: searchForm.awardName,
      levelId: searchForm.levelId,
      awardDateStart: searchForm.dateRange?.[0]?.format('YYYY-MM-DD'),
      awardDateEnd: searchForm.dateRange?.[1]?.format('YYYY-MM-DD')
    })
    message.success('数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败: ' + (error.message || '未知错误'))
  }
}

// 获取列表数据
const fetchData = async (shouldUpdateCharts = true) => {
  isLoading.value = true
  errorMessage.value = ''
  
  try {
    // 处理搜索参数
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      range: searchForm.range, // 使用searchForm中的范围参数
      reviewStatus: searchForm.reviewStatus // 使用searchForm中的审核状态
    };

    // 添加获奖名称搜索
    if (searchForm.awardName) {
      params.awardName = searchForm.awardName;
    }

    // 添加获奖级别搜索
    if (searchForm.levelId) {
      params.levelId = searchForm.levelId;
    }

    // 添加获奖日期范围搜索
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.awardDateStart = searchForm.dateRange[0].format('YYYY-MM-DD');
      params.awardDateEnd = searchForm.dateRange[1].format('YYYY-MM-DD');
    }

    // 如果是个人视图，添加userId参数
    if (showPersonalAwards.value) {
      try {
        const currentUserId = await getUserId(true)
        
        if (!currentUserId) {
          message.error('未获取到用户信息，请重新登录')
          return
        }
        
        params.userId = currentUserId
      } catch (userError) {
        console.error('获取用户信息过程中发生错误:', userError)
        message.error('获取用户信息失败，请重新登录')
        return
      }
    }

    // 使用统一的getAwards接口
    const response = await getAwards(params)
    
    if (response && response.code === 200) {
      dataSource.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0
      
      // 只在需要时更新图表
      if (shouldUpdateCharts && chartsInitialized.value) {
      nextTick(() => {
          debouncedUpdateCharts()
        })
      }
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = '获取获奖列表失败：' + (response?.message || '未知错误')
    }
  } catch (error) {
    console.error('获取获奖列表失败:', error)
    message.error('获取获奖列表失败: ' + (error.message || '未知错误'))
    errorMessage.value = '获取获奖列表失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 获取获奖级别
const fetchLevels = async () => {
  try {
    const response = await getAllLevels()
    
    if (response && response.code === 200) {
      levelOptions.value = response.data || []
      
      // 输出级别分数信息，便于调试
      if (levelOptions.value.length > 0) {
        console.log('获取到的获奖级别:', levelOptions.value.map(level => ({
          id: level.id,
          levelName: level.levelName,
          score: level.score
        })))
      }
    } else {
      message.error(response?.message || '获取获奖级别失败')
    }
  } catch (error) {
    console.error('获取获奖级别失败:', error)
    message.error('获取获奖级别失败: ' + (error.message || '未知错误'))
  }
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
  // 只更新必要的分页属性，保持其他配置不变
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  // 确保其他配置属性保持不变
  pagination.showSizeChanger = true
  pagination.pageSizeOptions = ['10', '20', '50']
  pagination.showTotal = total => `共 ${total} 条`

  // 处理排序
  if (sorter && sorter.field) {
    // 这里可以添加排序逻辑
  }
  
  fetchData()
}

// 切换个人/全部获奖视图
const togglePersonalAwards = () => {
  showPersonalAwards.value = !showPersonalAwards.value
  chartsInitialized.value = false  // 重置图表初始化状态
  fetchData()
}

// 更新所有图表
const updateCharts = async () => {
  try {
    // 释放旧的图表实例
    if (categoryChart) categoryChart.dispose()
    if (reviewStatusChart) reviewStatusChart.dispose()
    if (timeChart) timeChart.dispose()

    // 只有当数据存在且DOM元素已准备好时才初始化图表
    if (dataSource.value.length > 0 && categoryChartRef.value && reviewStatusChartRef.value && timeChartRef.value) {
      // 初始化各图表，使用当前选择的范围
      if (categoryChartRange.value) {
        await initCategoryChart(categoryChartRange.value)
      }
      if (reviewStatusChartRange.value) {
        await initReviewStatusChart(reviewStatusChartRange.value)
      }
      if (timeChartRange.value) {
        await initTimeChart(timeChartRange.value)
      }
    }
  } catch (error) {
    console.error('更新图表出错:', error)
    message.error('加载图表数据失败')
  }
}

// 初始化获奖级别分布图
const initCategoryChart = async (range) => {
  try {
    console.log('初始化获奖级别分布图...')
    // 检查DOM元素是否存在
    if (!categoryChartRef.value) {
      console.error('饼图DOM元素不存在')
      return
    }
    
    // 构建请求参数
    const params = { 
      range: range || 'in',
      reviewStatus: searchForm.reviewStatus
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalAwards.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    console.log('获取获奖级别分布数据参数:', params)
    
    // 请求级别分布数据
    const response = await getLevelDistribution(params)
    
    if (response && response.code === 200) {
      const categoryData = response.data
      console.log('获取到的获奖级别分布数据:', categoryData)
      
      if (!categoryData || categoryData.length === 0) {
        console.warn('获奖级别分布数据为空')
        // 处理空数据情况
        if (categoryChartRef.value) {
          if (categoryChart) {
            categoryChart.dispose()
          }
          categoryChart = echarts.init(categoryChartRef.value)
          categoryChart.setOption({
            title: {
              text: '暂无数据',
              left: 'center',
              top: 'center'
            }
          })
        }
        return
      }
      
      // 初始化图表
      if (categoryChart) {
        categoryChart.dispose()
      }
      categoryChart = echarts.init(categoryChartRef.value)
      
      // 设置图表配置
      const option = {
        title: {
          text: '获奖级别分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: categoryData.map(item => item.name || item.levelName)
        },
        series: [
          {
            name: '获奖级别',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: categoryData.map(item => ({
              name: item.name || item.levelName,
              value: item.value || item.count || 0
            }))
          }
        ]
      }
      
      categoryChart.setOption(option)
      console.log('级别分布图表已渲染')
    } else {
      console.error('获取获奖级别分布数据失败:', response)
    }
  } catch (error) {
    console.error('获奖级别分布图初始化失败:', error)
  }
}

// 初始化获奖时间分布图
const initTimeChart = async (range) => {
  try {
    console.log('初始化获奖时间分布图...')
    // 检查DOM元素是否存在
    if (!timeChartRef.value) {
      console.error('时间分布图DOM元素不存在')
      return
    }
    
    // 构建请求参数
    const params = { 
      range: range || 'in',
      reviewStatus: searchForm.reviewStatus
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalAwards.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    console.log('获取获奖时间分布数据参数:', params)
    
    // 请求时间分布数据
    const response = await getTimeDistribution(params)
    
    if (response && response.code === 200) {
      const timeData = response.data
      console.log('获取到的获奖时间分布数据:', timeData)
      
      if (!timeData || !timeData.months || timeData.months.length === 0) {
        console.warn('获奖时间分布数据为空')
        // 处理空数据情况
        if (timeChartRef.value) {
          if (timeChart) {
            timeChart.dispose()
          }
          timeChart = echarts.init(timeChartRef.value)
          timeChart.setOption({
            title: {
              text: '暂无数据',
              left: 'center',
              top: 'center'
            }
          })
        }
        return
      }
      
      // 初始化图表
      if (timeChart) {
        timeChart.dispose()
      }
      timeChart = echarts.init(timeChartRef.value)
      
      // 设置图表配置
      const option = {
        title: {
          text: '获奖时间分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: timeData.months,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '获奖数量',
            type: 'bar',
            data: timeData.data,
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
      
      timeChart.setOption(option)
      console.log('时间分布图表已渲染')
    } else {
      console.error('获取获奖时间分布数据失败:', response)
    }
  } catch (error) {
    console.error('获奖时间分布图初始化失败:', error)
  }
}

// 初始化审核状态分布图
const initReviewStatusChart = async (range) => {
  try {
    console.log('初始化审核状态分布图...')
    // 检查DOM元素是否存在
    if (!reviewStatusChartRef.value) {
      console.error('审核状态分布图DOM元素不存在')
      return
    }

    // 构建请求参数
    const params = {
      range: range || 'in'
    }

    // 如果是个人视图，添加userId参数
    if (showPersonalAwards.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }

    console.log('获取审核状态分布数据参数:', params)

    // 请求审核状态分布数据
    const response = await getReviewStatusOverview(params)

    if (response && response.code === 200) {
      const { reviewed, pending, rejected } = response.data
      console.log('获取到的审核状态分布数据:', response.data)

      // 初始化图表
      if (reviewStatusChart) {
        reviewStatusChart.dispose()
      }
      reviewStatusChart = echarts.init(reviewStatusChartRef.value)

      // 设置图表配置
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          left: 'left',
          bottom: 0,
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '审核状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            {
              value: reviewed,
              name: '已审核',
              itemStyle: { color: '#52c41a' }
            },
            {
              value: pending,
              name: '待审核',
              itemStyle: { color: '#faad14' }
            },
            {
              value: rejected,
              name: '已拒绝',
              itemStyle: { color: '#ff4d4f' }
            }
          ]
        }]
      }

      reviewStatusChart.setOption(option)
      console.log('审核状态分布图表已渲染')
    } else {
      console.error('获取审核状态分布数据失败:', response)
    }
  } catch (error) {
    console.error('审核状态分布图初始化失败:', error)
  }
}

// 处理审核状态筛选
const handleReviewStatusChange = (status) => {
  searchForm.reviewStatus = status
  fetchData()
}

// 切换获奖范围
const handleAwardRangeChange = (range) => {
  searchForm.range = range
  pagination.current = 1
  fetchData()
}

// 处理审核模态框相关
const reviewModalVisible = ref(false)
const currentReviewRecord = ref({})
const reviewAttachments = ref([])

// 处理立项审核
const handleReview = async (record) => {
  try {
    // 显示加载中
    message.loading('正在加载详情...', 0.5)
    currentReviewRecord.value = record
    
    // 获取获奖详情，包括附件
    try {
      const response = await getAwardDetail(record.id)
      if (response && response.code === 200 && response.data) {
        // 处理附件数据
        reviewAttachments.value = (response.data.attachments || []).map((file, index) => ({
          uid: `-${index}`,
          name: file.name || file.originalName || `附件${index + 1}`,
          status: 'done',
          url: file.url,
          response: { file: { id: file.id } },
          data: file
        }))
        console.log('获奖附件:', reviewAttachments.value)
      }
    } catch (error) {
      console.error('获取获奖详情失败:', error)
      message.error('获取获奖详情失败，但将继续审核流程')
    }
    
    // 显示审核模态框
    reviewModalVisible.value = true
    
  } catch (error) {
    console.error('打开审核对话框失败:', error)
    message.error('操作失败: ' + (error.message || '未知错误'))
  }
}

// 处理审核提交
const handleReviewSubmit = async (formData) => {
  try {
    console.log('接收到的表单数据:', formData)
    
    // 获取当前用户ID作为审核人
    await getUserId(true)
    
    if (!userId.value) {
      message.error('无法获取用户ID，请重新登录')
      reviewModalVisible.value = false
      return
    }
    
    // 调用审核API
    const submitData = {
      id: formData.id,
      reviewer: userId.value,
      reviewStatus: formData.reviewStatus,
      reviewComment: formData.reviewComment
    }
    
    console.log('发送到API的数据:', submitData)
    
    const response = await reviewAward(submitData)
    
    if (response && response.code === 200) {
      message.success('审核成功')
      reviewModalVisible.value = false
      fetchData()
      updateCharts()
    } else {
      message.error(response?.message || '审核失败')
    }
  } catch (error) {
    console.error('审核失败:', error)
    message.error('审核失败: ' + (error.message || '未知错误'))
  }
}

// 使用hasPermissionToReview函数替代isAdmin/isCurrentUserFirstResponsible函数
const hasPermissionToReview = (record) => {
  // 未审核的获奖才需要显示审核按钮
  if (record.ifReviewer) return false
  
  // 默认允许显示审核按钮，后端会做权限验证
  return true
}

// 格式化参与者显示
const formatParticipantsWithAllocation = (participants, record) => {
  if (!participants || !Array.isArray(participants) || participants.length === 0) {
    return '-'
  }
  
  return participants.map(p => {
    // 正确获取用户对象，可能是p.participant或p.user
    const user = p.participant || p.user
    
    // 获取用户名称
    let name = '未知用户'
    if (user) {
      name = user.nickname || user.username || user.name
    }
    
    // 分配比例显示为百分比，保留整数
    const allocation = p.allocationRatio ? `(占比${(parseFloat(p.allocationRatio) * 100).toFixed(0)}%)` : ''
    
    // 负责人标识，使用中文
    const leader = p.isLeader === 1 || p.isLeader === true ? '[负责人]' : ''
    
    return `${name}${leader}${allocation}`
  }).join('、 ')
}

// 格式化日期显示
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  try {
    return dayjs(dateStr).format('YYYY-MM-DD')
  } catch (e) {
    return dateStr
  }
}

// 校验规则
const rules = {
  awardName: [{ required: true, message: '请输入获奖名称', trigger: 'blur' }],
  levelId: [{ required: true, message: '请选择获奖级别', trigger: 'change' }],
  department: [{ required: true, message: '请输入所属系/教研室', trigger: 'blur' }],
  awardDate: [{ required: true, message: '请选择获奖日期', trigger: 'change' }],
}

// 教师获奖详情弹窗
const teacherDetailsVisible = ref(false)
const selectedTeacherDetailName = ref(null)
const teacherAwardDetails = ref([])
const teacherDetailsTotalScore = ref(0)
const teacherDetailsLoading = ref(false)

// 获取教师获奖详情
const showTeacherAwardDetails = async (record) => {
  try {
    teacherDetailsVisible.value = true
    selectedTeacherDetailName.value = record.userName
    teacherDetailsLoading.value = true

    // 构建请求参数
    const params = {
      userId: record.userId,
      page: 1,
      pageSize: 100, // 获取足够多的数据
      range: teacherRankChartRange.value, // 添加当前选中的range参数
      reviewStatus: searchForm.reviewStatus !== 'all' ? searchForm.reviewStatus : undefined // 添加审核状态参数
    }

    const response = await getTeacherAwardDetails(params)

    if (response && response.code === 200) {
      teacherAwardDetails.value = response.data.list || []
      teacherDetailsTotalScore.value = response.data.totalScore || 0
    } else {
      message.error(response?.message || '获取教师获奖详情失败')
    }
  } catch (error) {
    console.error('获取教师获奖详情失败:', error)
    message.error('获取教师获奖详情失败: ' + (error.message || '未知错误'))
  } finally {
    teacherDetailsLoading.value = false
  }
}

// 教师获奖详情表格列定义
const teacherAwardDetailColumns = [
  {
    title: '获奖名称',
    dataIndex: 'awardName',
    key: 'awardName',
    width: '20%',
    ellipsis: false,
  },
  {
    title: '授奖单位',
    dataIndex: 'department',
    key: 'department',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '级别',
    key: 'levelName',
    width: '15%',
  },
  {
    title: '获奖日期',
    key: 'awardDate',
    width: '10%',
  },
  {
    title: '分配比例',
    key: 'allocationRatio',
    width: '10%',
  },
  {
    title: '分数',
    key: 'score',
    width: '8%',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: '10%',
    ellipsis: false,
  }
]

// 文件上传相关
const fileList = ref([])
const existingFileList = ref([])

// 文件表格列定义
const fileColumns = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    key: 'fileName', 
    ellipsis: true
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 240
  }
]

// 处理文件上传
const handleFileUpload = ({ file, onSuccess, onError, onProgress }) => {
  utilUploadFile({
    file,
    uploadApi: uploadFiles,
    id: formState.id || 'temp_' + Date.now(),
    relatedId: formState.id,
    class: 'student_award_guidance',
    onProgress,
    onSuccess: (res) => {
      if (res && res.code === 200 && res.data) {
        // 将文件ID添加到formState.fileIds中
        if (!formState.fileIds) formState.fileIds = []
        formState.fileIds.push(res.data.id)
        
        // 如果有文件路径，添加到attachmentUrl
        if (res.data.filePath) {
          if (!formState.attachmentUrl) formState.attachmentUrl = []
          formState.attachmentUrl.push(res.data.filePath)
        }
      }
      onSuccess(res)
    },
    onError,
    fileList: fileList.value,
    formState: formState
  })
}

// 预览文件
const previewFile = (file) => {
  const fileId = file.response?.file?.uid || file.id
  if (fileId) {
    previewFileById(fileId)
  } else {
    message.warning('无法获取文件ID，预览失败')
  }
}

// 下载文件
const downloadFile = (file) => {
  const fileId = file.response?.file?.uid || file.id
  const fileName = file.name || file.originalName || '下载文件'
  if (fileId) {
    downloadFileById(fileId, fileName)
  } else {
    message.warning('无法获取文件ID，下载失败')
  }
}

// 删除文件
const confirmDeleteFile = (record) => {
  const fileId = record.response?.file?.uid
  if (fileId) {
    deleteFileById(fileId, {
      onSuccess: () => {
        // 从文件列表中移除
        const index = fileList.value.findIndex(item => 
          item.uid === record.uid || 
          item.id === fileId
        )
        
        if (index !== -1) {
          fileList.value.splice(index, 1)
        }
        
        // 从fileIds中移除
        const idIndex = formState.fileIds.indexOf(fileId)
        if (idIndex !== -1) {
          formState.fileIds.splice(idIndex, 1)
        }
        
        message.success('文件已删除')
      },
      onError: (errorMsg) => {
        message.error(`删除失败: ${errorMsg}`)
      }
    })
  } else {
    // 如果没有文件ID，只是从列表中移除
    const index = fileList.value.findIndex(item => item.uid === record.uid)
    if (index !== -1) {
      fileList.value.splice(index, 1)
      message.success('文件已从列表中移除')
    }
  }
}

// 删除已存在的文件
const removeExistingFile = (fileId) => {
  if (fileId) {
    deleteFileById(fileId, {
      onSuccess: () => {
        // 从已存在文件列表中移除
        const index = existingFileList.value.findIndex(item => item.id === fileId)
        if (index !== -1) {
          existingFileList.value.splice(index, 1)
        }
        
        // 从fileIds中移除
        if (formState.fileIds) {
          const idIndex = formState.fileIds.indexOf(fileId)
          if (idIndex !== -1) {
            formState.fileIds.splice(idIndex, 1)
          }
        }
        
        message.success('文件已删除')
      },
      onError: (errorMsg) => {
        message.error(`删除失败: ${errorMsg}`)
      }
    })
  }
}

// 文件大小格式化
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 修改处理获奖模态框确认的函数
const handleModalOk = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    // 校验分配比例
    if (!validateTotalAllocation()) {
      message.error('参与人员分配比例总和必须为100%')
      return
    }
    
    // 校验是否有负责人
    if (formState.participants.length > 0 && !formState.participants.some(p => p.isLeader)) {
      message.error('必须指定至少一名负责人')
      return
    }
    
    confirmLoading.value = true
    
    // 构建提交数据
    const submitData = {
      awardName: formState.awardName,
      levelId: formState.levelId,
      department: formState.department,
      awardDate: formState.awardDate ? formState.awardDate.format('YYYY-MM-DD') : null,
      remark: formState.remark,
      participants: formState.participants.map(p => ({
        userId: p.participantId,  // 修改这里，将participantId作为userId提交
        isLeader: p.isLeader ? 1 : 0,
        allocationRatio: p.allocationRatio / 100 // 将百分比转换为小数提交给后端
      }))
    }
    
    // 处理文件ID
    if (formState.fileIds && formState.fileIds.length > 0) {
      submitData.fileIds = JSON.stringify(formState.fileIds)
    }
    
    // 如果有附件URL
    if (formState.attachmentUrl && formState.attachmentUrl.length > 0) {
      submitData.attachmentUrl = JSON.stringify(formState.attachmentUrl)
    }
    
    let response
    
    if (isEdit.value && currentRecord.value) {
      // 更新获奖
      response = await updateAward({
        id: currentRecord.value.id,
        ...submitData
      })
    } else {
      // 添加获奖
      response = await createAward(submitData)
    }
    
    if (response && response.code === 200) {
      message.success(isEdit.value ? '更新成功' : '添加成功')
      modalVisible.value = false
      fetchData() // 刷新数据
      
      // 更新图表
      setTimeout(() => {
        updateCharts()
      }, 500)
    } else {
      message.error(response?.message || (isEdit.value ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    console.error(isEdit.value ? '更新获奖失败:' : '添加获奖失败:', error)
    message.error((isEdit.value ? '更新获奖失败: ' : '添加获奖失败: ') + (error.message || '未知错误'))
  } finally {
    confirmLoading.value = false
  }
}
  
  // 重置表单
const resetForm = () => {
  // 重置基本信息
  formState.id = undefined
  formState.awardName = ''
  formState.levelId = ''
  formState.department = ''
  formState.awardDate = null
  formState.remark = ''
  formState.participants = []
  formState.fileIds = []
  formState.attachmentUrl = []
  
  // 清空文件列表
  fileList.value = []
  existingFileList.value = []
  
  // 重置当前成员
  currentMember.id = ''
  currentMember.participantId = ''
  currentMember.displayName = ''
  currentMember.nickname = ''
  currentMember.username = ''
  currentMember.studentNumber = ''
  currentMember.allocationRatio = 10
  
  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 修改显示添加获奖弹窗
const showAddModal = () => {
  isEdit.value = false
  currentRecord.value = null
  
  // 重置表单
  resetForm()
  
  // 显示弹窗
  modalVisible.value = true
  
  // 如果表单ref存在，重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 修改处理编辑
const handleEdit = async (record) => {
  isEdit.value = true
  currentRecord.value = record
  
  try {
    const response = await getAwardDetail(record.id)
    
    if (response && response.code === 200) {
      const detail = response.data
      console.log('获取到的获奖详情:', detail) // 添加日志，查看API返回的详情数据结构
      
      // 设置基本信息
      formState.awardName = detail.awardName
      formState.levelId = detail.levelId
      formState.department = detail.department
      formState.awardDate = detail.awardDate ? dayjs(detail.awardDate) : null
      formState.remark = detail.remark || ''
      
      // 处理参与者数据
      formState.participants = (detail.participants || []).map(p => {
        // 获取用户信息，可能是p.participant或p.user
        const user = p.participant || p.user
        
        return {
          id: p.userId, // 使用正确的userId
          participantId: p.userId, // 使用userId作为participantId
          displayName: user ? (user.nickname || user.username) : '未知用户',
          nickname: user ? user.nickname : '',
          username: user ? user.username : '',
          studentNumber: user ? user.studentNumber : '',
          allocationRatio: p.allocationRatio * 100, // 将小数转换为百分比显示
          isLeader: p.isLeader === 1 || p.isLeader === true
        }
      })
      
      // 加载附件
      fileList.value = []
      existingFileList.value = []
      formState.fileIds = []
      formState.attachmentUrl = []
      
      if (detail.attachments && detail.attachments.length > 0) {
        // 存储附件ID
        formState.fileIds = detail.attachments.map(file => file.id)
        
        // 构建已有文件列表
        existingFileList.value = detail.attachments.map(file => ({
          ...file,
          fileName: file.originalName || file.name,
          originalName: file.originalName || file.name,
          size: file.size,
          status: 'done'
        }))
      }
      
      // 优化：检查并展示分数信息
      if (detail.level && detail.level.score !== undefined) {
        const score = parseFloat(detail.level.score)
        // 改为使用纯文本显示，不使用HTML标签
        const scoreText = detail.isInTimeRange ? 
          `${score}分` : 
          `0分 (统计范围外)`
        
        message.info(`该获奖级别分数: ${scoreText}`, 3)
      }
      
      // 验证分配比例
      validateTotalAllocation()
      
      // 重置当前成员
      currentMember.id = ''
      currentMember.participantId = ''
      currentMember.displayName = ''
      currentMember.nickname = ''
      currentMember.username = ''
      currentMember.studentNumber = ''
      currentMember.allocationRatio = 10
      
      // 显示弹窗
      modalVisible.value = true
    } else {
      message.error(response?.message || '获取获奖详情失败')
    }
  } catch (error) {
    console.error('获取获奖详情失败:', error)
    message.error('获取获奖详情失败: ' + (error.message || '未知错误'))
  }
}

// 导出获奖数据
const exportData = async () => {
  try {
    exportLoading.value = true
    
    // 构造导出参数 - 使用当前的筛选条件
    const params = {
      // 使用当前搜索表单的条件
      awardName: searchForm.awardName,
      levelId: searchForm.levelId,
      range: searchForm.range,
      reviewStatus: searchForm.reviewStatus,
      sortField: 'awardDate',
      sortOrder: 'desc',
      isExport: true // 标记为导出，返回所有数据
    }
    
    // 添加日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.awardDateStart = searchForm.dateRange[0].format('YYYY-MM-DD')
      params.awardDateEnd = searchForm.dateRange[1].format('YYYY-MM-DD')
    }
    
    // 如果是个人项目视图，添加用户ID
    if (showPersonalAwards.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      } else {
        message.error('无法获取用户ID，请重新登录')
        exportLoading.value = false
        return
      }
    }
    
    // 调用API获取数据
    const response = await getAwards(params)
    
    if (response && response.code === 200 && response.data && response.data.list) {
      // 准备导出数据 - 格式化数据
      const data = response.data.list.map(item => {
        // 格式化参与者信息
        const participants = (item.participants || [])
          .map(p => {
            const user = p.participant || p.user || {};
            const ratio = typeof p.allocationRatio === 'number' ? p.allocationRatio : 
                         (parseFloat(p.allocationRatio) || 0);
            return `${user.nickname || user.username || '未知'}(${(ratio * 100).toFixed(0)}%)${p.isLeader ? '[负责人]' : ''}`;
          })
          .join('; ');
          
        return {
          '获奖名称': item.awardName || '',
          '获奖编号': item.awardNumber || '',
          '获奖级别': item.level?.levelName || '',
          '获奖日期': item.awardDate || '',
          '颁发部门': item.department || '',
          '参与人员': participants,
          '学分': item.isInTimeRange ? (item.level?.score || 0) : '0 (统计范围外)',
          '审核状态': item.ifReviewer == true ? '已审核' : (item.ifReviewer == false ? '已拒绝' : '待审核'),
          '审核建议': item.reviewComment || '',
          '备注': item.remark || ''
        };
      });
      
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '学生获奖指导项目');
      
      // 设置列宽
      const columnWidths = [
        { wch: 30 }, // 获奖名称
        { wch: 15 }, // 获奖编号
        { wch: 12 }, // 获奖级别
        { wch: 12 }, // 获奖日期
        { wch: 15 }, // 颁发部门
        { wch: 40 }, // 参与人员
        { wch: 10 }, // 学分
        { wch: 12 }, // 审核状态
        { wch: 15 }, // 审核建议
        { wch: 20 }  // 备注
      ];
      worksheet['!cols'] = columnWidths;
      
      // 导出文件
      const fileName = `学生获奖指导项目_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      
      message.success('导出成功');
    } else {
      message.error(response?.message || '导出失败: 未获取到数据');
    }
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败: ' + (error.message || '未知错误'));
  } finally {
    exportLoading.value = false;
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 确认删除
const confirmDelete = (record) => {
  handleDelete(record)
}

// 处理删除
const handleDelete = async (record) => {
  try {
    // 使用正确的Modal.confirm方式
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除获奖记录"${record.awardName}"吗？此操作不可逆。`,
      okText: '确认',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          // 调用删除API
          await deleteAward(record.id);
          message.success('删除成功');

          // 重新加载数据
          fetchData();
        } catch (error) {
          console.error('删除记录失败:', error);
          message.error('删除记录失败: ' + (error.message || '未知错误'));
        }
      }
    });
  } catch (error) {
    console.error('显示确认对话框失败:', error);
    message.error('操作失败: ' + (error.message || '未知错误'));
  }
}

// 添加导入预览相关变量
const importPreviewVisible = ref(false)
const importPreviewData = ref([])
const importPreviewLoading = ref(false)
const importInProgress = ref(false)
const modifiedImportData = ref([])
const userIdCheckResults = ref({ notFound: 0 })
const lastConvertedExcelData = ref(null)

// 导入结果相关变量
const importResults = ref({
  total: 0,
  current: 0,
  success: 0,
  failed: 0,
  records: []
})
const importResultVisible = ref(false)

// 文件上传前验证
const beforeUpload = (file) => {
  // 检查文件类型
  const isValidType = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
    file.type === 'application/vnd.ms-excel' || 
    file.name.endsWith('.xlsx') || 
    file.name.endsWith('.xls') || 
    file.name.endsWith('.csv') ||
    file.type === 'application/zip' ||
    file.type === 'application/x-zip-compressed' ||
    file.name.endsWith('.zip') ||
    file.type === 'application/json' ||
    file.name.endsWith('.json');
    
  if (!isValidType) {
    message.error('只能上传Excel、JSON或ZIP文件');
    return false;
  }
  
  // 检查文件大小 (20MB限制)
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    message.error('文件必须小于20MB!');
    return false;
  }
  
  return isValidType && isLt20M;
}

// Excel文件上传前检查
const beforeExcelUpload = (file) => {
  // 检查文件类型
  const isExcel = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
    file.type === 'application/vnd.ms-excel' || 
    file.name.endsWith('.xlsx') || 
    file.name.endsWith('.xls') || 
    file.name.endsWith('.csv');
    
  if (!isExcel) {
    message.error('只能上传Excel文件 (.xlsx, .xls, .csv)');
    return false;
  }
  
  // 检查文件大小 (20MB限制)
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    message.error('文件必须小于20MB!');
    return false;
  }
  
  return isExcel && isLt20M;
};

// 处理Excel到JSON转换
const handleExcelToJsonConvert = async ({ file }) => {
  // 创建唯一的消息ID
  const messageKey = `excel_convert_${Date.now()}`;
  
  try {
    // 显示正在处理的提示
    message.loading({ 
      content: '正在解析Excel文件，请稍候...', 
      key: messageKey,
      duration: 0 // 不自动消失
    });
    
    // 获取文件基本名（不含扩展名）
    const fileName = file.name.split('.').slice(0, -1).join('.') || 'award_export';
    
    // 默认配置：表头在第三行 (从0开始计数，索引为2)
    const options = {
      headerRow: 2, // 第3行作为表头（索引从0开始，所以是2）
      sheetName: null // 默认使用第一个工作表
    };
    
    console.log('开始转换Excel文件:', fileName);
    
    // 转换Excel为JSON
    const awards = await excelToStudentAwardGuidanceAwardsJson(file, options);
    
    // 更新消息为处理完成
    message.loading({ 
      content: '数据解析完成...', 
      key: messageKey,
      duration: 1 // 1秒后自动消失
    });
    
    if (awards.length === 0) {
      message.warning('未从Excel文件中提取到任何有效数据', 3);
      return;
    }
    
    // 确保创建的JSON数据完全符合后端API的要求格式
    const optimizedData = awards.map(award => {
      // 移除可能的undefined值
      const cleanAward = {};
      
      // 只保留有值的字段
      Object.entries(award).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          cleanAward[key] = value;
        }
      });
      
      return cleanAward;
    });
    
    // 保存最近转换的数据以便直接导入
    lastConvertedExcelData.value = optimizedData;
    
    console.log(`Excel数据处理完成，共 ${optimizedData.length} 条数据`);
    
    // 弹出确认框，提供下载与直接导入两个选项
    Modal.confirm({
      title: 'Excel转换成功',
      content: `已成功从Excel中提取${optimizedData.length}条获奖记录。请选择操作方式：`,
      okText: '直接导入系统',
      cancelText: '下载JSON文件',
      onOk: () => {
        // 直接导入数据
        handleDirectImport(optimizedData);
      },
      onCancel: async () => {
        // 下载JSON文件
        try {
          await downloadJson(optimizedData, fileName);
          message.success(`已成功下载JSON文件`, 3);
        } catch (downloadErr) {
          console.error('下载JSON文件失败:', downloadErr);
          message.error('下载失败，请尝试使用"直接导入"功能', 5);
        }
      }
    });
    
  } catch (error) {
    console.error('Excel转JSON处理失败:', error);
    // 确保任何loading消息都被清除
    message.destroy(messageKey);
    message.error(`Excel文件处理失败: ${error.message || '未知错误'}`, 5);
  } finally {
    // 延迟0.5秒后销毁所有可能存在的消息，确保不会有消息一直存在
    setTimeout(() => {
      message.destroy(messageKey);
    }, 500);
  }
};

// 直接导入处理，包括参与者ID查找和级别ID映射
const handleDirectImport = async (awardsToImport) => {
  if (!awardsToImport || awardsToImport.length === 0) {
    message.error('无可导入的数据');
    return;
  }

  // 显示解析中的提示
  message.loading('正在解析JSON数据并检查用户ID，请稍候...', 0);
  
  // 首先确保已加载获奖级别数据
  if (!levelOptions.value || levelOptions.value.length === 0) {
    await fetchLevels();
  }

  importPreviewVisible.value = true;
  
  // 初始化用户ID检查结果
  userIdCheckResults.value = { notFound: 0 };
  
  // 准备预览数据
  const previewItems = awardsToImport.map((item, index) => ({
    index: index + 1,
    awardName: item.awardName || '',
    levelName: item.levelName || '',
    department: item.department || '',
    awardDate: item.awardDate || '',
    // 参与者信息
    participants: item.participants || [],
    userIdCheckStatus: 'checking', // 初始状态为"检查中"
    rawData: item // 保存原始数据
  }));
  
  importPreviewData.value = previewItems;
  importPreviewLoading.value = true;
  
  try {
    // 检查参与者的用户ID
    for (let i = 0; i < previewItems.length; i++) {
      const item = previewItems[i];
      const participants = item.participants || [];
      
      if (participants.length > 0) {
        for (let j = 0; j < participants.length; j++) {
          const participant = participants[j];
          if (participant.name) {
            try {
              const userSearchResult = await usersSearch({ keyword: participant.name });
              
              if (userSearchResult && userSearchResult.code === 200 && userSearchResult.data && userSearchResult.data.length > 0) {
                // 查找匹配的用户 - 使用nickname或username进行匹配
                const foundUser = userSearchResult.data.find(user => 
                  (user.nickname && user.nickname === participant.name) || 
                  (user.username && user.username === participant.name)
                );
                
                if (foundUser) {
                  // 找到了用户ID
                  participant.userId = foundUser.id;
                  participant.found = true;
                } else {
                  // 未找到用户ID
                  participant.found = false;
                  userIdCheckResults.value.notFound++;
                }
              } else {
                participant.found = false;
                userIdCheckResults.value.notFound++;
              }
            } catch (error) {
              console.error(`检查用户"${participant.name}"ID失败:`, error);
              participant.found = false;
              userIdCheckResults.value.notFound++;
            }
          } else {
            participant.found = false;
            userIdCheckResults.value.notFound++;
          }
        }
        
        // 更新检查状态
        if (participants.some(p => !p.found)) {
          item.userIdCheckStatus = 'notFound';
        } else {
          item.userIdCheckStatus = 'found';
        }
      }
      
      // 每5条更新一次显示
      if (i % 5 === 0 || i === previewItems.length - 1) {
        importPreviewData.value = [...previewItems];
      }
    }
    
    // 更新最终结果
    importPreviewData.value = [...previewItems];
    
    // 初始化修改后的数据为预览数据的副本
    modifiedImportData.value = [...importPreviewData.value];
    
    message.destroy();
    importPreviewLoading.value = false;
  } catch (error) {
    console.error('用户ID检查过程中出错:', error);
    message.error('用户ID检查失败: ' + (error.message || '未知错误'));
    importPreviewLoading.value = false;
  }
};

// 开始导入处理
const handleStartImport = async () => {
  // 防止重复点击
  if (importInProgress.value) {
    return;
  }

  importInProgress.value = true;
  importPreviewVisible.value = false;
  importResultVisible.value = true;
  
  // 准备导入结果统计
  importResults.value = {
    total: modifiedImportData.value.length,
    current: 0,
    success: 0,
    failed: 0,
    details: []
  };

  // 获取获奖级别选项，用于匹配levelId
  await fetchLevels();
  
  // 逐个处理获奖数据
  for (const item of modifiedImportData.value) {
    importResults.value.current++;
    
    try {
      // 准备获奖数据
      const awardData = { ...item.rawData };
      
      // 查找对应的levelId
      if (awardData.levelName && levelOptions.value) {
        const level = levelOptions.value.find(l => l.levelName === awardData.levelName);
        if (level) {
          awardData.levelId = level.id;
        } else {
          console.warn(`未找到匹配的级别: ${awardData.levelName}`);
        }
      }

      // 确保参与者有userId
      if (awardData.participants) {
        awardData.participants = awardData.participants.filter(p => p.userId);
      }

      // 调用创建获奖API
      const response = await createAward(awardData);
      
      if (response && response.code === 200) {
        importResults.value.success++;
        importResults.value.details.push({
          index: item.index,
          awardName: item.awardName,
          status: 'success',
          message: '导入成功'
        });
      } else {
        throw new Error(response?.message || '未知错误');
      }
    } catch (error) {
      importResults.value.failed++;
      importResults.value.details.push({
        index: item.index,
        awardName: item.awardName,
        status: 'error',
        message: `导入失败: ${error.message || '未知错误'}`
      });
    }

    // 更新导入结果状态
    importResults.value = { ...importResults.value };
  }
  
  importInProgress.value = false;
  
  // 所有数据处理完成后，提示用户
  if (importResults.value.failed === 0) {
    message.success(`所有${importResults.value.total}条记录已成功导入`);
  } else {
    message.warning(`导入完成，成功${importResults.value.success}条，失败${importResults.value.failed}条`);
  }
};

// 取消导入预览
const handleCancelImportPreview = () => {
  importPreviewVisible.value = false;
  importPreviewData.value = [];
  modifiedImportData.value = [];
  importPreviewLoading.value = false;
};

// 关闭导入结果弹窗
const handleCloseImportResult = () => {
  importResultVisible.value = false;
  // 刷新数据列表
  handleSearch();
};

// 导出失败记录
const handleExportFailedRecords = async () => {
  const failedRecords = importResults.value.details
    .filter(item => item.status === 'error')
    .map(item => {
      // 找到原始数据
      const originalItem = modifiedImportData.value.find(p => p.index === item.index);
      return originalItem ? {
        ...originalItem.rawData,
        error: item.message
      } : {
        awardName: item.awardName,
        error: item.message
      };
    });

  if (failedRecords.length === 0) {
    message.info('没有失败记录可导出');
    return;
  }

  try {
    const fileName = `导入失败记录_${dayjs().format('YYYYMMDD_HHmmss')}`;
    await downloadJson(failedRecords, fileName);
    message.success('失败记录已导出');
  } catch (error) {
    console.error('导出失败记录出错:', error);
    message.error(`导出失败: ${error.message || '未知错误'}`);
  }
};

// 下载JSON数据
const handleDownloadJson = async () => {
  if (!importPreviewData.value || importPreviewData.value.length === 0) {
    message.error('没有可下载的数据');
    return;
  }

  try {
    // 导出原始数据而不是预览数据
    const dataToExport = importPreviewData.value.map(item => item.rawData || item);
    const fileName = `学生获奖指导导入数据_${dayjs().format('YYYYMMDD_HHmmss')}`;
    
    await downloadJson(dataToExport, fileName);
    message.success('JSON文件下载成功');
  } catch (error) {
    console.error('下载JSON文件失败:', error);
    message.error('下载失败: ' + (error.message || '未知错误'));
  }
};

// 预览表格列定义
const previewColumns = [
  { title: '序号', dataIndex: 'index', width: 60 },
  { title: '获奖名称', dataIndex: 'awardName', width: 200 },
  { title: '获奖级别', dataIndex: 'levelName', width: 120 },
  { title: '所属系/教研室', dataIndex: 'department', width: 120 },
  { title: '获奖日期', dataIndex: 'awardDate', width: 120 },
  { title: '参与者', dataIndex: 'participants', width: 180 },
  { title: '备注', dataIndex: 'remark', width: 180 },
  { title: '用户ID检查', dataIndex: 'userIdCheckStatus', width: 100 }
];

// 结果表格列定义
const resultColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60
  },
  {
    title: '获奖名称',
    dataIndex: 'awardName',
    key: 'awardName',
    width: 300,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '消息',
    dataIndex: 'message',
    key: 'message',
    width: 300,
    ellipsis: true
  }
];

// 处理重新提交审核
const handleResubmit = (record) => {
  Modal.confirm({
    title: '确认重新提交审核',
    content: '是否确认将该获奖记录重新提交审核？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await resubmitForReview(record.id);
    }
  });
};

// 重新提交审核接口调用
const resubmitForReview = async (id) => {
  try {
    isLoading.value = true;
    const response = await reapplyReview({ id });
    
    if (response && response.code === 200) {
      message.success('重新提交审核成功');
      fetchData(); // 刷新数据
    } else {
      message.error(response?.message || '重新提交审核失败');
    }
  } catch (error) {
    console.error('重新提交审核失败:', error);
    message.error('重新提交审核失败: ' + (error.message || error));
  } finally {
    isLoading.value = false;
  }
};

// 组件销毁时清理图表
onBeforeUnmount(() => {
  if (categoryChart) {
    categoryChart.dispose();
    categoryChart = null;
  }
  if (reviewStatusChart) {
    reviewStatusChart.dispose();
    reviewStatusChart = null;
  }
  if (timeChart) {
    timeChart.dispose();
    timeChart = null;
  }
});
</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';
</style>