const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义科研项目级别模型
module.exports = sequelize.define('research_projects_levels_rules', // 数据库表名为research_projects_levels
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: 'ID',
        },
        levelName: {
            type: DataTypes.STRING(255),
            notEmpty: true,
            notNull: true,
            allowNull: false,
            unique: true,
            comment: '项目级别名称',
        },
        score: {
            type: DataTypes.INTEGER,
            notNull: true,
            allowNull: false,
            comment: '核算分数',
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: '项目级别的详细描述',
        },
        createdBy: {
            type: DataTypes.UUID,
            notNull: true,
            allowNull: false,
            comment: '创建者ID',
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '创建时间',
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '更新时间',
        },
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
    }); 