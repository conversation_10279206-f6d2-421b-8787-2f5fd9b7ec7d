const { body, param, validationResult } = require('express-validator');
const { Op } = require('sequelize');
const apiResponse = require('@utils/apiResponse');
const { getUserInfoFromRequest } = require('@utils/others');

// 由于没有专门的设置表，我们使用简单的JSON存储方式
// 实际项目中可以创建专门的 notification_settings 表

/**
 * 获取用户通知设置
 * @route GET /v1/sys/notifications/settings/:userId
 * @group 通知设置管理
 * @param {string} userId.path - 用户ID
 * @returns {object} 200 - 成功返回用户通知设置
 */
exports.getUserNotificationSettings = [
    param('userId').notEmpty().withMessage('用户ID不能为空'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            const { userId } = req.params;
            
            // 验证用户权限：只能查看自己的设置或管理员可以查看所有
            const currentUser = await getUserInfoFromRequest(req);
            if (currentUser.id !== userId && !['ADMIN-LV2', 'SUPER'].includes(currentUser.role)) {
                return apiResponse.forbiddenResponse(res, '无权限查看该用户的设置');
            }

            // 返回默认设置（实际项目中应该从数据库读取）
            const defaultSettings = {
                emailNotification: true,
                smsNotification: false,
                browserNotification: true,
                systemNotification: true,
                importantNotification: true,
                reminderNotification: true,
                normalNotification: false,
                resultNotification: true,
                emailTime: '09:00',
                smsTime: '10:00',
                quietHours: {
                    enabled: true,
                    start: '22:00',
                    end: '08:00'
                }
            };

            return apiResponse.successResponseWithData(res, '获取通知设置成功', defaultSettings);
        } catch (error) {
            console.error('获取用户通知设置失败:', error);
            return apiResponse.errorResponse(res, '获取用户通知设置失败');
        }
    }
];

/**
 * 更新用户通知设置
 * @route PUT /v1/sys/notifications/settings/:userId
 * @group 通知设置管理
 * @param {string} userId.path - 用户ID
 * @param {object} settings.body - 通知设置数据
 * @returns {object} 200 - 成功更新用户通知设置
 */
exports.updateUserNotificationSettings = [
    param('userId').notEmpty().withMessage('用户ID不能为空'),
    body('emailNotification').optional().isBoolean().withMessage('邮件通知设置必须是布尔值'),
    body('smsNotification').optional().isBoolean().withMessage('短信通知设置必须是布尔值'),
    body('browserNotification').optional().isBoolean().withMessage('浏览器通知设置必须是布尔值'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            const { userId } = req.params;
            const settings = req.body;
            
            // 验证用户权限：只能修改自己的设置或管理员可以修改所有
            const currentUser = await getUserInfoFromRequest(req);
            if (currentUser.id !== userId && !['ADMIN-LV2', 'SUPER'].includes(currentUser.role)) {
                return apiResponse.forbiddenResponse(res, '无权限修改该用户的设置');
            }

            // 这里应该保存到数据库，现在只是模拟成功
            console.log(`用户 ${userId} 的通知设置已更新:`, settings);

            return apiResponse.successResponseWithData(res, '通知设置更新成功', settings);
        } catch (error) {
            console.error('更新用户通知设置失败:', error);
            return apiResponse.errorResponse(res, '更新用户通知设置失败');
        }
    }
];

/**
 * 重置用户通知设置为默认值
 * @route POST /v1/sys/notifications/settings/:userId/reset
 * @group 通知设置管理
 * @param {string} userId.path - 用户ID
 * @returns {object} 200 - 成功重置用户通知设置
 */
exports.resetUserNotificationSettings = [
    param('userId').notEmpty().withMessage('用户ID不能为空'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorResponse(res, errors.array()[0].msg);
            }

            const { userId } = req.params;
            
            // 验证用户权限
            const currentUser = await getUserInfoFromRequest(req);
            if (currentUser.id !== userId && !['ADMIN-LV2', 'SUPER'].includes(currentUser.role)) {
                return apiResponse.forbiddenResponse(res, '无权限重置该用户的设置');
            }

            // 默认设置
            const defaultSettings = {
                emailNotification: true,
                smsNotification: false,
                browserNotification: true,
                systemNotification: true,
                importantNotification: true,
                reminderNotification: true,
                normalNotification: false,
                resultNotification: true,
                emailTime: '09:00',
                smsTime: '10:00',
                quietHours: {
                    enabled: true,
                    start: '22:00',
                    end: '08:00'
                }
            };

            return apiResponse.successResponseWithData(res, '通知设置已重置为默认值', defaultSettings);
        } catch (error) {
            console.error('重置用户通知设置失败:', error);
            return apiResponse.errorResponse(res, '重置用户通知设置失败');
        }
    }
];
