// 性能管理页面布局样式
// 用于统一所有性能页面的布局和容器样式

// 页面容器样式
.app-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.performance-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

// 卡片容器样式
.performance-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-weight: 600;
      color: #262626;
    }
  }
  
  .ant-card-body {
    padding: 16px;
  }
}

// 搜索表单样式
.search-form {
  .performance-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
    
    .ant-form-item-label {
      font-weight: 500;
    }
  }
  
  .search-actions-inline {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    
    .ant-btn {
      min-width: 80px;
    }
  }
}

// 表格容器样式
.performance-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
      color: #262626;
      border-bottom: 2px solid #f0f0f0;
    }
    
    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f0f0f0;
    }
    
    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }
}

// 表格底部信息样式
.table-footer {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-radius: 0 0 8px 8px;
  
  .table-info {
    display: flex;
    gap: 24px;
    
    span {
      font-size: 14px;
      color: #666;
      
      &.total-score {
        font-size: 16px;
        font-weight: bold;
        color: #1890ff;
      }
    }
  }
}

// 操作按钮区域样式
.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  
  .operations-left {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .operations-right {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}

// 筛选器样式
.range-filter {
  margin: 16px 0;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
  
  .ant-radio-group {
    display: flex;
    gap: 16px;
  }
}

// 响应式布局
@media (max-width: 768px) {
  .app-container,
  .performance-page {
    padding: 12px;
  }
  
  .performance-card {
    margin-bottom: 12px;
    
    .ant-card-body {
      padding: 12px;
    }
  }
  
  .table-operations {
    flex-direction: column;
    align-items: stretch;
    
    .operations-left,
    .operations-right {
      justify-content: center;
    }
  }
  
  .search-actions-inline {
    justify-content: center;
  }
  
  .table-footer {
    flex-direction: column;
    gap: 8px;
    text-align: center;
    
    .table-info {
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}

@media (max-width: 576px) {
  .performance-table {
    .ant-table {
      font-size: 12px;
    }
  }
  
  .search-actions-inline {
    .ant-btn {
      min-width: 70px;
      font-size: 12px;
    }
  }
}
