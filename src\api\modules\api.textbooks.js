import request from '../server'

/**
 * 获取教材与著作列表
 * @param {Object} params - 查询参数
 * @param {string} [params.materialName] - 教材与著作名称（模糊搜索）
 * @param {string} [params.categoryId] - 类别ID
 * @param {string} [params.startDate] - 开始日期
 * @param {string} [params.endDate] - 结束日期
 * @param {string} [params.userId] - 用户ID（可选，传入则获取特定用户的教材与著作）
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @param {string} [params.range='all'] - 范围筛选，可选值：all, in, out
 * @param {string} [params.reviewStatus='all'] - 审核状态筛选，可选值：all, reviewed, unreviewed
 * @returns {Promise} - 请求结果
 */
export function getTextbookList(data) {
  return request.post('/textbooks/list', data)
}

/**
 * 获取教材与著作详情
 * @param {Object} params 请求参数
 * @returns {Promise} 请求的结果
 */
export function getTextbookDetail(id) {
  return request.get(`/textbooks/detail/${id}`)
}

/**
 * 创建教材与著作（支持文件上传）
 * @param {Object} data 请求参数
 * @returns {Promise} 请求的结果
 */
export function createTextbook(data) {
  // 创建FormData对象用于包含文件上传
  const formData = new FormData();

  // 添加基本信息
  for (const key in data) {
    if (key !== 'files') {
      formData.append(key, data[key]);
    }
  }

  // 添加文件
  if (data.files && data.files.length) {
    data.files.forEach(file => {
      formData.append('files', file);
    });
  }

  return request.post('/textbooks/create', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 更新教材与著作（支持文件上传）
 * @param {Object} data 请求参数
 * @returns {Promise} 请求的结果
 */
export function updateTextbook(data) {
  // 创建FormData对象用于包含文件上传
  const formData = new FormData();

  // 添加基本信息
  for (const key in data) {
    if (key !== 'files') {
      formData.append(key, data[key]);
    }
  }

  // 添加文件
  if (data.files && data.files.length) {
    data.files.forEach(file => {
      formData.append('files', file);
    });
  }

  return request.post(`/textbooks/update/${data.id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 删除教材与著作
 * @param {Object} params 请求参数
 * @returns {Promise} 请求的结果
 */
export function deleteTextbook(id) {
  return request.post('/textbooks/delete', id)
}

/**
 * 获取教材与著作类别列表
 * @returns {Promise} 请求的结果
 */
export function getTextbookCategoryList() {
  return request.get('/textbookCategories')
}

/**
 * 创建教材与著作类别
 * @param {Object} params 请求参数
 * @returns {Promise} 请求的结果
 */
export function createTextbookCategory(data) {
  return request.post('/textbookCategories', data)
}

/**
 * 更新教材与著作类别
 * @param {Object} params 请求参数
 * @returns {Promise} 请求的结果
 */
export function updateTextbookCategory(id, data) {
  return request.put(`/textbookCategories/${id}`, data)
}

/**
 * 删除教材与著作类别
 * @param {Object} params 请求参数
 * @returns {Promise} 请求的结果
 */
export function deleteTextbookCategory(id) {
  return request.delete(`/textbookCategories/${id}`)
}

/**
 * 审核教材与著作
 * @param {Object} data - 审核数据
 * @param {string} data.id - 教材与著作ID
 * @param {number} data.reviewStatus - 审核状态(1:通过 2:拒绝)
 * @param {string} [data.reviewComment] - 审核意见
 * @param {string} [data.reviewer] - 审核人ID
 * @returns {Promise} - 请求结果
 */
export function reviewTextbook(data) {
  return request.post('/textbooks/review', data);
}

/**
 * 导入教材与著作数据
 * @param {File} file - 文件对象
 * @returns {Promise} - 请求结果
 */
export function importTextbooks(data) {
  return request.post('/textbooks/import', data, null, 'multipart/form-data')
}

/**
 * 导出教材与著作数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function exportTextbooks(params) {
  return request.download('/textbooks/export', params)
}

/**
 * 获取教材与著作统计概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getTextbookStatistics(params) {
  return request.get('/textbooks/statistics/overview', params)
}

/**
 * 获取教材与著作类别分布
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getTextbookCategoryDistribution(params) {
  return request.get('/textbooks/statistics/categoryDistribution', params)
}

/**
 * 获取教材与著作审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getTextbookReviewStatusOverview(params) {
  return request.get('/textbooks/statistics/reviewStatus', params)
}

/**
 * 获取教材与著作数量统计
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getTextbookCountStatistics(params) {
  return request.get('/textbooks/statistics/count', params)
}

/**
 * 获取教材与著作日期分布
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getTextbookDateDistribution(params) {
  return request.get('/textbooks/statistics/dateDistribution', params)
}

/**
 * 获取教师教材数量排行
 * @param {Object} params 参数对象
 * @param {string} params.range 统计范围 'all', 'in', 'out'
 * @param {string} params.reviewStatus 审核状态 'all', 'reviewed', 'rejected', 'pending'
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页条数
 * @param {boolean} params.isExport 是否导出，导出时不应用分页
 * @returns {Promise} 请求结果
 */
export function getAuthorRanking(params) {
  return request.post('/textbooks/statistics/teacher-ranking', params);
}

/**
 * 获取教材与著作总分统计
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */

export function getTextbooksTotalScore(params) {
  return request.post('/textbooks/statistics/textbooks-total-score', params);
}

/**
 * 获取用户教材与著作详情
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */

export function getUserTextbooksDetail(params) {
  return request.post('/textbooks/user/textbook-details', params);
}

/**重新提交审核 */
export function reapplyReview(params) {
  return request.post('/textbooks/reapply', params)
}