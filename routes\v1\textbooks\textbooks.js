const express = require('express');
const router = express.Router();
const textbooksController = require('../../../controllers/v1/textbooks/textbooksController');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建教材与著作权限中间件函数
const textbooksPermission = (action) => createModulePermission('textbooks', action);

/**
 * 获取教材与著作列表
 * @route POST /v1/sys/textbooks/list
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} materialName - 教材与著作名称（模糊搜索）
 * @param {string} categoryId - 类别ID
 * @param {string} startDate - 起始日期
 * @param {string} endDate - 结束日期
 * @param {string} userId - 用户ID（可选，如果提供则获取特定用户的教材）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 范围筛选，可选值：all, in, out
 * @param {string} reviewStatus - 审核状态筛选，可选值：all, reviewed, unreviewed
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], totalScore: 0, pagination: {total: 0, page: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', 
  authMiddleware, 
  textbooksPermission('list'), 
  textbooksController.getTextbooks
);

/**
 * 导入教材与著作数据
 * @route POST /v1/sys/textbooks/import
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', 
  authMiddleware, 
  textbooksPermission('import'), 
  upload.single('file'), 
  textbooksController.importTextbooks
);

/**
 * 导出教材与著作数据
 * @route GET /v1/sys/textbooks/export
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} materialName.query - 教材与著作名称（模糊搜索）
 * @param {string} categoryId.query - 类别ID
 * @param {string} userId.query - 用户ID
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/export', 
  authMiddleware, 
  textbooksPermission('export'), 
  textbooksController.exportTextbooks
);

/**
 * 创建教材与著作
 * @route POST /v1/sys/textbooks/create
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} materialName.body.required - 教材与著作名称
 * @param {string} publishDate.body.required - 出版日期
 * @param {string} categoryId.body.required - 类别ID
 * @param {string} remark.body - 备注
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', 
  authMiddleware, 
  textbooksPermission('create'), 
  upload.array('files', 5), 
  textbooksController.createTextbook
);

/**
 * 更新教材与著作
 * @route POST /v1/sys/textbooks/update/:id
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} id.path.required - 教材与著作ID
 * @param {string} materialName.body - 教材与著作名称
 * @param {string} publishDate.body - 出版日期
 * @param {string} categoryId.body - 类别ID
 * @param {string} remark.body - 备注
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @param {Array} deletedFileIds.body - 要删除的文件ID数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update/:id', 
  authMiddleware, 
  textbooksPermission('update'), 
  upload.array('files', 5), 
  textbooksController.updateTextbook
);

// 保留旧的路由以兼容
router.post('/update', 
  authMiddleware, 
  textbooksPermission('update'), 
  async (req, res) => {
    const { id, ...updateData } = req.body;
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少教材与著作ID',
        data: null
      });
    }
    req.params = { id };
    req.body = updateData;
    await textbooksController.updateTextbook(req, res);
  }
);

/**
 * 审核教材与著作
 * @route POST /v1/sys/textbooks/review
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} id.body.required - 教材与著作ID
 * @param {number} reviewStatus.body.required - 审核状态(1:通过 2:拒绝)
 * @param {string} reviewComment.body - 审核意见
 * @param {string} reviewer.body - 审核人ID
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/review', 
  authMiddleware, 
  textbooksPermission('review'), 
  textbooksController.reviewTextbook
);

/**
 * 删除教材与著作
 * @route POST /v1/sys/textbooks/delete
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} id.body.required - 教材与著作ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', 
  authMiddleware, 
  textbooksPermission('delete'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await textbooksController.deleteTextbook(req, res);
  }
);

/**
 * 获取教材与著作详情
 * @route GET /v1/sys/textbooks/:id
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} id.path.required - 教材与著作ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail/:id', 
  authMiddleware, 
  textbooksPermission('detail'), 
  textbooksController.getTextbookDetail
);

/**
 * 获取教材与著作统计数据
 * @route GET /v1/sys/textbooks/statistics/overview
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} userId.query - 用户ID（可选，传入则只统计该用户的数据）
 * @returns {object} 200 - {code: 200, message: "获取统计数据成功", data: {totalTextbooks: 0, inRangeTextbooks: 0, averageScore: 0, reviewCompletionRate: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/statistics/overview', 
  authMiddleware, 
  textbooksPermission('getStatisticsOverview'), 
  textbooksController.getStatisticsOverview
);

/**
 * 获取类别分布数据
 * @route GET /v1/sys/textbooks/statistics/categoryDistribution
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} userId.query - 用户ID（可选，传入则只统计该用户的数据）
 * @returns {object} 200 - {code: 200, message: "获取类别分布数据成功", data: {categoryDistribution: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/statistics/categoryDistribution', 
  authMiddleware, 
  textbooksPermission('getCategoryDistribution'), 
  textbooksController.getCategoryDistribution
);

/**
 * 获取审核状态数据
 * @route GET /v1/sys/textbooks/statistics/reviewStatus
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} userId.query - 用户ID（可选，传入则只统计该用户的数据）
 * @returns {object} 200 - {code: 200, message: "获取审核状态数据成功", data: {reviewStatusOverview: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/statistics/reviewStatus', 
  authMiddleware, 
  textbooksPermission('getReviewStatusOverview'), 
  textbooksController.getReviewStatusOverview
);

/**
 * 获取数量统计数据
 * @route GET /v1/sys/textbooks/statistics/count
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} userId.query - 用户ID（可选，传入则只统计该用户的数据）
 * @returns {object} 200 - {code: 200, message: "获取数量统计数据成功", data: {countStatistics: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/statistics/count', 
  authMiddleware, 
  textbooksPermission('getCountStatistics'), 
  textbooksController.getCountStatistics
);

/**
 * 获取日期分布数据
 * @route GET /v1/sys/textbooks/statistics/dateDistribution
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} userId.query - 用户ID（可选，传入则只统计该用户的数据）
 * @returns {object} 200 - {code: 200, message: "获取日期分布数据成功", data: {dateDistribution: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/statistics/dateDistribution', 
  authMiddleware, 
  textbooksPermission('getDateDistribution'), 
  textbooksController.getDateDistribution
);

/**
 * 获取教师教材数量排行
 * @route GET /v1/sys/textbooks/statistics/teacher-ranking
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} range.query - 统计范围，可选值：'in', 'out', 'all'，默认'all'
 * @returns {object} 200 - {code: 200, message: "获取教师教材数量排行成功", data: [{userId, nickname, studentNumber, textbooksCount, totalScore}]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-ranking', 
  authMiddleware, 
  textbooksPermission('getAuthorRanking'), 
  textbooksController.getAuthorRanking
);

/**
 * 获取教材与著作总分统计
 * @route POST /v1/sys/textbooks/statistics/textbooks-total-score
 * @group 教材与著作统计 - 教材与著作分数统计相关接口
 * @param {string} range - 数据范围: 'in' (统计范围内), 'out' (统计范围外), 'all' (全部，默认)
 * @param {string} reviewStatus - 审核状态:  'rejected' (已拒绝), 'pending' (待审核), 'reviewed' (已审核), 'all' (全部，默认)
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {categoryStats: [{categoryId, categoryName, count, totalScore}], overallStats: {totalTextbooks, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/textbooks-total-score', 
  authMiddleware, 
  textbooksPermission('getTextbooksTotalScore'), 
  textbooksController.getTextbooksTotalScore
);

/**
 * 获取用户教材与著作详情
 * @route POST /v1/sys/textbooks/user/textbook-details
 * @group 教材与著作统计 - 用户教材与著作详情相关接口
 * @param {string} userId.required - 用户ID
 * @param {string} range - 数据范围: 'in' (统计范围内), 'out' (统计范围外), 'all' (全部，默认)
 * @param {string} reviewStatus - 审核状态: 'rejected' (已拒绝), 'pending' (待审核), 'reviewed' (已审核), 'all' (全部，默认)
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{教材详情}], totalCount: 总记录数, statistics: {totalTextbooks, totalScore, approvedTextbooks, activeTextbooks}, timeInterval: {startTime, endTime, name}, pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/textbook-details', 
  authMiddleware, 
  textbooksPermission('getUserTextbooksDetail'), 
  textbooksController.getUserTextbooksDetail
);

/**
 * 重新提交教材审核
 * @route POST /v1/sys/textbooks/reapply
 * @group 教材与著作管理 - 教材与著作相关接口
 * @param {string} id.body.required - 教材ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/reapply', 
  authMiddleware, 
  textbooksPermission('reapply'),
  textbooksController.reapply
);

module.exports = router; 