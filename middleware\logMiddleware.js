const { Log } = require('../models');
const fs = require('fs');
const path = require('path');
const dayjs = require('dayjs');

/**
 * 操作日志中间件
 * 记录用户的API操作
 */
const logMiddleware = async (req, res, next) => {
  const startTime = Date.now();
  
  console.log(`[${dayjs().format('YYYY-MM-DD HH:mm:ss')}] ${req.method} ${req.originalUrl}`);
  console.log(`请求头: ${JSON.stringify(req.headers)}`);
  
  if (req.method !== 'GET') {
    console.log(`请求体: ${JSON.stringify(req.body)}`);
  }
  
  // 保存原始的res.json方法
  const originalJson = res.json;
  let responseBody;
  
  // 重写res.json方法，以捕获响应内容
  res.json = function(body) {
    responseBody = body;
    // 记录响应信息
    console.log(`[${dayjs().format('YYYY-MM-DD HH:mm:ss')}] 响应状态: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.getHeaders())}`);
    console.log(`响应体: ${JSON.stringify(body)}`);

    // 写入日志文件
    const logDir = path.join(__dirname, '../logs');
    
    // 确保日志目录存在
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    const logFile = path.join(logDir, `${dayjs().format('YYYY-MM-DD')}.log`);
    const logContent = `
[${dayjs().format('YYYY-MM-DD HH:mm:ss')}]
请求: ${req.method} ${req.originalUrl}
请求头: ${JSON.stringify(req.headers)}
请求体: ${req.method !== 'GET' ? JSON.stringify(req.body) : ''}
响应状态: ${res.statusCode}
响应头: ${JSON.stringify(res.getHeaders())}
响应体: ${JSON.stringify(body)}
-------------------------------------------------------------
`;
    
    fs.appendFile(logFile, logContent, err => {
      if (err) {
        console.error('写入日志文件失败:', err);
      }
    });

    return originalJson.call(this, body);
  };
  
  // 当请求结束时记录日志
  res.on('finish', async () => {
    try {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 获取请求信息
      const { method, originalUrl, body, ip } = req;
      const userId = req.user?.id || null;
      const username = req.user?.username || '未登录用户';
      
      // 获取响应状态
      const status = res.statusCode;
      const success = status >= 200 && status < 400;
      
      // 确定操作类型
      let operation = method;
      if (originalUrl.includes('login')) {
        operation = '登录';
      } else if (originalUrl.includes('logout')) {
        operation = '登出';
      } else if (method === 'POST' && originalUrl.includes('create')) {
        operation = '创建';
      } else if (method === 'POST' && originalUrl.includes('update')) {
        operation = '更新';
      } else if (method === 'POST' && originalUrl.includes('delete')) {
        operation = '删除';
      } else if (method === 'POST' && originalUrl.includes('import')) {
        operation = '导入';
      } else if (method === 'POST' && originalUrl.includes('export')) {
        operation = '导出';
      } else if (method === 'POST' && originalUrl.includes('list')) {
        operation = '查询列表';
      } else if (method === 'POST' && originalUrl.includes('detail')) {
        operation = '查询详情';
      }
      
      // 创建日志记录
      await Log.create({
        userId,
        username,
        operation,
        method,
        url: originalUrl,
        ip,
        requestParams: JSON.stringify(body),
        responseData: responseBody ? JSON.stringify(responseBody).substring(0, 500) : null, // 限制长度
        status,
        success,
        duration,
        createdAt: new Date(),
      });
    } catch (error) {
      console.error('日志记录失败:', error);
      // 日志记录失败不影响主流程
    }
  });
  
  next();
};

module.exports = {
  logMiddleware
}; 