import request from '../server'

// API 路径配置
const api = {
  list: '/researchProjectsLevels/list',
  all: '/researchProjectsLevels/all',
  detail: '/researchProjectsLevels/detail',
  create: '/researchProjectsLevels/create',
  update: '/researchProjectsLevels/update',
  delete: '/researchProjectsLevels/delete',
  batchDelete: '/researchProjectsLevels/batch-delete',
  import: '/researchProjectsLevels/import',
  export: '/researchProjectsLevels/export'
}

/**
 * 获取科研项目级别列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getResearchProjectsLevels(params) {
  const { page = 1, pageSize = 10, levelName } = params || {};
  
  // 构造参数对象
  const queryParams = {
    page,
    pageSize,
    levelName
  };
  
  return request.get(api.list, queryParams);
}

/**
 * 获取所有科研项目级别
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getAllResearchProjectsLevels(params) {  
  return request.get(api.all, params);
}

/**
 * 获取科研项目级别详情
 * @param {string} id - 级别ID
 * @returns {Promise} - 返回Promise对象
 */
export function getResearchProjectLevelDetail(id) {
  return request.get(api.detail, { id });
}

/**
 * 创建科研项目级别
 * @param {Object} data - 级别数据
 * @returns {Promise} - 返回Promise对象
 */
export function addResearchProjectLevel(data) {
  return request.post(api.create, data);
}

/**
 * 更新科研项目级别
 * @param {string} id - 级别ID
 * @param {Object} data - 级别数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateResearchProjectLevel(id, data) {
  return request.put(api.update, { id, ...data });
}

/**
 * 删除科研项目级别
 * @param {string} id - 级别ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteResearchProjectLevel(id) {
  return request.delete(api.delete, { id });
}

/**
 * 批量删除科研项目级别
 * @param {Array} ids - 级别ID数组
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteResearchProjectLevels(ids) {
  return request.delete(api.batchDelete, { ids });
}

/**
 * 导入科研项目级别数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回Promise对象
 */
export function importResearchProjectLevels(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request.post(api.import, formData, null, 'multipart/form-data');
}

/**
 * 导出科研项目级别数据
 * @param {Object} params - 过滤参数
 * @returns {Promise} - 返回Promise对象
 */
export function exportResearchProjectLevels(params) {
  return request.get(api.export, params, { responseType: 'blob' });
} 