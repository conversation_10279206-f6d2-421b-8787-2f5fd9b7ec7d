const userModel = require('../../../models/v1/mapping/userModel');
const highLevelPapersRulesModel = require('../../../models/v1/mapping/highLevelPapersRulesModel');
const highLevelPapersModel = require('../../../models/v1/mapping/highLevelPapersModel');
const highLevelPaperParticipantsModel = require('../../../models/v1/mapping/highLevelPaperParticipantsModel');
const { v4: uuidv4 } = require('uuid');
const { Op, Sequelize } = require('sequelize');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const fileModel = require('../../../models/v1/mapping/fileModel');
const upload = multer({ dest: 'uploads/' });
const { getTimeIntervalByName, getUserInfoFromRequest, getSequelizeInstance, isProjectInTimeRange } = require('../../../utils/others');
const { updateUserRankings } = require('../../../utils/rankingUtils');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');


// 使用导入的模型
const HighLevelPaper = highLevelPapersModel;
const HighLevelPaperParticipant = highLevelPaperParticipantsModel;
const User = userModel;
const HighLevelPapersRules = highLevelPapersRulesModel;

/**
 * 获取高水平论文列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPapers = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, title, paperLevelId, type, journal, authors, publishStartDate, publishEndDate, userId, reviewStatus = 'all', range = 'all', isExport = false } = req.query;
    // 构建查询条件
    const where = { status: 1 };
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = true; // 已通过审核
    } else if (reviewStatus === 'rejected') {
      where.ifReviewer = false; // 已拒绝
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null; // 待审核
    }

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("highLevelPapers");

    // 根据range参数确定时间范围筛选条件
    if (range === 'in' && timeInterval) {
      // 设置在指定时间区间内的条件
      where.publishDate = {
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 设置在指定时间区间外的条件
      where.publishDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    } else {
      console.log('查询所有论文，不限制时间范围');
    }
    
    // 标题模糊查询
    if (title) {
      where.title = { [Op.like]: `%${title}%` };
    }
    
    // 论文级别精确匹配
    if (paperLevelId) {
      where.paperLevelId = paperLevelId;
    }
    
    // 论文类型查询（根据前端传递的type参数）
    if (type) {
      // 先查询对应的paperLevel记录
      const paperLevel = await HighLevelPapersRules.findOne({
        where: { paperLevel: type }
      });
      
      if (paperLevel) {
        where.paperLevelId = paperLevel.id;
      }
    }
    
    // 期刊模糊查询
    if (journal) {
      where.journal = { [Op.like]: `%${journal}%` };
    }
    
    // 出版时间范围查询
    if (publishStartDate && publishEndDate) {
      where.publishDate = {
        [Op.between]: [publishStartDate, publishEndDate]
      };
    } else if (publishStartDate) {
      where.publishDate = {
        [Op.gte]: publishStartDate
      };
    } else if (publishEndDate) {
      where.publishDate = {
        [Op.lte]: publishEndDate
      };
    }
    
    console.log('最终查询条件:', JSON.stringify(where));
    
    // 构建包含内容
    const include = [
      {
        model: User,
        as: 'submitter',
        attributes: ['id', 'nickname', 'username'],
        required: false
      },
      {
        model: User,
        as: 'reviewer',
        attributes: ['id', 'nickname', 'username'],
        required: false
      },
      {
        model: HighLevelPapersRules,
        as: 'paperLevel',
        attributes: ['paperLevel', 'score', 'description'],
        required: false
      },
      {
        model: HighLevelPaperParticipant,
        as: 'participants',
        required: userId ? true : false,  // 如果有userId则必须匹配
        where: userId ? { userId: userId } : {},  // 添加用户ID过滤条件
        include: [{
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'username'],
          required: false,
          where: authors ? {
            [Op.or]: [
              { nickname: { [Op.like]: `%${authors}%` } },
              { username: { [Op.like]: `%${authors}%` } }
            ]
          } : {}
        }]
      }
    ];

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 设置查询选项
    const options = {
      where,
      order: [['publishDate', 'DESC']],
      include,
      distinct: true  // 添加distinct确保计数正确
    };

    // 如果不是导出操作，应用分页
    if (!isExport) {
      options.limit = pageSizeNum;
      options.offset = offset;
    }
    
    // 查询数据
    const result = await HighLevelPaper.findAndCountAll(options);
    
    // 处理数据，添加作者信息
    const enhancedRows = result.rows.map(paper => {
      const paperData = paper.toJSON ? paper.toJSON() : paper;
      
      // 不需要添加从 paperLevel 获取的分数，因为已经通过关联查询获取到
      
      // 添加作者详细信息
      if (paperData.participants && paperData.participants.length > 0) {
        // 提取作者信息
        paperData.authorDetails = paperData.participants.map(p => ({
          id: p.userId,
          name: p.user ? (p.user.nickname || p.user.username) : p.userId,
          allocationRatio: p.allocationRatio,
          authorRank: p.authorRank,
          isFirstAuthor: p.isFirstAuthor,
          isCorrespondingAuthor: p.isCorrespondingAuthor
        }));
        
        // 生成作者字符串
        paperData.authors = paperData.authorDetails
          .sort((a, b) => (a.authorRank || 999) - (b.authorRank || 999))
          .map(a => a.name)
          .join(', ');
        
        // 生成分配比例字符串
        paperData.allocationRatio = paperData.authorDetails
          .map(a => a.allocationRatio)
          .join(', ');
      } else {
        paperData.authorDetails = [];
        paperData.authors = '';
        paperData.allocationRatio = '';
      }
      
      return paperData;
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: enhancedRows,
        pagination: {
          total: result.count,
          page: pageNum,
          pageSize: pageSizeNum,
          totalPages: Math.ceil(result.count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取高水平论文列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取高水平论文列表失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 获取高水平论文详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPaperById = async (req, res) => {
  try {
    console.log("222222222");
    const { id } = req.params;
    
    const paper = await HighLevelPaper.findByPk(id, {
      include: [
        {
          model: User,
          as: 'submitter',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: HighLevelPapersRules,
          as: 'paperLevel',
          attributes: ['paperLevel', 'score', 'description'],
          required: false
        },
        {
          model: HighLevelPaperParticipant,
          as: 'participants',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'username', 'studentNumber']
          }]
        }
      ]
    });
    
    if (!paper) {
      return res.status(404).json({
        code: 404,
        message: '高水平论文不存在',
        data: null
      });
    }
    
    // 获取论文数据
    const paperData = paper.toJSON();
    
    // 查询论文关联的文件列表
    const files = await fileModel.findAll({
      where: {
        relatedId: id,
        relatedType: 'high_level_papers',
        isDeleted: 0
      },
      order: [['createdAt', 'DESC']]
    });
    
    // 处理文件信息，添加URL和其他前端需要的信息
    paperData.attachments = files.map(file => {
      const fileData = file.toJSON();
      // 构造文件URL
      const filePath = fileData.filePath;
      const url = filePath.startsWith('/') ? filePath : `/${filePath}`;
      
      return {
        id: fileData.id,
        name: fileData.originalName || fileData.fileName,
        fileName: fileData.fileName,
        fileSize: fileData.fileSize,
        fileType: fileData.mimeType,
        extension: fileData.extension,
        url: url,
        fileUrl: url,
        filePath: fileData.filePath,
        uploadTime: fileData.createdAt
      };
    });
    
    // 处理参与者信息
    if (paperData.participants && paperData.participants.length > 0) {
      // 提取作者信息
      paperData.authorDetails = paperData.participants.map(p => ({
        id: p.userId,
        nickname: p.user ? (p.user.nickname || p.user.username) : p.userId,
        username: p.user ? p.user.username : '',
        studentNumber: p.user ? p.user.studentNumber : '',
        allocationRatio: p.allocationRatio,
        authorRank: p.authorRank,
        isFirstAuthor: p.isFirstAuthor,
        isCorrespondingAuthor: p.isCorrespondingAuthor
      }));
      
      // 生成作者字符串
      paperData.authors = paperData.authorDetails
        .sort((a, b) => (a.authorRank || 999) - (b.authorRank || 999))
        .map(a => a.nickname)
        .join(', ');
      
      // 生成分配比例字符串
      paperData.allocationRatio = paperData.authorDetails
        .map(a => a.allocationRatio)
        .join(', ');
        
      // 找出第一作者
      const firstAuthor = paperData.participants.find(p => p.isFirstAuthor);
      if (firstAuthor) {
        paperData.firstAuthorId = firstAuthor.userId;
        paperData.firstAuthorName = firstAuthor.user ? (firstAuthor.user.nickname || firstAuthor.user.username) : firstAuthor.userId;
      }
      
      // 找出通讯作者
      const correspondingAuthor = paperData.participants.find(p => p.isCorrespondingAuthor);
      if (correspondingAuthor) {
        paperData.correspondingAuthorId = correspondingAuthor.userId;
        paperData.correspondingAuthorName = correspondingAuthor.user ? (correspondingAuthor.user.nickname || correspondingAuthor.user.username) : correspondingAuthor.userId;
      }
    } else {
      paperData.authorDetails = [];
      paperData.authors = '';
      paperData.allocationRatio = '';
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: paperData
    });
  } catch (error) {
    console.error('获取高水平论文详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取高水平论文详情失败',
      data: null
    });
  }
};

/**
 * 创建高水平论文
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createPaper = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(HighLevelPaper);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { 
      title,
      journal,
      paperLevelId,
      doi,
      publishDate,
      impactFactor,
      isFirstAffiliationOurs,
      submitterId,
      participants,
      fileIds,
      attachmentUrl,
      collegeCorrespondentAuthorNumber,
      correspondentAuthorNumber,
      remark,
      citations,
      status,
      // 添加新增字段
      allocationProportionBase,
      totalAllocationProportion,
      submitterRanking
    } = req.body;
    

    // 验证必要字段
    if (!title || !journal || !publishDate || !paperLevelId) {
      console.log('创建高水平论文请求参数body:', req.body);
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少必要字段'
      });
    }
    
    // 验证参与者数据
    let participantsList = [];
    if (participants) {
      try {
        if (typeof participants === 'string') {
          participantsList = JSON.parse(participants);
        } else {
          participantsList = participants;
        }
        
        if (!Array.isArray(participantsList) || participantsList.length === 0) {
          await transaction.rollback();
      return res.status(400).json({
        code: 400,
            message: '参与者数据格式不正确或为空'
      });
    }
      } catch (error) {
        console.error('解析参与者列表出错', error);
        await transaction.rollback();
      return res.status(400).json({
        code: 400,
          message: '参与者数据格式不正确'
      });
    }
    } else {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少参与者数据'
      });
    }
    
    // 创建高水平论文
    const newPaper = await highLevelPapersModel.create({
      title,
      journal,
      paperLevelId,
      doi,
      publishDate,
      impactFactor: parseFloat(impactFactor) || 0,
      isFirstAffiliationOurs: isFirstAffiliationOurs === '1' || isFirstAffiliationOurs === 1 || isFirstAffiliationOurs === true ? true : false,
      submitterId,
      collegeCorrespondentAuthorNumber: parseInt(collegeCorrespondentAuthorNumber) || 0,
      correspondentAuthorNumber: parseInt(correspondentAuthorNumber) || 0,
      remark,
      citations: parseInt(citations) || 0,
      status: status ? parseInt(status) : 1,
      // 新增字段
      allocationProportionBase: allocationProportionBase || null,
      totalAllocationProportion: totalAllocationProportion || null,
      submitterRanking: submitterRanking || null,
      // 初始先设置为null，稍后再用数据库生成的论文ID更新
      attachmentUrl: null
    }, { transaction });
    
    // 添加参与者
    if (participantsList && participantsList.length > 0) {
      const paperParticipants = participantsList.map((participant) => ({
        paperId: newPaper.id,
        userId: participant.userId,
        authorRank: participant.authorRank || 1,
        allocationRatio: parseFloat(participant.allocationRatio) || 0,
        isFirstAuthor: participant.isFirstAuthor === true || participant.isFirstAuthor === '1' || participant.isFirstAuthor === 1,
        isCorrespondingAuthor: participant.isCorrespondingAuthor === true || participant.isCorrespondingAuthor === '1' || participant.isCorrespondingAuthor === 1
      }));
      
      await HighLevelPaperParticipant.bulkCreate(paperParticipants, { transaction });
    }
    
    // 处理关联文件 - 使用前端传递的文件ID而非重新创建文件记录
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组，可能以JSON字符串形式传递
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl，可能以JSON字符串形式传递
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl === 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
      
      // 如果有文件ID，关联到论文
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            paperId: newPaper.id, // 设置文件的paperId为新创建的论文ID
            relatedId: newPaper.id,
            relatedType: 'high_level_papers' // 保持relatedType字段的兼容性
          };
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath; // 使用filePath字段，这是文件模型中的正确字段名
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );

          processedFileIds.push(fileId);
        }
      }
    } else {
      console.log('未提供文件ID，跳过文件关联处理');
    }
    
    // 提交事务
    await transaction.commit();

    // 事务提交成功后，异步移动文件到指定的论文目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'high_level_papers'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${newPaper.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 新增变量跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId, isDeleted: 0 }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
          continue;
        }
        
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                paperId: newPaper.id,
                relatedId: newPaper.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
      } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新论文的attachmentUrl为论文文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的论文文件夹路径更新论文的attachmentUrl，确保不包含文件名
            const paperFolderPath = `uploads\\high_level_papers\\${newPaper.id}\\`;
            
            await newPaper.update({
              attachmentUrl: paperFolderPath
            });
            console.log(`已更新论文 ${newPaper.id} 的attachmentUrl为: ${paperFolderPath}`);
          } catch (updateError) {
            console.error('更新论文attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响论文创建的返回结果，仅记录错误
        console.error('移动文件到论文目录失败:', moveError);
      }
    }
    
    return res.status(201).json({
      code: 201,
      message: '论文创建成功',
      data: newPaper
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('创建论文时发生错误:', error);
    console.error('请求体数据:', req.body);
    return res.status(500).json({
      code: 500,
      message: '创建论文失败: ' + error.message,
      requestData: req.body // 添加请求体数据到响应
    });
  }
};

/**
 * 更新高水平论文
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updatePaper = async (req, res) => {
  console.log("33333333");
  
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(HighLevelPaper);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { 
      title, journal, paperLevelId, doi, publishDate, 
      impactFactor, isFirstAffiliationOurs, participants, attachmentUrl, submitterId,
      collegeCorrespondentAuthorNumber, correspondentAuthorNumber,
      allocationProportionBase, totalAllocationProportion, 
      remark, citations, calculatedScore, status,
      fileIds, submitterRanking
    } = req.body;
    
    console.log("req.body====", req.body);
    
    // 查找论文
    const paper = await HighLevelPaper.findByPk(id, { 
      include: [
        {
          model: HighLevelPaperParticipant,
          as: 'participants',
          required: false
        },
        {
          model: highLevelPapersRulesModel,
          as: 'paperLevel',
          required: false
        }
      ],
      transaction 
    });
    
    if (!paper) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '高水平论文不存在',
        data: null
      });
    }

    // 如果论文已审核，需要更新用户排名
    if (paper.ifReviewer == 1) {
      try {
        console.log('开始更新用户排名数据，论文ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("highLevelPapers");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断论文变更前后是否在时间区间内（使用publishDate判断）
        const oldPublishDate = paper.publishDate;
        const newPublishDate = publishDate || paper.publishDate;
        console.log('论文发表日期 - 原始:', oldPublishDate, '新:', newPublishDate);
        
        const wasInTimeRange = timeInterval ? 
          isProjectInTimeRange(oldPublishDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
          
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(newPublishDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('论文时间范围状态 - 原始:', wasInTimeRange ? '在范围内' : '不在范围内', 
                   '变更后:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表（针对减分操作）
        let oldRankingTables = [];
        if (wasInTimeRange) {
          oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        // 确定需要更新的排名表（针对加分操作）
        let newRankingTables = [];
        if (isInTimeRange) {
          newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('排名表 - 原始:', oldRankingTables, '新:', newRankingTables);
        
        // 获取旧的级别分数
        const oldBaseScore = paper.paperLevel ? paper.paperLevel.score : 0;
        console.log('原始级别分数:', oldBaseScore);
        
        // 获取新的级别分数
        let newBaseScore = oldBaseScore;
        if (paperLevelId && paperLevelId !== paper.paperLevelId) {
          const newLevel = await highLevelPapersRulesModel.findByPk(paperLevelId, { transaction });
          if (newLevel) {
            newBaseScore = newLevel.score || 0;
          }
        }
        console.log('新级别分数:', newBaseScore);
        
        // 获取旧的参与者名单
        const oldParticipants = paper.participants || [];
        console.log('原始参与者数量:', oldParticipants.length);

        // 准备新的参与者列表
        let newParticipants = [];
        if (participants && Array.isArray(participants)) {
          newParticipants = participants;
        }
        console.log('新参与者数量:', newParticipants.length);
        
        // 从原始参与者中找出要删除的参与者 - 他们在旧列表中但不在新列表中
        const oldUserIds = oldParticipants.map(p => p.userId);
        const newUserIds = newParticipants.map(p => p.userId);
        
        // 找出要删除的参与者
        const deletedUserIds = oldUserIds.filter(id => !newUserIds.includes(id));
        console.log('要删除的参与者:', deletedUserIds);
        
        // 找出保留的参与者
        const keptUserIds = oldUserIds.filter(id => newUserIds.includes(id));
        console.log('保留的参与者:', keptUserIds);
        
        // 找出新增的参与者
        const addedUserIds = newUserIds.filter(id => !oldUserIds.includes(id));
        console.log('新增的参与者:', addedUserIds);
        
        // 1. 处理要删除的参与者 - 减少项目数量和分数
        if (deletedUserIds.length > 0) {
          const deletedParticipants = oldParticipants.filter(p => deletedUserIds.includes(p.userId));
          console.log('被删除的参与者完整数据:', JSON.stringify(deletedParticipants));
          
          const deletedIds = [];
          const deletedRatios = [];
          const deletedScores = [];
          const countDeltas = []; // 固定值为1的数组表示每人减少1个项目
          
          for (const participant of deletedParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            deletedIds.push(userId);
            deletedRatios.push(ratio);
            deletedScores.push(participantScore);
            countDeltas.push(1); // 每个被删除的参与者项目数-1
          }
          
          console.log('被删除参与者的排名更新数据:', {
            userIds: deletedIds,
            countDeltas: countDeltas,
            scores: deletedScores
          });
          
          // 对每个排名表执行减分操作
          for (const table of oldRankingTables) {
            console.log(`为被删除的参与者更新排名表 ${table}`);
            await updateUserRankings(
              deletedIds,
              table,
              'highLevelPapers',
              countDeltas, // 使用固定值1表示项目计数-1
              deletedScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去被删除参与者的分数和项目数');
        }
        
        // 2. 处理保留但分配比例变化的参与者 - 先减去原有分数，后面再加上新分数
        if (keptUserIds.length > 0) {
          const keptOldParticipants = oldParticipants.filter(p => keptUserIds.includes(p.userId));
          console.log('保留的参与者原始数据:', JSON.stringify(keptOldParticipants));
          
          const keptIds = [];
          const keptRatios = [];
          const keptScores = [];
          
          for (const participant of keptOldParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            keptIds.push(userId);
            keptRatios.push(ratio);
            keptScores.push(participantScore);
          }
          
          console.log('保留参与者的减分数据:', {
            userIds: keptIds,
            scores: keptScores
          });
          
          // 减去原有分数（但不减少项目计数）
          for (const table of oldRankingTables) {
            console.log(`为保留的参与者减去原有分数：${table}`);
            // 传递0表示不改变项目计数，只改变分数
            const zeroCounts = Array(keptIds.length).fill(0);
            await updateUserRankings(
              keptIds,
              table,
              'highLevelPapers',
              zeroCounts, // 项目计数不变
              keptScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去保留参与者的原有分数');
        }
        
        // 3. 处理所有新参与者名单（包括保留的和新增的）- 增加分数，对新增的也增加项目数
        if (newParticipants.length > 0) {
          console.log('新参与者完整数据:', JSON.stringify(newParticipants));
          
          const allNewIds = [];
          const allNewRatios = [];
          const allNewScores = [];
          const allCountDeltas = []; // 对新增参与者设为1，对保留的参与者设为0
          
          for (const participant of newParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = newBaseScore * ratio;
            
            allNewIds.push(userId);
            allNewRatios.push(ratio);
            allNewScores.push(participantScore);
            
            // 对新增参与者项目数+1，对保留的参与者项目数不变
            if (addedUserIds.includes(userId)) {
              allCountDeltas.push(1);
            } else {
              allCountDeltas.push(0);
            }
          }
          
          console.log('所有新参与者的加分数据:', {
            userIds: allNewIds,
            countDeltas: allCountDeltas,
            scores: allNewScores
          });
          
          // 为所有参与者添加分数，但只为新增参与者增加项目计数
          for (const table of newRankingTables) {
            console.log(`为所有参与者更新排名表：${table}`);
            await updateUserRankings(
              allNewIds,
              table,
              'highLevelPapers',
              allCountDeltas, // 使用差异化的计数更新：新增的+1，保留的不变
              allNewScores,
              transaction,
              "add" // 加分操作
            );
          }
          console.log('成功更新所有参与者的分数和新增参与者的项目数');
        }
        
        console.log('成功完成高水平论文参与者的排名数据更新');
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 更新论文信息
    await paper.update({
      title: title !== undefined ? title : paper.title,
      journal: journal !== undefined ? journal : paper.journal,
      paperLevelId: paperLevelId !== undefined ? paperLevelId : paper.paperLevelId,
      doi: doi !== undefined ? doi : paper.doi,
      publishDate: publishDate !== undefined ? publishDate : paper.publishDate,
      impactFactor: impactFactor !== undefined ? parseFloat(impactFactor) : paper.impactFactor,
      isFirstAffiliationOurs: isFirstAffiliationOurs !== undefined ? Boolean(isFirstAffiliationOurs) : paper.isFirstAffiliationOurs,
      // 使用论文ID作为文件夹路径
      attachmentUrl: `uploads\\high_level_papers\\${id}\\`,
      submitterId: submitterId !== undefined ? submitterId : paper.submitterId,
      // 添加新的字段处理
      collegeCorrespondentAuthorNumber: collegeCorrespondentAuthorNumber !== undefined ? parseInt(collegeCorrespondentAuthorNumber) : paper.collegeCorrespondentAuthorNumber,
      correspondentAuthorNumber: correspondentAuthorNumber !== undefined ? parseInt(correspondentAuthorNumber) : paper.correspondentAuthorNumber,
      allocationProportionBase: allocationProportionBase !== undefined ? allocationProportionBase : paper.allocationProportionBase,
      totalAllocationProportion: totalAllocationProportion !== undefined ? totalAllocationProportion : paper.totalAllocationProportion,
      remark: remark !== undefined ? remark : paper.remark,
      citations: citations !== undefined ? parseInt(citations) : paper.citations,
      status: status !== undefined ? parseInt(status) : paper.status
    }, { transaction });
    
    // 处理参与者数据，如果有提供
    if (participants && Array.isArray(participants)) {
      // 删除现有参与者记录
      await HighLevelPaperParticipant.destroy({
        where: { paperId: id },
        transaction
      });
      
      // 创建新的参与者记录
      const participantRecords = [];
      for (const participant of participants) {
        try {
          // 确保每个参与者都有必要的信息
          if (!participant.userId) {
            console.error('缺少参与者ID，跳过此参与者');
            continue;
          }
          
          // 创建参与者记录
          const participantRecord = await HighLevelPaperParticipant.create({
            id: uuidv4(),
            paperId: paper.id,
            userId: participant.userId,
            allocationRatio: participant.allocationRatio ? parseFloat(participant.allocationRatio) : 0,
            authorRank: participant.authorRank ? parseInt(participant.authorRank) : null,
            isFirstAuthor: participant.isFirstAuthor === undefined ? false : Boolean(participant.isFirstAuthor),
            isCorrespondingAuthor: participant.isCorrespondingAuthor === undefined ? false : Boolean(participant.isCorrespondingAuthor)
          }, { transaction });
          
          participantRecords.push(participantRecord);
        } catch (error) {
          console.error(`创建参与者记录失败: ${error.message}`);
        }
      }
    }
    
    // 处理文件关联 - 确保考虑fileIds参数
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组，可能以JSON字符串形式传递
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl，可能以JSON字符串形式传递
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl === 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
      
      // 如果有文件ID，更新关联关系
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            paperId: paper.id,
            relatedId: paper.id,
            relatedType: 'high_level_papers'
          };
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath;
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );
          
          processedFileIds.push(fileId);
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，异步移动文件到指定的论文目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'high_level_papers'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${paper.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId, isDeleted: 0 }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                paperId: paper.id,
                relatedId: paper.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新论文的attachmentUrl为论文文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的论文文件夹路径更新论文的attachmentUrl，确保不包含文件名
            const paperFolderPath = `uploads\\high_level_papers\\${paper.id}\\`;
            
            await paper.update({
              attachmentUrl: paperFolderPath
            });
            console.log(`已更新论文 ${paper.id} 的attachmentUrl为: ${paperFolderPath}`);
          } catch (updateError) {
            console.error('更新论文attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响论文更新的返回结果，仅记录错误
        console.error('移动文件到论文目录失败:', moveError);
      }
    }
    
    // 查询更新后的数据
    const updatedPaper = await HighLevelPaper.findByPk(id, {
      include: [
        {
          model: User,
          as: 'submitter',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: HighLevelPapersRules,
          as: 'paperLevel',
          attributes: ['paperLevel', 'score', 'description'],
          required: false
        },
        {
          model: HighLevelPaperParticipant,
          as: 'participants',
          include: [{
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'username', 'studentNumber']
          }]
        }
      ]
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: updatedPaper
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('更新高水平论文失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新高水平论文失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 删除高水平论文
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deletePaper = async (req, res) => {
  console.log("333333333");
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(HighLevelPaper);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少论文ID',
        data: null
      });
    }
    
    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找高水平论文
    const paper = await HighLevelPaper.findByPk(id, { 
      include: [
        {
          model: HighLevelPaperParticipant,
          as: 'participants',
          required: false
        },
        {
          model: highLevelPapersRulesModel,
          as: 'paperLevel',
          required: false
        }
      ],
      transaction 
    });
    
    if (!paper) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '高水平论文不存在',
        data: null
      });
    }
    
    // 检查当前用户是否有权限删除（管理员或者是提交者）
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth) && paper.submitterId !== userInfo.id) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限删除该论文，只能删除自己提交的论文',
        data: null
      });
    }
    
    // 如果论文已审核，需要更新用户排名
    if (paper.ifReviewer == 1) {
      try {
        console.log('开始处理删除论文的排名更新，论文ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("highLevelPapers");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断论文是否在时间区间内（使用publishDate判断）
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(paper.publishDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('论文时间范围状态:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('需要更新的排名表:', rankingTables);
        
        // 获取论文的级别分数
        const baseScore = paper.paperLevel ? paper.paperLevel.score : 0;
        console.log('论文级别分数:', baseScore);
        
        // 获取论文的所有参与者
        const participants = paper.participants || [];
        console.log('论文参与者数量:', participants.length);
        
        // 如果有参与者，批量处理减分操作
        if (participants.length > 0) {
          // 准备批量更新的数据
          const userIds = [];
          const scores = [];
          
          // 收集所有参与者数据
          for (const participant of participants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = baseScore * ratio;
            
            userIds.push(userId);
            scores.push(participantScore);
          }
          
          console.log('删除论文的参与者数据:', {
            userIds: userIds,
            scores: scores
          });
          
          // 创建固定值为1的计数数组，表示每个用户项目数-1
          const countDeltas = Array(userIds.length).fill(1);
          
          // 对每个排名表执行减分操作（一次性批量处理所有参与者）
          for (const table of rankingTables) {
            console.log(`为被删除论文的所有参与者更新排名表 ${table}`);
            await updateUserRankings(
              userIds,
              table,
              'highLevelPapers',
              countDeltas, // 使用固定值1表示项目计数-1
              scores, // 使用参与者分数数组
              transaction,
              "subtract" // 减分操作
            );
          }
          
          console.log('成功从排名表中减去高水平论文参与者的分数和项目数');
        }
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 先删除相关的参与者记录
    await HighLevelPaperParticipant.destroy({
      where: { paperId: id },
      transaction
    });
    
    // 查找论文关联的文件
    const paperFiles = await fileModel.findAll({
      where: { 
        relatedId: id,
        relatedType: 'high_level_papers'
      },
      transaction
    });
    
    // 收集文件夹路径
    let paperFolderPaths = new Set();
    
    if (paperFiles && paperFiles.length > 0) {
      console.log(`找到${paperFiles.length}个与论文关联的文件记录`);
      
      // 从文件路径中提取论文文件夹路径
      for (const file of paperFiles) {
        if (file.filePath) {
          try {
            // 从filePath中提取论文文件夹路径
            const folderPath = file.filePath.substring(0, file.filePath.lastIndexOf('\\'));
            if (folderPath.includes('high_level_papers')) {
              paperFolderPaths.add(folderPath);
            }
          } catch (error) {
            console.error('处理文件路径时出错:', error);
          }
        }
      }
      
      // 硬删除文件记录，而不是标记为已删除
      await fileModel.destroy({
        where: { 
          relatedId: id,
          relatedType: 'high_level_papers'
        },
        transaction
      });
      
      console.log(`已硬删除${paperFiles.length}条文件记录`);
    } else {
      console.log('未找到与论文关联的文件记录');
    }
    
    // 删除高水平论文
    await paper.destroy({ transaction });
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，执行文件系统删除操作（这些操作不能回滚，所以放在事务之外）
    try {
      // 导入文件控制器
      const fileController = require('../common/fileController');
      
      // 直接使用论文ID构造论文文件夹路径
      const paperFolderPath = `uploads/high_level_papers/${id}`;
      console.log(`尝试删除论文文件夹: ${paperFolderPath}`);
      await fileController.deleteDirectoryUtil(paperFolderPath);
      
      // 如果有收集到的文件夹路径，也尝试删除
      if (paperFolderPaths.size > 0) {
        for (const folderPath of paperFolderPaths) {
          console.log(`尝试删除文件夹: ${folderPath}`);
          await fileController.deleteDirectoryUtil(folderPath);
        }
      }
    } catch (fsError) {
      console.warn('删除文件系统中的文件时出错:', fsError);
      // 数据库事务已提交成功，文件系统操作失败不影响API返回结果
    }
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('删除高水平论文失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除高水平论文失败',
      data: null
    });
  }
};

/**
 * 获取论文统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPaperStats = async (req, res) => {
  try {
    // 同时支持GET请求和POST请求参数
    const params = { ...req.query, ...req.body };
    const { userId, year, userOnly } = params;
        
    // 确定是否需要查询单个用户的数据
    const whereCondition = { status: 1 };
    
    // 如果指定了年份，只查询该年份的论文
    if (year) {
      // 根据年份构建日期范围
      const startDate = `${year}-01-01`;
      const endDate = `${year}-12-31`;
      
      whereCondition.publishDate = {
        [Op.gte]: startDate,
        [Op.lte]: endDate
      };
    }
    
    // 获取所有论文
    const papers = await HighLevelPaper.findAll({
      attributes: [
        'id', 'paperLevelId', 'publishDate'
      ],
      where: whereCondition,
      include: [
        {
          model: HighLevelPapersRules,
          as: 'paperLevel',
          attributes: ['paperLevel', 'score'],
          required: false
        },
        {
          model: HighLevelPaperParticipant,
          as: 'participants',
          required: false
        }
      ]
    });
    
    // 如果需要过滤特定用户的数据
    let userPapers = papers;
    if (userOnly === 'true' && userId) {
      userPapers = papers.filter(paper => {
        return paper.participants && paper.participants.some(p => p.userId === userId);
      });
    }
    
    // 计算总论文数和总分
    const paperCount = userPapers.length;
    let totalScore = 0;
    
    // 按类型分组统计
    const typeDistribution = {};
    // 按年份分组统计
    const yearlyDistribution = {};
    
    userPapers.forEach(paper => {
      // 确保分数是数值类型
      const paperScore = paper.paperLevel ? parseFloat(paper.paperLevel.score) || 0 : 0;
      
      // 如果有特定用户ID，计算该用户的得分贡献
      if (userId && paper.participants) {
        const userParticipant = paper.participants.find(p => p.userId === userId);
        if (userParticipant) {
          // 确保分配比例是数值类型
          const allocationRatio = parseFloat(userParticipant.allocationRatio) || 0;
          totalScore += paperScore * allocationRatio;
        }
      } else {
        totalScore += paperScore;
      }
      
      // 类型统计
      const type = paper.paperLevel ? paper.paperLevel.paperLevel : '未分类';
      if (!typeDistribution[type]) {
        typeDistribution[type] = { count: 0, score: 0 };
      }
      typeDistribution[type].count += 1;
      
      if (userId && paper.participants) {
        const userParticipant = paper.participants.find(p => p.userId === userId);
        if (userParticipant) {
          // 确保分配比例是数值类型
          const allocationRatio = parseFloat(userParticipant.allocationRatio) || 0;
          typeDistribution[type].score += paperScore * allocationRatio;
        }
      } else {
        typeDistribution[type].score += paperScore;
      }
      
      // 年份统计
      if (paper.publishDate) {
        const year = new Date(paper.publishDate).getFullYear();
        if (!isNaN(year)) {
          if (!yearlyDistribution[year]) {
            yearlyDistribution[year] = { count: 0, score: 0 };
          }
          yearlyDistribution[year].count += 1;
          
          if (userId && paper.participants) {
            const userParticipant = paper.participants.find(p => p.userId === userId);
            if (userParticipant) {
              // 确保分配比例是数值类型
              const allocationRatio = parseFloat(userParticipant.allocationRatio) || 0;
              yearlyDistribution[year].score += paperScore * allocationRatio;
            }
          } else {
            yearlyDistribution[year].score += paperScore;
          }
        }
      }
    });
    
    // 计算用户排名（如果需要）
    let rankData = { rank: null, totalUsers: null };
    if (userOnly === 'true' && userId) {
      rankData = await calculateUserRanking(userId);
    }
    
    // 确保所有分数都是数值类型，并保留两位小数
    const formatScore = (score) => parseFloat(score.toFixed(2));
    
    // 处理类型分布中的分数
    Object.keys(typeDistribution).forEach(type => {
      typeDistribution[type].score = formatScore(typeDistribution[type].score);
    });
    
    // 处理年度分布中的分数
    Object.keys(yearlyDistribution).forEach(year => {
      yearlyDistribution[year].score = formatScore(yearlyDistribution[year].score);
    });
    
    // 处理分数趋势数据
    const scoreTrend = Object.keys(yearlyDistribution)
      .sort()
      .map(year => ({
        year,
        score: formatScore(yearlyDistribution[year].score),
        count: yearlyDistribution[year].count
      }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        paperCount,
        totalScore: formatScore(totalScore),
        rank: rankData.rank,
        totalUsers: rankData.totalUsers,
        typeDistribution,
        yearlyDistribution,
        scoreTrend
      }
    });
  } catch (error) {
    console.error('获取论文统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取论文统计数据失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 计算用户排名
 * @param {string} userId - 当前用户ID
 * @returns {Object} - 返回包含排名和总用户数的对象
 */
const calculateUserRanking = async (userId) => {
  try {
    // 获取所有用户参与的论文
    const papers = await HighLevelPaper.findAll({
      where: { status: 1 },
      include: [
        {
          model: HighLevelPaperParticipant,
          as: 'participants',
          required: true
        }
      ]
    });

    // 计算每个用户的总分
    const userScores = {};
    
    papers.forEach(paper => {
      const paperScore = paper.score || 0;
      
      paper.participants.forEach(participant => {
        const uid = participant.userId;
        if (!userScores[uid]) {
          userScores[uid] = 0;
        }
        userScores[uid] += paperScore * participant.allocationRatio;
      });
    });
    
    // 转换为数组并排序
    const sortedUsers = Object.entries(userScores)
      .map(([uid, score]) => ({ userId: uid, score }))
      .sort((a, b) => b.score - a.score);
    
    // 查找当前用户的排名
    const currentUserIndex = sortedUsers.findIndex(user => user.userId === userId);
    const rank = currentUserIndex !== -1 ? currentUserIndex + 1 : sortedUsers.length + 1;
    const totalUsers = sortedUsers.length;
        
    return {
      rank, 
      totalUsers
    };
  } catch (error) {
    console.error('计算用户排名失败:', error);
    // 如果计算失败，返回默认值
    return {
      rank: '--',
      totalUsers: '--'
    };
  }
};

/**
 * 审核论文
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewPaper = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(HighLevelPaper);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { reviewStatus = 1, reviewComment, reviewer } = req.body;
    
    console.log(`审核论文，ID: ${id}, 状态: ${reviewStatus}, 意见: ${reviewComment}`);
    
    // 参数验证
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少论文ID参数',
        data: null
      });
    }
    const userInfo = await getUserInfoFromRequest(req);

    // 权限检查：只有管理员才可以审核论文
    if (userInfo.role.roleAuth !== 'ADMIN-LV2' && userInfo.role.roleAuth !== 'SUPER') {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核论文',
        data: null
      });
    }
    
    // 查找论文
    const paper = await HighLevelPaper.findByPk(id, { transaction });
    if (!paper) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '论文不存在',
        data: null
      });
    }
    // 如果已经审核过，返回提示
    if (paper.ifReviewer !== null && paper.ifReviewer != undefined && paper.ifReviewer != 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: `论文已审核`,
        data: null
      });
    }
    
    // 更新论文审核状态
    const updateData = {
      ifReviewer: reviewStatus,
      reviewedAt: new Date(),
      reviewerId: reviewer || userInfo.id
    };
    
    // 添加审核意见（如果有）
    if (reviewComment !== undefined) {
      updateData.reviewComment = reviewComment;
    }
    
    await paper.update(updateData, { transaction });
    
    // 论文审核通过后，更新用户排名数据
    if (reviewStatus === 1) {
      try {
        
        // 获取论文级别对应的分数
        const paperLevel = await highLevelPapersRulesModel.findByPk(paper.paperLevelId, { transaction });
        if (!paperLevel) {
          console.error(`未找到论文级别信息，levelId: ${paper.paperLevelId}`);
          throw new Error('未找到论文级别信息');
        }
        
        const baseScore = paperLevel.score || 0;
        
        // 获取论文所有参与者及其分配比例
        const participants = await highLevelPaperParticipantsModel.findAll({
          where: { paperId: id },
          transaction
        });
        
        if (participants && participants.length > 0) {
          // 准备用户ID数组和得分数组
          const participantUserIds = [];
          const participantScores = [];
          
          // 计算每个参与者的得分
          for (const participant of participants) {
            const userId = participant.userId;
            const allocationRatio = parseFloat(participant.allocationRatio) || 0;
            
            // 计算该参与者应得的分数 = 论文基础分 * 分配比例
            const userScore = baseScore * allocationRatio;
            participantUserIds.push(userId);
            participantScores.push(userScore);
          }
          
          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("highLevelPapers");
          
          // 判断论文是否在时间区间内
          const isInTimeRange = timeInterval && paper.publishDate ? 
            isProjectInTimeRange(paper.publishDate, timeInterval.startTime, timeInterval.endTime) : 
            false;
          
          console.log(`论文ID ${id} 是否在统计时间区间内: ${isInTimeRange}`);
          console.log(`论文参与者数量: ${participantUserIds.length}, 基础分数: ${baseScore}`);
          
          // 根据论文是否在时间区间内，更新不同的排名表
          let rankingTables = [];
          
          if (isInTimeRange) {
            // 在区间内：更新范围内表和全部表
            rankingTables = [
              'user_ranking_reviewed_in', 
              'user_ranking_reviewed_all'
            ];
            console.log(`更新范围内排名表和全部排名表`);
          } else {
            // 不在区间内：更新范围外表和全部表
            rankingTables = [
              'user_ranking_reviewed_out', 
              'user_ranking_reviewed_all'
            ];
            console.log(`更新范围外排名表和全部排名表`);
          }
          
          try {
            for (const table of rankingTables) {
              // 更新所有参与者的排名数据：每人计数+1，分数增加各自的得分
              await updateUserRankings(
                participantUserIds,          // 所有参与者的用户ID数组
                table,                       // 表名
                'highLevelPapers',                     // 类型名
                Array(participantUserIds.length).fill(1), // 每个参与者计数+1
                participantScores,           // 每个参与者的得分数组
                transaction,                 // 传递事务对象
                "add"                        // 操作类型：加分
              );
            }
          } catch (rankingError) {
            console.error('更新排名表失败:', rankingError);
            // 对于数据完整性错误，应当回滚事务
            if (rankingError.name === 'SequelizeDatabaseError') {
              await transaction.rollback();
              throw new Error(`更新排名失败: ${rankingError.message}`);
            } else {
              // 对于其他非致命错误，记录但不中断流程
              console.warn(`排名更新出现非致命错误: ${rankingError.message}`);
            }
          }
        } else {
          console.log(`论文ID ${id} 没有参与者，无需更新排名`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 检查是否已经回滚，如果没有则回滚事务
        if (!error.message || !error.message.includes('更新排名失败')) {
          await transaction.rollback();
          throw error; // 将错误向上传播
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    console.log(`论文 ${id} 审核成功，审核人ID: ${updateData.reviewerId}, 状态: ${reviewStatus}`);
    
    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('审核论文失败:', error);
    return res.status(500).json({
      code: 500,
      message: '审核论文失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 导入高水平论文数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importPapers = async (req, res) => {
  try {
    // 确保上传目录存在
    const uploadDir = path.join(__dirname, '../../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    // 处理上传
    upload.single('file')(req, res, async function (err) {
      if (err) {
        return res.status(400).json({
          code: 400,
          message: '文件上传失败',
          data: null
        });
      }
      
      // 没有文件上传
      if (!req.file) {
        return res.status(400).json({
          code: 400,
          message: '请上传Excel文件',
          data: null
        });
      }
      
      const filePath = req.file.path;
      
      try {
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        
        const worksheet = workbook.getWorksheet(1);
        
        const results = {
          total: 0,
          success: 0,
          failed: 0,
          errors: []
        };
        
        // 跳过标题行，从第二行开始读取
        for (let i = 2; i <= worksheet.rowCount; i++) {
          results.total++;
          
          const row = worksheet.getRow(i);
          
          // 读取单元格值
          const title = row.getCell(1).value;
          const type = row.getCell(2).value;
          const journal = row.getCell(3).value;
          const impactFactor = row.getCell(4).value;
          const publishDate = row.getCell(5).value;
          const authors = row.getCell(6).value;
          const correspondingAuthor = row.getCell(7).value;
          const citations = row.getCell(8).value;
          const score = row.getCell(9).value;
          const status = row.getCell(10).value;
          const remark = row.getCell(11).value;
          
          // 数据校验
          if (!title) {
            results.failed++;
            results.errors.push({
              row: i,
              message: '论文题目不能为空'
            });
            continue;
          }
          
          try {
            // 创建高水平论文
            await HighLevelPaper.create({
              id: uuidv4(),
              title,
              type,
              journal,
              impactFactor: Number(impactFactor) || 0,
              publishDate: publishDate instanceof Date ? publishDate : null,
              authors: authors || '',
              correspondingAuthor: correspondingAuthor || '',
              citations: Number(citations) || 0,
              score: Number(score) || 0,
              status: status === '禁用' ? 0 : 1,
              remark: remark || null
            });
            
            results.success++;
          } catch (error) {
            results.failed++;
            results.errors.push({
              row: i,
              message: error.message
            });
          }
        }
        
        // 删除临时文件
        fs.unlinkSync(filePath);
        
        return res.status(200).json({
          code: 200,
          message: '导入成功',
          data: results
        });
      } catch (error) {
        // 删除临时文件
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        
        console.error('导入高水平论文数据失败:', error);
        return res.status(500).json({
          code: 500,
          message: '导入高水平论文数据失败',
          data: null
        });
      }
    });
  } catch (error) {
    console.error('导入高水平论文数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导入高水平论文数据失败',
      data: null
    });
  }
};

/**
 * 导出高水平论文数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportPapers = async (req, res) => {
  try {
    const { title, type, journal, author, publishStartDate, publishEndDate } = req.query;
    
    // 构建查询条件
    const where = {};
    if (title) where.title = { [Op.like]: `%${title}%` };
    if (type) where.type = type;
    if (journal) where.journal = { [Op.like]: `%${journal}%` };
    if (author) where.authors = { [Op.like]: `%${author}%` };
    if (publishStartDate) where.publishDate = { [Op.gte]: publishStartDate };
    if (publishEndDate) where.publishDate = { [Op.lte]: publishEndDate };
    if (publishStartDate && publishEndDate) {
      where.publishDate = {
        [Op.between]: [publishStartDate, publishEndDate]
      };
    }
    
    // 查询数据
    const papers = await HighLevelPaper.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });
    
    // 创建工作簿和工作表
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('高水平论文');
    
    // 添加标题行
    worksheet.columns = [
      { header: '提交人', key: 'submitter', width: 15 },
      { header: '提交人排位', key: 'submitterRanking', width: 15 },
      { header: '论文题目', key: 'title', width: 30 },
      { header: '期刊名称', key: 'journal', width: 20 },
      { header: '出版时间', key: 'publishDate', width: 15 },
      { header: '论文类型', key: 'type', width: 15 },
      { header: '本学院通讯作者人数', key: 'collegeCorrespondentAuthorNumber', width: 20 },
      { header: '通讯作者总人数', key: 'correspondentAuthorNumber', width: 15 },
      { header: '分配比例基数', key: 'allocationProportionBase', width: 15 },
      { header: '总分配比例', key: 'totalAllocationProportion', width: 15 },
      { header: '作者列表', key: 'authors', width: 30 },
      { header: '作者分配比例列表', key: 'allocationRatio', width: 20 },
      { header: '备注', key: 'remark', width: 30 },
      { header: '是否审核', key: 'ifReviewer', width: 10 },
      { header: '审核人', key: 'reviewer', width: 15 },
      { header: '影响因子', key: 'impactFactor', width: 10 },
      { header: '第一作者类型', key: 'firstAuthorType', width: 15 },
      { header: '是否共同第一作者', key: 'hasCoFirstAuthor', width: 15 },
      { header: '共同第一作者排名', key: 'firstAuthorRank', width: 15 },
      { header: '通讯作者', key: 'correspondingAuthor', width: 15 },
      { header: '引用次数', key: 'citations', width: 10 },
      { header: '计算分数', key: 'calculatedScore', width: 10 },
      { header: '评分', key: 'score', width: 10 },
      { header: '状态', key: 'status', width: 10 }
    ];
    
    // 添加数据行
    papers.forEach(paper => {
      worksheet.addRow({
        submitter: paper.submitter || '',
        submitterRanking: paper.submitterRanking || '',
        title: paper.title,
        journal: paper.journal,
        publishDate: paper.publishDate,
        type: paper.type,
        collegeCorrespondentAuthorNumber: paper.collegeCorrespondentAuthorNumber || 0,
        correspondentAuthorNumber: paper.correspondentAuthorNumber || 0,
        allocationProportionBase: paper.allocationProportionBase || '',
        totalAllocationProportion: paper.totalAllocationProportion || '',
        authors: paper.authors,
        allocationRatio: paper.allocationRatio || '',
        remark: paper.remark || '',
        ifReviewer: paper.ifReviewer ? '是' : '否',
        reviewer: paper.reviewer || '',
        impactFactor: paper.impactFactor,
        firstAuthorType: paper.firstAuthorType === 1 ? '我院研究生' : '非我院研究生',
        hasCoFirstAuthor: paper.hasCoFirstAuthor ? '是' : '否',
        firstAuthorRank: paper.hasCoFirstAuthor ? paper.firstAuthorRank : '-',
        correspondingAuthor: paper.correspondingAuthor || '',
        citations: paper.citations,
        calculatedScore: paper.calculatedScore || 0,
        score: paper.score,
        status: paper.status === 1 ? '启用' : '禁用'
      });
    });
    
    // 设置列的样式
    worksheet.getRow(1).font = { bold: true };
    
    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=high-level-papers.xlsx');
    
    // 写入响应
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error('导出高水平论文数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出高水平论文数据失败',
      data: null
    });
  }
};

/**
 * 获取论文类型分布数据（支持范围过滤）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPaperTypeDistribution = async (req, res) => {
  try {
    // 同时支持GET请求和POST请求参数
    const params = { ...req.query, ...req.body };
    const { range = 'all', reviewStatus = 'all', userId } = params;
    
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("highLevelPapers");
    
    
    // 构建查询条件
    const where = { status: 1 };
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = true;
    } else if (reviewStatus === 'rejected') {
      where.ifReviewer = false;
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null; // 待审核
    }
    
    // 根据range参数确定时间范围筛选条件
    if (range === 'in' && timeInterval) {
      // 设置在指定时间区间内的条件
      where.publishDate = {
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };

    } else if (range === 'out' && timeInterval) {
      // 设置在指定时间区间外的条件
      where.publishDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };

    } else {
      console.log('查询所有论文，不限制时间范围');
    }
    

    
    // 获取所有论文，仅查询必要字段
    const papers = await HighLevelPaper.findAll({
      attributes: ['id', 'paperLevelId', 'publishDate'],
      where,
      include: [
        {
          model: HighLevelPapersRules,
          as: 'paperLevel',
          attributes: ['paperLevel'],
          required: false
        },
        // 如果需要过滤特定用户，需要包含participants
        ...(userId ? [{
          model: HighLevelPaperParticipant,
          as: 'participants',
          attributes: ['userId'],
          required: false
        }] : [])
      ]
    });
    

    
    // 如果需要过滤特定用户，处理结果
    let filteredPapers = papers;
    if (userId) {
      filteredPapers = papers.filter(paper => {
        return paper.participants && paper.participants.some(p => p.userId === userId);
      });

    }
    
    // 初始化类型数据
    const typeData = {};
    
    // 预定义所有论文类型，确保即使没有数据也会显示
    const paperTypes = ['A1-I', 'A1-II', 'A2-III', 'A2-IV', '中国科技期刊卓越行动计划入选期刊', '中文核心期刊'];
    paperTypes.forEach(type => {
      typeData[type] = 0;
    });
    
    // 统计各类型论文数量
    filteredPapers.forEach(paper => {
      const type = paper.paperLevel ? paper.paperLevel.paperLevel : '未分类';
      if (typeData[type] !== undefined) {
        typeData[type]++;
      } else {
        typeData[type] = 1;
      }
    });
    
    // 转换为前端期望的格式：[{name, value}]
    const result = Object.entries(typeData).map(([name, value]) => ({ name, value }));
    
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取论文类型分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取论文类型分布数据失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 获取论文年度分布数据（支持范围过滤）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPaperYearlyDistribution = async (req, res) => {
  try {
    // 同时支持GET请求和POST请求参数
    const params = { ...req.query, ...req.body };
    const { range = 'all', reviewStatus = 'all', userId } = params;
        
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("highLevelPapers");
    
    
    // 构建查询条件
    const where = { status: 1 };
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = true;
    } else if (reviewStatus === 'rejected') {
      where.ifReviewer = false;
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null; // 待审核
    }
    
    // 根据range参数确定时间范围筛选条件
    if (range === 'in' && timeInterval) {
      // 设置在指定时间区间内的条件
      where.publishDate = {
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };

    } else if (range === 'out' && timeInterval) {
      // 设置在指定时间区间外的条件
      where.publishDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };

    } else {
      console.log('查询所有论文，不限制时间范围');
    }
    

    
    // 获取所有论文和参与者信息
    const papers = await HighLevelPaper.findAll({
      attributes: ['id', 'publishDate'],
      where,
      include: userId ? [{
        model: HighLevelPaperParticipant,
        as: 'participants',
        attributes: ['userId'],
        required: false
      }] : []
    });
    

    
    // 如果需要过滤特定用户，处理结果
    let filteredPapers = papers;
    if (userId) {
      filteredPapers = papers.filter(paper => {
        return paper.participants && paper.participants.some(p => p.userId === userId);
      });

    }
    
    // 初始化时间数据
    const timeData = {};
    
    // 统计各年度论文数量
    filteredPapers.forEach(paper => {
      if (paper.publishDate) {
        try {
          // 提取年份
          const publishYear = new Date(paper.publishDate).getFullYear().toString();
          
          if (!timeData[publishYear]) {
            timeData[publishYear] = 0;
          }
          timeData[publishYear]++;
        } catch (e) {
          console.error('处理时间数据错误:', e);
        }
      }
    });
    
    // 按年份排序
    const sortedYears = Object.keys(timeData).sort();
    
    // 构建前端所需的格式
    const result = {
      months: sortedYears,
      data: sortedYears.map(month => timeData[month])
    };
        
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取论文年度分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取论文年度分布数据失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 获取论文影响因子分布数据（支持范围过滤）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPaperImpactFactorDistribution = async (req, res) => {
  try {
    // 同时支持GET请求和POST请求参数
    const params = { ...req.query, ...req.body };
    const { range = 'all', reviewStatus = 'all', userId } = params;
    
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("highLevelPapers");
    
    
    // 构建查询条件
    const where = { status: 1 };
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = true;
    } else if (reviewStatus === 'rejected') {
      where.ifReviewer = false;
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null; // 待审核
    }
    
    // 根据range参数确定时间范围筛选条件
    if (range === 'in' && timeInterval) {
      // 设置在指定时间区间内的条件
      where.publishDate = {
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };

    } else if (range === 'out' && timeInterval) {
      // 设置在指定时间区间外的条件
      where.publishDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };

    } else {
      console.log('查询所有论文，不限制时间范围');
    }
    

    
    // 获取所有论文
    const papers = await HighLevelPaper.findAll({
      attributes: ['id', 'impactFactor', 'publishDate'],
      where,
      include: userId ? [{
        model: HighLevelPaperParticipant,
        as: 'participants',
        attributes: ['userId'],
        required: false
      }] : []
    });
    

    
    // 如果需要过滤特定用户，处理结果
    let filteredPapers = papers;
    if (userId) {
      filteredPapers = papers.filter(paper => {
        return paper.participants && paper.participants.some(p => p.userId === userId);
      });

    }
    
    // 按影响因子区间统计
    const distribution = {};
    
    // 定义区间
    const ranges = [
      { min: 0, max: 1, label: '0-1' },
      { min: 1, max: 2, label: '1-2' },
      { min: 2, max: 3, label: '2-3' },
      { min: 3, max: 4, label: '3-4' },
      { min: 4, max: 5, label: '4-5' },
      { min: 5, max: 6, label: '5-6' },
      { min: 6, max: 7, label: '6-7' },
      { min: 7, max: 8, label: '7-8' },
      { min: 8, max: 9, label: '8-9' },
      { min: 9, max: 10, label: '9-10' },
      { min: 10, max: Infinity, label: '10+' }
    ];
    
    // 初始化区间
    ranges.forEach(range => {
      distribution[range.label] = 0;
    });
    
    // 统计各区间论文数量
    filteredPapers.forEach(paper => {
      const impactFactor = parseFloat(paper.impactFactor) || 0;
      if (isNaN(impactFactor)) {
        return;
      }
      
      const rangeObj = ranges.find(r => impactFactor >= r.min && impactFactor < r.max);
      if (rangeObj) {
        distribution[rangeObj.label]++;
      }
    });
    
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: distribution
    });
  } catch (error) {
    console.error('获取论文影响因子分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取论文影响因子分布数据失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 获取教师排名分布数据（支持范围过滤）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPaperTeacherRanking = async (req, res) => {
  try {
    // 同时支持GET请求和POST请求参数
    const params = { ...req.query, ...req.body };
    const { range = 'all', reviewStatus = 'all', limit = 10, userId } = params;
        
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("highLevelPapers");
    
    
    // 构建查询条件
    const where = { status: 1 };
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = true;
    } else if (reviewStatus === 'rejected') {
      where.ifReviewer = false;
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null; // 待审核
    }
    
    // 根据range参数确定时间范围筛选条件
    if (range === 'in' && timeInterval) {
      // 设置在指定时间区间内的条件
      where.publishDate = {
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };

    } else if (range === 'out' && timeInterval) {
      // 设置在指定时间区间外的条件
      where.publishDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };

    } else {
      console.log('查询所有论文，不限制时间范围');
    }
    

    
    // 获取所有论文，包括关联的实体
    const papers = await HighLevelPaper.findAll({
      attributes: ['id'],
      where,
      include: [
        {
          model: HighLevelPapersRules,
          as: 'paperLevel',
          attributes: ['score'],
          required: false
        },
        {
          model: HighLevelPaperParticipant,
          as: 'participants',
          attributes: ['userId', 'allocationRatio'],
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'nickname', 'username'],
              required: false
            }
          ],
          required: false
        }
      ]
    });
    

    
    // 用户得分统计
    const userScores = {};
    
    // 统计每个用户的得分
    papers.forEach(paper => {
      const paperScore = paper.paperLevel ? parseFloat(paper.paperLevel.score || 0) : 0;
      
      if (paper.participants && paper.participants.length > 0) {
        paper.participants.forEach(participant => {
          if (!participant.userId) return;
          
          // 如果是过滤特定用户，且不是目标用户，则跳过
          if (userId && participant.userId !== userId) return;
          
          const user = participant.user;
          const nickname = user ? (user.nickname || user.username) : participant.userId;
          
          if (!userScores[participant.userId]) {
            userScores[participant.userId] = {
              userId: participant.userId,
              name: nickname,
                  score: 0,
                  paperCount: 0
                };
              }
              
          // 计算分配给此用户的分数
          const allocationRatio = parseFloat(participant.allocationRatio || 0) || (1 / paper.participants.length);
          const score = paperScore * allocationRatio;
          
          userScores[participant.userId].score += score;
          userScores[participant.userId].paperCount += 1;
        });
      }
    });
    
    // 转换为数组并排序
    const sortedTeachers = Object.values(userScores)
      .sort((a, b) => b.score - a.score)
      .slice(0, parseInt(limit));
    
    // 添加排名
    sortedTeachers.forEach((teacher, index) => {
      teacher.rank = index + 1;
      teacher.score = parseFloat(teacher.score.toFixed(2));
    });
        
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        total: Object.keys(userScores).length,
        list: sortedTeachers
      }
    });
  } catch (error) {
    console.error('获取教师排名分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师排名分布数据失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 获取用户论文总分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserTotalScore = async (req, res) => {
  try {
    // 同时支持GET请求和POST请求参数
    const params = { ...req.query, ...req.body };
    console.log('获取用户论文总分，请求参数:', params);
    
    const { userId, range = 'all', page = 1, pageSize = 10 } = params;
    const limit = parseInt(pageSize);
        
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '用户ID不能为空',
        data: null
      });
    }

    // 分页参数
    const pageNum = parseInt(page);
    const offset = (pageNum - 1) * limit;

    console.log(`获取用户论文总分，用户ID: ${userId}, 范围: ${range}`);

    // 获取时间区间
    let startDate, endDate;
    if (params.timeRange && params.timeRange.startDate && params.timeRange.endDate) {
      startDate = params.timeRange.startDate;
      endDate = params.timeRange.endDate;
      console.log('使用自定义时间范围:', startDate, '至', endDate);
    } else {
      // 使用系统默认的时间区间
      const timeInterval = await getTimeIntervalByName("highLevelPapers");
      startDate = timeInterval ? timeInterval.startTime : null;
      endDate = timeInterval ? timeInterval.endTime : null;
      console.log('使用默认时间范围:', startDate, '至', endDate);
    }

    // 构建查询条件
    let timeCondition = {};
    if (startDate && endDate && range !== 'all') {
      if (range === 'in') {
        timeCondition = {
          publishDate: {
            [Op.gte]: startDate,
            [Op.lte]: endDate
          }
        };
      } else if (range === 'out') {
        timeCondition = {
          [Op.or]: [
            { publishDate: { [Op.lt]: startDate } },
            { publishDate: { [Op.gt]: endDate } }
          ]
        };
      }
    }

    // 查询用户相关的所有论文，包括参与的和提交的
    const papers = await HighLevelPaper.findAll({
      where: {
        status: 1,
        ...timeCondition
      },
      include: [
        {
          model: userModel,
          as: 'submitter',
          attributes: ['id', 'nickname', 'username'],
          required: false
        },
        {
          model: highLevelPapersRulesModel,
          as: 'paperLevel',
          attributes: ['id', 'paperLevel', 'score'],
          required: false
        },
        {
          model: HighLevelPaperParticipant,
          as: 'participants',
          required: false,
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'nickname', 'username'],
              required: false
            }
          ]
        }
      ],
      order: [['publishDate', 'DESC']]
    });

    console.log(`查询到 ${papers.length} 篇论文`);

    // 跟踪用户的总分和论文详情
    let totalScore = 0;
    const paperDetails = [];

    // 处理每篇论文，计算用户得分
    papers.forEach(paper => {
      const paperJson = paper.toJSON();
      const paperScore = parseFloat(paperJson.paperLevel?.score || 0);

      // 基本论文信息
      const paperDetail = {
        id: paperJson.id,
        title: paperJson.title,
        journal: paperJson.journal,
        publishDate: paperJson.publishDate,
        type: paperJson.paperLevel?.paperLevel || '',
        impactFactor: paperJson.impactFactor || 0,
        totalScore: paperScore,
        userScore: 0,
        authorCount: paperJson.participants?.length || 0,
        role: ''
      };

      // 检查用户是否参与此论文
      let isUserInvolved = false;
      let userAllocation = 0;
      let userRole = '';

      // 检查用户是否为提交人
      if (paperJson.submitterId === userId) {
        isUserInvolved = true;
        userRole = 'submitter';
      }

      // 检查用户是否为参与者
      if (paperJson.participants && paperJson.participants.length > 0) {
        const userParticipant = paperJson.participants.find(p => p.userId === userId);
        if (userParticipant) {
          isUserInvolved = true;
          userAllocation = userParticipant.allocationRatio || 0;
          userRole = userParticipant.isFirstAuthor ? 'firstAuthor' : 
                    (userParticipant.isCorrespondingAuthor ? 'correspondingAuthor' : 'participant');
        }
      }

      // 如果用户参与了此论文，计算分数并添加到结果中
      if (isUserInvolved) {
        const scoreForUser = userAllocation > 0 ? 
          paperScore * userAllocation : 
          (paperJson.participants && paperJson.participants.length > 0 ? paperScore / paperJson.participants.length : 0);
        
        totalScore += scoreForUser;
        paperDetail.userScore = parseFloat(scoreForUser.toFixed(2));
        paperDetail.role = userRole;
        paperDetail.allocationRatio = userAllocation;
        
        paperDetails.push(paperDetail);
      }
    });

    // 按用户得分降序排序论文
    paperDetails.sort((a, b) => b.userScore - a.userScore);

    // 计算总论文数
    const totalPapers = paperDetails.length;

    // 应用分页
    const pagedPapers = paperDetails.slice(offset, offset + limit);

    // 获取用户信息
    let user = null;
    try {
      user = await userModel.findByPk(userId, {
        attributes: ['id', 'nickname', 'username'],
        raw: true
      });
    } catch (error) {
      console.error('获取用户信息失败:', error);
      user = { id: userId, nickname: userId, username: userId };
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        userId,
        nickname: user ? (user.nickname || user.username || userId) : userId,
        totalScore: parseFloat(totalScore.toFixed(2)),
        paperCount: totalPapers,
        papers: pagedPapers,
        pagination: {
          page: pageNum,
          pageSize: limit,
          total: totalPapers,
          totalPages: Math.ceil(totalPapers / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取用户论文总分失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户论文总分失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 获取所有用户论文总分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllUsersTotalScore = async (req, res) => {
  try {
    const { 
      range = 'all',
      page = 1, 
      pageSize = 10,
      sortField = 'totalScore',
      sortOrder = 'desc',
      nickname = '',
      reviewStatus = 'all'
    } = req.body;
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const isExport = req.body.isExport || false;
    
    // 根据range选择对应的排名表模型
    let RankingModel;
    switch(range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }
    
    // 查询条件
    const queryOptions = {
      order: [['paperScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'paperCount',
        'paperScore'
      ]
    };
    
    // 如果提供了昵称，添加筛选条件
    if (nickname) {
      queryOptions.where = {
        nickName: {
          [Op.like]: `%${nickname}%`
        }
      };
    }
    
    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }
    
    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);
    
    // 格式化返回数据
    const formattedResults = rows.map((item, index) => ({
      rank: index + 1 + offset,
      userId: item.userId,
      nickname: item.nickName,
      studentNumber: item.studentNumber,
      paperCount: parseInt(item.paperCount || 0),
      firstAuthorCount: 0, // 数据库中没有这些字段，设为默认值
      correspondingAuthorCount: 0,
      totalScore: parseFloat(item.paperScore || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: formattedResults,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取所有用户论文总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有用户论文总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取高水平论文总分统计（按级别和总体）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPapersTotalScore = async (req, res) => {
  try {
    const { 
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all' // 审核状态筛选: 'all'(全部), 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
    } = req.body;
    
    // 获取数据库连接实例
    let sequelize;
    if (highLevelPapersModel.sequelize) {
      sequelize = highLevelPapersModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }
    
    let userId = null; 
    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    // 调用存储过程
    const results = await sequelize.query(
      'CALL get_high_level_papers_total_score(?, ?, ?)',
      {
        replacements: [
          range || 'all',
          reviewStatus || 'all',
          userId || null
        ],
        type: sequelize.QueryTypes.RAW
      }
    );
    
    console.log("高水平论文总分统计存储过程返回结果:", JSON.stringify(results));
    
    // 处理查询结果
    let levelStats = [];
    let totalStats = { totalPapers: 0, totalScore: 0 };
    let timeInterval = null;
    
    // 分析和处理返回的结果结构
    if (Array.isArray(results)) {
      // 如果结果是扁平数组（没有嵌套）且包含levelId和levelName字段，则这是级别统计
      if (results.length > 0 && results[0] && typeof results[0].levelId === 'string') {
        levelStats = results.map(item => ({
          ...item,
          count: parseInt(item.count || 0),
          totalScore: parseFloat(item.totalScore || 0)
        }));
      } 
      // 如果结果是嵌套数组，则按照预期的三个结果集处理
      else if (results.length > 0) {
        // 第一个结果集：级别统计
        if (Array.isArray(results[0])) {
          levelStats = results[0].map(item => ({
            ...item,
            count: parseInt(item.count || 0),
            totalScore: parseFloat(item.totalScore || 0)
          }));
        }
        
        // 第二个结果集：总体统计
        if (results.length > 1 && Array.isArray(results[1]) && results[1].length > 0) {
          const totalStatsData = results[1][0];
          if (totalStatsData) {
            totalStats = {
              totalPapers: parseInt(totalStatsData.totalPapers || 0),
              totalScore: parseFloat(totalStatsData.totalScore || 0)
            };
          }
        }
        
        // 第三个结果集：时间区间
        if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
          const timeData = results[2][0];
          if (timeData) {
            timeInterval = {
              startTime: timeData.startTime,
              endTime: timeData.endTime,
              name: timeData.name
            };
          }
        }
      }
    }
    
    // 如果有级别统计但没有总体统计，则计算总体统计
    if (levelStats.length > 0 && (totalStats.totalPapers === 0 || totalStats.totalScore === 0)) {
      totalStats = {
        totalPapers: levelStats.reduce((sum, item) => sum + (item.count || 0), 0),
        totalScore: parseFloat(levelStats.reduce((sum, item) => sum + (item.totalScore || 0), 0).toFixed(2))
      };
    }
    
    // 如果没有从存储过程获得时间区间，则从数据库直接获取
    if (!timeInterval) {
      try {
        const timeIntervalData = await getTimeIntervalByName("highLevelPapers");
        if (timeIntervalData) {
          timeInterval = {
            startTime: timeIntervalData.startTime,
            endTime: timeIntervalData.endTime,
            name: timeIntervalData.name
          };
        }
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        levelStats: levelStats,
        totalStats: totalStats,
        timeInterval: timeInterval
      }
    });
  } catch (error) {
    console.error('获取高水平论文总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取高水平论文总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户高水平论文详情列表及得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserPapersDetail = async (req, res) => {
  try {
    const { 
      userId,                // 用户ID - 必填
      range = 'all',        // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      pageSize = 10,        // 每页记录数
      pageNum = 1           // 当前页码
    } = req.body;

    // 验证必填参数
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：userId',
        data: null
      });
    }
    
    // 获取数据库连接实例
    let sequelize;
    if (highLevelPapersModel.sequelize) {
      sequelize = highLevelPapersModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }
    
    // 调用存储过程
    const results = await sequelize.query(
      'CALL get_user_high_level_papers_detail(?, ?, ?, ?, ?)',
      {
        replacements: [
          userId,
          range || 'all',
          reviewStatus || 'all',
          pageSize,
          pageNum
        ],
        type: sequelize.QueryTypes.RAW
      }
    );
    
    console.log("用户高水平论文详情存储过程返回结果:", JSON.stringify(results));
    
    // 处理查询结果
    let totalCount = 0;
    let papersList = [];
    let statsData = { 
      totalPapers: 0, 
      firstAuthorCount: 0, 
      correspondingAuthorCount: 0, 
      participantPaperCount: 0, 
      totalScore: 0 
    };
    let timeInterval = null;
    
    // 分析和处理返回的结果结构
    if (Array.isArray(results) && results.length > 0) {
      // 第一个结果集: 总记录数
      if (results[0] && Array.isArray(results[0]) && results[0].length > 0) {
        totalCount = parseInt(results[0][0].totalCount || 0);
      }
      
      // 第二个结果集: 论文列表
      if (results.length > 1 && Array.isArray(results[1])) {
        papersList = results[1].map(item => ({
          ...item,
          baseScore: parseFloat(item.baseScore || 0),
          actualScore: parseFloat(item.actualScore || 0),
          allocationRatio: parseFloat(item.allocationRatio || 0),
          impactFactor: parseFloat(item.impactFactor || 0)
        }));
      }
      
      // 第三个结果集: 统计数据
      if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
        const stats = results[2][0];
        statsData = {
          totalPapers: parseInt(stats.totalPapers || 0),
          firstAuthorCount: parseInt(stats.firstAuthorCount || 0),
          correspondingAuthorCount: parseInt(stats.correspondingAuthorCount || 0),
          participantPaperCount: parseInt(stats.participantPaperCount || 0),
          totalScore: parseFloat(stats.totalScore || 0)
        };
      }
      
      // 第四个结果集: 时间区间
      if (results.length > 3 && Array.isArray(results[3]) && results[3].length > 0) {
        const timeData = results[3][0];
        timeInterval = {
          startTime: timeData.startTime,
          endTime: timeData.endTime,
          name: timeData.name
        };
      }
    }
    
    // 如果没有从存储过程获得时间区间，则从数据库直接获取
    if (!timeInterval) {
      try {
        const timeIntervalData = await getTimeIntervalByName("highLevelPapers");
        if (timeIntervalData) {
          timeInterval = {
            startTime: timeIntervalData.startTime,
            endTime: timeIntervalData.endTime,
            name: timeIntervalData.name
          };
        }
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }
    
    // 查询用户信息
    const user = await userModel.findByPk(userId);
    const userData = user ? {
      id: user.id,
      name: user.nickname || user.username,
      employeeNumber: user.studentNumber
    } : { id: userId, name: '未知用户', employeeNumber: '' };
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalCount,
        pageSize,
        pageNum,
        papers: papersList,
        stats: statsData,
        timeInterval,
        user: userData
      }
    });
  } catch (error) {
    console.error('获取用户高水平论文详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户高水平论文详情失败',
      error: error.message
    });
  }
};

/**
 * 重新提交高水平论文审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapply = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(highLevelPapersModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少论文ID',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找论文
    const paper = await highLevelPapersModel.findByPk(id, { transaction });
    
    if (!paper) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '论文不存在',
        data: null
      });
    }
    
    // 检查论文所有权或管理员权限
    const isOwner = paper.userId === userInfo.id;
    const isAdmin = userInfo.role.roleAuth === 'TEACHER-LV1' || userInfo.role.roleAuth === 'SUPER' || userInfo.role.roleAuth === 'ADMIN-LV2';
    
    if (!isOwner && !isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限重新提交该论文',
        data: null
      });
    }
    
    // 检查当前审核状态，只有被拒绝的论文可以重新提交
    if (paper.ifReviewer != 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '只有被拒绝的论文可以重新提交审核',
        data: null
      });
    }
    
    // 更新论文状态为待审核
    await paper.update({
      ifReviewer: null,  // 设置为待审核状态
      reviewComment: null, // 清空之前的审核意见
      reviewerId: null // 清空之前的审核人
    }, { transaction });
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();

    console.error('重新提交审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '重新提交审核失败',
      error: error.message
    });
  }
};

/**
 * 获取审核状态概览
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReviewStatusOverview = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("highLevelPapers");
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const whereCondition = {};

    // 时间范围筛选
    if (range === 'in' && timeInterval) {
      whereCondition.publishDate = {
        [Op.between]: [timeInterval.startTime, timeInterval.endTime]
      };
    } else if (range === 'out' && timeInterval) {
      whereCondition.publishDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }

    // 构建用户筛选条件
    let userCondition = {};
    if (userId) {
      userCondition = {
        include: [{
          model: highLevelPaperParticipantsModel,
          as: 'participants',
          where: { userId: userId },
          required: true
        }]
      };
    } else if (userInfo.role.roleAuth === 'TEACHER-LV1') {
      userCondition = {
        include: [{
          model: highLevelPaperParticipantsModel,
          as: 'participants',
          where: { userId: userInfo.id },
          required: true
        }]
      };
    }

    // 查询各状态数量
    const [reviewed, pending, rejected] = await Promise.all([
      highLevelPapersModel.count({
        where: { ...whereCondition, ifReviewer: 1 },
        ...userCondition
      }),
      highLevelPapersModel.count({
        where: { ...whereCondition, ifReviewer: null },
        ...userCondition
      }),
      highLevelPapersModel.count({
        where: { ...whereCondition, ifReviewer: 0 },
        ...userCondition
      })
    ]);

    const total = reviewed + pending + rejected;
    const reviewedRate = total > 0 ? ((reviewed / total) * 100).toFixed(1) : 0;

    return res.status(200).json({
      code: 200,
      message: '获取审核状态数据成功',
      data: {
        reviewed,
        pending,
        rejected,
        total,
        reviewedRate: parseFloat(reviewedRate)
      }
    });
  } catch (error) {
    console.error('获取审核状态概览失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取审核状态概览失败',
      error: error.message
    });
  }
};