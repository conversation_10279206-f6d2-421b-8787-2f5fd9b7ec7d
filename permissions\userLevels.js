/**
 * 用户级别模块权限配置
 * 简化版 - 角色验证
 */
module.exports = {
  // 查看用户级别列表权限
  list: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以查看用户级别列表
  },
  
  // 创建用户级别权限
  create: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以创建用户级别
  },
  
  // 更新用户级别权限
  update: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以更新用户级别
  },
  
  // 删除用户级别权限
  delete: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以删除用户级别
  },
  
  // 查看用户级别详情权限
  detail: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以查看用户级别详情
  },
  
  // 获取所有用户级别权限（用于下拉选择等）
  all: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 所有用户都可以获取用户级别列表用于选择
  },
  
  // 用户级别统计权限
  statistics: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以查看统计信息
  },
  
  // 导出用户级别权限
  export: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以导出用户级别数据
  },
  
  // 导入用户级别权限
  import: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以导入用户级别数据
  }
};
