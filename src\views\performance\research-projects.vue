<template>
  <div class="performance-container research-projects-container">
    <!-- 添加错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />

    <a-card title="科研项目管理" :bordered="false" class="performance-card">
      <template #extra>
        <a-space>
          <!-- <a-upload
            :customRequest="handleImport"
            :show-upload-list="false"
            :before-upload="beforeUpload"
          >
            <a-button type="primary" v-permission="'score:researchProjects:admin:update'">
              <template #icon><UploadOutlined /></template>
              导入数据
            </a-button>
          </a-upload>
          <a-upload
            :customRequest="handleJsonImport"
            :show-upload-list="false"
            :before-upload="beforeJsonUpload"
          >
            <a-button type="primary" v-permission="'score:researchProjects:admin:update'">
              <template #icon><FileTextOutlined /></template>
              JSON导入
            </a-button>
          </a-upload> -->
          <!-- 添加Excel转JSON导入按钮 -->
          <a-upload
            :customRequest="handleExcelToJsonConvert"
            :show-upload-list="false"
            :before-upload="beforeExcelUpload"
          >
            <a-button type="primary" v-permission="'score:researchProjects:admin:update'">
              <template #icon><FileExcelOutlined /></template>
              Excel数据导入
            </a-button>
          </a-upload>
          <a-button type="primary" @click="showAddModal" v-permission="'score:researchProjects:self:create'">
            <template #icon><PlusOutlined /></template>
            添加项目
          </a-button>
          <a-button :type="showPersonalProjects ? 'default' : 'primary'" @click="togglePersonalProjects" v-permission="'score:researchProjects:admin'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalProjects ? '查看全部项目' : '查看我的项目' }}
          </a-button>
        </a-space>
      </template>
        <!-- 科研项目填写说明区域 -->
        <a-card title="科研项目填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
          <a-alert
            class="mb-16"
            message="科研项目统计时间范围"
            :description="`统计时间：${timeRangeText || '加载中...'}`"
            type="info"
            show-icon
          />
          <div class="rule-content">
            <p><strong>填写说明：</strong></p>
            <ol class="detail-list">
              <li>科研项目获批年在统计时间范围内</li>
              <li>项目以批文为准</li>
              <li>此表由主持人为我院的教职工填写，各承担人比例由主持人分配</li>
              <li>此表中出现的姓名全部为我院教职工</li>
              <li>分配比例请填写小数，不要填写百分数</li>
              <li>所有承担人的总分配比例加总应为1</li>
              <li>本表仅做工作量统计，绩效计算等文件制定后根据统计结果进行计算</li>
            </ol>
          </div>
        </a-card>

        <!-- 图表区域 -->
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="审核状态分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="reviewStatusChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('reviewStatus', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="reviewStatusChartRef" id="reviewStatusChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="项目级别分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="levelChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('level', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="levelChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-bottom: 10px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="项目时间分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="timeChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('time', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="timeChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="项目得分分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="scoreChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('score', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="scoreChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

      <!-- 用户科研项目得分统计表格 -->
      <a-card title="用户科研项目得分统计" :bordered="false" style="margin-top: 24px;" >
        <template #extra>
          <a-space>
            <a-input-search
              v-model:value="userScoreSearchParams.nickname"
              v-permission="'score:researchProjects:admin:list'"
              placeholder="用户昵称"
              style="width: 150px;"
              @search="fetchAllUsersTotalScore"
              @pressEnter="fetchAllUsersTotalScore"
            />
            <a-select
              v-model:value="userScoreChartRange"
              style="width: 150px;"
              @change="handleUserScoreRangeChange"
            >
              <a-select-option value="in">统计范围内</a-select-option>
              <a-select-option value="out">统计范围外</a-select-option>
              <a-select-option value="all">全部项目</a-select-option>
            </a-select>
            <a-button type="primary" @click="exportUserScoreData" :loading="exporting" v-permission="'score:researchProjects:admin:list'">
              <template #icon><DownloadOutlined /></template>
              导出
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="userScoreColumns"
          :data-source="userScoreData"
          :pagination="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' ? userScorePagination : false"
          :loading="userScoreLoading"
          rowKey="userId"
          @change="handleUserScoreTableChange"
          :scroll="{ x: 800 }"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'rank'">
              <a-tag :color="getRankColor(record.rank)">{{ record.rank }}</a-tag>
            </template>
            <template v-else-if="column.key === 'totalScore'">
              <span style="font-weight: bold; color: #1890ff;">{{ record.totalScore ? parseFloat(record.totalScore).toFixed(2) : '0.00' }}分</span>
            </template>
            <template v-else-if="column.key === 'details'">
              <!-- 根据用户角色控制查看详情按钮的显示 -->
              <a-button 
                v-if="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId"
                type="link" 
                @click="showUserScoreDetails(record)" 
              >
                查看详情
              </a-button>
            </template>
          </template>
        </a-table>
      </a-card>

        <!-- 搜索表单 -->
        <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
          <a-form :model="searchParams" @finish="handleSearch" layout="vertical" class="performance-form">
            <a-row :gutter="[12, 8]">
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="项目名称" name="name">
                  <a-input
                    v-model:value="searchParams.name"
                    placeholder="请输入项目名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="项目级别" name="level">
                  <a-select
                    v-model:value="searchParams.level"
                    placeholder="请选择项目级别"
                    style="width: 100%"
                    allow-clear
                    :loading="levelLoading"
                  >
                    <a-select-option v-for="option in levelOptions" :key="option.id" :value="option.id">
                      {{ option.levelName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4" v-permission="'score:researchProjects:admin:list'">
                <a-form-item label="负责人" name="leader">
                  <a-select
                    v-model:value="searchParams.leader"
                    show-search
                    placeholder="请输入负责人姓名或工号"
                    :default-active-first-option="false"
                    :show-arrow="false"
                    :filter-option="false"
                    :not-found-content="leaderSearchLoading ? undefined : null"
                    :options="leaderOptions"
                    @search="handleLeaderSearch"
                    @change="handleLeaderChange"
                    allow-clear
                    style="width: 100%"
                  >
                    <template #notFoundContent>
                      <a-spin v-if="leaderSearchLoading" size="small" />
                      <div v-else>未找到匹配的用户</div>
                    </template>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="时间范围" name="dateRange">
                  <a-range-picker
                    v-model:value="searchParams.dateRange"
                    :format="'YYYY-MM-DD'"
                    style="width: 100%"
                    :placeholder="['开始日期', '结束日期']"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="获批时间" name="approvalDateRange">
                  <a-range-picker
                    v-model:value="searchParams.approvalDateRange"
                    :format="'YYYY-MM-DD'"
                    style="width: 100%"
                    :placeholder="['开始日期', '结束日期']"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="审核状态" name="reviewStatus">
                  <a-select
                    v-model:value="searchParams.reviewStatus"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部项目</a-select-option>
                    <a-select-option value="reviewed">已审核</a-select-option>
                    <a-select-option value="rejected">已拒绝</a-select-option>
                    <a-select-option value="pending">待审核</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="统计范围" name="range">
                  <a-select
                    v-model:value="searchParams.range"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="in">统计范围内</a-select-option>
                    <a-select-option value="out">统计范围外</a-select-option>
                    <a-select-option value="all">全部项目</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
                <a-form-item label=" " style="margin-bottom: 0;">
                  <div class="search-actions-inline">
                    <a-button type="primary" html-type="submit" size="default">
                      <template #icon><search-outlined /></template>
                      搜索
                    </a-button>
                    <a-button @click="resetSearch" size="default">
                      <template #icon><reload-outlined /></template>
                      重置
                    </a-button>
                    <a-button type="default" @click="exportCurrentProjects" :loading="exporting" size="default">
                      <template #icon><DownloadOutlined /></template>
                      导出
                    </a-button>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 数据表格 -->
        <div class="performance-table">
          <a-table
            :columns="columns"
            :data-source="dataSource"
            :loading="isLoading"
            :pagination="pagination"
            @change="handleTableChange"
            rowKey="id"
            :scroll="{ x: 1200 }"
            :bordered="true"
          >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'score'">
            <span v-if="isProjectInScoreRange(record)" style="font-weight: bold; color: #1890ff;">{{ record.score ? parseFloat(record.score).toFixed(2) : '0.00' }}分</span>
            <span v-else style="color: #999999;">不计分</span>
          </template>
          <template v-else-if="column.key === 'members'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ formatMembersWithAllocation(record.participants, record) }}</span>
          </template>
          <template v-else-if="column.key === 'name'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ record.name }}</span>
          </template>
          <template v-else-if="column.key === 'level'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ formatLevel(record.levelId) }}</span>
          </template>
          <template v-else-if="column.key === 'leader'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ formatLeader(record) }}</span>
          </template>
          <template v-else-if="column.key === 'reviewStatus'">
            <a-tag :color="record.ifReviewer === true ? 'success' : (record.ifReviewer === false ? 'error' : 'warning')">
              {{ record.ifReviewer === true ? '已审核' : (record.ifReviewer === false ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'submitter'">
            <span>{{ record.submitter ? (record.submitter.nickname || record.submitter.username || '未知') : '未设置' }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown placement="bottomRight" :trigger="['click']">
              <template #overlay>
                <a-menu class="action-dropdown-menu">
                  <!-- 编辑选项 -->
                  <a-menu-item
                    key="edit"
                    v-if="record.ifReviewer != 1 && hasPerms(showPersonalProjects ? 'score:researchProjects:self:update' : 'score:researchProjects:admin:update')"
                  >
                    <a @click="handleEdit(record)" class="action-menu-item">
                      <EditOutlined />
                      <span>编辑</span>
                    </a>
                  </a-menu-item>

                  <!-- 重新提交审核选项 -->
                  <a-menu-item
                    key="resubmit"
                    v-if="record.ifReviewer == 0 && hasPerms('score:researchProjects:self:reapply')"
                  >
                    <a @click="handleResubmit(record)" class="action-menu-item">
                      <ReloadOutlined />
                      <span>重新提交审核</span>
                    </a>
                  </a-menu-item>

                  <!-- 审核选项 -->
                  <a-menu-item
                    key="review"
                    v-if="record.ifReviewer == null && hasPerms('score:researchProjects:admin:review')"
                  >
                    <a @click="handleReview(record)" class="action-menu-item">
                      <CheckOutlined />
                      <span>审核</span>
                    </a>
                  </a-menu-item>

                  <a-menu-divider v-if="record.ifReviewer != 1 || record.ifReviewer == 0" />

                  <!-- 删除选项 -->
                  <a-menu-item
                    key="delete"
                    v-if="hasPerms(showPersonalProjects ? 'score:researchProjects:self:delete' : 'score:researchProjects:admin:delete')"
                  >
                    <a @click="confirmDelete(record)" class="action-menu-item text-danger">
                      <DeleteOutlined />
                      <span>删除</span>
                    </a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small" class="action-trigger-btn">
                操作
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
        </a-table>
      </div>

      <div class="table-footer">
        <div class="total-score">
          <span>总分：{{ typeof totalScore === 'number' ? totalScore.toFixed(2) : '0.00' }}分</span>
        </div>
      </div>

      <!-- 用户项目详情弹窗 -->
      <a-modal
        v-model:visible="userDetailsVisible"
        :title="`${'教师'}的项目得分详情`"
        width="900px"
        :footer="null"
        :autofocus="false"
        :focusTriggerAfterClose="false"
      >
        <a-table
          :columns="userProjectDetailColumns"
          :data-source="userProjectDetails"
          :pagination="userDetailsPagination"
          :loading="userDetailsLoading"
          rowKey="id"
          :scroll="{ x: 800 }"
          :bordered="true"
          @change="handleUserDetailsPaginationChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'role'">
              <a-tag :color="record.userRole === 'leader' ? 'blue' : 'green'">
                {{ record.userRole === 'leader' ? '负责人' : '成员' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'allocationProportion'">
              {{ (record.allocationProportion * 100).toFixed(2) }}%
            </template>
            <template v-else-if="column.key === 'userScore'">
              <span style="font-weight: bold; color: #1890ff;">{{ record.userScore ? parseFloat(record.userScore).toFixed(2) : '0.00' }}分</span>
            </template>
            <template v-else-if="column.key === 'totalScore'">
              {{ record.totalScore ? parseFloat(record.totalScore).toFixed(2) : '0.00' }}分
            </template>
            <template v-else-if="column.key === 'level'">
              <span>{{ record.levelName || formatLevel(record.levelId) }}</span>
            </template>
            <template v-if="column.key === 'reviewStatus'">
              <a-tag :color="record.reviewStatus === 'reviewed' ? 'success' : (record.reviewStatus === 'rejected' ? 'error' : 'warning')">
                {{ record.reviewStatusText }}
              </a-tag>
            </template>
          </template>
        </a-table>
        <div style="margin-top: 16px; text-align: right; font-weight: bold;">
          总得分: {{ typeof userDetailsTotalScore === 'number' ? userDetailsTotalScore.toFixed(2) : '0.00' }}分
        </div>
      </a-modal>

      <!-- 添加/编辑项目模态框 -->
      <a-modal
        v-model:visible="modalVisible"
        :title="modalTitle"
        width="800px"
        :maskClosable="false"
        @ok="handleSubmit"
        :confirmLoading="loading"
        @cancel="handleModalCancel"
      >
          <a-form
          ref="formRef"
            :model="formState"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
            :rules="rules"
        >
          <!-- 项目基本信息 -->
          <a-form-item
            name="projectId"
            label="项目编号"
          >
              <a-input v-model:value="formState.projectId" placeholder="请输入项目编号" />
            </a-form-item>
            
          <!-- 项目下达部门 -->
          <a-form-item
            name="projectIssuingDepartment"
            label="下达部门"
            :rules="[{ required: true, message: '请输入项目下达部门' }]"
          >
            <a-input v-model:value="formState.projectIssuingDepartment" placeholder="请输入项目下达部门" />
          </a-form-item>

          <!-- 此处保留项目类型字段，因为它虽然从查询中移除，但仍是项目的一个基本属性 -->
          <a-form-item
            name="type"
            label="项目类型"
            :rules="[{ required: true, message: '请输入项目类型' }]"
          >
            <a-input v-model:value="formState.type" placeholder="请输入项目类型" />
          </a-form-item>
          
          <!-- 获批时间 -->
          <a-form-item
            name="approvalDate"
            label="获批时间"
            :rules="[{ required: true, message: '请选择获批时间' }]"
          >
              <a-date-picker
                v-model:value="formState.approvalDate"
                style="width: 100%"
                placeholder="请选择获批时间"
              />
            </a-form-item>

          <!-- 经费金额 -->
          <a-form-item 
            name="fundingAmount" 
            label="经费金额"
            :rules="[{ required: true, message: '请输入项目经费金额' }]"
          >
            <a-input-number 
              v-model:value="formState.fundingAmount" 
              :min="0" 
              :precision="2" 
              :step="0.01" 
              style="width: 100%;" 
              placeholder="请输入项目经费金额（元）"
            />
          </a-form-item>

          <a-form-item
            name="name"
            label="项目名称"
            :rules="[{ required: true, message: '请输入项目名称' }]"
          >
              <a-input v-model:value="formState.name" placeholder="请输入项目名称" />
            </a-form-item>

          <a-form-item
            name="level"
            label="项目级别"
            :rules="[{ required: true, message: '请选择项目级别' }]"
          >
              <a-select
                v-model:value="formState.level"
                placeholder="请选择项目级别"
              :loading="levelLoading"
                @change="handleLevelChange"
              >
              <a-select-option v-for="option in levelOptions" :key="option.id" :value="option.id">
                {{ option.levelName }} ({{ option.score }}分)
              </a-select-option>
              </a-select>
            </a-form-item>
          
          <!-- 大学是否为第一单位 -->
          <a-form-item 
            name="isUniversityFirstUnit" 
            label="大学是否为第一单位"
            :rules="[{ required: true, message: '请选择大学是否为第一单位' }]"
          >
            <a-radio-group v-model:value="formState.isUniversityFirstUnit">
              <a-radio value="是">是</a-radio>
              <a-radio value="否">否</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <!-- 学院是否为第一单位 -->
          <a-form-item 
            name="isCollegeFirstUnit" 
            label="学院是否为第一单位"
            :rules="[{ required: true, message: '请选择学院是否为第一单位' }]"
          >
            <a-radio-group v-model:value="formState.isCollegeFirstUnit">
              <a-radio value="是">是</a-radio>
              <a-radio value="否">否</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <!-- 项目时间 -->
          <a-form-item
            name="startDate"
            label="开始时间"
            :rules="[{ required: true, message: '请选择开始时间' }]"
          >
              <a-date-picker
                v-model:value="formState.startDate"
                style="width: 100%"
                :disabled-date="disabledStartDate"
                @change="handleStartDateChange"
              placeholder="请选择开始时间"
              />
            </a-form-item>

          <a-form-item
            name="endDate"
            label="结束时间"
            :rules="[{ required: true, message: '请选择结束时间' }]"
          >
              <a-date-picker
                v-model:value="formState.endDate"
                style="width: 100%"
                :disabled-date="disabledEndDate"
              placeholder="请选择结束时间"
              />
            </a-form-item>

          <!-- 项目描述 -->
          <a-form-item
            name="description"
            label="项目描述"
          >
            <a-textarea
              v-model:value="formState.description"
              :rows="4"
              placeholder="请输入项目描述"
            />
            </a-form-item>

          <!-- 备注 -->
          <a-form-item
            name="remark"
            label="备注"
          >
            <a-textarea
              v-model:value="formState.remark"
              :rows="2"
              placeholder="请输入备注信息"
            />
          </a-form-item>
          
          <!-- 项目状态 -->
          <a-form-item
            name="status"
            label="项目状态"
          >
            <a-radio-group v-model:value="formState.status">
              <a-radio :value="1">进行中</a-radio>
              <a-radio :value="0">已结项</a-radio>
            </a-radio-group>
          </a-form-item>

          <!-- 项目参与人员 -->
          <a-form-item name="participants" label="项目参与人">
            <!-- 参与者选择和分配比例表单 -->
            <div class="member-selection">
              <a-row :gutter="8">
                <a-col :span="9">
                  <a-form-item-rest>
                    <a-select
                      v-model:value="currentMember.userId"
                      placeholder="请选择参与人员"
                      :filter-option="false"
                      show-search
                      allow-clear
                      :loading="userSearchLoading"
                      @search="handleTeacherSearch"
                      :not-found-content="userSearchLoading ? undefined : '未找到匹配结果'"
                      @change="handleCurrentMemberChange"
                      style="width: 100%;"
                    >
                      <a-select-option v-for="(option, index) in teacherOptions" :key="option.value || index" :value="option.value" :data="option">
                        {{ option.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item-rest>
                  </a-col>
                <a-col :span="9">
                  <a-form-item-rest>
                    <!-- 分配比例输入框 -->
                    <a-input-number 
                      v-model:value="currentMember.allocationRatio"
                      :min="0.1"
                      :max="100" 
                      :step="0.1"
                      :precision="1"
                      placeholder="分配比例"
                      style="width: 100%;"
                      addon-after="%"
                    />
                  </a-form-item-rest>
                  </a-col>
                <a-col :span="6">
                  <a-form-item-rest>
                    <a-button type="primary" @click="handleAddMember">添加参与人</a-button>
                  </a-form-item-rest>
                  </a-col>
                </a-row>
                
              <!-- 参与者列表 -->
              <div class="member-list" style="margin-top: 16px;">
                <a-alert v-if="formState.participants.length > 0 && !validateTotalAllocation()" message="警告：所有参与者的分配比例总和应为1（100%）" type="warning" show-icon style="margin-bottom: 8px;" />
                
                <div v-if="formState.participants.length > 0" class="members-container">
                  <a-divider style="margin: 8px 0">已添加参与人员</a-divider>
                  <p style="color: #666; font-size: 12px; margin-bottom: 8px;">已添加 {{ formState.participants.length }} 位参与人员</p>
                  <a-list 
                    :data-source="formState.participants" 
                    size="small"
                    bordered
                  >
                    <template #renderItem="{ item, index }">
                      <a-list-item>
                        <a-row style="width: 100%">
                          <a-col :span="5">
                            {{ item.displayName }}
                          </a-col>
                          <a-col :span="4">
                            <a-form-item-rest>
                              <a-switch 
                                :checked="item.isLeader" 
                                @change="(checked) => toggleLeader(index, checked)"
                                checkedChildren="负责人" 
                                unCheckedChildren="参与者"
                              />
                            </a-form-item-rest>
                          </a-col>
                          <a-col :span="5">
                            <a-form-item-rest>
                              <a-input-number 
                                v-model:value="item.allocationRatio" 
                                :min="0.1" 
                                :max="100" 
                                :step="0.1"
                                :precision="1"
                                style="width: 90%"
                                addon-after="%"
                                @change="validateTotalAllocation"
                              />
                            </a-form-item-rest>
                          </a-col>
                          <a-col :span="4">
                            <a-form-item-rest>
                              <a-input-number 
                                v-model:value="item.participantRank" 
                                :min="1" 
                                :max="99"
                                :precision="0"
                                style="width: 90%"
                                placeholder="排名"
                              />
                            </a-form-item-rest>
                          </a-col>
                          <a-col :span="5" style="text-align: right">
                            <a-button type="link" danger @click="() => handleRemoveMember(index)">删除</a-button>
                          </a-col>
                        </a-row>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>
                <div v-else style="color: #999; text-align: center; padding: 10px; border: 1px dashed #ddd; border-radius: 4px;">
                  还没有添加参与人员，请先选择参与人员并点击"添加"按钮
                </div>
              </div>
            </div>
          </a-form-item>

          <!-- 项目附件 -->
          <a-form-item
            name="attachments"
            label="项目附件"
          >
              <a-upload
                v-model:file-list="fileList"
                :customRequest="handleFileUpload"
                :before-upload="beforeUpload"
                multiple
                :show-upload-list="false"
              >
                <a-button>
                  <template #icon><UploadOutlined /></template>
                  选择文件
                </a-button>
              </a-upload>
              
              <!-- 使用表格显示已上传文件 -->
              <a-table
                :columns="fileColumns"
                :data-source="fileList"
                :pagination="false"
                size="small"
                style="margin-top: 16px;"
                rowKey="uid"
                v-if="fileList.length > 0"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'fileName'">
                    <span :title="record.name">{{ record.originalFileName || record.name }}</span>
                  </template>
                  <template v-if="column.key === 'fileSize'">
                    {{ formatFileSize(record.size || (record.data && record.data.size)) }}
                  </template>
                  <template v-if="column.key === 'status'">
                    <a-tag :color="record.status === 'done' ? 'success' : (record.status === 'error' ? 'error' : 'processing')">
                      {{ record.status === 'done' ? '已上传' : (record.status === 'error' ? '上传失败' : '上传中') }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === 'action'">
                    <a-space>
                      <a-button type="link" size="small" @click="previewFile(record)" v-if="record.status === 'done'">
                        <template #icon><EyeOutlined /></template>
                        预览
                      </a-button>
                      <a-button type="link" size="small" @click="downloadFile(record)" v-if="record.status === 'done'">
                        <template #icon><DownloadOutlined /></template>
                        下载
                      </a-button>
                      <a-popconfirm
                        title="确定要删除该文件吗？此操作将同时删除服务器上的文件"
                        @confirm="confirmDeleteFile(record)"
                        okText="确认"
                        cancelText="取消"
                      >
                        <a-button type="link" danger size="small">
                          <template #icon><DeleteOutlined /></template>
                          删除
                        </a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
              
              <div style="margin-top: 8px; color: #666; font-size: 12px;">
                支持上传文档、图片或压缩文件，单个文件不超过10MB，总大小不超过50MB
              </div>
          </a-form-item>

          <!-- 项目得分 -->
          <a-form-item
            name="score"
            label="项目得分"
          >
              <a-input-number
                v-model:value="formState.score"
                :min="0"
                :max="250"
                style="width: 100%"
                disabled
              addon-after="分"
              />
            </a-form-item>

          <!-- 提交人 -->
          <a-form-item
            name="submitterId"
            label="提交人"
            :rules="[{ required: true, message: '请选择提交人' }]"
          >
            <a-select
              v-model:value="formState.submitterId"
              placeholder="请选择提交人"
              :filter-option="false"
              show-search
              allow-clear
              :loading="submitterSearchLoading"
              @search="handleSubmitterSearch"
              :not-found-content="submitterSearchLoading ? undefined : '未找到匹配结果'"
              style="width: 100%;"
            >
              <a-select-option v-for="(option, index) in submitterOptions" :key="option.value || index" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>

    <!-- 添加导入结果显示模态框 -->
    <a-modal
      v-model:visible="importResultVisible"
      title="导入结果"
      width="800"
      :footer="null"
      :maskClosable="false"
      :closable="!importInProgress"
    >
      <div style="margin-bottom: 16px;">
        <a-progress 
          :percent="importResults.total > 0 ? Math.floor((importResults.current / importResults.total) * 100) : 0" 
          :status="importInProgress ? 'active' : (importResults.failed > 0 ? 'exception' : 'success')" 
        />
        <div style="margin-top: 16px; display: flex; justify-content: space-between;">
          <span>总记录数: <b>{{ importResults.total }}</b></span>
          <span>已处理: <b>{{ importResults.current }}</b></span>
          <span>成功: <b style="color: #52c41a">{{ importResults.success }}</b></span>
          <span>失败: <b style="color: #ff4d4f">{{ importResults.failed }}</b></span>
        </div>
      </div>
      
      <a-table
        :dataSource="importResults.details"
        :columns="resultColumns"
        rowKey="index"
        :pagination="{ pageSize: 10 }"
        :rowClassName="(record) => record.status === 'error' ? 'import-row-error' : ''"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag v-if="text === 'success'" color="success">成功</a-tag>
            <a-tag v-else color="error">失败</a-tag>
          </template>
        </template>
      </a-table>
      
      <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
        <a-space>
          <a-button 
            @click="exportFailedRecords" 
            :disabled="importInProgress || importResults.failed === 0"
            type="danger"
          >
            <template #icon><DownloadOutlined /></template>
            导出失败记录
          </a-button>
          <a-button 
            type="primary" 
            @click="importResultVisible = false"
            :disabled="importInProgress"
          >
            完成
          </a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 添加导入预览模态框 -->
    <a-modal
      v-model:visible="importPreviewVisible"
      title="导入预览"
      width="90%"
      :maskClosable="false"
      :footer="null"
      @cancel="handleCancelImportPreview"
    >
      <template v-if="importPreviewLoading">
        <div style="text-align: center; padding: 40px;">
          <a-spin size="large" />
          <p style="margin-top: 20px;">正在解析数据，请稍候...</p>
        </div>
      </template>
      <template v-else>
        <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;">
          <a-alert
            :type="userIdCheckResults.notFound > 0 ? 'warning' : 'success'"
            :message="userIdCheckResults.notFound > 0 ? 
              `存在${userIdCheckResults.notFound}个用户ID未找到，这些记录可能导入失败` : 
              '所有用户ID均已找到'"
            show-icon
            style="flex: 1;"
          />
          <div style="margin-left: 16px;">
            <a-space>
              <a-button @click="handleCancelImportPreview">
                取消
              </a-button>
              <a-button 
                type="primary" 
                @click="handleStartImport" 
                :disabled="importPreviewData.length === 0"
                :loading="importInProgress"
              >
                开始导入
              </a-button>
              <a-button 
                type="primary" 
                @click="handleDownloadJson"
                :disabled="importPreviewData.length === 0"
              >
                <template #icon><DownloadOutlined /></template>
                下载JSON
              </a-button>
            </a-space>
          </div>
        </div>

        <div style="margin-bottom: 16px; font-size: 14px;">
          <a-badge status="processing" :text="`共找到 ${importPreviewData.length} 条记录`" />
        </div>

        <a-table
          :columns="importPreviewColumns"
          :dataSource="importPreviewData.map((item, index) => ({ ...item, key: item.index || index }))"
          :rowKey="record => record.key"
          size="small"
          bordered
          :scroll="{ x: 1800, y: 600 }"
          :pagination="{ pageSize: 10 }"
        >
          <template #bodyCell="{ column, record }">
          </template>
        </a-table>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, createVNode, watch, h } from 'vue'
import { message, Modal, Radio, Input, Upload } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import debounce from 'lodash/debounce'
import axios from 'axios'
import useUserId from '@/composables/useUserId'
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()
import {
  getProjects,
  addResearchProject,
  updateResearchProject,
  deleteResearchProject,
  reviewProject,
  importResearchProjects,
  getLevelDistribution,
  getTypeDistribution,
  getTimeDistribution,
  getScoreDistribution,
  getAllUsersTotalScore,
  getResearchProjectDetail,
  getUserProjectDetails,
  reapplyReview,
  getReviewStatusOverview
} from '@/api/modules/api.researchProjects'
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  DownOutlined,
  CheckOutlined,
  ReloadOutlined,
  FileExcelOutlined,
  UserOutlined,
  EyeOutlined,
  DownloadOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'
import { getAllResearchProjectsLevels } from '@/api/rules/researchProjectsLevels'
import { usersSearch, usersFindOne } from '@/api/modules/api.users'
import { uploadFiles, deleteFile } from '@/api/modules/api.file'
import * as XLSX from 'xlsx';
import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole, getRoleFromStorage } = useUserRole()
import { hasPerms } from '@/libs/util.common';
import { getScoreTimeRange } from '@/api/modules/api.home';

// 导入相关状态变量
const importResultVisible = ref(false);
const importPreviewVisible = ref(false);
const importInProgress = ref(false);
const importResults = reactive({
  total: 0,
  success: 0,
  failed: 0,
  details: [],
  current: 0 // 添加当前进度
});
const importPreviewData = ref([]);
const importPreviewLoading = ref(false);
const userIdCheckResults = reactive({
  total: 0,
  found: 0,
  notFound: 0
});


// 图表状态和缓存相关
const chartsInitialized = ref(false);
const chartDataCache = reactive({
  level: {},
  reviewStatus: {},
  time: {},
  score: {}
});
const levelChartLoading = ref(false);
const reviewStatusChartLoading = ref(false);
const timeChartLoading = ref(false);
const scoreChartLoading = ref(false);

// 表单项相关变量
const levelOptions = ref([]); // 项目级别选项
const levelLoading = ref(false); // 加载项目级别选项状态


// 图表引用
const levelChartRef = ref(null)
const reviewStatusChartRef = ref(null)
const timeChartRef = ref(null)
const scoreChartRef = ref(null)

// 图表实例
let levelChart = null
let reviewStatusChart = null
let timeChart = null
let scoreChart = null

// 图表数据范围状态
const levelChartRange = ref('in')
const reviewStatusChartRange = ref('in')
const timeChartRange = ref('in')
const scoreChartRange = ref('in')

// 图表审核状态过滤
const levelChartReviewStatus = ref('reviewed')
const reviewStatusChartReviewStatus = ref('reviewed')
const timeChartReviewStatus = ref('reviewed')
const scoreChartReviewStatus = ref('reviewed')

// 添加错误状态
const errorMessage = ref('')

// 搜索参数
const searchParams = reactive({
  name: '',
  level: '',
  leader: '',
  dateRange: null,
  approvalDateRange: null,
  range: 'in',
  reviewStatus: 'reviewed'
});

// 查询参数对象
const queryParams = reactive({
  name: '',
  level: '',
  leader: '',
  startDate: undefined,
  endDate: undefined,
  approvalStartDate: undefined,
  approvalEndDate: undefined,
  range: 'all',
  reviewStatus: 'reviewed'
});

// 添加loading状态
const isLoading = ref(false);
const loading = ref(false);

// 添加数据源状态
const dataSource = ref([]);

// 添加分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
});

// 添加显示个人项目的状态
const showPersonalProjects = ref(false);

// 用户得分相关状态
const userScoreData = ref([]);
const userScoreLoading = ref(false);
const userScorePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
});
const userScoreSearchParams = reactive({
  projectId: '',
  nickname: '',
  reviewStatus: 'reviewed'
});
const userScoreChartRange = ref('in');

// 用户项目详情状态
const userDetailsVisible = ref(false);
const userDetailsLoading = ref(false);
const userProjectDetails = ref([]);
const userDetailsTotalScore = ref(0);
const selectedUserDetailName = ref('');
const selectedUserId = ref(null);
const userDetailsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
});

// 项目表单状态
const modalVisible = ref(false);
const isEdit = ref(false);
const modalTitle = ref('添加科研项目'); // 添加模态框标题
const editMode = ref(false); // 编辑模式标志
const confirmLoading = ref(false);
const formRef = ref(null);
const formState = reactive({
  id: '',
  projectId: '',
  name: '',
  level: '',
  type: '',
  projectIssuingDepartment: '',
  fundingAmount: null,
  isUniversityFirstUnit: '是',
  isCollegeFirstUnit: '是',
  startDate: null,
  approvalDate: null,
  endDate: null,
  participants: [], // 参与者列表
  description: '',
  remark: '',
  status: 1,
  score: 0,
  submitterId: null,
  
  // 重置文件相关字段
  attachmentUrl: [], // 文件路径数组
  fileIds: [], // 文件ID数组
  deletedFileIds: [] // 要删除的文件ID数组
});
const currentMember = reactive({
  userId: null,
  displayName: '',
  username: '',
  studentNumber: '',
  nickname: '',
  allocationRatio: 10, // 默认分配比例10%
  isLeader: false,
  participantRank: 1 // 默认排名为1
});
const fileList = ref([]);


// 用户筛选相关
const membersOptions = ref([]);
const membersSearchLoading = ref(false);
const userSearchLoading = ref(false);
const userSearchTimeout = ref(null);
const teacherOptions = ref([]); // 添加教师选项列表

// 添加提交人相关状态
const submitterSearchLoading = ref(false);
const submitterSearchTimeout = ref(null);
const submitterOptions = ref([]);

  // Excel转JSON相关逻辑
  const convertingExcel = ref(false);
  // 添加一个全局变量存储最近转换的Excel数据
  const lastConvertedExcelData = ref(null);
  
// 用户详情表格列
const userScoreColumns = [
  {
    title: '排名',
    key: 'rank',
    align: 'center',
  },
  {
    title: '用户名',
    dataIndex: 'nickname',
    key: 'nickname',
  },
  {
    title: '工号',
    dataIndex: 'studentNumber',
    key: 'studentNumber',
  },
  {
    title: '项目数量',
    dataIndex: 'projectCount',
    key: 'projectCount',
    align: 'center',
  },
  {
    title: '总得分',
    key: 'totalScore',
    align: 'center',
  },
  {
    title: '操作',
    key: 'details',
    align: 'center',
  },
];

// 用户项目详情列
const userProjectDetailColumns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: false,
    width: 180,
  },
  {
    title: '项目级别',
    key: 'level',
    width: 180,
  },
  {
    title: '角色',
    key: 'role',
    width: 100,
  },
  {
    title: '分配比例',
    key: 'allocationProportion',
    width: 100,
  },
  {
    title: '个人得分',
    key: 'userScore',
    width: 80,
  }
];

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
  ],
  level: [
    { required: true, message: '请选择项目级别', trigger: 'change' },
  ],
  type: [
    { required: true, message: '请输入项目类型', trigger: 'change' },
  ],
  projectIssuingDepartment: [
    { required: true, message: '请输入项目下达部门', trigger: 'blur' },
  ],
  startDate: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
  ],
  'user.nickname': [
    { required: true, message: '请选择项目负责人', trigger: 'change' },
  ],
};

// 计算总分
const totalScore = computed(() => {
  if (!dataSource.value || dataSource.value.length === 0) return 0;
  return dataSource.value.reduce((sum, item) => {
    if (isProjectInScoreRange(item)) {
      return sum + (parseFloat(item.score) || 0);
    }
    return sum;
  }, 0);
});

// 初始化搜索参数
const initSearchParams = () => {
  searchParams.name = '';
  searchParams.level = '';
  searchParams.leader = '';
  searchParams.dateRange = null;
  searchParams.approvalDateRange = null;
  searchParams.range = 'in';
  searchParams.reviewStatus = 'reviewed';
};

// 处理搜索
const handleSearch = () => {
  // 将搜索参数转换为查询参数
  queryParams.name = searchParams.name || '';
  queryParams.level = searchParams.level || '';
  // leader参数现在传递用户ID
  queryParams.leader = searchParams.leader || '';
  
  // 处理日期范围
  if (searchParams.dateRange && searchParams.dateRange.length === 2) {
    queryParams.startDate = searchParams.dateRange[0] ? dayjs(searchParams.dateRange[0]).format('YYYY-MM-DD') : undefined;
    queryParams.endDate = searchParams.dateRange[1] ? dayjs(searchParams.dateRange[1]).format('YYYY-MM-DD') : undefined;
    } else {
    queryParams.startDate = undefined;
    queryParams.endDate = undefined;
  }
  
  // 处理获批日期范围
  if (searchParams.approvalDateRange && searchParams.approvalDateRange.length === 2) {
    queryParams.approvalStartDate = searchParams.approvalDateRange[0] ? dayjs(searchParams.approvalDateRange[0]).format('YYYY-MM-DD') : undefined;
    queryParams.approvalEndDate = searchParams.approvalDateRange[1] ? dayjs(searchParams.approvalDateRange[1]).format('YYYY-MM-DD') : undefined;
  } else {
    queryParams.approvalStartDate = undefined;
    queryParams.approvalEndDate = undefined;
  }
  
  queryParams.range = searchParams.range;
  queryParams.reviewStatus = searchParams.reviewStatus;
  
  // 重置分页到第一页
  pagination.current = 1;
  
  // 获取数据
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  // 重置所有搜索参数
  searchParams.name = '';
  searchParams.level = '';
  searchParams.leader = '';
  searchParams.dateRange = null;
  searchParams.approvalDateRange = null;
  searchParams.range = 'all';
  searchParams.reviewStatus = 'reviewed';
  
  // 清空负责人选项
  leaderOptions.value = [];
  
  // 重置后立即搜索
  handleSearch();
};

// 格式化项目级别
const formatLevel = (levelId) => {
  // 如果传入的是一个对象且包含levelName属性，直接返回levelName
  if (levelId && typeof levelId === 'object' && levelId.levelName) {
    return levelId.levelName;
  }
  
  // 如果levelOptions已加载，尝试查找对应的级别名称
  if (levelOptions.value && levelOptions.value.length > 0) {
    const foundLevel = levelOptions.value.find(item => item.id === levelId);
    if (foundLevel) {
      return foundLevel.levelName;
    }
  }
  
  // 如果找不到匹配的级别，返回一个合理的默认值
  return levelId || '未知级别';
};

// 格式化负责人信息
const formatLeader = (record) => {
  if (!record.leader) return '未指定';
  return `${record.leader.nickname || record.leader.username || ''}${record.leader.studentNumber ? ` (${record.leader.studentNumber})` : ''}`;
};

// 格式化项目成员列表，显示分配比例
const formatMembersWithAllocation = (participants, project, isExport = false) => {
  if (!participants || !Array.isArray(participants) || participants.length === 0) {
    return '无';
  }

  // 如果是导出，提供更详细的信息
  if (isExport) {
    return participants.map(p => {
      const user = p.user || {};
      const name = user.nickname || user.username || '未知用户';
      const studentNumber = user.studentNumber ? `(${user.studentNumber})` : '';
      const ratio = p.allocationRatio ? `: ${(p.allocationRatio * 100).toFixed(2)}%` : '';
      const isLeader = p.isLeader ? '[负责人]' : '';
      return `${name}${studentNumber}${ratio}${isLeader}`;
    }).join('; ');
  }

  // 常规显示，简洁版
  return participants.map(p => {
    const user = p.user || {};
    const name = user.nickname || user.username || '未知用户';
    const ratio = p.allocationRatio ? `(${(p.allocationRatio * 100).toFixed(0)}%)` : '';
    return `${name}${ratio}`;
  }).join(', ');
};

// 判断项目是否在计分范围内
const isProjectInScoreRange = (record) => {
  // 这里可以根据实际业务逻辑判断
  return true; 
};

// 获取排名颜色
const getRankColor = (rank) => {
  const colors = ['#f50', '#fa8c16', '#faad14'];
  return rank <= 3 ? colors[rank - 1] : '';
};

// 计算总分
const calculateTotalScore = () => {
  // 总分通过computed属性计算
};

// 处理教师搜索
const handleTeacherSearch = (value) => {
  // 清除之前的延时搜索
  if (userSearchTimeout.value) {
    clearTimeout(userSearchTimeout.value);
  }
  
  // 如果搜索词为空，则清空选项列表并返回
  if (!value || value.trim() === '') {
    teacherOptions.value = [];
    return;
  }
  
  // 设置搜索中状态
  userSearchLoading.value = true;
  
  // 延时搜索，避免频繁请求
  userSearchTimeout.value = setTimeout(() => {
    // 调用用户搜索API
    usersSearch({ keyword: value.trim() })
      .then((res) => {
        // 处理API返回结果
        if (res && res.code === 200) {
          // 将返回的用户数据转换为下拉选项格式
          if (Array.isArray(res.data)) {
            teacherOptions.value = res.data.map(item => ({
              value: item.id, // 用户ID作为选项值
              label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`, // 显示名称和用户名
              ...item // 保留原始用户数据，方便后续使用
            }));
          } else if (res.data && Array.isArray(res.data.list)) {
            // 如果返回的是包含list属性的对象
            teacherOptions.value = res.data.list.map(item => ({
              value: item.id,
              label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`,
              ...item
            }));
          } else {
            // 如果数据结构不是预期的格式
            console.error('搜索成员返回的数据结构异常:', res.data);
            
            // 尝试转换数据结构
            const data = res.data;
            if (data && typeof data === 'object') {
              try {
                // 尝试将对象转换为数组
                const tempArray = Object.values(data);
                if (tempArray.length > 0 && typeof tempArray[0] === 'object') {
                  teacherOptions.value = tempArray.map(item => ({
                    value: item.id,
                    label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`,
                    ...item
                  }));
                } else {
                  teacherOptions.value = [{
                    value: data.id,
                    label: `${data.nickname || data.username || '未知'} (${data.studentNumber || '无工号'})`,
                    ...data
                  }];
                }
              } catch (e) {
                console.error('转换成员数据结构失败:', e);
                teacherOptions.value = [];
              }
            } else {
              teacherOptions.value = [];
            }
          }
        } else {
          teacherOptions.value = [];
          console.warn('用户搜索返回数据格式不正确:', res);
        }
      })
      .catch((error) => {
        console.error('搜索用户失败:', error);
        message.error('搜索用户失败');
        teacherOptions.value = [];
      })
      .finally(() => {
        userSearchLoading.value = false;
      });
  }, 500); // 500ms的防抖延迟
};

// 处理当前选中成员变更
const handleCurrentMemberChange = (value) => {
  // 查找选中的用户信息
  const selectedOption = teacherOptions.value.find(item => item.value === value);
  
  if (selectedOption) {
    // 更新当前选中成员信息
    currentMember.userId = selectedOption.value || selectedOption.id;
    currentMember.displayName = selectedOption.nickname || selectedOption.username || selectedOption.label || '未知';
    currentMember.username = selectedOption.username || '';
    currentMember.studentNumber = selectedOption.studentNumber || '';
    currentMember.nickname = selectedOption.nickname || '';
    
    // 默认分配比例设为10%
    if (!currentMember.allocationRatio) {
      currentMember.allocationRatio = 10;
    }
    
    console.log('已选择教师:', currentMember);
  } else if (value) {
    // 尝试从选项列表中找到匹配的选项
    const found = teacherOptions.value.find(opt => 
      (opt.nickname || opt.username) === value || opt.label === value
    );
    
    if (found) {
      currentMember.userId = found.value || found.id;
      currentMember.displayName = found.nickname || found.username || found.label || value;
      currentMember.username = found.username || '';
      currentMember.studentNumber = found.studentNumber || '';
      currentMember.nickname = found.nickname || '';
      
      // 默认分配比例设为10%
      if (!currentMember.allocationRatio) {
        currentMember.allocationRatio = 10;
      }
    } else {
      // 如果没有找到，直接使用输入的值
      currentMember.userId = '';
      currentMember.displayName = value;
      currentMember.username = '';
      currentMember.studentNumber = '';
      currentMember.nickname = value;
      
      // 默认分配比例设为10%
      if (!currentMember.allocationRatio) {
        currentMember.allocationRatio = 10;
      }
    }
  } else {
    // 重置当前选中成员
    currentMember.userId = null;
    currentMember.displayName = '';
    currentMember.username = '';
    currentMember.studentNumber = '';
    currentMember.nickname = '';
    currentMember.allocationRatio = 10;
  }
};

// 校验分配比例总和
const validateTotalAllocation = () => {
  if (!formState.participants || formState.participants.length === 0) {
    return true; // 如果没有参与者，则视为有效
  }
  
  const total = formState.participants.reduce(
    (sum, item) => sum + Number(item.allocationRatio || 0), 
    0
  );
  
  // 允许0.1%的误差
  return Math.abs(total - 100) < 0.1;
};

// 处理级别变化
const handleLevelChange = (value) => {
  if (!value) return;
  
  // 查找选择的级别对象
  const selectedLevel = levelOptions.value.find(level => level.id === value);
  
  if (selectedLevel && selectedLevel.score) {
    // 更新分数
    formState.score = selectedLevel.score;
    console.log('已更新项目得分为:', formState.score);
  }
};

// 获取用户得分
const fetchUserScore = async () => {
  // 这个函数应该实现从后端获取用户得分的逻辑
  try {
  } catch (error) {
    console.error('获取用户得分失败:', error);
  }
};

// 获取所有用户总分
const fetchAllUsersTotalScore = async () => {
  try {
    userScoreLoading.value = true;
    
    const params = {
      page: userScorePagination.current,
      pageSize: userScorePagination.pageSize,
      range: userScoreChartRange.value,
      projectId: userScoreSearchParams.projectId,
      nickname: userScoreSearchParams.nickname,
      reviewStatus: userScoreSearchParams.reviewStatus // 使用userScoreSearchParams的reviewStatus
    };
    
    // 如果是TEACHER-LV1角色，只允许查看自己的数据
    if (currentRole.roleAuth === 'TEACHER-LV1' && currentUserId.value) {
      params.userId = currentUserId.value;
      console.log('TEACHER-LV1角色，只查询自己的数据:', currentUserId.value);
    }
    
    const response = await getAllUsersTotalScore(params);
    
    if (response && response.code === 200) {
      userScoreData.value = response.data.list || [];
      userScorePagination.total = response.data.pagination?.total || 0;
    } else {
      message.error(response?.message || '获取用户得分数据失败');
    }
  } catch (error) {
    console.error('获取用户得分数据失败:', error);
    message.error('获取用户得分数据失败');
  } finally {
    userScoreLoading.value = false;
  }
};

// 用户得分范围变化处理
const handleUserScoreRangeChange = () => {
  userScorePagination.current = 1;
  fetchAllUsersTotalScore();
};

// 用户得分表格变化处理
const handleUserScoreTableChange = (pag) => {
  // 只更新必要的分页属性，保持其他配置不变
  userScorePagination.current = pag.current;
  userScorePagination.pageSize = pag.pageSize;
  // 确保其他配置属性保持不变
  userScorePagination.showSizeChanger = true;

  userScorePagination.pageSizeOptions = ['10', '20', '50'];
  userScorePagination.showTotal = total => `共 ${total} 条`;
  fetchAllUsersTotalScore();
};

// 重置用户得分搜索
const resetUserScoreSearch = () => {
  userScoreSearchParams.projectId = '';
  userScoreSearchParams.nickname = '';
  userScoreSearchParams.reviewStatus = 'reviewed';
  userScorePagination.current = 1;
  fetchAllUsersTotalScore();
};

// 显示用户得分详情
const showUserScoreDetails = async (record) => {
  // 检查权限：只有非TEACHER-LV1用户或查看自己记录的TEACHER-LV1用户可以查看详情
  if (currentRole.roleAuth === 'TEACHER-LV1' && record.userId !== currentUserId.value) {
    message.error('您只能查看自己的项目详情');
    return;
  }
  
  userDetailsVisible.value = true;
  userDetailsLoading.value = true;
  selectedUserDetailName.value = record.nickname || '用户';
  selectedUserId.value = record.userId;
  userDetailsPagination.current = 1; // 重置到第一页
  
  try {
    // 调用API获取用户项目详情
    const response = await getUserProjectDetails({
      userId: record.userId,
      range: userScoreChartRange.value,
      reviewStatus: userScoreSearchParams.reviewStatus,
      page: userDetailsPagination.current,
      pageSize: userDetailsPagination.pageSize
    });
    
    if (response && response.code === 200) {
      // 映射数据字段，确保与表格期望的格式一致
      userProjectDetails.value = response.data.projects.map(item => ({
        ...item,
        // 添加或转换字段以匹配表格期望的格式
        allocationProportion: item.allocationRatio, // 属性名映射
        levelName: item.levelName || '', // 级别名称
        
        // 保留level对象供formatLevel函数使用
        level: {
          id: item.levelId,
          levelName: item.levelName || formatLevel(item.levelId)
        },
        
        userScore: parseFloat(item.userScore || 0), // 确保是数字类型
        totalScore: parseFloat(item.totalScore || 0), // 确保是数字类型
        reviewStatusText: getReviewStatusText(item.reviewStatus) // 获取可读的审核状态文本
      }));
      
      // 计算总分
      userDetailsTotalScore.value = response.data.projects.reduce((total, item) => 
        total + parseFloat(item.userScore || 0), 0
      );
      
      userDetailsPagination.total = response.data.pagination?.total || 0;
    } else {
      message.error(response?.message || '获取用户项目详情失败');
    }
  } catch (error) {
    console.error('获取用户项目详情失败:', error);
    message.error('获取用户项目详情失败');
  } finally {
    userDetailsLoading.value = false;
  }
};

// 获取审核状态的可读文本
const getReviewStatusText = (reviewStatus) => {
  if (reviewStatus === 'reviewed') return '已审核';
  if (reviewStatus === 'rejected') return '已拒绝';
  return '待审核';
};

// 审核状态转换为可读文本
const getReviewStatusDesc = (status) => {
  if (reviewStatus === 1) return '已审核';
  if (reviewStatus === 0) return '已拒绝';
  return '待审核';
};

// 处理上传前检查
const beforeUpload = (file) => {
  // 检查文件类型
  const allowedTypes = [
    // 文档类型
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    // 图片类型
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/webp',
    // 压缩文件
    'application/zip',
    'application/x-rar-compressed'
  ];
  
  const isAllowedType = allowedTypes.includes(file.type);
  if (!isAllowedType) {
    message.error('文件类型不支持！请上传文档、图片或压缩文件');
    return isAllowedType || Upload.LIST_IGNORE;
  }
  
  // 检查单个文件大小限制（10MB）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('单个文件不能超过10MB！');
    return isLt10M || Upload.LIST_IGNORE;
  }
  
  // 计算当前已选择的文件总大小
  const totalSize = fileList.value.reduce((size, item) => {
    return size + (item.originFileObj?.size || 0);
  }, 0) + file.size;
  
  // 检查总文件大小限制（50MB）
  const isLt50M = totalSize / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('所有文件总大小不能超过50MB！');
    return isLt50M || Upload.LIST_IGNORE;
  }
  
  return true;
};

// 处理导入
const handleImport = async ({ file, onSuccess, onError }) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await importResearchProjects(formData);
    
    if (response && response.code === 200) {
      message.success('导入成功');
      onSuccess();
      fetchData();
    } else {
      message.error(response?.message || '导入失败');
      onError();
    }
  } catch (error) {
    console.error('导入失败:', error);
    message.error('导入失败');
    onError();
  }
};

// 处理表格变化
const handleTableChange = (pag) => {
  // 只更新必要的分页属性，保持其他配置不变
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // 确保其他配置属性保持不变
  pagination.showSizeChanger = true;

  pagination.pageSizeOptions = ['10', '20', '50'];
  pagination.showTotal = total => `共 ${total} 条`;
  fetchData();
};

// 清空表单数据
const resetForm = () => {
  // 重置表单状态
  Object.assign(formState, {
    id: null,
    userId: null,
    projectId: '', // 确保项目编号字段被重置为空字符串
    name: '',
    level: null,
    type: null,
    projectIssuingDepartment: '',
    fundingAmount: 0,
    isUniversityFirstUnit: '是',
    isCollegeFirstUnit: '是',
    startDate: null,
    approvalDate: null,
    endDate: null,
    status: 1,
    description: '',
    remark: '',
    score: 0,
    participants: [],
    submitterId: null,

    // 重置文件相关字段
    attachmentUrl: [], // 文件路径数组
    fileIds: [], // 文件ID数组
    deletedFileIds: [] // 要删除的文件ID数组
  });

  // 重置当前成员
  Object.assign(currentMember, {
    userId: null,
    displayName: '',
    username: '',
    studentNumber: '',
    nickname: '',
    allocationRatio: 10, // 默认分配比例10%
    isLeader: false,
    participantRank: 1 // 默认排名为1
  });

  // 清空文件列表
  fileList.value = [];

  // 清空提交人选项列表，避免数据污染
  submitterOptions.value = [];

  // 清空表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate();
  }

  console.log('表单已重置');
};

// 打开添加模态框
const showAddModal = async () => {
  // 重置表单
  resetForm();

  // 设置模态框标题和模式
  modalTitle.value = '添加科研项目';
  editMode.value = false;

  // 方案B：移除自动填充功能，让用户手动选择提交人
  // 这样可以避免显示问题，并且让用户有更多的控制权

  // 确保提交人字段为空，需要用户手动选择
  formState.submitterId = null;
  submitterOptions.value = [];

  console.log('添加模式：提交人字段已清空，需要用户手动选择');

  // 显示模态框
  modalVisible.value = true;
};

// 编辑项目
const handleEdit = async (record) => {
  try {
    // 设置模态框标题和模式
    modalTitle.value = '编辑科研项目';
    editMode.value = true;
    
    // 先重置表单，避免数据残留
    resetForm();
    
    // 显示loading状态
    loading.value = true;
    
    // 通过API获取项目详情
    console.log('正在获取项目详情:', record.id);
    const response = await getResearchProjectDetail(record.id);
    
    if (!response || response.code !== 200 || !response.data) {
      message.error('获取项目详情失败');
      loading.value = false;
      return;
    }
    
    const projectData = response.data;
    console.log('获取项目详情成功:', projectData);
    
    // 先显示模态框，再设置数据，以确保DOM已经渲染
    modalVisible.value = true;
    
    // 等待DOM更新后再设置表单数据
    await nextTick();
    
    // 复制记录数据到表单状态
    formState.id = projectData.id;
    formState.projectId = projectData.projectId || '';
    formState.name = projectData.name || '';
    formState.level = projectData.levelId || '';
    formState.type = projectData.type || '';
    formState.projectIssuingDepartment = projectData.projectIssuingDepartment || '';
    formState.fundingAmount = projectData.fundingAmount || 0;
    formState.isUniversityFirstUnit = projectData.isUniversityFirstUnit ? '是' : '否';
    formState.isCollegeFirstUnit = projectData.isCollegeFirstUnit ? '是' : '否';
    formState.startDate = projectData.startDate ? dayjs(projectData.startDate) : null;
    formState.approvalDate = projectData.approvalDate ? dayjs(projectData.approvalDate) : null;
    formState.endDate = projectData.endDate ? dayjs(projectData.endDate) : null;
    formState.status = projectData.status || 1;
    formState.description = projectData.description || '';
    formState.remark = projectData.remark || '';
    formState.score = projectData.score || 0;
    formState.submitterId = projectData.submitterId || null;
    
    // 处理提交人信息
    if (projectData.submitter) {
      const submitter = projectData.submitter;
      // 确保提交人选项正确设置，显示昵称优先
      submitterOptions.value = [{
        value: submitter.id,
        label: `${submitter.nickname || submitter.username || '未知用户'} (${submitter.studentNumber || '无工号'})`
      }];
      console.log('已设置编辑模式提交人:', submitter.nickname || submitter.username);
    } else if (projectData.submitterId) {
      // 如果只有submitterId但没有提交人详情，则尝试获取用户信息
      try {
        const userInfo = await usersFindOne({ id: projectData.submitterId });
        if (userInfo && userInfo.code === 200 && userInfo.data) {
          const submitterUser = userInfo.data;
          submitterOptions.value = [{
            value: submitterUser.id,
            label: `${submitterUser.nickname || submitterUser.username || '未知用户'} (${submitterUser.studentNumber || '无工号'})`
          }];
          console.log('已获取并设置编辑模式提交人:', submitterUser.nickname || submitterUser.username);
        } else {
          console.warn('无法获取提交人详细信息，ID:', projectData.submitterId);
          // 即使无法获取详情，也要保持submitterId的值
          submitterOptions.value = [{
            value: projectData.submitterId,
            label: `用户ID: ${projectData.submitterId} (详情获取失败)`
          }];
        }
      } catch (error) {
        console.warn('获取提交人信息失败:', error);
        // 发生错误时也要保持submitterId的值
        submitterOptions.value = [{
          value: projectData.submitterId,
          label: `用户ID: ${projectData.submitterId} (获取失败)`
        }];
      }
    } else {
      console.warn('项目数据中缺少提交人信息');
      submitterOptions.value = [];
    }
    
    // 处理参与人列表
    formState.participants = [];
    
    if (projectData.participants && Array.isArray(projectData.participants)) {
      formState.participants = projectData.participants.map(member => {
        const userData = member.user || {};
        return {
          userId: member.userId,
          displayName: userData.nickname || userData.username || '未知',
          username: userData.username || '',
          studentNumber: userData.studentNumber || '',
          nickname: userData.nickname || '',
          allocationRatio: member.allocationRatio * 100, // 将小数转换为百分比格式
          isLeader: member.isLeader || false,
          participantRank: member.participantRank || (member.isLeader ? 1 : null) // 如果是负责人且未设置排名，则默认为1
        };
      });
    }
    
    // 处理附件列表
    fileList.value = (projectData.attachments || []).map((file, index) => ({
      uid: `-${index}`,
      name: file.name || `附件${index + 1}`,
      status: 'done',
      url: file.url,
      response: { file: { id: file.id } },
      data: file
    }));
    
    // 重置当前成员
    currentMember.userId = null;
    currentMember.displayName = '';
    currentMember.username = '';
    currentMember.studentNumber = '';
    currentMember.nickname = '';
    currentMember.allocationRatio = 10; // 默认分配比例10%
    currentMember.isLeader = false;
    currentMember.participantRank = 1;
    
  } catch (error) {
    console.error('获取项目详情失败:', error);
    message.error('获取项目详情失败: ' + (error.message || error));
    modalVisible.value = false; // 发生错误时关闭模态框
  } finally {
    loading.value = false;
  }
};

// 确认删除
const confirmDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDelete(record)
  })
}

// 处理删除
const handleDelete = async (record) => {
  try {
    // 修改这里，确保仅传递id参数
    const response = await deleteResearchProject({ id: record.id });

    if (response && response.code === 200) {
      message.success('删除成功');
      fetchData();
    } else {
      message.error(response?.message || '删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 先进行表单验证
    await formRef.value.validate();

    loading.value = true;

    console.log('开始提交表单，模式:', editMode.value ? '编辑' : '添加');
    
    // 验证参与者
    if (!formState.participants || formState.participants.length === 0) {
      message.error('请至少添加一名参与人员');
      loading.value = false;
      return;
    }
    
    // 验证分配比例总和
    if (!validateTotalAllocation()) {
      message.error('所有参与者的分配比例总和必须为100%');
      loading.value = false;
      return;
    }
    
    // 确保有且仅有一个负责人
    const leaderCount = formState.participants.filter(p => p.isLeader).length;
    if (leaderCount !== 1) {
      message.error('请确保有且仅有一个项目负责人');
      loading.value = false;
      return;
    }
    
    // 验证提交人
    if (!formState.submitterId) {
      message.error('请选择提交人');
      loading.value = false;
      return;
    }

    // 验证提交人选项是否存在（确保选择的提交人是有效的）
    const selectedSubmitter = submitterOptions.value.find(option => option.value === formState.submitterId);
    if (!selectedSubmitter && submitterOptions.value.length > 0) {
      message.error('所选提交人无效，请重新选择');
      loading.value = false;
      return;
    }
    
    // 构建提交数据 - 使用原生FormData API
    const formData = new FormData();
    
    // 添加基本项目信息（确保不添加undefined或null值）
    if (formState.projectId) formData.append('projectId', formState.projectId);
    formData.append('name', formState.name || '');
    formData.append('levelId', formState.level || '');
    formData.append('type', formState.type || '');
    formData.append('projectIssuingDepartment', formState.projectIssuingDepartment || '');
    formData.append('fundingAmount', formState.fundingAmount || 0);
    
    // 将"是"/"否"转换为1/0
    formData.append('isUniversityFirstUnit', formState.isUniversityFirstUnit === '是' ? 1 : 0);
    formData.append('isCollegeFirstUnit', formState.isCollegeFirstUnit === '是' ? 1 : 0);
    
    // 添加提交人ID
    formData.append('submitterId', formState.submitterId);
    
    if (formState.startDate) {
      formData.append('startDate', dayjs(formState.startDate).format('YYYY-MM-DD'));
    }
    
    if (formState.approvalDate) {
      formData.append('approvalDate', dayjs(formState.approvalDate).format('YYYY-MM-DD'));
    }
    
    if (formState.endDate) {
      formData.append('endDate', dayjs(formState.endDate).format('YYYY-MM-DD'));
    }
    
    if (formState.description) formData.append('description', formState.description);
    if (formState.remark) formData.append('remark', formState.remark);
    formData.append('status', formState.status || 1);
    
    // 添加参与者信息
    // 找到负责人
    const leader = formState.participants.find(p => p.isLeader);
    if (leader) {
      formData.append('userId', leader.userId);
      // 将百分比转换为小数 (100% -> 1.0)
      formData.append('userAllocationProportion', (leader.allocationRatio / 100).toFixed(2));
    }
    
    // 添加其他成员 - 解决JSON字符串转换问题
    const members = formState.participants.filter(p => !p.isLeader);
    if (members.length > 0) {
      // 将所有成员的分配比例转换为小数
      const membersData = members.map(member => ({
        id: member.userId,
        allocationProportion: (member.allocationRatio / 100).toFixed(2),
        participantRank: member.participantRank || members.indexOf(member) + 2 // 如果没有设置排名，则按照顺序分配
      }));
      formData.append('members', JSON.stringify(membersData));
      console.log('成员数据:', JSON.stringify(membersData));
    }
    
    // ===== 重构文件处理逻辑 =====
    
    // 1. 直接检查文件列表，并使用最直接的方式构建文件ID和路径数组
    let hasUploadedFiles = false;
    const fileIds = [];
    const filePaths = [];
    
    console.log('开始处理文件数据，当前fileList:', fileList.value);
    
    // 处理上传的文件列表
    if (fileList.value && fileList.value.length > 0) {
      // 遍历fileList，收集已上传文件的ID和路径
      fileList.value.forEach(file => {
        console.log('检查文件:', file.name, '状态:', file.status);
        
        if (file.status === 'done') {
          // 尝试从不同位置获取文件ID
          let fileId = null;
          if (file.response && file.response.id) {
            fileId = file.response.id;
          } else if (file.response && file.response.fileInfo && file.response.fileInfo.id) {
            fileId = file.response.fileInfo.id;
          } else if (file.uid && file.uid.toString().length > 10) {
            // 可能是文件ID存储在uid中
            fileId = file.uid;
          }
          
          // 尝试从不同位置获取文件路径
          let filePath = null;
          if (file.filePath) {
            filePath = file.filePath;
          } else if (file.response && file.response.fileInfo && file.response.fileInfo.filePath) {
            filePath = file.response.fileInfo.filePath;
          }
          
          // 只有当文件ID和路径都存在时才添加到数组
          if (fileId && filePath) {
            if (!fileIds.includes(fileId)) {
              fileIds.push(fileId);
            }
            if (!filePaths.includes(filePath)) {
              filePaths.push(filePath);
            }
            hasUploadedFiles = true;
            console.log(`添加文件: ID=${fileId}, 路径=${filePath}`);
          } else {
            console.warn(`文件 ${file.name} 缺少ID或路径信息`);
          }
        }
      });
    }
    
    // 2. 检查formState中是否已经有文件信息
    if (!hasUploadedFiles && formState.fileIds && formState.fileIds.length > 0) {
      console.log('使用formState中的文件信息');
      
      // 确保attachmentUrl数组长度与fileIds相匹配
      if (formState.attachmentUrl && formState.attachmentUrl.length === formState.fileIds.length) {
        formState.fileIds.forEach((id, index) => {
          if (!fileIds.includes(id)) {
            fileIds.push(id);
          }
          if (!filePaths.includes(formState.attachmentUrl[index])) {
            filePaths.push(formState.attachmentUrl[index]);
          }
        });
        hasUploadedFiles = true;
      } else {
        console.warn('formState中的fileIds和attachmentUrl长度不匹配');
      }
    }
    
    // 3. 将收集到的文件信息添加到表单数据中
    if (hasUploadedFiles) {
      console.log('最终文件IDs:', fileIds);
      console.log('最终文件路径:', filePaths);
      
      if (fileIds.length > 0) {
        formData.append('fileIds', JSON.stringify(fileIds));
      }
      
      if (filePaths.length > 0) {
        formData.append('attachmentUrl', JSON.stringify(filePaths));
      }
    } else {
      console.log('没有找到已上传的文件');
    }
    
    // 添加要删除的文件IDs
    if (formState.deletedFileIds && formState.deletedFileIds.length > 0) {
      formData.append('deletedFileIds', JSON.stringify(formState.deletedFileIds));
      console.log('删除文件IDs:', formState.deletedFileIds);
    }
    
    // 调试输出表单内容摘要
    console.log('表单数据内容摘要:');
    for (let [key, value] of formData.entries()) {
      console.log(`${key}: ${typeof value === 'object' ? '[Object]' : value}`);
    }
    
    // 提交表单，添加重试逻辑
    let maxRetries = 2;
    let retryCount = 0;
    let response;
    
    const submitForm = async () => {
      try {
        if (editMode.value) {
          formData.append('id', formState.id);
          response = await updateResearchProject(formData);
        } else {
          response = await addResearchProject(formData);
        }
        return response;
      } catch (error) {
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`提交失败，正在重试 (${retryCount}/${maxRetries})...`, error);
          await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒再重试
          return submitForm();
        }
        throw error;
      }
    };
    
    response = await submitForm();
    
    if (response && response.code === 200) {
      message.success(editMode.value ? '项目更新成功' : '项目添加成功');
      
      // 如果是新建项目且有文件，则调用移动文件到项目目录接口
      if (!editMode.value && hasUploadedFiles && fileIds.length > 0) {
        try {
          const newProjectId = response.data?.id;
          if (newProjectId) {
            // 调用移动文件API
            const moveResponse = await axios.post('/api/v1/sys/file/move-to-project', {
              projectId: newProjectId,
              fileIds: JSON.stringify(fileIds),
              class: 'research_project'
            });
            
            if (moveResponse.data && moveResponse.data.code === 200) {
              console.log('文件已成功移动到项目目录:', moveResponse.data);
            } else {
              console.warn('移动文件到项目目录失败:', moveResponse.data);
            }
          }
        } catch (moveError) {
          console.error('调用移动文件接口失败:', moveError);
          // 不影响主流程，不显示错误提示
        }
      }
      
      modalVisible.value = false;
      fetchData();
    } else {
      message.error(response?.msg || response?.message || '操作失败');
    }
  } catch (error) {
    console.error('提交表单时出错:', error);
    message.error('表单验证失败或提交时发生错误：' + (error.message || error));
  } finally {
    loading.value = false;
  }
};

// 处理模态框取消
const handleModalCancel = async () => {
  // 检查是否有上传的文件需要删除
  if (fileList.value && fileList.value.length > 0 && !editMode.value) {
    try {
      console.log('模态框关闭，准备删除未保存的文件');
      // 收集所有需要删除的文件ID
      const filesToDelete = fileList.value
        .filter(file => file.status === 'done' && file.response)
        .map(file => {
          // 尝试从不同位置获取文件ID
          let fileId = null;
          if (file.response && file.response.id) {
            fileId = file.response.id;
          } else if (file.response && file.response.fileInfo && file.response.fileInfo.id) {
            fileId = file.response.fileInfo.id;
          } else if (file.uid && file.uid.toString().length > 10) {
            fileId = file.uid;
          }
          return fileId;
        })
        .filter(id => id); // 过滤掉null/undefined
      
      console.log('需要删除的文件IDs:', filesToDelete);
      
      // 如果有文件ID，则逐个删除
      if (filesToDelete.length > 0) {
        message.loading(`正在清理已上传文件...`, 1);
        
        // 并行删除所有文件
        await Promise.all(filesToDelete.map(async (fileId) => {
          try {
            console.log(`删除文件ID: ${fileId}`);
            await deleteFile(fileId);
          } catch (error) {
            console.error(`删除文件 ${fileId} 失败:`, error);
          }
        }));
        
        message.success('未保存的文件已清理');
      }
    } catch (error) {
      console.error('清理未保存文件失败:', error);
    }
  }
  
  // 重置表单状态并关闭模态框
  resetForm();
  modalVisible.value = false;

  // 额外清理，确保状态完全重置
  editMode.value = false;
  modalTitle.value = '';
  loading.value = false;

  console.log('模态框已关闭，所有状态已重置');
};

// 开始日期禁用逻辑
const disabledStartDate = (current) => {
  return current && current > dayjs();
};

// 结束日期禁用逻辑
const disabledEndDate = (current) => {
  return (current && current < formState.startDate) || (current && current > dayjs());
};

// 处理开始日期变化
const handleStartDateChange = (value) => {
  if (formState.endDate && value > formState.endDate) {
    formState.endDate = null;
  }
};

// 负责人搜索
const handleLeaderSearch = (value) => {
  // 清除之前的延时搜索
  if (leaderSearchTimeout.value) {
    clearTimeout(leaderSearchTimeout.value);
  }
  
  // 如果搜索词为空，则清空选项列表并返回
  if (!value || value.trim() === '') {
    leaderOptions.value = [];
    return;
  }
  
  // 设置搜索中状态
  leaderSearchLoading.value = true;
  
  // 延时搜索，避免频繁请求
  leaderSearchTimeout.value = setTimeout(() => {
    // 调用用户搜索API
    usersSearch({ keyword: value.trim() })
      .then((res) => {
        // 处理API返回结果
        if (res && res.code === 200) {
          // 将返回的用户数据转换为下拉选项格式
          if (Array.isArray(res.data)) {
            leaderOptions.value = res.data.map(item => ({
              value: item.id, // 用户ID作为选项值
              label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`, // 显示名称和用户名
              ...item // 保留原始用户数据，方便后续使用
            }));
          } else if (res.data && Array.isArray(res.data.list)) {
            // 如果返回的是包含list属性的对象
            leaderOptions.value = res.data.list.map(item => ({
              value: item.id,
              label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`,
              ...item
            }));
          } else {
            // 如果数据结构不是预期的格式
            console.error('搜索负责人返回的数据结构异常:', res.data);
            leaderOptions.value = [];
          }
        } else {
          leaderOptions.value = [];
          console.warn('用户搜索返回数据格式不正确:', res);
        }
      })
      .catch((error) => {
        console.error('搜索负责人失败:', error);
        message.error('搜索负责人失败');
        leaderOptions.value = [];
      })
      .finally(() => {
        leaderSearchLoading.value = false;
      });
  }, 500); // 500ms的防抖延迟
};

// 处理负责人选择变更
const handleLeaderChange = (value) => {
  console.log('选择负责人ID:', value);
  searchParams.leader = value; // 存储用户ID而非名称
};

// 成员搜索
const handleMembersSearch = async (value) => {
  if (!value || value.length < 2) return;
  
  try {
    membersSearchLoading.value = true;
    
    // 这里应该有一个API调用来搜索用户
    // 例如: const response = await searchUsers(value);
    // 如果有相关API，使用真实的API调用
    // 这里使用延时模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 模拟返回数据
    membersOptions.value = [
      { id: '1', nickname: '张三', studentNumber: '2023001' },
      { id: '2', nickname: '李四', studentNumber: '2023002' },
      { id: '3', nickname: '王五', studentNumber: '2023003' },
      { id: '4', nickname: value + '测试', studentNumber: '2023004' }
    ].filter(option => 
      option.nickname.includes(value) || 
      (option.studentNumber && option.studentNumber.includes(value))
    );
    
  } catch (error) {
    console.error('搜索成员失败:', error);
    message.error('搜索成员失败');
  } finally {
    membersSearchLoading.value = false;
  }
};

// 添加成员
const handleAddMember = () => {
  // 验证当前成员信息是否完整
  if (!currentMember.userId) {
    message.warning('请先选择参与人');
    return;
  }
  
  if (!currentMember.allocationRatio || currentMember.allocationRatio <= 0 || currentMember.allocationRatio > 100) {
    message.warning('分配比例必须大于0且不超过100');
    return;
  }
  
  // 检查是否已经存在相同的成员
  const existingMember = formState.participants.find(
    (item) => item.userId === currentMember.userId
  );
  
  if (existingMember) {
    message.warning(`${currentMember.displayName}已在参与人列表中`);
    return;
  }
  
  // 计算当前总分配比例
  const currentTotalRatio = formState.participants.reduce(
    (sum, item) => sum + Number(item.allocationRatio),
    0
  );
  
  // 检查添加新成员后总比例是否超过100%
  if (currentTotalRatio + Number(currentMember.allocationRatio) > 100) {
    message.warning(`添加此成员后总分配比例将超过100%，当前已分配${currentTotalRatio}%`);
    return;
  }
  
  // 自动设置排名
  if (formState.participants.length === 0) {
    // 如果是第一个成员，设置排名为1
    currentMember.participantRank = 1;
  } else {
    // 否则设置为当前最大排名+1
    const maxRank = Math.max(...formState.participants.map(p => p.participantRank || 0));
    currentMember.participantRank = maxRank + 1;
  }
  
  // 添加新成员到列表
  formState.participants.push({
    userId: currentMember.userId,
    displayName: currentMember.displayName,
    username: currentMember.username,
    studentNumber: currentMember.studentNumber,
    nickname: currentMember.nickname,
    allocationRatio: Number(currentMember.allocationRatio),
    isLeader: formState.participants.length === 0, // 第一个成员默认设为负责人
    participantRank: currentMember.participantRank // 添加排名信息
  });
  
  // 重置当前成员信息
  currentMember.userId = null;
  currentMember.displayName = '';
  currentMember.username = '';
  currentMember.studentNumber = '';
  currentMember.nickname = '';
  currentMember.allocationRatio = 10;
  currentMember.isLeader = false;
  currentMember.participantRank = formState.participants.length + 1; // 设置下一个成员的默认排名
  
  message.success('成员添加成功');
  
  // 验证总比例，但不自动调整
  validateTotalAllocation();
};

// 设置/取消项目负责人
const toggleLeader = (index, isLeader) => {
  // 如果设置为负责人，先将其他所有人设为非负责人
  if (isLeader) {
    formState.participants.forEach((p, i) => {
      if (i !== index) {
        p.isLeader = false;
      }
    });
  } else {
    // 如果尝试取消负责人，检查是否还有其他负责人
    const hasOtherLeader = formState.participants.some((p, i) => i !== index && p.isLeader);
    if (!hasOtherLeader) {
      message.warning('项目必须有一个负责人');
      // 防止切换，保持当前参与人为负责人
      formState.participants[index].isLeader = true;
      return;
    }
  }
  
  // 修改指定参与人的负责人状态
  formState.participants[index].isLeader = isLeader;
};

// 移除成员
const handleRemoveMember = (index) => {
  const member = formState.participants[index];
  
  // 如果要移除的是负责人，需要确保有其他成员可以设为负责人
  if (member.isLeader && formState.participants.length > 1) {
    // 设置另一个成员为负责人
    const newLeaderIndex = index === 0 ? 1 : 0;
    formState.participants[newLeaderIndex].isLeader = true;
  }
  
  // 移除成员
  formState.participants.splice(index, 1);
  
  // 检查分配比例总和
  if (!validateTotalAllocation() && formState.participants.length > 0) {
    message.warning('所有成员的分配比例总和应为1（即100%）');
  }
};

// 处理文件上传
const handleFileUpload = ({ file, onSuccess, onError, onProgress }) => {
  utilUploadFile({
    file,
    uploadApi: uploadFiles,
    id: formState.id || 'temp_' + Date.now(),
    relatedId: formState.id,
    class: 'research_projects',
    onProgress,
    onSuccess,
    onError,
    fileList: fileList.value,
    formState
  });
};

// 处理审核
const handleReview = async (record) => {
  try {
    // 创建一个ref存储审核信息
    const reviewInfo = ref({
      reviewStatus: 1, // 默认通过
      reviewComment: '', // 审核意见
      attachments: [] // 存储项目附件
    });
    
    // 显示加载中
    message.loading('正在加载项目详情...', 0.5);
    
    // 获取项目详情，包括附件
    try {
      // 获取项目详情
      const response = await getResearchProjectDetail(record.id);
      if (response && response.code === 200 && response.data) {
        // 设置项目附件
        reviewInfo.value.attachments = (response.data.attachments || []).map((file, index) => ({
          uid: `-${index}`,
          name: file.name || file.originalName || `附件${index + 1}`,
          status: 'done',
          url: file.url,
          response: { file: { id: file.id } },
          data: file
        }));
        console.log('项目附件:', reviewInfo.value.attachments);
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
      // 不中断流程，继续显示审核对话框
    }
    
    // 确认审核操作
    Modal.confirm({
      title: '审核科研项目',
      content: () => h('div', {}, [
        h('p', { style: { marginBottom: '10px' } }, `您正在审核"${record.name}"科研项目`),
        h('div', { style: { marginBottom: '10px' } }, [
          h('span', { style: { display: 'inline-block', width: '80px' } }, '审核结果：'),
          h(Radio.Group, {
            value: reviewInfo.value.reviewStatus,
            onChange: (e) => { reviewInfo.value.reviewStatus = e.target.value }
          }, () => [
            h(Radio, { value: 1 }, () => '通过'),
            h(Radio, { value: 0 }, () => '拒绝')
          ])
        ]),
        h('div', { style: { marginBottom: '15px' } }, [
          h('span', { style: { display: 'inline-block', width: '80px', verticalAlign: 'top' } }, '审核意见：'),
          h(Input.TextArea, {
            value: reviewInfo.value.reviewComment,
            onChange: (e) => { reviewInfo.value.reviewComment = e.target.value },
            rows: 3,
            placeholder: '请输入审核意见'
          })
        ]),
        // 添加项目附件列表
        reviewInfo.value.attachments && reviewInfo.value.attachments.length > 0 
          ? h('div', {}, [
              h('div', { style: { fontWeight: 'bold', marginBottom: '10px' } }, '项目附件:'),
              h('div', { style: { maxHeight: '200px', overflow: 'auto' } },
                h('table', { style: { width: '100%', borderCollapse: 'collapse' } }, [
                  h('thead', {}, 
                    h('tr', { style: { backgroundColor: '#f5f5f5' } }, [
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8' } }, '文件名'),
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8', width: '120px' } }, '大小'),
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8', width: '150px' } }, '操作')
                    ])
                  ),
                  h('tbody', {}, 
                    reviewInfo.value.attachments.map(file => 
                      h('tr', { key: file.uid, style: { borderBottom: '1px solid #e8e8e8' } }, [
                        h('td', { style: { padding: '8px', textAlign: 'left' } }, file.name),
                        h('td', { style: { padding: '8px', textAlign: 'left' } }, formatFileSize(file.data?.size || 0)),
                        h('td', { style: { padding: '8px', textAlign: 'left' } },
                          h('div', { style: { display: 'flex', gap: '8px' }}, [
                            h('a', { 
                              style: { color: '#1890ff', cursor: 'pointer' },
                              onClick: () => previewFile(file)
                            }, '预览'),
                            h('a', { 
                              style: { color: '#1890ff', cursor: 'pointer' },
                              onClick: () => downloadFile(file)
                            }, '下载')
                          ])
                        )
                      ])
                    )
                  )
                ])
              )
            ])
          : h('div', { style: { color: '#999', marginTop: '10px' } }, '该项目没有附件')
      ]),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 获取当前用户ID作为审核人
          await getUserId(true);
          
          if (!userId.value) {
            message.error('无法获取用户ID，请重新登录');
            return;
          }
          
          console.log('审核人ID:', userId.value);
          
          // 调用审核API，直接传递ID和用户ID
          const submitData = {
            id: record.id,
            reviewer: userId.value,
            reviewStatus: reviewInfo.value.reviewStatus,
            reviewComment: reviewInfo.value.reviewComment
          };
          
          const response = await reviewProject(submitData);
          
          if (response && response.code === 200) {
            message.success('项目审核成功');
            fetchData(); // 刷新数据
          } else {
            message.error(response?.message || '审核失败');
          }
        } catch (error) {
          console.error('审核项目失败:', error);
          message.error('审核项目失败: ' + (error.message || '未知错误'));
        }
      },
      width: 600
    });
  } catch (error) {
    console.error('打开审核对话框失败:', error);
    message.error('操作失败: ' + (error.message || '未知错误'));
  }
};


// 切换个人/全部项目视图
const togglePersonalProjects = async () => {
  showPersonalProjects.value = !showPersonalProjects.value;
  console.log(`切换到${showPersonalProjects.value ? '个人' : '全部'}项目视图`);
  
  // 销毁现有图表，避免重复请求
  console.log('销毁现有图表实例，准备重新初始化');
  if (levelChart) {
    levelChart.dispose();
    levelChart = null;
  }
  if (reviewStatusChart) {
    reviewStatusChart.dispose();
    reviewStatusChart = null;
  }
  if (timeChart) {
    timeChart.dispose();
    timeChart = null;
  }
  if (scoreChart) {
    scoreChart.dispose();
    scoreChart = null;
  }
  // 删除对项目负责人得分排名图的清理

  // 清除图表缓存
  clearChartCache();
  
  // 重置查询参数，只保留分页信息
  queryParams.name = '';
  queryParams.level = '';
  queryParams.leader = '';
  queryParams.startDate = undefined;
  queryParams.endDate = undefined;
  queryParams.range = 'all';
  queryParams.reviewStatus = 'all';
  
  // 重置分页到第一页
  pagination.current = 1;
  
  try {
    // 重新获取数据
    await fetchData();
    
    // 等待DOM更新后初始化图表
    setTimeout(() => {
      console.log('DOM更新后初始化图表');
      initLevelChart(levelChartRange.value, levelChartReviewStatus.value);
      initReviewStatusChart(reviewStatusChartRange.value, reviewStatusChartReviewStatus.value);
      initTimeChart(timeChartRange.value, timeChartReviewStatus.value);
      initScoreChart(scoreChartRange.value, scoreChartReviewStatus.value);
      // 删除对项目负责人得分排名图的初始化
    }, 100);
    
    // 如果切换到个人视图，获取用户得分数据
    if (showPersonalProjects.value) {
      try {
        await fetchUserScore();
      } catch (error) {
        console.error('获取用户得分数据失败:', error);
      }
    }
  } catch (error) {
    console.error('切换视图失败:', error);
    message.error('切换视图失败，请稍后重试');
  }
}

// 更新所有图表
const updateCharts = async () => {
  try {
    console.log('开始尝试更新图表...');

    // 避免重复更新，只有当图表未初始化时才进行更新
    if (!chartsInitialized.value) {
      chartsInitialized.value = true; // 立即标记为已初始化，防止重复调用
      
      // 同时初始化所有图表，并使用Promise.all等待所有图表初始化完成
      await Promise.all([
        initLevelChart(levelChartRange.value, levelChartReviewStatus.value),
        initReviewStatusChart(reviewStatusChartRange.value, reviewStatusChartReviewStatus.value),
        initTimeChart(timeChartRange.value, timeChartReviewStatus.value),
        initScoreChart(scoreChartRange.value, scoreChartReviewStatus.value)
        // 删除对项目负责人得分排名图的初始化
      ]);
      
      console.log('所有图表更新完成');
    } else {
      console.log('图表已初始化，跳过更新');
    }
  } catch (error) {
    console.error('更新图表出错:', error);
    message.error('加载图表数据失败');
    chartsInitialized.value = false; // 出错时重置状态，允许重试
  }
};

// 获取数据的方法
const fetchData = async () => {
  try {
    // 避免重复请求
    if (pendingRequests.projects) {
      console.log('项目列表请求已在进行中，跳过');
      return;
    }
    
    pendingRequests.projects = true;
    loading.value = true;
    errorMessage.value = '';
    
    const params = {
      ...queryParams,
      page: pagination.current,
      pageSize: pagination.pageSize
    };
    
    // 如果是个人视图，添加userId参数
    if (showPersonalProjects.value) {
      try {
        const currentUserId = await getUserId(true);
        if (currentUserId) {
          params.userId = currentUserId;
        }
      } catch (error) {
        console.warn('获取用户ID失败，但仍继续初始化图表:', error);
      }
    }
    
    const response = await getProjects(params);
    
    if (response.code === 200) {
      // 确保levelOptions.value是数组
      if (!Array.isArray(levelOptions.value)) {
        console.warn('levelOptions不是数组，初始化为空数组');
        levelOptions.value = [];
      }
      
      dataSource.value = response.data.list.map((item, index) => {
        // 根据levelId查找对应的级别项目
        const level = levelOptions.value.find(option => option.id === item.levelId);
        
        return {
          ...item,
          key: item.id || index,
          // 从级别中获取分数，如果找不到则为0
          score: level ? level.score : 0
        };
      });
      
      pagination.total = response.data.pagination.total;
      
      // 计算总分
      calculateTotalScore();
      
      // 数据加载成功后，清除缓存并更新图表（只更新一次）
      console.log('数据加载成功，准备更新图表');
      clearChartCache(); // 清除缓存，确保获取最新数据
      chartsInitialized.value = false; // 重置初始化标志，确保重新初始化
      
      // 图表数据只加载一次
      updateCharts();
    } else {
      message.error(response.message || '获取数据失败');
      errorMessage.value = response.message || '获取数据失败';
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    errorMessage.value = error.message || '获取数据失败';
  } finally {
    loading.value = false;
    pendingRequests.projects = false; // 释放请求锁
  }
};

// 组件加载时的处理
onMounted(async () => {
  try {
    // 获取当前用户角色
    currentRole.value = await getUserRole();
    console.log("当前用户角色:", currentRole.value);
    console.log("roleAuth===", currentRole.value ? currentRole.value.roleAuth : 'undefined');
    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true);
      console.log("当前用户ID:", currentUserId.value);
    } catch (error) {
      console.error("获取用户ID失败:", error);
    }
    
    // 如果是教师角色，默认显示个人项目
    if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
      showPersonalProjects.value = true;
      console.log('用户为教师角色，默认显示个人项目');
    }
    
    // 初始化搜索参数
    initSearchParams();
    
    // 获取项目级别列表
    try {
      await fetchLevelOptions();
    } catch (error) {
      console.error('获取项目级别列表失败:', error);
      message.error('获取项目级别列表失败');
      // 确保levelOptions是一个数组
      levelOptions.value = [];
    }
    
    // 获取项目数据 - 只调用一次fetchData，它会负责加载数据和初始化图表
    try {
      await fetchData();
    } catch (error) {
      console.error('获取项目数据失败:', error);
      message.error('获取项目数据失败');
    }
    
    // 获取用户科研项目得分统计数据
    try {
      await fetchAllUsersTotalScore();
    } catch (error) {
      console.error('获取用户得分统计数据失败:', error);
      message.error('获取用户得分统计数据失败');
    }
    
    // 监听窗口大小变化，以调整图表尺寸
    window.addEventListener('resize', debounce(() => {
      try {
        levelChart && levelChart.resize();
        reviewStatusChart && reviewStatusChart.resize();
        timeChart && timeChart.resize();
        scoreChart && scoreChart.resize();
      } catch (error) {
        console.error('调整图表大小出错:', error);
      }
    }, 300));
  } catch (error) {
    console.error('组件挂载时出错:', error);
    message.error('初始化界面失败');
  }
});

// 表格列定义
const columns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    width: 180,
    ellipsis: false
  },
  {
    title: '项目级别',
    dataIndex: 'levelId',
    key: 'level',
    width: 140,
    ellipsis: false
  },
  {
    title: '负责人',
    dataIndex: 'leader',
    key: 'leader',
    width: 120,
    ellipsis: true
  },
  {
    title: '参与人员',
    dataIndex: 'participants',
    key: 'members',
    width: 180,
    ellipsis: true
  },
  {
    title: '开始时间',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 100,
    ellipsis: true
  },
  {
    title: '获批时间',
    dataIndex: 'approvalDate',
    key: 'approvalDate',
    width: 100,
    ellipsis: true
  },
  {
    title: '结束时间',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 100,
    ellipsis: true
  },
  {
    title: '计算分数',
    dataIndex: 'score',
    key: 'score',
    width: 80,
    align: 'center'
  },
  {
    title: '审核状态',
    dataIndex: 'ifReviewer',
    key: 'reviewStatus',
    width: 90,
    align: 'center'
  },
  {
    title: '审核建议',
    dataIndex: 'reviewComment',
    key: 'reviewComment',
    width: 120,
    ellipsis: true
  },
  {
    title: '提交人',
    dataIndex: 'submitter',
    key: 'submitter',
    width: 120,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
    align: 'center'
  }
];

// 清除图表缓存
const clearChartCache = () => {
  Object.keys(chartDataCache).forEach(key => {
    chartDataCache[key] = {};
  });
};

// 初始化级别分布图
const initLevelChart = async (range = 'in', reviewStatus = 'all') => {
  try {
    // 仅使用已有的缓存，不再发送请求
    const cacheKey = `${range}_${reviewStatus}_${showPersonalProjects.value ? 'personal' : 'all'}`;
    if (chartDataCache.level[cacheKey]) {
      if (levelChartRef.value) {
        if (!levelChart) {
          levelChart = echarts.init(levelChartRef.value);
        }
        levelChart.setOption(chartDataCache.level[cacheKey]);
      }
    } else {
      // 直接调用changeChartRange来加载数据
      await changeChartRange('level', range);
    }
  } catch (error) {
    console.error('项目级别分布图初始化失败:', error);
    message.error('加载级别分布图表失败');
  }
};

// 初始化审核状态分布图
const initReviewStatusChart = async (range = 'in', reviewStatus = 'all') => {
  try {
    // 仅使用已有的缓存，不再发送请求
    const cacheKey = `${range}_${reviewStatus}_${showPersonalProjects.value ? 'personal' : 'all'}`;
    if (chartDataCache.reviewStatus[cacheKey]) {
      if (reviewStatusChartRef.value) {
        if (!reviewStatusChart) {
          reviewStatusChart = echarts.init(reviewStatusChartRef.value);
        }
        reviewStatusChart.setOption(chartDataCache.reviewStatus[cacheKey]);
      }
    } else {
      // 直接调用changeChartRange来加载数据
      await changeChartRange('reviewStatus', range);
    }
  } catch (error) {
    console.error('审核状态分布图初始化失败:', error);
    message.error('加载审核状态分布图表失败');
  }
};

// 初始化时间分布图
const initTimeChart = async (range = 'in', reviewStatus = 'all') => {
  try {
    // 仅使用已有的缓存，不再发送请求
    const cacheKey = `${range}_${reviewStatus}_${showPersonalProjects.value ? 'personal' : 'all'}`;
    if (chartDataCache.time[cacheKey]) {
      if (timeChartRef.value) {
        if (!timeChart) {
          timeChart = echarts.init(timeChartRef.value);
        }
        timeChart.setOption(chartDataCache.time[cacheKey]);
      }
    } else {
      // 直接调用changeChartRange来加载数据
      await changeChartRange('time', range);
    }
  } catch (error) {
    console.error('项目时间分布图初始化失败:', error);
    message.error('加载时间分布图表失败');
  }
};

// 初始化得分分布图
const initScoreChart = async (range = 'in', reviewStatus = 'all') => {
  try {
    // 仅使用已有的缓存，不再发送请求
    const cacheKey = `${range}_${reviewStatus}_${showPersonalProjects.value ? 'personal' : 'all'}`;
    if (chartDataCache.score[cacheKey]) {
      if (scoreChartRef.value) {
        if (!scoreChart) {
          scoreChart = echarts.init(scoreChartRef.value);
        }
        scoreChart.setOption(chartDataCache.score[cacheKey]);
      }
    } else {
      // 直接调用changeChartRange来加载数据
      await changeChartRange('score', range);
    }
  } catch (error) {
    console.error('初始化得分分布图表出错:', error);
    message.error('加载得分分布图表失败');
  }
};

// 切换图表范围
const changeChartRange = async (chartType, range) => {
  try {
    // 更新对应图表的范围值
    if (chartType === 'level') {
      levelChartRange.value = range;
      // 清除缓存
      chartDataCache.level = {};
      // 直接获取新数据
      await fetchLevelChartData(range, levelChartReviewStatus.value);
    } else if (chartType === 'reviewStatus') {
      reviewStatusChartRange.value = range;
      // 清除缓存
      chartDataCache.reviewStatus = {};
      // 直接获取新数据
      await fetchReviewStatusChartData(range, reviewStatusChartReviewStatus.value);
    } else if (chartType === 'time') {
      timeChartRange.value = range;
      // 清除缓存
      chartDataCache.time = {};
      // 直接获取新数据
      await fetchTimeChartData(range, timeChartReviewStatus.value);
    } else if (chartType === 'score') {
      scoreChartRange.value = range;
      // 清除缓存
      chartDataCache.score = {};
      // 直接获取新数据
      await fetchScoreChartData(range, scoreChartReviewStatus.value);
    }
  } catch (error) {
    console.error('切换图表范围失败:', error);
    message.error('切换图表范围失败');
  }
};

// 获取级别分布图数据
const fetchLevelChartData = async (range, reviewStatus) => {
  try {
    // 设置请求锁
    pendingRequests.levelDistribution = true;
    
    // 显示加载状态
    levelChartLoading.value = true;
    if (levelChart) levelChart.showLoading();
    
    // 构建请求参数
    const params = { range, reviewStatus };
    
    // 如果是个人视图，添加userId参数
    if (showPersonalProjects.value) {
      try {
        const currentUserId = await getUserId(true);
        if (currentUserId) {
          params.userId = currentUserId;
        }
      } catch (error) {
        console.warn('获取用户ID失败，但仍继续初始化图表:', error);
      }
    }
    
    // 请求级别分布数据
    const response = await getLevelDistribution(params);
    
    // 隐藏加载状态
    levelChartLoading.value = false;
    if (levelChart) levelChart.hideLoading();
    
    if (response && response.code === 200) {
      const levelData = response.data;
      
      // 创建图表配置
      const option = {
        title: {
          text: '项目级别分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: levelData.map(item => item.name)
        },
        series: [
          {
            name: '项目级别',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: levelData
          }
        ]
      };
      
      // 缓存图表配置
      const cacheKey = `${range}_${reviewStatus}_${showPersonalProjects.value ? 'personal' : 'all'}`;
      chartDataCache.level[cacheKey] = option;
      
      // 只有在DOM引用存在时才渲染图表
      if (levelChartRef.value) {
        if (!levelChart) {
          levelChart = echarts.init(levelChartRef.value);
        }
        
        // 设置图表配置
        levelChart.setOption(option);
      } else {
        console.warn('levelChartRef不存在，API请求成功但无法渲染图表');
      }
    } else {
      console.error('获取项目级别分布数据失败:', response);
      message.error('加载级别分布图表数据失败');
    }
  } catch (error) {
    levelChartLoading.value = false;
    if (levelChart) levelChart.hideLoading();
    console.error('获取级别分布图数据失败:', error);
    message.error('加载级别分布图表失败');
  } finally {
    pendingRequests.levelDistribution = false; // 释放请求锁
  }
};

// 获取审核状态分布图数据
const fetchReviewStatusChartData = async (range, reviewStatus) => {
  try {
    // 设置请求锁
    pendingRequests.reviewStatusDistribution = true;

    // 显示加载状态
    reviewStatusChartLoading.value = true;
    if (reviewStatusChart) reviewStatusChart.showLoading();

    // 构建请求参数
    const params = { range };

    // 如果是个人视图，添加userId参数
    if (showPersonalProjects.value) {
      try {
        const currentUserId = await getUserId(true);
        if (currentUserId) {
          params.userId = currentUserId;
        }
      } catch (error) {
        console.warn('获取用户ID失败，但仍继续初始化图表:', error);
      }
    }

    // 请求审核状态分布数据
    const response = await getReviewStatusOverview(params);

    // 隐藏加载状态
    reviewStatusChartLoading.value = false;
    if (reviewStatusChart) reviewStatusChart.hideLoading();

    if (response && response.code === 200) {
      const { reviewed, pending, rejected } = response.data;

      // 创建图表配置
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          left: 'left',
          bottom: 0,
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '审核状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            {
              value: reviewed,
              name: '已审核',
              itemStyle: { color: '#52c41a' }
            },
            {
              value: pending,
              name: '待审核',
              itemStyle: { color: '#faad14' }
            },
            {
              value: rejected,
              name: '已拒绝',
              itemStyle: { color: '#ff4d4f' }
            }
          ]
        }]
      };

      // 缓存图表数据
      const cacheKey = `${range}_${reviewStatus}_${showPersonalProjects.value ? 'personal' : 'all'}`;
      chartDataCache.reviewStatus[cacheKey] = option;

      // 只有在DOM引用存在时才渲染图表
      if (reviewStatusChartRef.value) {
        // 初始化图表
        if (!reviewStatusChart) {
          reviewStatusChart = echarts.init(reviewStatusChartRef.value);
        }

        // 设置图表配置
        reviewStatusChart.setOption(option);
      } else {
        console.warn('reviewStatusChartRef不存在，API请求成功但无法渲染图表');
      }
    } else {
      console.error('获取审核状态分布数据失败:', response);
      message.error('加载审核状态分布图表数据失败');
    }
  } catch (error) {
    reviewStatusChartLoading.value = false;
    if (reviewStatusChart) reviewStatusChart.hideLoading();
    console.error('获取审核状态分布图数据失败:', error);
    message.error('加载审核状态分布图表失败');
  } finally {
    pendingRequests.reviewStatusDistribution = false; // 释放请求锁
  }
};

// 获取时间分布图数据
const fetchTimeChartData = async (range, reviewStatus) => {
  try {
    // 设置请求锁
    pendingRequests.timeDistribution = true;
    
    // 显示加载状态
    timeChartLoading.value = true;
    if (timeChart) timeChart.showLoading();
    
    // 构建请求参数
    const params = { range, reviewStatus };
    
    // 如果是个人视图，添加userId参数
    if (showPersonalProjects.value) {
      try {
        const currentUserId = await getUserId(true);
        if (currentUserId) {
          params.userId = currentUserId;
        }
      } catch (error) {
        console.warn('获取用户ID失败，但仍继续初始化图表:', error);
      }
    }
    
    const response = await getTimeDistribution(params);
    
    // 隐藏加载状态
    timeChartLoading.value = false;
    if (timeChart) timeChart.hideLoading();
    
    if (response && response.code === 200) {
      const timeData = response.data;
      
      // 创建图表配置
      const option = {
        title: {
          text: '项目时间分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: timeData.months,
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '项目数量',
            type: 'bar',
            data: timeData.data,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2378f7' },
                  { offset: 0.7, color: '#2378f7' },
                  { offset: 1, color: '#83bff6' }
                ])
              }
            }
          }
        ]
      };
      
      // 缓存图表数据
      const cacheKey = `${range}_${reviewStatus}_${showPersonalProjects.value ? 'personal' : 'all'}`;
      chartDataCache.time[cacheKey] = option;
      
      // 只有在DOM引用存在时才渲染图表
      if (timeChartRef.value) {
        // 初始化图表
        if (!timeChart) {
          timeChart = echarts.init(timeChartRef.value);
        }
        
        // 设置图表配置
        timeChart.setOption(option);
      } else {
        console.warn('timeChartRef不存在，API请求成功但无法渲染图表');
      }
    } else {
      console.error('获取项目时间分布数据失败:', response);
      message.error('加载时间分布图表数据失败');
    }
  } catch (error) {
    timeChartLoading.value = false;
    if (timeChart) timeChart.hideLoading();
    console.error('获取时间分布图数据失败:', error);
    message.error('加载时间分布图表数据失败');
  } finally {
    pendingRequests.timeDistribution = false; // 释放请求锁
  }
};

// 获取得分分布图数据
const fetchScoreChartData = async (range, reviewStatus) => {
  try {
    // 设置请求锁
    pendingRequests.scoreDistribution = true;
    
    // 显示加载状态
    scoreChartLoading.value = true;
    if (scoreChart) scoreChart.showLoading();
    
    // 构建请求参数
    const params = { range, reviewStatus };
    
    // 如果是个人视图，添加userId参数
    if (showPersonalProjects.value) {
      try {
        const currentUserId = await getUserId(true);
        if (currentUserId) {
          params.userId = currentUserId;
        }
      } catch (error) {
        console.warn('获取用户ID失败，但仍继续初始化图表:', error);
      }
    }
    
    // 请求得分分布数据
    const response = await getScoreDistribution(params);
    
    // 隐藏加载状态
    scoreChartLoading.value = false;
    if (scoreChart) scoreChart.hideLoading();
    
    if (response && response.code === 200) {
      const scoreData = response.data;
      
      // 创建图表配置
      const option = {
        title: {
          text: '项目得分分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: scoreData.map(item => item.name)
        },
        series: [
          {
            name: '得分范围',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: scoreData
          }
        ]
      };
      
      // 缓存图表数据
      const cacheKey = `${range}_${reviewStatus}_${showPersonalProjects.value ? 'personal' : 'all'}`;
      chartDataCache.score[cacheKey] = option;
      
      // 只有在DOM引用存在时才渲染图表
      if (scoreChartRef.value) {
        // 初始化图表
        if (!scoreChart) {
          scoreChart = echarts.init(scoreChartRef.value);
        }
        
        // 设置图表配置
        scoreChart.setOption(option);
      } else {
        console.warn('scoreChartRef不存在，API请求成功但无法渲染图表');
      }
    } else {
      console.error('获取项目得分分布数据失败:', response);
      message.error('加载得分分布图表数据失败');
    }
  } catch (error) {
    scoreChartLoading.value = false;
    if (scoreChart) scoreChart.hideLoading();
    console.error('获取得分分布图数据失败:', error);
    message.error('加载得分分布图表数据失败');
  } finally {
    pendingRequests.scoreDistribution = false; // 释放请求锁
  }
};

// 切换图表审核状态
const changeChartReviewStatus = async (chartType, status) => {
  try {
    // 更新对应图表的审核状态值
    if (chartType === 'level') {
      levelChartReviewStatus.value = status;
      // 清除缓存
      chartDataCache.level = {};
      // 直接获取新数据
      await fetchLevelChartData(levelChartRange.value, status);
    } else if (chartType === 'reviewStatus') {
      reviewStatusChartReviewStatus.value = status;
      // 清除缓存
      chartDataCache.reviewStatus = {};
      // 直接获取新数据
      await fetchReviewStatusChartData(reviewStatusChartRange.value, status);
    } else if (chartType === 'time') {
      timeChartReviewStatus.value = status;
      // 清除缓存
      chartDataCache.time = {};
      // 直接获取新数据
      await fetchTimeChartData(timeChartRange.value, status);
    } else if (chartType === 'score') {
      scoreChartReviewStatus.value = status;
      // 清除缓存
      chartDataCache.score = {};
      // 直接获取新数据
      await fetchScoreChartData(scoreChartRange.value, status);
    }
  } catch (error) {
    console.error('切换图表审核状态失败:', error);
    message.error('切换图表审核状态失败');
  }
};

// 项目参与人表格列定义
const participantColumns = [
  { title: '参与人', dataIndex: 'displayName', key: 'displayName' },
  { 
    title: '分配比例', 
    dataIndex: 'allocationRatio', 
    key: 'allocationRatio',
    customRender: ({ text }) => `${parseFloat(text).toFixed(1)}%`
  },
  { 
    title: '负责人', 
    dataIndex: 'isLeader', 
    key: 'isLeader',
    customRender: ({ text }) => text ? '是' : '否'
  },
  {
    title: '操作',
    key: 'action',
    customRender: ({ record, index }) => {
      return h('div', [
        h('a-button', {
          type: 'link',
          onClick: () => toggleLeader(index),
          disabled: record.isLeader
        }, record.isLeader ? '已设为负责人' : '设为负责人'),
        h('a-button', {
          type: 'link',
          danger: true,
          onClick: () => handleRemoveMember(index)
        }, '移除')
      ]);
    }
  }
];

// 处理提交人搜索
const handleSubmitterSearch = (value) => {
  // 清除之前的延时搜索
  if (submitterSearchTimeout.value) {
    clearTimeout(submitterSearchTimeout.value);
  }
  
  // 如果搜索词为空，则清空选项列表并返回
  if (!value || value.trim() === '') {
    submitterOptions.value = [];
    return;
  }
  
  // 设置搜索中状态
  submitterSearchLoading.value = true;
  
  // 延时搜索，避免频繁请求
  submitterSearchTimeout.value = setTimeout(() => {
    // 调用用户搜索API
    usersSearch({ keyword: value.trim() })
      .then((res) => {
        // 处理API返回结果
        if (res && res.code === 200) {
          // 将返回的用户数据转换为下拉选项格式
          if (Array.isArray(res.data)) {
            submitterOptions.value = res.data.map(item => ({
              value: item.id, // 用户ID作为选项值
              label: `${item.nickname || item.username || '未知用户'} (${item.studentNumber || '无工号'})`, // 优先显示昵称
              ...item // 保留原始用户数据，方便后续使用
            }));
          } else if (res.data && Array.isArray(res.data.list)) {
            // 如果返回的是包含list属性的对象
            submitterOptions.value = res.data.list.map(item => ({
              value: item.id,
              label: `${item.nickname || item.username || '未知用户'} (${item.studentNumber || '无工号'})`,
              ...item
            }));
          } else {
            // 如果数据结构不是预期的格式
            console.error('搜索提交人返回的数据结构异常:', res.data);
            submitterOptions.value = [];
          }
        } else {
          submitterOptions.value = [];
          console.warn('用户搜索返回数据格式不正确:', res);
        }
      })
      .catch((error) => {
        console.error('搜索提交人失败:', error);
        message.error('搜索提交人失败');
        submitterOptions.value = [];
      })
      .finally(() => {
        submitterSearchLoading.value = false;
      });
  }, 500); // 500ms的防抖延迟
};

// 添加请求锁，用于防止重复请求
const pendingRequests = reactive({
  levelDistribution: false,
  reviewStatusDistribution: false,
  timeDistribution: false,
  scoreDistribution: false,
  projects: false
});

// 展示审核详情
const showReviewDetails = (record) => {
  let statusColor = '';
  let statusText = '';
  
  if (record.ifReviewer === 1) {
    statusColor = '#52c41a';
    statusText = '已审核';
  } else if (record.ifReviewer === 0) {
    statusColor = '#f5222d';
    statusText = '已拒绝';
  } else {
    statusColor = '#faad14';
    statusText = '待审核';
  }
  
  Modal.info({
    title: '审核详情',
    content: h('div', {}, [
      h('p', {}, [
        h('span', { style: { fontWeight: 'bold' } }, '审核状态：'),
        h('span', { style: { color: statusColor } }, statusText)
      ]),
      record.reviewComment ? h('p', {}, [
        h('span', { style: { fontWeight: 'bold' } }, '审核意见：'),
        h('span', {}, record.reviewComment || '无')
      ]) : null,
      record.reviewerId ? h('p', {}, [
        h('span', { style: { fontWeight: 'bold' } }, '审核人：'),
        h('span', {}, record.reviewer?.nickname || record.reviewer?.username || record.reviewerId || '未知')
      ]) : null,
      record.reviewedAt ? h('p', {}, [
        h('span', { style: { fontWeight: 'bold' } }, '审核时间：'),
        h('span', {}, record.reviewedAt)
      ]) : null
    ]),
    okText: '关闭'
  });
};

// 负责人搜索相关状态
const leaderSearchLoading = ref(false);
const leaderOptions = ref([]);
const leaderSearchTimeout = ref(null);

// 处理文件移除
const handleFileRemove = (file) => {
  console.log('移除文件:', file);
  
  // 移除文件，添加动画效果
  message.loading('正在移除文件...', 0.5);
  
  // 尝试从file对象获取文件ID
  let fileId = null;
  if (file.response && file.response.id) {
    fileId = file.response.id;
  } else if (file.response && file.response.fileInfo && file.response.fileInfo.id) {
    fileId = file.response.fileInfo.id;
  } else if (file.uid && file.uid.toString().length > 10) {
    fileId = file.uid;
  }
  
  // 尝试从file对象获取文件路径
  let filePath = null;
  if (file.filePath) {
    filePath = file.filePath;
  } else if (file.response && file.response.fileInfo && file.response.fileInfo.filePath) {
    filePath = file.response.fileInfo.filePath;
  }
  
  console.log('要移除的文件ID:', fileId);
  console.log('要移除的文件路径:', filePath);
  
  // 如果是编辑模式且文件有ID，记录要删除的文件ID
  if (editMode.value && fileId) {
    if (!formState.deletedFileIds) {
      formState.deletedFileIds = [];
    }
    
    // 检查是否已经记录过这个ID，避免重复添加
    if (!formState.deletedFileIds.includes(fileId)) {
      formState.deletedFileIds.push(fileId);
      console.log('文件已标记为需要删除，ID:', fileId);
    }
  }
  
  // 从formState.fileIds中移除
  if (formState.fileIds && fileId) {
    const index = formState.fileIds.indexOf(fileId);
    if (index !== -1) {
      formState.fileIds.splice(index, 1);
      console.log('已从formState.fileIds中移除文件ID:', fileId);
    }
  }
  
  // 从formState.attachmentUrl中移除
  if (formState.attachmentUrl && filePath) {
    const index = formState.attachmentUrl.indexOf(filePath);
    if (index !== -1) {
      formState.attachmentUrl.splice(index, 1);
      console.log('已从formState.attachmentUrl中移除文件路径:', filePath);
    }
  }
  
  // 从fileList中移除文件
  const index = fileList.value.indexOf(file);
  const newFileList = [...fileList.value];
  if (index !== -1) {
    newFileList.splice(index, 1);
    fileList.value = newFileList;
    console.log('文件已从fileList中移除');
  }
  
  console.log('文件移除后的状态:');
  console.log('- formState.fileIds:', formState.fileIds);
  console.log('- formState.attachmentUrl:', formState.attachmentUrl);
  console.log('- formState.deletedFileIds:', formState.deletedFileIds);
  
  // 返回false以阻止默认行为，我们已手动处理移除逻辑
  return false;
};

// 在适当位置添加文件列表表格列定义
const fileColumns = [
  {
    title: '文件名',
    key: 'fileName',
    ellipsis: true
  },
  {
    title: '大小',
    key: 'fileSize',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 280
  }
];

// 在适当位置添加文件大小格式化函数
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 在适当位置添加文件删除函数
const confirmDeleteFile = async (file) => {
  try {
    console.log('准备删除文件:', file);
    
    // 从多个可能的位置获取文件ID
    let fileId = null;
    if (file.response && file.response.file && file.response.file.id) {
      fileId = file.response.file.id;
    } else if (file.response && file.response.id) {
      fileId = file.response.id;
    } else if (file.response && file.response.fileInfo && file.response.fileInfo.id) {
      fileId = file.response.fileInfo.id;
    } else if (file.data && file.data.id) {
      fileId = file.data.id;
    } else if (file.uid && file.uid.toString().length > 10) {
      fileId = file.uid;
    }
    
    console.log('获取到的文件ID:', fileId);
    
    if (!fileId) {
      message.error('无法获取文件ID，删除失败');
      return;
    }
    
    message.loading('正在删除文件...');
    
    // 调用后端API删除文件
    const response = await deleteFile(fileId);
    
    if (response && response.code === 200) {
      // 从本地列表中移除文件
      handleFileRemove(file);
      message.success('文件已成功删除');
    } else {
      message.error(response?.message || '删除文件失败');
    }
  } catch (error) {
    console.error('删除文件时发生错误:', error);
    message.error('删除文件失败: ' + (error.message || '未知错误'));
  }
};


// 导出用户科研项目得分统计数据
const exporting = ref(false);

// 修改导出函数实现
const exportUserScoreData = async () => {
  try {
    exporting.value = true;
    // 使用getAllUsersTotalScore接口获取所有数据
    const res = await getAllUsersTotalScore({
      range: userScoreChartRange.value,
      reviewStatus: userScoreSearchParams.reviewStatus,
      // 设置大值获取所有数据
      page: 1,
      pageSize: 10000,
      projectId: userScoreSearchParams.projectId,
      nickname: userScoreSearchParams.nickname
    });

    if (!res || !res.data || !res.data.list || !Array.isArray(res.data.list) || res.data.list.length === 0) {
      message.error('导出失败：获取数据为空');
      return;
    }

    // 准备Excel数据
    const data = res.data.list.map(item => {
      return {
        '排名': item.rank || '-',
        '姓名': item.nickname || '未知',
        '学/工号': item.studentNumber || '-',
        '项目总数': item.projectCount || 0,
        '负责项目数': item.leaderProjectCount || 0,
        '总分': parseFloat(item.totalScore || 0).toFixed(2)
      };
    });

    // 导出数据
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '用户科研项目得分统计');

    // 设置列宽
    const columnWidths = [
      { wch: 8 }, // 排名
      { wch: 36 }, // 用户ID
      { wch: 12 }, // 姓名
      { wch: 12 }, // 学/工号
      { wch: 10 }, // 项目总数
      { wch: 12 }, // 负责项目数
      { wch: 10 }, // 总分
    ];
    worksheet['!cols'] = columnWidths;

    // 生成文件名
    const fileName = `用户科研项目得分统计_${dayjs().format('YYYY-MM-DD')}.xlsx`;

    // 下载文件
    XLSX.writeFile(workbook, fileName);
    message.success('导出成功');
  } catch (error) {
    console.error('导出用户科研项目得分统计数据失败:', error);
    message.error('导出用户科研项目得分统计数据失败');
  } finally {
    exporting.value = false;
  }
};

// 导出当前筛选的科研项目列表
const exportCurrentProjects = async () => {
  try {
    exporting.value = true;
    
    // 构建查询参数，与搜索相同但设置isExport为true
    const exportParams = {
      ...queryParams,
      isExport: true // 告知后端这是导出操作
    };
    
    // 调用API获取所有符合筛选条件的数据
    const response = await getProjects(exportParams);
    
    if (!response || !response.data || !response.data.list || !Array.isArray(response.data.list) || response.data.list.length === 0) {
      message.error('导出失败：未找到符合条件的记录');
      return;
    }
    
    // 准备Excel数据
    const data = response.data.list.map(item => {
      // 从level中获取分数
      const level = levelOptions.value.find(option => option.id === item.levelId);
      const score = level ? level.score : (item.score || 0);
      
      return {
        '项目ID': item.projectId || item.id,
        '项目名称': item.name,
        '项目级别': formatLevel(item.levelId),
        '项目类型': item.type,
        '负责人': formatLeader(item),
        '成员': formatMembersWithAllocation(item.participants, item, true),
        '开始日期': item.startDate,
        '获批日期': item.approvalDate,
        '结束日期': item.endDate,
        '项目得分': isProjectInScoreRange(item) ? parseFloat(score).toFixed(2) : '不计分',
        '审核状态': item.ifReviewer === true ? '已审核' : (item.ifReviewer === false ? '已拒绝' : '待审核'),
        '是否在统计范围内': item.isInTimeRange ? '是' : '否',
        '创建时间': formatDateTime(item.createdAt)
      };
    });
    
    // 导出数据
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '科研项目列表');
    
    // 设置列宽
    const columnWidths = [
      { wch: 36 }, // 项目ID
      { wch: 30 }, // 项目名称
      { wch: 15 }, // 项目级别
      { wch: 15 }, // 项目类型
      { wch: 12 }, // 负责人
      { wch: 30 }, // 成员
      { wch: 12 }, // 开始日期
      { wch: 12 }, // 获批日期
      { wch: 12 }, // 结束日期
      { wch: 10 }, // 项目得分
      { wch: 10 }, // 审核状态
      { wch: 15 }, // 是否在统计范围内
      { wch: 20 }  // 创建时间
    ];
    worksheet['!cols'] = columnWidths;
    
    // 生成文件名
    const fileName = `科研项目列表_${dayjs().format('YYYY-MM-DD')}.xlsx`;
    
    // 下载文件
    XLSX.writeFile(workbook, fileName);
    message.success('导出成功');
  } catch (error) {
    console.error('导出科研项目数据失败:', error);
    message.error('导出科研项目数据失败');
  } finally {
    exporting.value = false;
  }
};

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return '';
  return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};

// 导入文件处理工具函数
import { previewFile as utilPreviewFile, downloadFile as utilDownloadFile, deleteFile as utilDeleteFile, uploadFile as utilUploadFile } from '@/utils/others';

// 文件预览函数
const previewFile = (file) => {
  console.log("file==",file);
  utilPreviewFile(file);
};

// 文件下载函数
const downloadFile = (file) => {
  utilDownloadFile(file);
};

// 添加用户项目详情分页处理函数
const handleUserDetailsPaginationChange = async (pagination, filters, sorter) => {
  userDetailsLoading.value = true;
  // 只更新必要的分页属性，保持其他配置不变
  userDetailsPagination.current = pagination.current;
  userDetailsPagination.pageSize = pagination.pageSize;
  // 确保其他配置属性保持不变
  userDetailsPagination.showSizeChanger = true;

  userDetailsPagination.pageSizeOptions = ['10', '20', '50'];
  userDetailsPagination.showTotal = total => `共 ${total} 条`;

  try {
    // 重新获取数据
    const response = await getUserProjectDetails({
      userId: selectedUserId.value,
      range: userScoreChartRange.value,
      reviewStatus: userScoreSearchParams.reviewStatus,
      page: userDetailsPagination.current,
      pageSize: userDetailsPagination.pageSize
    });
    
    if (response && response.code === 200) {
      userProjectDetails.value = response.data.list.map(item => ({
        ...item,
        allocationProportion: item.userAllocationRatio,
        levelName: item.level ? item.level.levelName : '',
        level: item.level,
        userScore: parseFloat(item.userScore || 0),
        totalScore: parseFloat(item.totalScore || 0),
        reviewStatus: item.ifReviewer,
        reviewStatusText: item.reviewStatusText || getReviewStatusDesc(item.ifReviewer)
      }));
    } else {
      message.error(response?.message || '获取用户项目详情失败');
    }
  } catch (error) {
    console.error('获取用户项目详情分页数据失败:', error);
    message.error('获取用户项目详情分页数据失败');
  } finally {
    userDetailsLoading.value = false;
  }
};

// 初始化所有图表
const initAllCharts = async () => {
  // 获取全部图表数据
  // 使用新的获取数据方法，而不是调用initXxxChart
  await Promise.all([
    fetchLevelChartData(levelChartRange.value, levelChartReviewStatus.value),
    fetchReviewStatusChartData(reviewStatusChartRange.value, reviewStatusChartReviewStatus.value),
    fetchTimeChartData(timeChartRange.value, timeChartReviewStatus.value),
    fetchScoreChartData(scoreChartRange.value, scoreChartReviewStatus.value)
  ]);
};

// 获取项目级别列表
const fetchLevelOptions = async () => {
  try {
    levelLoading.value = true;
    const response = await getAllResearchProjectsLevels();
    if (response && response.code === 200) {
      levelOptions.value = Array.isArray(response.data) ? response.data : [];
    } else {
      message.error(response?.message || '获取项目级别列表失败');
      // 确保总是设置为数组，即使请求失败
      levelOptions.value = [];
    }
  } catch (error) {
    console.error('获取项目级别列表失败:', error);
    message.error('获取项目级别列表失败');
    // 确保总是设置为数组，即使发生错误
    levelOptions.value = [];
  } finally {
    levelLoading.value = false;
  }
};

// Excel文件上传前检查
const beforeExcelUpload = (file) => {
  // 检查文件类型
  const isExcel = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
    file.type === 'application/vnd.ms-excel' || 
    file.name.endsWith('.csv');
  
  if (!isExcel) {
    message.error('只能上传Excel文件 (.xlsx, .xls, .csv)!');
    return false;
  }
  
  // 文件大小限制：20MB
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    message.error('文件大小不能超过20MB!');
    return false;
  }
  
  console.log('Excel文件检查通过，准备开始转换');
  return true; // 返回true允许上传
};

// 处理Excel到JSON转换
const handleExcelToJsonConvert = async ({ file }) => {
  // 创建唯一的消息ID
  const messageKey = `excel_convert_${Date.now()}`;
  
  try {
    convertingExcel.value = true;
    
    // 显示正在处理的提示，使用key可以后续精确销毁
    message.loading({ 
      content: '正在解析Excel文件，请稍候...', 
      key: messageKey,
      duration: 0 // 不自动消失
    });
    
    // 获取文件基本名（不含扩展名）
    const fileName = file.name.split('.').slice(0, -1).join('.') || 'project_export';
    
    // 默认配置：表头在第3行 (从0开始计数，索引为2)
    const options = {
      headerRow: 3, // 第3行作为表头（索引从0开始，所以是2）
      sheetName: null // 默认使用第一个工作表
    };
    
    console.log('开始转换Excel文件:', fileName);
    
    // 导入excelToResearchProjectsJson函数
    const { excelToResearchProjectsJson } = await import('@/utils/fileUtils');
    
    // 转换Excel为JSON
    const projectsResult = await excelToResearchProjectsJson(file, options);
    
    // 更新消息为处理完成
    message.loading({ 
      content: '数据解析完成...', 
      key: messageKey,
      duration: 1 // 1秒后自动消失
    });
    
    // 从返回结果中获取项目数据
    const projectsData = projectsResult.data || [];
    
    if (projectsData.length === 0) {
      message.warning('未从Excel文件中提取到任何有效数据', 3);
      return;
    }
    
    console.log(`解析成功，共 ${projectsData.length} 条项目数据`);
    
    // 处理JSON数据，准备导入
    // 进一步处理每个项目，确保数据格式符合后端API的要求
    const optimizedData = projectsData.map(project => {
      // 移除可能的undefined值
      const cleanProject = {};
      
      // 只保留有值的字段
      Object.entries(project).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          cleanProject[key] = value;
        }
      });
      
      // 确保字段格式符合API要求
      if (cleanProject.isUniversityFirstUnit === true) {
        cleanProject.isUniversityFirstUnit = '是';
      }
      
      if (cleanProject.isCollegeFirstUnit === true) {
        cleanProject.isCollegeFirstUnit = '是';
      }
      
      // 其他处理逻辑...
      
      return cleanProject;
    });
    
    // 保存最近转换的数据以便直接导入
    lastConvertedExcelData.value = optimizedData;
    
    console.log(`Excel数据处理完成，共 ${optimizedData.length} 条数据`);
    
    // 弹出确认框，提供下载与直接导入两个选项
    Modal.confirm({
      title: 'Excel转换成功',
      content: `已成功从Excel中提取${optimizedData.length}条项目记录。请选择操作方式：`,
      okText: '直接导入系统',
      cancelText: '下载JSON文件',
      onOk: () => {
        // 直接导入数据
        handleDirectImport(optimizedData);
      },
      onCancel: async () => {
        // 下载JSON文件
        try {
          // 创建Blob对象
          const blob = new Blob([JSON.stringify(optimizedData, null, 2)], { type: 'application/json' });
          
          // 创建下载链接
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${fileName}.json`;
          
          // 触发下载
          document.body.appendChild(link);
          link.click();
          
          // 清理
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
          
          message.success(`已成功下载JSON文件`, 3);
        } catch (downloadErr) {
          console.error('下载JSON文件失败:', downloadErr);
          message.error('下载失败，请尝试使用"直接导入"功能', 5);
        }
      }
    });
    
  } catch (error) {
    console.error('Excel转JSON处理失败:', error);
    // 确保任何loading消息都被清除
    message.destroy(messageKey);
    message.error(`Excel文件处理失败: ${error.message || '未知错误'}`, 5);
  } finally {
    // 最后确保状态复位
    convertingExcel.value = false;
    
    // 延迟0.5秒后销毁所有可能存在的消息，确保不会有消息一直存在
    setTimeout(() => {
      message.destroy(messageKey);
    }, 500);
  }
};

// 处理JSON文件上传前检查
const beforeJsonUpload = (file) => {
  // 检查文件类型
  const isJson = file.type === 'application/json' || file.name.endsWith('.json');
  
  if (!isJson) {
    message.error('只能上传JSON文件!');
    return false;
  }
  
  // 文件大小限制：20MB
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    message.error('文件大小不能超过20MB!');
    return false;
  }
  
  // 显示处理提示
  message.loading({
    content: '准备处理JSON文件...',
    key: 'jsonUpload',
    duration: 2
  });
  
  return true; // 返回true允许上传
};

// 处理JSON导入
const handleJsonImport = async ({ file, onSuccess, onError }) => {
  try {
    // 创建唯一的消息ID
    const messageKey = `json_import_${Date.now()}`;
    
    // 显示正在处理的提示
    message.loading({ 
      content: '正在解析JSON文件，请稍候...', 
      key: messageKey,
      duration: 0 // 不自动消失
    });
    
    // 读取文件内容
    const reader = new FileReader();
    
    reader.onload = async (e) => {
      try {
        // 解析JSON内容
        const jsonContent = JSON.parse(e.target.result);
        
        // 更新消息
        message.loading({ 
          content: '数据解析完成...', 
          key: messageKey,
          duration: 1
        });
        
        // 检查数据
        if (!Array.isArray(jsonContent) || jsonContent.length === 0) {
          message.error('JSON文件格式不正确或没有包含有效数据', 3);
          onError(new Error('JSON文件格式不正确'));
          return;
        }
        
        console.log(`成功解析JSON文件，包含 ${jsonContent.length} 条记录`);
        
        // 弹出确认框
        Modal.confirm({
          title: '确认导入',
          content: `已解析出 ${jsonContent.length} 条项目记录，是否导入系统？`,
          okText: '开始导入',
          cancelText: '取消',
          onOk: () => {
            // 开始导入
            handleDirectImport(jsonContent);
            onSuccess();
          },
          onCancel: () => {
            message.info('已取消导入');
            onError(new Error('用户取消导入'));
          }
        });
      } catch (parseError) {
        console.error('解析JSON文件失败:', parseError);
        message.error(`解析JSON文件失败: ${parseError.message || '格式错误'}`, 3);
        onError(parseError);
      } finally {
        // 确保消息被清除
        setTimeout(() => {
          message.destroy(messageKey);
        }, 500);
      }
    };
    
    reader.onerror = (error) => {
      console.error('读取JSON文件失败:', error);
      message.error('读取JSON文件失败', 3);
      onError(error);
      message.destroy(messageKey);
    };
    
    // 开始读取文件
    reader.readAsText(file);
    
  } catch (error) {
    console.error('JSON导入处理失败:', error);
    message.error(`JSON文件处理失败: ${error.message || '未知错误'}`, 3);
    onError(error);
  }
};

// 处理直接导入
const handleDirectImport = async (jsonData) => {
  if (!jsonData || !Array.isArray(jsonData) || jsonData.length === 0) {
    message.warning('没有可导入的数据');
    return;
  }
  
  try {
    // 显示导入中状态
    importInProgress.value = true;
    
    // 处理JSON数据为可预览的格式
    await processJsonData(jsonData);
    
    // 显示预览界面后，导入按钮不应该继续显示加载状态
    importInProgress.value = false;
    
  } catch (error) {
    console.error('处理导入数据失败:', error);
    message.error(`处理导入数据失败: ${error.message || '未知错误'}`);
    importInProgress.value = false;
  }
};

// 用户ID检查函数
const checkUserIds = async (dataList) => {
  try {
    const checkPromises = dataList.map(async (item, index) => {
      // 这里可以根据需要添加用户ID检查逻辑
      // 如果有需要检查用户ID的字段(例如userId, leaderId等)
      
      // 模拟检查(实际应调用API检查)
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
      
      // 更新检查状态
      item.userIdCheckStatus = 'success'; // 可以是 'success', 'warning', 'error'
      
      // 更新统计
      userIdCheckResults.found++;
      
      return item;
    });
    
    // 等待所有检查完成
    await Promise.all(checkPromises);
    
  } catch (error) {
    console.error('检查用户ID失败:', error);
    message.error('检查用户ID失败，请检查数据格式');
  }
};

// 显示行详情
const showRowDetails = (record) => {
  Modal.info({
    title: '数据详情',
    content: h('div', { style: 'max-height: 500px; overflow-y: auto;' }, [
      h('pre', null, JSON.stringify(record, null, 2))
    ]),
    width: 800
  });
};

// 取消导入预览
const handleCancelImportPreview = () => {
  importPreviewVisible.value = false;
  importPreviewData.value = [];
};

// 开始导入
const handleStartImport = async () => {
  if (importPreviewData.value.length === 0) {
    message.warning('没有可导入的数据');
    return;
  }
  
  try {
    // 设置导入中状态
    importInProgress.value = true;
    
    // 重置导入结果
    Object.assign(importResults, {
      total: importPreviewData.value.length,
      success: 0,
      failed: 0,
      details: [],
      current: 0 // 重置当前进度
    });
    
    // 尝试获取当前用户ID作为默认提交人
    let currentUserId = null;
    try {
      currentUserId = await getUserId(true);
    } catch (error) {
      console.warn('获取当前用户ID失败，将使用后端默认处理:', error);
    }
    
    // 逐条导入数据
    for (let i = 0; i < importPreviewData.value.length; i++) {
      // 更新当前进度
      importResults.current = i + 1;
      
      const previewItem = importPreviewData.value[i];
      // 确保originalData存在，如果不存在则使用previewItem本身
      const originalData = previewItem.rawData || previewItem;
      
      try {
        // 创建一个进度消息
        const progressKey = `import_progress_${Date.now()}`;
        message.loading({
          content: `正在导入第 ${i + 1}/${importPreviewData.value.length} 条数据...`,
          key: progressKey,
          duration: 0
        });
        
        // 准备安全访问函数，避免undefined错误
        const safeGet = (obj, path, defaultValue = '') => {
          if (!obj) return defaultValue;
          const value = obj[path];
          return value !== undefined && value !== null ? value : defaultValue;
        };
        
        // 根据API要求构造导入数据
        const formData = new FormData();
        
        // 1. 添加基本项目信息
        formData.append('name', safeGet(originalData, 'name', `项目 ${i + 1}`));
        formData.append('projectId', safeGet(originalData, 'projectId'));
        formData.append('projectIssuingDepartment', safeGet(originalData, 'projectIssuingDepartment', '未知部门'));
        formData.append('type', safeGet(originalData, 'type', '未知类型'));
        
        // 2. 添加日期信息
        formData.append('approvalDate', safeGet(originalData, 'approvalDate', '2023-01-01'));
        formData.append('startDate', safeGet(originalData, 'startDate', '2023-01-01'));
        formData.append('endDate', safeGet(originalData, 'endDate', '2023-12-31'));
        
        // 3. 添加经费信息
        formData.append('fundingAmount', safeGet(originalData, 'fundingAmount', 0));
        
        // 4. 添加级别ID - 需要从级别名称查找对应的ID
        if (originalData.levelName) {
          const matchedLevel = levelOptions.value.find(
            level => level.levelName === originalData.levelName
          );
          
          if (matchedLevel) {
            formData.append('levelId', matchedLevel.id);
          } else if (originalData.levelId) {
            formData.append('levelId', originalData.levelId);
          } else if (levelOptions.value.length > 0) {
            // 如果没找到匹配的级别，使用第一个级别作为默认值
            formData.append('levelId', levelOptions.value[0].id);
            console.warn(`未找到级别 "${originalData.levelName}" 对应的ID，使用默认级别: ${levelOptions.value[0].levelName}`);
          } else {
            throw new Error(`未找到级别 "${originalData.levelName}" 对应的ID，且没有可用的默认级别`);
          }
        } else if (originalData.levelId) {
          formData.append('levelId', originalData.levelId);
        } else if (levelOptions.value.length > 0) {
          // 如果没有级别信息，使用第一个级别作为默认值
          formData.append('levelId', levelOptions.value[0].id);
          console.warn(`缺少级别信息，使用默认级别: ${levelOptions.value[0].levelName}`);
        } else {
          throw new Error('项目级别不能为空，且没有可用的默认级别');
        }
        
        // 5. 添加单位信息
        const isUniversityFirst = safeGet(originalData, 'isUniversityFirstUnit', true);
        const isCollegeFirst = safeGet(originalData, 'isCollegeFirstUnit', true);
        formData.append('isUniversityFirstUnit', isUniversityFirst === '是' || isUniversityFirst === true ? '1' : '0');
        formData.append('isCollegeFirstUnit', isCollegeFirst === '是' || isCollegeFirst === true ? '1' : '0');
        
        // 6. 添加备注和状态
        formData.append('remark', safeGet(originalData, 'remark'));
        formData.append('status', safeGet(originalData, 'status', '1'));
        
        // 7. 添加提交人ID - 删除这行，将在确定主持人ID后添加
        // formData.append('submitterId', currentUserId || '');
        
        // 8. 处理参与者 - 找出主持人
        const participants = originalData.participants || [];
        let leader = participants.find(p => p.isLeader);
        
        // 如果没有明确标记的主持人，使用第一个参与者作为主持人
        if (!leader && participants.length > 0) {
          leader = participants[0];
        }
        
        let leaderUserId = null;
        let leaderUserInfo = null;
        
        // 直接从原始数据中查找leaderName
        const projectLeaderName = originalData.leaderName || (leader && leader.name) || '';
        
        if (projectLeaderName) {
          console.log(`项目"${originalData.name}"的主持人为: "${projectLeaderName}", 开始查询用户ID`);
          
          try {
            // 直接使用projectLeaderName查找
            const userResult = await usersSearch({ keyword: projectLeaderName });
            if (userResult && userResult.code === 200 && userResult.data.length > 0) {
              leaderUserId = userResult.data[0].id;
              leaderUserInfo = userResult.data[0];
              console.log(`成功找到主持人"${projectLeaderName}"的用户ID: ${leaderUserId}`);
            } else {
              console.warn(`未找到主持人"${projectLeaderName}"对应的用户ID`);
            }
          } catch (error) {
            console.error(`查询主持人"${projectLeaderName}"用户ID失败:`, error);
          }
        } else if (leader && leader.studentNumber) {
          // 如果没有名字但有工号，通过工号查找
          console.log(`项目"${originalData.name}"没有主持人名称，尝试通过工号"${leader.studentNumber}"查找`);
          
          try {
            const userResult = await usersSearch({ keyword: leader.studentNumber });
            if (userResult && userResult.code === 200 && userResult.data.length > 0) {
              leaderUserId = userResult.data[0].id;
              leaderUserInfo = userResult.data[0];
              console.log(`成功通过工号"${leader.studentNumber}"找到用户ID: ${leaderUserId}`);
            } else {
              console.warn(`未找到工号"${leader.studentNumber}"对应的用户ID`);
            }
          } catch (error) {
            console.error(`查询工号"${leader.studentNumber}"用户ID失败:`, error);
          }
        }
        
        // 如果没有找到主持人ID，使用当前用户作为主持人
        if (!leaderUserId) {
          console.warn(`无法找到项目"${originalData.name}"的主持人用户ID，将使用当前登录用户作为主持人`);
          leaderUserId = currentUserId;
        }
        
        // 设置提交人为主持人 - 删除条件逻辑，直接设置一次
        // 如果leaderUserId是数组（这种情况不应该发生），取第一个元素
        const finalSubmitterId = Array.isArray(leaderUserId) ? leaderUserId[0] : (leaderUserId || currentUserId || '');
        
        // 仅添加一次submitterId
        formData.append('submitterId', finalSubmitterId);
        console.log(`设置项目"${originalData.name}"的提交人ID为: ${finalSubmitterId}`);
        
        if (leader) {
          // 8.1 添加主持人信息 - userId和userAllocationProportion是API必需的字段
          // 使用找到的用户ID
          formData.append('userId', finalSubmitterId);
          
          // 分配比例 - 直接使用原始值，不做转换
          const allocationRatio = leader.allocationRatio || 0;
          formData.append('userAllocationProportion', allocationRatio.toString());
        } else {
          // 没有主持人信息，使用默认值
          formData.append('userId', finalSubmitterId);
          formData.append('userAllocationProportion', '1.00');
        }
        
        // 8.2 处理其他参与者 - members数组
        const otherParticipants = participants.filter(p => !p.isLeader);
        
        if (otherParticipants.length > 0) {
          // 准备members数组，包含id, allocationProportion, participantRank
          const membersArray = await Promise.all(otherParticipants.map(async (participant, index) => {
            // 查找参与者的用户ID
            let participantUserId = null;
            
            try {
              // 优先通过姓名查找
              if (participant.name) {
                const userResult = await usersSearch({ keyword: participant.name });
                if (userResult && userResult.code === 200 && userResult.data.length > 0) {
                  participantUserId = userResult.data[0].id;
                }
              }
              
              // 如果姓名查找失败，再尝试通过工号查找
              if (!participantUserId && participant.studentNumber) {
                const userResult = await usersSearch({ keyword: participant.studentNumber });
                if (userResult && userResult.code === 200 && userResult.data.length > 0) {
                  participantUserId = userResult.data[0].id;
                }
              }
            } catch (error) {
              console.warn(`查找参与者 ${participant.name || '未知'} 的用户ID失败:`, error);
            }
            
            // 如果找不到用户ID，使用姓名作为标识
            const id = participantUserId || participant.name || `参与者${index+2}`;
            
            // 分配比例 - 直接使用原始值，不做转换
            const allocationRatio = participant.allocationRatio || 0;
            
            // 返回参与者对象
            return {
              id: id,
              allocationProportion: allocationRatio.toString(),
              participantRank: participant.participantRank || (index + 2)
            };
          }));
          
          // 添加members数组到formData
          formData.append('members', JSON.stringify(membersArray));
        }
        
        // 9. 调用API导入项目
        console.log('准备导入项目:', formData);
        const response = await addResearchProject(formData);
        
        // 获取项目名称用于显示
        const projectName = safeGet(originalData, 'name', `项目 ${i + 1}`);
        
        // 根据响应更新状态
        if (response && response.code === 200) {
          importResults.success++;
          importResults.details.push({
            projectName: projectName,
            status: 'success',
            message: '导入成功'
          });
          
          // 更新进度消息
          message.success({
            content: `第 ${i + 1} 条导入成功`,
            key: progressKey,
            duration: 1
          });
        } else {
          importResults.failed++;
          importResults.details.push({
            projectName: projectName,
            status: 'error',
            message: response?.message || response?.msg || '导入失败'
          });
          
          // 更新进度消息
          message.error({
            content: `第 ${i + 1} 条导入失败: ${response?.message || response?.msg || '未知错误'}`,
            key: progressKey,
            duration: 2
          });
        }
        
        // 等待一小段时间，避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 300));
        
      } catch (itemError) {
        console.error(`导入第 ${i + 1} 条数据失败:`, itemError);
        
        importResults.failed++;
        importResults.details.push({
          projectName: safeGet(originalData, 'name', `项目 ${i + 1}`),
          status: 'error',
          message: itemError.message || '导入过程中发生错误'
        });
        
        // 显示错误消息
        message.error(`第 ${i + 1} 条导入失败: ${itemError.message || '未知错误'}`, 2);
        
        // 继续下一条，不中断整体导入
        continue;
      }
    }
    
    // 显示导入结果
    importPreviewVisible.value = false;
    importResultVisible.value = true;
    
    // 刷新数据
    fetchData();
    
  } catch (error) {
    console.error('批量导入数据失败:', error);
    message.error(`批量导入失败: ${error.message || '未知错误'}`);
  } finally {
    // 重置状态
    importInProgress.value = false;
    // 清除所有可能的loading消息
    message.destroy();
  }
};

// 添加或更新导出失败记录的函数
const exportFailedRecords = () => {
  try {
    // 检查是否有失败记录
    const failedRecords = importResults.details.filter(item => item.status === 'error');
    
    if (failedRecords.length === 0) {
      message.info('没有失败记录需要导出');
      return;
    }
    
    // 准备导出数据 
    const exportData = failedRecords.map(item => {
      // 尝试找到原始记录以获取更多信息
      const originalRecordIndex = importPreviewData.value.findIndex(
        preview => preview.name === item.projectName
      );
      
      const originalData = originalRecordIndex >= 0 
        ? (importPreviewData.value[originalRecordIndex].rawData || importPreviewData.value[originalRecordIndex])
        : {};
        
      return {
        '项目名称': item.projectName,
        '项目编号': originalData.projectId || '',
        '项目级别': originalData.levelName || '',
        '项目类型': originalData.type || '',
        '下达部门': originalData.projectIssuingDepartment || '',
        '主持人': originalData.leaderName || '',
        '开始日期': originalData.startDate || '',
        '获批日期': originalData.approvalDate || '',
        '结束日期': originalData.endDate || '',
        '错误信息': item.message || '未知错误'
      };
    });
    
    // 导出到Excel
    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '导入失败记录');
    
    // 设置列宽
    const columnWidths = [
      { wch: 40 }, // 项目名称
      { wch: 20 }, // 项目编号
      { wch: 20 }, // 项目级别
      { wch: 20 }, // 项目类型
      { wch: 20 }, // 下达部门
      { wch: 20 }, // 主持人
      { wch: 15 }, // 开始日期
      { wch: 15 }, // 获批日期
      { wch: 15 }, // 结束日期
      { wch: 60 }  // 错误信息
    ];
    worksheet['!cols'] = columnWidths;
    
    // 尝试使用JSON导出备份
    try {
      // 创建JSON格式的失败记录
      const jsonData = JSON.stringify(
        failedRecords.map(item => {
          const originalRecordIndex = importPreviewData.value.findIndex(
            preview => preview.name === item.projectName
          );
          
          if (originalRecordIndex >= 0) {
            return {
              ...importPreviewData.value[originalRecordIndex].rawData,
              importError: item.message
            };
          }
          return { projectName: item.projectName, importError: item.message };
        }),
        null, 
        2
      );
      
      // 创建Blob对象
      const jsonBlob = new Blob([jsonData], { type: 'application/json' });
      
      // 创建下载链接
      const jsonUrl = URL.createObjectURL(jsonBlob);
      const jsonLink = document.createElement('a');
      jsonLink.href = jsonUrl;
      jsonLink.download = `项目导入失败记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.json`;
      
      // 触发下载
      document.body.appendChild(jsonLink);
      jsonLink.click();
      document.body.removeChild(jsonLink);
      URL.revokeObjectURL(jsonUrl);
    } catch (jsonError) {
      console.error('导出JSON失败记录失败:', jsonError);
      // 即使JSON导出失败，仍然继续Excel导出
    }
    
    // 生成文件名
    const fileName = `项目导入失败记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
    
    // 下载Excel文件
    XLSX.writeFile(workbook, fileName);
    message.success('导出失败记录成功');
    
  } catch (error) {
    console.error('导出失败记录出错:', error);
    message.error('导出失败记录失败');
  }
};

// 导入预览表格列定义
const importPreviewColumns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    width: 180
  },
  {
    title: '项目编号',
    dataIndex: 'projectId',
    key: 'projectId',
    width: 120
  },
  {
    title: '项目级别',
    dataIndex: 'levelName',
    key: 'level',
    width: 120,
    customRender: ({ text, record }) => {
      // 优先显示levelName，如果有levelId则从levelOptions中查找
      if (record.levelName) {
        return record.levelName;
      } else if (record.levelId) {
        const level = levelOptions.value.find(item => item.id === record.levelId);
        return level ? level.levelName : record.levelId;
      }
      return text || '-';
    }
  },
  {
    title: '项目类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '下达部门',
    dataIndex: 'projectIssuingDepartment',
    key: 'projectIssuingDepartment',
    width: 120
  },
  {
    title: '主持人',
    dataIndex: 'leaderName',
    key: 'leaderName',
    width: 100,
    customRender: ({ text, record }) => {
      // 从参与者中找到标记为主持人的
      if (record.participants && record.participants.length > 0) {
        const leader = record.participants.find(p => p.isLeader);
        return leader ? leader.name : (text || '-');
      }
      return text || '-';
    }
  },
  {
    title: '主持人分配比例',
    key: 'leaderAllocationRatio',
    width: 120,
    customRender: ({ record }) => {
      if (record.participants && record.participants.length > 0) {
        const leader = record.participants.find(p => p.isLeader);
        if (leader) {
          return leader.allocationRatio;
        }
      }
      return '-';
    }
  },
  {
    title: '参与人数',
    key: 'participantsCount',
    width: 100,
    customRender: ({ record }) => {
      if (record.participants) {
        return record.participants.length;
      }
      return 0;
    }
  },
  {
    title: '参与者',
    key: 'participants',
    width: 180,
    ellipsis: true,
    customRender: ({ record }) => {
      if (record.participants && record.participants.length > 0) {
        const nonLeaders = record.participants.filter(p => !p.isLeader);
        return nonLeaders.map(p => `${p.name || '未知'}(${p.allocationRatio})`).join('、');
      }
      return '-';
    }
  },
  {
    title: '批准经费(万)',
    dataIndex: 'fundingAmount',
    key: 'fundingAmount',
    width: 120
  },
  {
    title: '开始日期',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 120
  },
  {
    title: '获批日期',
    dataIndex: 'approvalDate',
    key: 'approvalDate',
    width: 120
  },
  {
    title: '结束日期',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 120
  }
];

// 处理JSON数据的公共方法
const processJsonData = async (parsedData) => {
  // 不论是什么文件，都先进入预览模式
  importPreviewLoading.value = true;
  message.loading('正在解析JSON数据并检查用户ID，请稍候...', 0);
  
  try {
    // 确保数据是数组形式
    const dataArray = Array.isArray(parsedData) ? parsedData : [parsedData];
    
    // 初始化用户ID检查结果
    userIdCheckResults.total = dataArray.length;
    userIdCheckResults.found = 0;
    userIdCheckResults.notFound = 0;

    // 准备预览数据
    const previewItems = dataArray.map((item, index) => {
      // 确保item不为null或undefined
      if (!item) {
        console.warn(`第 ${index + 1} 条数据为空，使用默认值`);
        item = { name: `项目 ${index + 1}` };
      }
      
      // 确保参与者数组存在
      const participants = item.participants || [];
      
      // 查找主持人
      const leader = participants.find(p => p.isLeader) || {};
      
      // 查找非主持人参与者
      const otherParticipants = participants.filter(p => !p.isLeader);
      
      // 创建预览项
      const previewItem = {
        index: index + 1,
        name: item.name || `项目 ${index + 1}`,
        projectId: item.projectId || '',
        levelId: item.levelId || '',
        levelName: item.levelName || '',
        type: item.type || '',
        projectIssuingDepartment: item.projectIssuingDepartment || '',
        fundingAmount: item.fundingAmount || 0,
        startDate: item.startDate || '',
        approvalDate: item.approvalDate || '',
        endDate: item.endDate || '',
        submitter: item.submitter || '',
        userIdCheckStatus: 'checking', // 初始状态为"检查中"
        
        // 主持人信息
        leaderName: leader.name || item.leaderName || '',
        leaderStudentNumber: leader.studentNumber || '',
        leaderAllocationRatio: leader.allocationRatio || 0,
        
        // 参与者信息
        participants: participants,
        participantsCount: participants.length,
        otherParticipants: otherParticipants,
        
        // 其他可能的字段
        isUniversityFirstUnit: item.isUniversityFirstUnit || true,
        isCollegeFirstUnit: item.isCollegeFirstUnit || true,
        status: item.status || 1,
        remark: item.remark || '',
        
        // 保存原始数据用于导入
        rawData: item
      };
      
      return previewItem;
    });
    
    importPreviewData.value = previewItems;
    
    // 显示预览弹窗
    importPreviewVisible.value = true;
    
    // 开始检查用户ID
    for (let i = 0; i < previewItems.length; i++) {
      const item = previewItems[i];
      
      // 检查主持人信息
      if (item.leaderName) {
        try {
          // 优先使用leaderName查找
          const userSearchResult = await usersSearch({ keyword: item.leaderName });
          
          if (userSearchResult && userSearchResult.code === 200 && userSearchResult.data.length > 0) {
            // 找到了用户ID
            item.userIdCheckStatus = 'success';
            item.userId = userSearchResult.data[0].id;
            userIdCheckResults.found++;
          } else {
            // 未找到用户ID，尝试通过工号查找
            if (item.leaderStudentNumber) {
              try {
                const studentResult = await usersSearch({ keyword: item.leaderStudentNumber });
                if (studentResult && studentResult.code === 200 && studentResult.data.length > 0) {
                  item.userIdCheckStatus = 'success';
                  item.userId = studentResult.data[0].id;
                  userIdCheckResults.found++;
                } else {
                  item.userIdCheckStatus = 'warning';
                  userIdCheckResults.notFound++;
                }
              } catch (error) {
                console.error(`检查主持人工号"${item.leaderStudentNumber}"失败:`, error);
                item.userIdCheckStatus = 'error';
                userIdCheckResults.notFound++;
              }
            } else {
              item.userIdCheckStatus = 'warning';
              userIdCheckResults.notFound++;
            }
          }
        } catch (error) {
          console.error(`检查主持人名称"${item.leaderName}"失败:`, error);
          // 尝试通过工号查找
          if (item.leaderStudentNumber) {
            try {
              const studentResult = await usersSearch({ keyword: item.leaderStudentNumber });
              if (studentResult && studentResult.code === 200 && studentResult.data.length > 0) {
                item.userIdCheckStatus = 'success';
                item.userId = studentResult.data[0].id;
                userIdCheckResults.found++;
              } else {
                item.userIdCheckStatus = 'warning';
                userIdCheckResults.notFound++;
              }
            } catch (error) {
              console.error(`检查主持人工号"${item.leaderStudentNumber}"失败:`, error);
              item.userIdCheckStatus = 'error';
              userIdCheckResults.notFound++;
            }
          } else {
            item.userIdCheckStatus = 'error';
            userIdCheckResults.notFound++;
          }
        }
      } else if (item.leaderStudentNumber) {
        // 如果没有leaderName但有工号，通过工号查找
        try {
          const userSearchResult = await usersSearch({ keyword: item.leaderStudentNumber });
          
          if (userSearchResult && userSearchResult.code === 200 && userSearchResult.data.length > 0) {
            // 找到了用户ID
            item.userIdCheckStatus = 'success';
            item.userId = userSearchResult.data[0].id;
            userIdCheckResults.found++;
          } else {
            // 未找到用户ID
            item.userIdCheckStatus = 'warning';
            userIdCheckResults.notFound++;
          }
        } catch (error) {
          console.error(`检查主持人工号"${item.leaderStudentNumber}"失败:`, error);
          item.userIdCheckStatus = 'error';
          userIdCheckResults.notFound++;
        }
      } else {
        // 没有主持人信息
        item.userIdCheckStatus = 'warning';
        userIdCheckResults.notFound++;
      }
      
      // 每5条更新一次显示
      if (i % 5 === 0 || i === previewItems.length - 1) {
        importPreviewData.value = [...previewItems];
      }
    }
    
    // 更新最终结果
    importPreviewData.value = [...previewItems];
  } catch (error) {
    console.error('处理JSON数据失败:', error);
    message.error(`处理JSON数据失败: ${error.message || '未知错误'}`);
  } finally {
    message.destroy();
    importPreviewLoading.value = false;
  }
};

// 结果表格列定义
const resultColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 300,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '消息',
    dataIndex: 'message',
    key: 'message',
    width: 300,
    ellipsis: true
  }
];

// 添加在其他状态变量附近
const currentRole = ref('');
const currentUserId = ref('');

// 处理重新提交审核
const handleResubmit = (record) => {
  Modal.confirm({
    title: '确认重新提交审核',
    content: '是否确认将该项目重新提交审核？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await resubmitForReview(record.id);
    }
  });
};

// 重新提交审核接口调用
const resubmitForReview = async (id) => {
  try {
    loading.value = true;
    const response = await reapplyReview({ id });
    
    // 修改判断条件，适应后端实际返回的响应格式
    if (response && response.code === 200) {
      message.success('重新提交审核成功');
      fetchData(); // 刷新数据
    } else {
      message.error(response?.message || '重新提交审核失败');
    }
  } catch (error) {
    console.error('重新提交审核失败:', error);
    message.error('重新提交审核失败: ' + (error.message || error));
  } finally {
    loading.value = false;
  }
};

// 添加时间范围文本
const timeRangeText = ref('');

// 获取时间范围
const getTimeRange = () => {
  // 调用API获取时间范围
  getScoreTimeRange('researchProjects').then(res => {
    if (res.code === 200 && res.data) {
      timeRangeText.value = res.data.timeRange || '';
    } else {
      timeRangeText.value = '暂无时间范围数据';
    }
  }).catch(error => {
    console.error('获取时间范围失败:', error);
    timeRangeText.value = '获取时间范围失败';
  });
};

// 在onMounted中调用
onMounted(() => {
  getTimeRange();
});

</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';

// 页面特定样式
.research-projects {
  margin: 24px;
  border: 1px solid #f0f0f0;
}

.text-danger {
  color: #ff4d4f;
}

.members-container {
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;

  .ant-list {
    max-height: 200px;
    overflow-y: auto;
  }
}

.member-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.filter-container {
  margin-bottom: 16px;
}

/* 导入预览表格中错误行的样式 */
:deep(.error-row) {
  background-color: #fff2f0;
}
</style>