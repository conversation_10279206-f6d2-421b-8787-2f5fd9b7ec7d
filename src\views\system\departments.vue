<template>
  <div class="departments-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>部门管理</h2>
      <p>管理组织架构中的部门信息</p>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchForm.departmentName"
            placeholder="请输入部门名称"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.status"
            placeholder="状态"
            allow-clear
            style="width: 100%"
          >
            <a-select-option :value="1">启用</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="8">
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
            <a-button type="primary" @click="handleCreate" v-permission="'departments:create'">
              <PlusOutlined />
              新建部门
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 部门表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="departments"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 部门名称列 -->
        <template #departmentName="{ record }">
          <div class="department-name">
            <span class="name">{{ record.departmentName }}</span>
            <a-tag v-if="record.departmentCode" color="blue" size="small">
              {{ record.departmentCode }}
            </a-tag>
          </div>
        </template>

        <!-- 上级部门列 -->
        <template #parent="{ record }">
          <span v-if="record.parent">{{ record.parent.departmentName }}</span>
          <span v-else class="text-muted">无</span>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="record.status === 1 ? 'success' : 'error'">
            {{ record.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-dropdown>
            <a-button type="link" size="small">
              操作 <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="detail" @click="handleDetail(record)" v-permission="'departments:detail'">
                  <EyeOutlined />
                  查看详情
                </a-menu-item>
                <a-menu-item key="edit" @click="handleEdit(record)" v-permission="'departments:update'">
                  <EditOutlined />
                  编辑
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="delete" @click="handleDelete(record)" v-permission="'departments:delete'">
                  <DeleteOutlined />
                  删除
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-table>
    </div>

    <!-- 部门表单弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑部门' : '新建部门'"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitLoading"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="部门名称" name="departmentName">
          <a-input v-model:value="formState.departmentName" placeholder="请输入部门名称" />
        </a-form-item>

        <a-form-item label="部门代码" name="departmentCode">
          <a-input v-model:value="formState.departmentCode" placeholder="请输入部门代码" />
        </a-form-item>

        <a-form-item label="上级部门" name="parentId">
          <a-tree-select
            v-model:value="formState.parentId"
            :tree-data="departmentTreeOptions"
            placeholder="请选择上级部门"
            allow-clear
            tree-default-expand-all
            :field-names="{ label: 'departmentName', value: 'id', children: 'children' }"
          />
        </a-form-item>

        <a-form-item label="排序" name="sort">
          <a-input-number
            v-model:value="formState.sort"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="状态" name="status" v-if="isEdit">
          <a-radio-group v-model:value="formState.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="部门描述" name="description">
          <a-textarea
            v-model:value="formState.description"
            placeholder="请输入部门描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 部门详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="部门详情"
      width="600px"
      :footer="null"
    >
      <a-descriptions :column="2" bordered v-if="currentDepartment">
        <a-descriptions-item label="部门名称" :span="2">
          {{ currentDepartment.departmentName }}
        </a-descriptions-item>
        <a-descriptions-item label="部门代码">
          {{ currentDepartment.departmentCode || '无' }}
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="currentDepartment.status === 1 ? 'success' : 'error'">
            {{ currentDepartment.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="上级部门" :span="2">
          {{ currentDepartment.parent?.departmentName || '无' }}
        </a-descriptions-item>
        <a-descriptions-item label="排序">
          {{ currentDepartment.sort }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatDate(currentDepartment.createdAt) }}
        </a-descriptions-item>
        <a-descriptions-item label="部门描述" :span="2">
          {{ currentDepartment.description || '无' }}
        </a-descriptions-item>
        <a-descriptions-item label="子部门" :span="2" v-if="currentDepartment.children?.length">
          <a-space wrap>
            <a-tag v-for="child in currentDepartment.children" :key="child.id" color="blue">
              {{ child.departmentName }}
            </a-tag>
          </a-space>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DownOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import {
  getDepartments,
  getDepartmentTree,
  getDepartmentDetail,
  createDepartment,
  updateDepartment,
  deleteDepartment
} from '@/api/modules/api.departments'

// 响应式数据
const loading = ref(false)
const departments = ref([])
const modalVisible = ref(false)
const detailVisible = ref(false)
const submitLoading = ref(false)
const isEdit = ref(false)
const currentDepartment = ref(null)
const departmentTreeOptions = ref([])
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  departmentName: '',
  status: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表单数据
const formState = reactive({
  departmentName: '',
  departmentCode: '',
  description: '',
  parentId: undefined,
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  departmentName: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 100, message: '部门名称长度在2-100个字符之间', trigger: 'blur' }
  ],
  departmentCode: [
    { max: 50, message: '部门代码不能超过50个字符', trigger: 'blur' }
  ],
  sort: [
    { type: 'number', min: 0, message: '排序必须是非负整数', trigger: 'blur' }
  ]
}

// 表格列配置
const columns = [
  {
    title: '部门名称',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 200,
    slots: { customRender: 'departmentName' }
  },
  {
    title: '上级部门',
    dataIndex: 'parent',
    key: 'parent',
    width: 150,
    slots: { customRender: 'parent' }
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    align: 'center',
    slots: { customRender: 'status' }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180,
    customRender: ({ text }) => formatDate(text)
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    align: 'center',
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 加载部门列表
const loadDepartments = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getDepartments(params)
    if (response.status === 1) {
      departments.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载部门列表失败:', error)
    message.error('加载部门列表失败')
  } finally {
    loading.value = false
  }
}

// 加载部门树
const loadDepartmentTree = async () => {
  try {
    const response = await getDepartmentTree()
    if (response.status === 1) {
      departmentTreeOptions.value = response.data || []
    }
  } catch (error) {
    console.error('加载部门树失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadDepartments()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    departmentName: '',
    status: undefined
  })
  pagination.current = 1
  loadDepartments()
}

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadDepartments()
}

// 新建部门
const handleCreate = () => {
  isEdit.value = false
  modalVisible.value = true
  Object.assign(formState, {
    departmentName: '',
    departmentCode: '',
    description: '',
    parentId: undefined,
    sort: 0,
    status: 1
  })
  loadDepartmentTree()
}

// 编辑部门
const handleEdit = (record) => {
  isEdit.value = true
  modalVisible.value = true
  Object.assign(formState, {
    departmentName: record.departmentName,
    departmentCode: record.departmentCode,
    description: record.description,
    parentId: record.parentId,
    sort: record.sort,
    status: record.status
  })
  currentDepartment.value = record
  loadDepartmentTree()
}

// 查看详情
const handleDetail = async (record) => {
  try {
    const response = await getDepartmentDetail(record.id)
    if (response.status === 1) {
      currentDepartment.value = response.data
      detailVisible.value = true
    }
  } catch (error) {
    console.error('获取部门详情失败:', error)
    message.error('获取部门详情失败')
  }
}

// 删除部门
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除部门"${record.departmentName}"吗？`,
    onOk: async () => {
      try {
        const response = await deleteDepartment(record.id)
        if (response.status === 1) {
          message.success('删除部门成功')
          loadDepartments()
        } else {
          message.error(response.message || '删除部门失败')
        }
      } catch (error) {
        console.error('删除部门失败:', error)
        message.error('删除部门失败')
      }
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    let response
    if (isEdit.value) {
      response = await updateDepartment(currentDepartment.value.id, formState)
    } else {
      response = await createDepartment(formState)
    }

    if (response.status === 1) {
      message.success(isEdit.value ? '更新部门成功' : '创建部门成功')
      modalVisible.value = false
      loadDepartments()
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

// 页面加载
onMounted(() => {
  loadDepartments()
})
</script>

<style scoped>
.departments-container {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.table-section {
  margin-top: 16px;
}

.department-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.department-name .name {
  font-weight: 500;
}

.text-muted {
  color: #999;
}
</style>
