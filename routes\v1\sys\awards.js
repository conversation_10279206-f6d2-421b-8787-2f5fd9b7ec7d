const express = require('express');
const router = express.Router();
const awardController = require('../../../controllers/v1/rules/awardsController');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });

/**
 * 获取获奖列表
 * @route GET /v1/sys/awards
 * @group 获奖管理 - 系统获奖相关接口
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @param {string} name.query - 获奖名称（模糊搜索）
 * @param {string} type.query - 获奖类型
 * @param {string} level.query - 获奖级别
 * @param {string} teacherName.query - 教师姓名
 * @param {string} awardDate.query - 获奖时间
 * @param {boolean} ifReviewer.query - 审核状态(true：已审核，false：未审核)
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {page: 1, pageSize: 10, total: 0, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/', awardController.getAwards);

/**
 * 获取个人奖项统计
 * @route GET /v1/sys/awards/personal-stats
 * @group 获奖管理 - 系统获奖相关接口
 * @param {string} userId.query.required - 用户ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {awardCount, totalScore, typeDistribution, levelDistribution, timeDistribution, recentAwards}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/personal-stats', awardController.getPersonalAwardStats);

/**
 * 导出获奖数据
 * @route GET /v1/sys/awards/export
 * @group 获奖管理 - 系统获奖相关接口
 * @param {string} name.query - 获奖名称（模糊搜索）
 * @param {string} type.query - 获奖类型
 * @param {string} level.query - 获奖级别
 * @param {string} teacherName.query - 教师姓名
 * @param {string} awardDate.query - 获奖时间
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/export', awardController.exportAwards);

/**
 * 导入获奖数据
 * @route POST /v1/sys/awards/import
 * @group 获奖管理 - 系统获奖相关接口
 * @param {file} file.body.required - Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', upload.single('file'), awardController.importAwards);

/**
 * 获取获奖详情
 * @route GET /v1/sys/awards/:id
 * @group 获奖管理 - 系统获奖相关接口
 * @param {string} id.path.required - 获奖ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {获奖详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/:id', awardController.getAwardById);

/**
 * 创建获奖
 * @route POST /v1/sys/awards
 * @group 获奖管理 - 系统获奖相关接口
 * @param {string} name.body.required - 获奖名称
 * @param {string} type.body.required - 获奖类型
 * @param {string} level.body.required - 获奖级别
 * @param {string} teachers.body.required - 获奖教师
 * @param {string} awardDate.body.required - 获奖时间
 * @param {string} awardUnit.body - 获奖单位
 * @param {number} awardRank.body - 获奖排名
 * @param {number} score.body - 评分
 * @param {string} remark.body - 备注
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "获奖ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/', awardController.createAward);

/**
 * 更新获奖
 * @route PUT /v1/sys/awards/:id
 * @group 获奖管理 - 系统获奖相关接口
 * @param {string} id.path.required - 获奖ID
 * @param {string} name.body - 获奖名称
 * @param {string} type.body - 获奖类型
 * @param {string} level.body - 获奖级别
 * @param {string} teachers.body - 获奖教师
 * @param {string} awardDate.body - 获奖时间
 * @param {string} awardUnit.body - 获奖单位
 * @param {number} awardRank.body - 获奖排名
 * @param {number} score.body - 评分
 * @param {string} remark.body - 备注
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/:id', awardController.updateAward);

/**
 * 删除获奖
 * @route DELETE /v1/sys/awards/:id
 * @group 获奖管理 - 系统获奖相关接口
 * @param {string} id.path.required - 获奖ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/:id', awardController.deleteAward);

/**
 * 获取所有用户获奖得分排名
 * @route POST /v1/sys/awards/all-users-score
 * @group 获奖管理 - 系统获奖相关接口
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页数量，默认10
 * @param {string} nickname.body - 用户昵称（模糊搜索）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {page: 1, pageSize: 10, total: 0, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/all-users-score', awardController.getAllUsersAwardScore);

/**
 * 审核奖项
 * @route POST /v1/sys/awards/:id/review
 * @group 获奖管理 - 系统获奖相关接口
 * @param {string} id.path.required - 获奖ID
 * @returns {object} 200 - {code: 200, message: "审核成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/:id/review', awardController.reviewAward);

module.exports = router; 