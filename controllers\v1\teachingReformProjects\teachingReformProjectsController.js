const teachingReformProjectModel = require('@models/v1/mapping/teachingReformProjectsModel');
const teachingReformProjectLevelsModel = require('@models/v1/mapping/teachingReformProjectLevelsModel');
const teachingReformProjectParticipantsModel = require('@models/v1/mapping/teachingReformParticipantsModel');
const userModel = require('@models/v1/mapping/userModel');
const fileModel = require('@models/v1/mapping/fileModel');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const sequelize = require('@config/dbConfig');
const { getTimeIntervalByName, getUserInfoFromRequest, getSequelizeInstance } = require('../../../utils/others');
const fs = require('fs');
const path = require('path');
const { isProjectInTimeRange } = require('../../../utils/others');
const { updateUserRankings, RANKING_TYPE_MAPPINGS } = require('../../../utils/rankingUtils');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');


/**
 * 获取教学改革项目列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjects = async (req, res) => {
  try {
    const { 
      projectName, 
      levelId, 
      approvalDateStart, 
      approvalDateEnd, 
      userId, 
      page = 1, 
      pageSize = 10,
      range = 'all',
      reviewStatus = 'all',
      sortField = 'approvalDate', 
      sortOrder = 'desc',
      query,
      isExport = false
    } = req.body;
    
    // 构建查询条件
    const whereCondition = {};
    
    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    }
    // reviewStatus === 'all' 不添加筛选条件
    
    // 根据项目名称搜索
    if (projectName) {
      whereCondition.projectName = { [Op.like]: `%${projectName}%` };
    }

    // 根据项目级别过滤
    if (levelId) {
      whereCondition.levelId = levelId;
    }

    // 根据批准日期过滤
    if (approvalDateStart) {
      whereCondition.approvalDate = { ...whereCondition.approvalDate, [Op.gte]: approvalDateStart };
    }
    
    if (approvalDateEnd) {
      whereCondition.approvalDate = { ...whereCondition.approvalDate, [Op.lte]: approvalDateEnd };
    }
    
    // 根据关键词搜索
    if (query) {
      whereCondition[Op.or] = [
        { projectName: { [Op.like]: `%${query}%` } },
        { projectNumber: { [Op.like]: `%${query}%` } },
        { approvalDepartment: { [Op.like]: `%${query}%` } }
      ];
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingReformProjects");
    
    // 根据range参数筛选数据 - 添加到查询条件中
    if (range === 'in' && timeInterval) {
      // 在时间区间内的项目
      whereCondition.approvalDate = { 
        ...(whereCondition.approvalDate || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的项目
      whereCondition.approvalDate = { 
        ...(whereCondition.approvalDate || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 如果提供了userId，先获取用户参与的项目ID
    if (userId) {
      const participations = await teachingReformProjectParticipantsModel.findAll({
        where: { userId: userId },
        attributes: ['projectId']
      });
      
      const projectIds = participations.map(p => p.projectId);
      
      if (projectIds.length === 0) {
        // 用户没有参与任何项目
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: {
            list: [],
            pagination: {
              page: pageNum,
              pageSize: pageSizeNum,
              total: 0,
              totalPages: 0
            }
          }
        });
      }
      
      whereCondition.id = { [Op.in]: projectIds };
    }
    
    // 查询项目列表 - 如果是导出，不使用分页限制
    const queryOptions = {
      where: whereCondition,
      include: [
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'nickname', 'username', 'studentNumber']
            }
          ]
        }
      ],
      order: [[sortField, sortOrder]]
    };
    
    // 仅在非导出模式下应用分页
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }
    
    const { count, rows } = await teachingReformProjectModel.findAndCountAll(queryOptions);
    
    // 处理项目数据，添加isInTimeRange字段但不再根据range筛选
    const processedProjects = rows.map(project => {
      const projectJson = project.toJSON();
      
      // 添加isInTimeRange字段以便前端展示
      let isInTimeRange = false;
      if (timeInterval && projectJson.approvalDate) {
        isInTimeRange = isProjectInTimeRange(projectJson.approvalDate, timeInterval.startTime, timeInterval.endTime);
      }
      
      projectJson.isInTimeRange = isInTimeRange;
      return projectJson;
    });
    
    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: processedProjects,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取教学改革项目列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教学改革项目列表失败',
      error: error.message
    });
  }
};

/**
 * 获取项目详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    // 查询项目详情
    const project = await teachingReformProjectModel.findByPk(id, {
      include: [
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score']
        },
        {
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'nickname', 'username', 'studentNumber']
            }
          ]
        }
      ]
    });
    
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '未找到项目',
        data: null
      });
    }
    
    // 将项目数据转换为JSON对象，方便后续处理
    const projectData = project.toJSON();
    
    // 查询项目关联的文件列表
    const files = await fileModel.findAll({
      where: {
        projectId: id
      },
      attributes: [
        'id', 
        'fileName', 
        'originalName', 
        'filePath', 
        'fileSize', 
        'mimeType', 
        'extension',
        'uploaderId', 
        'relatedId', 
        'relatedType', 
        'createdAt', 
        'updatedAt'
      ],
      order: [['createdAt', 'DESC']]
    });
    
    // 处理文件信息，添加URL和其他前端需要的信息
    projectData.attachments = files.map(file => {
      const fileData = file.toJSON();
      // 构造文件URL
      const filePath = fileData.filePath;
      const url = filePath ? (filePath.startsWith('/') ? filePath : `/${filePath}`) : '';
      
      return {
        id: fileData.id,
        name: fileData.originalName,
        fileName: fileData.fileName,
        size: fileData.fileSize,
        type: fileData.mimeType,
        extension: fileData.extension,
        url: url,
        filePath: fileData.filePath,
        uploadTime: fileData.createdAt
      };
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: projectData
    });
  } catch (error) {
    console.error('获取项目详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目详情失败',
      error: error.message
    });
  }
};

/**
 * 创建教学改革项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createProject = async (req, res) => {
  // 获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(teachingReformProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();
  
  try {
    const { 
      projectNumber, 
      projectName, 
      approvalDepartment, 
      approvalDate, 
      approvalFund, 
      levelId, 
      startYear,
      endYear,
      remark,
      participants,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl    // 接收前端传递的文件路径
    } = req.body;
    console.log("创建项目请求参数:", req.body); 
    // 验证必填项
    if (!projectName || !levelId || !participants || participants.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '项目名称、级别和参与者不能为空',
        data: null
      });
    }
    
    // 确认项目级别存在
    const level = await teachingReformProjectLevelsModel.findByPk(levelId, { transaction });
    if (!level) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到项目级别',
        data: null
      });
    }
    
    // 验证参与者
    const participantUserIds = participants.map(p => p.userId);
    const users = await userModel.findAll({
      where: { id: { [Op.in]: participantUserIds } },
      transaction
    });
    
    if (users.length !== participantUserIds.length) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '存在无效的参与者ID',
        data: null
      });
    }
    
    // 验证分配比例总和为1 (0-1的小数形式)
    const totalRatio = participants.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
    if (Math.abs(totalRatio - 1) > 0.01) {  // 允许0.01的误差
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '参与者分配比例总和必须为1',
        data: null
      });
    }
    
    // 创建项目
    const projectId = uuidv4();
    const project = await teachingReformProjectModel.create({
      id: projectId,
      projectNumber,
      projectName,
      approvalDepartment,
      approvalDate,
      approvalFund,
      levelId,
      startYear,
      endYear,
      remark,
      reviewerId: null,
      ifReviewer: false,
      attachmentUrl: null, // 初始设置为null，稍后使用ID更新
      createdAt: new Date(),
      updatedAt: new Date()
    }, { transaction });
    
    // 创建参与者
    const participantRecords = await Promise.all(participants.map(async (p) => {
      return await teachingReformProjectParticipantsModel.create({
        id: uuidv4(),
        projectId,
        userId: p.userId,
        allocationRatio: parseFloat(p.allocationRatio), // 直接使用前端传递的小数值(0-1)
        isLeader: !!p.isLeader
      }, { transaction });
    }));
    
    // 处理关联文件 - 使用前端传递的文件ID而非重新创建文件记录
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl === 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
      
      // 如果有文件ID，关联到项目
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            projectId: project.id, // 设置文件的projectId为新创建的项目ID
            relatedId: project.id,
            relatedType: 'teaching_reform_projects' // 使用teachingReformProjects作为relatedType
          };
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath; // 使用filePath字段，这是文件模型中的正确字段名
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );

          processedFileIds.push(fileId);
        }
      }
    } else if (req.files && req.files.length > 0) {
      // 处理通过multer上传的文件
      const uploadedFiles = req.files;
      
      for (const file of uploadedFiles) {
        // 创建文件记录
        const fileRecord = await fileModel.create({
          id: uuidv4(),
          originalName: file.originalname,
          filePath: file.path,
          mimeType: file.mimetype,
          size: file.size,
          projectId: project.id,
          relatedId: project.id,
          relatedType: 'teaching_reform_projects'
        }, { transaction });
        
        processedFileIds.push(fileRecord.id);
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，删除标记为删除的物理文件
    if (req.filesToDeleteAfterCommit && req.filesToDeleteAfterCommit.length > 0) {
      try {
        for (const filePath of req.filesToDeleteAfterCommit) {
          if (filePath && fs.existsSync(filePath)) {
            try {
              fs.unlinkSync(filePath);
              console.log(`成功删除物理文件: ${filePath}`);
            } catch (deleteError) {
              console.error(`删除物理文件失败: ${filePath}`, deleteError);
            }
          }
        }
      } catch (error) {
        console.error('删除物理文件时出错:', error);
      }
    }
    
    // 事务提交成功后，异步移动文件到指定的项目目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'teaching_reform_projects'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${project.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 新增变量跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: project.id,
                relatedId: project.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新项目的attachmentUrl为项目文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的项目文件夹路径，确保不包含文件名
            const projectFolderPath = `uploads\\teaching_reform_projects\\${project.id}\\`;
            
            await project.update({
              attachmentUrl: projectFolderPath
            });
            console.log(`已更新项目 ${project.id} 的attachmentUrl为: ${projectFolderPath}`);
            
          } catch (updateError) {
            console.error('更新项目attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响项目创建的返回结果，仅记录错误
        console.error('移动文件到项目目录失败:', moveError);
      }
    }
    
    // 查询创建的项目详情
    const createdProject = await teachingReformProjectModel.findByPk(projectId, {
      include: [
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score']
        },
        {
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'nickname', 'username', 'studentNumber']
            }
          ]
        }
      ]
    });
    
    // 查询关联的文件
    const files = await fileModel.findAll({
      where: {
        projectId: projectId
      }
    });
    
    // 添加文件信息到响应数据
    const responseData = createdProject.toJSON();
    responseData.attachments = files.map(file => file.toJSON());
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: responseData
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('创建项目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建项目失败',
      error: error.message
    });
  }
};

/**
 * 更新教学改革项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateProject = async (req, res) => {
  // 获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(teachingReformProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();
  
  try {
    const { id } = req.params;
    const { 
      projectNumber, 
      projectName, 
      approvalDepartment, 
      approvalDate, 
      approvalFund, 
      levelId, 
      startYear,
      endYear,
      remark,
      participants,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl,   // 接收前端传递的文件路径
    } = req.body;
    console.log("req.body===",req.body);
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    // 查询项目是否存在
    const project = await teachingReformProjectModel.findByPk(id, {
      include: [
        {
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          required: false
        },
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          required: false
        }
      ],
      transaction
    });
    
    if (!project) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到项目',
        data: null
      });
    }
    
    // 权限检查：管理员或者项目的参与者可以编辑
    const userInfo = await getUserInfoFromRequest(req);
    const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');
    
    // 检查用户是否是项目参与者
    const isParticipant = await teachingReformProjectParticipantsModel.findOne({
      where: { 
        projectId: id,
        userId: userInfo.id
      },
      transaction
    });
    
    if (!isAdmin && !isParticipant) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限编辑该项目',
        data: null
      });
    }
    
    // 如果更新级别，确认级别存在
    if (levelId && levelId !== project.levelId) {
      const level = await teachingReformProjectLevelsModel.findByPk(levelId, { transaction });
      if (!level) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '未找到项目级别',
          data: null
        });
      }
    }
    
    // 如果更新参与者，验证参与者
    if (participants && participants.length > 0) {
      const participantUserIds = participants.map(p => p.userId);
      const users = await userModel.findAll({
        where: { id: { [Op.in]: participantUserIds } },
        transaction
      });
      
      if (users.length !== participantUserIds.length) {
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '存在无效的参与者ID',
          data: null
        });
      }
      
      // 验证分配比例总和为1 (0-1的小数形式)
      const totalRatio = participants.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
      if (Math.abs(totalRatio - 1) > 0.01) {  // 允许0.01的误差
        await transaction.rollback();
        return res.status(400).json({
          code: 400,
          message: '参与者分配比例总和必须为1',
          data: null
        });
      }
    }
    
    // 如果项目已审核，需要更新用户排名
    if (project.ifReviewer == 1) {
      try {
        console.log('开始更新用户排名数据，项目ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("teachingReformProjects");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断项目变更前后是否在时间区间内（使用approvalDate判断）
        const oldApprovalDate = project.approvalDate;
        const newApprovalDate = approvalDate || project.approvalDate;
        console.log('项目审批日期 - 原始:', oldApprovalDate, '新:', newApprovalDate);
        
        const wasInTimeRange = timeInterval ? 
          isProjectInTimeRange(oldApprovalDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
          
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(newApprovalDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('项目时间范围状态 - 原始:', wasInTimeRange ? '在范围内' : '不在范围内', 
                   '变更后:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表（针对减分操作）
        let oldRankingTables = [];
        if (wasInTimeRange) {
          oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        // 确定需要更新的排名表（针对加分操作）
        let newRankingTables = [];
        if (isInTimeRange) {
          newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('排名表 - 原始:', oldRankingTables, '新:', newRankingTables);
        
        // 获取旧的级别分数
        const oldBaseScore = project.level ? project.level.score : 0;
        console.log('原始级别分数:', oldBaseScore);
        
        // 获取新的级别分数
        let newBaseScore = oldBaseScore;
        if (levelId && levelId !== project.levelId) {
          const newLevel = await teachingReformProjectLevelsModel.findByPk(levelId, { transaction });
          if (newLevel) {
            newBaseScore = newLevel.score || 0;
          }
        }
        console.log('新级别分数:', newBaseScore);
        
        // 获取旧的参与者名单
        const oldParticipants = project.participants || [];
        console.log('原始参与者数量:', oldParticipants.length);
        
        // 准备新的参与者列表
        let newParticipants = [];
        
        // 使用提供的participants数组
        if (participants && Array.isArray(participants)) {
          newParticipants = participants.map(p => ({
            userId: p.userId,
            allocationRatio: parseFloat(p.allocationRatio) || 0,
            isLeader: p.isLeader || false
          }));
        }
        console.log('新参与者数量:', newParticipants.length);
        
        // 从原始参与者中找出要删除的参与者 - 他们在旧列表中但不在新列表中
        const oldUserIds = oldParticipants.map(p => p.userId);
        const newUserIds = newParticipants.map(p => p.userId);
        
        // 找出要删除的参与者
        const deletedUserIds = oldUserIds.filter(id => !newUserIds.includes(id));
        console.log('要删除的参与者:', deletedUserIds);
        
        // 找出保留的参与者
        const keptUserIds = oldUserIds.filter(id => newUserIds.includes(id));
        console.log('保留的参与者:', keptUserIds);
        
        // 找出新增的参与者
        const addedUserIds = newUserIds.filter(id => !oldUserIds.includes(id));
        console.log('新增的参与者:', addedUserIds);
        
        // 1. 处理要删除的参与者 - 减少项目数量和分数
        if (deletedUserIds.length > 0) {
          const deletedParticipants = oldParticipants.filter(p => deletedUserIds.includes(p.userId));
          console.log('被删除的参与者完整数据:', JSON.stringify(deletedParticipants));
          
          const deletedIds = [];
          const deletedRatios = [];
          const deletedScores = [];
          const countDeltas = []; // 固定值为1的数组表示每人减少1个项目
          
          for (const participant of deletedParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            deletedIds.push(userId);
            deletedRatios.push(ratio);
            deletedScores.push(participantScore);
            countDeltas.push(1); // 每个被删除的参与者项目数-1
          }
          
          console.log('被删除参与者的排名更新数据:', {
            userIds: deletedIds,
            countDeltas: countDeltas,
            scores: deletedScores
          });
          
          // 对每个排名表执行减分操作
          for (const table of oldRankingTables) {
            console.log(`为被删除的参与者更新排名表 ${table}`);
            await updateUserRankings(
              deletedIds,
              table,
              'teachingReformProjects',
              countDeltas, // 使用固定值1表示项目计数-1
              deletedScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去被删除参与者的分数和项目数');
        }
        
        // 2. 处理保留但分配比例变化的参与者 - 先减去原有分数，后面再加上新分数
        if (keptUserIds.length > 0) {
          const keptOldParticipants = oldParticipants.filter(p => keptUserIds.includes(p.userId));
          console.log('保留的参与者原始数据:', JSON.stringify(keptOldParticipants));
          
          const keptIds = [];
          const keptRatios = [];
          const keptScores = [];
          
          for (const participant of keptOldParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;
            
            keptIds.push(userId);
            keptRatios.push(ratio);
            keptScores.push(participantScore);
          }
          
          console.log('保留参与者的减分数据:', {
            userIds: keptIds,
            scores: keptScores
          });
          
          // 减去原有分数（但不减少项目计数）
          for (const table of oldRankingTables) {
            console.log(`为保留的参与者减去原有分数：${table}`);
            // 传递0表示不改变项目计数，只改变分数
            const zeroCounts = Array(keptIds.length).fill(0);
            await updateUserRankings(
              keptIds,
              table,
              'teachingReformProjects',
              zeroCounts, // 项目计数不变
              keptScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去保留参与者的原有分数');
        }
        
        // 3. 处理所有新参与者名单（包括保留的和新增的）- 增加分数，对新增的也增加项目数
        if (newParticipants.length > 0) {
          console.log('新参与者完整数据:', JSON.stringify(newParticipants));
          
          const allNewIds = [];
          const allNewRatios = [];
          const allNewScores = [];
          const allCountDeltas = []; // 对新增参与者设为1，对保留的参与者设为0
          
          for (const participant of newParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = newBaseScore * ratio;
            
            allNewIds.push(userId);
            allNewRatios.push(ratio);
            allNewScores.push(participantScore);
            
            // 对新增参与者项目数+1，对保留的参与者项目数不变
            if (addedUserIds.includes(userId)) {
              allCountDeltas.push(1);
            } else {
              allCountDeltas.push(0);
            }
          }
          
          console.log('所有新参与者的加分数据:', {
            userIds: allNewIds,
            countDeltas: allCountDeltas,
            scores: allNewScores
          });
          
          // 为所有参与者添加分数，但只为新增参与者增加项目计数
          for (const table of newRankingTables) {
            console.log(`为所有参与者更新排名表：${table}`);
            await updateUserRankings(
              allNewIds,
              table,
              'teachingReformProjects',
              allCountDeltas, // 使用差异化的计数更新：新增的+1，保留的不变
              allNewScores,
              transaction,
              "add" // 加分操作
            );
          }
          console.log('成功更新所有参与者的分数和新增参与者的项目数');
        }
        
        console.log('成功完成教学改革项目参与者的排名数据更新');
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 更新项目信息
    const updateData = {};
    if (projectNumber !== undefined) updateData.projectNumber = projectNumber;
    if (projectName !== undefined) updateData.projectName = projectName;
    if (approvalDepartment !== undefined) updateData.approvalDepartment = approvalDepartment;
    if (approvalDate !== undefined) updateData.approvalDate = approvalDate;
    if (approvalFund !== undefined) updateData.approvalFund = approvalFund;
    if (levelId !== undefined) updateData.levelId = levelId;
    if (startYear !== undefined) updateData.startYear = startYear;
    if (endYear !== undefined) updateData.endYear = endYear;
    if (remark !== undefined) updateData.remark = remark;
    // 设置标准化的附件文件夹路径
    updateData.attachmentUrl = `uploads\\teaching_reform_projects\\${id}\\`;
    updateData.updatedAt = new Date();
    
    await project.update(updateData, { transaction });
    
    // 如果更新参与者
    if (participants && participants.length > 0) {
      // 删除原有参与者
      await teachingReformProjectParticipantsModel.destroy({
        where: { projectId: id },
        transaction
      });
      
      // 创建新参与者
      await Promise.all(participants.map(async (p) => {
        return await teachingReformProjectParticipantsModel.create({
          id: uuidv4(),
          projectId: id,
          userId: p.userId,
          allocationRatio: parseFloat(p.allocationRatio), // 直接使用前端传递的小数值(0-1)
          isLeader: !!p.isLeader
        }, { transaction });
      }));
    }
    
    // 处理文件关联
    let processedFileIds = [];
    let fileIdArray = [];
    
    // 解析文件ID数组
    if (fileIds) {
      if (typeof fileIds === 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
    } else {
      fileIdArray = [];
    }
    
    // 解析attachmentUrl
    let attachmentUrlArray = [];
    if (attachmentUrl) {
      if (typeof attachmentUrl === 'string') {
        try {
          attachmentUrlArray = JSON.parse(attachmentUrl);
        } catch (error) {
          console.error('解析文件路径出错:', error);
          attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
        }
      } else if (Array.isArray(attachmentUrl)) {
        attachmentUrlArray = attachmentUrl;
      }
    }
    
    // 获取当前项目关联的所有未删除文件
    const existingFiles = await fileModel.findAll({
      where: {
        projectId: id,
        relatedType: 'teaching_reform_projects',
        relatedId: id
      },
      attributes: ['id'],
      transaction
    });
    
    // 找出需要删除的文件ID（存在于现有文件但不在fileIds中的文件）
    if (existingFiles.length > 0) {
      const existingFileIds = existingFiles.map(file => file.id);
      const filesToDelete = existingFileIds.filter(fileId => !fileIdArray.includes(fileId));
      
      // 删除文件记录和物理文件
      if (filesToDelete.length > 0) {
        console.log(`将删除以下文件: ${filesToDelete.join(', ')}`);
        
        // 首先获取这些文件的完整信息，包括filePath
        const filesInfo = await fileModel.findAll({
          where: { 
            id: { [Op.in]: filesToDelete }
          },
          transaction
        });
        
        // 删除物理文件（记录路径以便后面删除）
        const physicalFilesToDelete = filesInfo.map(file => file.filePath).filter(Boolean);
        
        // 从数据库中删除文件记录
        await fileModel.destroy({
          where: { 
            id: { [Op.in]: filesToDelete }
          },
          transaction
        });
        
        // 添加物理文件路径到事务后要删除的文件列表中
        req.filesToDeleteAfterCommit = req.filesToDeleteAfterCommit || [];
        req.filesToDeleteAfterCommit.push(...physicalFilesToDelete);
      }
    }
    
    // 处理文件关联
    if (fileIdArray.length > 0) {
      // 为每个文件ID更新关联关系
      for (let i = 0; i < fileIdArray.length; i++) {
        const fileId = fileIdArray[i];
        const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
        
        const updateData = { 
          projectId: project.id,
          relatedId: project.id,
          relatedType: 'teaching_reform_projects'
        };
        
        // 如果存在文件路径，添加到更新数据中
        if (filePath) {
          updateData.filePath = filePath;
        }
        
        await fileModel.update(
          updateData,
          { 
            where: { id: fileId },
            transaction
          }
        );
        
        processedFileIds.push(fileId);
      }
    }
    
    // 处理新上传的文件
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        // 创建文件记录
        const fileRecord = await fileModel.create({
          id: uuidv4(),
          originalName: file.originalname,
          filePath: file.path,
          mimeType: file.mimetype,
          size: file.size,
          projectId: project.id,
          relatedId: project.id,
          relatedType: 'teaching_reform_projects'
        }, { transaction });
        
        processedFileIds.push(fileRecord.id);
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，删除标记为删除的物理文件
    if (req.filesToDeleteAfterCommit && req.filesToDeleteAfterCommit.length > 0) {
      try {
        for (const filePath of req.filesToDeleteAfterCommit) {
          if (filePath && fs.existsSync(filePath)) {
            try {
              fs.unlinkSync(filePath);
              console.log(`成功删除物理文件: ${filePath}`);
            } catch (deleteError) {
              console.error(`删除物理文件失败: ${filePath}`, deleteError);
            }
          }
        }
      } catch (error) {
        console.error('删除物理文件时出错:', error);
      }
    }
    
    // 事务提交成功后，异步移动文件到指定的项目目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'teaching_reform_projects'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${project.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: project.id,
                relatedId: project.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新项目的attachmentUrl为项目文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            const projectFolderPath = `uploads\\teaching_reform_projects\\${project.id}\\`;
            
            await project.update({
              attachmentUrl: projectFolderPath
            });
            console.log(`已更新项目 ${project.id} 的attachmentUrl为: ${projectFolderPath}`);
          } catch (updateError) {
            console.error('更新项目attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响项目更新的返回结果，仅记录错误
        console.error('移动文件到项目目录失败:', moveError);
      }
    }
    
    // 查询更新后的项目详情
    const updatedProject = await teachingReformProjectModel.findByPk(id, {
      include: [
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score']
        },
        {
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'nickname', 'username', 'studentNumber']
            }
          ]
        }
      ]
    });
    
    // 查询关联的文件
    const files = await fileModel.findAll({
      where: {
        projectId: id
      }
    });
    
    // 添加文件信息到响应数据
    const responseData = updatedProject.toJSON();
    responseData.attachments = files.map(file => file.toJSON());
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: responseData
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('更新项目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新项目失败',
      error: error.message
    });
  }
};

/**
 * 删除教学改革项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteProject = async (req, res) => {
  // 获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(teachingReformProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();
  
  try {
    const { id } = req.params;
    console.log("req.body===",req.body);
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    // 查询项目是否存在
    const project = await teachingReformProjectModel.findByPk(id, { 
      include: [
        {
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          required: false
        },
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          required: false
        }
      ],
      transaction 
    });
    
    if (!project) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到项目',
        data: null
      });
    }
    
    // 权限检查：管理员或者项目的参与者可以删除
    const userInfo = await getUserInfoFromRequest(req);
    const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');
    
    // 检查用户是否是项目参与者
    const isParticipant = await teachingReformProjectParticipantsModel.findOne({
      where: { 
        projectId: id,
        userId: userInfo.id
      },
      transaction
    });
    
    if (!isAdmin && !isParticipant) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限删除该项目',
        data: null
      });
    }
    
    // 如果项目已审核，需要更新用户排名
    if (project.ifReviewer == 1) {
      try {
        console.log('开始处理删除项目的排名更新，项目ID:', id);
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("teachingReformProjects");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));
        
        // 判断项目是否在时间区间内（使用approvalDate判断）
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(project.approvalDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        console.log('项目时间范围状态:', isInTimeRange ? '在范围内' : '不在范围内');
        
        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        console.log('需要更新的排名表:', rankingTables);
        
        // 获取项目的级别分数
        const baseScore = project.level ? project.level.score : 0;
        console.log('项目级别分数:', baseScore);
        
        // 获取项目的所有参与者
        const participants = project.participants || [];
        console.log('项目参与者数量:', participants.length);
        
        // 如果有参与者，批量处理减分操作
        if (participants.length > 0) {
          // 准备批量更新的数据
          const userIds = [];
          const scores = [];
          
          // 收集所有参与者数据
          for (const participant of participants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = baseScore * ratio;
            
            userIds.push(userId);
            scores.push(participantScore);
          }
          
          console.log('删除项目的参与者数据:', {
            userIds: userIds,
            scores: scores
          });
          
          // 创建固定值为1的计数数组，表示每个用户项目数-1
          const countDeltas = Array(userIds.length).fill(1);
          
          // 对每个排名表执行减分操作（一次性批量处理所有参与者）
          for (const table of rankingTables) {
            console.log(`为被删除项目的所有参与者更新排名表 ${table}`);
            await updateUserRankings(
              userIds,
              table,
              'teachingReformProjects',
              countDeltas, // 使用固定值1表示项目计数-1
              scores, // 使用参与者分数数组
              transaction,
              "subtract" // 减分操作
            );
          }
          
          console.log('成功从排名表中减去教学改革项目参与者的分数和项目数');
        }
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 收集项目文件夹路径
    let projectFolderPaths = new Set();
    
    // 处理项目附件 - 检查是否有附件模型并进行处理
    try {
      const projectFiles = await fileModel.findAll({
        where: { 
          projectId: id
        },
        transaction
      });
      
      if (projectFiles && projectFiles.length > 0) {
        console.log(`找到${projectFiles.length}个与项目关联的文件记录`);
        
        // 从文件路径中提取项目文件夹路径
        for (const file of projectFiles) {
          if (file.filePath) {
            // 从filePath中提取项目文件夹路径
            const folderPath = file.filePath.substring(0, file.filePath.lastIndexOf('/'));
            if (folderPath.includes('teaching_reform_projects')) {
              projectFolderPaths.add(folderPath);
            }
          }
          
          // 将文件标记为已删除
          await file.destroy({ transaction });
        }
      } else {
        console.log('未找到与项目关联的文件记录');
      }
    } catch (attachmentError) {
      console.warn('处理项目文件时出错:', attachmentError);
      // 继续执行，不因附件删除失败而中断整个操作
    }
    
    // 删除项目参与者
    await teachingReformProjectParticipantsModel.destroy({
      where: { projectId: id },
      transaction
    });
    
    // 标记项目为已删除，而不是物理删除
    await project.destroy({ transaction });
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，执行文件系统删除操作（这些操作不能回滚，所以放在事务之外）
    try {
      // 直接使用项目ID构造项目文件夹路径
      const projectFolderPath = `uploads/teaching_reform_projects/${id}`;
      console.log(`尝试删除项目文件夹: ${projectFolderPath}`);
      
      // 如果fileController中有deleteDirectoryUtil方法，使用它删除文件夹
      if (fileController && typeof fileController.deleteDirectoryUtil === 'function') {
        await fileController.deleteDirectoryUtil(projectFolderPath);
      } else {
        // 否则使用fs模块直接删除
        if (fs.existsSync(projectFolderPath)) {
          fs.rmdirSync(projectFolderPath, { recursive: true });
          console.log(`成功删除项目文件夹: ${projectFolderPath}`);
        }
      }
    } catch (fsError) {
      console.warn('删除文件系统中的文件时出错:', fsError);
      // 数据库事务已提交成功，文件系统操作失败不影响API返回结果
    }
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('删除项目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除项目失败',
      error: error.message
    });
  }
};

/**
 * 审核教学改革项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewProject = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(teachingReformProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id, reviewStatus, reviewComment, reviewer } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    if (reviewStatus === undefined) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少审核状态',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找项目
    const project = await teachingReformProjectModel.findByPk(id, { transaction });
    
    if (!project) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }
    
    // 权限检查 - 只有管理员可以审核
    const hasPermission = userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER';
    
    if (!hasPermission) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核项目',
        data: null
      });
    }
    
    // 更新审核状态
    // ifReviewer: null/undefined=待审核, 0=拒绝, 1=同意审核
    const updateData = {
      ifReviewer: reviewStatus,
      reviewedAt: new Date(),
      reviewerId: reviewer || userInfo.id
    };
    
    // 添加审核意见（如果有）
    if (reviewComment !== undefined) {
      updateData.reviewComment = reviewComment;
    }
    
    await project.update(updateData, { transaction });
    
    // 项目审核通过后，更新用户排名数据
    if (reviewStatus === 1) {
      try {
        // 获取项目级别对应的分数
        const projectLevel = await teachingReformProjectLevelsModel.findByPk(project.levelId, { transaction });
        if (!projectLevel) {
          console.error(`未找到项目级别信息，levelId: ${project.levelId}`);
          throw new Error('未找到项目级别信息');
        }
        
        const baseScore = projectLevel.score || 0;
        
        // 获取项目所有参与者及其分配比例
        const participants = await teachingReformProjectParticipantsModel.findAll({
          where: { projectId: id },
          transaction
        });
        
        if (participants && participants.length > 0) {
          // 准备用户ID数组和得分数组
          const participantUserIds = [];
          const participantScores = [];
          
          // 计算每个参与者的得分
          for (const participant of participants) {
            const userId = participant.userId;
            const allocationRatio = parseFloat(participant.allocationRatio) || 0;
            
            // 计算该参与者应得的分数 = 项目基础分 * 分配比例
            const userScore = baseScore * allocationRatio;
            
            participantUserIds.push(userId);
            participantScores.push(userScore);
          }
          
          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("teachingReformProjects");
          
          // 判断项目是否在时间区间内
          const isInTimeRange = timeInterval && project.approvalDate ? 
            isProjectInTimeRange(project.approvalDate, timeInterval.startTime, timeInterval.endTime) : 
            false;
          
          console.log(`教学改革项目ID ${id} 是否在统计时间区间内: ${isInTimeRange}`);
          console.log(`项目参与者数量: ${participantUserIds.length}, 基础分数: ${baseScore}`);
          
          // 根据项目是否在时间区间内，更新不同的排名表
          let rankingTables = [];
          
          if (isInTimeRange) {
            // 在区间内：更新范围内表和全部表
            rankingTables = [
              'user_ranking_reviewed_in', 
              'user_ranking_reviewed_all'
            ];
            console.log(`更新范围内排名表和全部排名表`);
          } else {
            // 不在区间内：更新范围外表和全部表
            rankingTables = [
              'user_ranking_reviewed_out', 
              'user_ranking_reviewed_all'
            ];
            console.log(`更新范围外排名表和全部排名表`);
          }
          
          try {
            for (const table of rankingTables) {
              // 更新所有参与者的排名数据：每人计数+1，分数增加各自的得分
              await updateUserRankings(
                participantUserIds,          // 所有参与者的用户ID数组
                table,                       // 表名
                'teachingReformProjects',           // 类型名
                Array(participantUserIds.length).fill(1), // 每个参与者计数+1
                participantScores,           // 每个参与者的得分数组
                transaction,                 // 传递事务对象
                "add"                        // 操作类型：加分
              );
            }
          } catch (rankingError) {
            console.error('更新排名表失败:', rankingError);
            // 所有排名更新错误都应该回滚事务
            await transaction.rollback();
            throw new Error(`更新排名失败: ${rankingError.message}`);
          }
        } else {
          console.log(`项目ID ${id} 没有参与者，无需更新排名`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 检查是否已经回滚，如果没有则回滚事务
        if (!error.message || !error.message.includes('更新排名失败')) {
          await transaction.rollback();
          throw error; // 将错误向上传播
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('审核项目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '审核失败',
      error: error.message
    });
  }
};

/**
 * 获取项目时间分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTimeDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus } = req.body;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingReformProjects");
    
    // 构建查询条件
    let whereCondition = {};
    
    // 更新审核状态筛选逻辑，对应四个选项：全部、已通过、已拒绝、待审核
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    }
    // reviewStatus === 'all' 不添加筛选条件
    
    // 根据range参数筛选数据 - 添加到查询条件中
    if (range === 'in' && timeInterval) {
      // 在时间区间内的项目
      whereCondition.approvalDate = { 
        ...(whereCondition.approvalDate || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的项目
      whereCondition.approvalDate = { 
        ...(whereCondition.approvalDate || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    let projectIds = [];
    
    // 如果提供了userId，只统计该用户参与的项目
    if (userId) {
      const participations = await teachingReformProjectParticipantsModel.findAll({
        where: { userId },
        attributes: ['projectId']
      });
      
      projectIds = participations.map(p => p.projectId);
      
      if (projectIds.length === 0) {
        // 该用户没有参与项目
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
      
      whereCondition.id = { [Op.in]: projectIds };
    }
    
    // 查询所有项目
    const projects = await teachingReformProjectModel.findAll({
      where: whereCondition,
      attributes: ['id', 'approvalDate', 'startYear', 'ifReviewer']
    });
    
    // 初始化年份数据
    const yearData = {};
    
    // 统计项目时间分布
    projects.forEach(project => {
      // 获取项目年份，优先使用approvalDate，其次使用startYear
      let year = null;
      
      if (project.approvalDate) {
        year = new Date(project.approvalDate).getFullYear().toString();
      } else if (project.startYear) {
        year = project.startYear.toString();
      }
      
      if (!year) return;
      
      yearData[year] = (yearData[year] || 0) + 1;
    });
    
    // 转换为前端期望的格式：[{year, count}]
    const result = Object.entries(yearData).map(([year, count]) => ({ year, count }));
    
    // 按年份排序
    result.sort((a, b) => parseInt(a.year) - parseInt(b.year));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取项目时间分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目时间分布数据失败',
      error: error.message
    });
  }
};

/**
 * 导入教学改革项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importProjects = async (req, res) => {
  try {
    const { projects } = req.body;
    
    if (!projects || !Array.isArray(projects) || projects.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请提供有效的项目数据',
        data: null
      });
    }
    
    // 导入结果统计
    const results = {
      success: 0,
      failed: 0,
      failedItems: []
    };
    
    // 逐个处理项目数据
    for (const projectData of projects) {
      try {
        // 检查必填字段
        if (!projectData.projectName || !projectData.levelId) {
          results.failed++;
          results.failedItems.push({
            data: projectData,
            reason: '项目名称或级别为空'
          });
          continue;
        }
        
        // 检查级别是否存在
        const level = await teachingReformProjectLevelsModel.findByPk(projectData.levelId);
        if (!level) {
          // 尝试通过级别名称查找级别
          if (projectData.levelName) {
            const levelByName = await teachingReformProjectLevelsModel.findOne({
              where: { levelName: projectData.levelName }
            });
            
            if (levelByName) {
              projectData.levelId = levelByName.id;
            } else {
              results.failed++;
              results.failedItems.push({
                data: projectData,
                reason: '无效的项目级别'
              });
              continue;
            }
          } else {
            results.failed++;
            results.failedItems.push({
              data: projectData,
              reason: '无效的项目级别'
            });
            continue;
          }
        }
        
        // 检查并处理参与者
        if (!projectData.participants || !Array.isArray(projectData.participants) || projectData.participants.length === 0) {
          results.failed++;
          results.failedItems.push({
            data: projectData,
            reason: '缺少参与者'
          });
          continue;
        }
        
        // 验证参与者
        const participantUserIds = projectData.participants.map(p => p.userId);
        const users = await userModel.findAll({
          where: { id: { [Op.in]: participantUserIds } }
        });
        
        if (users.length !== participantUserIds.length) {
          // 尝试通过工号查找用户
          const validParticipants = [];
          
          for (const p of projectData.participants) {
            if (p.userId) {
              const user = users.find(u => u.id === p.userId);
              if (user) {
                validParticipants.push({
                  ...p,
                  userId: user.id
                });
                continue;
              }
            }
            
            if (p.staffId) {
              const user = await userModel.findOne({
                where: { staffId: p.staffId }
              });
              
              if (user) {
                validParticipants.push({
                  ...p,
                  userId: user.id
                });
              }
            }
          }
          
          if (validParticipants.length === 0) {
            results.failed++;
            results.failedItems.push({
              data: projectData,
              reason: '无效的参与者'
            });
            continue;
          }
          
          projectData.participants = validParticipants;
        }
        
        // 验证分配比例总和为1 (0-1的小数形式)
        const totalRatio = projectData.participants.reduce((sum, p) => sum + parseFloat(p.allocationRatio || 0), 0);
        if (Math.abs(totalRatio - 1) > 0.01) {  // 允许0.01的误差
          results.failed++;
          results.failedItems.push({
            data: projectData,
            reason: '参与者分配比例总和必须为1'
          });
          continue;
        }
        
        // 创建项目
        const projectId = uuidv4();
        await teachingReformProjectModel.create({
          id: projectId,
          projectNumber: projectData.projectNumber,
          projectName: projectData.projectName,
          approvalDepartment: projectData.approvalDepartment,
          approvalDate: projectData.approvalDate,
          approvalFund: projectData.approvalFund,
          levelId: projectData.levelId,
          startYear: projectData.startYear,
          endYear: projectData.endYear,
          remark: projectData.remark,
          reviewerId: null,
          ifReviewer: false
        });
        
        // 创建参与者
        await Promise.all(projectData.participants.map(async (p) => {
          return await teachingReformProjectParticipantsModel.create({
            id: uuidv4(),
            projectId,
            userId: p.userId,
            allocationRatio: parseFloat(p.allocationRatio), // 直接使用前端传递的小数值(0-1)
            isLeader: !!p.isLeader
          }, { transaction });
        }));
        
        results.success++;
      } catch (err) {
        console.error('导入项目失败:', err);
        results.failed++;
        results.failedItems.push({
          data: projectData,
          reason: err.message
        });
      }
    }
    
    return res.status(200).json({
      code: 200,
      message: `导入完成，成功: ${results.success}，失败: ${results.failed}`,
      data: results
    });
  } catch (error) {
    console.error('导入项目批处理失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导入项目批处理失败',
      error: error.message
    });
  }
};

/**
 * 导出教学改革项目
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportProjects = async (req, res) => {
  try {
    const { 
      userId, 
      status, 
      levelId, 
      startDate, 
      endDate, 
      fileName = '教学改革项目数据.xlsx',
      reviewStatus
    } = req.query;
    
    // 构建查询条件
    const whereCondition = {};
    
    if (userId) {
      // 查询用户参与的项目ID列表
      const participations = await teachingReformProjectParticipantsModel.findAll({
        where: { userId },
        attributes: ['projectId']
      });
      
      const projectIds = participations.map(p => p.projectId);
      
      if (projectIds.length === 0) {
        // 没有项目，返回空数据
        return res.status(200).send([]);
      }
      
      whereCondition.id = { [Op.in]: projectIds };
    }
    
    // 根据状态过滤
    if (status) {
      whereCondition.status = status;
    }
    
    // 根据级别过滤
    if (levelId) {
      whereCondition.levelId = levelId;
    }
    
    // 根据审核状态过滤
    if (reviewStatus !== undefined) {
      whereCondition.ifReviewer = reviewStatus === 'reviewed' ? true : false;
    }
    
    // 根据日期范围过滤
    if (startDate && endDate) {
      whereCondition.approvalDate = {
        [Op.between]: [startDate, endDate]
      };
    } else if (startDate) {
      whereCondition.approvalDate = {
        [Op.gte]: startDate
      };
    } else if (endDate) {
      whereCondition.approvalDate = {
        [Op.lte]: endDate
      };
    }
    
    // 查询所有符合条件的项目
    const projects = await teachingReformProjectModel.findAll({
      where: whereCondition,
      include: [
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score']
        },
        {
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'nickname', 'username', 'studentNumber']
            }
          ]
        }
      ]
    });
    
    // 格式化导出数据
    const exportData = projects.map(project => {
      const projectData = project.toJSON();
      
      return {
        id: projectData.id,
        projectNumber: projectData.projectNumber,
        projectName: projectData.projectName,
        approvalDepartment: projectData.approvalDepartment,
        approvalDate: projectData.approvalDate,
        approvalFund: projectData.approvalFund,
        levelId: projectData.level.id,
        levelName: projectData.level.levelName,
        score: projectData.level.score,
        startYear: projectData.startYear,
        endYear: projectData.endYear,
        remark: projectData.remark,
        ifReviewer: projectData.ifReviewer,
        participants: projectData.participants.map(p => ({
          userId: p.user.id,
          name: p.user.nickname || p.user.username,
          username: p.user.username,
          studentNumber: p.user.studentNumber,
          allocationRatio: p.allocationRatio * 100, // 将小数形式(0-1)转换为百分比形式(0-100)
          isLeader: p.isLeader
        }))
      };
    });
    
    return res.status(200).json({
      code: 200,
      message: '导出成功',
      data: exportData
    });
  } catch (error) {
    console.error('导出项目失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出项目失败',
      error: error.message
    });
  }
};

/**
 * 获取项目级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus } = req.body;
    
    // 构建查询条件
    let whereCondition = {};
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingReformProjects");
    
    // 更新审核状态筛选逻辑，对应四个选项：全部、已通过、已拒绝、待审核
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    }
    // reviewStatus === 'all' 不添加筛选条件
    
    // 根据range参数筛选数据 - 添加到查询条件中
    if (range === 'in' && timeInterval) {
      // 在时间区间内的项目
      whereCondition.approvalDate = { 
        ...(whereCondition.approvalDate || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的项目
      whereCondition.approvalDate = { 
        ...(whereCondition.approvalDate || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    let projectIds = [];
    
    // 如果提供了userId，只统计该用户参与的项目
    if (userId) {
      const participations = await teachingReformProjectParticipantsModel.findAll({
        where: { userId },
        attributes: ['projectId']
      });
      
      projectIds = participations.map(p => p.projectId);
      
      if (projectIds.length === 0) {
        // 该用户没有参与项目
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
      
      whereCondition.id = { [Op.in]: projectIds };
    }
    
    // 查询所有项目
    const projects = await teachingReformProjectModel.findAll({
      where: whereCondition,
      include: [
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName'],
          required: true,
        },
      ]
    });
    
    // 初始化级别数据
    const levelData = {};
    
    // 统计项目级别分布
    projects.forEach(project => {
      const projectJson = project.toJSON();
      const levelName = projectJson.level.levelName;
      levelData[levelName] = (levelData[levelName] || 0) + 1;
    });
    
    // 转换为前端期望的格式：[{levelName, count}]
    const result = Object.entries(levelData).map(([levelName, count]) => ({ levelName, count }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取项目级别分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目级别分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取教师项目排名数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherProjectRanking = async (req, res) => {
  try {
    console.log('获取教师项目排名，参数:', req.body);
    const { 
      range = 'all', 
      reviewStatus = 'all',
      nickname = '',
      page = 1, 
      pageSize,
      isExport = false 
    } = req.body;
    const limit = pageSize != null ? pageSize : 10;
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(limit);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 根据range选择对应的排名表模型
    let RankingModel;
    switch(range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }
    
    // 查询条件：按教学项目总分降序排序
    const queryOptions = {
      order: [['teachingProjectScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'teachingProjectCount',
        'teachingProjectScore'
      ]
    };
    
    // 如果提供了昵称，添加筛选条件
    if (nickname) {
      queryOptions.where = {
        nickName: {
          [Op.like]: `%${nickname}%`
        }
      };
    }

    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }
    
    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);
    
    // 格式化返回数据
    const formattedResults = rows.map((item, index) => ({
      rank: index + 1 + offset,
      userId: item.userId,
      userName: item.nickName,
      studentNumber: item.studentNumber,
      totalProjects: item.teachingProjectCount || 0,
      totalScore: parseFloat(item.teachingProjectScore || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: formattedResults,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取教师项目排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师项目排名失败',
      error: error.message
    });
  }
};

/**
 * 获取教师项目详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherProjectDetails = async (req, res) => {
  try {
    const { userId, page = 1, pageSize = 10, range = 'all', reviewStatus } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }
    
    // 查询该用户参与的项目ID
    const participations = await teachingReformProjectParticipantsModel.findAll({
      where: { userId },
      attributes: ['projectId', 'allocationRatio', 'isLeader']
    });
    
    const projectIds = participations.map(p => p.projectId);
    
    if (projectIds.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          list: [],
          totalScore: 0
        }
      });
    }
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingReformProjects");
    
    // 构建项目查询条件
    let whereCondition = {
      id: { [Op.in]: projectIds }
    };
    
    // 根据审核状态过滤
    if (reviewStatus !== undefined && reviewStatus !== 'all') {
      whereCondition.ifReviewer = reviewStatus === 'reviewed' ? true : false;
    }
    
    // 根据range参数筛选数据 - 添加到查询条件中
    if (range === 'in' && timeInterval) {
      // 在时间区间内的项目
      whereCondition.approvalDate = { 
        ...(whereCondition.approvalDate || {}), 
        [Op.gte]: timeInterval.startTime,
        [Op.lte]: timeInterval.endTime
      };
    } else if (range === 'out' && timeInterval) {
      // 不在时间区间内的项目
      whereCondition.approvalDate = { 
        ...(whereCondition.approvalDate || {}), 
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }
    // range === 'all' 不添加筛选条件
    
    // 查询项目列表
    const projects = await teachingReformProjectModel.findAll({
      where: whereCondition,
      include: [
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
        }
      ],
      order: [['approvalDate', 'DESC']]
    });
    
    // 计算总得分和处理项目数据，添加用户的贡献比例
    let totalScore = 0;
    const processedProjects = projects.map(project => {
      const projectJson = project.toJSON();
      const participation = participations.find(p => p.projectId === projectJson.id);
      
      // 计算得分贡献
      const ratio = participation ? participation.allocationRatio : 0;
      const projectScore = projectJson.level?.score || 0;
      const score = projectScore * ratio;
      
      totalScore += score;
      
      // 添加isInTimeRange字段以便前端展示
      let isInTimeRange = false;
      if (timeInterval && projectJson.approvalDate) {
        isInTimeRange = isProjectInTimeRange(projectJson.approvalDate, timeInterval.startTime, timeInterval.endTime);
      }
      
      return {
        ...projectJson,
        allocationRatio: participation ? participation.allocationRatio : 0,
        isLeader: participation ? participation.isLeader : false,
        contributionScore: score,
        isInTimeRange
      };
    });
    
    // 分页处理
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedProjects = processedProjects.slice(startIndex, endIndex);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: paginatedProjects,
        totalScore: totalScore,
        total: processedProjects.length,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  } catch (error) {
    console.error('获取教师项目详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师项目详情失败',
      error: error.message
    });
  }
};

/**
 * 获取项目统计概览数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectStatistics = async (req, res) => {
  try {
    const { userId } = req.body;
    
    // 构建查询条件
    let whereCondition = {};
    let projectIds = [];
    
    // 如果提供了userId，只统计该用户参与的项目
    if (userId) {
      const participations = await teachingReformProjectParticipantsModel.findAll({
        where: { userId },
        attributes: ['projectId']
      });
      
      projectIds = participations.map(p => p.projectId);
      
      if (projectIds.length === 0) {
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: {
            totalProjects: 0,
            activeProjects: 0,
            averageScore: 0,
            reviewedRate: 0
          }
        });
      }
      
      whereCondition.id = { [Op.in]: projectIds };
    }
    
    // 查询所有项目
    const projects = await teachingReformProjectModel.findAll({
      where: whereCondition,
      include: [
        {
          model: teachingReformProjectLevelsModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
        }
      ]
    });
    
    // 统计数据
    const totalProjects = projects.length;
    
    // 获取当前时间
    const now = new Date();
    const currentYear = now.getFullYear();
    
    // 统计活跃项目（执行年份包含当前年份的项目）
    const activeProjects = projects.filter(project => {
      return project.startYear <= currentYear && project.endYear >= currentYear;
    }).length;
    
    // 计算平均分数
    const totalScore = projects.reduce((sum, project) => {
      return sum + (project.level?.score || 0);
    }, 0);
    const averageScore = totalProjects > 0 ? totalScore / totalProjects : 0;
    
    // 统计已审核的比例
    const reviewedProjects = projects.filter(project => project.ifReviewer).length;
    const reviewedRate = totalProjects > 0 ? reviewedProjects / totalProjects : 0;
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalProjects,
        activeProjects,
        averageScore,
        reviewedRate,
        reviewedProjects,
        totalScore
      }
    });
  } catch (error) {
    console.error('获取项目统计概览失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目统计概览失败',
      error: error.message
    });
  }
};

/**
 * 获取教学改革项目总分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectsTotalScore = async (req, res) => {
  try {
    const { range = 'all', reviewStatus = 'all' } = req.body;

    // 查找数据库连接，通过已有模型获取sequelize实例
    let sequelize;
    if (teachingReformProjectModel.sequelize) {
      sequelize = teachingReformProjectModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }

    let userId = null; 
    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    // 调用存储过程获取教学改革项目总分统计
    const results = await sequelize.query(
      'CALL get_teaching_reform_projects_total_score(?, ?, ?)',
      {
        replacements: [range || 'all', reviewStatus || 'all', userId],
        type: sequelize.QueryTypes.RAW,
        raw: true,
        nest: true
      }
    );

    console.log('存储过程原始返回结果:', JSON.stringify(results));

    // 处理存储过程的结果
    let levelStats = [];
    let overallStats = { totalProjects: 0, totalScore: 0 };
    let timeInterval = null;

    // 根据实际返回结果格式处理数据
    if (Array.isArray(results) && results.length > 0) {
      // 检查返回的是单层数组(直接就是结果数组)还是嵌套数组(多个结果集)
      const isNestedArray = Array.isArray(results[0]) && !results[0].hasOwnProperty('levelId') && !results[0].hasOwnProperty('levelName');
      
      if (isNestedArray) {
        // 处理嵌套数组格式 (传统格式)
        // 第一个结果集：按级别统计
        if (results[0] && Array.isArray(results[0])) {
          levelStats = results[0].map(item => ({
            levelId: item.levelId,
            levelName: item.levelName,
            count: parseInt(item.count || 0),
            totalScore: parseFloat(item.totalScore || 0)
          }));
        }

        // 第二个结果集：总体统计
        if (results.length > 1 && Array.isArray(results[1]) && results[1].length > 0) {
          overallStats = {
            totalProjects: parseInt(results[1][0].totalProjects || 0),
            totalScore: parseFloat(results[1][0].totalScore || 0)
          };
        }

        // 第三个结果集：时间区间
        if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
          timeInterval = {
            startTime: results[2][0].startTime,
            endTime: results[2][0].endTime,
            name: results[2][0].name
          };
        }
      } else {
        // 处理单层数组格式 (当前情况)
        // 将results直接作为levelStats使用
        levelStats = results.map(item => ({
          levelId: item.levelId,
          levelName: item.levelName,
          count: parseInt(item.count || 0),
          totalScore: parseFloat(item.totalScore || 0)
        }));
        
        // 根据levelStats计算总体统计
        if (levelStats.length > 0) {
          overallStats = {
            totalProjects: levelStats.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0),
            totalScore: parseFloat(
              levelStats.reduce((sum, item) => sum + (parseFloat(item.totalScore) || 0), 0).toFixed(2)
            )
          };
        }
      }
    }

    // 如果没有获取到时间区间信息，则尝试使用工具函数获取
    if (!timeInterval) {
      try {
        timeInterval = await getTimeIntervalByName("teachingReformProjects");
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        levelStats,
        overallStats,
        timeInterval
      }
    });
  } catch (error) {
    console.error('获取教学改革项目总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教学改革项目总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户教学改革项目详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserProjectsDetail = async (req, res) => {
  try {
    const { 
      userId, 
      range = 'all', 
      reviewStatus = 'all', 
      page = 1, 
      pageSize = 10 
    } = req.body;

    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }

    // 查询用户是否存在
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 查找数据库连接，通过已有模型获取sequelize实例
    let sequelize;
    if (teachingReformProjectModel.sequelize) {
      sequelize = teachingReformProjectModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }

    // 调用存储过程获取用户教学改革项目详情
    const results = await sequelize.query(
      'CALL get_user_teaching_reform_projects_detail(?, ?, ?, ?, ?)',
      {
        replacements: [
          userId,
          range || 'all',
          reviewStatus || 'all',
          parseInt(pageSize),
          parseInt(page)
        ],
        type: sequelize.QueryTypes.RAW,
        raw: true,
        nest: true
      }
    );

    // 处理存储过程的结果
    let totalCount = 0;
    let projects = [];
    let stats = {};
    let timeInterval = {};

    // 使用工具函数或直接处理结果
    const resultSets = Array.isArray(results) ? results : [];

    // 第一个结果集：总记录数
    if (resultSets.length > 0 && Array.isArray(resultSets[0]) && resultSets[0].length > 0) {
      totalCount = parseInt(resultSets[0][0].totalCount || 0);
    }

    // 第二个结果集：项目详情列表
    if (resultSets.length > 1 && Array.isArray(resultSets[1])) {
      projects = resultSets[1].map(item => ({
        projectId: item.projectId,
        projectNumber: item.projectNumber,
        projectName: item.projectName,
        levelId: item.levelId,
        levelName: item.levelName,
        approvalDate: item.approvalDate,
        approvalDepartment: item.approvalDepartment,
        approvalFund: parseFloat(item.approvalFund || 0),
        startYear: item.startYear,
        endYear: item.endYear,
        baseScore: parseFloat(item.baseScore || 0),
        allocationRatio: parseFloat(item.allocationRatio || 0),
        isLeader: item.isLeader === 1,
        actualScore: parseFloat(item.actualScore || 0).toFixed(2),
        remark: item.remark,
        ifReviewer: item.ifReviewer,
        reviewStatus: item.reviewStatus,
        reviewerName: item.reviewerName,
        reviewComment: item.reviewComment,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));
    }

    // 第三个结果集：统计数据
    if (resultSets.length > 2 && Array.isArray(resultSets[2]) && resultSets[2].length > 0) {
      stats = {
        totalProjects: parseInt(resultSets[2][0].totalProjects || 0),
        leaderProjectCount: parseInt(resultSets[2][0].leaderProjectCount || 0),
        participantProjectCount: parseInt(resultSets[2][0].participantProjectCount || 0),
        totalScore: parseFloat(resultSets[2][0].totalScore || 0).toFixed(2)
      };
    }

    // 第四个结果集：时间区间
    if (resultSets.length > 3 && Array.isArray(resultSets[3]) && resultSets[3].length > 0) {
      timeInterval = {
        startTime: resultSets[3][0].startTime,
        endTime: resultSets[3][0].endTime,
        name: resultSets[3][0].name
      };
    } else {
      // 如果没有第四个结果集，尝试获取时间区间
      timeInterval = await getTimeIntervalByName("teachingReformProjects");
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: projects,
        stats,
        timeInterval,
        user: {
          id: user.id,
          name: user.nickname || user.username,
          employeeNumber: user.studentNumber
        },
        pagination: {
          total: totalCount,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(totalCount / parseInt(pageSize))
        }
      }
    });
  } catch (error) {
    console.error('获取用户教学改革项目详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户教学改革项目详情失败',
      error: error.message
    });
  }
};

/**
 * 重新提交教学改革项目审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapply = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(teachingReformProjectModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少项目ID',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找项目
    const project = await teachingReformProjectModel.findByPk(id, { transaction });
    
    if (!project) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }
    
    // 检查项目所有权或管理员权限
    const isOwner = project.userId === userInfo.id;
    const isAdmin = userInfo.role.roleAuth === 'TEACHER-LV1' || userInfo.role.roleAuth === 'SUPER' || userInfo.role.roleAuth === 'ADMIN-LV2';
    
    if (!isOwner && !isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限重新提交该项目',
        data: null
      });
    }
    
    // 检查当前审核状态，只有被拒绝的项目可以重新提交
    if (project.ifReviewer != 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '只有被拒绝的项目可以重新提交审核',
        data: null
      });
    }
    
    // 更新项目状态为待审核
    await project.update({
      ifReviewer: null,  // 设置为待审核状态
      reviewComment: null, // 清空之前的审核意见
      reviewerId: null // 清空之前的审核人
    }, { transaction });
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();

    console.error('重新提交审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '重新提交审核失败',
      error: error.message
    });
  }
};

/**
 * 获取审核状态概览
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReviewStatusOverview = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingReformProjects");
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const whereCondition = {};

    // 时间范围筛选
    if (range === 'in' && timeInterval) {
      whereCondition.approvalDate = {
        [Op.between]: [timeInterval.startTime, timeInterval.endTime]
      };
    } else if (range === 'out' && timeInterval) {
      whereCondition.approvalDate = {
        [Op.or]: [
          { [Op.lt]: timeInterval.startTime },
          { [Op.gt]: timeInterval.endTime }
        ]
      };
    }

    // 构建用户筛选条件
    let userCondition = {};
    if (userId) {
      userCondition = {
        include: [{
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          where: { userId: userId },
          required: true
        }]
      };
    } else if (userInfo.role.roleAuth === 'TEACHER-LV1') {
      userCondition = {
        include: [{
          model: teachingReformProjectParticipantsModel,
          as: 'participants',
          where: { userId: userInfo.id },
          required: true
        }]
      };
    }

    // 查询各状态数量
    const [reviewed, pending, rejected] = await Promise.all([
      teachingReformProjectModel.count({
        where: { ...whereCondition, ifReviewer: 1 },
        ...userCondition
      }),
      teachingReformProjectModel.count({
        where: { ...whereCondition, ifReviewer: null },
        ...userCondition
      }),
      teachingReformProjectModel.count({
        where: { ...whereCondition, ifReviewer: 0 },
        ...userCondition
      })
    ]);

    const total = reviewed + pending + rejected;
    const reviewedRate = total > 0 ? ((reviewed / total) * 100).toFixed(1) : 0;

    return res.status(200).json({
      code: 200,
      message: '获取审核状态数据成功',
      data: {
        reviewed,
        pending,
        rejected,
        total,
        reviewedRate: parseFloat(reviewedRate)
      }
    });
  } catch (error) {
    console.error('获取审核状态概览失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取审核状态概览失败',
      error: error.message
    });
  }
};