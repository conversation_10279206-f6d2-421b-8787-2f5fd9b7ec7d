const express = require('express');
const router = express.Router();
const projectController = require('../../../controllers/v1/teachingReformProjects/teachingReformProjectsController');
const multer = require('multer');
const path = require('path');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建教学改革项目权限中间件函数
const projectsPermission = (action) => createModulePermission('teachingReformProjects', action);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/teaching_reform_projects/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'teaching-reform-projects-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

/**
 * 获取教学改革项目列表
 * @route POST /v1/sys/teaching-reform/projects
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {string} projectName - 项目名称（模糊搜索）
 * @param {string} levelId - 项目级别ID
 * @param {string} approvalDateStart - 获批开始日期
 * @param {string} approvalDateEnd - 获批结束日期
 * @param {string} userId - 用户ID（可选，传入则获取用户参与的项目）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 范围筛选，可选值：all, in, out
 * @param {string} reviewStatus - 审核状态筛选，可选值：all, reviewed, unreviewed
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, page: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', 
  authMiddleware, 
  projectsPermission('list'), 
  projectController.getProjects
);

/**
 * 导入项目数据
 * @route POST /v1/sys/teaching-reform/projects/import
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects/import', 
  authMiddleware, 
  projectsPermission('import'), 
  upload.single('file'), 
  projectController.importProjects
);

/**
 * 导出项目数据
 * @route POST /v1/sys/teaching-reform/projects/export
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {string} projectName - 项目名称（模糊搜索）
 * @param {string} levelId - 项目级别ID
 * @param {string} approvalDateStart - 获批开始日期
 * @param {string} approvalDateEnd - 获批结束日期
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects/export', 
  authMiddleware, 
  projectsPermission('export'), 
  projectController.exportProjects
);

/**
 * 创建项目
 * @route POST /v1/sys/teaching-reform/project/create
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {string} projectName.body.required - 项目名称
 * @param {string} approvalDepartment.body.required - 下达部门
 * @param {string} approvalDate.body.required - 获批日期
 * @param {string} levelId.body.required - 项目级别ID
 * @param {number} startYear.body.required - 执行起始年
 * @param {number} endYear.body.required - 执行结束年
 * @param {Array} participants.body.required - 参与者数组，包含userId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/create', 
  authMiddleware, 
  projectsPermission('create'), 
  upload.array('files', 5), 
  projectController.createProject
);

/**
 * 更新项目
 * @route POST /v1/sys/teaching-reform/project/update
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {string} id.body.required - 项目ID
 * @param {string} projectName.body - 项目名称
 * @param {string} approvalDepartment.body - 下达部门
 * @param {string} approvalDate.body - 获批日期
 * @param {string} levelId.body - 项目级别ID
 * @param {Array} participants.body - 参与者数组，包含userId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @param {Array} deletedFileIds.body - 要删除的文件ID数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/update/:id', 
  authMiddleware, 
  projectsPermission('update'), 
  upload.array('files', 5), 
  projectController.updateProject
);

/**
 * 删除项目
 * @route POST /v1/sys/teaching-reform/project/delete
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/project/delete/:id', 
  authMiddleware, 
  projectsPermission('delete'), 
  projectController.deleteProject
);

/**
 * 获取项目详情
 * @route POST /v1/sys/teaching-reform/project/detail
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/detail', 
  authMiddleware, 
  projectsPermission('detail'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await projectController.getProjectDetail(req, res);
  }
);

/**
 * 获取项目时间分布数据
 * @route POST /v1/sys/teaching-reform/statistics/time-distribution
 * @group 教学改革项目统计 - 教学改革项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的项目
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {years: ["YYYY",...], data: [数量,...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/time-distribution', 
  authMiddleware, 
  projectsPermission('timeDistribution'), 
  projectController.getTimeDistribution
);

/**
 * 审核项目
 * @route POST /v1/sys/teaching-reform/project/review
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/review', 
  authMiddleware, 
  projectsPermission('review'), 
  projectController.reviewProject
);

/**
 * 获取项目级别分布数据
 * @route POST /v1/sys/teaching-reform/statistics/level-distribution
 * @group 教学改革项目统计 - 教学改革项目级别分布统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} userId - 用户ID，可选
 * @param {boolean} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{levelName: '级别名称', count: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/level-distribution', 
  authMiddleware, 
  projectsPermission('levelDistribution'), 
  projectController.getLevelDistribution
);

/**
 * 获取教师项目排名数据
 * @route POST /v1/sys/teaching-reform/statistics/teacher-ranking
 * @group 教学改革项目统计 - 教师项目排名统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} reviewStatus - 审核状态: 'all', 'reviewed', 'unreviewed'
 * @param {number} page - 页码
 * @param {number} pageSize - 每页记录数
 * @param {boolean} isExport - 是否导出所有数据，导出时不应用分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{userId, userName, studentNumber, totalProjects, totalScore}], pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-ranking', 
  authMiddleware, 
  projectsPermission('teacherRanking'), 
  projectController.getTeacherProjectRanking
);

/**
 * 获取教师项目详情
 * @route POST /v1/sys/teaching-reform/statistics/teacher-projects
 * @group 教学改革项目统计 - 教师项目详情
 * @param {string} userId - 用户ID
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 数据范围
 * @param {boolean} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], totalScore: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-projects', 
  authMiddleware, 
  projectsPermission('teacherProjects'), 
  projectController.getTeacherProjectDetails
);

/**
 * 获取项目统计概览数据
 * @route POST /v1/sys/teaching-reform/statistics/overview
 * @group 教学改革项目统计 - 项目统计概览
 * @param {string} userId - 用户ID，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalProjects, activeProjects, averageScore, reviewedRate}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/overview', 
  authMiddleware, 
  projectsPermission('overview'), 
  projectController.getProjectStatistics
);

/**
 * 获取教学改革项目总分统计
 * @route POST /v1/sys/teaching-reform/statistics/projects-total-score
 * @group 教学改革项目统计 - 教学改革项目统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值：'reviewed'(已通过), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部)，默认'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {levelStats: [{levelId, levelName, count, totalScore},...], overallStats: {totalProjects, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/projects-total-score', 
  authMiddleware, 
  projectsPermission('projectsTotalScore'), 
  projectController.getProjectsTotalScore
);

/**
 * 获取用户教学改革项目详情
 * @route POST /v1/sys/teaching-reform/user/details
 * @group 教学改革项目统计 - 教学改革项目统计相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值：'reviewed'(已通过), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部)，默认'all'
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{projectId, projectName, levelName, approvalDate, baseScore, actualScore, reviewStatus...}], stats: {totalProjects, leaderProjectCount, participantProjectCount, totalScore}, user: {id, name, employeeNumber}, pagination: {total, page, pageSize, totalPages}, timeInterval}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/details', 
  authMiddleware, 
  projectsPermission('userProjectsDetail'), 
  projectController.getUserProjectsDetail
);

/**
 * 重新提交教学改革项目审核
 * @route POST /v1/sys/teaching-reform/project/reapply
 * @group 教学改革项目管理 - 教学改革项目相关接口
 * @param {string} id.body.required - 项目ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/reapply',
  authMiddleware,
  projectsPermission('reapply'),
  projectController.reapply
);

/**
 * 获取审核状态概览
 * @route POST /v1/sys/teaching-reform/statistics/review-status-overview
 * @group 教学改革项目统计 - 教学改革项目统计相关接口
 * @param {string} range.body - 查询范围：'in'|'out'|'all'，默认'all'
 * @param {string} userId.body - 用户ID，可选
 * @returns {object} 200 - 审核状态统计数据
 * @security JWT
 */
router.post('/statistics/review-status-overview',
  authMiddleware,
  projectsPermission('reviewStatusOverview'),
  projectController.getReviewStatusOverview
);

module.exports = router;