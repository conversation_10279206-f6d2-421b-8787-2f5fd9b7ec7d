/**
 * 消息通知模块权限配置
 * 简化版 - 角色验证 + 教师数据自动填充
 */
module.exports = {
  // 基本权限配置
  list: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['user_id']
    }
  },
  
  create: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以创建通知
  },
  
  update: {
    roles: ['ADMIN-LV2', 'SUPER'],
    teacherAccess: {
      enforce: true,
      params: ['user_id']
    }
  },
  
  delete: {
    roles: ['ADMIN-LV2', 'SUPER'],
    teacherAccess: {
      enforce: true,
      params: ['user_id']
    }
  },
  
  detail: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['user_id']
    }
  },
  
  // 发送通知权限
  send: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以发送通知
  },
  
  // 获取用户列表权限
  getUsers: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 所有用户都可以获取用户列表用于选择发送对象
  },
  
  // 获取部门列表权限
  getDepartments: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以获取部门列表
  },

  // 获取用户级别列表权限
  getUserLevels: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以获取用户级别列表
  },
  
  // 批量标记已读权限
  batchMarkAsRead: {
    roles: ['ADMIN-LV2', 'SUPER'],
    teacherAccess: {
      enforce: true,
      params: ['user_id']
    }
  },
  
  // 通知统计权限
  statistics: {
    roles: ['ADMIN-LV2', 'SUPER'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 通知管理权限（管理员专用）
  manage: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以管理所有通知
  },
  
  // 导出通知权限
  export: {
    roles: ['ADMIN-LV2', 'SUPER'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  }
};
