import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useNotificationStore = defineStore('notification', () => {
  // 通知列表
  const notifications = ref([
    {
      key: '1',
      title: '关于2023年度绩效评分工作的通知',
      sender: '系统管理员',
      sendTime: '2023-12-01 10:00:00',
      status: '已读',
      content: '请各位教师于12月31日前完成年度绩效评分的填报工作。',
      type: 'system'
    },
    {
      key: '2',
      title: '科研项目评分标准更新通知',
      sender: '科研处',
      sendTime: '2023-12-05 14:30:00',
      status: '未读',
      content: '自2024年1月1日起，科研项目评分标准将按照新规定执行。',
      type: 'important'
    },
    {
      key: '3',
      title: '教学与科研获奖材料提交提醒',
      sender: '教务处',
      sendTime: '2023-12-10 09:15:00',
      status: '已读',
      content: '请获得教学或科研奖项的教师于12月20日前提交相关证明材料。',
      type: 'reminder'
    },
    {
      key: '4',
      title: '国际交流活动报名通知',
      sender: '国际交流处',
      sendTime: '2023-12-15 16:45:00',
      status: '未读',
      content: '2024年度国际学术交流活动报名已开始，请有意向参加的教师登录系统进行报名。',
      type: 'normal'
    },
    {
      key: '5',
      title: '社会服务项目评分结果公示',
      sender: '社会服务部',
      sendTime: '2023-12-20 11:30:00',
      status: '未读',
      content: '2023年度社会服务项目评分结果已出，请各位教师登录系统查看。',
      type: 'result'
    }
  ])

  // 获取通知列表（实际项目中会从API获取）
  const getNotifications = async () => {
    // 这里应该是一个API调用
    // const response = await fetch('/api/notifications')
    // notifications.value = await response.json()
    
    // 此处使用模拟数据
    console.log('获取通知列表')
  }

  // 获取未读通知数量
  const unreadCount = computed(() => {
    return notifications.value.filter(item => item.status === '未读').length
  })

  // 标记为已读
  const markAsRead = (id) => {
    const notification = notifications.value.find(item => item.key === id)
    if (notification) {
      notification.status = '已读'
    }
  }

  // 标记为未读
  const markAsUnread = (id) => {
    const notification = notifications.value.find(item => item.key === id)
    if (notification) {
      notification.status = '未读'
    }
  }

  // 添加通知
  const addNotification = (notification) => {
    notifications.value.unshift({
      key: Date.now().toString(),
      status: '未读',
      sendTime: new Date().toLocaleString(),
      ...notification
    })
  }

  // 删除通知
  const removeNotification = (id) => {
    const index = notifications.value.findIndex(item => item.key === id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 全部标记为已读
  const markAllAsRead = () => {
    notifications.value.forEach(item => {
      item.status = '已读'
    })
  }

  return {
    notifications,
    unreadCount,
    getNotifications,
    markAsRead,
    markAsUnread,
    addNotification,
    removeNotification,
    markAllAsRead
  }
}) 