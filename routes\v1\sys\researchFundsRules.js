const express = require('express');
const router = express.Router();
const researchFundsRulesController = require('../../../controllers/v1/sys/researchFundsRulesController');

/**
 * 获取科研经费核算规则列表
 * @route GET /v1/sys/research-funds-rules/list
 * @group 科研经费管理 - 科研经费核算规则相关接口
 * @param {number} min_amount.query - 经费范围下限
 * @param {number} max_amount.query - 经费范围上限
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0, page: 1, pageSize: 10}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list', researchFundsRulesController.getResearchFundsRules);

/**
 * 获取科研经费核算规则详情
 * @route GET /v1/sys/research-funds-rules/detail
 * @group 科研经费管理 - 科研经费核算规则相关接口
 * @param {string} id.query.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail', researchFundsRulesController.getResearchFundsRuleDetail);

/**
 * 创建科研经费核算规则
 * @route POST /v1/sys/research-funds-rules/create
 * @group 科研经费管理 - 科研经费核算规则相关接口
 * @param {number} min_amount.body.required - 经费范围下限
 * @param {number} max_amount.body - 经费范围上限
 * @param {number} score.body - 固定核算分数
 * @param {string} score_formula.body - 分数计算公式
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', researchFundsRulesController.createResearchFundsRule);

/**
 * 更新科研经费核算规则
 * @route PUT /v1/sys/research-funds-rules/update
 * @group 科研经费管理 - 科研经费核算规则相关接口
 * @param {string} id.body.required - 规则ID
 * @param {number} min_amount.body - 经费范围下限
 * @param {number} max_amount.body - 经费范围上限
 * @param {number} score.body - 固定核算分数
 * @param {string} score_formula.body - 分数计算公式
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/update', researchFundsRulesController.updateResearchFundsRule);

/**
 * 删除科研经费核算规则
 * @route DELETE /v1/sys/research-funds-rules/delete
 * @group 科研经费管理 - 科研经费核算规则相关接口
 * @param {string} id.query.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete', researchFundsRulesController.deleteResearchFundsRule);

/**
 * 批量删除科研经费核算规则
 * @route DELETE /v1/sys/research-funds-rules/batch-delete
 * @group 科研经费管理 - 科研经费核算规则相关接口
 * @param {Array} ids.query.required - 规则ID列表
 * @returns {object} 200 - {code: 200, message: "批量删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/batch-delete', researchFundsRulesController.batchDeleteResearchFundsRules);

module.exports = router; 