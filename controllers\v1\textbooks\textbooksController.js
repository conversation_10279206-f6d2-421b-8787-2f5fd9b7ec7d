const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const { Op } = require('sequelize');
const { getTimeIntervalByName, getUserInfoFromRequest, getSequelizeInstance, isProjectInTimeRange } = require('../../../utils/others');
const textbooksModel = require('../../../models/v1/mapping/textbooksModel');
const textbookCategoriesModel = require('../../../models/v1/mapping/textbookCategoriesModel');
const userModel = require('../../../models/v1/mapping/userModel');
const fileModel = require('../../../models/v1/mapping/fileModel');
const Excel = require('exceljs');
const fileController = require('../common/fileController');
const { Sequelize } = require('sequelize');
const { updateUserRankings } = require('../../../utils/rankingUtils');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');

/**
 * 获取教材与著作列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTextbooks = async (req, res) => {
  try {
    const { 
      materialName, 
      categoryId, 
      startDate, 
      endDate, 
      userId, 
      page = 1, 
      pageSize = 10,
      range = 'all',
      reviewStatus = 'all',
      isExport = false // 新增参数：是否为导出操作
    } = req.body;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("textbooks");
    
    // 构建查询条件
    const whereCondition = {};
    
    // 根据范围参数筛选数据
    if (range == 'in' && timeInterval) {
      // 统计范围内：有效出版（与时间区间有交集）
      whereCondition.publishDate = { 
        [Op.between]: [timeInterval.startTime, timeInterval.endTime] 
      };
    } else if (range == 'out' && timeInterval) {
      // 统计范围外：无效出版（与时间区间无交集）
      whereCondition[Op.or] = [
        { publishDate: { [Op.lt]: timeInterval.startTime } },
        { publishDate: { [Op.gt]: timeInterval.endTime } }
      ];
    }
    // range == 'all' 不添加额外筛选条件
    
    // 更新审核状态筛选逻辑，对应四个选项：全部、已通过、已拒绝、待审核
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    }
    // reviewStatus == 'all' 不添加筛选条件
    
    // 添加其他筛选条件
    if (materialName) {
      whereCondition.materialName = { [Op.like]: `%${materialName}%` };
    }
    
    if (categoryId) {
      whereCondition.categoryId = categoryId;
    }
    
    if (startDate) {
      whereCondition.publishDate = { 
        ...whereCondition.publishDate,
        [Op.gte]: startDate 
      };
    }
    
    if (endDate) {
      whereCondition.publishDate = { 
        ...whereCondition.publishDate,
        [Op.lte]: endDate 
      };
    }
    
    // 如果提供了userId，添加到查询条件
    if (userId) {
      whereCondition.userId = userId;
    }
    
    // 查询选项，包含where条件和include关联
    const queryOptions = {
      where: whereCondition,
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          attributes: ['categoryAndPosition', 'score'],
          required: false
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['nickname', 'username', 'studentNumber'],
          required: false
        }
      ],
      order: [['publishDate', 'DESC']]
    };
    
    // 只有在不是导出操作时才应用分页限制
    if (!isExport) {
      // 分页参数
      const pageNum = parseInt(page);
      const pageSizeNum = parseInt(pageSize);
      const offset = (pageNum - 1) * pageSizeNum;
      const limit = pageSizeNum;
      
      queryOptions.offset = offset;
      queryOptions.limit = limit;
    }
    
    console.log('查询条件:', whereCondition);
    console.log('是否导出:', isExport);
    
    // 查询数据
    const { count, rows } = await textbooksModel.findAndCountAll(queryOptions);
    
    // 处理数据
    const processedRows = rows.map(item => {
      const itemJSON = item.toJSON();
      
      // 检查是否在统计时间范围内 - 使用isProjectInTimeRange函数
      let isInTimeRange = false;
      if (timeInterval && itemJSON.publishDate) {
        isInTimeRange = isProjectInTimeRange(
          itemJSON.publishDate,
          timeInterval.startTime,
          timeInterval.endTime
        );
      }
      
      itemJSON.isInTimeRange = isInTimeRange;
      return itemJSON;
    });
    
    // 计算总分
    const totalScore = processedRows.reduce((sum, item) => {
      return sum + (parseFloat(item.category?.score) || 0);
    }, 0);
    
    // 设置分页参数（导出时不需要分页，但需要保持响应格式一致）
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: processedRows,
        totalScore: totalScore,
        pagination: {
          total: count,
          page: pageNum,
          pageSize: pageSizeNum,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取教材与著作列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教材与著作列表失败',
      error: error.message
    });
  }
};

/**
 * 获取教材与著作详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTextbookDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少教材与著作ID',
        data: null
      });
    }
    
    // 查询教材与著作详情
    const textbook = await textbooksModel.findByPk(id, {
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          required: false
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        }
      ]
    });
    
    // 查询项目关联的文件列表
    const files = await fileModel.findAll({
      where: {
        projectId: id
      },
      attributes: [
        'id', 
        'fileName', 
        'originalName', 
        'filePath', 
        'fileSize', 
        'mimeType', 
        'extension',
        'uploaderId', 
        'relatedId', 
        'relatedType', 
        'createdAt', 
        'updatedAt'
      ],
      order: [['createdAt', 'DESC']]
    });
    
    // 将项目数据转换为JSON对象，方便后续处理
    const textbookData = textbook.toJSON();

    // 处理文件信息，添加URL和其他前端需要的信息
    textbookData.attachments = files.map(file => {
      const fileData = file.toJSON();
      // 构造文件URL
      const filePath = fileData.filePath;
      const url = filePath.startsWith('/') ? filePath : `/${filePath}`;
      
      return {
        id: fileData.id,
        name: fileData.originalName,
        fileName: fileData.fileName,
        size: fileData.fileSize,
        type: fileData.mimeType,
        extension: fileData.extension,
        url: url,
        filePath: fileData.filePath,
        uploadTime: fileData.createdAt
      };
    });
    
    if (!textbookData) {
      return res.status(404).json({
        code: 404,
        message: '未找到教材与著作',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: textbookData
    });
  } catch (error) {
    console.error('获取教材与著作详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教材与著作详情失败',
      error: error.message
    });
  }
};

/**
 * 创建教材与著作
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createTextbook = async (req, res) => {
  // 获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(textbooksModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { 
      userId, 
      materialName, 
      publishDate, 
      categoryId, 
      remark,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl    // 接收前端传递的文件路径
    } = req.body;
    
    // 验证必填字段
    if (!userId) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '用户ID不能为空',
        data: null
      });
    }
    
    if (!materialName) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '教材与著作名称不能为空',
        data: null
      });
    }
    
    if (!publishDate) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '出版日期不能为空',
        data: null
      });
    }
    
    if (!categoryId) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '类别ID不能为空',
        data: null
      });
    }
    
    // 检查用户是否存在
    const user = await userModel.findByPk(userId, { transaction });
    if (!user) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }
    
    // 检查类别是否存在
    const category = await textbookCategoriesModel.findByPk(categoryId, { transaction });
    if (!category) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '类别不存在',
        data: null
      });
    }
    
    // 创建教材与著作
    const textbook = await textbooksModel.create({
      id: uuidv4(),
      userId,
      materialName,
      publishDate,
      categoryId,
      remark,
      ifReviewer: 0,
      reviewerId: null,
      attachmentUrl: null, // 初始设置为null，稍后使用ID更新
      createdAt: new Date(),
      updatedAt: new Date()
    }, { transaction });
    
    // 处理关联文件 - 使用前端传递的文件ID而非重新创建文件记录
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组，可能以JSON字符串形式传递
      if (typeof fileIds == 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 解析attachmentUrl，可能以JSON字符串形式传递
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl == 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }
      
      // 如果有文件ID，关联到教材
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;
          
          const updateData = { 
            projectId: textbook.id, // 设置projectId字段为教材ID
            relatedId: textbook.id,
            relatedType: 'textbooks' // 使用textbooks作为relatedType
          };
          
          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath; // 使用filePath字段，这是文件模型中的正确字段名
          }
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );

          processedFileIds.push(fileId);
        }
      }
    } else if (req.files && req.files.length > 0) {
      // 处理通过multer上传的文件
      const uploadedFiles = req.files;
      
      for (const file of uploadedFiles) {
        // 创建文件记录
        const fileRecord = await fileModel.create({
          originalName: file.originalname,
          filePath: file.path,
          mimeType: file.mimetype,
          size: file.size,
          projectId: textbook.id, // 设置projectId字段为教材ID
          relatedId: textbook.id,
          relatedType: 'textbooks'
        }, { transaction });
        
        processedFileIds.push(fileRecord.id);
      }
    }
    
    // 提交事务
    await transaction.commit();

    // 事务提交成功后，异步移动文件到指定的教材目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'textbooks'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${textbook.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 新增变量跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: textbook.id,
                relatedId: textbook.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
              
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新教材的attachmentUrl为教材文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的教材文件夹路径，确保不包含文件名
            const textbookFolderPath = `uploads\\textbooks\\${textbook.id}\\`;
            
            await textbook.update({
              attachmentUrl: textbookFolderPath
            });
            console.log(`已更新教材 ${textbook.id} 的attachmentUrl为: ${textbookFolderPath}`);
            
          } catch (updateError) {
            console.error('更新教材attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响教材创建的返回结果，仅记录错误
        console.error('移动文件到教材目录失败:', moveError);
      }
    }
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: textbook
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('创建教材与著作失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建教材与著作失败',
      error: error.message
    });
  }
};

/**
 * 更新教材与著作
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateTextbook = async (req, res) => {
  // 获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(textbooksModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    console.log('更新教材与著作请求参数:', req.params);
    console.log('req.body==', req.body);
    
    const { 
      materialName, 
      publishDate, 
      categoryId, 
      remark,
      userId,
      fileIds         // 接收前端传递的文件ID数组
    } = req.body;
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少教材与著作ID',
        data: null
      });
    }
    
    // 查询更新前的教材与著作信息（包含类别信息）
    const oldTextbook = await textbooksModel.findByPk(id, { 
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          attributes: ['id', 'categoryAndPosition', 'score'],
          required: false
        }
      ],
      transaction 
    });
    
    if (!oldTextbook) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到教材与著作',
        data: null
      });
    }
    
    // 保存旧数据，用于后续更新排名
    const oldCategoryId = oldTextbook.categoryId;
    const oldCategoryScore = oldTextbook.category ? oldTextbook.category.score : 0;
    const oldUserId = oldTextbook.userId;
    const oldPublishDate = oldTextbook.publishDate;
    const oldIfReviewer = oldTextbook.ifReviewer;
    
    // 权限检查：管理员或者所有者可以编辑
    const userInfo = await getUserInfoFromRequest(req);
    const isAdmin = userInfo.role && (userInfo.role.roleAuth == 'ADMIN-LV2' || userInfo.role.roleAuth == 'SUPER');
    const isOwner = oldTextbook.userId == userInfo.id;
    
    if (!isAdmin && !isOwner) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限编辑该教材与著作',
        data: null
      });
    }
    
    // 如果更改类别，检查类别是否存在
    let newCategoryScore = oldCategoryScore;
    if (categoryId && categoryId !== oldTextbook.categoryId) {
      const category = await textbookCategoriesModel.findByPk(categoryId, { transaction });
      if (!category) {
        await transaction.rollback();
        return res.status(404).json({
          code: 404,
          message: '类别不存在',
          data: null
        });
      }
      newCategoryScore = category.score || 0;
    }
    
    // 更新教材与著作
    const updateData = {};
    if (userId !== undefined) updateData.userId = userId;
    if (materialName !== undefined) updateData.materialName = materialName;
    if (publishDate !== undefined) updateData.publishDate = publishDate;
    if (categoryId !== undefined) updateData.categoryId = categoryId;
    if (remark !== undefined) updateData.remark = remark;
    // 设置标准化的附件文件夹路径
    updateData.attachmentUrl = `uploads\\textbooks\\${id}\\`;
    updateData.updatedAt = new Date();
    
    await oldTextbook.update(updateData, { transaction });
    
    // 处理文件关联
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];
      
      // 解析文件ID数组，可能以JSON字符串形式传递
      if (typeof fileIds == 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
      
      // 如果有文件ID，更新关联关系
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (const fileId of fileIdArray) {
          const updateData = { 
            projectId: oldTextbook.id, // 确保设置projectId字段为教材ID
            relatedId: oldTextbook.id,
            relatedType: 'textbooks'
          };
          
          await fileModel.update(
            updateData,
            { 
              where: { id: fileId },
              transaction
            }
          );
          
          processedFileIds.push(fileId);
        }
      }
    }
    
    // 处理新上传的文件
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        // 创建文件记录
        const fileRecord = await fileModel.create({
          originalName: file.originalname,
          filePath: file.path,
          mimeType: file.mimetype,
          size: file.size,
          projectId: oldTextbook.id, // 确保设置projectId字段为教材ID
          relatedId: oldTextbook.id,
          relatedType: 'textbooks'
        }, { transaction });
        
        processedFileIds.push(fileRecord.id);
      }
    }
    
    // 获取更新后的教材与著作数据（包括新类别信息）
    const updatedTextbook = await textbooksModel.findByPk(id, {
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          attributes: ['id', 'categoryAndPosition', 'score'],
          required: false
        }
      ],
      transaction
    });
    console.log('oldTextbook.ifReviewer==', oldTextbook.ifReviewer);
    
    // 判断是否需要更新排名
    // 只有已审核的教材才需要更新排名，并且只在以下情况更新：
    // 1. 类别发生变化（分数可能不同）
    // 2. 用户发生变化（需要更新不同用户的排名）
    // 3. 发布日期发生变化（可能影响是否在统计时间范围内）
    if (oldTextbook.ifReviewer == true) {
      const newCategoryId = updatedTextbook.categoryId;
      const newCategoryScore = updatedTextbook.category ? updatedTextbook.category.score : 0;
      const newUserId = updatedTextbook.userId;
      const newPublishDate = updatedTextbook.publishDate;
      console.log('newCategoryId==', newCategoryId);
      console.log('newCategoryScore==', newCategoryScore);
      console.log('newUserId==', newUserId);
      console.log('newPublishDate==', newPublishDate);
      console.log('oldCategoryId==', oldCategoryId);
      console.log('oldCategoryScore==', oldCategoryScore);
      console.log('oldUserId==', oldUserId);
      console.log('oldPublishDate==', oldPublishDate);
      // 检查是否需要更新排名 
      const needUpdateRanking = 
        oldCategoryId !== newCategoryId || 
        oldCategoryScore !== newCategoryScore ||
        oldUserId !== newUserId ||
        oldPublishDate !== newPublishDate;
      
      if (needUpdateRanking) {
        try {
          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("textbooks");
          
          // 判断旧记录是否在时间区间内
          const oldIsInTimeRange = timeInterval && oldPublishDate ? 
            isProjectInTimeRange(oldPublishDate, timeInterval.startTime, timeInterval.endTime) : 
            false;
          
          // 判断新记录是否在时间区间内
          const newIsInTimeRange = timeInterval && newPublishDate ? 
            isProjectInTimeRange(newPublishDate, timeInterval.startTime, timeInterval.endTime) : 
            false;
          
          // 定义将要更新的排名表
          let oldRankingTables = [];
          let newRankingTables = [];
          
          // 确定旧记录应该更新的排名表
          if (oldIsInTimeRange) {
            oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
          } else {
            oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
          }
          
          // 确定新记录应该更新的排名表
          if (newIsInTimeRange) {
            newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
          } else {
            newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
          }
          
          // 如果用户ID发生变化，需要对两个用户分别处理
          if (oldUserId !== newUserId) {
            // 先减去旧用户的分数
            for (const table of oldRankingTables) {
              await updateUserRankings(
                [oldUserId],
                table,
                'textbooks',
                [1], // 减少一个计数
                [oldCategoryScore],
                transaction,
                "subtract" // 减分操作
              );
            }
            
            // 再为新用户添加分数
            for (const table of newRankingTables) {
              await updateUserRankings(
                [newUserId],
                table,
                'textbooks',
                [1], // 增加一个计数
                [newCategoryScore],
                transaction,
                "add" // 加分操作
              );
            }
          } 
          // 如果用户ID没变，但其他条件变了
          else if (needUpdateRanking) {
            // 如果时间范围导致的表变更
            if (JSON.stringify(oldRankingTables) !== JSON.stringify(newRankingTables)) {
              // 从旧表中减去
              for (const table of oldRankingTables) {
                await updateUserRankings(
                  [oldUserId],
                  table,
                  'textbooks',
                  [1], // 减少一个计数
                  [oldCategoryScore],
                  transaction,
                  "subtract" // 减分操作
                );
              }
              
              // 向新表中添加
              for (const table of newRankingTables) {
                await updateUserRankings(
                  [newUserId],
                  table,
                  'textbooks',
                  [1], // 增加一个计数
                  [newCategoryScore],
                  transaction,
                  "add" // 加分操作
                );
              }
            } 
            // 如果只是类别/分数变更但表没变
            else if (oldCategoryScore !== newCategoryScore) {
              // 对相同的表进行分数调整
              for (const table of newRankingTables) {
                // 先减去旧分数
                await updateUserRankings(
                  [newUserId],
                  table,
                  'textbooks',
                  [0], // 不改变计数
                  [oldCategoryScore],
                  transaction,
                  "subtract" // 减分操作
                );
                
                // 再添加新分数
                await updateUserRankings(
                  [newUserId],
                  table,
                  'textbooks',
                  [0], // 不改变计数
                  [newCategoryScore],
                  transaction,
                  "add" // 加分操作
                );
              }
            }
          }
          
          console.log('用户排名更新成功');
        } catch (rankingError) {
          console.error('更新排名失败:', rankingError);
          await transaction.rollback();
          throw new Error(`更新排名失败: ${rankingError.message}`);
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，异步移动文件到指定的教材目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'textbooks'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${oldTextbook.id}/`;
        
        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }
        
        // 跟踪已移动的文件路径
        const movedFilePaths = [];
        
        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });
          
          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }
          
          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);
          
          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }
              
              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: oldTextbook.id,
                relatedId: oldTextbook.id,
                relatedType: storagePath
              });
              
              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }
        
        // 更新教材的attachmentUrl为教材文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            const textbookFolderPath = `uploads\\textbooks\\${oldTextbook.id}\\`;
            
            await oldTextbook.update({
              attachmentUrl: textbookFolderPath
            });
            console.log(`已更新教材 ${oldTextbook.id} 的attachmentUrl为: ${textbookFolderPath}`);
          } catch (updateError) {
            console.error('更新教材attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响教材更新的返回结果，仅记录错误
        console.error('移动文件到教材目录失败:', moveError);
      }
    }
    
    // 重新加载最新数据
    const finalUpdatedTextbook = await textbooksModel.findByPk(id, {
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          required: false
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        }
      ]
    });
    
    // 查询关联的文件
    const files = await fileModel.findAll({
      where: {
        relatedId: id,
        projectId: id,
        relatedType: 'textbooks'
      }
    });
    
    // 添加文件信息到响应数据
    const responseData = finalUpdatedTextbook.toJSON();
    responseData.attachments = files.map(file => file.toJSON());
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: responseData
    });
  } catch (error) {
    // 回滚事务
    if (transaction) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        console.error('事务回滚失败:', rollbackError);
      }
    }
    
    console.error('更新教材与著作失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新教材与著作失败',
      error: error.message
    });
  }
};

/**
 * 删除教材与著作
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteTextbook = async (req, res) => {
  // 获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(textbooksModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少教材与著作ID',
        data: null
      });
    }
    
    // 查询教材与著作是否存在，并获取详细信息（包括类别信息）
    const textbook = await textbooksModel.findByPk(id, { 
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          attributes: ['id', 'categoryAndPosition', 'score'],
          required: false
        }
      ],
      transaction 
    });
    
    if (!textbook) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到教材与著作',
        data: null
      });
    }
    
    // 权限检查：管理员或者所有者可以删除
    const userInfo = await getUserInfoFromRequest(req);
    const isAdmin = userInfo.role && (userInfo.role.roleAuth == 'ADMIN-LV2' || userInfo.role.roleAuth == 'SUPER');
    const isOwner = textbook.userId == userInfo.id;
    
    if (!isAdmin && !isOwner) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限删除该教材与著作',
        data: null
      });
    }
    
    // 获取关联的文件
    const files = await fileModel.findAll({
      where: {
        relatedId: id,
        projectId: id,
        relatedType: 'textbooks'
      },
      transaction
    });
    
    // 标记关联的文件为已删除
    if (files && files.length > 0) {
      await fileModel.destroy({
        where: { relatedId: id },
        transaction
      });
    }
    
    // 如果教材已审核，需要更新排名
    if (textbook.ifReviewer == true) {
      try {
        // 获取必要的数据
        const userId = textbook.userId;
        const baseScore = textbook.category ? textbook.category.score : 0;
        const publishDate = textbook.publishDate;
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("textbooks");
        
        // 判断教材是否在时间区间内
        const isInTimeRange = timeInterval && publishDate ? 
          isProjectInTimeRange(publishDate, timeInterval.startTime, timeInterval.endTime) : 
          false;
        
        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        // 从排名表中减去分数和计数
        for (const table of rankingTables) {
          await updateUserRankings(
            [userId],
            table,
            'textbooks',
            [1], // 删除一个计数
            [baseScore],
            transaction,
            "subtract" // 减分操作
          );
        }
        
        console.log(`已从排名表 ${rankingTables.join(', ')} 中减去分数: ${baseScore}`);
      } catch (rankingError) {
        console.error('更新排名表失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }
    
    // 从数据库中删除教材与著作记录，而不是仅标记为已删除
    await textbook.destroy({ transaction });
    
    // 提交事务
    await transaction.commit();
    
    // 事务提交成功后，执行文件系统删除操作（这些操作不能回滚，所以放在事务之外）
    try {
      // 直接使用教材ID构造教材文件夹路径
      const textbookFolderPath = `uploads/textbooks/${id}`;
      console.log(`尝试删除教材文件夹: ${textbookFolderPath}`);
      await fileController.deleteDirectoryUtil(textbookFolderPath);
    } catch (fsError) {
      console.warn('删除文件系统中的文件时出错:', fsError);
      // 数据库事务已提交成功，文件系统操作失败不影响API返回结果
    }
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('删除教材与著作失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除教材与著作失败',
      error: error.message
    });
  }
};

/**
 * 审核教材与著作
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewTextbook = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(textbooksModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id, reviewStatus, reviewComment, reviewer } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少教材与著作ID',
        data: null
      });
    }
    
    if (reviewStatus == undefined) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少审核状态',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找教材与著作，包含类别信息以获取分数
    const textbook = await textbooksModel.findByPk(id, {
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          attributes: ['id', 'categoryAndPosition', 'score'],
          required: false
        }
      ],
      transaction
    });
    
    if (!textbook) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '教材与著作不存在',
        data: null
      });
    }
    
    // 权限检查 - 只有管理员可以审核
    const hasPermission = userInfo.role.roleAuth == 'ADMIN-LV2' || userInfo.role.roleAuth == 'SUPER';
    
    if (!hasPermission) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核教材与著作',
        data: null
      });
    }
    
    // 更新审核状态
    // ifReviewer: null/undefined=待审核, 0=拒绝, 1=同意审核
    const updateData = {
      ifReviewer: reviewStatus,
      reviewedAt: new Date(),
      reviewerId: reviewer || userInfo.id
    };
    
    // 添加审核意见（如果有）
    if (reviewComment !== undefined) {
      updateData.reviewComment = reviewComment;
    }
    
    await textbook.update(updateData, { transaction });
    
    // 教材审核通过后，更新用户排名数据
    if (reviewStatus == 1) {
      try {
        // 获取教材类别对应的分数
        if (!textbook.category) {
          console.error(`未找到教材类别信息，categoryId: ${textbook.categoryId}`);
          throw new Error('未找到教材类别信息');
        }
        
        // 确保baseScore是数字类型
        const scoreValue = textbook.category.score || "0";
        const baseScore = parseFloat(scoreValue);
        
        if (isNaN(baseScore)) {
          console.error(`教材类别分数无效: ${scoreValue}`);
          throw new Error(`教材类别分数无效: ${scoreValue}`);
        }
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("textbooks");
        
        // 判断教材是否在时间区间内
        const isInTimeRange = timeInterval ? 
          isProjectInTimeRange(
            textbook.publishDate,
            timeInterval.startTime,
            timeInterval.endTime
          ) : false;
        
        console.log(`教材ID ${id} 是否在统计时间区间内: ${isInTimeRange}`);
        console.log(`基础分数: ${baseScore}`);
        
        // 根据教材是否在时间区间内，更新不同的排名表
        let rankingTables = [];
        
        if (isInTimeRange) {
          // 在区间内：更新范围内表和全部表
          rankingTables = [
            'user_ranking_reviewed_in', 
            'user_ranking_reviewed_all'
          ];
          console.log(`更新范围内排名表和全部排名表`);
        } else {
          // 不在区间内：更新范围外表和全部表
          rankingTables = [
            'user_ranking_reviewed_out', 
            'user_ranking_reviewed_all'
          ];
          console.log(`更新范围外排名表和全部排名表`);
        }
        
        try {
          for (const table of rankingTables) {
            // 更新用户的排名数据：教材数量+1，分数增加基础分
            await updateUserRankings(
              [textbook.userId],     // 用户ID数组
              table,                 // 表名
              'textbooks',           // 类型名
              [1],                   // 计数+1
              [baseScore],           // 得分数组 - 确保是数字类型
              transaction,           // 传递事务对象
              "add"                  // 操作类型：加分
            );
          }
        } catch (rankingError) {
          console.error('更新排名表失败:', rankingError);
          // 对于所有错误，都应当回滚事务以保证数据一致性
          await transaction.rollback();
          throw new Error(`更新排名失败: ${rankingError.message}`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 检查是否已经回滚，如果没有则回滚事务
        if (!error.message || !error.message.includes('更新排名失败')) {
          await transaction.rollback();
          throw error; // 将错误向上传播
        }
      }
    }
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    try {
      await transaction.rollback();
    } catch (rollbackError) {
      console.error('回滚事务失败:', rollbackError);
    }
    
    console.error('审核教材与著作失败:', error);
    return res.status(500).json({
      code: 500,
      message: '审核失败',
      error: error.message
    });
  }
};

/**
 * 导入教材与著作数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importTextbooks = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未上传文件',
        data: null
      });
    }
    
    const workbook = new Excel.Workbook();
    await workbook.xlsx.readFile(req.file.path);
    
    const worksheet = workbook.getWorksheet(1);
    const rows = worksheet.getRows(2, worksheet.rowCount - 1);
    
    let total = 0;
    let success = 0;
    let failed = 0;
    const errors = [];
    
    // 获取所有类别
    const categories = await textbookCategoriesModel.findAll();
    const categoryMap = {};
    categories.forEach(category => {
      categoryMap[category.categoryAndPosition] = category.id;
    });
    
    // 处理每一行数据
    for (const row of rows) {
      if (!row.getCell(1).value) continue; // 跳过空行
      total++;
      
      try {
        const materialName = row.getCell(1).value?.toString().trim();
        const publishDateRaw = row.getCell(2).value;
        const categoryAndPosition = row.getCell(3).value?.toString().trim();
        const studentNumber = row.getCell(4).value?.toString().trim();
        const remark = row.getCell(5).value?.toString().trim();
        
        // 检查必填字段
        if (!materialName || !publishDateRaw || !categoryAndPosition || !studentNumber) {
          failed++;
          errors.push(`第${row.number}行数据不完整，必填字段不能为空`);
          continue;
        }
        
        // 处理日期
        let publishDate;
        if (typeof publishDateRaw == 'string') {
          publishDate = new Date(publishDateRaw);
        } else if (publishDateRaw instanceof Date) {
          publishDate = publishDateRaw;
        } else {
          failed++;
          errors.push(`第${row.number}行出版日期格式错误`);
          continue;
        }
        
        // 检查日期是否有效
        if (isNaN(publishDate.getTime())) {
          failed++;
          errors.push(`第${row.number}行出版日期无效`);
          continue;
        }
        
        // 查找类别ID
        const categoryId = categoryMap[categoryAndPosition];
        if (!categoryId) {
          failed++;
          errors.push(`第${row.number}行类别"${categoryAndPosition}"不存在`);
          continue;
        }
        
        // 查找用户ID
        const user = await userModel.findOne({
          where: { studentNumber: studentNumber }
        });
        
        if (!user) {
          failed++;
          errors.push(`第${row.number}行工号"${studentNumber}"对应的用户不存在`);
          continue;
        }
        
        // 创建教材与著作
        await textbooksModel.create({
          id: uuidv4(),
          userId: user.id,
          materialName,
          publishDate,
          categoryId,
          remark: remark || null,
          ifReviewer: 0,
          reviewerId: null,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        
        success++;
      } catch (error) {
        failed++;
        errors.push(`第${row.number}行导入失败: ${error.message}`);
      }
    }
    
    // 删除临时文件
    fs.unlinkSync(req.file.path);
    
    return res.status(200).json({
      code: 200,
      message: '导入完成',
      data: {
        total,
        success,
        failed,
        errors
      }
    });
  } catch (error) {
    console.error('导入教材与著作数据失败:', error);
    
    // 如果文件存在，删除临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    return res.status(500).json({
      code: 500,
      message: '导入失败',
      error: error.message
    });
  }
};

/**
 * 导出教材与著作数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportTextbooks = async (req, res) => {
  try {
    const { fileName = '教材与著作数据.xlsx', materialName, categoryId, userId } = req.query;
    
    // 构建查询条件
    const whereCondition = {};
    
    if (materialName) {
      whereCondition.materialName = { [Op.like]: `%${materialName}%` };
    }
    
    if (categoryId) {
      whereCondition.categoryId = categoryId;
    }
    
    if (userId) {
      whereCondition.userId = userId;
    }
    
    // 查询数据
    const textbooks = await textbooksModel.findAll({
      where: whereCondition,
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          required: false
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['nickname', 'username', 'studentNumber'],
          required: false
        }
      ],
      order: [['publishDate', 'DESC']]
    });
    
    // 创建工作簿
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('教材与著作数据');
    
    // 设置列头
    worksheet.columns = [
      { header: '教材与著作名称', key: 'materialName', width: 40 },
      { header: '出版日期', key: 'publishDate', width: 15 },
      { header: '类别', key: 'category', width: 30 },
      { header: '作者姓名', key: 'userName', width: 15 },
      { header: '工号', key: 'studentNumber', width: 15 },
      { header: '分数', key: 'score', width: 10 },
      { header: '审核状态', key: 'ifReviewer', width: 15 },
      { header: '备注', key: 'remark', width: 40 }
    ];
    
    // 添加数据
    textbooks.forEach(textbook => {
      worksheet.addRow({
        materialName: textbook.materialName,
        publishDate: textbook.publishDate ? new Date(textbook.publishDate).toISOString().split('T')[0] : '',
        category: textbook.category?.categoryAndPosition || '',
        userName: textbook.user?.nickname || textbook.user?.username || '',
        studentNumber: textbook.user?.studentNumber || '',
        score: textbook.category?.score || '',
        ifReviewer: textbook.ifReviewer ? '已审核' : '未审核',
        remark: textbook.remark || ''
      });
    });
    
    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(fileName)}`);
    
    // 发送文件
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error('导出教材与著作数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出失败',
      error: error.message
    });
  }
};

/**
 * 获取统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getStatistics = async (req, res) => {
  try {
    const { userId, range = 'in' } = req.body;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("textbooks");
    
    // 构建查询条件
    const whereCondition = {};
    
    // 如果有用户ID，限定为该用户的数据
    if (userId) {
      whereCondition.userId = userId;
    }
    
    // 注意：由于需要在应用层进行日期判断，这里先获取所有数据
    // 根据range参数对数据库查询进行初步过滤，使用数据库层面的过滤条件
    // 但最终的时间范围判断将在应用层使用isProjectInTimeRange完成
    if (range == 'in' && timeInterval) {
      // 统计范围内：有效出版（与时间区间有交集）
      whereCondition.publishDate = { 
        [Op.between]: [timeInterval.startTime, timeInterval.endTime] 
      };
    } else if (range == 'out' && timeInterval) {
      // 统计范围外：无效出版（与时间区间无交集）
      whereCondition[Op.or] = [
        { publishDate: { [Op.lt]: timeInterval.startTime } },
        { publishDate: { [Op.gt]: timeInterval.endTime } }
      ];
    }
    
    // 获取所有教材与著作及其类别
    const textbooks = await textbooksModel.findAll({
      where: whereCondition,
      include: [
        {
          model: textbookCategoriesModel,
          as: 'category',
          attributes: ['score'],
          required: false
        }
      ]
    });
    
    // 应用层面使用isProjectInTimeRange函数再次过滤
    let inRangeTextbooks = 0;
    if (timeInterval) {
      textbooks.forEach(textbook => {
        const isInRange = isProjectInTimeRange(
          textbook.publishDate,
          timeInterval.startTime,
          timeInterval.endTime
        );
        
        if (isInRange) {
          inRangeTextbooks++;
        }
      });
    }
    
    // 总教材与著作数量
    const totalTextbooks = textbooks.length;
    
    // 计算平均分数
    const totalScore = textbooks.reduce((sum, textbook) => {
      return sum + (parseFloat(textbook.category?.score) || 0);
    }, 0);
    
    const averageScore = totalTextbooks > 0 ? (totalScore / totalTextbooks) : 0;
    
    // 计算审核完成率
    const reviewedCount = textbooks.filter(textbook => textbook.ifReviewer).length;
    const reviewCompletionRate = totalTextbooks > 0 ? ((reviewedCount / totalTextbooks) * 100) : 0;
    
    return res.status(200).json({
      code: 200,
      message: '获取统计数据成功',
      data: {
        totalTextbooks,
        inRangeTextbooks: range == 'in' ? totalTextbooks : (range == 'out' ? 0 : inRangeTextbooks),
        averageScore,
        reviewCompletionRate
      }
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取统计数据失败',
      error: error.message
    });
  }
};

/**
 * 获取教材与著作统计概览数据
 */
exports.getStatisticsOverview = async (req, res) => {
  try {
    const { userId, reviewStatus } = req.query;
    const whereCondition = {};
    
    if (userId) {
      whereCondition.userId = userId;
    }
    
    // 根据审核状态筛选
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = true;
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = false;
    }
    
    // 获取总教材与著作数量
    const totalTextbooks = await textbooksModel.count({
      where: whereCondition
    });
    
    // 获取统计范围内教材与著作数量 - 使用isProjectInTimeRange函数
    const timeInterval = await getTimeIntervalByName("textbooks");
    
    // 获取所有教材与著作
    const textbooks = await textbooksModel.findAll({
      where: whereCondition,
      include: [{
        model: textbookCategoriesModel,
        as: 'category',
        attributes: ['score']
      }]
    });
    
    // 计算在时间范围内的教材数量
    let inRangeTextbooks = 0;
    if (timeInterval) {
      textbooks.forEach(textbook => {
        if (isProjectInTimeRange(
          textbook.publishDate,
          timeInterval.startTime,
          timeInterval.endTime
        )) {
          inRangeTextbooks++;
        }
      });
    }
    
    // 获取平均分数
    let averageScore = 0;
    if (textbooks.length > 0) {
      const totalScore = textbooks.reduce((sum, textbook) => {
        return sum + (textbook.category ? parseFloat(textbook.category.score) || 0 : 0);
      }, 0);
      averageScore = totalScore / textbooks.length;
    }
    
    // 获取审核完成率
    const reviewedCount = textbooks.filter(textbook => textbook.ifReviewer == 1).length;
    const reviewCompletionRate = totalTextbooks > 0 ? (reviewedCount / totalTextbooks) * 100 : 0;
    
    res.status(200).json({
      code: 200,
      message: '获取教材与著作统计概览数据成功',
      data: {
        totalTextbooks,
        inRangeTextbooks,
        averageScore,
        reviewCompletionRate
      }
    });
  } catch (error) {
    console.error('获取教材与著作统计概览数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取教材与著作统计概览数据失败: ' + error.message
    });
  }
};

/**
 * 获取教材与著作类别分布数据
 */
exports.getCategoryDistribution = async (req, res) => {
  try {
    const { userId, range = 'all', reviewStatus } = req.query;
    let whereCondition = {};
    
    if (userId) {
      whereCondition.userId = userId;
    }
    
    // 更新审核状态筛选逻辑，对应四个选项：全部、已通过、已拒绝、待审核
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    }
    // reviewStatus == 'all' 不添加筛选条件
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("textbooks");
    
    // 获取所有类别和教材
    const categories = await textbookCategoriesModel.findAll();
    
    // 获取所有教材（不加时间筛选，在应用层进行筛选）
    const allTextbooks = await textbooksModel.findAll({
      where: whereCondition,
      attributes: ['id', 'categoryId', 'publishDate']
    });
    
    // 使用Map统计每个类别的符合条件的教材数量
    const categoryCountMap = new Map();
    categories.forEach(category => {
      categoryCountMap.set(category.id, {
        name: category.categoryAndPosition,
        count: 0
      });
    });
    
    // 应用层筛选，按照range和时间区间过滤
    allTextbooks.forEach(textbook => {
      // 判断教材是否在时间区间内
      let includeTextbook = true;
      
      if (timeInterval && range !== 'all') {
        const isInRange = isProjectInTimeRange(
          textbook.publishDate,
          timeInterval.startTime,
          timeInterval.endTime
        );
        
        // 根据range参数决定是否包含该教材
        if ((range == 'in' && !isInRange) || (range == 'out' && isInRange)) {
          includeTextbook = false;
        }
      }
      
      // 如果符合条件，增加对应类别的计数
      if (includeTextbook && categoryCountMap.has(textbook.categoryId)) {
        const categoryData = categoryCountMap.get(textbook.categoryId);
        categoryData.count++;
      }
    });
    
    // 转换为结果数组，并过滤掉数量为0的类别
    const result = Array.from(categoryCountMap.values())
      .filter(item => item.count > 0)
      .map(item => ({
        name: item.name,
        value: item.count
      }));
    
    res.status(200).json({
      code: 200,
      message: '获取教材与著作类别分布数据成功',
      data: result
    });
  } catch (error) {
    console.error('获取教材与著作类别分布数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取教材与著作类别分布数据失败: ' + error.message
    });
  }
};

/**
 * 获取教材与著作审核状态数据
 */
exports.getReviewStatusOverview = async (req, res) => {
  try {
    const { userId, range = 'all', reviewStatus } = req.query;
    let whereCondition = {};
    
    if (userId) {
      whereCondition.userId = userId;
    }
    
    // 根据审核状态筛选
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    }
    // reviewStatus == 'all' 不添加筛选条件
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("textbooks");
    
    // 获取所有教材（不在数据库层筛选时间范围，而在应用层处理）
    const textbooks = await textbooksModel.findAll({
      where: whereCondition,
      attributes: ['id', 'publishDate', 'ifReviewer']
    });
    
    // 在应用层筛选符合时间范围要求的教材
    const filteredTextbooks = range !== 'all' && timeInterval
      ? textbooks.filter(textbook => {
          const isInRange = isProjectInTimeRange(
            textbook.publishDate,
            timeInterval.startTime,
            timeInterval.endTime
          );
          
          // 根据range参数决定是否包含该教材
          return (range == 'in' && isInRange) || (range == 'out' && !isInRange);
        })
      : textbooks;
    
    // 统计已审核和未审核的数量
    const reviewed = filteredTextbooks.filter(textbook => textbook.ifReviewer == true).length;
    const unreviewed = filteredTextbooks.filter(textbook => textbook.ifReviewer == false).length;
    const pending = filteredTextbooks.filter(textbook => textbook.ifReviewer == null).length;
    
    const total = reviewed + unreviewed + pending;
    const reviewedRate = total > 0 ? ((reviewed + unreviewed) / total) * 100 : 0;
    
    res.status(200).json({
      code: 200,
      message: '获取教材与著作审核状态数据成功',
      data: {
        reviewed,
        unreviewed,
        pending,
        total,
        reviewedRate
      }
    });
  } catch (error) {
    console.error('获取教材与著作审核状态数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取教材与著作审核状态数据失败: ' + error.message
    });
  }
};

/**
 * 获取教材与著作数量统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCountStatistics = async (req, res) => {
  try {
    const { userId, range = 'all', reviewStatus } = req.query;
    let whereCondition = {};
    
    if (userId) {
      whereCondition.userId = userId;
    }
    
    // 更新审核状态筛选逻辑，对应四个选项：全部、已通过、已拒绝、待审核
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    }
    // reviewStatus == 'all' 不添加筛选条件
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("textbooks");
    
    // 获取所有教材（不添加时间筛选，在应用层进行筛选）
    const allTextbooks = await textbooksModel.findAll({
      where: whereCondition,
      attributes: ['id', 'categoryId', 'publishDate']
    });
    
    // 获取所有类别
    const categories = await textbookCategoriesModel.findAll();
    
    // 使用应用层筛选，按照类别统计
    const categoryMap = new Map();
    
    // 初始化类别映射
    categories.forEach(category => {
      categoryMap.set(category.id, {
        id: category.id,
        name: category.categoryAndPosition,
        count: 0
      });
    });
    
    // 遍历教材，根据时间范围筛选并统计
    allTextbooks.forEach(textbook => {
      // 判断是否在时间区间内
      let includeTextbook = true;
      
      if (timeInterval && range !== 'all') {
        const isInRange = isProjectInTimeRange(
          textbook.publishDate, 
          timeInterval.startTime, 
          timeInterval.endTime
        );
        
        // 根据range参数决定是否包含该教材
        if ((range == 'in' && !isInRange) || (range == 'out' && isInRange)) {
          includeTextbook = false;
        }
      }
      
      // 如果符合条件，增加对应类别的计数
      if (includeTextbook && categoryMap.has(textbook.categoryId)) {
        categoryMap.get(textbook.categoryId).count++;
      }
    });
    
    // 转换为数组返回
    const result = Array.from(categoryMap.values())
      .map(item => ({
        name: item.name,
        value: item.count
      }));
    
    res.status(200).json({
      code: 200,
      message: '获取教材与著作数量统计成功',
      data: result
    });
  } catch (error) {
    console.error('获取教材与著作数量统计失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取教材与著作数量统计失败: ' + error.message
    });
  }
};

/**
 * 获取教材与著作出版日期分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDateDistribution = async (req, res) => {
  try {
    const { userId, range = 'all', reviewStatus } = req.query;
    let whereCondition = {};
    
    if (userId) {
      whereCondition.userId = userId;
    }
    
    // 更新审核状态筛选逻辑，对应四个选项：全部、已通过、已拒绝、待审核
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1; // 已通过
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    }
    // reviewStatus == 'all' 不添加筛选条件
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("textbooks");
    
    // 获取所有教材与著作（不在数据库层筛选时间，而在应用层处理）
    const allTextbooks = await textbooksModel.findAll({
      where: whereCondition,
      attributes: ['publishDate'],
      order: [['publishDate', 'ASC']]
    });
    
    // 应用层筛选符合时间范围要求的教材
    const textbooks = range !== 'all' && timeInterval
      ? allTextbooks.filter(textbook => {
          const isInRange = isProjectInTimeRange(
            textbook.publishDate,
            timeInterval.startTime,
            timeInterval.endTime
          );
          
          // 根据range参数决定是否包含该教材
          return (range == 'in' && isInRange) || (range == 'out' && !isInRange);
        })
      : allTextbooks;
    
    // 按年份统计
    const yearMap = new Map();
    
    for (const textbook of textbooks) {
      const year = new Date(textbook.publishDate).getFullYear();
      const count = yearMap.get(year) || 0;
      yearMap.set(year, count + 1);
    }
    
    // 构建数据
    const years = Array.from(yearMap.keys()).sort();
    const counts = years.map(year => yearMap.get(year));
    
    // 计算累计数量
    const cumulativeCounts = [];
    let cumulative = 0;
    
    for (const count of counts) {
      cumulative += count;
      cumulativeCounts.push(cumulative);
    }
    
    res.status(200).json({
      code: 200,
      message: '获取教材与著作出版日期分布成功',
      data: {
        years,
        counts,
        cumulativeCounts
      }
    });
  } catch (error) {
    console.error('获取教材与著作出版日期分布失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取教材与著作出版日期分布失败: ' + error.message
    });
  }
};

/**
 * 获取教师教材数量排行
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAuthorRanking = async (req, res) => {
  try {
    const { 
      range = 'all', 
      page = 1, 
      pageSize, 
      isExport = false,
      nickname = '' 
    } = req.body;
    
    const limit = pageSize != null ? pageSize : 10;
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(limit);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 根据range选择对应的排名表模型
    let RankingModel;
    switch(range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }
    
    // 查询条件：按教材总分降序排序
    const queryOptions = {
      order: [['textbookScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'textbookCount',
        'textbookScore'
      ]
    };
    
    // 添加昵称模糊查询条件
    if (nickname) {
      queryOptions.where = {
        nickName: {
          [Sequelize.Op.like]: `%${nickname}%`
        }
      };
    }
    
    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }

    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);
    
    // 格式化返回数据
    const resultData = rows.map((item, index) => ({
      rank: item.rank || index + 1 + offset,
      userId: item.userId,
      nickname: item.nickName,
      studentNumber: item.studentNumber,
      textbooksCount: item.textbookCount || 0,
      totalScore: parseFloat(item.textbookScore || 0).toFixed(2)
    }));
    
    res.status(200).json({
      code: 200,
      message: '获取教师教材数量排行成功',
      data: {
        list: resultData,
        pagination: {
          total: count,
          page: pageNum,
          limit: pageSizeNum,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取教师教材数量排行失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取教师教材数量排行失败: ' + error.message
    });
  }
};

/**
 * 获取教材与著作总分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTextbooksTotalScore = async (req, res) => {
  try {
    const { range = 'all', reviewStatus = 'all' } = req.body;

    let sequelize;
    try {
      sequelize = getSequelizeInstance(textbooksModel);
    } catch (error) {
      return res.status(500).json({
        code: 500,
        message: '无法获取数据库连接实例',
        error: error.message
      });
    }

    let userId = null; 
    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    // 获取数据库连接
    const results = await sequelize.query(
      'CALL get_textbooks_total_score(?, ?, ?)',
      {
        replacements: [range, reviewStatus, userId],
        type: Sequelize.QueryTypes.RAW,
      }
    );
    
    console.log("存储过程返回结果:", JSON.stringify(results));
    
    // 处理查询结果
    let categoryStats = [];
    let overallStats = { totalTextbooks: 0, totalScore: 0 };
    let timeInterval = null;
    
    // 修正处理逻辑，直接处理返回的单个数组
    if (Array.isArray(results) && results.length > 0) {
      // 如果存储过程返回了单个数组，直接使用它作为类别统计
      categoryStats = results.map(item => ({
        ...item,
        count: parseInt(item.count || 0, 10),
        totalScore: parseFloat(item.totalScore || 0)
      }));
      
      // 根据categoryStats计算总体统计
      overallStats = {
        totalTextbooks: categoryStats.reduce((sum, item) => sum + (item.count || 0), 0),
        totalScore: parseFloat(categoryStats.reduce((sum, item) => sum + (item.totalScore || 0), 0).toFixed(2))
      };
    }
    
    // 获取时间区间
    try {
      const timeIntervalData = await getTimeIntervalByName("textbooks");
      if (timeIntervalData) {
        timeInterval = {
          startTime: timeIntervalData.startTime,
          endTime: timeIntervalData.endTime,
          name: timeIntervalData.name
        };
      }
    } catch (error) {
      console.error("获取时间区间失败:", error);
    }

    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取教材与著作总分统计成功',
      data: {
        categoryStats: categoryStats,
        overallStats: overallStats,
        timeInterval: timeInterval
      }
    });
  } catch (error) {
    console.error('获取教材与著作总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教材与著作总分统计失败: ' + error.message,
      error: error.message
    });
  }
};

/**
 * 获取用户教材与著作详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserTextbooksDetail = async (req, res) => {
  try {
    const { 
      userId, 
      range = 'all', 
      reviewStatus = 'all',
      pageSize = 10,
      page = 1
    } = req.body;

    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }

    // 查询用户是否存在
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 获取数据库连接
    let sequelize;
    try {
      sequelize = getSequelizeInstance(textbooksModel);
    } catch (error) {
      return res.status(500).json({
        code: 500,
        message: '无法获取数据库连接实例',
        error: error.message
      });
    }

    const results = await sequelize.query(
      'CALL get_user_textbooks_detail(?, ?, ?, ?, ?)',
      {
        replacements: [userId, range, reviewStatus, pageSize, page],
        type: Sequelize.QueryTypes.RAW,
      }
    );
    
    console.log("存储过程返回结果:", JSON.stringify(results));

    // 处理查询结果
    let totalCount = 0;
    let detailsList = [];
    let userStatistics = { 
      totalTextbooks: 0, 
      totalScore: 0, 
      approvedTextbooks: 0, 
      activeTextbooks: 0 
    };
    let timeInterval = null;
    
    // 分析和处理返回的结果结构
    if (Array.isArray(results) && results.length > 0) {
      // 第一个结果集: 总记录数
      if (results[0] && Array.isArray(results[0]) && results[0].length > 0) {
        totalCount = parseInt(results[0][0].totalCount || 0, 10);
      }
      
      // 第二个结果集: 教材详情列表
      if (results.length > 1 && Array.isArray(results[1])) {
        detailsList = results[1].map(item => ({
          ...item,
          score: parseFloat(item.score || 0),
          ifReviewer: item.ifReviewer == null ? null : parseInt(item.ifReviewer, 10)
        }));
      }
      
      // 第三个结果集: 用户统计数据
      if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
        const stats = results[2][0];
        if (stats) {
          userStatistics = {
            totalTextbooks: parseInt(stats.totalTextbooks || 0, 10),
            totalScore: parseFloat(stats.totalScore || 0),
            approvedTextbooks: parseInt(stats.approvedTextbooks || 0, 10),
            activeTextbooks: parseInt(stats.activeTextbooks || 0, 10)
          };
        }
      }
      
      // 第四个结果集: 时间区间
      if (results.length > 3 && Array.isArray(results[3]) && results[3].length > 0) {
        const timeData = results[3][0];
        if (timeData) {
          timeInterval = {
            startTime: timeData.startTime,
            endTime: timeData.endTime,
            name: timeData.name
          };
        }
      }
    }
    
    // 如果没有从存储过程获得时间区间，则从数据库直接获取
    if (!timeInterval) {
      try {
        const timeIntervalData = await getTimeIntervalByName("textbooks");
        if (timeIntervalData) {
          timeInterval = {
            startTime: timeIntervalData.startTime,
            endTime: timeIntervalData.endTime,
            name: timeIntervalData.name
          };
        }
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }

    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取用户教材与著作详情成功',
      data: {
        list: detailsList,
        totalCount: totalCount,
        statistics: userStatistics,
        timeInterval: timeInterval,
        pagination: {
          page: parseInt(page, 10),
          pageSize: parseInt(pageSize, 10),
          total: totalCount,
          totalPages: Math.ceil(totalCount / parseInt(pageSize, 10))
        }
      }
    });
  } catch (error) {
    console.error('获取用户教材与著作详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户教材与著作详情失败: ' + error.message,
      error: error.message
    });
  }
}; 

/**
 * 重新提交教材审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapply = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(textbooksModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少教材ID',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找教材
    const textbook = await textbooksModel.findByPk(id, { transaction });
    
    if (!textbook) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '教材不存在',
        data: null
      });
    }
    
    // 检查教材所有权或管理员权限
    const isOwner = textbook.userId === userInfo.id;
    const isAdmin = userInfo.role.roleAuth === 'TEACHER-LV1' || userInfo.role.roleAuth === 'SUPER' || userInfo.role.roleAuth === 'ADMIN-LV2';
    
    if (!isOwner && !isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限重新提交该教材',
        data: null
      });
    }
    
    // 检查当前审核状态，只有被拒绝的教材可以重新提交
    if (textbook.ifReviewer != 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '只有被拒绝的教材可以重新提交审核',
        data: null
      });
    }
    
    // 更新教材状态为待审核
    await textbook.update({
      ifReviewer: null,  // 设置为待审核状态
      reviewComment: null, // 清空之前的审核意见
      reviewerId: null // 清空之前的审核人
    }, { transaction });
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('重新提交审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '重新提交审核失败',
      error: error.message
    });
  }
};