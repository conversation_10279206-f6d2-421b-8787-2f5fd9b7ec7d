const { Op } = require('sequelize');
const deductionsRulesModel = require('../../../models/v1/mapping/deductionsRulesModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取扣减规则列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeductionsRules = async (req, res) => {
  try {
    console.log('🔍 获取扣减规则列表 - 请求参数:', req.query);
    
    // 获取查询参数
    const { page = 1, pageSize = 10 } = req.query;
    
    // 确保 page 和 pageSize 是有效的数字，否则使用默认值
    const pageNum = page ? parseInt(page) : 1;
    const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

    // 如果 page 或 pageSize 是无效数字，返回默认值
    const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
    const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;
    
    console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

    // 分页查询
    const offset = (validPage - 1) * validPageSize;
    try {
      console.log('📚 执行数据库查询...');
      
      // 查询未按时获得学位的规则
      const degreeRules = await deductionsRulesModel.findAndCountAll({
        where: {
          unTimelyDegreeCount: {
            [Op.ne]: null
          }
        },
        offset,
        limit: validPageSize,
        order: [['createdAt', 'DESC']]
      });

      // 查询未就业的规则
      const employmentRules = await deductionsRulesModel.findAndCountAll({
        where: {
          unemploymentDate: {
            [Op.ne]: null
          }
        },
        offset,
        limit: validPageSize,
        order: [['createdAt', 'DESC']]
      });
      
      console.log(`✅ 查询成功: 学位规则${degreeRules.count}条, 就业规则${employmentRules.count}条`);
      
      const degreeTotalPages = Math.ceil(degreeRules.count / validPageSize);
      const employmentTotalPages = Math.ceil(employmentRules.count / validPageSize);
      
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          degreeRules: {
            total: degreeRules.count,
            page: validPage,
            pageSize: validPageSize,
            totalPages: degreeTotalPages,
            list: degreeRules.rows
          },
          employmentRules: {
            total: employmentRules.count,
            page: validPage,
            pageSize: validPageSize,
            totalPages: employmentTotalPages,
            list: employmentRules.rows
          }
        }
      });
    } catch (dbError) {
      console.error('❌ 数据库查询失败:', dbError);
      return res.status(500).json({
        code: 500,
        message: `数据库查询失败: ${dbError.message}`,
        data: null
      });
    }
  } catch (error) {
    console.error('❌ 获取扣减规则列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取扣减规则列表失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取扣减规则详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeductionsRuleDetail = async (req, res) => {
  try {
    const { id } = req.query;
    console.log('🔍 获取扣减规则详情 - ID:', id);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    const rule = await deductionsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }
    
    console.log(`✅ 查询成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 获取扣减规则详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取扣减规则详情失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 创建扣减规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createDeductionsRule = async (req, res) => {
  try {
    const { 
      unTimelyDegreeCount,
      unTimelyDegreeDeduction,
      unemploymentDate,
      unemploymentDeduction
    } = req.body;
    
    console.log('📝 创建扣减规则 - 请求数据:', {
      unTimelyDegreeCount,
      unTimelyDegreeDeduction,
      unemploymentDate,
      unemploymentDeduction
    });
    
    // 创建规则
    const rule = await deductionsRulesModel.create({
      id: uuidv4(),
      unTimelyDegreeCount,
      unTimelyDegreeDeduction,
      unemploymentDate,
      unemploymentDeduction,
      createdBy: req.user.id
    });
    
    console.log(`✅ 创建成功: 规则ID ${rule.id}`);
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 创建扣减规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `创建扣减规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 更新扣减规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateDeductionsRule = async (req, res) => {
  try {
    const { 
      id,
      unTimelyDegreeCount,
      unTimelyDegreeDeduction,
      unemploymentDate,
      unemploymentDeduction
    } = req.body;
    
    console.log('📝 更新扣减规则 - 请求数据:', {
      id,
      unTimelyDegreeCount,
      unTimelyDegreeDeduction,
      unemploymentDate,
      unemploymentDeduction
    });

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    // 查找规则
    const rule = await deductionsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }

    // 更新规则
    await rule.update({
      unTimelyDegreeCount: unTimelyDegreeCount !== undefined ? unTimelyDegreeCount : rule.unTimelyDegreeCount,
      unTimelyDegreeDeduction: unTimelyDegreeDeduction !== undefined ? unTimelyDegreeDeduction : rule.unTimelyDegreeDeduction,
      unemploymentDate: unemploymentDate || rule.unemploymentDate,
      unemploymentDeduction: unemploymentDeduction !== undefined ? unemploymentDeduction : rule.unemploymentDeduction
    });
    
    console.log(`✅ 更新成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 更新扣减规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `更新扣减规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 删除扣减规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteDeductionsRule = async (req, res) => {
  try {
    const { id } = req.query;
    console.log('🗑️ 删除扣减规则 - ID:', id);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    const rule = await deductionsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }

    await rule.destroy();
    
    console.log(`✅ 删除成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('❌ 删除扣减规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `删除扣减规则失败: ${error.message}`,
      data: null
    });
  }
}; 