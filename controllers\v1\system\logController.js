const { UserOptLog } = require('../../../models');
const { Op } = require('sequelize');
const { Sequelize } = require('sequelize');

/**
 * 获取操作日志列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLogs = async (req, res) => {
  try {
    const { username, operation, startTime, endTime, status, page = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (username) where.username = { [Op.like]: `%${username}%` };
    if (operation) where.operation = { [Op.like]: `%${operation}%` };
    if (status !== undefined) where.status = status;
    
    // 添加时间范围查询
    if (startTime && endTime) {
      where.createdAt = {
        [Op.between]: [new Date(startTime), new Date(endTime)]
      };
    } else if (startTime) {
      where.createdAt = {
        [Op.gte]: new Date(startTime)
      };
    } else if (endTime) {
      where.createdAt = {
        [Op.lte]: new Date(endTime)
      };
    }
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await UserOptLog.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['createdAt', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取操作日志列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取操作日志列表失败',
      data: null
    });
  }
};

/**
 * 获取操作日志详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLogById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const log = await UserOptLog.findByPk(id);
    if (!log) {
      return res.status(404).json({
        code: 404,
        message: '日志不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: log
    });
  } catch (error) {
    console.error('获取操作日志详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取操作日志详情失败',
      data: null
    });
  }
};

/**
 * 清空日志
 * @route POST /v1/logs/clear
 * @group 日志管理 - 系统日志相关接口
 * @returns {object} 200 - {"status": 1,"message": "清空成功","data": null,"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.clearLogs = async (req, res) => {
  try {
    // 检查用户权限
    const user = req.user;
    const userRoles = await user.getRoles();
    const isAdmin = userRoles.some(role => role.code === 'admin');
    
    if (!isAdmin) {
      return res.status(403).json({
        code: 403,
        message: '权限不足',
        data: null
      });
    }
    
    // 清空日志表
    await UserOptLog.destroy({
      where: {},
      truncate: true
    });
    
    // 记录清空操作
    await UserOptLog.create({
      userId: user.id,
      username: user.username,
      operation: '清空操作日志',
      method: 'DELETE',
      path: '/v1/logs/clear',
      ip: req.ip,
      status: 1,
      result: '清空操作日志成功'
    });
    
    return res.status(200).json({
      code: 200,
      message: '清空操作日志成功',
      data: null
    });
  } catch (error) {
    console.error('清空操作日志失败:', error);
    return res.status(500).json({
      code: 500,
      message: '清空操作日志失败',
      data: null
    });
  }
};

/**
 * 批量删除日志
 * @route POST /v1/logs/batch
 * @group 日志管理 - 系统日志相关接口
 * @param {array} ids.body.required - 日志ID列表
 * @returns {object} 200 - {"status": 1,"message": "删除成功","data": null,"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
exports.batchDeleteLogs = async (req, res) => {
  try {
    const { ids } = req.body;
    
    // 校验参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '日志ID不能为空',
        data: null
      });
    }
    
    // 检查用户权限
    const user = req.user;
    const userRoles = await user.getRoles();
    const isAdmin = userRoles.some(role => role.code === 'admin');
    
    if (!isAdmin) {
      return res.status(403).json({
        code: 403,
        message: '权限不足',
        data: null
      });
    }
    
    // 批量删除日志
    const result = await UserOptLog.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });
    
    // 记录删除操作
    await UserOptLog.create({
      userId: user.id,
      username: user.username,
      operation: '批量删除操作日志',
      method: 'DELETE',
      path: '/v1/logs/batch',
      ip: req.ip,
      status: 1,
      result: `批量删除操作日志成功，共删除 ${result} 条记录`
    });
    
    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: {
        count: result
      }
    });
  } catch (error) {
    console.error('批量删除操作日志失败:', error);
    return res.status(500).json({
      code: 500,
      message: '批量删除操作日志失败',
      data: null
    });
  }
};

/**
 * 获取操作日志统计信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLogStats = async (req, res) => {
  try {
    const { startTime, endTime } = req.body;
    
    // 构建查询条件
    const where = {};
    
    // 添加时间范围查询
    if (startTime && endTime) {
      where.createdAt = {
        [Op.between]: [new Date(startTime), new Date(endTime)]
      };
    } else if (startTime) {
      where.createdAt = {
        [Op.gte]: new Date(startTime)
      };
    } else if (endTime) {
      where.createdAt = {
        [Op.lte]: new Date(endTime)
      };
    }
    
    // 总日志数
    const totalCount = await UserOptLog.count({ where });
    
    // 按模块统计
    const moduleStats = await UserOptLog.findAll({
      attributes: [
        'module',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where,
      group: ['module'],
      order: [[Sequelize.literal('count'), 'DESC']],
      raw: true
    });
    
    // 按操作人统计
    const operatorStats = await UserOptLog.findAll({
      attributes: [
        'operator',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where,
      group: ['operator'],
      order: [[Sequelize.literal('count'), 'DESC']],
      limit: 10,
      raw: true
    });
    
    // 按IP统计
    const ipStats = await UserOptLog.findAll({
      attributes: [
        'operatorIP',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where,
      group: ['operatorIP'],
      order: [[Sequelize.literal('count'), 'DESC']],
      limit: 10,
      raw: true
    });
    
    // 按时间统计（最近7天）
    const now = new Date();
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();
    
    const dailyStats = await Promise.all(
      last7Days.map(async (date) => {
        const startOfDay = new Date(`${date}T00:00:00Z`);
        const endOfDay = new Date(`${date}T23:59:59Z`);
        
        const count = await UserOptLog.count({
          where: {
            ...where,
            createdAt: {
              [Op.between]: [startOfDay, endOfDay]
            }
          }
        });
        
        return { date, count };
      })
    );
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalCount,
        moduleStats: moduleStats.filter(item => item.module), // 过滤掉module为null的记录
        operatorStats,
        ipStats,
        dailyStats
      }
    });
  } catch (error) {
    console.error('获取操作日志统计信息失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取操作日志统计信息失败',
      data: null
    });
  }
}; 