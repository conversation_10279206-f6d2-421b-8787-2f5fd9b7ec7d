const { Op, Sequelize } = require('sequelize');
const { getUserInfoFromRequest, getTimeIntervalByName } = require('../../../utils/others');
const teachingResearchAwardLevelsModel = require('../../../models/v1/mapping/teachingResearchAwardLevelsModel');
const teachingResearchAwardsModel = require('../../../models/v1/mapping/teachingResearchAwardsModel');
const teachingResearchAwardParticipantsModel = require('../../../models/v1/mapping/teachingResearchAwardParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取教学科研奖励级别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeachingResearchAwardLevels = async (req, res) => {
    try {
        console.log('🔍 获取教学科研奖励级别列表 - 请求参数:', req.body);

        // 获取查询参数（支持POST请求的body参数）
        const { page = 1, pageSize = 10, levelName } = req.body;

        // 确保 page 和 pageSize 是有效的数字，否则使用默认值
        const pageNum = page ? parseInt(page) : 1;
        const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

        // 如果 page 或 pageSize 是无效数字，返回默认值
        const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
        const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;

        console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

        // 构建查询条件
        const where = {};
        if (levelName) {
            where.levelName = { [Op.like]: `%${levelName}%` };
        }

        console.log('🔍 最终查询条件:', JSON.stringify(where));

        // 分页查询
        const offset = (validPage - 1) * validPageSize;
        try {
            console.log('📚 执行数据库查询...');
            const { count, rows } = await teachingResearchAwardLevelsModel.findAndCountAll({
                where,
                offset,
                limit: validPageSize,
                order: [['createdAt', 'DESC']] // 默认按时间倒序
            });

            console.log(`✅ 查询成功: 共${count}条记录`);

            const totalPages = Math.ceil(count / validPageSize);

            return res.status(200).json({
                code: 200,
                message: '获取成功',
                data: {
                    total: count,
                    page: validPage,
                    pageSize: validPageSize,
                    totalPages,
                    list: rows
                }
            });
        } catch (dbError) {
            console.error('❌ 数据库查询失败:', dbError);
            return res.status(500).json({
                code: 500,
                message: `数据库查询失败: ${dbError.message}`,
                data: null
            });
        }
    } catch (error) {
        console.error('❌ 获取教学科研奖励级别列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取教学科研奖励级别列表失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取所有教学科研奖励级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllTeachingResearchAwardLevels = async (req, res) => {
    try {
        const levels = await teachingResearchAwardLevelsModel.findAll();
        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: levels
        });
    } catch (error) {
        console.error('❌ 获取所有教学科研奖励级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取所有教学科研奖励级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取教学科研奖励级别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeachingResearchAwardLevelDetail = async (req, res) => {
    try {
        const { id } = req.query;
        console.log('🔍 获取教学科研奖励级别详情 - ID:', id);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        const level = await teachingResearchAwardLevelsModel.findByPk(id);
        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '未找到该级别',
                data: null
            });
        }

        console.log(`✅ 查询成功: 级别ID ${id}`);

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: level
        });
    } catch (error) {
        console.error('❌ 获取教学科研奖励级别详情失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取教学科研奖励级别详情失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 创建教学科研奖励级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createTeachingResearchAwardLevel = async (req, res) => {
    try {
        const { levelName, score, description } = req.body;
        console.log('📝 创建教学科研奖励级别 - 请求数据:', { levelName, score, description });

        // 校验必填参数
        if (!levelName || score === undefined) {
            return res.status(400).json({
                code: 400,
                message: '级别名称和基础分数不能为空',
                data: null
            });
        }

        // 检查级别名称是否已存在
        const existingLevel = await teachingResearchAwardLevelsModel.findOne({
            where: { levelName }
        });

        if (existingLevel) {
            return res.status(400).json({
                code: 400,
                message: '该级别名称已存在',
                data: null
            });
        }

        const userInfo = await getUserInfoFromRequest(req);
        const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');

        if (!isAdmin) {
            return res.status(403).json({
              code: 403,
              message: '您没有权限创建教学科研奖励级别',
              data: null
            });
          }

        // 创建级别
        const level = await teachingResearchAwardLevelsModel.create({
            id: uuidv4(),
            levelName,
            score,
            description,
            status: 1
        });

        console.log(`✅ 创建成功: 级别ID ${level.id}`);

        return res.status(201).json({
            code: 200,
            message: '创建成功',
            data: level
        });
    } catch (error) {
        console.error('❌ 创建教学科研奖励级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `创建教学科研奖励级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 更新教学科研奖励级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateTeachingResearchAwardLevel = async (req, res) => {
    try {
        // 从路由处理器中，id 被放在 req.body 中（直接传递的情况）
        // 或者从 req.params 中（路由处理器处理后的情况）
        const id = req.body.id || req.params.id;
        const { levelName, score, description, status } = req.body;
        console.log('📝 更新教学科研奖励级别 - 请求数据:', { id, levelName, score, description, status });

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        // 查找级别
        const level = await teachingResearchAwardLevelsModel.findByPk(id);
        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '未找到该级别',
                data: null
            });
        }

        // 如果更新了级别名称，检查是否与其他记录重复
        if (levelName && levelName !== level.levelName) {
            const existingLevel = await teachingResearchAwardLevelsModel.findOne({
                where: {
                    id: { [Op.ne]: id },
                    levelName
                }
            });

            if (existingLevel) {
                return res.status(400).json({
                    code: 400,
                    message: '该级别名称已存在',
                    data: null
                });
            }
        }

        // 更新级别
        await level.update({
            levelName: levelName || level.levelName,
            score: score !== undefined ? score : level.score,
            description: description !== undefined ? description : level.description,
            status: status !== undefined ? status : level.status
        });

        console.log(`✅ 更新成功: 级别ID ${id}`);

        return res.status(200).json({
            code: 200,
            message: '更新成功',
            data: level
        });
    } catch (error) {
        console.error('❌ 更新教学科研奖励级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `更新教学科研奖励级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 删除教学科研奖励级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteTeachingResearchAwardLevel = async (req, res) => {
    try {
        // 从路由处理器中，id 被放在 req.body 中（直接传递的情况）
        // 或者从 req.params 中（路由处理器处理后的情况）
        // 或者从 req.query 中（GET请求的情况）
        const id = req.body.id || req.params.id || req.query.id;
        console.log('🗑️ 删除教学科研奖励级别 - ID:', id);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        const level = await teachingResearchAwardLevelsModel.findByPk(id);
        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '未找到该级别',
                data: null
            });
        }

        await level.destroy();

        console.log(`✅ 删除成功: 级别ID ${id}`);

        return res.status(200).json({
            code: 200,
            message: '删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 删除教学科研奖励级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `删除教学科研奖励级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 批量删除教学科研奖励级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteTeachingResearchAwardLevels = async (req, res) => {
    try {
        const { ids } = req.body;
        console.log('🗑️ 批量删除教学科研奖励级别 - IDs:', ids);

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                code: 400,
                message: 'ID列表不能为空',
                data: null
            });
        }

        await teachingResearchAwardLevelsModel.destroy({
            where: {
                id: {
                    [Op.in]: ids
                }
            }
        });

        console.log(`✅ 批量删除成功: 共${ids.length}条记录`);

        return res.status(200).json({
            code: 200,
            message: '批量删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 批量删除教学科研奖励级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: `批量删除教学科研奖励级别失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取级别及其奖励数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelsWithCount = async (req, res) => {
    try {
        console.log('🔍 获取级别及其奖励数量');

        // 查询所有级别及其对应的奖励数量
        const levels = await teachingResearchAwardLevelsModel.findAll({
            attributes: [
                'id',
                'levelName',
                'score',
                'description',
                'status',
                [
                    Sequelize.fn('COUNT', Sequelize.col('awards.id')),
                    'awardCount'
                ]
            ],
            include: [
                {
                    model: teachingResearchAwardsModel,
                    as: 'awards',
                    attributes: [],
                    required: false
                }
            ],
            group: ['TeachingResearchAwardLevels.id'],
            order: [['score', 'DESC'], ['createdAt', 'DESC']],
            raw: false
        });

        // 处理返回数据
        const processedLevels = levels.map(level => {
            const levelJson = level.toJSON();
            return {
                ...levelJson,
                awardCount: parseInt(levelJson.awardCount) || 0
            };
        });

        console.log(`📋 查询结果: ${processedLevels.length} 条级别数据`);

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: processedLevels
        });
    } catch (error) {
        console.error('❌ 获取级别及其奖励数量失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取级别及其奖励数量失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
    try {
        const { range = 'all', userId } = req.body;
        console.log('🔍 获取级别分布数据', { range, userId });

        // 构建查询条件
        const where = {};

        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("teachingResearchAwards");

        // 根据range参数添加时间筛选
        if (timeInterval && range !== 'all') {
            const intervalStartTime = new Date(timeInterval.startTime);
            const intervalEndTime = new Date(timeInterval.endTime);

            if (range === 'in') {
                // 在时间范围内
                where.awardTime = {
                    [Op.gte]: intervalStartTime,
                    [Op.lte]: intervalEndTime
                };
            } else if (range === 'out') {
                // 在时间范围外
                where[Op.or] = [
                    { awardTime: { [Op.lt]: intervalStartTime } },
                    { awardTime: { [Op.gt]: intervalEndTime } }
                ];
            }
        }

        // 如果指定了userId，添加用户参与条件
        if (userId) {
            // 查询用户参与的奖励ID
            const participantAwards = await teachingResearchAwardParticipantsModel.findAll({
                where: { participantId: userId },
                attributes: ['awardId'],
                raw: true
            });

            const awardIds = participantAwards.map(p => p.awardId);

            // 添加用户条件：是第一负责人或参与者
            where[Op.or] = [
                { firstResponsibleId: userId },
                { id: { [Op.in]: awardIds } }
            ];
        }

        // 查询级别分布
        const distribution = await teachingResearchAwardsModel.findAll({
            where,
            attributes: [
                'awardLevelId',
                [Sequelize.fn('COUNT', Sequelize.col('teaching_research_awards.id')), 'count']
            ],
            include: [
                {
                    model: teachingResearchAwardLevelsModel,
                    as: 'awardLevel',
                    attributes: ['id', 'levelName', 'score'],
                    required: false
                }
            ],
            group: ['awardLevelId', 'awardLevel.id'],
            order: [[Sequelize.fn('COUNT', Sequelize.col('teaching_research_awards.id')), 'DESC']],
            raw: false
        });

        // 格式化返回数据
        const result = distribution.map(item => {
            const itemJson = item.toJSON();
            return {
                name: itemJson.awardLevel ? itemJson.awardLevel.levelName : '未知级别',
                value: parseInt(itemJson.count) || 0,
                levelId: itemJson.awardLevelId,
                score: itemJson.awardLevel ? itemJson.awardLevel.score : 0
            };
        });

        console.log(`📊 级别分布统计结果: ${result.length} 个级别`);

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: result
        });
    } catch (error) {
        console.error('❌ 获取级别分布数据失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取级别分布数据失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取指定级别下的奖励列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardsByLevel = async (req, res) => {
    try {
        const { id } = req.params;
        const { page = 1, pageSize = 10 } = req.query;

        console.log('🔍 获取级别下的奖励列表', { levelId: id, page, pageSize });

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: '级别ID不能为空',
                data: null
            });
        }

        // 验证级别是否存在
        const level = await teachingResearchAwardLevelsModel.findByPk(id);
        if (!level) {
            return res.status(404).json({
                code: 404,
                message: '未找到该级别',
                data: null
            });
        }

        // 分页参数
        const pageNum = parseInt(page);
        const pageSizeNum = parseInt(pageSize);
        const offset = (pageNum - 1) * pageSizeNum;

        // 查询该级别下的奖励
        const { count, rows } = await teachingResearchAwardsModel.findAndCountAll({
            where: { awardLevelId: id },
            include: [
                {
                    model: userModel,
                    as: 'firstResponsible',
                    attributes: ['id', 'nickname', 'username', 'studentNumber'],
                    required: false
                },
                {
                    model: teachingResearchAwardParticipantsModel,
                    as: 'participants',
                    include: [
                        {
                            model: userModel,
                            as: 'participant',
                            attributes: ['id', 'nickname', 'username', 'studentNumber'],
                            required: false
                        }
                    ],
                    required: false
                }
            ],
            offset,
            limit: pageSizeNum,
            order: [['awardTime', 'DESC']]
        });

        // 处理返回数据
        const processedAwards = rows.map(award => {
            const awardJson = award.toJSON();

            // 处理参与者信息
            if (awardJson.participants && awardJson.participants.length > 0) {
                awardJson.memberDetails = awardJson.participants.map(p => ({
                    id: p.participantId,
                    name: p.participant ? (p.participant.nickname || p.participant.username) : '未知用户',
                    allocationRatio: p.allocationRatio,
                    isLeader: p.isLeader
                }));
            }

            return awardJson;
        });

        console.log(`📋 查询结果: 级别"${level.levelName}"下共${count}个奖励`);

        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: {
                level: {
                    id: level.id,
                    levelName: level.levelName,
                    score: level.score,
                    description: level.description
                },
                awards: {
                    list: processedAwards,
                    pagination: {
                        total: count,
                        page: pageNum,
                        pageSize: pageSizeNum,
                        totalPages: Math.ceil(count / pageSizeNum)
                    }
                }
            }
        });
    } catch (error) {
        console.error('❌ 获取级别下的奖励列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取级别下的奖励列表失败: ${error.message}`,
            data: null
        });
    }
};
