const express = require('express');
const teacherController = require('../../../controllers/v1/sys/teacherController');

const router = express.Router();

/**
 * 获取教师列表
 * @route POST /v1/sys/teacher/list
 * @group 教师管理 - 教师相关接口
 * @param {object} query.body - 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'createdAt', order: 'ascend'}}
 * @returns {object} 200 - {status: "success", message: "Success.", data: {result: [], current: 1, pageSize: 15, total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', teacherController.getTeachers);

/**
 * 获取指定教师详情
 * @route POST /v1/sys/teacher/detail
 * @group 教师管理 - 教师相关接口
 * @param {number} id.body.required - 教师ID
 * @returns {object} 200 - {status: "success", message: "Success.", data: {教师信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/detail', teacherController.getTeacherById);

/**
 * 创建教师
 * @route POST /v1/sys/teacher/create
 * @group 教师管理 - 教师相关接口
 * @param {string} name.body.required - 教师姓名
 * @param {string} employeeId.body.required - 工号
 * @param {string} department.body - 所属部门
 * @param {string} title.body - 职称
 * @param {string} email.body - 邮箱
 * @param {string} phone.body - 手机号
 * @returns {object} 200 - {status: "success", message: "教师创建成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', teacherController.createTeacher);

/**
 * 更新教师
 * @route POST /v1/sys/teacher/update
 * @group 教师管理 - 教师相关接口
 * @param {number} id.body.required - 教师ID
 * @param {string} name.body - 教师姓名
 * @param {string} employeeId.body - 工号
 * @param {string} department.body - 所属部门
 * @param {string} title.body - 职称
 * @param {string} email.body - 邮箱
 * @param {string} phone.body - 手机号
 * @returns {object} 200 - {status: "success", message: "教师更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update', teacherController.updateTeacher);

/**
 * 删除教师
 * @route POST /v1/sys/teacher/delete
 * @group 教师管理 - 教师相关接口
 * @param {number} id.body.required - 教师ID
 * @returns {object} 200 - {status: "success", message: "教师删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', teacherController.deleteTeacher);

/**
 * 导入教师
 * @route POST /v1/sys/teacher/import
 * @group 教师管理 - 教师相关接口
 * @param {file} file.body.required - Excel文件
 * @returns {object} 200 - {status: "success", message: "教师导入成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', teacherController.importTeachers);

/**
 * 导出教师
 * @route POST /v1/sys/teacher/export
 * @group 教师管理 - 教师相关接口
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/export', teacherController.exportTeachers);

module.exports = router; 