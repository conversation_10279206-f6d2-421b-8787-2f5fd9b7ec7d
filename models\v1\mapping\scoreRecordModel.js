const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义评分记录模型
const ScoreRecord = sequelize.define('score_records', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: 'ID'
  },
  teacherId: {
    type: DataTypes.CHAR(36),
    allowNull: false,
    comment: '教师ID'
  },
  year: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '评分年份'
  },
  researchProjectScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '科研项目得分'
  },
  researchFundScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '科研经费得分'
  },
  paperScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '论文得分'
  },
  awardScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '获奖得分'
  },
  exchangeScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '国际交流得分'
  },
  serviceScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '社会服务得分'
  },
  employmentScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '就业质量得分'
  },
  deductionScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00,
    comment: '扣分得分'
  },
  totalScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '总分'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    defaultValue: 1,
    comment: '状态'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'score_records',
  timestamps: true,
  indexes: [
    {
      name: 'idx_score_record_teacher_id',
      fields: ['teacherId']
    },
    {
      name: 'idx_score_record_year',
      fields: ['year']
    },
    {
      name: 'idx_score_record_status',
      fields: ['status']
    }
  ]
});

module.exports = ScoreRecord; 