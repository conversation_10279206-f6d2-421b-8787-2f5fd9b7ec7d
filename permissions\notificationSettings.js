/**
 * 通知设置模块权限配置
 * 简化版 - 角色验证 + 教师数据自动填充
 */
module.exports = {
  // 查看通知设置权限
  view: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  // 更新通知设置权限
  update: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  // 重置通知设置权限
  reset: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  }
};
