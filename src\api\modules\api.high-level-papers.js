import request from '../server'

// 更高效的查询参数构建
function buildQueryString(params) {
  if (!params || Object.keys(params).length === 0) {
    return '';
  }
  
  // 添加时间戳防止缓存
  const paramsWithCache = { 
    ...params,
    _t: Date.now() 
  };
  
  const queryParams = Object.entries(paramsWithCache)
    .filter(([_, value]) => value !== undefined && value !== null && value !== '')
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  
  return queryParams ? `?${queryParams}` : '';
}

/**
 * 获取高水平论文列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getHighLevelPapers(params) {
  return request.get('/highLevelPapers/list', params)
}

/**
 * 获取个人高水平论文列表
 * @param {String} userId - 用户ID
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getPersonalHighLevelPapers(params) {
  return request.get('/highLevelPapers/list', params)
}

/**
 * 获取论文统计数据
 * @param {Object} params
 * @returns {Promise}
 */
export function getPaperStats(params) {
  return request.get('/highLevelPapers/stats', params)
}

/**
 * 获取高水平论文详情
 * @param {String} id - 论文ID
 * @returns {Promise}
 */
export function getHighLevelPaperDetail(id) {
  return request.get(`/highLevelPapers/detail/${id}?_t=${Date.now()}`)
}

/**
 * 添加高水平论文
 * @param {Object} data - 论文数据
 * @returns {Promise}
 */
export function addHighLevelPaper(data) {
  return request.post('/highLevelPapers', data)
}

/**
 * 更新高水平论文
 * @param {String} id - 论文ID
 * @param {Object} data - 论文数据
 * @returns {Promise}
 */
export function updateHighLevelPaper(id, data) {
  console.log("data====",data);
  return request.post(`/highLevelPapers/update/${id}`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 删除高水平论文
 * @param {String} id - 论文ID
 * @returns {Promise}
 */
export function deleteHighLevelPaper(id) {
  return request.delete(`/highLevelPapers/delete/${id}`)
}

/**
 * 导入高水平论文数据
 * @param {File} file - 要上传的文件
 * @returns {Promise}
 */
export function importHighLevelPapers(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request.post('/highLevelPapers/import', formData, null, 'multipart/form-data')
}

/**
 * 导出高水平论文数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function exportHighLevelPapers(params) {
  const queryString = buildQueryString(params);
  return request.download(`/highLevelPapers/export${queryString}`)
}

/**
 * 获取论文类型分布数据
 * @param {Object} params
 * @returns {Promise}
 */
export function getPaperTypeDistribution(params) {
  return request.post('/highLevelPapers/type-distribution', params)
}

/**
 * 获取论文年份分布数据
 * @param {Object} params
 * @returns {Promise}
 */
export function getPaperYearlyDistribution(params) {
  return request.post('/highLevelPapers/yearly-distribution', params)
}

/**
 * 获取论文影响因子分布数据
 * @param {Object} params
 * @returns {Promise}
 */
export function getPaperImpactFactorDistribution(params) {
  return request.post('/highLevelPapers/impact-factor-distribution', params)
}

/**
 * 获取论文作者排名数据
 * @param {Object} params
 * @returns {Promise}
 */
export function getPaperTeacherRanking(params) {
  return request.post('/highLevelPapers/teacher-ranking-distribution', params)
}

/**
 * 获取所有用户的论文总分
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function getAllUsersTotalScore(params) {
  return request.post('/highLevelPapers/teacher-ranking', params)
}

/**
 * 获取用户论文总分
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function getUserTotalScore(params) {
  return request.post('/highLevelPapers/user-total-score', params)
}

/**
 * 审核论文
 * @param {String} id - 论文ID
 * @param {Object} data - 审核数据，包含 reviewStatus, reviewComment, reviewer
 * @returns {Promise}
 */
export function reviewPaper(id, data) {
  return request.post(`/highLevelPapers/${id}/review`, data)
}

/**
 * 获取论文参与者列表
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function getPaperParticipants(params) {
  const queryString = buildQueryString(params);
  return request.get(`/highLevelPaperParticipants${queryString}`);
}

/**
 * 获取论文参与者详情
 * @param {String} id - 参与者ID
 * @returns {Promise}
 */
export function getPaperParticipantById(id) {
  return request.get(`/highLevelPaperParticipants/${id}`);
}

/**
 * 添加论文参与者
 * @param {Object} data - 参与者数据
 * @returns {Promise}
 */
export function addPaperParticipant(data) {
  return request.post('/highLevelPaperParticipants', data);
}

/**
 * 更新论文参与者
 * @param {String} id - 参与者ID
 * @param {Object} data - 参与者数据
 * @returns {Promise}
 */
export function updatePaperParticipant(id, data) {
  return request.put(`/highLevelPaperParticipants/${id}`, data);
}

/**
 * 删除论文参与者
 * @param {String} id - 参与者ID
 * @returns {Promise}
 */
export function deletePaperParticipant(id) {
  return request.delete(`/highLevelPaperParticipants/${id}`);
}

/**
 * 批量添加论文参与者
 * @param {Object} data - 包含paperId和participants数组的数据
 * @returns {Promise}
 */
export function batchAddPaperParticipants(data) {
  return request.post('/highLevelPaperParticipants/batch', data);
}

/**
 * 获取高水平论文规则（类型）列表
 * @param {Object} params
 * @returns {Promise}
 */
export function getHighLevelPapersRules(params) {
  return request.get('/sys/highLevelPapersRules', params)
}

/**
 * 获取高水平论文总分统计
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getHighLevelPapersTotalScore(params) {
  return request.post('/highLevelPapers/statistics/papers-total-score', params);
}

/**
 * 获取用户高水平论文详情
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getUserHighLevelPapersDetail(params) {
  return request.post('/highLevelPapers/user/details', params);
}

/**重新提交审核 */
export function reapplyReview(params) {
  return request.post('/highLevelPapers/reapply', params)
}

/**
 * 获取审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getReviewStatusOverview(params) {
  return request.post('/highLevelPapers/statistics/review-status-overview', params)
}
