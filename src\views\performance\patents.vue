<template>
  <div class="performance-container patents-container">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />

    <a-card title="专利管理" :bordered="false" class="performance-card">
      <template #extra>
        <a-space>
          <a-upload
            :customRequest="handleImport"
            :show-upload-list="false"
            :before-upload="beforeUpload"
          >
            <a-button type="primary" v-permission="'score:patents:admin:update'">
              <template #icon><UploadOutlined /></template>
              导入数据
            </a-button>
          </a-upload>
          <a-upload
            :customRequest="handleExcelToJsonConvert"
            :show-upload-list="false"
            :before-upload="beforeExcelUpload"
            accept=".xlsx,.xls,.csv"
          >
            <a-button type="primary" v-permission="'score:patents:admin:update'">
              <template #icon><FileExcelOutlined /></template>
              Excel数据导入
            </a-button>
          </a-upload>
          
          <a-button type="primary" @click="showAddModal" v-permission="'score:patents:self:create'">
            <template #icon><PlusOutlined /></template>
            添加专利
          </a-button>
          <a-button :type="showPersonalPatents ? 'default' : 'primary'" @click="togglePersonalPatents" v-permission="'score:patents:admin'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalPatents ? '查看全部专利' : '查看我的专利' }}
          </a-button>
        </a-space>
      </template> 
        <!-- 专利填写说明区域 -->
        <a-card title="专利填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
          <a-alert
            class="mb-16"
            message="专利统计时间范围"
            :description="`统计时间：${timeRangeText || '加载中...'}`"
            type="info"
            show-icon
          />
          <div class="rule-content">
            <p><strong>填写说明：</strong></p>
            <ol class="detail-list">
              <li>专利授权时间和转化时间范围在统计时间内</li>
              <li>本表中出现的所有人名均为我院教职工</li>
              <li>审核需要提供专利授权证明和转化证明</li>
              <li>本表由第一负责人填写</li>
              <li>总分配比例应为100%</li>
              <li>"分配比例"填写百分数，如：30%、50%等</li>
            </ol>
          </div>
        </a-card>

        <!-- 图表区域 -->
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="审核状态分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="reviewStatusChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('reviewStatus', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="reviewStatusChartRef" id="reviewStatusChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="专利分类分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="categoryChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('category', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="categoryChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-bottom: 10px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="专利时间分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="timeChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('time', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="timeChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

    <!-- 用户专利得分统计表格 -->
    <a-card title="用户专利得分统计" :bordered="false" style="margin-top: 24px;">
      <template #extra>
        <a-space>
          <a-input-search
            v-model:value="userScoreSearchParams.nickname"
            v-permission="'score:patents:admin:list'"
            placeholder="用户昵称"
            style="width: 150px;"
            @search="fetchAllUsersTotalScore"
            @pressEnter="fetchAllUsersTotalScore"
          />
          <a-select
            v-model:value="userScoreChartRange"
            style="width: 150px;"
            @change="handleUserScoreRangeChange"
          >
            <a-select-option value="in">统计范围内</a-select-option>
            <a-select-option value="out">统计范围外</a-select-option>
            <a-select-option value="all">全部专利</a-select-option>
          </a-select>
          <a-button type="primary" @click="exportUserScoreData" :loading="exporting" v-permission="'score:patents:admin:list'">
            <template #icon><DownloadOutlined /></template>
            导出
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="userScoreColumns"
        :data-source="userScoreData"
        :pagination="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' ? userScorePagination : false"
        :loading="userScoreLoading"
        rowKey="userId"
        @change="handleUserScoreTableChange"
        :scroll="{ x: 800 }"
        :bordered="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'rank'">
            <a-tag :color="getRankColor(record.rank)">{{ record.rank }}</a-tag>
          </template>
          <template v-else-if="column.key === 'totalScore'">
            <span style="font-weight: bold; color: #1890ff;">{{ parseFloat(record.totalScore).toFixed(2) }}分</span>
          </template>
          <template v-else-if="column.key === 'details'">
            <a-button type="link" @click="showUserScoreDetails(record)"
            v-if="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId"
            >
              查看详情
            </a-button>
          </template>
          <template v-else-if="column.key === 'userScore'">
            <span style="font-weight: bold; color: #1890ff;">{{ parseFloat(record.userScore).toFixed(2) }}分</span>
          </template>
        </template>
      </a-table>
    </a-card>

        <!-- 搜索表单 -->
        <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
          <a-form :model="searchForm" @finish="handleSearch" layout="vertical" class="performance-form">
            <a-row :gutter="[12, 8]">
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="专利名称" name="patentName">
                  <a-input
                    v-model:value="searchForm.patentName"
                    placeholder="请输入专利名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="专利分类" name="categoryId">
                  <a-select
                    v-model:value="searchForm.categoryId"
                    placeholder="请选择专利分类"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="category in categoryOptions" :key="category.id" :value="category.id">
                      {{ category.categoryName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="授权日期" name="dateRange">
                  <a-range-picker
                    v-model:value="searchForm.dateRange"
                    :format="'YYYY-MM-DD'"
                    style="width: 100%"
                    :placeholder="['开始日期', '结束日期']"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="统计范围" name="range">
                  <a-select
                    v-model:value="searchForm.range"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="in">统计范围内</a-select-option>
                    <a-select-option value="out">统计范围外</a-select-option>
                    <a-select-option value="all">全部专利</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="审核状态" name="reviewStatus">
                  <a-select
                    v-model:value="searchForm.reviewStatus"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部状态</a-select-option>
                    <a-select-option value="reviewed">已审核</a-select-option>
                    <a-select-option value="reject">已拒绝</a-select-option>
                    <a-select-option value="pending">待审核</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
                <a-form-item label=" " style="margin-bottom: 0;">
                  <div class="search-actions-inline">
                    <a-button type="primary" html-type="submit" size="default">
                      <template #icon><SearchOutlined /></template>
                      搜索
                    </a-button>
                    <a-button @click="resetSearch" size="default">
                      <template #icon><ReloadOutlined /></template>
                      重置
                    </a-button>
                    <a-button type="default" @click="exportCurrentFiltered" :loading="exportingCurrent" size="default">
                      <template #icon><DownloadOutlined /></template>
                      导出
                    </a-button>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 数据表格 -->
        <div class="performance-table">
          <a-table
            :columns="columns"
            :data-source="dataSource"
            :loading="isLoading"
            :pagination="pagination"
            @change="handleTableChange"
            rowKey="id"
            :scroll="{ x: 1200 }"
            :bordered="true"
          >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'score'">
            <span v-if="record.score > 0" style="font-weight: bold; color: #1890ff;">{{ record.score }}分</span>
            <span v-else style="color: #999999;">不计分</span>
          </template>
          <template v-else-if="column.key === 'participants'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ formatParticipantsWithAllocation(record.participants, record) }}</span>
          </template>
          <template v-else-if="column.key === 'firstResponsible'">
            <span style="word-break: break-all; white-space: pre-wrap;">
              {{ record.firstResponsible ? (record.firstResponsible.nickname || record.firstResponsible.username) : '-' }}
            </span>
          </template>
          <template v-else-if="column.key === 'patentName'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ record.patentName }}</span>
          </template>
          <template v-else-if="column.key === 'categoryName'">
            <a-tag color="blue">{{ record.categoryName }}</a-tag>
          </template>
          <template v-else-if="column.key === 'ifReviewer'">
            <a-tag :color="record.ifReviewer === 1 ? 'success' : (record.ifReviewer === 0 ? 'error' : 'warning')">
              {{ record.ifReviewer === 1 ? '已审核' : (record.ifReviewer === 0 ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'reviewComment'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ record.reviewComment }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown placement="bottomRight" :trigger="['click']">
              <template #overlay>
                <a-menu class="action-dropdown-menu">
                  <!-- 编辑选项 -->
                  <a-menu-item
                    key="edit"
                    v-if="record.ifReviewer != 1 && hasPerms(showPersonalPatents ? 'score:patents:self:update' : 'score:patents:admin:update')"
                  >
                    <a @click="handleEdit(record)" class="action-menu-item">
                      <EditOutlined />
                      <span>编辑</span>
                    </a>
                  </a-menu-item>

                  <!-- 重新提交审核选项 -->
                  <a-menu-item
                    key="resubmit"
                    v-if="record.ifReviewer === 0 && hasPerms('score:patents:self:reapply')"
                  >
                    <a @click="handleResubmit(record)" class="action-menu-item">
                      <ReloadOutlined />
                      <span>重新提交审核</span>
                    </a>
                  </a-menu-item>

                  <!-- 审核选项 - 仅管理员视图显示 -->
                  <a-menu-item
                    key="review"
                    v-if="hasPerms('score:patents:admin:review') && record.ifReviewer == null"
                  >
                    <a @click="handleReview(record)" class="action-menu-item">
                      <AuditOutlined />
                      <span>审核</span>
                    </a>
                  </a-menu-item>

                  <a-menu-divider v-if="record.ifReviewer != 1 || hasPermissionToReview(record)" />

                  <!-- 删除选项 -->
                  <a-menu-item
                    key="delete"
                    v-if="hasPerms(showPersonalPatents ? 'score:patents:self:delete' : 'score:patents:admin:delete')"
                  >
                    <a @click="confirmDelete(record)" class="action-menu-item text-danger">
                      <DeleteOutlined />
                      <span>删除</span>
                    </a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small" class="action-trigger-btn">
                操作
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
          </a-table>
        </div>

        <div class="table-footer">
          <div class="total-score">
            <span>总分：{{ totalScore.toFixed(2) }}分</span>
          </div>
        </div>
      </a-card>

    <!-- 添加专利模态框 -->
    <ZyModal
      :show="modalVisible"
      :title="isEdit ? '编辑专利' : '添加专利'"
      :min-width="600"
      :min-height="600"
      @close="handleModalCancel"
    >
      <div class="patent-form">
        <a-form 
          ref="formRef" 
          :model="formState" 
          :rules="rules" 
          :label-col="{ span: 6 }" 
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item name="patentName" label="专利名称">
            <a-input v-model:value="formState.patentName" placeholder="请输入专利名称" />
          </a-form-item>
          
          <a-form-item name="categoryId" label="专利分类">
            <a-select
              v-model:value="formState.categoryId"
              placeholder="请选择专利分类"
              style="width: 100%;"
            >
              <a-select-option v-for="category in categoryOptions" :key="category.id" :value="category.id">
                {{ category.categoryName }} ({{ category.score }}分)
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item name="authorizationDate" label="授权日期">
            <a-date-picker 
              v-model:value="formState.authorizationDate" 
              style="width: 100%;" 
              format="YYYY-MM-DD" 
              placeholder="请选择授权日期"
            />
          </a-form-item>

          <a-form-item name="conversionDate" label="转化日期">
            <a-date-picker 
              v-model:value="formState.conversionDate" 
              style="width: 100%;" 
              format="YYYY-MM-DD" 
              placeholder="请选择转化日期"
            />
          </a-form-item>

          <a-form-item name="remark" label="备注">
            <a-textarea v-model:value="formState.remark" placeholder="请输入备注" :rows="3" />
          </a-form-item>

          <!-- 添加文件上传组件 -->
          <a-form-item
            name="attachments"
            label="附件"
          >
            <a-upload
              v-model:file-list="fileList"
              :customRequest="handleFileUpload"
              :before-upload="beforeUpload"
              multiple
              :show-upload-list="false"
            >
              <a-button>
                <template #icon><UploadOutlined /></template>
                选择文件
              </a-button>
            </a-upload>
            
            <!-- 使用表格显示已上传文件 -->
            <a-table
              :columns="fileColumns"
              :data-source="fileList"
              :pagination="false"
              size="small"
              style="margin-top: 16px;"
              rowKey="uid"
              v-if="fileList.length > 0"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fileName'">
                  <span :title="record.name">{{ record.originalFileName || record.name }}</span>
                </template>
                <template v-if="column.key === 'fileSize'">
                  {{ formatFileSize(record.size || (record.data && record.data.size)) }}
                </template>
                <template v-if="column.key === 'status'">
                  <a-tag :color="record.status === 'done' ? 'success' : (record.status === 'error' ? 'error' : 'processing')">
                    {{ record.status === 'done' ? '已上传' : (record.status === 'error' ? '上传失败' : '上传中') }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="previewFile(record)" v-if="record.status === 'done'">
                      <template #icon><EyeOutlined /></template>
                      预览
                    </a-button>
                    <a-button type="link" size="small" @click="downloadFile(record)" v-if="record.status === 'done'">
                      <template #icon><DownloadOutlined /></template>
                      下载
                    </a-button>
                    <a-popconfirm
                      title="确定要删除该文件吗？此操作将同时删除服务器上的文件"
                      @confirm="confirmDeleteFile(record)"
                      okText="确认"
                      cancelText="取消"
                    >
                      <a-button type="link" danger size="small">
                        <template #icon><DeleteOutlined /></template>
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
            
            <div style="margin-top: 8px; color: #666; font-size: 12px;">
              支持上传文档、图片或压缩文件，单个文件不超过10MB，总大小不超过50MB
            </div>
          </a-form-item>

          <a-form-item label="参与人员" required>
            <div class="members-container">
              <!-- 成员添加区域 -->
              <a-row :gutter="8" style="margin-bottom: 8px;">
                <a-col :span="12">
                  <a-select
                    v-model:value="currentMember.displayName"
                    placeholder="请选择参与人员"
                    :filter-option="false"
                    show-search
                    allow-clear
                    :loading="membersSearchLoading"
                    @search="handleMembersSearch"
                    :not-found-content="membersSearchLoading ? undefined : '未找到匹配结果'"
                    @change="handleCurrentMemberChange"
                  >
                    <a-select-option v-for="(option, index) in membersOptions" :key="option.id || index" :value="option.nickname || option.username" :data="option">
                      {{ option.nickname || option.username }} ({{ option.studentNumber || '无工号' }})
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="8">
                  <a-input-number
                    v-model:value="currentMember.allocationRatio"
                    placeholder="分配比例(%)"
                    :min="0"
                    :max="100"
                    :step="5"
                    :precision="1"
                    style="width: 100%"
                    addon-after="%"
                    @change="() => validateTotalAllocation()"
                  />
                </a-col>
                <a-col :span="4">
                  <a-button type="primary" @click="addParticipant">
                    <template #icon><PlusOutlined /></template>
                    添加
                  </a-button>
                </a-col>
              </a-row>
              
              <!-- 已添加成员列表 -->
              <a-divider v-if="formState.participants && formState.participants.length > 0" style="margin: 8px 0">已添加参与人员</a-divider>
              <div v-if="formState.participants && formState.participants.length > 0">
                <p style="color: #666; font-size: 12px; margin-bottom: 8px;">已添加 {{ formState.participants.length }} 位参与人员</p>
                <a-list 
                  :data-source="formState.participants" 
                  size="small"
                  bordered
                >
                  <template #renderItem="{ item, index }">
                    <a-list-item>
                      <a-row style="width: 100%">
                        <a-col :span="8">
                          {{ item.nickname || item.username || item.participantId }}
                        </a-col>
                        <a-col :span="4">
                          <a-switch 
                            :checked="item.isLeader" 
                            @change="(checked) => toggleLeader(index, checked)"
                            checkedChildren="负责人" 
                            unCheckedChildren="参与者"
                          />
                        </a-col>
                        <a-col :span="8">
                          <a-input-number
                            v-model:value="item.allocationRatio"
                            :min="0"
                            :max="100"
                            :step="5"
                            :precision="1"
                            style="width: 90%"
                            addon-after="%"
                            @change="validateTotalAllocation"
                          />
                        </a-col>
                        <a-col :span="4" style="text-align: right">
                          <a-button type="link" danger @click="() => removeParticipant(index)">删除</a-button>
                        </a-col>
                      </a-row>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
              <div v-else style="color: #999; text-align: center; padding: 10px; border: 1px dashed #ddd; border-radius: 4px;">
                还没有添加参与人员，请先选择参与人员并点击"添加"按钮
              </div>
            </div>

            <div v-if="formState.participants.length > 0" style="margin-top: 8px; color: #ff4d4f;">
              {{ allocationMsg }}
            </div>
          </a-form-item>
          
          <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
            <a-space>
              <a-button type="primary" @click="handleModalOk" :loading="confirmLoading">
                提交
              </a-button>
              <a-button @click="handleModalCancel">
                取消
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </ZyModal>

    <!-- 文件预览弹窗 -->
    <a-modal
      :visible="previewVisible"
      :title="previewTitle"
      :footer="null"
      @cancel="previewVisible = false"
    >
      <img alt="预览图片" style="width: 100%" :src="previewImage" />
    </a-modal>
    
    <!-- 审核模态框 -->
    <a-modal
      title="专利审核"
      :visible="reviewModalVisible"
      @ok="handleReviewSubmit"
      @cancel="handleReviewCancel"
      :confirmLoading="reviewConfirmLoading"
      width="700px"
    >
      <div v-if="currentReviewPatent">
        <div class="patent-info-section">
          <h3>{{ currentReviewPatent.patentName }}</h3>
          <p><strong>授权时间：</strong>{{ currentReviewPatent.authorizationDate ? dayjs(currentReviewPatent.authorizationDate).format('YYYY-MM-DD') : '-' }}</p>
          <p><strong>专利类型：</strong>{{ currentReviewPatent.category ? currentReviewPatent.category.categoryName : '-' }}</p>
          <p><strong>负责人：</strong>{{ currentReviewPatent.firstResponsible ? currentReviewPatent.firstResponsible.nickname || currentReviewPatent.firstResponsible.username : '-' }}</p>
          <p v-if="currentReviewPatent.conversionDate"><strong>转化时间：</strong>{{ dayjs(currentReviewPatent.conversionDate).format('YYYY-MM-DD') }}</p>
        </div>
        
        <a-divider />
        
        <a-form :model="reviewForm" layout="vertical">
          <a-form-item label="审核意见" name="reviewComment">
            <a-textarea 
              v-model:value="reviewForm.reviewComment" 
              :rows="4" 
              placeholder="请输入审核意见（选填）" 
            />
          </a-form-item>
          
          <a-form-item label="审核结果" name="reviewStatus">
            <a-radio-group v-model:value="reviewForm.reviewStatus">
              <a-radio :value="1">通过</a-radio>
              <a-radio :value="0">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <!-- 显示附件列表 -->
          <div v-if="currentReviewPatent.attachments && currentReviewPatent.attachments.length > 0">
            <h4>附件列表</h4>
            <a-list size="small">
              <a-list-item v-for="(file, index) in currentReviewPatent.attachments" :key="index">
                <template #actions>
                  <a-button type="link" size="small" @click="previewFile(file)">
                    <template #icon><EyeOutlined /></template>
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="downloadFile(file)">
                    <template #icon><DownloadOutlined /></template>
                    下载
                  </a-button>
                </template>
                <a-list-item-meta
                  :title="file.name || file.originalFileName || `附件${index + 1}`"
                  :description="formatFileSize(file.size || (file.data && file.data.size)) || '未知大小'"
                >
                  <template #avatar>
                    <file-outlined />
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </a-list>
          </div>
          <div v-else>
            <a-empty description="暂无附件" />
          </div>
        </a-form>
      </div>
      <a-spin v-else />
    </a-modal>

    <!-- 用户得分详情弹窗 -->
    <a-modal
      v-model:visible="userDetailsVisible"
      :title="`${'教师'}的专利得分详情`"
      width="900px"
      :footer="null"
      :autofocus="false"
      :focusTriggerAfterClose="false"
    >
      <a-table
        :columns="userDetailsColumns"
        :data-source="userProjectDetails"
        :pagination="userDetailsPagination"
        :loading="userDetailsLoading"
        rowKey="id"
        :scroll="{ x: 800 }"
        :bordered="true"
        @change="handleUserDetailsPaginationChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'role'">
            <a-tag :color="record.role === 'leader' ? 'blue' : 'green'">
              {{ record.role === 'leader' ? '负责人' : '成员' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'userAllocationRatio'">
            {{ (record.userAllocationRatio * 100).toFixed(2) }}%
          </template>
          <template v-else-if="column.key === 'userScore'">
            <span style="font-weight: bold; color: #1890ff;">{{ record.userScore ? parseFloat(record.userScore).toFixed(2) : '0.00' }}分</span>
          </template>
          <template v-else-if="column.key === 'totalScore'">
            {{ record.totalScore ? parseFloat(record.totalScore).toFixed(2) : '0.00' }}分
          </template>
          <template v-else-if="column.key === 'categoryName'">
            <span>{{ record.categoryName || '-' }}</span>
          </template>
          <template v-if="column.key === 'reviewStatusText'">
            <a-tag :color="record.ifReviewer === 1 ? 'success' : (record.ifReviewer === 0 ? 'error' : 'warning')">
              {{ record.ifReviewer === 1 ? '已审核' : (record.ifReviewer === 0 ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
        </template>
      </a-table>
      <div style="margin-top: 16px; text-align: right; font-weight: bold;">
        总得分: {{ typeof userDetailsTotalScore === 'number' ? userDetailsTotalScore.toFixed(2) : '0.00' }}分
      </div>
    </a-modal>
    
    <!-- 专利导入预览模态框 -->
    <a-modal
      title="专利导入预览"
      :visible="importPreviewVisible"
      width="90%"
      :maskClosable="false"
      :footer="null"
      @cancel="handleCancelImportPreview"
    >
      <template v-if="importPreviewLoading">
        <div style="text-align: center; padding: 40px;">
          <a-spin size="large" />
          <p style="margin-top: 20px;">正在解析数据，请稍候...</p>
        </div>
      </template>
      <template v-else>
        <div style="margin-bottom: 16px;">
          <a-alert
            :type="userIdCheckResults.notFound > 0 ? 'warning' : 'success'"
            :message="userIdCheckResults.notFound > 0 ? 
              `存在${userIdCheckResults.notFound}个用户ID未找到，这些记录可能导入失败` : 
              '所有用户ID均已找到'"
            show-icon
          />
          <div style="margin-top: 8px;">
            <a-space>
              <span>共找到 <b>{{ importPreviewData.length }}</b> 条记录</span>
              <a-button type="primary" @click="handleStartImport" :loading="importInProgress">
                开始导入
              </a-button>
              <a-button @click="handleCancelImportPreview">
                取消
              </a-button>
              <a-button type="primary" @click="handleDownloadJson">
                <template #icon><DownloadOutlined /></template>
                下载JSON
              </a-button>
            </a-space>
          </div>
        </div>
        
        <a-table
          :columns="previewColumns"
          :dataSource="importPreviewData.map((item, index) => ({ 
            ...item, 
            index: index + 1,
            key: index
          }))"
          :pagination="{ pageSize: 10 }"
          :rowClassName="(record) => record.userIdCheckStatus === 'notFound' ? 'import-row-error' : ''"
          :scroll="{ x: 1200 }"
          size="small"
          bordered
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'userIdCheckStatus'">
              <a-tag v-if="text === 'checking'" color="blue">检查中</a-tag>
              <a-tag v-else-if="text === 'found'" color="green">已找到</a-tag>
              <a-tag v-else color="red">未找到</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'participants'">
              <div>
                <div v-if="record.rawData?.participants && record.rawData.participants.length">
                  <div v-for="(participant, pIndex) in record.rawData.participants" :key="pIndex">
                    {{ participant.name || '未命名' }}
                    <a-tag v-if="participant.found !== undefined" :color="participant.found ? 'green' : 'red'">
                      {{ participant.found ? '已找到' : '未找到' }}
                    </a-tag>
                    <span style="color: #999; margin-left: 5px;">
                      (排名:{{ participant.participantRank || pIndex+1 }}
                      {{ participant.isLeader ? '/负责人' : '' }}
                      {{ participant.allocationRatio ? `/分配比例:${participant.allocationRatio}` : '' }})
                    </span>
                  </div>
                </div>
                <span v-else>{{ text || '-' }}</span>
              </div>
            </template>
          </template>
        </a-table>
      </template>
    </a-modal>

    <!-- 专利导入结果模态框 -->
    <a-modal
      title="专利导入结果"
      :visible="importResultVisible"
      :width="800"
      :footer="null"
      :maskClosable="false"
      :closable="!importInProgress"
    >
      <div style="margin-bottom: 16px;">
        <a-progress 
          :percent="importResults.total > 0 ? Math.floor((importResults.current / importResults.total) * 100) : 0" 
          :status="importInProgress ? 'active' : (importResults.failed > 0 ? 'exception' : 'success')" 
        />
        <div style="margin-top: 16px; display: flex; justify-content: space-between;">
          <span>总记录数: <b>{{ importResults.total }}</b></span>
          <span>已处理: <b>{{ importResults.current }}</b></span>
          <span>成功: <b style="color: #52c41a">{{ importResults.success }}</b></span>
          <span>失败: <b style="color: #ff4d4f">{{ importResults.failed }}</b></span>
        </div>
      </div>
      
      <a-table
        :dataSource="importResults.details"
        :columns="resultColumns"
        rowKey="index"
        :pagination="{ pageSize: 10 }"
        :rowClassName="(record) => record.status === 'error' ? 'import-row-error' : ''"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag v-if="text === 'success'" color="success">成功</a-tag>
            <a-tag v-else color="error">失败</a-tag>
          </template>
        </template>
      </a-table>
      
      <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
        <a-space>
          <a-button 
            @click="exportFailedRecords" 
            :disabled="importInProgress || importResults.failed === 0"
            type="danger"
          >
            <template #icon><DownloadOutlined /></template>
            导出失败记录
          </a-button>
          <a-button 
            type="primary" 
            @click="importResultVisible = false"
            :disabled="importInProgress"
          >
            完成
          </a-button>
        </a-space>
      </div>
    </a-modal>
  </div>
</template> 

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch, nextTick, h } from 'vue'
import { UploadOutlined, DownloadOutlined, PlusOutlined, UserOutlined, SearchOutlined, EyeOutlined, DeleteOutlined, FileOutlined, FileExcelOutlined, ReloadOutlined, EditOutlined, AuditOutlined, DownOutlined } from '@ant-design/icons-vue'
import { message, Modal, Upload } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import * as XLSX from 'xlsx'
import ZyModal from '@/components/common/ZyModal.vue'
import useUserId from '@/composables/useUserId'
import { debounce } from 'lodash'
import { previewFile as utilPreviewFile, downloadFile as utilDownloadFile, uploadFile as utilUploadFile, deleteFile as utilDeleteFile } from '@/utils/others'
import { uploadFiles, deleteFile } from '@/api/modules/api.file'

// 导入API
import {
  getPatentDetail,
  createPatent,
  updatePatent,
  deletePatent,
  importPatents,
  exportPatents,
  getPatentTimeDistribution,
  reviewPatent,
  getPatents,
  getPatentDetailByGet,
  getAllUsersTotalScore,
  getUserPatentDetails,
  reapplyReview,
  getReviewStatusOverview
} from '@/api/modules/api.patents'

import {getPatentCategories, getCategoryDistribution } from '@/api/modules/api.patentCategories'
import { usersSearch } from '@/api/modules/api.users'

import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole, getRoleFromStorage } = useUserRole()
import { hasPerms } from '@/libs/util.common';
import { getScoreTimeRange } from '@/api/modules/api.home';

// 图表引用
const categoryChartRef = ref(null)
const reviewStatusChartRef = ref(null)
const timeChartRef = ref(null)

// 图表实例
let categoryChart = null
let reviewStatusChart = null
let timeChart = null

// 图表数据范围状态
const categoryChartRange = ref('in')
const reviewStatusChartRange = ref('in')
const timeChartRange = ref('in')

// 图表审核状态过滤
const categoryChartReviewStatus = ref('reviewed')
const reviewStatusChartReviewStatus = ref('reviewed')
const timeChartReviewStatus = ref('reviewed')

// 切换图表数据范围
const changeChartRange = async (chartType, range) => {
  try {
    // 根据图表类型修改对应范围
    switch (chartType) {
      case 'category':
        categoryChartRange.value = range;
        await initCategoryChart(range, categoryChartReviewStatus.value);
        break;
      case 'reviewStatus':
        reviewStatusChartRange.value = range;
        await initReviewStatusChart(range, reviewStatusChartReviewStatus.value);
        break;
      case 'time':
        timeChartRange.value = range;
        await initTimeChart(range, timeChartReviewStatus.value);
        break;
      default:
        console.warn('未知的图表类型:', chartType);
    }
  } catch (error) {
    console.error(`更新${chartType}图表出错:`, error);
    message.error('更新图表数据失败');
  }
}

// 处理图表审核状态变更
const handleChartReviewStatusChange = async (chartType, status) => {
  try {
    switch (chartType) {
      case 'category':
        categoryChartReviewStatus.value = status;
        await initCategoryChart(categoryChartRange.value, status);
        break;
      case 'time':
        timeChartReviewStatus.value = status;
        await initTimeChart(timeChartRange.value, status);
        break;
      default:
        console.warn('未知的图表类型:', chartType);
    }
  } catch (error) {
    console.error(`更新${chartType}审核状态出错:`, error);
    message.error('更新图表数据失败');
  }
}

// 表格列定义
const columns = [
  {
    title: '专利名称',
    dataIndex: 'patentName',
    key: 'patentName',
    width: '20%',
    ellipsis: true,
  },
  {
    title: '专利分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '授权日期',
    dataIndex: 'authorizationDate',
    key: 'authorizationDate',
    width: '10%',
    sorter: true,
  },
  {
    title: '转化日期',
    dataIndex: 'conversionDate',
    key: 'conversionDate',
    width: '10%',
  },
  {
    title: '第一负责人',
    dataIndex: 'firstResponsible',
    key: 'firstResponsible',
    width: '10%',
  },
  {
    title: '参与人员及分配比例',
    dataIndex: 'participants',
    key: 'participants',
    width: '15%',
    ellipsis: true,
  },
  {
    title: '得分',
    dataIndex: 'score',
    key: 'score',
    sorter: true,
    width: '7%',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '审核状态',
    dataIndex: 'ifReviewer',
    key: 'ifReviewer',
    width: '10%',
  },
  {
    title: '审核建议',
    dataIndex: 'reviewComment',
    key: 'reviewComment',
    width: '15%',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
    align: 'center'
  },
]

// 数据源
const dataSource = ref([])
const isLoading = ref(false)
const categoryOptions = ref([])

// 搜索表单
const searchForm = reactive({
  patentName: '',
  categoryId: undefined,
  dateRange: [],
  reviewStatus: 'reviewed', // 添加审核状态
  range: 'in' // 添加统计范围
})

// 重置搜索条件
const resetSearch = () => {
  searchForm.patentName = ''
  searchForm.categoryId = undefined
  searchForm.dateRange = []
  searchForm.reviewStatus = 'reviewed' // 重置为默认值
  searchForm.range = 'in' // 重置为默认值
  handleSearch()
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
})

// 总分计算
const totalScore = computed(() => {
  // 直接使用后端返回的分数进行计算
  const sum = dataSource.value.reduce((sum, item) => {
    return sum + (parseFloat(item.score) || 0);
  }, 0);
  
  // 保留两位小数
  return parseFloat(sum.toFixed(2));
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)

// 表单引用
const formRef = ref(null)

// 表单数据
const formState = reactive({
  userId: '',
  patentName: '',
  patentNumber: '',
  patentType: '',
  applicationDate: null,
  authorizationDate: null,
  remark: '',
  fileList: [],
  deletedFiles: [], // 添加删除文件ID数组
  fileIds: [], // 添加文件ID数组
  attachmentUrl: [] // 添加文件路径数组
});

// 添加在其他状态变量附近
const currentRole = ref('');
const currentUserId = ref('');

// 是否显示个人专利
const showPersonalPatents = ref(false)

// 使用useUserId composable
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()

// 文件相关状态
const fileList = ref([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 添加文件列定义
const fileColumns = [
  {
    title: '文件名',
    dataIndex: 'name',
    key: 'fileName',
    ellipsis: true,
    width: '40%'
  },
  {
    title: '大小',
    key: 'fileSize',
    width: '20%'
  },
  {
    title: '状态',
    key: 'status',
    width: '15%'
  },
  {
    title: '操作',
    key: 'action',
    width: '25%'
  }
];

// 文件预览
const previewFile = (file) => {
  if (file.url && /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(file.url)) {
    // For image files, use the built-in preview modal
    previewImage.value = file.url;
    previewVisible.value = true;
    previewTitle.value = file.name || file.originalFileName || '文件预览';
  } else {
    // For other file types, use the utility function
    utilPreviewFile(file, {
      onImagePreview: (imageInfo) => {
        previewImage.value = imageInfo.url;
        previewVisible.value = true;
        previewTitle.value = imageInfo.title || '文件预览';
      }
    });
  }
};

// 文件下载
const downloadFile = (file) => {
  utilDownloadFile(file);
};

// 确认删除文件
const confirmDeleteFile = async (file) => {
  try {
    const result = await utilDeleteFile(file, {
      deleteApi: deleteFile,
      onSuccess: () => {
        // If the file is already uploaded to server and has an ID, record it for deletion
        if (file.id || (file.response && file.response.file && file.response.file.id)) {
          const fileId = file.id || file.response.file.id;
          if (fileId) {
            formState.deletedFiles.push(fileId);
          }
        }
        
        // Remove from file list
        const index = fileList.value.findIndex(item => item.uid === file.uid);
        if (index > -1) {
          fileList.value.splice(index, 1);
        }
        
        // Release object URL if it exists
        if (file.url && file.url.startsWith('blob:')) {
          URL.revokeObjectURL(file.url);
        }
      }
    });
    
    if (result.success) {
      message.success('文件已删除');
    }
  } catch (error) {
    console.error('删除文件失败:', error);
    message.error('删除文件失败: ' + (error.message || '未知错误'));
  }
};

// 文件上传前校验
const beforeUpload = (file) => {
  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
    return false || Upload.LIST_IGNORE;
  }
  
  // 检查总大小
  const currentTotalSize = fileList.value.reduce((sum, f) => {
    return sum + (f.size || (f.originFileObj && f.originFileObj.size) || 0);
  }, 0);
  
  const newTotalSize = currentTotalSize + file.size;
  const isLt50M = newTotalSize / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('文件总大小不能超过50MB!');
    return false || Upload.LIST_IGNORE;
  }
  
  // 检查是否有重名文件
  const isDuplicate = fileList.value.some(f => f.name === file.name);
  if (isDuplicate) {
    message.warning(`已存在同名文件: ${file.name}`);
    return false || Upload.LIST_IGNORE;
  }
  
  return true;
};

// 自定义文件上传
const handleFileUpload = ({ file, onSuccess, onError, onProgress }) => {
  return utilUploadFile({
    file,
    uploadApi: uploadFiles,
    id: currentRecord.value?.id || 'temp_' + Date.now(),
    relatedId: currentRecord.value?.id,
    class: 'patents',
    onProgress,
    onSuccess: (res) => {
      console.log('File upload success:', res);
      // 从不同位置尝试获取文件ID
      const fileId = res?.fileInfo?.id || res?.id || res?.response?.id || res?.response?.fileInfo?.id;
      console.log("fileId===", fileId);
      
      if (fileId) {
        // 确保文件ID被正确设置
        file.id = fileId;
        
        // 确保将文件ID添加到formState.fileIds数组
        if (!formState.fileIds.includes(fileId)) {
          formState.fileIds.push(fileId);
        }
        
        // 设置文件路径
        const filePath = res?.fileInfo?.filePath || file.url;
        if (filePath && !formState.attachmentUrl.includes(filePath)) {
          formState.attachmentUrl.push(filePath);
        }
        
        console.log("更新后的formState.fileIds:", formState.fileIds);
        console.log("更新后的formState.attachmentUrl:", formState.attachmentUrl);
      }
      onSuccess(res);
    },
    onError,
    fileList: fileList.value,
    formState
  });
};

// 自定义导入处理
const handleImport = async ({ file, onSuccess, onError }) => {
  try {
    message.loading('正在导入数据...')
    const res = await importPatents(file)
    
    if (res && res.code === 200) {
      message.success('导入成功')
      onSuccess()
      fetchData() // 重新获取数据
    } else {
      message.error(res?.message || '导入失败')
      onError()
    }
  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败: ' + (error.message || '未知错误'))
    onError()
  }
}

// 处理导出
const handleExport = async () => {
  try {
    const fileName = `专利数据_${dayjs().format('YYYY-MM-DD')}.xlsx`
    await exportPatents({
      fileName: fileName,
      patentName: searchForm.patentName,
      categoryId: searchForm.categoryId,
      authorizationDateStart: searchForm.dateRange?.[0]?.format('YYYY-MM-DD'),
      authorizationDateEnd: searchForm.dateRange?.[1]?.format('YYYY-MM-DD')
    })
    message.success('数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败: ' + (error.message || '未知错误'))
  }
}

// 添加错误状态
const errorMessage = ref('')

// 获取列表数据
const fetchData = async () => {
  isLoading.value = true
  errorMessage.value = ''
  
  try {
    // 处理搜索参数
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      range: searchForm.range,
      reviewStatus: searchForm.reviewStatus
    };

    // 添加专利名称搜索
    if (searchForm.patentName) {
      params.patentName = searchForm.patentName;
    }

    // 添加专利分类搜索
    if (searchForm.categoryId) {
      params.categoryId = searchForm.categoryId;
    }

    // 添加授权日期搜索
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.authorizationDateStart = searchForm.dateRange[0].format('YYYY-MM-DD');
      params.authorizationDateEnd = searchForm.dateRange[1].format('YYYY-MM-DD');
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalPatents.value) {
      try {
        const currentUserId = await getUserId(true)
        
        if (!currentUserId) {
          message.error('未获取到用户信息，请重新登录')
          return
        }
        
        params.userId = currentUserId
      } catch (userError) {
        console.error('获取用户信息过程中发生错误:', userError)
        message.error('获取用户信息失败，请重新登录')
        return
      }
    }

    // 使用统一的API接口获取数据
    const response = await getPatents(params)
    
    if (response && response.code === 200) {
      
      // 处理数据，确保categoryName字段存在
      const processedData = (response.data.list || []).map(item => {
        // 如果item.category对象存在并且有categoryName属性
        if (item.category && item.category.categoryName) {
          return {
            ...item,
            categoryName: item.category.categoryName,
            score: item.category.score
          }
        }
        return item
      })
      
      // 更新数据源不触发额外的监听器操作
      dataSource.value = processedData
      pagination.total = response.data.pagination?.total || 0
      
      // 通过延迟更新图表，防止重复初始化
      nextTick(() => {
        // 每次获取数据后都尝试初始化图表
        updateCharts()
        chartsInitialized.value = true
      })
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = '获取专利列表失败：' + (response?.message || '未知错误')
    }
  } catch (error) {
    console.error('获取专利列表失败:', error)
    message.error('获取专利列表失败: ' + (error.message || '未知错误'))
    errorMessage.value = '获取专利列表失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 获取专利分类
const fetchCategories = async () => {
  try {
    const response = await getPatentCategories()
    
    if (response && response.code === 200) {
      categoryOptions.value = response.data || []
    } else {
      message.error(response?.message || '获取专利分类失败')
    }
  } catch (error) {
    console.error('获取专利分类失败:', error)
    message.error('获取专利分类失败: ' + (error.message || '未知错误'))
  }
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
  // 只更新必要的分页属性，保持其他配置不变
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  // 确保其他配置属性保持不变
  pagination.showSizeChanger = true
  pagination.pageSizeOptions = ['10', '20', '50']
  pagination.showTotal = total => `共 ${total} 条`

  // 处理排序
  if (sorter && sorter.field) {
    // 这里可以添加排序逻辑
  }
  
  fetchData()
}

// 切换个人/全部专利视图
const togglePersonalPatents = () => {
  showPersonalPatents.value = !showPersonalPatents.value
  chartsInitialized.value = false  // 重置图表初始化状态
  fetchData()
}

// 更新所有图表
const updateCharts = async () => {
  try {
    // 释放旧的图表实例
    if (categoryChart) categoryChart.dispose()
    if (reviewStatusChart) reviewStatusChart.dispose()
    if (timeChart) timeChart.dispose()

    // 只要DOM元素已准备好就初始化图表，不再依赖dataSource
    if (categoryChartRef.value) {
      await initCategoryChart(categoryChartRange.value, categoryChartReviewStatus.value)
    } else {
      console.warn('categoryChartRef不存在，无法初始化分类图表')
    }

    if (reviewStatusChartRef.value) {
      await initReviewStatusChart(reviewStatusChartRange.value, reviewStatusChartReviewStatus.value)
    } else {
      console.warn('reviewStatusChartRef不存在，无法初始化审核状态图表')
    }

    if (timeChartRef.value) {
      await initTimeChart(timeChartRange.value, timeChartReviewStatus.value)
    } else {
      console.warn('timeChartRef不存在，无法初始化时间图表')
    }
  } catch (error) {
    console.error('更新图表出错:', error)
    message.error('加载图表数据失败')
  }
}

// 初始化专利分类分布图
const initCategoryChart = async (range, reviewStatus = 'reviewed') => {
  if (!categoryChartRef.value) {
    console.warn('categoryChartRef不存在，无法初始化分类图表')
    return
  }
  
  try {
    console.log('开始初始化专利分类分布图，range:', range, 'reviewStatus:', reviewStatus)
    
    // 构建请求参数
    const params = { 
      range,
      reviewStatus
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalPatents.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    console.log('请求专利分类分布数据，参数:', params)
    
    // 请求分类分布数据
    const response = await getCategoryDistribution(params)
    
    if (response && response.code === 200) {
      const categoryData = response.data
      console.log('获取到专利分类分布数据:', categoryData)
      
      // 确保categoryChartRef.value存在
      if (!categoryChartRef.value) {
        console.warn('categoryChartRef不存在，无法初始化分类图表')
        return
      }
      
      // 释放旧的图表实例
      if (categoryChart) {
        categoryChart.dispose()
      }
      
      // 初始化图表
      categoryChart = echarts.init(categoryChartRef.value)
      console.log('分类图表实例创建成功')
      
      // 设置图表配置
      categoryChart.setOption({
        title: {
          text: '专利分类分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: categoryData.map(item => item.name)
        },
        series: [
          {
            name: '专利分类',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: categoryData
          }
        ]
      })
      console.log('分类图表配置设置成功')
    } else {
      console.error('获取专利分类分布数据失败:', response)
    }
  } catch (error) {
    console.error('专利分类分布图初始化失败:', error)
  }
}

// 初始化专利时间分布图
const initTimeChart = async (range, reviewStatus = 'reviewed') => {
  if (!timeChartRef.value) {
    console.warn('timeChartRef不存在，无法初始化时间分布图表')
    return
  }
  
  try {
    console.log('开始初始化专利时间分布图，range:', range, 'reviewStatus:', reviewStatus)
    
    // 构建请求参数
    const params = { 
      range,
      reviewStatus
    }
    
    // 如果是个人视图，添加userId参数
    if (showPersonalPatents.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    console.log('请求专利时间分布数据，参数:', params)
    
    // 请求时间分布数据
    const response = await getPatentTimeDistribution(params)
    
    if (response && response.code === 200) {
      const timeData = response.data
      console.log('获取到专利时间分布数据:', timeData)
      
      // 确保timeChartRef.value存在
      if (!timeChartRef.value) {
        console.warn('timeChartRef不存在，无法初始化时间分布图表')
        return
      }
      
      // 释放旧的图表实例
      if (timeChart) {
        timeChart.dispose()
      }
      
      // 初始化图表
      timeChart = echarts.init(timeChartRef.value)
      console.log('时间图表实例创建成功')
      
      // 设置图表配置
      timeChart.setOption({
        title: {
          text: '专利时间分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: timeData.months,
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '专利数量',
            type: 'bar',
            data: timeData.data,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2378f7' },
                  { offset: 0.7, color: '#2378f7' },
                  { offset: 1, color: '#83bff6' }
                ])
              }
            }
          }
        ]
      })
      console.log('时间图表配置设置成功')
    } else {
      console.error('获取专利时间分布数据失败:', response)
    }
  } catch (error) {
    console.error('专利时间分布图初始化失败:', error)
  }
}

// 初始化审核状态分布图
const initReviewStatusChart = async (range = 'in', reviewStatus = 'all') => {
  try {
    if (!reviewStatusChartRef.value) {
      console.warn('reviewStatusChartRef不存在，无法初始化审核状态分布图表')
      return
    }

    // 释放旧的图表实例
    if (reviewStatusChart) {
      reviewStatusChart.dispose()
    }

    // 构建请求参数
    const params = { range }

    // 如果是个人视图，添加userId参数
    if (showPersonalPatents.value) {
      try {
        const currentUserId = await getUserId(true)
        if (currentUserId) {
          params.userId = currentUserId
        }
      } catch (error) {
        console.warn('获取用户ID失败，但仍继续初始化图表:', error)
      }
    }

    // 请求审核状态分布数据
    const response = await getReviewStatusOverview(params)

    if (response && response.code === 200) {
      const { reviewed, pending, rejected } = response.data

      // 初始化图表
      reviewStatusChart = echarts.init(reviewStatusChartRef.value)
      console.log('审核状态图表实例创建成功')

      // 设置图表配置
      reviewStatusChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          left: 'left',
          bottom: 0,
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '审核状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            {
              value: reviewed,
              name: '已审核',
              itemStyle: { color: '#52c41a' }
            },
            {
              value: pending,
              name: '待审核',
              itemStyle: { color: '#faad14' }
            },
            {
              value: rejected,
              name: '已拒绝',
              itemStyle: { color: '#ff4d4f' }
            }
          ]
        }]
      })
      console.log('审核状态图表配置设置成功')
    } else {
      console.error('获取审核状态分布数据失败:', response)
    }
  } catch (error) {
    console.error('审核状态分布图初始化失败:', error)
  }
}

// 参与者格式化
const formatParticipantsWithAllocation = (participants, record) => {
  if (!participants || !Array.isArray(participants) || participants.length === 0) {
    return '-'
  }
  
  return participants.map(p => {
    // 正确获取嵌套的participant对象
    const participant = p.participant
    
    // 获取用户名称
    let name = '未知用户'
    if (participant) {
      name = participant.nickname || participant.realName || participant.username || participant.name
    }
    
    // 分配比例显示为百分比，保留整数
    const allocation = p.allocationRatio ? `(占比${(parseFloat(p.allocationRatio) * 100).toFixed(0)}%)` : ''
    
    // 负责人标识，使用中文
    const leader = p.isLeader ? '[负责人]' : ''
    
    return `${name}${leader}${allocation}`
  }).join('、 ')
}

// 校验规则
const rules = {
  patentName: [{ required: true, message: '请输入专利名称', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择专利分类', trigger: 'change' }],
  authorizationDate: [{ required: true, message: '请选择授权日期', trigger: 'change' }],
}

// 分配比例信息
const allocationMsg = ref('')

// 校验分配比例总和是否为1
const validateTotalAllocation = () => {
  if (!formState.participants || formState.participants.length === 0) {
    allocationMsg.value = ''
    return true
  }

  const total = formState.participants.reduce((sum, p) => sum + (p.allocationRatio || 0), 0)
  const isValid = Math.abs(total - 100) < 1 // 允许1%的误差
  
  allocationMsg.value = isValid 
    ? '分配比例总和: 100%' 
    : `分配比例总和: ${(total * 100).toFixed(0)}%，需要调整为100%`
  
  return isValid
}

// 新增成员相关参数
const currentMember = reactive({
  displayName: '',  // 用于显示的名称
  participantId: '',  // 用户ID，用于提交到后端
  id: '',
  nickname: '',
  username: '',
  studentNumber: '',
  allocationRatio: 10, // 默认10%
  isLeader: false
})

// 成员搜索相关
const membersOptions = ref([])
const membersSearchLoading = ref(false)
const membersSearchTimeout = ref(null)

// 参与人员搜索
const handleMembersSearch = (value) => {
  // 清除之前的定时器
  if (membersSearchTimeout.value) {
    clearTimeout(membersSearchTimeout.value)
  }
  
  // 如果输入为空，清空选项
  if (!value || value.trim() === '') {
    membersOptions.value = []
    return
  }
  
  // 设置500ms延迟，避免频繁请求
  membersSearchTimeout.value = setTimeout(async () => {
    membersSearchLoading.value = true
    try {
      // 调用搜索接口
      const response = await usersSearch({ keyword: value })
      
      if (response && response.code === 200) {
        // 检查返回的数据结构
        if (Array.isArray(response.data)) {
          membersOptions.value = response.data
        } else if (response.data && Array.isArray(response.data.list)) {
          // 如果返回的是包含list属性的对象
          membersOptions.value = response.data.list
        } else {
          // 如果数据结构不是预期的格式
          console.error('搜索成员返回的数据结构异常:', response.data)
          
          // 尝试转换数据结构
          const data = response.data
          if (data && typeof data === 'object') {
            try {
              // 尝试将对象转换为数组
              const tempArray = Object.values(data)
              if (tempArray.length > 0 && typeof tempArray[0] === 'object') {
                membersOptions.value = tempArray
              } else {
                membersOptions.value = [data] // 如果只有一个对象，创建包含它的数组
              }
            } catch (e) {
              console.error('转换成员数据结构失败:', e)
              membersOptions.value = []
            }
          } else {
            membersOptions.value = []
          }
        }
        
      } else {
        membersOptions.value = []
        console.error('搜索成员失败:', response?.message || '未知错误')
      }
    } catch (error) {
      console.error('搜索成员出错:', error)
      membersOptions.value = []
    } finally {
      membersSearchLoading.value = false
    }
  }, 500)
}

// 当选择成员时更新当前成员信息
const handleCurrentMemberChange = (value, option) => {
  // 在vue3的a-select中，option参数是一个包含data属性的对象
  const selectedOption = option ? 
    (option.data ? option.data : 
      (option.option ? option.option.data : null)) : null
  
  if (selectedOption) {
    // 直接使用选项数据
    currentMember.id = selectedOption.id || ''           // 用户ID
    currentMember.participantId = selectedOption.id      // 设置participantId为用户ID
    currentMember.displayName = selectedOption.nickname || selectedOption.username || value
    currentMember.nickname = selectedOption.nickname || selectedOption.username || value
    currentMember.username = selectedOption.username || ''
    currentMember.studentNumber = selectedOption.studentNumber || ''
    
    // 默认分配比例设为10%
    if (!currentMember.allocationRatio) {
      currentMember.allocationRatio = 10
    }
  } else if (value) {
    // 尝试从选项列表中找到匹配的选项
    const found = membersOptions.value.find(opt => 
      (opt.nickname || opt.username) === value
    )
    
    if (found) {
      currentMember.id = found.id || ''                // 用户ID
      currentMember.participantId = found.id           // 设置participantId为用户ID
      currentMember.displayName = found.nickname || found.username || value
      currentMember.nickname = found.nickname || found.username || value
      currentMember.username = found.username || ''
      currentMember.studentNumber = found.studentNumber || ''
      
      // 默认分配比例设为10%
      if (!currentMember.allocationRatio) {
        currentMember.allocationRatio = 10
      }
    } else {
      // 如果没有找到，直接使用输入的值
      currentMember.id = ''
      currentMember.participantId = ''  // 没有用户ID，设为空
      currentMember.displayName = value
      currentMember.nickname = value
      currentMember.username = ''
      currentMember.studentNumber = ''
      
      // 默认分配比例设为10%
      if (!currentMember.allocationRatio) {
        currentMember.allocationRatio = 10
      }
    }
  }
}

// 添加参与人
const addParticipant = () => {
  // 验证成员数据
  if (!currentMember.participantId) {
    message.error('请选择参与人员')
    return
  }
  
  // 检查分配比例
  if (currentMember.allocationRatio === undefined || 
      currentMember.allocationRatio === null || 
      isNaN(parseFloat(currentMember.allocationRatio))) {
    message.error('请输入有效的成员分配比例')
    return
  }
  
  // 检查是否已存在该成员
  const exists = formState.participants.some(
    p => p.id === currentMember.id || p.participantId === currentMember.participantId
  )
  
  if (exists) {
    message.warning('该参与人员已添加')
    return
  }
  
  // 创建新成员对象
  const newMember = {
    id: currentMember.id, // 用户ID
    participantId: currentMember.participantId, // 用户ID，用于提交到后端
    displayName: currentMember.displayName, // 显示名称
    nickname: currentMember.nickname,
    username: currentMember.username || '',
    studentNumber: currentMember.studentNumber || '',
    allocationRatio: parseFloat(currentMember.allocationRatio) || 0,
    isLeader: formState.participants.length === 0 // 第一个添加的成员默认为负责人
  }
  
  try {
    // 添加到参与人员列表
    formState.participants.push(newMember)
    
    // 重置当前成员数据
    currentMember.id = ''
    currentMember.participantId = ''
    currentMember.displayName = ''
    currentMember.nickname = ''
    currentMember.username = ''
    currentMember.studentNumber = ''
    currentMember.allocationRatio = 10
    
    // 检查总比例，不自动调整
    validateTotalAllocation()
    
    message.success('参与人员添加成功')
  } catch (err) {
    console.error('添加成员时发生错误:', err)
    message.error('添加成员失败，请查看控制台错误信息')
  }
}

// 处理表单提交
const handleModalOk = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    // 校验分配比例
    if (!validateTotalAllocation()) {
      message.error('参与人员分配比例总和必须为100%')
      return
    }
    console.log("formState",formState);
    
    // 校验是否有负责人
    if (formState.participants.length > 0 && !formState.participants.some(p => p.isLeader)) {
      message.error('必须指定至少一名负责人')
      return
    }
    
    // 从参与人员中找到负责人，设置为第一负责人ID
    const leader = formState.participants.find(p => p.isLeader)
    if (leader) {
      formState.firstResponsibleID = leader.participantId
    }
    
    confirmLoading.value = true
    
    // 构建提交数据
    const formData = new FormData()
    
    // 添加基本信息
    formData.append('patentName', formState.patentName)
    formData.append('categoryId', formState.categoryId)
    if (formState.authorizationDate) {
      formData.append('authorizationDate', formState.authorizationDate.format('YYYY-MM-DD'))
    }
    if (formState.conversionDate) {
      formData.append('conversionDate', formState.conversionDate.format('YYYY-MM-DD'))
    }
    if (formState.remark) {
      formData.append('remark', formState.remark)
    }
    if (formState.firstResponsibleID) {
      formData.append('firstResponsibleID', formState.firstResponsibleID)
    }
    
    // 修改参与人员信息传递方式 - 直接添加参与人员数组
    // 根据后端代码，直接添加每个参与者的信息
    formState.participants.forEach((participant, index) => {
      formData.append(`participants[${index}][participantId]`, participant.participantId)
      formData.append(`participants[${index}][isLeader]`, participant.isLeader ? 1 : 0)
      formData.append(`participants[${index}][allocationRatio]`, participant.allocationRatio / 100) // 将百分比转换为小数
    })
    
    // 添加文件
    if (fileList.value && fileList.value.length > 0) {
      // 收集所有已上传成功的文件ID
      console.log('处理文件列表:', fileList.value);
      const fileIds = fileList.value
        .filter(file => file.status === 'done')
        .map(file => {
          // 尝试从不同位置获取文件ID
          const fileId = file.id || 
                      (file.response && file.response.id) || 
                      (file.response && file.response.file && file.response.file.id) ||
                      (file.response && file.response.fileInfo && file.response.fileInfo.id) || 
                      (file.uid && file.uid.toString().length > 10 ? file.uid : null);
          console.log(`文件 ${file.name} ID: ${fileId}`);
          return fileId;
        })
        .filter(id => id); // 过滤掉undefined或null
      
      console.log('最终收集到的有效文件ID:', fileIds);
      
      // 如果有文件ID，添加到formData
      if (fileIds.length > 0) {
        formData.append('fileIds', JSON.stringify(fileIds));
      }

      // 同时处理文件路径
      const filePaths = fileList.value
        .filter(file => file.status === 'done')
        .map(file => {
          const filePath = file.filePath || 
                         (file.response && file.response.fileInfo && file.response.fileInfo.filePath) ||
                         file.url;
          return filePath;
        })
        .filter(path => path);
      
      if (filePaths.length > 0) {
        formData.append('attachmentUrl', JSON.stringify(filePaths));
      }
    } else if (isEdit.value) {
      // 如果是编辑模式且没有文件，发送空数组表示删除所有文件
      formData.append('fileIds', JSON.stringify([]));
      formData.append('attachmentUrl', JSON.stringify([]));
    }
    
    // 另外检查formState.fileIds是否有值，确保兼容性
    if (formState.fileIds && formState.fileIds.length > 0 && !formData.has('fileIds')) {
      console.log('使用formState中的文件IDs:', formState.fileIds);
      formData.append('fileIds', JSON.stringify(formState.fileIds));
      
      // 确保attachmentUrl也存在
      if (formState.attachmentUrl && formState.attachmentUrl.length > 0 && !formData.has('attachmentUrl')) {
        formData.append('attachmentUrl', JSON.stringify(formState.attachmentUrl));
      }
    }
    
    // 打印formData中的所有键值对，帮助调试
    for (const [key, value] of formData.entries()) {
      console.log(`${key}: ${value}`);
    }
    
    // 添加要删除的文件ID（编辑模式下）
    if (isEdit.value && formState.deletedFiles && formState.deletedFiles.length > 0) {
      formData.append('deletedFileIds', JSON.stringify(formState.deletedFiles))
    }
    
    let response
    
    if (isEdit.value && currentRecord.value) {
      // 更新专利
      console.log("formData",formData);
      response = await updatePatent(currentRecord.value.id, formData)
    } else {
      // 添加专利
      response = await createPatent(formData)
    }
    
    if (response && response.code === 200) {
      message.success(isEdit.value ? '更新成功' : '添加成功')
      modalVisible.value = false
      fetchData() // 刷新数据
    } else {
      message.error(response?.message || (isEdit.value ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    console.error(isEdit.value ? '更新专利失败:' : '添加专利失败:', error)
    message.error((isEdit.value ? '更新专利失败: ' : '添加专利失败: ') + (error.message || '未知错误'))
  } finally {
    confirmLoading.value = false
  }
}

// 取消弹窗
const handleModalCancel = () => {
  modalVisible.value = false
}

// 确认删除
const confirmDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDelete(record)
  })
}

// 删除专利
const handleDelete = async (record) => {
  try {
    // 发送删除请求
    const response = await deletePatent(record.id)

    if (response && response.code === 200) {
      message.success('删除成功')
      fetchData()
    } else {
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('删除专利失败:', error)
    message.error('删除专利失败: ' + (error.message || '未知错误'))
  }
}

// 显示添加专利弹窗
const showAddModal = () => {
  isEdit.value = false
  currentRecord.value = null
  
  // 重置表单
  formState.patentName = ''
  formState.categoryId = ''
  formState.authorizationDate = null
  formState.conversionDate = null
  formState.remark = ''
  formState.firstResponsibleID = ''  // 重置第一负责人ID
  formState.participants = []
  formState.deletedFiles = [] // 重置删除文件ID数组
  formState.fileIds = [] // 重置文件ID数组
  formState.attachmentUrl = [] // 重置文件路径数组
  
  // 重置文件列表
  fileList.value = []
  
  // 重置当前成员
  currentMember.id = ''
  currentMember.participantId = ''
  currentMember.displayName = ''
  currentMember.nickname = ''
  currentMember.username = ''
  currentMember.studentNumber = ''
  currentMember.allocationRatio = 10
  
  // 显示弹窗
  modalVisible.value = true
  
  // 如果表单ref存在，重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 编辑专利
const handleEdit = async (record) => {
  isEdit.value = true
  currentRecord.value = { ...record }
  
  try {
    // 重置文件列表和删除文件ID数组
    fileList.value = []
    formState.deletedFiles = []
    formState.fileIds = [] // 重置文件ID数组
    formState.attachmentUrl = [] // 重置文件路径数组
    
    // 获取专利详情并填充表单
    const response = await getPatentDetail(record.id)
    
    if (response && response.code === 200) {
      const detail = response.data
      
      // 填充表单数据
      formState.patentName = detail.patentName || record.patentName
      formState.categoryId = detail.categoryId || record.categoryId
      formState.authorizationDate = detail.authorizationDate ? dayjs(detail.authorizationDate) : null
      formState.conversionDate = detail.conversionDate ? dayjs(detail.conversionDate) : null
      formState.remark = detail.remark || record.remark
      
      // 填充第一负责人信息
      formState.firstResponsibleID = detail.firstResponsibleID || ''
      
      // 填充参与者数据
      const participants = detail.participants || record.participants || []
      
      formState.participants = Array.isArray(participants) 
        ? participants.map(p => {
            // 提取用户ID (participantId)
            const userId = p.participantId || (p.participant ? p.participant.id : '')

            return {
              id: userId,                 // 保留用户ID
              participantId: userId,      // 正确设置：participantId应该是用户ID
              displayName: p.participant ? 
                (p.participant.nickname || p.participant.username || '未知用户') : 
                (userId ? '未知用户' : ''),
              nickname: p.participant ? 
                (p.participant.nickname || p.participant.username || '未知用户') : 
                (userId ? '未知用户' : ''),
              username: p.participant ? p.participant.username : '',
              studentNumber: p.participant ? p.participant.studentNumber : '',
              isLeader: p.isLeader === 1 || p.isLeader === true,
              allocationRatio: (parseFloat(p.allocationRatio) || 0) * 100 // 将小数转换为百分比显示
            }
          })
        : []
      
      // 填充附件数据
      if (detail.attachments && Array.isArray(detail.attachments)) {
        fileList.value = detail.attachments.map((file, index) => {
          const fileObj = {
            uid: file.id || `-${index}`,
            id: file.id,
            name: file.name || file.originalName || `附件${index + 1}`,
            status: 'done',
            url: file.url,
            response: { file: { id: file.id } }
          };
          
          // 同时添加到fileIds和attachmentUrl
          if (file.id && !formState.fileIds.includes(file.id)) {
            formState.fileIds.push(file.id);
          }
          if (file.url && !formState.attachmentUrl.includes(file.url)) {
            formState.attachmentUrl.push(file.url);
          }
          
          return fileObj;
        })
      }
      
      // 显示弹窗
      modalVisible.value = true
      
      // 校验分配比例
      nextTick(() => {
        validateTotalAllocation()
      })
    } else {
      message.error(response?.message || '获取专利详情失败')
      // 使用表格中的数据作为回退
      fillFormFromRecord(record)
    }
  } catch (error) {
    console.error('获取专利详情失败:', error)
    message.error('获取专利详情失败: ' + (error.message || '未知错误'))
    // 使用表格中的数据作为回退
    fillFormFromRecord(record)
  }
}

// 从表格记录填充表单
const fillFormFromRecord = (record) => {
  formState.patentName = record.patentName
  formState.categoryId = record.categoryId
  formState.authorizationDate = record.authorizationDate ? dayjs(record.authorizationDate) : null
  formState.conversionDate = record.conversionDate ? dayjs(record.conversionDate) : null
  formState.remark = record.remark
  
  // 填充第一负责人信息
  formState.firstResponsibleID = record.firstResponsibleID || ''
  
  // 填充参与者数据
  formState.participants = record.participants 
    ? [...record.participants].map(p => {
        // 提取用户ID (participantId)
        const userId = p.participantId || (p.participant ? p.participant.id : '')
        
        return {
          id: userId,
          participantId: userId,
          displayName: p.participant ? 
            (p.participant.nickname || p.participant.username || '未知用户') : 
            (userId ? '未知用户' : ''),
          nickname: p.participant ? 
            (p.participant.nickname || p.participant.username || '未知用户') : 
            (userId ? '未知用户' : ''),
          username: p.participant ? p.participant.username : '',
          studentNumber: p.participant ? p.participant.studentNumber : '',
          isLeader: p.isLeader === 1 || p.isLeader === true,
          allocationRatio: (parseFloat(p.allocationRatio) || 0) * 100 // 将小数转换为百分比显示
        }
      })
    : []
  
  // 尝试填充附件数据
  if (record.attachments && Array.isArray(record.attachments)) {
    fileList.value = record.attachments.map((file, index) => ({
      uid: file.id || `-${index}`,
      id: file.id,
      name: file.name || file.originalName || `附件${index + 1}`,
      status: 'done',
      url: file.url,
      response: { file: { id: file.id } }
    }))
  } else {
    // 如果没有附件数据，清空文件列表
    fileList.value = []
  }
  
  // 显示弹窗
  modalVisible.value = true
  
  // 校验分配比例
  nextTick(() => {
    validateTotalAllocation()
  })
}

// 修改toggleLeader函数，确保只有一个负责人
const toggleLeader = (index, isLeader) => {
  // 如果设置为负责人，先将其他所有人设为非负责人
  if (isLeader) {
    formState.participants.forEach((p, i) => {
      if (i !== index) {
        p.isLeader = false
      }
    })
  }
  
  // 修改指定参与人的负责人状态
  formState.participants[index].isLeader = isLeader
}

// 修改移除参与人函数
const removeParticipant = (index) => {
  formState.participants.splice(index, 1)
  
  // 如果只剩下一个参与人员，自动设置为100%
  if (formState.participants.length === 1) {
    formState.participants[0].allocationRatio = 100
  }
  
  // 验证总比例，但不自动调整
  validateTotalAllocation()
}

// 记录图表是否已初始化的状态
const chartsInitialized = ref(false)

// 使用防抖函数包装updateCharts
const debouncedUpdateCharts = debounce(() => {
  updateCharts()
}, 300)

// 监听数据变化
watch(
  dataSource, 
  () => {
    // 只在图表已初始化的情况下更新
    if (chartsInitialized.value) {
      debouncedUpdateCharts()
    }
  }, 
  { deep: true }
)

// 图表范围变化时清除已初始化标志，以便重新初始化图表
watch([categoryChartRange, reviewStatusChartRange, timeChartRange], () => {
  chartsInitialized.value = false
})

// 加载数据
onMounted(async () => {
  try {
    // 获取当前用户角色
    currentRole.value = await getUserRole();
    console.log("当前用户角色:", currentRole.value);
    
    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true);
      console.log("当前用户ID:", currentUserId.value);
    } catch (error) {
      console.error("获取用户ID失败:", error);
    }
    
    // 如果是教师角色，默认显示个人专利
    if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
      showPersonalPatents.value = true;
      console.log('用户为教师角色，默认显示个人专利');
    }
    
    // 获取专利数据
    try {
      await fetchData();
    } catch (error) {
      console.error('获取专利数据失败:', error);
      message.error('获取专利数据失败');
    }
    
    // 获取专利分类
    try {
      await fetchCategories();
    } catch (error) {
      console.error('获取专利分类失败:', error);
      message.error('获取专利分类失败');
    }
    
    // 初始化用户得分统计
    try {
      await fetchAllUsersTotalScore();
    } catch (error) {
      console.error('获取用户得分统计数据失败:', error);
      message.error('获取用户得分统计数据失败');
    }
    
    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', debounce(() => {
      try {
        if (categoryChart) categoryChart.resize()
        if (reviewStatusChart) reviewStatusChart.resize()
        if (timeChart) timeChart.resize()
      } catch (error) {
        console.error('调整图表大小出错:', error)
      }
    }, 200))
    
    // 主动初始化图表，解决图表不会自动渲染的问题
    nextTick(() => {
      try {
        initCategoryChart(categoryChartRange.value, categoryChartReviewStatus.value)
        initReviewStatusChart(reviewStatusChartRange.value)
        initTimeChart(timeChartRange.value, timeChartReviewStatus.value)
      } catch (error) {
        console.error('初始化图表失败:', error);
        message.error('图表初始化失败');
      }
    })

    // 获取时间范围
    const getTimeRange = () => {
      // 调用API获取时间范围
      getScoreTimeRange('patents').then(res => {
        if (res.code === 200 && res.data) {
          timeRangeText.value = res.data.timeRange || '';
        } else {
          timeRangeText.value = '暂无时间范围数据';
        }
      }).catch(error => {
        console.error('获取时间范围失败:', error);
        timeRangeText.value = '获取时间范围失败';
      });
    };

    // 在onMounted中调用
    getTimeRange();
  } catch (error) {
    console.error('组件挂载时出错:', error);
    message.error('初始化界面失败');
  }
})

// 处理专利审核
const handleReview = async (record) => {
  try {
    // 清空之前的表单数据
    reviewForm.reviewComment = '';
    reviewForm.reviewStatus = null;
    
    // 获取专利详情
    const response = await getPatentDetailByGet(record.id);
    
    if (response && response.code === 200) {
      currentReviewPatent.value = response.data;
      
      // 处理附件列表
      if (response.data.attachments && Array.isArray(response.data.attachments)) {
        // 确保附件对象格式正确
        currentReviewPatent.value.attachments = response.data.attachments.map((attachment, index) => ({
          uid: attachment.id || `-${index}`,
          id: attachment.id,
          name: attachment.name || attachment.fileName || `附件${index + 1}`,
          originalFileName: attachment.originalName || attachment.name || attachment.fileName,
          status: 'done',
          url: attachment.url || attachment.fileUrl || attachment.filePath,
          filePath: attachment.filePath || attachment.url || attachment.fileUrl,
          size: attachment.fileSize || attachment.size,
          type: attachment.fileType || attachment.mimeType,
          response: { 
            id: attachment.id, 
            fileInfo: attachment 
          }
        }));
      } else {
        currentReviewPatent.value.attachments = [];
      }
      
      // 打开模态框
      reviewModalVisible.value = true;
    } else {
      message.error(response?.message || '获取专利详情失败');
    }
  } catch (error) {
    console.error('获取专利详情失败:', error);
    message.error('获取专利详情失败: ' + (error.message || '未知错误'));
  }
}

// 使用hasPermissionToReview函数替代isAdmin/isCurrentUserFirstResponsible函数
const hasPermissionToReview = (record) => {
  // 未审核的专利才需要显示审核按钮（ifReviewer为null时显示）
  return true;
}

// 增加文件大小格式化函数
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 审核模态框相关
const reviewModalVisible = ref(false)
const reviewConfirmLoading = ref(false)
const currentReviewPatent = ref(null)
const reviewForm = reactive({
  reviewStatus: null,
  reviewComment: '',
})

// 处理审核提交
const handleReviewSubmit = async () => {
  try {
    reviewConfirmLoading.value = true
    // 构建提交数据
    const submitData = {
      id: currentReviewPatent.value.id,
      reviewer: userId.value,
      reviewStatus: reviewForm.reviewStatus,
      reviewComment: reviewForm.reviewComment
    }
    // 调用审核API
    const response = await reviewPatent(currentReviewPatent.value.id, submitData)
    if (response && response.code === 200) {
      message.success('审核提交成功')
      reviewModalVisible.value = false
      fetchData() // 刷新数据
    } else {
      message.error('审核提交失败')
    }
  } catch (error) {
    console.error('审核提交失败:', error)
    message.error('审核提交失败: ' + (error.message || '未知错误'))
  } finally {
    reviewConfirmLoading.value = false
  }
}

// 处理审核取消
const handleReviewCancel = () => {
  reviewModalVisible.value = false
}

// 用户得分统计数据
const userScoreData = ref([])
const userScoreLoading = ref(false)
const exporting = ref(false)
const userScoreChartRange = ref('in')
const userScoreChartReviewStatus = ref('reviewed');
const userScorePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
})
// 添加时间范围文本
const timeRangeText = ref('');
const userScoreSearchParams = ref({
  patentName: '',
  nickname: '',
  reviewStatus: 'reviewed',
  range: 'in',
  page: 1,
  pageSize: 10,
  sortField: 'totalScore',
  sortOrder: 'desc'
})

// 用户得分统计表格列定义
const userScoreColumns = [
  {
    title: '排名',
    dataIndex: 'rank',
    key: 'rank',
    width: 80,
    align: 'center',
    sorter: true
  },
  {
    title: '用户昵称',
    dataIndex: 'nickname',
    key: 'nickname',
    width: 120
  },
  {
    title: '学号',
    dataIndex: 'studentNumber',
    key: 'studentNumber',
    width: 120
  },
  {
    title: '专利数量',
    dataIndex: 'patentCount',
    key: 'patentCount',
    width: 100,
    sorter: true
  },
  {
    title: '负责专利数',
    dataIndex: 'leaderPatentCount',
    key: 'leaderPatentCount',
    width: 120,
    sorter: true
  },
  {
    title: '总分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: 100,
    sorter: true
  },
  {
    title: '详情',
    key: 'details',
    width: 100,
    fixed: 'right'
  }
]

// 获取排名颜色
const getRankColor = (rank) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return '#cd7f32' // 铜色
  return 'blue'
}

// 获取所有用户专利总得分统计
const fetchAllUsersTotalScore = async (params = {}) => {
  try {
    userScoreLoading.value = true
    
    // 合并查询参数
    const queryParams = {
      ...userScoreSearchParams.value,
      ...params
    }
    
    console.log('请求参数:', queryParams);
    const response = await getAllUsersTotalScore(queryParams)
    console.log('API返回数据:', response);
    
    if (response && response.code === 200) {
      // 确保数据格式正确
      const processedData = (response.data.list || []).map(item => ({
        ...item,
        // 确保totalScore是数字
        totalScore: typeof item.totalScore === 'number' ? item.totalScore : parseFloat(item.totalScore || 0),
        // 确保其他字段存在
        patentCount: parseInt(item.patentCount || 0),
        leaderPatentCount: parseInt(item.leaderPatentCount || 0),
        rank: item.rank || 0,
        nickname: item.nickname || item.userName || '未知用户',
      }));
      
      console.log('处理后的数据:', processedData);
      userScoreData.value = processedData;
      
      userScorePagination.current = response.data.pagination?.page || 1
      userScorePagination.pageSize = response.data.pagination?.pageSize || 10
      userScorePagination.total = response.data.pagination?.total || 0
    } else {
      console.error('API返回错误:', response);
      message.error(response?.message || '获取用户专利得分统计失败')
    }
  } catch (error) {
    console.error('获取用户专利得分统计失败', error)
    message.error('获取用户专利得分统计失败: ' + (error.message || '未知错误'))
  } finally {
    userScoreLoading.value = false
  }
}

// 处理用户得分统计表格变化
const handleUserScoreTableChange = (pagination, filters, sorter) => {
  // 更新搜索参数
  userScoreSearchParams.value.page = pagination.current
  userScoreSearchParams.value.pageSize = pagination.pageSize
  // 更新分页配置，保持其他配置不变
  userScorePagination.current = pagination.current
  userScorePagination.pageSize = pagination.pageSize
  userScorePagination.showSizeChanger = true
  userScorePagination.pageSizeOptions = ['10', '20', '50']
  userScorePagination.showTotal = total => `共 ${total} 条`

  // 处理排序
  if (sorter && sorter.field) {
    userScoreSearchParams.value.sortField = sorter.field
    userScoreSearchParams.value.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc'
  }
  
  fetchAllUsersTotalScore()
}

// 处理用户得分统计范围变化
const handleUserScoreRangeChange = (value) => {
  userScoreChartRange.value = value
  userScoreSearchParams.value.range = value
  fetchAllUsersTotalScore()
}

// 处理用户得分统计范围变化
const handleUserScoreReviewStatusChange = (value) => {
  userScoreChartReviewStatus.value = value
  userScoreSearchParams.value.reviewStatus = value
  fetchAllUsersTotalScore()
}

// 重置用户得分统计搜索
const resetUserScoreSearch = () => {
  userScoreSearchParams.value = {
    patentName: '',
    nickname: '',
    reviewStatus: 'all',
    range: userScoreChartRange.value,
    page: 1,
    pageSize: 10,
    sortField: 'totalScore',
    sortOrder: 'desc'
  }
  fetchAllUsersTotalScore()
}

// 导出用户得分统计数据
const exportUserScoreData = async () => {
  try {
    exporting.value = true
    
    // 构造导出参数
    const exportParams = {
      ...userScoreSearchParams.value,
      isExport: true
    }
    
    const response = await getAllUsersTotalScore(exportParams)
    
    if (response && response.code === 200) {
      // 转换数据为CSV格式
      const data = response.data.list || []
      const csvData = [
        ['排名', '用户ID', '用户昵称', '学号', '专利数量', '负责专利数', '总分'],
        ...data.map(item => [
          item.rank,
          item.userId,
          item.nickname,
          item.studentNumber || '',
          item.patentCount,
          item.leaderPatentCount,
          item.totalScore
        ])
      ]
      
      // 转换为CSV字符串
      const csvContent = csvData.map(row => row.join(',')).join('\n')
      
      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.setAttribute('href', url)
      link.setAttribute('download', `专利得分统计_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      message.success('导出成功')
    } else {
      message.error('导出失败')
    }
  } catch (error) {
    console.error('导出用户专利得分统计失败', error)
    message.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 获取用户专利详情数据
const fetchUserPatentDetails = async (page = 1, pageSize = 10) => {
  userDetailsLoading.value = true;
  
  try {
    console.log('userScoreChartReviewStatus=',userScoreChartReviewStatus.value);
    // 调用API获取用户专利详情
    const response = await getUserPatentDetails({
      userId: currentUserDetail.value?.userId,
      range: userScoreChartRange.value,
      reviewStatus: userScoreChartReviewStatus.value,
      page: page,
      pageSize: pageSize
    });
    
    if (response && response.code === 200) {
      console.log('用户专利详情数据:', response.data);
      // 映射数据字段，确保与表格期望的格式一致
      userProjectDetails.value = response.data.list.map(item => {
        // 解析userScore和totalScore为数字
        const userScoreNum = parseFloat(item.userScore || 0);
        const totalScoreNum = parseFloat(item.totalScore || 0);
        
        return {
          ...item,
          // 添加或转换字段以匹配表格期望的格式
          categoryName: item.category ? item.category.categoryName : '',
          userScore: userScoreNum, // 确保是数字类型
          totalScore: totalScoreNum, // 确保是数字类型
          reviewStatusText: getReviewStatusDesc(item.ifReviewer)
        };
      });
      userDetailsTotalScore.value = parseFloat(response.data.totalScore || 0);
      userDetailsPagination.total = response.data.total || 0;
    } else {
      message.error(response?.message || '获取用户专利详情失败');
    }
  } catch (error) {
    console.error('获取用户专利详情失败:', error);
    message.error('获取用户专利详情失败');
  } finally {
    userDetailsLoading.value = false;
  }
};

// 显示用户得分详情
const showUserScoreDetails = async (record) => {
  userDetailsVisible.value = true;
  selectedUserDetailName.value = record.nickname || '用户';
  currentUserDetail.value = record;
  
  // 重置分页到第一页
  userDetailsPagination.current = 1;
  userDetailsPagination.pageSize = 10;
  
  // 加载第一页数据
  await fetchUserPatentDetails(1, 10);
};

// 处理导出当前筛选
const exportingCurrent = ref(false);

const exportCurrentFiltered = async () => {
  try {
    exportingCurrent.value = true;
    
    // 构造导出参数，与当前搜索条件一致
    const params = {
      patentName: searchForm.patentName,
      categoryId: searchForm.categoryId,
      authorizationDateStart: searchForm.dateRange?.[0]?.format('YYYY-MM-DD'),
      authorizationDateEnd: searchForm.dateRange?.[1]?.format('YYYY-MM-DD'),
      reviewStatus: searchForm.reviewStatus,
      page: 1,
      pageSize: 1000, // 获取较大数量的记录
      range: searchForm.range
    };
    
    // 如果是个人视图，添加userId参数
    if (showPersonalPatents.value) {
      try {
        const currentUserId = await getUserId(true);
        if (currentUserId) {
          params.userId = currentUserId;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        message.error('获取用户信息失败，无法导出个人数据');
        exportingCurrent.value = false;
        return;
      }
    }
    
    // 调用getPatents接口获取数据
    const response = await getPatents(params);
    
    if (response && response.code === 200 && response.data.list) {
      // 处理导出的数据
      const exportData = response.data.list.map(patent => {
        // 处理参与者信息
        const participantsText = formatParticipantsWithAllocation(patent.participants, patent);
        
        return {
          '专利名称': patent.patentName || '',
          '专利分类': patent.categoryName || '',
          '授权日期': patent.authorizationDate ? dayjs(patent.authorizationDate).format('YYYY-MM-DD') : '',
          '转化日期': patent.conversionDate ? dayjs(patent.conversionDate).format('YYYY-MM-DD') : '',
          '第一负责人': patent.firstResponsible ? (patent.firstResponsible.nickname || patent.firstResponsible.username) : '-',
          '参与人员及分配比例': participantsText || '-',
          '得分': patent.score > 0 ? `${patent.score}分` : '不计分',
          '备注': patent.remark || '',
          '审核状态': patent.ifReviewer === 1 ? '已审核' : (patent.ifReviewer === 0 ? '已拒绝' : '待审核')
        };
      });
      
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      
      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '专利列表');
      
      // 生成Excel文件名称
      const fileName = `专利数据_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      
      // 下载文件
      XLSX.writeFile(workbook, fileName);
      
      message.success('导出成功');
    } else {
      message.error(response?.message || '导出失败');
    }
  } catch (error) {
    console.error('导出筛选数据失败:', error);
    message.error('导出失败: ' + (error.message || '未知错误'));
  } finally {
    exportingCurrent.value = false;
  }
};

// 显示用户得分详情
const userDetailsVisible = ref(false);
const userDetailsLoading = ref(false);
const userProjectDetails = ref([]);
const userDetailsTotalScore = ref(0);
const selectedUserDetailName = ref('');
const currentUserDetail = ref(null);

// 用户详情分页配置
const userDetailsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
});

// 处理用户详情分页变化
const handleUserDetailsPaginationChange = (pagination) => {
  // 只更新必要的分页属性，保持其他配置不变
  userDetailsPagination.current = pagination.current;
  userDetailsPagination.pageSize = pagination.pageSize;
  // 确保其他配置属性保持不变
  userDetailsPagination.showSizeChanger = true
  userDetailsPagination.pageSizeOptions = ['10', '20', '50']
  userDetailsPagination.showTotal = total => `共 ${total} 条`
  fetchUserPatentDetails(pagination.current, pagination.pageSize);
};

const userDetailsColumns = [
  {
    title: '专利名称',
    dataIndex: 'patentName',
    key: 'patentName',
    width: '25%',
    ellipsis: true
  },
  {
    title: '专利类型',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: '13%',
    ellipsis: true
  },
  {
    title: '授权日期',
    dataIndex: 'authorizationDate',
    key: 'authorizationDate',
    width: '12%',
    customRender: ({ text }) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
  },
  {
    title: '转化日期',
    dataIndex: 'conversionDate',
    key: 'conversionDate',
    width: '12%',
    customRender: ({ text }) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    width: '8%',
    align: 'center'
  },
  {
    title: '分配比例',
    dataIndex: 'userAllocationRatio',
    key: 'userAllocationRatio',
    width: '10%',
    align: 'center'
  },
  {
    title: '得分',
    dataIndex: 'userScore',
    key: 'userScore',
    width: '10%',
    align: 'right'
  }
];

// 获取审核状态描述
const getReviewStatusDesc = (status) => {
  if (status === 1) return '已审核';
  if (status === 0) return '已拒绝';
  return '待审核';
};

const userIdCheckResults = ref({
  total: 0,
  found: 0,
  notFound: 0
});

// 验证Excel文件上传
const beforeExcelUpload = (file) => {
  // 检查文件类型
  const isExcel = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
    file.type === 'application/vnd.ms-excel' || 
    file.name.endsWith('.csv');
  
  if (!isExcel) {
    message.error('只能上传Excel文件 (.xlsx, .xls, .csv)!');
    return false;
  }
  
  // 文件大小限制：20MB
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    message.error('文件大小不能超过20MB!');
    return false;
  }
  
  return true;
};

// 处理Excel到JSON转换
const handleExcelToJsonConvert = async ({ file }) => {
  // 创建唯一的消息ID
  const messageKey = `excel_convert_${Date.now()}`;
  
  try {
    convertingExcel.value = true;
    
    // 显示正在处理的提示
    message.loading({ 
      content: '正在解析Excel文件，请稍候...', 
      key: messageKey,
      duration: 0
    });
    
    // 获取文件基本名（不含扩展名）
    const fileName = file.name.split('.').slice(0, -1).join('.') || 'patent_export';
    
    // 默认配置：表头在第3行 (从0开始计数，索引为2)
    const options = {
      headerRow: 2, // 第3行作为表头
      sheetName: null // 默认使用第一个工作表
    };
    
    // 导入excelToPatentsJson函数
    const { excelToPatentsJson } = await import('@/utils/fileUtils');
    
    // 转换Excel为JSON
    const patentsData = await excelToPatentsJson(file, options);
    
    // 更新消息为处理完成
    message.loading({ 
      content: '数据解析完成...', 
      key: messageKey,
      duration: 1
    });
    
    if (patentsData.length === 0) {
      message.warning('未从Excel文件中提取到任何有效数据', 3);
      return;
    }
    
    console.log(`解析成功，共 ${patentsData.length} 条专利数据`);
    
    // 处理预览数据
    preparePatentsForPreview(patentsData);
    
  } catch (error) {
    console.error('Excel转JSON处理失败:', error);
    // 确保任何loading消息都被清除
    message.destroy(messageKey);
    message.error(`Excel文件处理失败: ${error.message || '未知错误'}`, 5);
  } finally {
    // 最后确保状态复位
    convertingExcel.value = false;
    
    // 延迟0.5秒后销毁所有可能存在的消息
    setTimeout(() => {
      message.destroy(messageKey);
    }, 500);
  }
};

// 准备专利数据进行预览
const preparePatentsForPreview = async (patents) => {
  importPreviewLoading.value = true;
  importPreviewData.value = [];
  userIdCheckResults.value = {
    total: patents.length,
    found: 0,
    notFound: 0
  };
  
  // 处理专利数据，准备预览
  const previewData = patents.map((patent, index) => {
    return {
      key: index,
      patentName: patent.patentName || '',
      categoryName: patent.categoryName || '',
      authorizationDate: patent.authorizationDate || '',
      conversionDate: patent.conversionDate || '',
      firstResponsible: patent.firstResponsible || '',
      remark: patent.remark || '',
      participants: patent.participants?.map(p => p.name).join(', ') || '',
      userIdCheckStatus: 'checking', // 初始状态为检查中
      rawData: patent // 保存原始数据
    };
  });
  
  importPreviewData.value = previewData;
  importPreviewVisible.value = true;
  
  // 检查用户ID
  await checkParticipantsUserId();
  
  importPreviewLoading.value = false;
};

// 检查参与者用户ID
const checkParticipantsUserId = async () => {
  const patentsData = importPreviewData.value;
  let foundCount = 0;
  let notFoundCount = 0;
  
  for (let i = 0; i < patentsData.length; i++) {
    const patent = patentsData[i];
    const participants = patent.rawData?.participants || [];
    let allFound = true;
    
    for (let j = 0; j < participants.length; j++) {
      const participant = participants[j];
      
      try {
        // 搜索用户ID
        const response = await usersSearch({ keyword: participant.name });
        if (response && response.data && response.data.length > 0) {
          // 找到匹配的用户
          participant.participantId = response.data[0].id;
          participant.userId = response.data[0].id;
          foundCount++;
        } else {
          // 未找到匹配的用户
          participant.participantId = null;
          participant.userId = null;
          notFoundCount++;
          allFound = false;
        }
      } catch (error) {
        console.error(`查找用户"${participant.name}"失败:`, error);
        participant.participantId = null;
        participant.userId = null;
        notFoundCount++;
        allFound = false;
      }
    }
    
    // 更新检查状态
    patent.userIdCheckStatus = allFound ? 'found' : 'notFound';
  }
  
  // 更新结果统计
  userIdCheckResults.value = {
    total: foundCount + notFoundCount,
    found: foundCount,
    notFound: notFoundCount
  };
};

// 取消导入预览
const handleCancelImportPreview = () => {
  importPreviewVisible.value = false;
  importPreviewData.value = [];
};

// 开始导入专利数据
const handleStartImport = async () => {
  if (importPreviewData.value.length === 0) {
    message.warning('没有可导入的专利数据');
    return;
  }
  
  // 重置导入结果
  importResults.total = importPreviewData.value.length;
  importResults.success = 0;
  importResults.failed = 0;
  importResults.current = 0;
  importResults.details = [];
  
  // 显示结果模态框
  importResultVisible.value = true;
  importInProgress.value = true;
  
  try {
    for (let i = 0; i < importPreviewData.value.length; i++) {
      importResults.current = i + 1;
      const patent = importPreviewData.value[i].rawData;
      
      try {
        // 构建请求数据
        const patentData = {
          patentName: patent.patentName,
          categoryId: null, // 需要查找专利分类ID
          authorizationDate: patent.authorizationDate,
          conversionDate: patent.conversionDate,
          remark: patent.remark,
          participants: patent.participants
        };
        
        // 查找专利分类ID
        if (patent.categoryName) {
          // 在分类选项中查找匹配的分类
          const category = categoryOptions.value.find(
            c => c.categoryName === patent.categoryName || c.categoryName.includes(patent.categoryName)
          );
          
          if (category) {
            patentData.categoryId = category.id;
          } else {
            throw new Error(`未找到专利分类: ${patent.categoryName}`);
          }
        } else {
          throw new Error('专利分类不能为空');
        }
        
        // 确保第一负责人ID已设置
        if (patent.participants.length > 0) {
          const leader = patent.participants.find(p => p.isLeader === 1);
          if (leader && leader.participantId) {
            patentData.firstResponsibleID = leader.participantId;
          } else if (patent.participants[0].participantId) {
            patentData.firstResponsibleID = patent.participants[0].participantId;
          } else {
            throw new Error('无法确定第一负责人ID');
          }
        } else {
          throw new Error('参与者列表不能为空');
        }
        
        // 发送创建专利请求
        const response = await createPatent(patentData);
        
        if (response && response.code === 200) {
          importResults.success++;
          importResults.details.push({
            index: importResults.details.length + 1,
            patentName: patent.patentName,
            message: '导入成功',
            status: 'success'
          });
        } else {
          throw new Error(response?.message || '创建专利失败');
        }
      } catch (error) {
        console.error(`导入专利"${patent.patentName}"失败:`, error);
        importResults.failed++;
        importResults.details.push({
          index: importResults.details.length + 1,
          patentName: patent.patentName,
          message: `导入失败: ${error.message || '未知错误'}`,
          status: 'error'
        });
      }
      
      // 等待短暂延迟，让UI有时间更新
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 导入完成后重新获取数据
    fetchData();
    
  } catch (error) {
    console.error('导入过程中发生错误:', error);
    message.error(`导入过程中发生错误: ${error.message || '未知错误'}`);
  } finally {
    importInProgress.value = false;
    importPreviewVisible.value = false;
  }
};

// 导出失败记录
const exportFailedRecords = async () => {
  const failedRecords = importResults.details
    .filter(item => item.status === 'error')
    .map((item, index) => ({
      '序号': index + 1,
      '专利名称': item.patentName,
      '失败原因': item.message
    }));
    
  if (failedRecords.length === 0) {
    message.info('没有失败记录需要导出');
    return;
  }
  
  try {
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(failedRecords);
    
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '导入失败记录');
    
    // 生成Excel文件名称
    const fileName = `专利导入失败记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
    
    // 下载文件
    XLSX.writeFile(workbook, fileName);
    
    message.success('导出失败记录成功');
  } catch (error) {
    console.error('导出失败记录出错:', error);
    message.error(`导出失败记录出错: ${error.message || '未知错误'}`);
  }
};

// 处理直接导入
const handleDirectImport = async (patentsData) => {
  // 显示预览模态框
  preparePatentsForPreview(patentsData);
};

// 导入预览数据和结果数据
const importPreviewData = ref([]);
const importPreviewLoading = ref(false);
const importPreviewVisible = ref(false);
const importResultVisible = ref(false);
const importInProgress = ref(false);
const convertingExcel = ref(false);

// 预览表格列定义
const previewColumns = [
  { title: '序号', dataIndex: 'index', width: 60, fixed: 'left' },
  { title: '专利名称', dataIndex: 'patentName', ellipsis: true, width: 200, fixed: 'left' },
  { title: '专利分类', dataIndex: 'categoryName', width: 120 },
  { title: '授权日期', dataIndex: 'authorizationDate', width: 100 },
  { title: '转化日期', dataIndex: 'conversionDate', width: 100 },
  { title: '第一负责人', dataIndex: 'firstResponsible', width: 100 },
  { title: '参与人员', dataIndex: 'participants', width: 200 },
  { title: '备注', dataIndex: 'remark', width: 150 },
  { title: '用户ID状态', dataIndex: 'userIdCheckStatus', width: 120, fixed: 'right' }
];

// 结果表格列定义
const resultColumns = [
  { title: '序号', dataIndex: 'index', width: 60 },
  { title: '专利名称', dataIndex: 'patentName', ellipsis: true },
  { title: '状态', dataIndex: 'status', width: 80 },
  { title: '结果信息', dataIndex: 'message', ellipsis: true }
];

// 导入结果数据
const importResults = reactive({
  total: 0,
  current: 0,
  success: 0,
  failed: 0,
  details: []
});

// 下载预览数据为JSON文件
const handleDownloadJson = async () => {
  try {
    if (!importPreviewData.value || importPreviewData.value.length === 0) {
      message.warning('没有可下载的数据');
      return;
    }
    
    // 提取原始数据
    const originalData = importPreviewData.value.map(item => item.rawData);
    
    // 导入downloadJson工具函数
    const { downloadJson } = await import('@/utils/fileUtils');
    
    // 下载为JSON文件
    await downloadJson(originalData, `专利数据_${dayjs().format('YYYY-MM-DD')}`);
    message.success('JSON文件下载成功');
  } catch (error) {
    console.error('下载JSON文件失败:', error);
    message.error('下载失败: ' + (error.message || '未知错误'));
  }
};

// 重新提交审核
const handleResubmit = (record) => {
  Modal.confirm({
    title: '确认重新提交审核',
    content: '是否确认将该专利重新提交审核？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await resubmitForReview(record.id);
    }
  });
};

// 重新提交审核接口调用
const resubmitForReview = async (id) => {
  try {
    isLoading.value = true;
    const response = await reapplyReview({ id });
    
    if (response && response.code === 200) {
      message.success('重新提交审核成功');
      fetchData(); // 刷新数据
    } else {
      message.error(response?.message || '重新提交审核失败');
    }
  } catch (error) {
    console.error('重新提交审核失败:', error);
    message.error('重新提交审核失败: ' + (error.message || error));
  } finally {
    isLoading.value = false;
  }
};

// 组件销毁时清理图表
onBeforeUnmount(() => {
  if (categoryChart) {
    categoryChart.dispose();
    categoryChart = null;
  }
  if (reviewStatusChart) {
    reviewStatusChart.dispose();
    reviewStatusChart = null;
  }
  if (timeChart) {
    timeChart.dispose();
    timeChart = null;
  }
});
</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';

// 页面特定样式
.patents {
  margin: 24px;
  border: 1px solid #f0f0f0;
}

.text-danger {
  color: #ff4d4f;
}

.import-row-error {
  background-color: #fff2f0 !important;
}

:deep(.import-row-error) {
  background-color: rgba(245, 34, 45, 0.05);
}

:deep(.import-row-error:hover > td) {
  background-color: rgba(245, 34, 45, 0.1) !important;
}
</style>