/**
 * 部门管理模块权限配置
 * 简化版 - 角色验证
 */
module.exports = {
  // 查看部门列表权限
  list: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 获取所有部门权限（用于下拉选择）
  all: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
  },

  // 获取部门树权限
  tree: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 查看部门详情权限
  detail: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 创建部门权限
  create: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 更新部门权限
  update: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 删除部门权限
  delete: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 获取部门统计权限
  statistics: {
    roles: ['ADMIN-LV2', 'SUPER']
  }
};
