import request from '../server'

/**
 * 获取教育教学列表
 * @param {Object} data - 查询参数
 * @returns {Promise} - 请求Promise
 */
export function getEducations(data) {
  return request.post('/v1/education/list', data)
}

/**
 * 获取教育教学详情
 * @param {String} id - 记录ID
 * @returns {Promise} - 请求Promise
 */
export function getEducationDetail(id) {
  return request.post('/v1/education/detail', { id })
}

/**
 * 创建教育教学记录
 * @param {Object} data - 教育教学记录数据
 * @returns {Promise} - 请求Promise
 */
export function createEducation(data) {
  return request.post('/v1/education/create', data)
}

/**
 * 更新教育教学记录
 * @param {String} id - 记录ID
 * @param {Object} data - 教育教学记录数据
 * @returns {Promise} - 请求Promise
 */
export function updateEducation(id, data) {
  return request.post('/v1/education/update', { id, ...data })
}

/**
 * 删除教育教学记录
 * @param {String} id - 记录ID
 * @returns {Promise} - 请求Promise
 */
export function deleteEducation(id) {
  return request.post('/v1/education/delete', { id })
}

/**
 * 导入教育教学记录
 * @param {File} file - Excel文件
 * @returns {Promise} - 请求Promise
 */
export function importEducations(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request.post('/v1/education/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出教育教学记录
 * @param {Object} data - 筛选条件
 * @returns {Promise} - 请求Promise
 */
export function exportEducations(data) {
  return request.post('/v1/education/export', data, {
    responseType: 'blob'
  })
} 