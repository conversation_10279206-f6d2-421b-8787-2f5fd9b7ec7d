const express = require('express');
const router = express.Router();
const awardLevelController = require('../../../controllers/v1/teachingResearchAwards/teachingResearchAwardLevelsController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建教学科技奖励级别权限中间件函数
const levelsPermission = (action) => createModulePermission('teachingResearchAwardLevels', action);

/**
 * 获取奖励级别列表（分页）
 * @route POST /v1/teaching-research-award-levels/list
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} levelName - 级别名称（模糊搜索）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {items: [], total: 0, page: 1, pageSize: 10, totalPages: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list',
  authMiddleware,
  levelsPermission('list'),
  awardLevelController.getTeachingResearchAwardLevels
);

/**
 * 获取奖励级别列表（不分页）
 * @route GET /v1/teaching-research-award-levels/levels
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels',
  authMiddleware,
  levelsPermission('list'),
  awardLevelController.getAllTeachingResearchAwardLevels
);

/**
 * 获取所有级别及其奖励数量
 * @route GET /v1/teaching-research-award-levels/levels-with-count
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id, levelName, score, description, awardCount},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels-with-count',
  authMiddleware,
  levelsPermission('listWithCount'),
  awardLevelController.getLevelsWithCount
);

/**
 * 获取奖励级别详情
 * @route GET /v1/teaching-research-award-levels/level/:id
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/:id',
  authMiddleware,
  levelsPermission('detail'),
  awardLevelController.getTeachingResearchAwardLevelDetail
);

/**
 * 创建奖励级别
 * @route POST /v1/teaching-research-award-levels/create
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} levelName.body.required - 级别名称
 * @param {number} score.body.required - 基础分数
 * @param {string} description.body - 级别描述
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create',
  authMiddleware,
  levelsPermission('create'),
  awardLevelController.createTeachingResearchAwardLevel
);

/**
 * 创建奖励级别（兼容旧路由）
 * @route POST /v1/teaching-research-award-levels/level/create
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} levelName.body.required - 级别名称
 * @param {number} score.body.required - 基础分数
 * @param {string} description.body - 级别描述
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/create',
  authMiddleware,
  levelsPermission('create'),
  awardLevelController.createTeachingResearchAwardLevel
);

/**
 * 更新奖励级别
 * @route POST /v1/teaching-research-award-levels/update
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.body.required - 级别ID
 * @param {string} levelName.body - 级别名称
 * @param {number} score.body - 基础分数
 * @param {string} description.body - 级别描述
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update',
  authMiddleware,
  levelsPermission('update'),
  async (req, res) => {
    const { id, ...updateData } = req.body;
    req.params = { id };
    req.body = updateData;
    await awardLevelController.updateTeachingResearchAwardLevel(req, res);
  }
);

/**
 * 更新奖励级别（兼容旧路由）
 * @route POST /v1/teaching-research-award-levels/level/update
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.body.required - 级别ID
 * @param {string} levelName.body - 级别名称
 * @param {number} score.body - 基础分数
 * @param {string} description.body - 级别描述
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/update',
  authMiddleware,
  levelsPermission('update'),
  async (req, res) => {
    const { id, ...updateData } = req.body;
    req.params = { id };
    req.body = updateData;
    await awardLevelController.updateTeachingResearchAwardLevel(req, res);
  }
);

/**
 * 删除奖励级别
 * @route POST /v1/teaching-research-award-levels/delete
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.body.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete',
  authMiddleware,
  levelsPermission('delete'),
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await awardLevelController.deleteTeachingResearchAwardLevel(req, res);
  }
);

/**
 * 删除奖励级别（兼容旧路由）
 * @route POST /v1/teaching-research-award-levels/level/delete
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.body.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/delete',
  authMiddleware,
  levelsPermission('delete'),
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await awardLevelController.deleteTeachingResearchAwardLevel(req, res);
  }
);

/**
 * 获取奖励级别分布数据
 * @route POST /v1/teaching-research-award-levels/statistics/distribution
 * @group 教学科技奖励级别统计 - 教学科技奖励级别统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的奖励
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "级别名称", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/distribution',
  authMiddleware,
  levelsPermission('distribution'),
  awardLevelController.getLevelDistribution
);

/**
 * 获取级别下的奖励列表
 * @route GET /v1/teaching-research-award-levels/level/:id/awards
 * @group 教学科技奖励级别管理 - 教学科技奖励级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/:id/awards',
  authMiddleware,
  levelsPermission('awards'),
  awardLevelController.getAwardsByLevel
);

module.exports = router;
