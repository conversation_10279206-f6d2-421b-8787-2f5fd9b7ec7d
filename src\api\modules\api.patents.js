import request from '../server'

// 获取专利详情（POST方式）
export function getPatentDetail(id) {
  return request.post('/patents/detail', { id })
}

// 修改前
export function getPatentDetailByGet(id) {
  return request.get('/patents/detail', { id })
}

// 创建专利（支持文件上传）
export function createPatent(data) {
  return request.post('/patents/create', data)
}

// 更新专利（支持文件上传）
export function updatePatent(id, data) {
  // 确保data包含id
  if (data instanceof FormData && !data.has('id')) {
    data.append('id', id);
  }

  console.log("提交到服务器的数据:", data);

  // 如果是FormData，打印所有键值对帮助调试
  if (data instanceof FormData) {
    console.log("FormData内容:");
    for (const [key, value] of data.entries()) {
      console.log(`${key}: ${value}`);
    }
  }

  return request.post('/patents/update', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 删除专利
export function deletePatent(id) {
  return request.post('/patents/delete', { id })
}

// 获取专利类别列表
export function getPatentCategoryList() {
  return request.get('/patentCategories')
}

// 创建专利类别
export function createPatentCategory(data) {
  return request.post('/patentCategories', data)
}

// 更新专利类别
export function updatePatentCategory(id, data) {
  return request.put(`/patentCategories/${id}`, data)
}

// 删除专利类别
export function deletePatentCategory(id) {
  return request.delete(`/patentCategories/${id}`)
}

/**
 * 审核专利
 * @param {String} id - 专利ID
 * @param {Object} data - 审核数据，包含 reviewStatus, reviewComment, reviewer
 * @returns {Promise} - 请求结果
 */
export function reviewPatent(id, data) {
  return request.post(`/patents/${id}/review`, data)
}

/**
 * 导入专利数据
 * @param {File} file - 文件对象
 * @returns {Promise} - 请求结果
 */
export function importPatents(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request.post('/patents/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出专利数据
 * @param {Object} data - 查询条件
 * @returns {Promise} - 请求结果
 */
export function exportPatents(data) {
  return request.get('/patents/export', { params: data || {} })
}

/**
 * 获取专利列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getPatents(params) {
  return request.post('/patents/list', params || {})
}

/**
 * 获取专利时间分布数据
 * @param {Object} data - 请求参数
 * @returns {Promise} - 请求结果
 */
export function getPatentTimeDistribution(data) {
  return request.post('/patents/patent-statistics/time-distribution', data)
}

/**
 * 获取所有用户专利总得分统计
 * @param {Object} data - 请求参数
 * @returns {Promise} - 请求结果
 */
export function getAllUsersTotalScore(data) {
  return request.post('/patents/patent-statistics/teacher-ranking', data)
}

/**
 * 获取用户专利详情
 * @param {Object} params - 请求参数
 * @param {string} params.userId - 用户ID，必填
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {string} [params.reviewStatus='all'] - 可选，审核状态筛选，可选值：'reviewed'(已通过), 'reject'(已拒绝), 'pending'(待审核), 'all'(全部)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @returns {Promise} - 请求Promise对象
 */
export function getUserPatentDetails(params) {
  return request.post('/patents/patent-statistics/user-patent-details', params)
}

/**
 * 获取专利总分统计
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getPatentsTotalScore(params) {
  return request.post('/patents/statistics/patents-total-score', params);
}

/**
 * 获取用户专利详情
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getUserPatentsDetail(params) {
  return request.post('/patents/user/details', params);
}

/**重新提交审核 */
export function reapplyReview(params) {
  return request.post('/patents/reapply', params)
}

/**
 * 获取审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getReviewStatusOverview(params) {
  return request.post('/patents/statistics/review-status-overview', params)
}
