const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { BadRequestError } = require('../utils/errors');

/**
 * 创建文件夹
 * @param {string} folderPath - 文件夹路径
 */
function createFolder(folderPath) {
    const folders = folderPath.split(path.sep);
    let currentPath = '';

    folders.forEach((folder) => {
        currentPath = path.join(currentPath, folder);
        if (!fs.existsSync(currentPath)) {
            fs.mkdirSync(currentPath);
        }
    });
}

/**
 * 文件上传中间件
 * @param {string} uploadPath - 文件存放的目标文件夹名称 主目录上 例如 uploads
 * @param {string} fieldName - 上传文件的字段名 默认值：file 通过req.file取到
 * @param {array} allowedFileTypes - 允许上传的图片类型
 * @returns {Function} - Express 中间件函数
 */
function uploadMiddleware(uploadPath, fieldName = 'file', allowedFileTypes = []) {
    const storage = multer.diskStorage({
        destination: (req, file, cb) => {
            let destination;
            if (file.mimetype.startsWith('image/') && file.mimetype !== 'image/svg+xml') {
                destination = path.join(uploadPath, 'images');
            } else if (file.mimetype.startsWith('video/') || file.mimetype.startsWith('audio/')) {
                destination = path.join(uploadPath, 'media');
            } else {
                destination = path.join(uploadPath, 'files');
            }

            if (allowedFileTypes.length > 0 && !allowedFileTypes.includes(file.mimetype)) {
                return cb(new Error('该文件类型不允许上传：'+file.mimetype), null);
            }

            createFolder(destination);
            cb(null, destination);
        },
        filename: (req, file, cb) => {
            const timestamp = Date.now();
            const extension = path.extname(file.originalname);
            const newFilename = `${timestamp}${extension}`;
            cb(null, newFilename);
        }
    });

    const upload = multer({storage});

    return upload.single(fieldName);
}

/**
 * 多文件上传中间件
 * @param {string} uploadPath - 文件上传的目标路径
 * @param {array} fieldNames - 上传文件的字段名 默认值：files 通过req.files取到  files 是数组
 * @returns {Function} - Express 中间件函数
 */
function uploadArrayMiddleware(uploadPath, fieldNames = ['files']) {
    const storage = multer.diskStorage({
        destination: (req, file, cb) => {
            let destination;
            if (file.mimetype.startsWith('image/') && file.mimetype !== 'image/svg+xml') {
                destination = path.join(uploadPath, 'images', Date.now().toString());
            } else if (file.mimetype.startsWith('video/') || file.mimetype.startsWith('audio/')) {
                destination = path.join(uploadPath, 'media', Date.now().toString());
            } else {
                destination = path.join(uploadPath, 'files', Date.now().toString());
            }
            createFolder(destination);
            cb(null, destination);
        },
        filename: (req, file, cb) => {
            const timestamp = Date.now();
            const extension = path.extname(file.originalname);
            const newFilename = `${timestamp}${extension}`;
            cb(null, newFilename);
        }
    });

    const upload = multer({ storage });

    // 使用 upload.array，支持多个文件
    return upload.array(fieldNames);
}

// 配置文件存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../uploads'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new BadRequestError('不支持的文件类型'));
  }
};

// 创建multer实例
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 限制文件大小为5MB
  }
});

module.exports = { 
    uploadMiddleware, 
    uploadArrayMiddleware,
    upload
}; 