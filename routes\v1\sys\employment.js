const express = require('express');
const router = express.Router();
const employmentController = require('../../../controllers/v1/rules/employmentController');
const employmentQualityMiddleware = require('../../../middleware/employmentQualityMiddleware');
const multer = require('multer');
const path = require('path');

// 配置 multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../../uploads'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// ===== 固定路径路由 - 按照从具体到通用的顺序排列 =====

/**
 * 获取就业质量列表
 * @route GET /v1/sys/employment/list
 * @group 就业管理 - 就业质量相关接口
 * @param {string} major.query - 专业名称
 * @param {string} year.query - 年份
 * @param {string} employmentIndustry.query - 就业行业
 * @param {string} employmentRegion.query - 就业地区
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @param {string} userId.query - 用户ID（筛选个人记录）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0, page: 1, pageSize: 10, totalPages: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list', employmentQualityMiddleware.validatePagination, employmentController.getEmploymentList);

/**
 * 导出就业质量数据
 * @route GET /v1/sys/employment/export
 * @group 就业管理 - 就业质量相关接口
 * @param {string} major.query - 专业名称
 * @param {string} year.query - 年份
 * @param {string} employmentIndustry.query - 就业行业
 * @param {string} employmentRegion.query - 就业地区
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/export', employmentController.exportEmployments);

/**
 * 获取就业率趋势
 * @route GET /v1/sys/employment/stats/rate-trend
 * @group 就业管理 - 就业质量统计接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {trends: [{year: "2020", employmentRate: "95.5"}, ...], startYear: 2016, endYear: 2020}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/rate-trend', employmentQualityMiddleware.validateEmploymentRateTrend, employmentController.getEmploymentRateTrend);

/**
 * 获取就业行业分布
 * @route GET /v1/sys/employment/stats/industry-distribution
 * @group 就业管理 - 就业质量统计接口
 * @param {string} year.query - 年份
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{industry: "IT", count: 50}, ...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/industry-distribution', employmentQualityMiddleware.validateDistributionParams, employmentController.getEmploymentIndustryDistribution);

/**
 * 获取就业地区分布
 * @route GET /v1/sys/employment/stats/region-distribution
 * @group 就业管理 - 就业质量统计接口
 * @param {string} year.query - 年份
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{region: "一线城市", count: 30}, ...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/region-distribution', employmentQualityMiddleware.validateDistributionParams, employmentController.getEmploymentRegionDistribution);

/**
 * 获取专业评分排名
 * @route GET /v1/sys/employment/stats/major-ranking
 * @group 就业管理 - 就业质量统计接口
 * @param {string} year.query - 年份
 * @param {number} limit.query - 限制数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{major: "计算机科学与技术", score: 95.5}, ...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/major-ranking', employmentQualityMiddleware.validateDistributionParams, employmentController.getMajorScoreRanking);

/**
 * 获取就业质量年度分析
 * @route GET /v1/sys/employment/analysis
 * @group 就业管理 - 就业质量统计接口
 * @param {string} year.query.required - 年份
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {year, totalGraduates, totalEmployed, avgEmploymentRate, avgSalary, employmentRateRanking, salaryRanking, satisfactionRanking, departmentData}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/analysis', employmentQualityMiddleware.validateEmploymentAnalysis, employmentController.getEmploymentAnalysis);

/**
 * 获取个人就业质量记录
 * @route GET /v1/sys/employment/personal
 * @group 就业管理 - 个人就业质量接口
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @param {string} major.query - 专业名称
 * @param {string} year.query - 年份
 * @param {string} employmentIndustry.query - 就业行业
 * @param {string} employmentRegion.query - 就业地区
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0, page: 1, pageSize: 10, totalPages: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/personal', [
  employmentQualityMiddleware.validatePagination,
  employmentQualityMiddleware.validateEmploymentQualityId,
  employmentQualityMiddleware.checkEmploymentQualityPermission
], employmentController.getPersonalEmployments);

/**
 * 获取个人就业质量统计
 * @route GET /v1/sys/employment/personal-stats
 * @group 就业管理 - 个人就业质量接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {years: [], employmentRates: [], industries: [], regions: [], salaries: [], scores: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/personal-stats', employmentQualityMiddleware.validateEmploymentQualityId, employmentController.getPersonalEmploymentStats);

/**
 * 导入就业质量数据
 * @route POST /v1/sys/employment/import
 * @group 就业管理 - 就业质量相关接口
 * @param {file} file.body.required - Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', upload.single('file'), employmentController.importEmployments);

// ===== 参数路径路由 =====

/**
 * 获取就业质量详情
 * @route GET /v1/sys/employment/:id
 * @group 就业管理 - 就业质量相关接口
 * @param {string} id.path.required - 就业质量记录ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {就业质量详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/:id', employmentQualityMiddleware.validatePathId, employmentController.getEmploymentById);

/**
 * 创建就业质量记录
 * @route POST /v1/sys/employment
 * @group 就业管理 - 就业质量相关接口
 * @param {string} major.body.required - 专业名称
 * @param {string} year.body.required - 年份
 * @param {number} employmentRate.body.required - 就业率
 * @param {string} employmentIndustry.body - 就业行业
 * @param {string} employmentRegion.body - 就业地区
 * @param {number} averageSalary.body - 平均薪资
 * @param {number} majorMatchRate.body - 专业对口率
 * @param {number} employmentSatisfaction.body - 就业满意度
 * @param {number} score.body - 评分
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "就业质量记录ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/', employmentQualityMiddleware.validateEmploymentQuality, employmentController.createEmployment);

/**
 * 更新就业质量记录
 * @route PUT /v1/sys/employment/:id
 * @group 就业管理 - 就业质量相关接口
 * @param {string} id.path.required - 就业质量记录ID
 * @param {string} major.body - 专业名称
 * @param {string} year.body - 年份
 * @param {number} employmentRate.body - 就业率
 * @param {string} employmentIndustry.body - 就业行业
 * @param {string} employmentRegion.body - 就业地区
 * @param {number} averageSalary.body - 平均薪资
 * @param {number} majorMatchRate.body - 专业对口率
 * @param {number} employmentSatisfaction.body - 就业满意度
 * @param {number} score.body - 评分
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/:id', [
  employmentQualityMiddleware.validatePathId,
  employmentQualityMiddleware.validateEmploymentQuality,
  employmentQualityMiddleware.checkEmploymentQualityPermission
], employmentController.updateEmployment);

/**
 * 删除就业质量记录
 * @route DELETE /v1/sys/employment/:id
 * @group 就业管理 - 就业质量相关接口
 * @param {string} id.path.required - 就业质量记录ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/:id', [
  employmentQualityMiddleware.validatePathId,
  employmentQualityMiddleware.checkEmploymentQualityPermission
], employmentController.deleteEmployment);

module.exports = router; 