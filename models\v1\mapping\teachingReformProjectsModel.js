const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const projectLevelModel = require('./teachingReformProjectLevelsModel');
const userModel = require('./userModel');
const participantModel = require('./teachingReformParticipantsModel');

// 定义教学改革项目模型
const TeachingReformProject = sequelize.define('teaching_reform_projects', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    projectNumber: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '项目编号'
    },
    projectName: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '项目名称'
    },
    approvalDepartment: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '下达部门'
    },
    approvalDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '获批日期'
    },
    approvalFund: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        comment: '批准经费(万元)'
    },
    levelId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '级别ID'
    },
    startYear: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '执行起始年'
    },
    endYear: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '执行结束年'
    },
    remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注'
    },
    reviewerId: {
        type: DataTypes.CHAR(36),
        allowNull: true,
        comment: '审核人ID'
    },
    ifReviewer: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        comment: '审核状态（0，拒审核 1，审核，null未审核）'
      },
      attachmentUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '附件URL'
      },
      reviewComment: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '审核意见'
      },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'teaching_reform_projects',
    timestamps: true,
    indexes: [
        {
            name: 'idx_project_number',
            fields: ['projectNumber']
        },
        {
            name: 'idx_approval_date',
            fields: ['approvalDate']
        },
        {
            name: 'idx_level',
            fields: ['levelId']
        }
    ]
});

// 建立与项目级别的关联关系
TeachingReformProject.belongsTo(projectLevelModel, {
    foreignKey: 'levelId',
    as: 'level'
});

// 建立与审核人的关联关系
TeachingReformProject.belongsTo(userModel, {
    foreignKey: 'reviewerId',
    as: 'reviewer'
});

// 建立与参与者的关联关系
TeachingReformProject.hasMany(participantModel, {
    foreignKey: 'projectId',
    as: 'participants'
});

module.exports = TeachingReformProject; 