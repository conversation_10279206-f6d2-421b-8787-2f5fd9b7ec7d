import request from '../server'

/**
 * 获取获奖级别列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getAwardLevels(params) {
  return request.get('/studentAwardGuidanceAwardLevels/levels', { params })
}

/**
 * 获取所有级别
 * @returns {Promise} - 请求结果
 */
export function getAllLevels() {
  return request.get('/studentAwardGuidanceAwardLevels/all-levels')
}

/**
 * 获取所有级别及其获奖数量
 * @returns {Promise} - 请求结果
 */
export function getLevelsWithCount() {
  return request.get('/studentAwardGuidanceAwardLevels/levels-with-count')
}

/**
 * 获取获奖级别详情
 * @param {string} id - 级别ID
 * @returns {Promise} - 请求结果
 */
export function getAwardLevelDetail(id) {
  return request.get(`/studentAwardGuidanceAwardLevels/level/${id}`)
}

/**
 * 创建获奖级别
 * @param {Object} data - 级别数据
 * @returns {Promise} - 请求结果
 */
export function createAwardLevel(data) {
  return request.post('/studentAwardGuidanceAwardLevels/level/create', data)
}

/**
 * 更新获奖级别
 * @param {string} id - 级别ID
 * @param {Object} data - 更新数据
 * @returns {Promise} - 请求结果
 */
export function updateAwardLevel(id, data) {
  return request.post('/studentAwardGuidanceAwardLevels/level/update', { id, ...data })
}

/**
 * 删除获奖级别
 * @param {string} id - 级别ID
 * @returns {Promise} - 请求结果
 */
export function deleteAwardLevel(id) {
  return request.post('/studentAwardGuidanceAwardLevels/level/delete', { id })
}

/**
 * 获取获奖级别分布数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getLevelDistribution(params = {}) {
  return request.post('/studentAwardGuidanceAwardLevels/statistics/distribution', params)
}