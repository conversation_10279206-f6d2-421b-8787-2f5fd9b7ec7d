<template>
  <div class="header-notification">
    <a-badge :count="unreadCount" :dot="showDot">
      <a-dropdown :trigger="['click']" placement="bottomRight">
        <BellOutlined class="notification-icon" />
        <template #overlay>
          <a-menu @click="handleMenuClick">
            <a-menu-item key="center">
              <span v-if="unreadCount > 0">您有 {{ unreadCount }} 条未读消息</span>
              <span v-else>暂无未读消息</span>
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item v-for="notification in recentNotifications" :key="notification.key">
              <div class="notification-item" :class="{ 'unread': notification.status === '未读' }">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-time">{{ notification.sendTime }}</div>
              </div>
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="readAll" v-if="unreadCount > 0">
              <span>全部标为已读</span>
            </a-menu-item>
            <a-menu-item key="viewAll">
              <span>查看全部消息</span>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </a-badge>
  </div>
</template>

<script setup>
import { BellOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '@/stores/notification'
import { computed, onMounted } from 'vue'

const router = useRouter()
const notificationStore = useNotificationStore()

// 获取未读消息数量
const unreadCount = computed(() => notificationStore.unreadCount)

// 是否显示红点
const showDot = computed(() => unreadCount.value > 0)

// 获取最近5条通知
const recentNotifications = computed(() => {
  return notificationStore.notifications.slice(0, 5)
})

// 菜单点击处理
const handleMenuClick = ({ key }) => {
  if (key === 'center') {
    router.push('/notification/center')
  } else if (key === 'readAll') {
    notificationStore.markAllAsRead()
  } else if (key === 'viewAll') {
    router.push('/notification/list')
  } else {
    // 点击具体的通知
    const notification = notificationStore.notifications.find(item => item.key === key)
    if (notification) {
      notificationStore.markAsRead(key)
      router.push('/notification/center')
    }
  }
}

// 初始化数据
onMounted(() => {
  notificationStore.getNotifications()
})
</script>

<style scoped>
.header-notification {
  display: inline-block;
  padding: 0 12px;
  cursor: pointer;
}

.notification-icon {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.65);
}

.notification-item {
  padding: 5px 0;
}

.notification-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

.notification-time {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.unread .notification-title {
  color: #1890ff;
  font-weight: 500;
}
</style> 