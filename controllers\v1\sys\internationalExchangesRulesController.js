const { Op } = require('sequelize');
const internationalExchangesRulesModel = require('../../../models/v1/mapping/internationalExchangesRulesModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取国际交流规则列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getInternationalExchangesRules = async (req, res) => {
    try {
        console.log('🔍 获取国际交流规则列表 - 请求参数:', req.query);
        
        // 获取查询参数
        const { page = 1, pageSize = 10, project } = req.query;
        
        // 确保 page 和 pageSize 是有效的数字，否则使用默认值
        const pageNum = page ? parseInt(page) : 1;
        const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

        // 如果 page 或 pageSize 是无效数字，返回默认值
        const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
        const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;
        
        console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

        // 构建查询条件
        const where = {};
        if (project) {
            where.project = { [Op.like]: `%${project}%` };
        }
        
        console.log('🔍 最终查询条件:', JSON.stringify(where));

        // 分页查询
        const offset = (validPage - 1) * validPageSize;
        try {
            console.log('📚 执行数据库查询...');
            const { count, rows } = await internationalExchangesRulesModel.findAndCountAll({
                where,
                offset,
                limit: validPageSize,
                order: [['createdAt', 'DESC']] // 默认按时间倒序
            });
            
            console.log(`✅ 查询成功: 共${count}条记录`);
            
            const totalPages = Math.ceil(count / validPageSize);
            
            return res.status(200).json({
                code: 200,
                message: '获取成功',
                data: {
                    total: count,
                    page: validPage,
                    pageSize: validPageSize,
                    totalPages,
                    list: rows
                }
            });
        } catch (dbError) {
            console.error('❌ 数据库查询失败:', dbError);
            return res.status(500).json({
                code: 500,
                message: `数据库查询失败: ${dbError.message}`,
                data: null
            });
        }
    } catch (error) {
        console.error('❌ 获取国际交流规则列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取国际交流规则列表失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取国际交流规则详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getInternationalExchangeRuleDetail = async (req, res) => {
    try {
        const { id } = req.query;
        console.log('🔍 获取国际交流规则详情 - ID:', id);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        const rule = await internationalExchangesRulesModel.findByPk(id);
        if (!rule) {
            return res.status(404).json({
                code: 404,
                message: '未找到该规则',
                data: null
            });
        }
        
        console.log(`✅ 查询成功: 规则ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: rule
        });
    } catch (error) {
        console.error('❌ 获取国际交流规则详情失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取国际交流规则详情失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 创建国际交流规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createInternationalExchangeRule = async (req, res) => {
    try {
        const { project, score, description, maxPeople } = req.body;
        console.log('📝 创建国际交流规则 - 请求数据:', { project, score });
        
        // 校验必填参数
        if (!project || score === undefined) {
            return res.status(400).json({
                code: 400,
                message: '交流类型和基础分数不能为空',
                data: null
            });
        }
        
        // 检查交流类型是否已存在
        const existingRule = await internationalExchangesRulesModel.findOne({
            where: { project }
        });

        if (existingRule) {
            return res.status(400).json({
                code: 400,
                message: '该交流类型已存在',
                data: null
            });
        }
        
        // 创建规则
        const rule = await internationalExchangesRulesModel.create({
            id: uuidv4(),
            project,
            score,
            description,
            maxPeople,
            createdBy: req.user.id
        });
        
        console.log(`✅ 创建成功: 规则ID ${rule.id}`);
        
        return res.status(201).json({
            code: 200,
            message: '创建成功',
            data: rule
        });
    } catch (error) {
        console.error('❌ 创建国际交流规则失败:', error);
        return res.status(500).json({
            code: 500,
            message: `创建国际交流规则失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 更新国际交流规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateInternationalExchangeRule = async (req, res) => {
    try {
        const { id, project, score, description, maxPeople } = req.body;
        console.log('📝 更新国际交流规则 - 请求数据:', { id, project, score });

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        // 查找规则
        const rule = await internationalExchangesRulesModel.findByPk(id);
        if (!rule) {
            return res.status(404).json({
                code: 404,
                message: '未找到该规则',
                data: null
            });
        }

        // 如果更新了交流类型，检查是否与其他记录重复
        if (project && project !== rule.project) {
            const existingRule = await internationalExchangesRulesModel.findOne({
                where: {
                    id: { [Op.ne]: id },
                    project
                }
            });

            if (existingRule) {
                return res.status(400).json({
                    code: 400,
                    message: '该交流类型已存在',
                    data: null
                });
            }
        }

        // 更新规则
        await rule.update({
            project: project || rule.project,
            score: score !== undefined ? score : rule.score,
            description: description || rule.description,
            maxPeople: maxPeople || rule.maxPeople
        });
        
        console.log(`✅ 更新成功: 规则ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '更新成功',
            data: rule
        });
    } catch (error) {
        console.error('❌ 更新国际交流规则失败:', error);
        return res.status(500).json({
            code: 500,
            message: `更新国际交流规则失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 删除国际交流规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteInternationalExchangeRule = async (req, res) => {
    try {
        const { id } = req.query;
        console.log('🗑️ 删除国际交流规则 - ID:', id);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        const rule = await internationalExchangesRulesModel.findByPk(id);
        if (!rule) {
            return res.status(404).json({
                code: 404,
                message: '未找到该规则',
                data: null
            });
        }

        await rule.destroy();
        
        console.log(`✅ 删除成功: 规则ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 删除国际交流规则失败:', error);
        return res.status(500).json({
            code: 500,
            message: `删除国际交流规则失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 批量删除国际交流规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteInternationalExchangeRules = async (req, res) => {
    try {
        const { ids } = req.body;
        console.log('🗑️ 批量删除国际交流规则 - IDs:', ids);

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                code: 400,
                message: 'ID列表不能为空',
                data: null
            });
        }

        await internationalExchangesRulesModel.destroy({
            where: {
                id: {
                    [Op.in]: ids
                }
            }
        });
        
        console.log(`✅ 批量删除成功: 共${ids.length}条记录`);
        
        return res.status(200).json({
            code: 200,
            message: '批量删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 批量删除国际交流规则失败:', error);
        return res.status(500).json({
            code: 500,
            message: `批量删除国际交流规则失败: ${error.message}`,
            data: null
        });
    }
}; 