<!-- 通用统计卡片组件 -->
<template>
  <a-card :title="title" :bordered="false" style="margin-bottom: 24px;">
    <a-spin :spinning="loading">
      <!-- 时间区间信息 -->
      <div v-if="data.timeInterval" class="time-interval">
        <a-tag color="blue">
          <InfoCircleOutlined /> 统计时间: {{ data.timeInterval.startTime }} 至 {{ data.timeInterval.endTime }}
        </a-tag>
      </div>

      <!-- 总体统计卡片 -->
      <a-card :bordered="false" :bodyStyle="{ padding: '10px' }" style="margin-bottom: 16px;">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-statistic 
              :title="countLabel"
              :value="totalCount" 
              style="text-align: center;"
              :valueStyle="{ color: '#1890ff', fontSize: '24px', fontWeight: 'bold' }"
            />
          </a-col>
          <a-col :span="12">
            <a-statistic 
              title="总得分"
              :value="totalScore" 
              style="text-align: center;"
              :valueStyle="{ color: '#52c41a', fontSize: '24px', fontWeight: 'bold' }"
            />
          </a-col>
        </a-row>
      </a-card>
      
      <!-- 饼图，单独成行 -->
      <div style="margin-bottom: 16px; text-align: center;">
        <div ref="chartContainer" class="chart-container"></div>
      </div>

      <!-- 统计表格 -->
      <a-table
        :columns="columns"
        :dataSource="stats"
        :rowKey="rowKey"
        :pagination="false"
        :bordered="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'totalScore'">
            <span style="font-weight: bold; color: #1890ff;">{{ record.totalScore?.toFixed(2) }}</span>
          </template>
        </template>
      </a-table>
    </a-spin>
  </a-card>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'
import { InfoCircleOutlined } from '@ant-design/icons-vue'

// 定义组件属性
const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    required: true
  },
  // 计数标签，如"项目总数"、"论文总数"等
  countLabel: {
    type: String,
    required: true
  },
  // 获取数据的API函数
  fetchApi: {
    type: Function,
    required: true
  },
  // 表格列配置
  columns: {
    type: Array,
    required: true
  },
  // 表格行键名
  rowKey: {
    type: String,
    default: 'id'
  },
  // 总数对应的键名，如"totalProjects"、"totalPapers"等
  totalCountKey: {
    type: String,
    required: true
  },
  // 从API响应中获取统计数据的自定义函数
  getStatsFromResponse: {
    type: Function,
    default: (response) => response.data.levelStats || []
  },
  // 自定义参数
  customParams: {
    type: Object,
    default: () => ({})
  },
  // 是否自动加载数据，默认为true
  shouldAutoLoad: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits(['data-loaded'])

// 筛选条件
const filters = reactive({
  range: 'all',
  reviewStatus: 'all'
})

// 加载状态
const loading = ref(false)
// 数据是否已加载标记
const isDataLoaded = ref(false)

// 数据结构
const data = reactive({
  stats: [], // 统计数据（按级别/类型）
  overallStats: {}, // 总体统计数据
  totalStats: {}, // 总体统计数据（替代结构）
  timeInterval: null // 时间区间
})

// 图表相关
const chartContainer = ref(null)
let chart = null
const chartColors = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', 
  '#722ed1', '#13c2c2', '#fa8c16', '#eb2f96', '#a0d911'
]

// 计算属性：统计数据
const stats = computed(() => data.stats)

// 计算属性：总数
const totalCount = computed(() => {
  // 尝试从不同可能的数据结构中获取总数
  if (data.overallStats && data.overallStats[props.totalCountKey] !== undefined) {
    return data.overallStats[props.totalCountKey]
  }
  
  if (data.totalStats && data.totalStats[props.totalCountKey] !== undefined) {
    return data.totalStats[props.totalCountKey]
  }
  
  // 如果没有预定义的总数，从统计数据中计算
  if (data.stats && data.stats.length > 0) {
    return data.stats.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0)
  }
  
  return 0
})

// 计算属性：总得分
const totalScore = computed(() => {
  // 尝试从不同可能的数据结构中获取总分
  let score = 0
  
  if (data.overallStats && data.overallStats.totalScore !== undefined) {
    score = data.overallStats.totalScore
  } else if (data.totalStats && data.totalStats.totalScore !== undefined) {
    score = data.totalStats.totalScore
  } else if (data.stats && data.stats.length > 0) {
    score = data.stats.reduce((sum, item) => sum + (parseFloat(item.totalScore) || 0), 0)
  }
  
  return score.toFixed(2)
})

// 初始化图表
const initChart = () => {
  if (chartContainer.value && !chart) {
    chart = echarts.init(chartContainer.value)
    updateChart()
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chart) return
  
  const chartData = data.stats.map(item => {
    const name = item.levelName || item.typeName || item.categoryName || '未分类'
    return {
      name,
      value: parseFloat(item.totalScore) || 0
    }
  })
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      bottom: 15,
      left: 'center',
      fontSize: 12,
      formatter: function (name) {
        // 如果名称超过8个字符，截断并添加省略号
        return name.length > 8 ? name.substring(0, 8) + '...' : name;
      },
      tooltip: {
        show: true,
        formatter: function (params) {
          return params.name;
        }
      },
      padding: [5, 10],
      itemGap: 8,
      itemWidth: 15,
      itemHeight: 10,
      pageButtonItemGap: 5,
      pageButtonGap: 5,
      pageButtonPosition: 'end',
      pageIconColor: '#2f4554',
      pageIconInactiveColor: '#aaa',
      pageIconSize: 15,
      pageTextStyle: {
        color: '#333'
      },
      // 设置grid布局相关配置
      grid: {
        top: 10,
        bottom: 20
      },
      // 分页相关配置
      pageButtonCount: 5,
      selector: false,
      // 设置为网格布局，每行显示3个图例项
      layoutGrid: {
        rows: 2,
        itemWidth: 100
      }
    },
    series: [
      {
        name: '分数分布',
        type: 'pie',
        radius: ['35%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData,
        color: chartColors
      }
    ]
  }
  
  chart.setOption(option)
}

// 窗口大小变化时重新调整图表大小
const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

// 获取数据
const fetchData = async () => {
  // 如果正在加载，不要重复请求
  if (loading.value) return

  loading.value = true
  
  try {
    console.log(`${props.title}: 开始获取数据，参数:`, {
      range: filters.range,
      reviewStatus: filters.reviewStatus,
      ...props.customParams
    });

    const response = await props.fetchApi({
      range: filters.range,
      reviewStatus: filters.reviewStatus,
      ...props.customParams
    })
    
    console.log(`${props.title} API返回数据:`, response)
    
    if (response && response.code === 200) {
      // 获取统计数据并处理
      const statsData = props.getStatsFromResponse(response)
      
      // 处理统计数据
      data.stats = Array.isArray(statsData) 
        ? statsData.map(item => ({
          ...item,
          count: parseInt(item.count || 0),
          totalScore: parseFloat(item.totalScore || 0)
        }))
        : []
      
      console.log('处理后的统计数据:', data.stats)
      
      // 保存总体统计数据（支持不同的API响应结构）
      data.overallStats = response.data.overallStats || {}
      data.totalStats = response.data.totalStats || {}
      
      // 如果API没有返回总计数据，从统计数据计算
      if ((!data.overallStats || !data.overallStats[props.totalCountKey]) && 
          (!data.totalStats || !data.totalStats[props.totalCountKey]) && 
          data.stats.length > 0) {
        
        // 根据统计数据计算总数和总分
        const totalCount = data.stats.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0)
        const totalScore = parseFloat(
          data.stats
            .reduce((sum, item) => sum + (parseFloat(item.totalScore) || 0), 0)
            .toFixed(2)
        )
        
        // 根据API返回结构选择合适的对象来保存计算结果
        if (response.data.overallStats) {
          data.overallStats[props.totalCountKey] = totalCount
          data.overallStats.totalScore = totalScore
        } else if (response.data.totalStats) {
          data.totalStats[props.totalCountKey] = totalCount
          data.totalStats.totalScore = totalScore
        } else {
          // 如果都不存在，创建一个新的totalStats对象
          data.totalStats = {
            [props.totalCountKey]: totalCount,
            totalScore: totalScore
          }
        }
      }
      
      // 时间区间数据处理
      data.timeInterval = response.data.timeInterval
      console.log('时间区间数据:', data.timeInterval)
      
      // 更新图表
      nextTick(() => {
        updateChart()
      })
      
      // 标记数据已加载
      isDataLoaded.value = true
      
      // 向父组件发送数据加载完成事件
      const result = {
        totalScore: parseFloat(totalScore.value),
        totalCount: totalCount.value,
        stats: data.stats,
        timeInterval: data.timeInterval
      }
      emit('data-loaded', result)
    } else {
      message.error(response?.message || `获取${props.title}数据失败`)
    }
  } catch (error) {
    console.error(`获取${props.title}数据失败:`, error)
    message.error(`获取${props.title}数据失败: ` + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 重新加载数据的方法，可以强制刷新
const reloadData = async (forceReload = false) => {
  // 如果数据已加载且不是强制刷新，则不重新加载
  if (isDataLoaded.value && !forceReload) {
    console.log(`${props.title}: 数据已加载，跳过重复请求`);
    return;
  }
  
  await fetchData();
}

// 监听数据变化，更新图表
watch(() => data.stats, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// 监听筛选条件变化，重新加载数据
watch(() => filters, (newFilters, oldFilters) => {
  // 检测是否有实际变化，避免不必要的请求
  if (JSON.stringify(newFilters) !== JSON.stringify(oldFilters)) {
    console.log(`${props.title}: 筛选条件发生变化，重置isDataLoaded状态`);
    isDataLoaded.value = false;
    fetchData();
  }
}, { deep: true })

// 监听customParams变化，同步到本地filters但不自动加载数据
watch(() => props.customParams, (newParams, oldParams) => {
  if (newParams) {
    console.log(`${props.title}: 同步外部筛选条件`, newParams);
    // 检查是否有实际变化
    let hasChanged = false;
    
    // 更新筛选条件
    if (newParams.range !== undefined && newParams.range !== filters.range) {
      filters.range = newParams.range;
      hasChanged = true;
    }
    if (newParams.reviewStatus !== undefined && newParams.reviewStatus !== filters.reviewStatus) {
      filters.reviewStatus = newParams.reviewStatus;
      hasChanged = true;
    }
    
    // 如果筛选条件发生变化，重置数据加载状态
    if (hasChanged) {
      console.log(`${props.title}: 外部筛选条件有变化，重置isDataLoaded状态`);
      isDataLoaded.value = false;
    }
    // 注意：此处不再自动调用fetchData，由父组件控制加载时机
  }
}, { deep: true })

// 组件挂载时同步筛选条件并初始化
onMounted(() => {
  // 初始化同步外部传入的筛选条件
  if (props.customParams) {
    if (props.customParams.range !== undefined) {
      filters.range = props.customParams.range;
    }
    if (props.customParams.reviewStatus !== undefined) {
      filters.reviewStatus = props.customParams.reviewStatus;
    }
  }
  
  // 仅当shouldAutoLoad为true时自动加载数据
  if (props.shouldAutoLoad) {
    fetchData()
  }
  
  nextTick(() => {
    initChart()
    window.addEventListener('resize', handleResize)
  })
})

// 向外部暴露方法
defineExpose({
  // 重新加载数据的方法，可传递参数控制是否强制刷新
  reloadData,
  // 重新调整图表大小的方法
  resizeChart: handleResize,
  // 暴露数据加载状态
  isDataLoaded
})

// 组件卸载时移除事件监听
const onUnmounted = () => {
  window.removeEventListener('resize', handleResize)
  if (chart) {
    chart.dispose()
    chart = null
  }
}
</script> 

<style scoped>
.chart-container {
  height: 300px;
  width: 100%;
  margin: 0 auto;
}

.time-interval {
  margin-bottom: 16px;
  text-align: left;
  font-size: 13px;
}
</style> 