const express = require('express');
const highLevelPaperParticipantsController = require('../../../controllers/v1/highLevelPapers/highLevelPaperParticipantsController');

const router = express.Router();

/**
 * @api {get} /v1/sys/highLevelPaperParticipants 获取高水平论文参与者列表
 * @apiName GetHighLevelPaperParticipants
 * @apiGroup 高水平论文参与者
 * @apiParam {String} [paperId] 论文ID
 * @apiParam {String} [userId] 用户ID
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 200,
 *     "message": "获取成功",
 *     "data": []
 *   }
 */
router.get('/highLevelPaperParticipants', highLevelPaperParticipantsController.getParticipants);

/**
 * @api {get} /v1/sys/highLevelPaperParticipants/:id 获取高水平论文参与者详情
 * @apiName GetHighLevelPaperParticipantById
 * @apiGroup 高水平论文参与者
 * @apiParam {String} id 参与者ID
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 200,
 *     "message": "获取成功",
 *     "data": {}
 *   }
 */
router.get('/highLevelPaperParticipants/:id', highLevelPaperParticipantsController.getParticipantById);

/**
 * @api {post} /v1/sys/highLevelPaperParticipants 添加高水平论文参与者
 * @apiName AddHighLevelPaperParticipant
 * @apiGroup 高水平论文参与者
 * @apiParam {String} paperId 论文ID
 * @apiParam {String} userId 用户ID
 * @apiParam {Number} [allocationRatio] 分配比例
 * @apiParam {Number} [authorRank] 作者排名
 * @apiParam {Boolean} [isFirstAuthor] 是否第一作者
 * @apiParam {Boolean} [isCorrespondingAuthor] 是否通讯作者
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 201 OK
 *   {
 *     "code": 200,
 *     "message": "添加成功",
 *     "data": {}
 *   }
 */
router.post('/highLevelPaperParticipants', highLevelPaperParticipantsController.addParticipant);

/**
 * @api {put} /v1/sys/highLevelPaperParticipants/:id 更新高水平论文参与者
 * @apiName UpdateHighLevelPaperParticipant
 * @apiGroup 高水平论文参与者
 * @apiParam {String} id 参与者ID
 * @apiParam {Number} [allocationRatio] 分配比例
 * @apiParam {Number} [authorRank] 作者排名
 * @apiParam {Boolean} [isFirstAuthor] 是否第一作者
 * @apiParam {Boolean} [isCorrespondingAuthor] 是否通讯作者
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 200,
 *     "message": "更新成功",
 *     "data": {}
 *   }
 */
router.put('/highLevelPaperParticipants/:id', highLevelPaperParticipantsController.updateParticipant);

/**
 * @api {delete} /v1/sys/highLevelPaperParticipants/:id 删除高水平论文参与者
 * @apiName DeleteHighLevelPaperParticipant
 * @apiGroup 高水平论文参与者
 * @apiParam {String} id 参与者ID
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 200,
 *     "message": "删除成功",
 *     "data": null
 *   }
 */
router.delete('/highLevelPaperParticipants/:id', highLevelPaperParticipantsController.deleteParticipant);

/**
 * @api {post} /v1/sys/highLevelPaperParticipants/batch 批量添加高水平论文参与者
 * @apiName BatchAddHighLevelPaperParticipants
 * @apiGroup 高水平论文参与者
 * @apiParam {String} paperId 论文ID
 * @apiParam {Array} participants 参与者数组
 * @apiParam {String} participants.userId 用户ID
 * @apiParam {Number} [participants.allocationRatio] 分配比例
 * @apiParam {Number} [participants.authorRank] 作者排名
 * @apiParam {Boolean} [participants.isFirstAuthor] 是否第一作者
 * @apiParam {Boolean} [participants.isCorrespondingAuthor] 是否通讯作者
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 201 OK
 *   {
 *     "code": 200,
 *     "message": "批量添加成功",
 *     "data": {
 *       "total": 0,
 *       "success": 0,
 *       "participants": []
 *     }
 *   }
 */
router.post('/highLevelPaperParticipants/batch', highLevelPaperParticipantsController.batchAddParticipants);

module.exports = router; 