import {useRouter} from 'vue-router'
import dbUtils from "./util.strotage";

/**
 * 检查路由对象是否具有权限
 * @param {Array} perms - 权限列表
 * @param {Object} route - 路由对象
 * @returns {boolean} - 是否具有权限
 */
function hasPermission(perms, route) {
    if (route.meta && route.meta.perms) {
        // 如果路由对象定义了 meta 属性或者定义 meta.perms 属性，那么就根据权限值来判断是否具有权限
        return perms.some(perm => route.meta.perms.includes(perm))
    } else {
        // 如果路由对象没有定义 meta 属性或者没有定义 meta.perms 属性，那么默认认为具有权限，返回 true。
        return true
    }
}

/**
 * 根据权限列表筛选异步路由配置
 * @param {Array} routes - 路由配置表
 * @param {Array} perms - 权限列表
 * @returns {Array} - 筛选后的路由配置
 */
function filterAsyncRouter(routes, perms) {
    try {
        if (!Array.isArray(routes) || routes.length === 0) {
            console.warn('路由列表为空或不是数组');
            return [];
        }
        
        if (!Array.isArray(perms) || perms.length === 0) {
            console.warn('权限列表为空或不是数组，使用默认权限');
            perms = ['index']; // 使用默认权限
        }
        
        const res = []
        
        // 首页始终可见
        const indexRoute = routes.find(route => route.path === '/index' || route.name === 'index');
        if (indexRoute && !indexRoute.hidden) {
            res.push({...indexRoute});
        }

        routes.forEach(route => {
            // 跳过已添加的首页
            if (route === indexRoute) {
                return;
            }
            
            // 创建临时变量 tmp，避免修改原始的路由对象
            const tmp = {...route}
            
            try {
                if (!tmp.hidden && tmp.children) {
                    // 先对子路由进行深度筛选，确保子路由也符合权限要求
                    tmp.children = filterAsyncRouter(tmp.children, perms)
                    if (tmp.children && tmp.children.length > 0) {
                        res.push(tmp)
                    }
                } else {
                    // 对于没有子路由的路由对象，直接进行权限判断
                    if (!tmp.hidden && hasPermission(perms, tmp)) {
                        res.push(tmp)
                    }
                }
            } catch (routeError) {
                console.error('处理路由时出错:', routeError, '路由:', tmp);
                // 错误时跳过此路由继续处理下一个
            }
        })

        return res
    } catch (error) {
        console.error('筛选路由时出错:', error);
        return [];
    }
}


export const menuList = function () {
    try {
        const asyncRoutes = useRouter().options.routes[0].children.filter(e => !e.hidden)
        
        //筛选路由表
        let permissionList = dbUtils.get('perms') || [];
        
        // 确保permissionList是数组
        if (!Array.isArray(permissionList)) {
            console.error('权限列表无效:', permissionList);
            // 使用默认权限 - 允许所有人访问首页
            permissionList = ['index'];
        }
        
        if (permissionList.length === 0) {
            console.warn('没有找到权限列表数据，使用基础权限');
            // 使用基础权限
            permissionList = ['index'];
        }
        
        console.log('当前用户权限:', permissionList);
        
        let accessedRouters;
        if (permissionList.includes('*')) {
            // 拥有所有权限
            accessedRouters = asyncRoutes;
            console.log('用户拥有所有权限，菜单项数量:', accessedRouters.length);
        } else {
            // 根据权限筛选路由
            accessedRouters = filterAsyncRouter(asyncRoutes, permissionList);
            console.log('根据权限筛选后的菜单项数量:', accessedRouters.length);
        }
        
        // 确保始终能访问首页
        if (accessedRouters.length === 0) {
            console.warn('筛选后无可访问菜单，添加首页作为默认菜单');
            // 找到首页路由
            const homeRoute = asyncRoutes.find(route => route.path === '/index' || route.name === 'index');
            if (homeRoute) {
                accessedRouters.push(homeRoute);
            }
        }
        
        return accessedRouters;
    } catch (error) {
        console.error('菜单列表生成出错:', error);
        // 发生错误时返回空数组，避免整个应用崩溃
        return [];
    }
}




