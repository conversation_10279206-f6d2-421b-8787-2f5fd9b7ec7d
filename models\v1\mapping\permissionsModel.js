const {DataTypes} = require('sequelize');
const sequelize = require('@config/dbConfig');
const Role = require('./roleModel');
const role_permissions = require('./role_permissions');
// 权限表
// https://www.sequelize.cn/core-concepts/model-instances
const Permissions = sequelize.define('permissions', // Sequelize模型名 没有提供表名 则以此为表名
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: 'ID',
        },
        name: {
            type: DataTypes.STRING,
            notEmpty: true,
            notNull: true,
            allowNull: false,
            comment: '权限名称',
        },
        key: {
            type: DataTypes.STRING,
            notEmpty: true,
            notNull: true,
            allowNull: false,
            comment: '权限键',
        },
        parent_key: {
            type: DataTypes.STRING,
            comment: '父级权限键（可选）',
        },
        auth: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
            comment: '是否是权限按钮',
        },
        status: {
            type: DataTypes.INTEGER,
            defaultValue: 1,  // 1 正常 0 停用
            comment: '状态',
        },
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
    });

// 添加静态方法: 转换为树状结构
Permissions.toTree = function (permissions) {
    const tree = [];
    const map = {};
    
    // 复制数据，防止直接修改原始数据
    permissions.forEach(permission => {
        // 创建一个新对象，而不是引用
        map[permission.dataValues.key] = { 
            ...permission.dataValues,
            children: [] // 预先初始化children数组
        };
    });
    
    permissions.forEach(permission => {
        const node = map[permission.dataValues.key];
        const parent = map[permission.dataValues.parent_key];
        
        if (parent) {
            // 不要直接推送对象引用，而是推送键值
            parent.children.push(node);
        } else {
            tree.push(node);
        }
    });
    
    // 清理：移除空的children数组
    const cleanEmptyChildren = (nodes) => {
        for (const node of nodes) {
            if (node.children && node.children.length === 0) {
                delete node.children;
            } else if (node.children) {
                cleanEmptyChildren(node.children);
            }
        }
    };
    
    cleanEmptyChildren(tree);
    return tree;
};

// 添加静态方法: 转换为扁平结构
Permissions.toFlat = function (permissions) {
    const flat = [];
    const flatten = (permission, parentKey = null) => {
        const flatPermission = { ...permission, parentKey };
        flat.push(flatPermission);

        permission.children.forEach(child => {
            flatten(child, permission.dataValues.key);
        });
    };

    permissions.forEach(permission => {
        flatten(permission);
    });

    return flat;
};

module.exports = Permissions;

