const express = require('express');
const router = express.Router();

/**
 * 获取教师绩效统计
 * @route GET /v1/sys/statistics/teacher
 * @group 统计管理 - 绩效统计相关接口
 * @param {number} page.query - 页码
 * @param {number} pageSize.query - 每页数量
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {page: 1, pageSize: 10, total: 0, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/teacher', (req, res) => {
  res.json({
    code: 200,
    message: '获取成功',
    data: {
      list: [],
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      }
    }
  });
});

/**
 * 获取院系绩效统计
 * @route GET /v1/sys/statistics/department
 * @group 统计管理 - 绩效统计相关接口
 * @param {number} page.query - 页码
 * @param {number} pageSize.query - 每页数量
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {page: 1, pageSize: 10, total: 0, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/department', (req, res) => {
  res.json({
    code: 200,
    message: '获取成功',
    data: {
      list: [],
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      }
    }
  });
});

/**
 * 获取年度绩效统计
 * @route GET /v1/sys/statistics/year
 * @group 统计管理 - 绩效统计相关接口
 * @param {number} page.query - 页码
 * @param {number} pageSize.query - 每页数量
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {page: 1, pageSize: 10, total: 0, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/year', (req, res) => {
  res.json({
    code: 200,
    message: '获取成功',
    data: {
      list: [],
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      }
    }
  });
});

/**
 * 导出教师绩效统计
 * @route GET /v1/sys/statistics/teacher/export
 * @group 统计管理 - 绩效统计相关接口
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/teacher/export', (req, res) => {
  res.json({
    code: 200,
    message: '导出成功',
    data: null
  });
});

/**
 * 导出院系绩效统计
 * @route GET /v1/sys/statistics/department/export
 * @group 统计管理 - 绩效统计相关接口
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/department/export', (req, res) => {
  res.json({
    code: 200,
    message: '导出成功',
    data: null
  });
});

/**
 * 导出年度绩效统计
 * @route GET /v1/sys/statistics/year/export
 * @group 统计管理 - 绩效统计相关接口
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/year/export', (req, res) => {
  res.json({
    code: 200,
    message: '导出成功',
    data: null
  });
});

module.exports = router; 