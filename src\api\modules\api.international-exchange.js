import request from '../server'

// 获取国际交流列表
export function getInternationalExchanges(params) {
  console.log('调用获取国际交流列表API:', params);
  try {
    // 打印处理后的参数
    const processedParams = { ...params };
    
    // 处理可能的数组参数，确保它们被正确序列化
    if (params && params.usernameList && Array.isArray(params.usernameList)) {
      processedParams.usernameList = params.usernameList.join(',');
      console.log('处理usernameList数组为逗号分隔字符串:', processedParams.usernameList);
    }
    
    console.log('处理后的请求参数:', processedParams);
    
    // 发起请求
    const result = request.get('/sys/international', processedParams);
    
    // 添加结果处理
    result.then(data => {
      console.log('获取国际交流列表API响应:', data);
      if (data && data.code !== 200) {
        console.error('获取国际交流列表API错误:', data.message || '未知错误');
      }
    }).catch(err => {
      console.error('获取国际交流列表API异常:', err);
    });
    
    return result;
  } catch (error) {
    console.error('调用获取国际交流列表API出错:', error);
    return Promise.reject(error);
  }
}

// 获取国际交流详情
export function getInternationalExchangeDetail(id) {
  console.log('调用获取国际交流详情API, ID:', id);
  return request.get(`/international/${id}`)
}

// 添加国际交流
export function addInternationalExchange(data) {
  console.log('调用添加国际交流API, 数据:', data);
  return request.post('/international', data)
}

// 更新国际交流
export function updateInternationalExchange(id, data) {
  console.log('调用更新国际交流API, ID:', id, '数据:', data);
  return request.put(`/international/${id}`, data)
}

// 删除国际交流
export function deleteInternationalExchange(id) {
  console.log('调用删除国际交流API, ID:', id);
  return request.delete(`/international/${id}`)
}

// 导入国际交流数据
export function importInternationalExchanges(file) {
  console.log('调用导入国际交流API, 文件:', file?.name);
  const formData = new FormData()
  formData.append('file', file)
  return request.post('/sys/international/import', formData, null, 'multipart/form-data')
}

// 导出国际交流数据
export function exportInternationalExchanges(params) {
  console.log('调用导出国际交流API, 原始参数:', params);
  // 移除空值参数
  const queryParams = {};
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      queryParams[key] = value;
    }
  });
  
  console.log('处理后的导出参数:', queryParams);
  
  // 使用模板字符串构建URL，确保参数正确传递
  let url = '/sys/international/export';
  const queryString = Object.entries(queryParams)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  
  if (queryString) {
    url += `?${queryString}`;
  }
  
  console.log('导出请求URL:', url);
  return request.get(url, { responseType: 'blob' });
}

// 获取个人国际交流统计数据
export function getPersonalExchangeStats(userId) {
  console.log('调用获取个人国际交流统计API, 用户ID:', userId);
  
  // 使用模板字符串直接构建URL
  const url = userId ? `/sys/international/personal-stats?userId=${encodeURIComponent(userId)}` : '/sys/international/personal-stats';
  console.log('个人统计请求URL:', url);
  
  // 添加完整URL日志
  const config = require('@/config').default;
  const fullUrl = `${config.apiBaseUrl}${url}`;
  console.log('完整API请求地址:', fullUrl);
  
  try {
    const result = request.get(url);
    
    // 添加结果处理
    result.then(data => {
      console.log('获取个人国际交流统计API响应:', data);
      if (data && data.code !== 200) {
        console.error('获取个人国际交流统计API错误:', data.message || '未知错误');
      }
    }).catch(err => {
      console.error('获取个人国际交流统计API异常:', err);
    });
    
    return result;
  } catch (error) {
    console.error('调用获取个人国际交流统计API出错:', error);
    return Promise.reject(error);
  }
}

// 获取用户列表
export function getUserList() {
  console.log('调用获取用户列表API');
  return request.get('/teacher/list')
} 