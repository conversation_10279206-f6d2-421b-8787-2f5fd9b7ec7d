const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const studentAwardGuidanceAwardLevelModel = require('./studentAwardGuidanceAwardLevelsModel');
const userModel = require('./userModel');

// 定义指导学生获奖主表模型
const StudentAwardGuidanceAward = sequelize.define('student_award_guidance_awards', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '奖项ID'
    },
    awardName: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '获奖名称/出国研究生姓名'
    },
    department: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '系/教研室'
    },
    awardDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '获奖时间/出国交流时间'
    },
    levelId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '级别ID'
    },
    remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注'
    },
    reviewerId: {
        type: DataTypes.CHAR(36),
        allowNull: true,
        comment: '审核人ID'
    },
    ifReviewer: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        comment: '审核状态（0，拒审核 1，审核，null未审核）'
      },
      attachmentUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '附件URL'
      },
      reviewComment: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '审核意见'
      },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'student_award_guidance_awards',
    timestamps: true,
    indexes: [
        {
            name: 'idx_award_date',
            fields: ['awardDate']
        },
        {
            name: 'idx_award_level',
            fields: ['levelId']
        },
        {
            name: 'idx_department',
            fields: ['department']
        }
    ]
});

// 建立与奖项级别的关联关系
StudentAwardGuidanceAward.belongsTo(studentAwardGuidanceAwardLevelModel, {
    foreignKey: 'levelId',
    as: 'level'
});

// 建立与审核人的关联关系
StudentAwardGuidanceAward.belongsTo(userModel, {
    foreignKey: 'reviewerId',
    as: 'reviewer'
});

// 建立与参与者的关联关系
StudentAwardGuidanceAward.hasMany(require('./studentAwardGuidanceParticipantsModel'), {
    foreignKey: 'awardId',
    as: 'participants'
});

module.exports = StudentAwardGuidanceAward; 