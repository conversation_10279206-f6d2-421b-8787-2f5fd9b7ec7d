import request from '../server'

/**
 * 获取用户参与的获奖记录列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getUserAwards(params) {
  return request.post('/studentAwardGuidanceAwards/award-projects', params)
}

/**
 * 获取所有获奖记录列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getAllAwards(params) {
  return request.post('/studentAwardGuidanceAwards/award-projects-all', params)
}

/**
 * 获取获奖列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getAwards(params) {
  return request.post('/studentAwardGuidanceAwards/awards/list', params)
}

/**
 * 获取获奖记录详情
 * @param {string} id - 获奖记录ID
 * @returns {Promise} - 请求结果
 */
export function getAwardDetail(id) {
  return request.post('/studentAwardGuidanceAwards/award/detail', { id })
}

/**
 * 创建获奖记录
 * @param {Object} data - 获奖记录数据
 * @returns {Promise} - 请求结果
 */
export function createAward(data) {
  return request.post('/studentAwardGuidanceAwards/award/create', data)
}

/**
 * 更新获奖记录
 * @param {Object} data - 更新数据，包含id字段
 * @returns {Promise} - 请求结果
 */
export function updateAward(data) {
  return request.post('/studentAwardGuidanceAwards/award/update', data)
}

/**
 * 删除获奖记录
 * @param {string} id - 获奖记录ID
 * @returns {Promise} - 请求结果
 */
export function deleteAward(id) {
  return request.delete(`/studentAwardGuidanceAwards/award/delete/${id}`)
}

/**
 * 导入获奖数据
 * @param {File} file - 文件对象
 * @returns {Promise} - 请求结果
 */
export function importAwards(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/studentAwardGuidanceAwards/awards/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出获奖数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function exportAwards(params) {
  return request.post('/studentAwardGuidanceAwards/awards/export', params)
}

/**
 * 审核获奖记录
 * @param {string} id - 获奖记录ID
 * @param {boolean} reviewStatus - 审核状态
 * @param {string} reviewComment - 审核意见
 * @param {string} reviewer - 审核人ID
 * @returns {Promise} - 请求结果
 */
export function reviewAward(data) {
  return request.post('/studentAwardGuidanceAwards/award/review', data)
}

/**
 * 获取获奖时间分布数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getTimeDistribution(params = {}) {
  return request.post('/studentAwardGuidanceAwards/statistics/time-distribution', params)
}

/**
 * 获取获奖级别分布数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getLevelDistribution(params = {}) {
  return request.post('/studentAwardGuidanceAwards/statistics/level-distribution', params)
}

/**
 * 获取教师获奖排名数据
 * @param {Object} params - 查询参数，包括range、reviewStatus和limit
 * @returns {Promise} - 请求结果
 */
export function getTeacherAwardRanking(params = {}) {
  return request.post('/studentAwardGuidanceAwards/statistics/teacher-ranking', params)
}

/**
 * 获取教师获奖详情
 * @param {Object} data - 查询参数，包括userId、page、pageSize等
 * @returns {Promise} - 请求结果
 */
export function getTeacherAwardDetails(data) {
  return request.post('/studentAwardGuidanceAwards/statistics/teacher-award-details', data)
}

/**
 * 获取获奖统计概览数据
 * @param {Object} params - 查询参数，可选包含userId
 * @returns {Promise} - 请求结果
 */
export function getAwardStatistics(params = {}) {
  return request.post('/studentAwardGuidanceAwards/statistics/overview', params)
}

/**
 * 获取获奖总分统计
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getAwardTotalScore(params) {
  return request.post('/studentAwardGuidanceAwards/statistics/awards-total-score', params);
}

/**
 * 获取用户获奖详情
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getUserAwardDetails(params) {
  return request.post('/studentAwardGuidanceAwards/user/details', params);
}

/**重新提交审核 */
export function reapplyReview(params) {
  return request.post('/studentAwardGuidanceAwards/award/reapply', params)
}

/**
 * 获取审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getReviewStatusOverview(params) {
  return request.post('/studentAwardGuidanceAwards/statistics/review-status-overview', params)
}
