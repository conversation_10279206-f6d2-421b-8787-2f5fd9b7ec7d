import service from '../server'

// ==================== 基础排名管理接口 ====================

/**
 * 获取总体排名列表
 * @param {Object} data 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'rank', order: 'ascend'}}
 * @returns {Promise} 包含排名列表的Promise对象
 */
export const getAllRankings = (data) => {
  return service.post('/ranking/all/list', data)
}

/**
 * 获取统计时间内排名列表
 * @param {Object} data 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'rank', order: 'ascend'}}
 * @returns {Promise} 包含排名列表的Promise对象
 */
export const getInRankings = (data) => {
  return service.post('/ranking/in/list', data)
}

/**
 * 获取统计时间外排名列表
 * @param {Object} data 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'rank', order: 'ascend'}}
 * @returns {Promise} 包含排名列表的Promise对象
 */
export const getOutRankings = (data) => {
  return service.post('/ranking/out/list', data)
}

/**
 * 获取单个总体排名详情
 * @param {number} id 排名ID
 * @returns {Promise} 包含排名详情的Promise对象
 */
export const getAllRankingDetail = (id) => {
  return service.post('/ranking/all/detail', { id })
}

/**
 * 获取单个校内排名详情
 * @param {number} id 排名ID
 * @returns {Promise} 包含排名详情的Promise对象
 */
export const getInRankingDetail = (id) => {
  return service.post('/ranking/in/detail', { id })
}

/**
 * 获取单个统计范围排名详情
 * @param {number} id 排名ID
 * @returns {Promise} 包含排名详情的Promise对象
 */
export const getOutRankingDetail = (id) => {
  return service.post('/ranking/out/detail', { id })
}

/**
 * 更新总体排名信息
 * @param {Object} data 更新数据 {id: number, ...updateData}
 * @returns {Promise} 包含更新结果的Promise对象
 */
export const updateAllRanking = (data) => {
  return service.post('/ranking/all/update', data)
}

/**
 * 更新统计范围内排名信息
 * @param {Object} data 更新数据 {id: number, ...updateData}
 * @returns {Promise} 包含更新结果的Promise对象
 */
export const updateInRanking = (data) => {
  return service.post('/ranking/in/update', data)
}

/**
 * 更新统计范围外排名信息
 * @param {Object} data 更新数据 {id: number, ...updateData}
 * @returns {Promise} 包含更新结果的Promise对象
 */
export const updateOutRanking = (data) => {
  return service.post('/ranking/out/update', data)
}

/**
 * 手动刷新排名数据
 * @param {string} type 排名类型 (all/in/out)
 * @returns {Promise} 包含刷新结果的Promise对象
 */
export const refreshRankings = (type) => {
  return service.post('/ranking/refresh', { type })
}

/**
 * 获取用户排名统计信息
 * @returns {Promise} 包含统计信息的Promise对象
 */
export const getRankingStats = () => {
  return service.post('/ranking/stats')
}

/**
 * 获取用户排名详情（按用户ID）
 * @param {string} userId 用户ID
 * @param {string} type 排名类型 (all/in/out)
 * @returns {Promise} 包含用户排名的Promise对象
 */
export const getUserRanking = (userId, type) => {
  return service.post('/ranking/user', { userId, type })
}

// ==================== 绩效分析接口 ====================

/**
 * 获取用户综合绩效雷达图数据
 * @param {string} userId 用户ID
 * @param {string} range 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {Promise} 包含雷达图数据的Promise对象
 */
export const getRadarData = (userId, range = 'all') => {
  return service.post('/ranking/radar', { userId, range })
}

/**
 * 获取用户绩效分布图数据
 * @param {string} userId 用户ID
 * @param {string} range 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {Promise} 包含分布图数据的Promise对象
 */
export const getDistributionData = (userId, range = 'all') => {
  return service.post('/ranking/distribution', { userId, range })
}

/**
 * 获取排名趋势对比数据
 * @param {string} userId 用户ID
 * @returns {Promise} 包含排名趋势数据的Promise对象
 */
export const getRankingTrend = (userId) => {
  return service.post('/ranking/trend', { userId })
}

/**
 * 获取部门绩效对比数据
 * @param {string} range 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {Promise} 包含部门对比数据的Promise对象
 */
export const getDepartmentComparison = (range = 'all') => {
  return service.post('/ranking/department-comparison', { range })
}

/**
 * 获取用户绩效分析报告
 * @param {string} userId 用户ID
 * @param {string} range 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {Promise} 包含分析报告数据的Promise对象
 */
export const getAnalysisReport = (userId, range = 'all') => {
  return service.post('/ranking/analysis-report', { userId, range })
}

// ==================== 便捷方法 ====================

/**
 * 获取用户完整绩效分析数据（包含雷达图、分布图、趋势图）
 * @param {string} userId 用户ID
 * @param {string} range 统计范围筛选
 * @returns {Promise} 包含完整分析数据的Promise对象
 */
export const getUserCompleteAnalysis = async (userId, range = 'all') => {
  try {
    const [radarData, distributionData, trendData, reportData] = await Promise.all([
      getRadarData(userId, range),
      getDistributionData(userId, range),
      getRankingTrend(userId),
      getAnalysisReport(userId, range)
    ])

    return {
      radar: radarData,
      distribution: distributionData,
      trend: trendData,
      report: reportData,
      userId,
      range,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('获取用户完整绩效分析数据失败:', error)
    throw error
  }
}

/**
 * 获取多个用户的雷达图对比数据
 * @param {Array<string>} userIds 用户ID数组
 * @param {string} range 统计范围筛选
 * @returns {Promise} 包含对比数据的Promise对象
 */
export const getUsersRadarComparison = async (userIds, range = 'all') => {
  try {
    const radarDataList = await Promise.all(
      userIds.map(userId => getRadarData(userId, range))
    )

    return {
      users: radarDataList,
      range,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('获取用户雷达图对比数据失败:', error)
    throw error
  }
}
