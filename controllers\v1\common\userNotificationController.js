/**
 * 用户个人通知控制器
 * 提供用户查看个人通知的接口
 */

const { authMiddleware } = require('../../../middleware/authMiddleware');
const apiResponse = require('../../../utils/apiResponse');
const notificationModel = require('../../../models/v1/mapping/notificationsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const { Op } = require('sequelize');

/**
 * 获取用户个人通知列表
 */
exports.getUserNotifications = [
    authMiddleware,
    async (req, res) => {
        try {
            const userId = req.user.id;
            const { page = 1, pageSize = 10, type, isRead } = req.query;
            
            // 构建查询条件
            const whereCondition = {
                [Op.or]: [
                    // 全员通知
                    { sendMode: 'all' },
                    // 个人通知
                    {
                        sendMode: 'single',
                        userId: userId
                    }
                ]
            };

            // 添加类型过滤
            if (type) {
                whereCondition.type = type;
            }

            // 添加状态过滤（使用status字段）
            whereCondition.status = 1; // 只查询正常状态的通知
            
            // 分页参数
            const offset = (page - 1) * pageSize;
            const limit = parseInt(pageSize);
            
            // 查询通知列表
            const { count, rows } = await notificationModel.findAndCountAll({
                where: whereCondition,
                include: [
                    {
                        model: userModel,
                        as: 'initiator',
                        attributes: ['id', 'nickname', 'username'],
                        required: false
                    }
                ],
                order: [['createdAt', 'DESC']],
                offset,
                limit
            });
            
            // 格式化返回数据
            const notifications = rows.map(notification => ({
                id: notification.id,
                type: notification.type,
                title: notification.title,
                content: notification.content,
                abstract: notification.abstract,
                sendMode: notification.sendMode,
                isRead: false, // 暂时设为false，后续可以添加已读状态字段
                createdAt: notification.createdAt,
                updatedAt: notification.updatedAt,
                initiator: notification.initiator ? {
                    id: notification.initiator.id,
                    nickname: notification.initiator.nickname,
                    username: notification.initiator.username
                } : null
            }));
            
            return apiResponse.successResponseWithData(res, "获取通知列表成功", {
                list: notifications,
                pagination: {
                    page: parseInt(page),
                    pageSize: limit,
                    total: count
                }
            });
            
        } catch (error) {
            console.error('获取用户通知列表失败:', error);
            return apiResponse.ErrorResponse(res, "获取通知列表失败");
        }
    }
];

/**
 * 标记通知为已读（暂时返回成功，后续可扩展）
 */
exports.markNotificationAsRead = [
    authMiddleware,
    async (req, res) => {
        try {
            const userId = req.user.id;
            const { notificationId } = req.body;

            if (!notificationId) {
                return apiResponse.validationErrorWithData(res, "通知ID不能为空");
            }

            // 查找通知
            const notification = await notificationModel.findOne({
                where: {
                    id: notificationId,
                    [Op.or]: [
                        { sendMode: 'all' },
                        {
                            sendMode: 'single',
                            userId: userId
                        }
                    ],
                    status: 1
                }
            });

            if (!notification) {
                return apiResponse.notFoundResponse(res, "通知不存在或无权访问");
            }

            // 暂时直接返回成功，后续可以添加已读状态记录表
            return apiResponse.successResponse(res, "通知已标记为已读");

        } catch (error) {
            console.error('标记通知已读失败:', error);
            return apiResponse.ErrorResponse(res, "标记已读失败");
        }
    }
];

/**
 * 获取未读通知数量（暂时返回总数量）
 */
exports.getUnreadNotificationCount = [
    authMiddleware,
    async (req, res) => {
        try {
            const userId = req.user.id;

            // 查询通知总数量（暂时作为未读数量）
            const totalCount = await notificationModel.count({
                where: {
                    [Op.or]: [
                        { sendMode: 'all' },
                        {
                            sendMode: 'single',
                            userId: userId
                        }
                    ],
                    status: 1
                }
            });

            return apiResponse.successResponseWithData(res, "获取未读通知数量成功", {
                count: totalCount
            });

        } catch (error) {
            console.error('获取未读通知数量失败:', error);
            return apiResponse.ErrorResponse(res, "获取未读通知数量失败");
        }
    }
];

/**
 * 获取通知详情
 */
exports.getNotificationDetail = [
    authMiddleware,
    async (req, res) => {
        try {
            const userId = req.user.id;
            const { id } = req.params;
            
            if (!id) {
                return apiResponse.validationErrorWithData(res, "通知ID不能为空");
            }
            
            // 查找通知详情
            const notification = await notificationModel.findOne({
                where: {
                    id,
                    [Op.or]: [
                        { sendMode: 'all' },
                        {
                            sendMode: 'single',
                            userId: userId
                        }
                    ],
                    status: 1
                },
                include: [
                    {
                        model: userModel,
                        as: 'initiator',
                        attributes: ['id', 'nickname', 'username'],
                        required: false
                    }
                ]
            });
            
            if (!notification) {
                return apiResponse.notFoundResponse(res, "通知不存在或无权访问");
            }
            
            // 格式化返回数据
            const notificationDetail = {
                id: notification.id,
                type: notification.type,
                title: notification.title,
                content: notification.content,
                abstract: notification.abstract,
                sendMode: notification.sendMode,
                isRead: true, // 查看详情后自动标记为已读
                createdAt: notification.createdAt,
                updatedAt: notification.updatedAt,
                initiator: notification.initiator ? {
                    id: notification.initiator.id,
                    nickname: notification.initiator.nickname,
                    username: notification.initiator.username
                } : null
            };
            
            return apiResponse.successResponseWithData(res, "获取通知详情成功", notificationDetail);
            
        } catch (error) {
            console.error('获取通知详情失败:', error);
            return apiResponse.ErrorResponse(res, "获取通知详情失败");
        }
    }
];

/**
 * 批量标记通知为已读
 */
exports.batchMarkAsRead = [
    authMiddleware,
    async (req, res) => {
        try {
            const userId = req.user.id;
            const { notificationIds } = req.body;
            
            if (!notificationIds || !Array.isArray(notificationIds) || notificationIds.length === 0) {
                return apiResponse.validationErrorWithData(res, "通知ID列表不能为空");
            }
            
            // 暂时只验证通知是否存在，不实际更新状态
            const validNotifications = await notificationModel.count({
                where: {
                    id: {
                        [Op.in]: notificationIds
                    },
                    [Op.or]: [
                        { sendMode: 'all' },
                        {
                            sendMode: 'single',
                            userId: userId
                        }
                    ],
                    status: 1
                }
            });

            const updatedCount = validNotifications;
            
            return apiResponse.successResponseWithData(res, "批量标记已读成功", {
                updatedCount
            });
            
        } catch (error) {
            console.error('批量标记通知已读失败:', error);
            return apiResponse.ErrorResponse(res, "批量标记已读失败");
        }
    }
];
