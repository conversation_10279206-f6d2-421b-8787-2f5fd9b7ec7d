<template>
  <div class="data-visualization">
    <a-card title="数据可视化" :bordered="false">
      <a-alert
        message="数据可视化说明"
        description="本页面用于生成各类数据报表和图表。您可以自定义报表模板、选择数据维度、设置展示方式，并支持导出多种格式。"
        type="info"
        show-icon
        class="mb-4"
      />

      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="templates" tab="报表模板">
          <div class="template-header">
            <a-space>
              <a-button type="primary" @click="handleCreateTemplate">
                <template #icon><PlusOutlined /></template>
                新建模板
              </a-button>
              <a-button @click="handleImportTemplate">
                <template #icon><ImportOutlined /></template>
                导入模板
              </a-button>
            </a-space>
          </div>

          <a-table :columns="templateColumns" :data-source="templateData" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="record.status === 'active' ? 'green' : 'red'">
                  {{ record.status === 'active' ? '启用' : '禁用' }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" @click="handleEditTemplate(record)">编辑</a-button>
                  <a-button type="link" @click="handlePreviewTemplate(record)">预览</a-button>
                  <a-button type="link" @click="handleToggleTemplate(record)">
                    {{ record.status === 'active' ? '禁用' : '启用' }}
                  </a-button>
                  <a-button type="link" danger @click="handleDeleteTemplate(record)">删除</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="reports" tab="报表生成">
          <a-form layout="inline" :model="reportForm" class="search-form">
            <a-form-item label="报表类型">
              <a-select v-model:value="reportForm.type" placeholder="请选择报表类型" style="width: 200px">
                <a-select-option value="performance">绩效报表</a-select-option>
                <a-select-option value="research">科研报表</a-select-option>
                <a-select-option value="teaching">教学报表</a-select-option>
                <a-select-option value="social">社会服务报表</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="时间范围">
              <a-range-picker v-model:value="reportForm.dateRange" />
            </a-form-item>
            <a-form-item label="院系">
              <a-select v-model:value="reportForm.department" placeholder="请选择院系" style="width: 200px">
                <a-select-option value="all">全部院系</a-select-option>
                <a-select-option value="computer">计算机学院</a-select-option>
                <a-select-option value="economics">经济学院</a-select-option>
                <a-select-option value="medical">医学院</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleGenerateReport">
                  <template #icon><FileSearchOutlined /></template>
                  生成报表
                </a-button>
                <a-button @click="handleResetForm">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>

          <a-card title="报表预览" :bordered="false" class="mt-4">
            <template #extra>
              <a-space>
                <a-button type="primary" @click="handleExportPDF">
                  <template #icon><FilePdfOutlined /></template>
                  导出PDF
                </a-button>
                <a-button type="primary" @click="handleExportExcel">
                  <template #icon><FileExcelOutlined /></template>
                  导出Excel
                </a-button>
                <a-button type="primary" @click="handleExportWord">
                  <template #icon><FileWordOutlined /></template>
                  导出Word
                </a-button>
              </a-space>
            </template>
            <div ref="reportPreviewRef" class="report-preview"></div>
          </a-card>
        </a-tab-pane>

        <a-tab-pane key="charts" tab="图表生成">
          <a-form layout="inline" :model="chartForm" class="search-form">
            <a-form-item label="图表类型">
              <a-select v-model:value="chartForm.type" placeholder="请选择图表类型" style="width: 200px">
                <a-select-option value="bar">柱状图</a-select-option>
                <a-select-option value="line">折线图</a-select-option>
                <a-select-option value="pie">饼图</a-select-option>
                <a-select-option value="radar">雷达图</a-select-option>
                <a-select-option value="scatter">散点图</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="数据维度">
              <a-select v-model:value="chartForm.dimension" placeholder="请选择数据维度" style="width: 200px">
                <a-select-option value="department">院系维度</a-select-option>
                <a-select-option value="title">职称维度</a-select-option>
                <a-select-option value="age">年龄维度</a-select-option>
                <a-select-option value="year">年度维度</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="指标选择">
              <a-select v-model:value="chartForm.metrics" mode="multiple" placeholder="请选择指标" style="width: 300px">
                <a-select-option value="research">科研项目数</a-select-option>
                <a-select-option value="teaching">教学获奖数</a-select-option>
                <a-select-option value="international">国际交流次数</a-select-option>
                <a-select-option value="social">社会服务次数</a-select-option>
                <a-select-option value="score">绩效得分</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleGenerateChart">
                  <template #icon><AreaChartOutlined /></template>
                  生成图表
                </a-button>
                <a-button @click="handleResetChartForm">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>

          <a-card title="图表预览" :bordered="false" class="mt-4">
            <template #extra>
              <a-space>
                <a-button type="primary" @click="handleExportChart">
                  <template #icon><DownloadOutlined /></template>
                  导出图表
                </a-button>
                <a-button type="primary" @click="handleSaveChart">
                  <template #icon><SaveOutlined /></template>
                  保存图表
                </a-button>
              </a-space>
            </template>
            <div ref="chartPreviewRef" class="chart-preview"></div>
          </a-card>
        </a-tab-pane>

        <a-tab-pane key="dashboard" tab="仪表盘">
          <div class="dashboard-header">
            <a-space>
              <a-button type="primary" @click="handleCreateDashboard">
                <template #icon><PlusOutlined /></template>
                新建仪表盘
              </a-button>
              <a-button @click="handleImportDashboard">
                <template #icon><ImportOutlined /></template>
                导入仪表盘
              </a-button>
            </a-space>
          </div>

          <a-row :gutter="16" class="mt-4">
            <a-col :span="6" v-for="dashboard in dashboardList" :key="dashboard.id">
              <a-card :title="dashboard.name" :bordered="false">
                <template #extra>
                  <a-dropdown>
                    <a-button type="link">
                      <EllipsisOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item key="edit" @click="handleEditDashboard(dashboard)">
                          <EditOutlined />编辑
                        </a-menu-item>
                        <a-menu-item key="share" @click="handleShareDashboard(dashboard)">
                          <ShareAltOutlined />分享
                        </a-menu-item>
                        <a-menu-item key="delete" @click="handleDeleteDashboard(dashboard)">
                          <DeleteOutlined />删除
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </template>
                <div class="dashboard-preview">
                  <img :src="dashboard.preview" :alt="dashboard.name" />
                </div>
                <div class="dashboard-info">
                  <span>创建时间：{{ dashboard.createTime }}</span>
                  <span>更新时间：{{ dashboard.updateTime }}</span>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 模板编辑弹窗 -->
    <a-modal
      v-model:visible="templateModalVisible"
      :title="templateModalTitle"
      width="800px"
      @ok="handleTemplateModalOk"
      @cancel="handleTemplateModalCancel"
    >
      <a-form :model="templateForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="模板名称" required>
          <a-input v-model:value="templateForm.name" placeholder="请输入模板名称" />
        </a-form-item>
        <a-form-item label="模板类型" required>
          <a-select v-model:value="templateForm.type" placeholder="请选择模板类型">
            <a-select-option value="performance">绩效报表</a-select-option>
            <a-select-option value="research">科研报表</a-select-option>
            <a-select-option value="teaching">教学报表</a-select-option>
            <a-select-option value="social">社会服务报表</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="模板描述">
          <a-textarea v-model:value="templateForm.description" placeholder="请输入模板描述" :rows="4" />
        </a-form-item>
        <a-form-item label="报表布局">
          <div class="template-layout">
            <div class="layout-item" v-for="(item, index) in templateForm.layout" :key="index">
              <div class="layout-header">
                <span>模块 {{ index + 1 }}</span>
                <a-space>
                  <a-button type="link" @click="handleMoveUp(index)" :disabled="index === 0">
                    <UpOutlined />
                  </a-button>
                  <a-button type="link" @click="handleMoveDown(index)" :disabled="index === templateForm.layout.length - 1">
                    <DownOutlined />
                  </a-button>
                  <a-button type="link" danger @click="handleRemoveLayout(index)">
                    <DeleteOutlined />
                  </a-button>
                </a-space>
              </div>
              <div class="layout-content">
                <a-form-item label="模块类型">
                  <a-select v-model:value="item.type" placeholder="请选择模块类型">
                    <a-select-option value="table">表格</a-select-option>
                    <a-select-option value="chart">图表</a-select-option>
                    <a-select-option value="text">文本</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="数据源">
                  <a-select v-model:value="item.dataSource" placeholder="请选择数据源">
                    <a-select-option value="performance">绩效数据</a-select-option>
                    <a-select-option value="research">科研数据</a-select-option>
                    <a-select-option value="teaching">教学数据</a-select-option>
                    <a-select-option value="social">社会服务数据</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="展示方式">
                  <a-select v-model:value="item.display" placeholder="请选择展示方式">
                    <a-select-option value="bar">柱状图</a-select-option>
                    <a-select-option value="line">折线图</a-select-option>
                    <a-select-option value="pie">饼图</a-select-option>
                    <a-select-option value="table">表格</a-select-option>
                  </a-select>
                </a-form-item>
              </div>
            </div>
            <a-button type="dashed" block @click="handleAddLayout">
              <PlusOutlined />添加模块
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 仪表盘编辑弹窗 -->
    <a-modal
      v-model:visible="dashboardModalVisible"
      :title="dashboardModalTitle"
      width="800px"
      @ok="handleDashboardModalOk"
      @cancel="handleDashboardModalCancel"
    >
      <a-form :model="dashboardForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="仪表盘名称" required>
          <a-input v-model:value="dashboardForm.name" placeholder="请输入仪表盘名称" />
        </a-form-item>
        <a-form-item label="仪表盘描述">
          <a-textarea v-model:value="dashboardForm.description" placeholder="请输入仪表盘描述" :rows="4" />
        </a-form-item>
        <a-form-item label="布局设置">
          <a-radio-group v-model:value="dashboardForm.layout">
            <a-radio value="grid">网格布局</a-radio>
            <a-radio value="flex">弹性布局</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="图表列表">
          <div class="chart-list">
            <div class="chart-item" v-for="(chart, index) in dashboardForm.charts" :key="index">
              <div class="chart-header">
                <span>图表 {{ index + 1 }}</span>
                <a-space>
                  <a-button type="link" @click="handleMoveChartUp(index)" :disabled="index === 0">
                    <UpOutlined />
                  </a-button>
                  <a-button type="link" @click="handleMoveChartDown(index)" :disabled="index === dashboardForm.charts.length - 1">
                    <DownOutlined />
                  </a-button>
                  <a-button type="link" danger @click="handleRemoveChart(index)">
                    <DeleteOutlined />
                  </a-button>
                </a-space>
              </div>
              <div class="chart-content">
                <a-form-item label="图表类型">
                  <a-select v-model:value="chart.type" placeholder="请选择图表类型">
                    <a-select-option value="bar">柱状图</a-select-option>
                    <a-select-option value="line">折线图</a-select-option>
                    <a-select-option value="pie">饼图</a-select-option>
                    <a-select-option value="radar">雷达图</a-select-option>
                    <a-select-option value="scatter">散点图</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="数据源">
                  <a-select v-model:value="chart.dataSource" placeholder="请选择数据源">
                    <a-select-option value="performance">绩效数据</a-select-option>
                    <a-select-option value="research">科研数据</a-select-option>
                    <a-select-option value="teaching">教学数据</a-select-option>
                    <a-select-option value="social">社会服务数据</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="数据维度">
                  <a-select v-model:value="chart.dimension" placeholder="请选择数据维度">
                    <a-select-option value="department">院系维度</a-select-option>
                    <a-select-option value="title">职称维度</a-select-option>
                    <a-select-option value="age">年龄维度</a-select-option>
                    <a-select-option value="year">年度维度</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="指标选择">
                  <a-select v-model:value="chart.metrics" mode="multiple" placeholder="请选择指标">
                    <a-select-option value="research">科研项目数</a-select-option>
                    <a-select-option value="teaching">教学获奖数</a-select-option>
                    <a-select-option value="international">国际交流次数</a-select-option>
                    <a-select-option value="social">社会服务次数</a-select-option>
                    <a-select-option value="score">绩效得分</a-select-option>
                  </a-select>
                </a-form-item>
              </div>
            </div>
            <a-button type="dashed" block @click="handleAddChart">
              <PlusOutlined />添加图表
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  PlusOutlined,
  ImportOutlined,
  FileSearchOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  AreaChartOutlined,
  DownloadOutlined,
  SaveOutlined,
  EditOutlined,
  ShareAltOutlined,
  DeleteOutlined,
  EllipsisOutlined,
  UpOutlined,
  DownOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'

// 当前激活的标签页
const activeTab = ref('templates')

// 报表模板相关
const templateColumns = [
  { title: '模板名称', dataIndex: 'name', key: 'name' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', key: 'action' }
]

const templateData = ref([
  {
    id: 1,
    name: '教师绩效报表',
    type: '绩效报表',
    createTime: '2024-03-15',
    status: 'active'
  },
  {
    id: 2,
    name: '科研项目报表',
    type: '科研报表',
    createTime: '2024-03-14',
    status: 'active'
  },
  {
    id: 3,
    name: '教学成果报表',
    type: '教学报表',
    createTime: '2024-03-13',
    status: 'inactive'
  }
])

// 报表表单
const reportForm = reactive({
  type: undefined,
  dateRange: [],
  department: undefined
})

// 图表表单
const chartForm = reactive({
  type: undefined,
  dimension: undefined,
  metrics: []
})

// 仪表盘列表
const dashboardList = ref([
  {
    id: 1,
    name: '教师绩效概览',
    preview: 'https://via.placeholder.com/300x200',
    createTime: '2024-03-15',
    updateTime: '2024-03-15'
  },
  {
    id: 2,
    name: '科研项目分析',
    preview: 'https://via.placeholder.com/300x200',
    createTime: '2024-03-14',
    updateTime: '2024-03-14'
  },
  {
    id: 3,
    name: '教学成果展示',
    preview: 'https://via.placeholder.com/300x200',
    createTime: '2024-03-13',
    updateTime: '2024-03-13'
  }
])

// 模板编辑弹窗
const templateModalVisible = ref(false)
const templateModalTitle = ref('新建模板')
const templateForm = reactive({
  name: '',
  type: undefined,
  description: '',
  layout: []
})

// 仪表盘编辑弹窗
const dashboardModalVisible = ref(false)
const dashboardModalTitle = ref('新建仪表盘')
const dashboardForm = reactive({
  name: '',
  description: '',
  layout: 'grid',
  charts: []
})

// 图表预览引用
const chartPreviewRef = ref(null)
const reportPreviewRef = ref(null)

// 初始化图表
const initChart = () => {
  if (!chartPreviewRef.value) return
  
  nextTick(() => {
    const chart = echarts.init(chartPreviewRef.value)
    let option = {}

    switch (chartForm.type) {
      case 'bar':
        option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: chartForm.metrics
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: ['计算机学院', '经济学院', '医学院', '其他院系']
          },
          yAxis: {
            type: 'value'
          },
          series: chartForm.metrics.map(metric => ({
            name: metric,
            type: 'bar',
            data: [120, 200, 150, 80]
          }))
        }
        break
      case 'line':
        option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: chartForm.metrics
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['1月', '2月', '3月', '4月', '5月', '6月']
          },
          yAxis: {
            type: 'value'
          },
          series: chartForm.metrics.map(metric => ({
            name: metric,
            type: 'line',
            data: [120, 132, 101, 134, 90, 230]
          }))
        }
        break
      case 'pie':
        option = {
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            left: 10,
            bottom: 10
          },
          series: [
            {
              name: '数据分布',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 1048, name: '计算机学院' },
                { value: 735, name: '经济学院' },
                { value: 580, name: '医学院' },
                { value: 484, name: '其他院系' }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
        break
      case 'radar':
        option = {
          tooltip: {},
          legend: {
            data: ['计算机学院', '经济学院', '医学院']
          },
          radar: {
            indicator: [
              { name: '科研项目', max: 100 },
              { name: '教学成果', max: 100 },
              { name: '国际交流', max: 100 },
              { name: '社会服务', max: 100 },
              { name: '就业质量', max: 100 }
            ]
          },
          series: [{
            name: '院系对比',
            type: 'radar',
            data: [
              {
                value: [80, 90, 70, 85, 75],
                name: '计算机学院'
              },
              {
                value: [70, 85, 80, 75, 80],
                name: '经济学院'
              },
              {
                value: [85, 75, 85, 80, 70],
                name: '医学院'
              }
            ]
          }]
        }
        break
      case 'scatter':
        option = {
          tooltip: {},
          legend: {
            data: ['计算机学院', '经济学院', '医学院']
          },
          xAxis: {
            type: 'value',
            name: '科研项目数'
          },
          yAxis: {
            type: 'value',
            name: '教学获奖数'
          },
          series: [
            {
              name: '计算机学院',
              type: 'scatter',
              data: [[10, 8], [15, 12], [20, 15]]
            },
            {
              name: '经济学院',
              type: 'scatter',
              data: [[8, 10], [12, 15], [15, 18]]
            },
            {
              name: '医学院',
              type: 'scatter',
              data: [[12, 6], [18, 10], [22, 12]]
            }
          ]
        }
        break
    }

    chart.setOption(option)
    
    // 添加窗口大小改变时的自适应
    window.addEventListener('resize', () => {
      chart.resize()
    })
  })
}

// 组件挂载后初始化
onMounted(() => {
  nextTick(() => {
    // 初始化报表预览
    if (reportPreviewRef.value) {
      initReportPreview()
    }

    // 初始化图表预览
    if (chartPreviewRef.value) {
      initChart()
    }
  })
})

// 初始化报表预览
const initReportPreview = () => {
  if (!reportPreviewRef.value) return
  
  nextTick(() => {
    const reportPreview = document.createElement('div')
    reportPreview.innerHTML = `
      <div style="padding: 20px;">
        <h2 style="text-align: center;">教师绩效报表</h2>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr>
              <th style="border: 1px solid #ddd; padding: 8px;">院系</th>
              <th style="border: 1px solid #ddd; padding: 8px;">教师人数</th>
              <th style="border: 1px solid #ddd; padding: 8px;">平均绩效分</th>
              <th style="border: 1px solid #ddd; padding: 8px;">最高绩效分</th>
              <th style="border: 1px solid #ddd; padding: 8px;">最低绩效分</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">计算机学院</td>
              <td style="border: 1px solid #ddd; padding: 8px;">120</td>
              <td style="border: 1px solid #ddd; padding: 8px;">85.5</td>
              <td style="border: 1px solid #ddd; padding: 8px;">95.2</td>
              <td style="border: 1px solid #ddd; padding: 8px;">75.8</td>
            </tr>
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">经济学院</td>
              <td style="border: 1px solid #ddd; padding: 8px;">150</td>
              <td style="border: 1px solid #ddd; padding: 8px;">82.3</td>
              <td style="border: 1px solid #ddd; padding: 8px;">93.7</td>
              <td style="border: 1px solid #ddd; padding: 8px;">70.5</td>
            </tr>
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">医学院</td>
              <td style="border: 1px solid #ddd; padding: 8px;">180</td>
              <td style="border: 1px solid #ddd; padding: 8px;">88.7</td>
              <td style="border: 1px solid #ddd; padding: 8px;">97.1</td>
              <td style="border: 1px solid #ddd; padding: 8px;">78.2</td>
            </tr>
          </tbody>
        </table>
      </div>
    `
    reportPreviewRef.value.appendChild(reportPreview)
  })
}

// 模板相关方法
const handleCreateTemplate = () => {
  templateModalTitle.value = '新建模板'
  templateForm.name = ''
  templateForm.type = undefined
  templateForm.description = ''
  templateForm.layout = []
  templateModalVisible.value = true
}

const handleEditTemplate = (record) => {
  templateModalTitle.value = '编辑模板'
  templateForm.name = record.name
  templateForm.type = record.type
  templateForm.description = record.description || ''
  templateForm.layout = record.layout || []
  templateModalVisible.value = true
}

const handlePreviewTemplate = (record) => {
  message.info(`预览模板：${record.name}`)
}

const handleToggleTemplate = (record) => {
  record.status = record.status === 'active' ? 'inactive' : 'active'
  message.success(`${record.status === 'active' ? '启用' : '禁用'}成功`)
}

const handleDeleteTemplate = (record) => {
  message.success(`删除模板：${record.name}`)
}

const handleImportTemplate = () => {
  message.info('导入模板')
}

const handleTemplateModalOk = () => {
  message.success('保存成功')
  templateModalVisible.value = false
}

const handleTemplateModalCancel = () => {
  templateModalVisible.value = false
}

const handleAddLayout = () => {
  templateForm.layout.push({
    type: undefined,
    dataSource: undefined,
    display: undefined
  })
}

const handleMoveUp = (index) => {
  if (index > 0) {
    const temp = templateForm.layout[index]
    templateForm.layout[index] = templateForm.layout[index - 1]
    templateForm.layout[index - 1] = temp
  }
}

const handleMoveDown = (index) => {
  if (index < templateForm.layout.length - 1) {
    const temp = templateForm.layout[index]
    templateForm.layout[index] = templateForm.layout[index + 1]
    templateForm.layout[index + 1] = temp
  }
}

const handleRemoveLayout = (index) => {
  templateForm.layout.splice(index, 1)
}

// 报表相关方法
const handleGenerateReport = () => {
  if (!reportForm.type) {
    message.warning('请选择报表类型')
    return
  }
  if (!reportForm.dateRange || reportForm.dateRange.length === 0) {
    message.warning('请选择时间范围')
    return
  }
  if (!reportForm.department) {
    message.warning('请选择院系')
    return
  }
  message.success('报表生成成功')
  initReportPreview()
}

const handleResetForm = () => {
  reportForm.type = undefined
  reportForm.dateRange = []
  reportForm.department = undefined
}

const handleExportPDF = () => {
  message.success('导出PDF成功')
}

const handleExportExcel = () => {
  message.success('导出Excel成功')
}

const handleExportWord = () => {
  message.success('导出Word成功')
}

// 图表相关方法
const handleGenerateChart = () => {
  if (!chartForm.type) {
    message.warning('请选择图表类型')
    return
  }
  if (!chartForm.dimension) {
    message.warning('请选择数据维度')
    return
  }
  if (!chartForm.metrics || chartForm.metrics.length === 0) {
    message.warning('请选择指标')
    return
  }
  message.success('图表生成成功')
  initChart()
}

const handleResetChartForm = () => {
  chartForm.type = undefined
  chartForm.dimension = undefined
  chartForm.metrics = []
}

const handleExportChart = () => {
  message.success('导出图表成功')
}

const handleSaveChart = () => {
  message.success('保存图表成功')
}

// 仪表盘相关方法
const handleCreateDashboard = () => {
  dashboardModalTitle.value = '新建仪表盘'
  dashboardForm.name = ''
  dashboardForm.description = ''
  dashboardForm.layout = 'grid'
  dashboardForm.charts = []
  dashboardModalVisible.value = true
}

const handleEditDashboard = (dashboard) => {
  dashboardModalTitle.value = '编辑仪表盘'
  dashboardForm.name = dashboard.name
  dashboardForm.description = dashboard.description || ''
  dashboardForm.layout = dashboard.layout || 'grid'
  dashboardForm.charts = dashboard.charts || []
  dashboardModalVisible.value = true
}

const handleShareDashboard = (dashboard) => {
  message.success(`分享仪表盘：${dashboard.name}`)
}

const handleDeleteDashboard = (dashboard) => {
  message.success(`删除仪表盘：${dashboard.name}`)
}

const handleImportDashboard = () => {
  message.info('导入仪表盘')
}

const handleDashboardModalOk = () => {
  message.success('保存成功')
  dashboardModalVisible.value = false
}

const handleDashboardModalCancel = () => {
  dashboardModalVisible.value = false
}

const handleAddChart = () => {
  dashboardForm.charts.push({
    type: undefined,
    dataSource: undefined,
    dimension: undefined,
    metrics: []
  })
}

const handleMoveChartUp = (index) => {
  if (index > 0) {
    const temp = dashboardForm.charts[index]
    dashboardForm.charts[index] = dashboardForm.charts[index - 1]
    dashboardForm.charts[index - 1] = temp
  }
}

const handleMoveChartDown = (index) => {
  if (index < dashboardForm.charts.length - 1) {
    const temp = dashboardForm.charts[index]
    dashboardForm.charts[index] = dashboardForm.charts[index + 1]
    dashboardForm.charts[index + 1] = temp
  }
}

const handleRemoveChart = (index) => {
  dashboardForm.charts.splice(index, 1)
}
</script>

<style scoped>
.data-visualization {
  padding: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.template-header,
.dashboard-header {
  margin-bottom: 16px;
}

.search-form {
  margin-bottom: 16px;
}

.report-preview,
.chart-preview {
  height: 500px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  width: 100%;
  min-height: 400px;
}

.template-layout,
.chart-list {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
}

.layout-item,
.chart-item {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
}

.layout-header,
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.layout-content,
.chart-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.dashboard-preview {
  margin-bottom: 16px;
}

.dashboard-preview img {
  width: 100%;
  height: auto;
  border-radius: 4px;
}

.dashboard-info {
  display: flex;
  justify-content: space-between;
  color: #999;
  font-size: 12px;
}
</style> 