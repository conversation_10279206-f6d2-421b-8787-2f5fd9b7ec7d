/**
* TODO: 米白色系主题
*/

// 主色
$color-primary: #d2b48c;

// 辅助色
$color-info: #b8c1c5;
$color-success: #80b178;
$color-warning: #d8c49a;
$color-danger: #d1786b;

// 文字
$color-text-main: #333333;
$color-text-normal: #666666;
$color-text-sub: #999999;
$color-text-placeholder: #b3b3b3;

// 边框
$color-border-1: #d0d0d0;
$color-border-2: #e0e0e0;
$color-border-3: #f0f0f0;
$color-border-4: #f5f5f5;


// 表单样式
$form-padding: 16px;
$form-border-radius: 4px;
$form-input-height: 40px;
$form-input-border-width: 1px;



// 渐变色
$gradient-1: linear-gradient(to right, #d2b48c, #d1786b);
$gradient-2: linear-gradient(to right, #d2b48c, #d8c49a);
$gradient-3: linear-gradient(to right, #d2b48c, #80b178);
$gradient-4: linear-gradient(to right, #d2b48c, #b8c1c5);
$gradient-5: linear-gradient(to right, #d2b48c, #d0d0d0);
$gradient-6: linear-gradient(to right, #d2b48c, #e0e0e0);
$gradient-7: linear-gradient(to right, #d2b48c, #f0f0f0);
$gradient-8: linear-gradient(to right, #d2b48c, #f5f5f5);


// 背景
$color-bg: #f1f1f1;
// 动画
$animation-duration: 0.3s;
$animation-timing-function: ease-in-out;


@media (prefers-color-scheme: dark) {

  /**
* TODO: 蓝灰色系主题
*/
  // 主色
  $color-primary: #607d8b;

  // 辅助色
  $color-info: #b8c1c5;
  $color-success: #80b178;
  $color-warning: #d8c49a;
  $color-danger: #d1786b;

  // 文字
  $color-text-main: #333333;
  $color-text-normal: #666666;
  $color-text-sub: #999999;
  $color-text-placeholder: #b3b3b3;

  // 边框
  $color-border-1: #d0d0d0;
  $color-border-2: #e0e0e0;
  $color-border-3: #f0f0f0;
  $color-border-4: #f5f5f5;

  // 表单样式
  $form-padding: 16px;
  $form-border-radius: 4px;
  $form-input-height: 40px;
  $form-input-border-width: 1px;

  // 背景
  $color-bg: #ffffff;

  // 渐变色
  $gradient-1: linear-gradient(to right, #607d8b, #d1786b);
  $gradient-2: linear-gradient(to right, #607d8b, #d8c49a);
  $gradient-3: linear-gradient(to right, #607d8b, #80b178);
  $gradient-4: linear-gradient(to right, #607d8b, #b8c1c5);
  $gradient-5: linear-gradient(to right, #607d8b, #d0d0d0);
  $gradient-6: linear-gradient(to right, #607d8b, #e0e0e0);
  $gradient-7: linear-gradient(to right, #607d8b, #f0f0f0);
  $gradient-8: linear-gradient(to right, #607d8b, #f5f5f5);

  // 动画
  $animation-duration: 0.3s;
  $animation-timing-function: ease-in-out;

}


