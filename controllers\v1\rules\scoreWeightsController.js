const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const scoreWeightModel = require('../../../models/v1/mapping/scoreWeightsModel');
const { getUserInfoFromRequest } = require('../../../utils/others');

/**
 * 获取评分权重列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getScoreWeights = async (req, res) => {
  try {
    const weights = await scoreWeightModel.findAll({
      order: [['createdAt', 'ASC']]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: weights
    });
  } catch (error) {
    console.error('获取评分权重列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取评分权重列表失败',
      error: error.message
    });
  }
};

/**
 * 获取评分权重详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getWeightDetail = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少权重ID',
        data: null
      });
    }

    // 查询权重详情
    const weight = await scoreWeightModel.findByPk(id);

    if (!weight) {
      return res.status(404).json({
        code: 404,
        message: '未找到评分权重',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: weight
    });
  } catch (error) {
    console.error('获取评分权重详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取评分权重详情失败',
      error: error.message
    });
  }
};

/**
 * 创建评分权重
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createWeight = async (req, res) => {
  try {
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 权限检查：只有管理员可以创建评分权重
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      return res.status(403).json({
        code: 403,
        message: '您没有权限创建评分权重',
        data: null
      });
    }
    
    const { categoryName, categoryCode, weight, description } = req.body;

    // 验证必要字段
    if (!categoryName || !categoryCode) {
      return res.status(400).json({
        code: 400,
        message: '类别名称和类别代码不能为空',
        data: null
      });
    }

    // 检查类别代码是否已存在
    const existingWeight = await scoreWeightModel.findOne({
      where: {
        categoryCode: categoryCode
      }
    });

    if (existingWeight) {
      return res.status(400).json({
        code: 400,
        message: '类别代码已存在',
        data: null
      });
    }

    // 创建评分权重
    const newWeight = await scoreWeightModel.create({
      id: uuidv4(),
      categoryName,
      categoryCode,
      weight: weight || 1.00,
      description,
      status: 1
    });

    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: newWeight
    });
  } catch (error) {
    console.error('创建评分权重失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建评分权重失败',
      error: error.message
    });
  }
};

/**
 * 更新评分权重
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateWeight = async (req, res) => {
  try {
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 权限检查：只有管理员可以更新评分权重
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      return res.status(403).json({
        code: 403,
        message: '您没有权限更新评分权重',
        data: null
      });
    }
    
    const { id } = req.params;
    const { categoryName, categoryCode, weight, description, status } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少权重ID',
        data: null
      });
    }

    // 查询权重是否存在
    const weightRecord = await scoreWeightModel.findByPk(id);

    if (!weightRecord) {
      return res.status(404).json({
        code: 404,
        message: '未找到评分权重',
        data: null
      });
    }

    // 如果要更新类别代码，检查新代码是否已存在
    if (categoryCode && categoryCode !== weightRecord.categoryCode) {
      const existingWeight = await scoreWeightModel.findOne({
        where: {
          categoryCode: categoryCode,
          id: { [Op.ne]: id }
        }
      });

      if (existingWeight) {
        return res.status(400).json({
          code: 400,
          message: '类别代码已存在',
          data: null
        });
      }
    }

    // 更新权重
    const updateData = {};
    if (categoryName !== undefined) updateData.categoryName = categoryName;
    if (categoryCode !== undefined) updateData.categoryCode = categoryCode;
    if (weight !== undefined) updateData.weight = weight;
    if (description !== undefined) updateData.description = description;
    if (status !== undefined) updateData.status = status;

    await weightRecord.update(updateData);

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: await scoreWeightModel.findByPk(id)
    });
  } catch (error) {
    console.error('更新评分权重失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新评分权重失败',
      error: error.message
    });
  }
};

/**
 * 删除评分权重
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteWeight = async (req, res) => {
  try {
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 权限检查：只有管理员可以删除评分权重
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      return res.status(403).json({
        code: 403,
        message: '您没有权限删除评分权重',
        data: null
      });
    }
    
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少权重ID',
        data: null
      });
    }

    // 查询权重是否存在
    const weight = await scoreWeightModel.findByPk(id);

    if (!weight) {
      return res.status(404).json({
        code: 404,
        message: '未找到评分权重',
        data: null
      });
    }

    // 删除权重
    await weight.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除评分权重失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除评分权重失败',
      error: error.message
    });
  }
};

/**
 * 获取评分权重及使用情况
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getWeightsWithUsage = async (req, res) => {
  try {
    // 获取所有权重
    const weights = await scoreWeightModel.findAll({
      order: [['createdAt', 'ASC']]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: weights
    });
  } catch (error) {
    console.error('获取权重及使用情况失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取权重及使用情况失败',
      error: error.message
    });
  }
};