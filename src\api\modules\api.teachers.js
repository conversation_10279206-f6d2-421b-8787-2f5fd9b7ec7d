import { create } from 'lodash'
import service from '../server'

// 教师相关接口
const api = {
  list: '/sys/teacher/list',
  detail: '/sys/teacher/detail',
  create: '/sys/teacher/create',
  update: '/sys/teacher/update',
  delete: '/sys/teacher/delete',
  import: '/sys/teacher/import',
  export: '/sys/teacher/export',
}

// 获取教师列表
export const teachersList = (data) => {
  return service.post(api.list, data)
}

// 获取教师详情
export const teachersDetail = (data) => {
  return service.post(api.detail, data)
}

// 创建教师
export const teachersCreate = (data) => {
  return service.post(api.create, data)
}

// 更新教师
export const teachersUpdate = (data) => {
  return service.post(api.update, data)
}

// 删除教师
export const teachersDelete = (data) => {
  return service.post(api.delete, data)
}

// 导入教师
export const teachersImport = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return service.post(api.import, formData, null, 'multipart/form-data')
}

// 导出教师
export const teachersExport = (data) => {
  return service.post(api.export, data, null, 'blob')
} 