const bcrypt = require("bcryptjs");
const request = require("request");
const crypto = require('crypto');
const axios = require('axios');
const timeIntervalModel = require('../models/v1/mapping/timeIntervalModel');
const jwt = require('jsonwebtoken');
const { JWT_SECRET } = require('../config');
const userModel = require('../models/v1/mapping/userModel');
const roleModel = require('../models/v1/mapping/roleModel');


/**
 * 随机数
 * @param {number} length 随机数长度
 * @returns {number} 生成的随机数
 */
const randomNumber = function(length) {
  return Math.floor(Math.random() * Math.pow(10, length));
};

/**
 * bcrypt 加密数据
 * @param {number|string} value 要加密的值
 * @returns {Promise<string>} 加密后的值
 */
const encryption = function(value) {
  return new Promise((resolve, reject) => {
    bcrypt.hash(value, 10, function(err, hash) {
      if (err) {
        return reject(err);
      }
      resolve(hash);
    });
  });
};

/**
 * bcrypt 解密数据 （密码匹配）
 * @param {number|string} value 未加密的值
 * @param {string} enValue 已加密的值
 * @returns {Promise<boolean>} 两个密码是否相同
 */
const decryption = function(value, enValue) {
  return new Promise((resolve, reject) => {
    bcrypt.compare(value, enValue, function(err, same) {
      if (err) {
        return reject(err);
      }
      resolve(same);
    });
  });
};

/**
 * 获取用户的真实公网IP
 * @param {Object} req 请求对象
 * @returns {string} 用户IP地址
 */
const getPublicIP = function(req) {
  const forwardedFor = req.headers['x-forwarded-for'];
  if (forwardedFor) {
    return forwardedFor.split(',')[0];
  }
  return req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         req.connection.socket?.remoteAddress;
};

/**
 * IP地址解析
 * @param {string} ip IP地址
 * @returns {Promise<string>} 解析后的位置信息
 */
const parseIP = async function(ip) {
  try {
    const response = await axios.get(`http://ip-api.com/json/${ip}`);
    if (response.data.status === 'success') {
      return `${response.data.country} ${response.data.regionName} ${response.data.city}`;
    }
    return '未知';
  } catch (error) {
    console.error('IP解析失败:', error);
    return '未知';
  }
};

/**
 * IP地址解析经纬度
 * @param {string} clientIp 客户端IP
 * @returns {Promise<Object>} 包含位置信息的对象
 */
const IPToLonAndLat = function(clientIp) {
  return new Promise((resolve, reject) => {
    request(
      `https://ipinfo.io/${clientIp}?token=1c5d78e2e95e22`,
      {method: 'GET'},
      function(error, response, body) {
        if (error !== null) {
          if (!body) {
            resolve({});
            return;
          }
        }
        if (body) {
          let b = JSON.parse(body);
          let info = {
            ip: b.ip,
            city: b.city,
            region: b.region,
            country: b.country,
            loc: b.loc ? [Number(b.loc.split(',')[1]), Number(b.loc.split(',')[0])] : '未知',
            latitude: b.loc ? Number(b.loc.split(',')[0]) : 0, // 纬度
            longitude: b.loc ? Number(b.loc.split(',')[1]) : 0 // 经度
          };
          resolve(info);
        }
      }
    );
  });
};

/**
 * 根据邮箱生成头像
 * @param {string} email 电子邮件地址
 * @returns {string} Gravatar头像URL
 */
const getEmailAvatar = function(email) {
  const hash = crypto.createHash('md5').update(email.toLowerCase().trim()).digest('hex');
  return `https://www.gravatar.com/avatar/${hash}?d=identicon`;
};

/**
 * 使用星号替换字符串中间部分
 * @param {string} inputString 输入字符串
 * @param {number} length 星号数量
 * @returns {string} 替换后的字符串
 */
const replaceMiddleWithAsterisks = function(inputString, length = 5) {
  if (inputString.length < 3) {
    return inputString; // 字符串长度小于3，无法替换中间字符
  }
  const middleIndex = Math.floor(inputString.length / 2);
  const start = inputString.slice(0, middleIndex - 1); // 中间字符的前一个字符
  const end = inputString.slice(middleIndex + 2); // 中间字符的后一个字符
  const asterisks = '*'.repeat(length); // 用 '*' 替换中间的3个字符

  return start + asterisks + end;
};

/**
 * 根据名称获取时间区间
 * @param {string} [name] 时间区间名称，如果不提供则根据调用文件名自动确定
 * @returns {Promise<Object|null>} 返回时间区间对象，包含startTime和endTime，如果不存在则返回null
 */
const getTimeIntervalByName = async function(name) {
  try {
    // 如果没有提供name参数，则根据调用栈获取文件名
    if (!name) {
      const stack = new Error().stack;
      const callerLine = stack.split('\n')[3]; // 获取调用者的行
      
      // 从调用栈中提取文件名
      const match = callerLine.match(/\((.+):(\d+):(\d+)\)/);
      if (match && match[1]) {
        const filePath = match[1];
        const fileName = filePath.split('/').pop().split('\\').pop(); // 获取文件名
        const baseName = fileName.split('.')[0]; // 移除扩展名
        
        // 将驼峰命名转换为下划线命名，并转换为小写
        const formattedName = baseName
          .replace(/([A-Z])/g, '_$1')
          .toLowerCase()
          .replace(/^_/, ''); // 移除开头的下划线
        
        name = formattedName;
        console.log(`🔍 自动确定时间区间名称: ${name} (基于文件名)`);
      } else {
        console.warn('⚠️ 无法自动确定时间区间名称: 无法从调用栈获取文件名');
        return null;
      }
    } else {
      console.log(`🔍 查询时间区间 - 名称: ${name}`);
    }
    
    // 查询时间区间
    const timeInterval = await timeIntervalModel.findOne({
      where: {
        name: name
      }
    });
    
    if (!timeInterval) {
      console.log(`⚠️ 未找到名称为 ${name} 的时间区间`);
      return null;
    }
    
    console.log(`✅ 查询成功: 时间区间 ${name}, 开始时间: ${timeInterval.startTime}, 结束时间: ${timeInterval.endTime}`);
    
    return {
      id: timeInterval.id,
      name: timeInterval.name,
      startTime: timeInterval.startTime,
      endTime: timeInterval.endTime
    };
  } catch (error) {
    console.error(`❌ 查询时间区间失败: ${error.message}`);
    return null;
  }
};

/**
 * 通过token获取用户信息和角色
 * @param {string} token - JWT token
 * @param {boolean} includePermissions - 是否包含权限信息
 * @returns {Promise<Object>} 包含用户信息和角色的对象
 * @throws {Error} 如果token无效或用户不存在
 */
const getUserInfoFromToken = async (token, includePermissions = false) => {
  try {
    // 移除Bearer前缀(如果有)
    if (token.startsWith('Bearer ')) {
      token = token.slice(7);
    }
    
    // 解析token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // 查询用户是否存在
    const user = await userModel.findByPk(decoded.id);
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 查询角色信息
    const role = await roleModel.findByPk(user.roleId);
    if (!role) {
      throw new Error('角色不存在');
    }
    
    // 构建基本用户信息
    const userInfo = {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      studentNumber: user.studentNumber,
      status: user.status,
      roleId: user.roleId,
      departmentId: user.departmentId,
      userLevelId: user.userLevelId,
      role: {
        id: role.id,
        name: role.name,
        roleAuth: role.roleAuth
      }
    };
    
    // 如果需要权限信息且有权限关联
    if (includePermissions && role.permissions) {
      userInfo.permissions = role.permissions;
    }
    
    return userInfo;
  } catch (error) {
    console.error('解析token获取用户信息失败:', error);
    throw error;
  }
};

/**
 * 通过请求头获取用户信息和角色
 * @param {Object} req - Express请求对象
 * @param {boolean} includePermissions - 是否包含权限信息
 * @returns {Promise<Object>} 包含用户信息和角色的对象
 * @throws {Error} 如果token无效或用户不存在
 */
const getUserInfoFromRequest = async (req, includePermissions = false) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    throw new Error('未提供认证Token');
  }
  
  return getUserInfoFromToken(authHeader, includePermissions);
};

/**
 * 将存储过程返回的结果转换为数组格式
 * @param {Object|Array} results - 存储过程返回的结果
 * @param {boolean} [debug=false] - 是否输出调试信息
 * @param {boolean} [parseNumbers=false] - 是否将数字字符串转换为数值类型
 * @returns {Array} - 转换后的数组
 */
function convertStoredProcResultToArray(results, debug = false, parseNumbers = false) {
  // 调试日志函数
  const log = (message, data) => {
    if (debug) {
      console.log(`[convertStoredProcResultToArray] ${message}`, data ? data : '');
    }
  };

  // 检查结果是否为空
  if (results === null || results === undefined) {
    log('结果为空，返回空数组');
    return [];
  }

  // 如果结果已经是数组，直接处理
  if (Array.isArray(results)) {
    log('结果已经是数组，长度:', results.length);
    
    if (parseNumbers) {
      return results.map(item => convertNumericFields(item));
    }
    return results;
  }

  // 如果结果是单个对象
  if (typeof results === 'object') {
    log('结果是对象，类型:', Object.prototype.toString.call(results));
    
    // 检查是否是类似 {"0": {...}, "1": {...}} 的格式
    const hasNumericKeys = Object.keys(results).some(key => !isNaN(parseInt(key)));
    
    if (hasNumericKeys) {
      log('检测到数字键对象，转换为数组');
      const array = Object.values(results);
      return parseNumbers ? array.map(item => convertNumericFields(item)) : array;
    }
    
    // 检查是否有数组属性
    for (const key in results) {
      if (Array.isArray(results[key])) {
        log(`找到数组属性 ${key}，长度:`, results[key].length);
        const array = results[key];
        return parseNumbers ? array.map(item => convertNumericFields(item)) : array;
      }
    }
    
    // 单个对象包装成数组
    log('单个对象包装成数组');
    const array = [results];
    return parseNumbers ? array.map(item => convertNumericFields(item)) : array;
  }

  // 如果结果格式无法识别
  log('无法识别的结果格式:', results);
  return [];
}

/**
 * 转换对象中的数字字段
 * @param {Object} item - 要处理的对象
 * @returns {Object} - 处理后的对象
 */
function convertNumericFields(item) {
  if (!item || typeof item !== 'object') return item;
  
  const newItem = { ...item };
  const numericFields = [
    'totalScore', 'totalAwards', 'score', 'count', 'amount', 'ratio',
    'allocationRatio', 'rank', 'page', 'pageSize', 'total'
  ];
  
  numericFields.forEach(field => {
    if (newItem[field] !== undefined && newItem[field] !== null) {
      const value = newItem[field];
      // 检查是否是数字字符串
      if (typeof value === 'string' && !isNaN(parseFloat(value))) {
        newItem[field] = parseFloat(value);
      }
    }
  });
  
  return newItem;
}

/**
 * 通过模型获取数据库连接实例
 * @param {Object} model - Sequelize 模型对象
 * @returns {Object} sequelize实例
 * @throws {Error} 如果无法获取数据库连接实例
 */
const getSequelizeInstance = (model) => {
  if (model && model.sequelize) {
    return model.sequelize;
  }
  throw new Error('无法获取数据库连接实例');
};

/**
 * 判断项目是否在时间范围内
 * @param {string|Date} projectDate - 项目日期
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
const isProjectInTimeRange = function(projectDate, startDate, endDate) {
  if (!projectDate || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const projectDateObj = new Date(projectDate);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 项目日期必须在开始日期和结束日期之间
  return projectDateObj >= startDateObj && projectDateObj <= endDateObj;
};

/**
 * 判断两个年份区间是否存在交集
 * @param {string|number} startYear - 开始年份
 * @param {string|number} endYear - 结束年份，如果为null表示至今
 * @param {string} intervalStartTime - 区间开始时间，格式为YYYY-MM-DD
 * @param {string} intervalEndTime - 区间结束时间，格式为YYYY-MM-DD
 * @returns {boolean} - 是否存在交集
 */
const isYearRangeOverlap = (startYear, endYear, intervalStartTime, intervalEndTime) => {
  if (!startYear || !intervalStartTime || !intervalEndTime) return false;
  
  // 从intervalStartTime和intervalEndTime中提取年份
  const intervalStartYear = new Date(intervalStartTime).getFullYear();
  const intervalEndYear = new Date(intervalEndTime).getFullYear();
  
  // 将开始年份和结束年份转换为数字
  const startYearNum = parseInt(startYear, 10);
  const endYearNum = endYear ? parseInt(endYear, 10) : Infinity;
  
  // 判断两个区间是否有交集
  // 区间A: [startYearNum, endYearNum]
  // 区间B: [intervalStartYear, intervalEndYear]
  // 两个区间有交集的条件：区间A的结束不早于区间B的开始，且区间A的开始不晚于区间B的结束
  return startYearNum <= intervalEndYear && endYearNum >= intervalStartYear;
};

module.exports = {
  randomNumber,
  encryption,
  decryption,
  getPublicIP,
  parseIP,
  IPToLonAndLat,
  getEmailAvatar,
  replaceMiddleWithAsterisks,
  getTimeIntervalByName,
  getUserInfoFromToken,
  getUserInfoFromRequest,
  convertStoredProcResultToArray,
  getSequelizeInstance,
  isProjectInTimeRange,
  isYearRangeOverlap
};
