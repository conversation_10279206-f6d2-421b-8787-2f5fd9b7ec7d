<template>
  <section class="zy-get user-form-optimized">
    <!-- 基本信息区域 -->
    <div class="form-section">
      <h3 class="section-title">基本信息</h3>
      <a-form :model="state.form"
              class="zy-form"
              :label-col="labelCol"
              ref="formRef"
              :wrapper-col="wrapperCol">

        <!-- 头像单独一行 -->
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="头像"
                         name="avatar"
                         :rules="[{ required: true, message: '请上传头像!' }]">
              <div class="upload-box">
                <a-textarea v-model:value="state.form.avatar" allowClear placeholder="在线地址" :rows="2"></a-textarea>
                <a-image style="margin: 1rem 0" v-if="state.form.avatar" width="80px" :src="state.form.avatar"></a-image>
                <ZyUpload v-model:url="state.form.avatar"/>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 基本信息两列布局 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="昵称"
                         name="nickname"
                         :rules="[{ required: true, message: '请输入昵称!' }]">
              <a-input v-model:value="state.form.nickname" allowClear placeholder="请输入昵称"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="学号/工号"
                         name="studentNumber"
                         :rules="[{ required: true, message: '请输入学号/工号!' }]">
              <a-input v-model:value="state.form.studentNumber" allowClear placeholder="请输入学号/工号"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="用户名"
                         name="username"
                         :rules="[{ required: true, message: '请输入用户名!' }]">
              <a-input v-model:value="state.form.username" allowClear placeholder="请输入用户名"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="state.form.email" allowClear placeholder="请输入邮箱"/>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24" v-if="!state.form._id">
          <a-col :span="12">
            <a-form-item label="密码"
                         name="password"
                         :rules="[{ required: true, message: '请输入密码!' }]">
              <a-input-password v-model:value="state.form.password" allowClear placeholder="请输入密码"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <!-- 预留空间 -->
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="角色" name="roleId"
                         :rules="[{ required: true, message: '请选择角色!' }]">
              <ZyRoleSelect v-model:value="state.form.roleId"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="部门" name="departmentId">
              <a-select v-model:value="state.form.departmentId"
                        placeholder="请选择部门"
                        allowClear
                        :loading="state.loading.departments">
                <a-select-option v-for="dept in state.departments"
                                :key="dept.id"
                                :value="dept.id">
                  {{ dept.departmentName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 备注单独一行 -->
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="备注" name="remark">
              <a-textarea v-model:value="state.form.remark" allowClear placeholder="请输入备注" :rows="3"/>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 职称记录管理 -->
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="level-records-section">
              <div class="level-records-header">
                <span>职称获得记录</span>
                <a-button type="primary" size="small" @click="addLevelRecord">
                  <template #icon><PlusOutlined /></template>
                  添加记录
                </a-button>
              </div>

              <div class="level-records-list" v-if="state.levelRecords.length > 0">
                <div v-for="(record, index) in state.levelRecords"
                     :key="index"
                     class="level-record-item">
                  <a-row :gutter="16" align="middle">
                    <a-col :xs="24" :sm="24" :md="8" :lg="8">
                      <div class="form-item-wrapper">
                        <label class="form-label">职称级别</label>
                        <a-select v-model:value="record.levelId"
                                 placeholder="请选择职称级别"
                                 :loading="state.loading.userLevels"
                                 style="width: 100%">
                          <a-select-option v-for="level in state.userLevels"
                                          :key="level.id"
                                          :value="level.id">
                            {{ level.levelName }} (级别: {{ level.sort }})
                          </a-select-option>
                        </a-select>
                      </div>
                    </a-col>
                    <a-col :xs="24" :sm="12" :md="6" :lg="6">
                      <div class="form-item-wrapper">
                        <label class="form-label">获得时间</label>
                        <a-date-picker v-model:value="record.obtainedAt"
                                      placeholder="获得时间"
                                      style="width: 100%" />
                      </div>
                    </a-col>
                    <a-col :xs="24" :sm="10" :md="8" :lg="8">
                      <div class="form-item-wrapper">
                        <label class="form-label">备注</label>
                        <a-input v-model:value="record.remarks"
                                placeholder="如：通过评审、调动等" />
                      </div>
                    </a-col>
                    <a-col :xs="24" :sm="2" :md="2" :lg="2">
                      <div class="form-item-wrapper">
                        <label class="form-label" style="visibility: hidden;">操作</label>
                        <a-button type="text"
                                 danger
                                 @click="removeLevelRecord(index)"
                                 style="width: 100%">
                          <template #icon><DeleteOutlined /></template>
                          删除
                        </a-button>
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </div>

              <div v-else class="no-records">
                <a-empty description="暂无职称记录" />
              </div>
            </div>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <ZyFormButton @save="onSubmit" @close="close"/>
  </section>
</template>

<script setup>
import {reactive, toRaw, ref, onMounted} from 'vue';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import ZyFormButton from "comps/common/ZyFormButton.vue";
import ZyUpload from "../../../components/common/ZyUpload.vue";
import {ZyConfirm, ZyNotification} from "../../../libs/util.toast";
import ZyRoleSelect from "../../../components/common/ZyRoleSelect.vue";
import {usersCreate, usersUpdate, getUserLevels, getDepartments, getUserLevelRecords, saveUserLevelRecords} from "../../../api/modules/api.users";
import dayjs from 'dayjs';

const labelCol = {
  style: {
    width: '80px',
  },
}
const wrapperCol = {
  span: 20,
}

const state = reactive({
  form: {
    avatar: 'https://gravatar.kuibu.net/avatar/5c04c6164bbf04f3e6bcbd01dfd00e03?s=100'
  },
  departments: [],
  userLevels: [],
  levelRecords: [],
  loading: {
    departments: false,
    userLevels: false
  }
});

const props = defineProps({
  updateData: {
    type: Object,
    default: () => {
    }
  }
})
const emit = defineEmits(['close'])
const formRef = ref();
const isAdd = ref(!props.updateData) // 是否是添加
if (!isAdd.value) {
  state.form = props.updateData || {}
}

// 初始化数据
onMounted(async () => {
  await loadDepartments()
  await loadUserLevels()
  if (!isAdd.value && state.form.id) {
    await loadUserLevelRecords()
  }
})

// 加载部门列表
const loadDepartments = async () => {
  try {
    state.loading.departments = true
    const res = await getDepartments()
    state.departments = res.data || []
  } catch (error) {
    console.error('加载部门列表失败:', error)
    ZyNotification.error('加载部门列表失败')
  } finally {
    state.loading.departments = false
  }
}

// 加载用户级别列表
const loadUserLevels = async () => {
  try {
    state.loading.userLevels = true
    const res = await getUserLevels()
    state.userLevels = res.data || []
  } catch (error) {
    console.error('加载用户级别列表失败:', error)
    ZyNotification.error('加载用户级别列表失败')
  } finally {
    state.loading.userLevels = false
  }
}

// 加载用户职称记录
const loadUserLevelRecords = async () => {
  try {
    const res = await getUserLevelRecords({ userId: state.form.id })
    state.levelRecords = (res.data || []).map(record => ({
      levelId: record.levelId,
      obtainedAt: record.obtainedAt ? dayjs(record.obtainedAt) : null,
      remarks: record.remarks || ''
    }))
  } catch (error) {
    console.error('加载用户职称记录失败:', error)
    ZyNotification.error('加载用户职称记录失败')
  }
}

// 添加职称记录
const addLevelRecord = () => {
  state.levelRecords.push({
    levelId: '',
    obtainedAt: null,
    remarks: ''
  })
}

// 删除职称记录
const removeLevelRecord = (index) => {
  state.levelRecords.splice(index, 1)
}

const onSubmit = async () => {
  try {
    await formRef.value.validateFields();
    if (!isAdd.value) {
      delete state.form.password
    }

    let FUC = isAdd.value ? usersCreate : usersUpdate
    const userRes = await FUC(toRaw(state.form))

    // 保存职称记录（新增和编辑都支持，包括删除所有记录的情况）
    try {
      // 验证职称记录
      const validRecords = state.levelRecords.filter(record =>
        record.levelId && record.obtainedAt
      ).map(record => ({
        levelId: record.levelId,
        obtainedAt: record.obtainedAt.format('YYYY-MM-DD'),
        remarks: record.remarks || ''
      }))

      // 对于新增用户，使用返回的用户ID；对于编辑用户，使用原有ID
      const userId = isAdd.value ? userRes.data?.id || userRes.data : state.form.id

      // 总是调用保存接口，即使是空数组（用于删除所有记录）
      await saveUserLevelRecords({
        userId: userId,
        records: validRecords
      })
    } catch (recordError) {
      console.error('保存职称记录失败:', recordError)
      ZyNotification.warning('用户信息保存成功，但职称记录保存失败')
    }

    ZyNotification.success('操作成功')
    emit('close', true)
  } catch (errorInfo) {
    console.log('Failed:', errorInfo);
    ZyNotification.error('操作失败')
  }
};

const close = () => {
  ZyConfirm('还没保存数据，确认退出?').then(ok => {
    if (!ok) return
    emit('close')
  })
}
</script>

<style lang="scss" scoped>
.user-form-optimized {
  .form-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #f0f0f0;
    }
  }

  // 优化表单项间距
  .ant-form-item {
    margin-bottom: 16px;
  }

  // 两列布局时的标签宽度调整
  .ant-col-12 .ant-form-item-label {
    width: 70px !important;
    min-width: 70px;
  }
}

.upload-box {
  display: flex;
  flex-direction: column;
}

.level-records-section {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
  min-height: 200px;
  margin-top: 16px;

  .level-records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-weight: 600;
    color: #262626;
    font-size: 16px;
  }

  .level-records-list {
    max-height: 400px;
    overflow-y: auto;

    .level-record-item {
      background: white;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 4px 8px rgba(24,144,255,0.15);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .form-item-wrapper {
        margin-bottom: 8px;

        @media (min-width: 768px) {
          margin-bottom: 0;
        }

        .form-label {
          display: block;
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
          font-weight: 500;
        }
      }
    }
  }

  .no-records {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    color: #999;
  }
}

// 响应式优化
@media (max-width: 768px) {
  .user-form-optimized {
    .ant-col-12 {
      width: 100% !important;
      max-width: 100% !important;
    }

    .ant-col-12 .ant-form-item-label {
      width: 80px !important;
    }
  }
}
</style>
