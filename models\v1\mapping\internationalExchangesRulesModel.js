const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义国际交流与合作评价核算规则模型
module.exports = sequelize.define('international_exchanges_rules', // 数据库表名为international_exchanges_rules
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: '主键，使用 UUID 唯一标识每条记录',
        },
        project: {
            type: DataTypes.STRING(255),
            notNull: true,
            allowNull: false,
            comment: '项目名称（如来华学习交流、赴港澳台学习交流等）',
        },
        score: {
            type: DataTypes.DECIMAL(10, 2),
            notNull: true,
            allowNull: false,
            comment: '核算分数',
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: '项目的详细描述（如来华学习交流超过 90 天的境外研究生）',
        },
        maxPeople: {
            type: DataTypes.INTEGER,
            allowNull: true,
            comment: '额外信息（如最多计算 3 个研究生）',
        },
        createdBy: {
            type: DataTypes.UUID,
            notNull: true,
            allowNull: false,
            comment: '创建者 ID（userId，与 id 一致的 UUID）',
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录创建时间',
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录最后修改时间',
        },
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
        indexes: [
            {
                unique: true,
                fields: ['project'],
                name: 'uk_project'
            }
        ]
    }); 