import request from '../server'

// API 路径配置
const api = {
  list: '/sys/textbook-publishing-rules/list',
  detail: '/sys/textbook-publishing-rules/detail',
  create: '/sys/textbook-publishing-rules/create',
  update: '/sys/textbook-publishing-rules/update',
  delete: '/sys/textbook-publishing-rules/delete',
  batchDelete: '/sys/textbook-publishing-rules/batch-delete',
  import: '/sys/textbook-publishing-rules/import',
  export: '/sys/textbook-publishing-rules/export'
}

/**
 * 获取教材出版规则列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getTextbookPublishingRules(params) {
  const { page = 1, pageSize = 10, category, role } = params || {};
  
  // 构造参数对象
  const queryParams = {
    page,
    pageSize,
    category,
    role
  };
  
  return request.get(api.list, queryParams);
}

/**
 * 获取教材出版规则详情
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function getTextbookPublishingRuleDetail(id) {
  return request.get(api.detail, { id });
}

/**
 * 创建教材出版规则
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function addTextbookPublishingRule(data) {
  return request.post(api.create, data);
}

/**
 * 更新教材出版规则
 * @param {string} id - 规则ID
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateTextbookPublishingRule(id, data) {
  return request.put(api.update, { id, ...data });
}

/**
 * 删除教材出版规则
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteTextbookPublishingRule(id) {
  return request.delete(api.delete, { id });
}

/**
 * 批量删除教材出版规则
 * @param {Array} ids - 规则ID数组
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteTextbookPublishingRules(ids) {
  return request.delete(api.batchDelete, { ids });
}

/**
 * 导入教材出版规则数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回Promise对象
 */
export function importTextbookPublishingRules(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request.post(api.import, formData, null, 'multipart/form-data');
}

/**
 * 导出教材出版规则数据
 * @param {Object} params - 过滤参数
 * @returns {Promise} - 返回Promise对象
 */
export function exportTextbookPublishingRules(params) {
  return request.get(api.export, params, { responseType: 'blob' });
}