import request from '../server'

// 获取科研经费列表
export function getResearchFunds(params) {
  return request.get('/researchFunds', { params })
}

// 获取科研经费详情
export function getResearchFundDetail(id) {
  return request.get(`/researchFunds/${id}`)
}

// 添加科研经费
export function addResearchFund(data) {
  return request.post('/researchFunds', data)
}

// 更新科研经费
export function updateResearchFund(id, data) {
  return request.put(`/researchFunds/${id}`, data)
}

// 删除科研经费
export function deleteResearchFund(id) {
  return request.delete(`/researchFunds/${id}`)
}

// 导入科研经费数据
export function importResearchFunds(file) {
  if (!file) {
    return Promise.reject(new Error('文件不能为空'))
  }
  const formData = new FormData()
  formData.append('file', file)
  return request.post('/sys/research-funds/import', formData, null, 'multipart/form-data')
}

// 导出科研经费数据
export function exportResearchFunds(params) {
  return request.get('/sys/research-funds/export', { params, responseType: 'blob' })
}

// 获取当前用户参与的科研经费列表
export function getUserResearchFunds(params) {
  console.log('【API】调用getUserResearchFunds，参数:', params);
  
  // 直接解构参数，确保不会有嵌套
  const { userId, page = 1, pageSize = 10, name, type, amount, leader, startDate, endDate } = params || {};
  
  // 构造参数对象
  const queryParams = {
    page,
    pageSize,
    userOnly: true,
    userId,
    name,
    type,
    amount,
    leader,
    startDate, 
    endDate
  };
  
  // 使用request.get方法
  return request.get('/sys/research-funds', queryParams)
    .then(response => {
      console.log('【API】getUserResearchFunds响应:', response);
      return response;
    })
    .catch(error => {
      console.error('【API】getUserResearchFunds错误:', error);
      console.error('【API】错误详情:', error.message, error.error);
      throw error;
    });
}

// 获取所有科研经费列表
export function getAllResearchFunds(params) {
  console.log('【API】调用getAllResearchFunds，参数:', params);
  
  // 直接解构参数，确保不会有嵌套
  const { page = 1, pageSize = 10, name, type, amount, leader, startDate, endDate } = params || {};
  
  // 构造参数对象
  const queryParams = {
    page,
    pageSize,
    name,
    type,
    amount,
    leader,
    startDate,
    endDate
  };
  
  // 使用request.get方法
  return request.get('/researchFunds', queryParams)
    .then(response => {
      console.log('【API】getAllResearchFunds响应:', response);
      return response;
    })
    .catch(error => {
      console.error('【API】getAllResearchFunds错误:', error);
      console.error('【API】错误详情:', error.message, error.error);
      throw error;
    });
} 