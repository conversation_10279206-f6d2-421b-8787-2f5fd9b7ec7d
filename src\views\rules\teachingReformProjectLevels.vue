<template>
  <div class="project-levels">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
    
    <a-card title="教学改革项目级别管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            添加级别
          </a-button>
          <a-button @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出级别
          </a-button>
        </a-space>
      </template>

      <a-alert
        message="级别说明"
        description="项目级别用于对教学改革项目进行归类和评分，不同级别的项目对应不同的分数。维护人员可以添加、编辑和删除项目级别，但删除时需要确保该级别下没有关联的项目。"
        type="info"
        show-icon
        style="margin-bottom: 16px;"
      />

      <a-table
        :columns="columns"
        :data-source="filteredDataSource"
        :pagination="pagination"
        :loading="isLoading"
        rowKey="id"
        @change="handleTableChange"
        :scroll="{ x: 800 }"
        :bordered="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'score'">
            <span style="font-weight: bold; color: #1890ff;">{{ record.score }}分</span>
          </template>
          <template v-else-if="column.key === 'projectCount'">
            <a @click="showProjectsInLevel(record)" v-if="record.projectCount > 0">
              <a-tag color="blue">{{ record.projectCount }}</a-tag>
            </a>
            <a-tag v-else color="default">0</a-tag>
          </template>
          <template v-else-if="column.key === 'createdAt'">
            {{ formatDate(record.createdAt) }}
          </template>
          <template v-else-if="column.key === 'updatedAt'">
            {{ formatDate(record.updatedAt) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这个级别吗？如果有项目使用此级别将无法删除"
                @confirm="handleDelete(record)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a :class="{'text-danger': record.projectCount === 0, 'text-disabled': record.projectCount > 0}">
                  删除
                </a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 使用ZyModal替代a-modal -->
      <ZyModal
        :show="modalVisible"
        :title="isEdit ? '编辑项目级别' : '添加项目级别'"
        :min-width="500"
        :min-height="300"
        @close="handleModalCancel"
      >
        <div class="level-form">
          <a-form
            :model="formState"
            :rules="rules"
            ref="formRef"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="级别名称" name="levelName">
              <a-input v-model:value="formState.levelName" placeholder="请输入级别名称" />
            </a-form-item>
            
            <a-form-item label="级别分数" name="score">
              <a-input-number
                v-model:value="formState.score"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="备注" name="remark">
              <a-textarea
                v-model:value="formState.remark"
                :rows="4"
                placeholder="请输入备注"
              />
            </a-form-item>
            
            <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
              <a-space>
                <a-button type="primary" @click="handleSubmit" :loading="confirmLoading">
                  提交
                </a-button>
                <a-button @click="handleModalCancel">
                  取消
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </ZyModal>

      <!-- 项目列表模态框 -->
      <ZyModal
        :show="projectsModalVisible"
        title="关联的项目列表"
        :min-width="800"
        :min-height="500"
        @close="closeProjectsModal"
      >
        <div v-if="selectedLevel">
          <h3>{{ selectedLevel.levelName }} ({{ selectedLevel.score }}分)</h3>
          <a-table
            :columns="projectColumns"
            :data-source="levelProjects"
            :pagination="{ pageSize: 10 }"
            :loading="projectsLoading"
            rowKey="id"
            :bordered="true"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'approvalDate'">
                {{ formatDate(record.approvalDate) }}
              </template>
              <template v-else-if="column.key === 'participants'">
                {{ formatParticipants(record.participants) }}
              </template>
              <template v-else-if="column.key === 'ifReviewer'">
                <a-tag :color="record.ifReviewer ? 'green' : 'orange'">
                  {{ record.ifReviewer ? '已审核' : '未审核' }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </ZyModal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onBeforeUnmount, watch } from 'vue'
import { PlusOutlined, SearchOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import ZyModal from '@/components/common/ZyModal.vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'

// 导入API
import {
  getProjectLevels,
  getLevelsWithCount,
  getProjectLevelDetail,
  createProjectLevel,
  updateProjectLevel,
  deleteProjectLevel,
  getProjectsByLevel
} from '@/api/modules/api.teachingReformProjectLevels'

// 表格列定义
const columns = [
  {
    title: '级别名称',
    dataIndex: 'levelName',
    key: 'levelName',
    sorter: true,
    width: '200px',
  },
  {
    title: '分数',
    dataIndex: 'score',
    key: 'score',
    sorter: (a, b) => a.score - b.score,
    width: '100px',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true,
  },
  {
    title: '项目数量',
    dataIndex: 'projectCount',
    key: 'projectCount',
    sorter: (a, b) => a.projectCount - b.projectCount,
    width: '100px',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    sorter: true,
    width: '150px',
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    sorter: true,
    width: '150px',
  },
  {
    title: '操作',
    key: 'action',
    width: '150px',
    fixed: 'right',
  },
]

// 项目列表列定义
const projectColumns = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
    ellipsis: true,
    width: '300px',
  },
  {
    title: '项目编号',
    dataIndex: 'projectNumber',
    key: 'projectNumber',
    width: '120px',
  },
  {
    title: '获批日期',
    dataIndex: 'approvalDate',
    key: 'approvalDate',
    width: '120px',
  },
  {
    title: '参与人员',
    key: 'participants',
    width: '200px',
  },
  {
    title: '审核状态',
    key: 'ifReviewer',
    width: '100px',
  },
]

// 数据源
const dataSource = ref([])
const isLoading = ref(false)
const searchValue = ref('')

// 计算过滤后的数据源
const filteredDataSource = computed(() => {
  if (!searchValue.value) return dataSource.value
  
  return dataSource.value.filter(item => 
    item.levelName.toLowerCase().includes(searchValue.value.toLowerCase()) ||
    (item.remark && item.remark.toLowerCase().includes(searchValue.value.toLowerCase()))
  )
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100'],
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)

// 项目列表模态框
const projectsModalVisible = ref(false)
const selectedLevel = ref(null)
const levelProjects = ref([])
const projectsLoading = ref(false)

// 图表相关
const chartRef = ref(null)
let chart = null

// 表单引用
const formRef = ref(null)

// 表单数据
const formState = reactive({
  levelName: '',
  score: 0,
  remark: ''
})

// 表单验证规则
const rules = {
  levelName: [
    { required: true, message: '请输入级别名称', trigger: 'blur' },
    { min: 2, max: 50, message: '级别名称长度在2-50个字符之间', trigger: 'blur' }
  ],
  score: [
    { required: true, message: '请输入级别分数', trigger: 'change' },
    { type: 'number', message: '级别分数必须为数字', trigger: 'change' }
  ]
}

// 添加错误状态
const errorMessage = ref('')

// 获取列表数据
const fetchData = async () => {
  isLoading.value = true
  errorMessage.value = ''
  
  try {
    // 获取带有项目数量的级别列表
    const response = await getLevelsWithCount()
    
    if (response && response.code === 200) {
      dataSource.value = response.data || []
      pagination.total = dataSource.value.length
      
      // 初始化图表
      initChart()
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = '获取级别列表失败：' + (response?.message || '未知错误')
    }
  } catch (error) {
    console.error('获取项目级别列表失败:', error)
    message.error('获取项目级别列表失败: ' + (error.message || '未知错误'))
    errorMessage.value = '获取级别列表失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 销毁旧图表
  if (chart) {
    chart.dispose()
  }
  
  // 创建新图表
  chart = echarts.init(chartRef.value)
  
  // 处理数据
  const chartData = dataSource.value.map(item => ({
    name: item.levelName,
    value: item.projectCount || 0
  }))
  
  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 10,
      data: chartData.map(item => item.name)
    },
    series: [
      {
        name: '项目数量',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData
      }
    ]
  }
  
  // 应用图表选项
  chart.setOption(option)
  
  // 窗口大小变化时自适应
  const resizeChart = () => {
    chart && chart.resize()
  }
  
  window.addEventListener('resize', resizeChart)
  
  // 组件卸载时移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('resize', resizeChart)
    chart && chart.dispose()
  })
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  
  // 处理排序
  if (sorter.field && sorter.order) {
    // 在实际应用中，这里可能需要调用后端接口进行排序
    if (sorter.field === 'levelName') {
      dataSource.value.sort((a, b) => {
        return sorter.order === 'ascend' 
          ? a.levelName.localeCompare(b.levelName)
          : b.levelName.localeCompare(a.levelName)
      })
    } else if (sorter.field === 'createdAt' || sorter.field === 'updatedAt') {
      dataSource.value.sort((a, b) => {
        const dateA = new Date(a[sorter.field])
        const dateB = new Date(b[sorter.field])
        return sorter.order === 'ascend' ? dateA - dateB : dateB - dateA
      })
    }
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
}

// 重置搜索
const handleReset = () => {
  searchValue.value = ''
  pagination.current = 1
}

// 添加新级别
const showAddModal = () => {
  isEdit.value = false
  currentRecord.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑级别
const handleEdit = async (record) => {
  isEdit.value = true
  currentRecord.value = record
  resetForm()
  
  try {
    const response = await getProjectLevelDetail(record.id)
    
    if (response && response.code === 200) {
      const detail = response.data
      
      formState.levelName = detail.levelName
      formState.score = detail.score
      formState.remark = detail.remark
      
      modalVisible.value = true
    } else {
      message.error(response?.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取级别详情失败:', error)
    message.error('获取级别详情失败: ' + (error.message || '未知错误'))
  }
}

// 查看级别下的项目
const showProjectsInLevel = async (record) => {
  if (!record || record.projectCount <= 0) return
  
  selectedLevel.value = record
  projectsModalVisible.value = true
  projectsLoading.value = true
  levelProjects.value = []
  
  try {
    const response = await getProjectsByLevel(record.id)
    
    if (response && response.code === 200) {
      levelProjects.value = response.data || []
    } else {
      message.error(response?.message || '获取项目列表失败')
    }
  } catch (error) {
    console.error('获取级别项目列表失败:', error)
    message.error('获取级别项目列表失败: ' + (error.message || '未知错误'))
  } finally {
    projectsLoading.value = false
  }
}

// 关闭项目列表模态框
const closeProjectsModal = () => {
  projectsModalVisible.value = false
  selectedLevel.value = null
  levelProjects.value = []
}

// 处理删除
const handleDelete = async (record) => {
  if (record.projectCount && record.projectCount > 0) {
    message.error(`该级别下有${record.projectCount}个项目，无法删除`)
    return
  }
  
  try {
    const response = await deleteProjectLevel(record.id)
    
    if (response && response.code === 200) {
      message.success('删除成功')
      fetchData()
    } else {
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('删除项目级别失败:', error)
    message.error('删除项目级别失败: ' + (error.message || '未知错误'))
  }
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate().then(async () => {
    try {
      confirmLoading.value = true
      
      let response
      if (isEdit.value) {
        response = await updateProjectLevel(currentRecord.value.id, {
          levelName: formState.levelName,
          score: formState.score,
          remark: formState.remark
        })
      } else {
        response = await createProjectLevel({
          levelName: formState.levelName,
          score: formState.score,
          remark: formState.remark
        })
      }
      
      if (response && response.code === 200) {
        message.success(isEdit.value ? '更新成功' : '添加成功')
        modalVisible.value = false
        fetchData()
      } else {
        message.error(response?.message || (isEdit.value ? '更新失败' : '添加失败'))
      }
    } catch (error) {
      console.error(isEdit.value ? '更新项目级别失败:' : '添加项目级别失败:', error)
      message.error(isEdit.value ? '更新项目级别失败: ' + (error.message || '未知错误') : '添加项目级别失败: ' + (error.message || '未知错误'))
    } finally {
      confirmLoading.value = false
    }
  }).catch(errors => {
    console.log('表单验证失败', errors)
    const errorFields = errors.errorFields || []
    errorFields.forEach(field => {
      message.error(`${field.name.join('.')}：${field.errors.join(', ')}`)
    })
  })
}

// 导出级别数据
const handleExport = () => {
  try {
    if (dataSource.value.length === 0) {
      message.warning('没有数据可导出')
      return
    }
    
    // 创建导出数据
    const data = dataSource.value.map(item => ({
      '级别名称': item.levelName,
      '分数': item.score,
      '项目数量': item.projectCount || 0,
      '备注': item.remark || '',
      '创建时间': formatDate(item.createdAt),
      '更新时间': formatDate(item.updatedAt)
    }))
    
    // 创建CSV内容
    const headers = Object.keys(data[0])
    let csvContent = headers.join(',') + '\n'
    
    data.forEach(row => {
      const rowContent = headers.map(header => {
        let cell = row[header] !== undefined ? row[header] : ''
        // 如果内容包含逗号、换行或引号，需要用引号包裹并处理引号
        if (cell.toString().includes(',') || cell.toString().includes('\n') || cell.toString().includes('"')) {
          cell = '"' + cell.toString().replace(/"/g, '""') + '"'
        }
        return cell
      }).join(',')
      csvContent += rowContent + '\n'
    })
    
    // 创建下载链接
    const encodedUri = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvContent)
    const link = document.createElement('a')
    link.setAttribute('href', encodedUri)
    link.setAttribute('download', '教学改革项目级别列表.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    message.success('导出成功')
  } catch (error) {
    console.error('导出级别列表失败:', error)
    message.error('导出级别列表失败: ' + (error.message || '未知错误'))
  }
}

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
}

// 重置表单
const resetForm = () => {
  formState.levelName = ''
  formState.score = 0
  formState.remark = ''
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 格式化参与者
const formatParticipants = (participants) => {
  if (!participants || participants.length === 0) return '-'
  
  return participants.map(p => {
    const userName = p.user?.nickname || p.user?.username || '未知'
    return p.isLeader ? `${userName} [主持]` : userName
  }).join(', ')
}

// 加载数据
onMounted(() => {
  fetchData()
})

// 监听窗口大小变化，自适应图表大小
watch([() => window.innerWidth, () => window.innerHeight], () => {
  chart && chart.resize()
})
</script>

<style scoped>
.project-levels {
  margin: 24px;
}

.text-danger {
  color: #ff4d4f;
}

.text-disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

.level-form {
  padding: 20px;
}

/* 图表容器样式 */
.chart-container {
  height: 400px;
  margin-top: 24px;
}
</style> 