const { Op, Sequelize } = require('sequelize');
const userModel = require('../../../models/v1/mapping/userModel');
const logger = require('../../../utils/logger');
const { getTimeIntervalByName, getUserInfoFromRequest, getSequelizeInstance } = require('../../../utils/others');
const highLevelPapersModel = require('../../../models/v1/mapping/highLevelPapersModel');

const HighLevelPaper = highLevelPapersModel;

/**
 * 创建并更新排名缓存表
 * 调用存储过程重新计算三种情况下的排名数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateRankingTables = async (req, res) => {
    logger.info('更新排名缓存表：', req.body);
    try {
        // // 获取当前用户信息
        // const userInfo = await getUserInfoFromRequest(req);
        // // 检查当前用户是否有权限（只有超级管理员可以执行）
        // if (!['SUPER'].includes(userInfo.role.roleAuth)) {
        //     return res.status(403).json({
        //         code: 403,
        //         message: '您没有权限更新排名缓存表',
        //         data: null
        //     });
        // }

        // 使用工具函数获取数据库连接实例
        let sequelize;
        try {
            sequelize = getSequelizeInstance(HighLevelPaper);
        } catch (error) {
            return res.status(500).json({
                code: 500,
                message: '无法获取数据库连接实例',
                error: error.message
            });
        }

        // 直接调用update_ranking_tables存储过程
        await sequelize.query('CALL update_ranking_tables()');

        logger.info('排名缓存表已更新');

        return res.status(200).json({
            code: 200,
            message: '排名缓存表更新成功',
            data: {
                tables: ['user_ranking_reviewed_in', 'user_ranking_reviewed_out', 'user_ranking_reviewed_all'],
                updateTime: new Date()
            }
        });
    } catch (error) {
        logger.error('更新排名缓存表失败:', error);
        return res.status(500).json({
            code: 500,
            message: `更新排名缓存表失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取用户排名数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserRankings = async (req, res) => {
    try {
        const { range = 'all', page = 1, limit = 10, userId } = req.query;

        // 参数验证
        if (!['in', 'out', 'all'].includes(range)) {
            return res.status(400).json({
                code: 400,
                message: '参数错误：range参数必须是 in, out 或 all',
                data: null
            });
        }

        // 确定要查询的表名
        const tableName = `user_ranking_reviewed_${range}`;

        const sequelize = await getSequelizeInstance(HighLevelPaper);

        let query = `SELECT * FROM ${tableName}`;
        let countQuery = `SELECT COUNT(*) as total FROM ${tableName}`;
        let replacements = [];

        // 如果指定了用户ID，只查询特定用户
        if (userId) {
            query += ` WHERE userId = ?`;
            countQuery += ` WHERE userId = ?`;
            replacements.push(userId);
        }

        // 添加排序和分页
        query += ` ORDER BY \`rank\` ASC LIMIT ? OFFSET ?`;
        replacements.push(parseInt(limit), (parseInt(page) - 1) * parseInt(limit));

        // 执行查询
        const [rankings, countResult] = await Promise.all([
            sequelize.query(query, { replacements, type: sequelize.QueryTypes.SELECT }),
            sequelize.query(countQuery, { replacements: userId ? [userId] : [], type: sequelize.QueryTypes.SELECT })
        ]);

        const total = countResult[0].total;
        const pages = Math.ceil(total / limit);

        return res.status(200).json({
            code: 200,
            message: '获取排名数据成功',
            result: {
                records: rankings,
                total: parseInt(total),
                size: parseInt(limit),
                current: parseInt(page),
                pages: parseInt(pages)
            }
        });
    } catch (error) {
        logger.error('获取排名数据失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取排名数据失败: ${error.message}`,
            data: null
        });
    }
};
