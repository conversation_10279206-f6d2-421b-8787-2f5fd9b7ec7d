const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const textbookCategoriesModel = require('../../../models/v1/mapping/textbookCategoriesModel');
const textbooksModel = require('../../../models/v1/mapping/textbooksModel');

/**
 * 获取教材与著作类别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTextbookCategories = async (req, res) => {
  try {
    const categories = await textbookCategoriesModel.findAll({
      order: [['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: categories
    });
  } catch (error) {
    console.error('获取教材与著作类别列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教材与著作类别列表失败',
      error: error.message
    });
  }
};

/**
 * 获取教材与著作类别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTextbookCategoryDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少类别ID',
        data: null
      });
    }
    
    // 查询类别详情
    const category = await textbookCategoriesModel.findByPk(id);
    
    if (!category) {
      return res.status(404).json({
        code: 404,
        message: '未找到教材与著作类别',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: category
    });
  } catch (error) {
    console.error('获取教材与著作类别详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教材与著作类别详情失败',
      error: error.message
    });
  }
};

/**
 * 创建教材与著作类别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createTextbookCategory = async (req, res) => {
  try {
    const { categoryAndPosition, score } = req.body;
    
    // 验证必要字段
    if (!categoryAndPosition) {
      return res.status(400).json({
        code: 400,
        message: '类别名称不能为空',
        data: null
      });
    }
    
    if (!score) {
      return res.status(400).json({
        code: 400,
        message: '分数不能为空',
        data: null
      });
    }
    
    // 检查类别名称是否已存在
    const existingCategory = await textbookCategoriesModel.findOne({
      where: {
        categoryAndPosition: categoryAndPosition
      }
    });
    
    if (existingCategory) {
      return res.status(400).json({
        code: 400,
        message: '类别名称已存在',
        data: null
      });
    }
    
    // 创建教材与著作类别
    const category = await textbookCategoriesModel.create({
      id: uuidv4(),
      categoryAndPosition,
      score,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: category
    });
  } catch (error) {
    console.error('创建教材与著作类别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建教材与著作类别失败',
      error: error.message
    });
  }
};

/**
 * 更新教材与著作类别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateTextbookCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { categoryAndPosition, score } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少类别ID',
        data: null
      });
    }
    
    // 查询类别是否存在
    const category = await textbookCategoriesModel.findByPk(id);
    
    if (!category) {
      return res.status(404).json({
        code: 404,
        message: '未找到教材与著作类别',
        data: null
      });
    }
    
    // 如果要更新类别名称，检查新名称是否已存在
    if (categoryAndPosition && categoryAndPosition !== category.categoryAndPosition) {
      const existingCategory = await textbookCategoriesModel.findOne({
        where: {
          categoryAndPosition: categoryAndPosition,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingCategory) {
        return res.status(400).json({
          code: 400,
          message: '类别名称已存在',
          data: null
        });
      }
    }
    
    // 更新类别
    const updateData = {};
    if (categoryAndPosition !== undefined) updateData.categoryAndPosition = categoryAndPosition;
    if (score !== undefined) updateData.score = score;
    updateData.updatedAt = new Date();
    
    await category.update(updateData);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: await textbookCategoriesModel.findByPk(id)
    });
  } catch (error) {
    console.error('更新教材与著作类别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新教材与著作类别失败',
      error: error.message
    });
  }
};

/**
 * 删除教材与著作类别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteTextbookCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少类别ID',
        data: null
      });
    }
    
    // 查询类别是否存在
    const category = await textbookCategoriesModel.findByPk(id);
    
    if (!category) {
      return res.status(404).json({
        code: 404,
        message: '未找到教材与著作类别',
        data: null
      });
    }
    
    // 检查是否有教材与著作使用此类别
    const textbooksCount = await textbooksModel.count({
      where: { categoryId: id }
    });
    
    if (textbooksCount > 0) {
      return res.status(400).json({
        code: 400,
        message: `该类别已被${textbooksCount}个教材与著作记录使用，无法删除`,
        data: null
      });
    }
    
    // 删除类别
    await category.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除教材与著作类别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除教材与著作类别失败',
      error: error.message
    });
  }
};

/**
 * 获取所有类别及其教材与著作数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCategoriesWithCount = async (req, res) => {
  try {
    // 获取所有类别
    const categories = await textbookCategoriesModel.findAll({
      order: [['createdAt', 'ASC']]
    });
    
    // 获取每个类别的教材与著作数量
    const result = await Promise.all(categories.map(async (category) => {
      const count = await textbooksModel.count({
        where: { categoryId: category.id }
      });
      
      return {
        ...category.toJSON(),
        textbooksCount: count
      };
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取类别及教材与著作数量失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取类别及教材与著作数量失败',
      error: error.message
    });
  }
}; 