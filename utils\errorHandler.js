const apiResponse = require('./apiResponse');
const logger = require('./logger');
const chalk = require('chalk');

// 全局错误处理中间件
const errorHandler = (err, req, res, next) => {
    if (typeof err === 'string') {
        // 自定义错误
        return apiResponse.errorResponse(res, err);
    }

    if (err.name === 'ValidationError') {
        // mongoose验证错误
        return apiResponse.validationErrorResponse(res, err.message);
    }

    if (err.name === 'UnauthorizedError') {
        // jwt认证错误
        return apiResponse.unauthorizedResponse(res, err.message);
    }

    // 默认服务器错误
    return apiResponse.errorResponse(res, err.message);
}

module.exports = errorHandler; 