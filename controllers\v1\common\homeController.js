const teacherModel = require('../../../models/v1/mapping/teacherModel');
const researchFundModel = require('../../../models/v1/mapping/researchFundModel');
const awardModel = require('../../../models/v1/mapping/awardsModel');
const internationalExchangeModel = require('../../../models/v1/mapping/internationalExchangesModel');
const socialServiceModel = require('../../../models/v1/mapping/socialServicesModel');
const employmentQualityModel = require('../../../models/v1/mapping/employmentQualityModel');
const deductionModel = require('../../../models/v1/mapping/deductionModel');
const scoreRecordModel = require('../../../models/v1/mapping/scoreRecordModel');
const userOptLogModel = require('../../../models/v1/mapping/userOptLogModel');
const notificationsModel = require('../../../models/v1/mapping/notificationsModel');
const { getUserInfoFromRequest } = require('../../../utils/others');

const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userModel = require('../../../models/v1/mapping/userModel');

const academicAppointmentsModel = require('../../../models/v1/mapping/academicAppointmentsModel');
const conferencesModel = require('../../../models/v1/mapping/conferencesModel');
const patentsModel = require('../../../models/v1/mapping/patentsModel');
const studentProjectsModel = require('../../../models/v1/mapping/studentProjectGuidanceProjectsModel');
const studentAwardsModel = require('../../../models/v1/mapping/studentAwardGuidanceAwardsModel');
const teachingProjectsModel = require('../../../models/v1/mapping/teachingReformProjectsModel');
const textbooksModel = require('../../../models/v1/mapping/textbooksModel');
const researchProjectModel = require('../../../models/v1/mapping/researchProjectModel');
const highLevelPapersModel = require('../../../models/v1/mapping/highLevelPapersModel');
const teachingWorkloadsModel = require('../../../models/v1/mapping/teachingWorkloadsModel');
const teachingResearchAwardsModel = require('../../../models/v1/mapping/teachingResearchAwardsModel');

const teachingResearchAwardParticipantsModel = require('../../../models/v1/mapping/teachingResearchAwardParticipantsModel');
const teachingWorkloadParticipantsModel = require('../../../models/v1/mapping/teachingWorkloadParticipantsModel');
const conferenceParticipantsModel = require('../../../models/v1/mapping/conferenceParticipantsModel');
const patentParticipantsModel = require('../../../models/v1/mapping/patentParticipantsModel');
const highLevelPaperParticipantsModel = require('../../../models/v1/mapping/highLevelPaperParticipantsModel');
const researchProjectParticipantsModel = require('../../../models/v1/mapping/researchProjectParticipantsModel');  
const studentProjectGuidanceParticipantsModel = require('../../../models/v1/mapping/studentProjectGuidanceParticipantsModel');
const studentAwardGuidanceParticipantsModel = require('../../../models/v1/mapping/studentAwardGuidanceParticipantsModel');
const teachingReformProjectParticipantsModel = require('../../../models/v1/mapping/teachingReformParticipantsModel');

const { Op, Sequelize } = require('sequelize');

/**
 * 获取首页统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getHomeStats = async (req, res) => {
  try {
    const { year } = req.body;
    // 默认使用当前年份
    const currentYear = year || new Date().getFullYear().toString();

    // 获取教师总数
    const teacherCount = await teacherModel.count({
      where: { status: 1 }
    });

    // 获取科研项目数量
    const researchProjectCount = await researchProjectModel.count({
      where: {
        startDate: currentYear,
        status: 1
      }
    });

    // 获取科研经费总额
    const researchFundStats = await researchFundModel.findOne({
      attributes: [
        [Sequelize.fn('SUM', Sequelize.col('amount')), 'totalAmount'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        startDate: currentYear,
        status: 1
      },
      raw: true
    });

    // 获取高水平论文数量
    const paperCount = await highLevelPapersModel.count({
      where: {
        publishDate: currentYear,
        status: 1
      }
    });

    // 获取获奖数量
    const awardCount = await awardModel.count({
      where: {
        awardDate: currentYear,
        status: 1
      }
    });

    // 获取国际交流数量
    const exchangeCount = await internationalExchangeModel.count({
      where: {
        startDate: {
          [Op.between]: [`${currentYear}-01-01`, `${currentYear}-12-31`]
        },
        status: 1
      }
    });

    // 获取社会服务数量
    const serviceCount = await socialServiceModel.count({
      where: {
        startDate: {
          [Op.between]: [`${currentYear}-01-01`, `${currentYear}-12-31`]
        },
        status: 1
      }
    });

    // 获取就业质量数据
    const employmentQuality = await employmentQualityModel.findAll({
      attributes: [
        [Sequelize.fn('AVG', Sequelize.col('employmentRate')), 'avgEmploymentRate'],
        [Sequelize.fn('AVG', Sequelize.col('averageSalary')), 'avgSalary'],
        [Sequelize.fn('AVG', Sequelize.col('majorMatchRate')), 'avgMajorMatchRate'],
        [Sequelize.fn('AVG', Sequelize.col('employmentSatisfaction')), 'avgEmploymentSatisfaction']
      ],
      where: {
        year: currentYear
      },
      raw: true
    });

    // 获取绩效得分情况
    const scoreStats = await scoreRecordModel.findOne({
      attributes: [
        [Sequelize.fn('AVG', Sequelize.col('totalScore')), 'avgScore'],
        [Sequelize.fn('MAX', Sequelize.col('totalScore')), 'maxScore'],
        [Sequelize.fn('MIN', Sequelize.col('totalScore')), 'minScore'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        year: currentYear
      },
      raw: true
    });

    // 计算就业率
    const overallEmploymentRate = employmentQuality.totalGraduates && employmentQuality.totalGraduates > 0
      ? (employmentQuality.totalEmployed / employmentQuality.totalGraduates * 100).toFixed(2)
      : 0;

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        startDate: currentYear,
        teacherCount: teacherCount || 0,
        researchStats: {
          projectCount: researchProjectCount || 0,
          fundCount: researchFundStats.count || 0,
          fundAmount: researchFundStats.totalAmount || 0,
          paperCount: paperCount || 0
        },
        awardCount: awardCount || 0,
        exchangeCount: exchangeCount || 0,
        serviceCount: serviceCount || 0,
        employmentStats: {
          totalGraduates: employmentQuality.totalGraduates || 0,
          totalEmployed: employmentQuality.totalEmployed || 0,
          employmentRate: overallEmploymentRate,
          averageSalary: employmentQuality.avgSalary || 0
        },
        scoreStats: {
          count: scoreStats.count || 0,
          averageScore: scoreStats.avgScore || 0,
          maxScore: scoreStats.maxScore || 0,
          minScore: scoreStats.minScore || 0
        }
      }
    });
  } catch (error) {
    console.error('获取首页统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取首页统计数据失败',
      data: null
    });
  }
}


/**
 * 获取绩效趋势数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTrendData = async (req, res) => {
  try {
    const { startYear, endYear } = req.query;

    // 默认查询近5年数据
    const currentYear = new Date().getFullYear();
    const queryStartYear = startYear || (currentYear - 4).toString();
    const queryEndYear = endYear || currentYear.toString();

    // 生成年份列表
    const years = [];
    for (let year = parseInt(queryStartYear); year <= parseInt(queryEndYear); year++) {
      years.push(year.toString());
    }

    // 按年份查询科研项目数量
    const projectTrend = await Promise.all(
      years.map(async (year) => {
        const count = await researchProjectModel.count({
          where: {
            year,
            status: 1
          }
        });
        return { year, count };
      })
    );

    // 按年份查询科研经费总额
    const fundTrend = await Promise.all(
      years.map(async (year) => {
        const result = await researchFundModel.findOne({
          attributes: [
            [Sequelize.fn('SUM', Sequelize.col('amount')), 'totalAmount']
          ],
          where: {
            year,
            status: 1
          },
          raw: true
        });
        return { year, amount: result.totalAmount || 0 };
      })
    );

    // 按年份查询高水平论文数量
    const paperTrend = await Promise.all(
      years.map(async (year) => {
        const count = await highLevelPapersModel.count({
          where: {
            year,
            status: 1
          }
        });
        return { year, count };
      })
    );

    // 按年份查询获奖数量
    const awardTrend = await Promise.all(
      years.map(async (year) => {
        const count = await awardModel.count({
          where: {
            year,
            status: 1
          }
        });
        return { year, count };
      })
    );

    // 按年份查询就业率趋势
    const employmentTrend = await Promise.all(
      years.map(async (year) => {
        const result = await employmentQualityModel.findOne({
          attributes: [
            [Sequelize.fn('AVG', Sequelize.col('employmentRate')), 'avgEmploymentRate']
          ],
          where: { year },
          raw: true
        });
        return { year, rate: result.avgEmploymentRate || 0 };
      })
    );

    // 按年份查询平均绩效得分
    const scoreTrend = await Promise.all(
      years.map(async (year) => {
        const result = await scoreRecordModel.findOne({
          attributes: [
            [Sequelize.fn('AVG', Sequelize.col('totalScore')), 'avgScore']
          ],
          where: { year },
          raw: true
        });
        return { year, score: result.avgScore || 0 };
      })
    );

    return res.status(200).json({
      code: 200,
      message: '获取绩效趋势数据成功',
      data: {
        years,
        projectTrend,
        fundTrend,
        paperTrend,
        awardTrend,
        employmentTrend,
        scoreTrend
      }
    });
  } catch (error) {
    console.error('获取绩效趋势数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取绩效趋势数据失败',
      data: null
    });
  }
};

/**
 * 获取教师分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherDistribution = async (req, res) => {
  try {
    // 按职称分布
    const titleDistribution = await teacherModel.findAll({
      attributes: [
        'title',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        status: 1,
        title: {
          [Op.ne]: null
        }
      },
      group: ['title'],
      raw: true
    });

    // 按学院/部门分布
    const departmentDistribution = await teacherModel.findAll({
      attributes: [
        'department',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        status: 1,
        department: {
          [Op.ne]: null
        }
      },
      group: ['department'],
      raw: true
    });

    // 按学历分布
    const educationDistribution = await teacherModel.findAll({
      attributes: [
        'education',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        status: 1,
        education: {
          [Op.ne]: null
        }
      },
      group: ['education'],
      raw: true
    });

    // 按年龄段分布
    const now = new Date();
    const ageRanges = [
      { range: '30岁以下', min: now.getFullYear() - 30, max: now.getFullYear() },
      { range: '30-40岁', min: now.getFullYear() - 40, max: now.getFullYear() - 30 },
      { range: '40-50岁', min: now.getFullYear() - 50, max: now.getFullYear() - 40 },
      { range: '50岁以上', min: now.getFullYear() - 100, max: now.getFullYear() - 50 }
    ];

    const ageDistribution = await Promise.all(
      ageRanges.map(async ({ range, min, max }) => {
        const count = await teacherModel.count({
          where: {
            status: 1,
            birthYear: {
              [Op.between]: [min, max]
            }
          }
        });
        return { range, count };
      })
    );

    return res.status(200).json({
      code: 200,
      message: '获取教师分布数据成功',
      data: {
        titleDistribution,
        departmentDistribution,
        educationDistribution,
        ageDistribution
      }
    });
  } catch (error) {
    console.error('获取教师分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师分布数据失败',
      data: null
    });
  }
};

/**
 * 获取排行榜数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRankingData = async (req, res) => {
  try {
    const { year, limit = 10 } = req.query;

    // 默认使用当前年份
    const currentYear = year || new Date().getFullYear().toString();

    // 教师绩效总分排行
    const teacherScoreRanking = await scoreRecordModel.findAll({
      attributes: ['teacherId', 'teacherName', 'totalScore'],
      where: {
        year: currentYear
      },
      order: [['totalScore', 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    // 科研项目数量排行
    const projectCountRanking = await researchProjectModel.findAll({
      attributes: [
        'leader',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        year: currentYear,
        status: 1
      },
      group: ['leader'],
      order: [[Sequelize.literal('count'), 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    // 科研经费排行
    const fundAmountRanking = await researchFundModel.findAll({
      attributes: [
        'leader',
        [Sequelize.fn('SUM', Sequelize.col('amount')), 'totalAmount']
      ],
      where: {
        year: currentYear,
        status: 1
      },
      group: ['leader'],
      order: [[Sequelize.literal('totalAmount'), 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    // 论文数量排行
    const paperCountRanking = await highLevelPapersModel.findAll({
      attributes: [
        'author',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        year: currentYear,
        status: 1
      },
      group: ['author'],
      order: [[Sequelize.literal('count'), 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    // 获奖数量排行
    const awardCountRanking = await awardModel.findAll({
      attributes: [
        'winner',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        year: currentYear,
        status: 1
      },
      group: ['winner'],
      order: [[Sequelize.literal('count'), 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    return res.status(200).json({
      code: 200,
      message: '获取排行榜数据成功',
      data: {
        year: currentYear,
        teacherScoreRanking,
        projectCountRanking,
        fundAmountRanking,
        paperCountRanking,
        awardCountRanking
      }
    });
  } catch (error) {
    console.error('获取排行榜数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取排行榜数据失败',
      data: null
    });
  }
};

/**
 * 获取最近通知
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRecentNotifications = async (req, res) => {
  try {
    const { limit = 5 } = req.body;
    const userId = req.user.id;

    // 查询最近的通知
    const notifications = await notificationModel.findAll({
      where: {
        status: 1, // 只查询有效的通知
        validStartTime: {
          [Op.lte]: new Date() // 已经开始的通知
        },
        [Op.or]: [
          { validEndTime: null }, // 无结束时间
          { validEndTime: { [Op.gte]: new Date() } } // 未过期
        ]
      },
      order: [['priority', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      include: [
        {
          model: userModel,
          as: 'publisher',
          attributes: ['id', 'username', 'nickname']
        },
        {
          model: userNotificationModel,
          as: 'userNotifications',
          where: { userId },
          required: false
        }
      ]
    });

    // 处理已读状态
    const formattedNotifications = notifications.map(notification => {
      const userNotification = notification.userNotifications && notification.userNotifications.length > 0
        ? notification.userNotifications[0]
        : null;

      return {
        ...notification.toJSON(),
        isRead: userNotification ? userNotification.isRead : false,
        readAt: userNotification ? userNotification.readAt : null,
        userNotifications: undefined
      };
    });

    return res.status(200).json({
      code: 200,
      message: '获取最近通知成功',
      data: formattedNotifications
    });
  } catch (error) {
    console.error('获取最近通知失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取最近通知失败',
      data: null
    });
  }
};

/**
 * 获取待办事项
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTodos = async (req, res) => {
  try {
    const { limit = 5 } = req.body;
    const userId = req.user.id;

    // 查询指定给当前用户的待办事项
    const todos = await todoModel.findAll({
      where: {
        status: 1, // 有效的待办
        assigneeId: userId,
        completed: false, // 未完成的待办
        dueDate: {
          [Op.gte]: new Date() // 未逾期的待办
        }
      },
      order: [['priority', 'DESC'], ['dueDate', 'ASC']],
      limit: parseInt(limit),
      include: [
        {
          model: userModel,
          as: 'creator',
          attributes: ['id', 'username', 'nickname']
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '获取待办事项成功',
      data: todos
    });
  } catch (error) {
    console.error('获取待办事项失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取待办事项失败',
      data: null
    });
  }
};

/**
 * 获取绩效趋势数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPerformanceTrend = async (req, res) => {
  try {
    const { startYear, endYear } = req.body;

    // 默认查询近5年数据
    const currentYear = new Date().getFullYear();
    const queryStartYear = startYear || (currentYear - 4).toString();
    const queryEndYear = endYear || currentYear.toString();

    // 生成年份列表
    const years = [];
    for (let year = parseInt(queryStartYear); year <= parseInt(queryEndYear); year++) {
      years.push(year.toString());
    }

    // 查询各年份的总分平均值
    const scoreTrend = await Promise.all(
      years.map(async (year) => {
        const result = await scoreRecordModel.findOne({
          attributes: [
            [Sequelize.fn('AVG', Sequelize.col('totalScore')), 'avgScore']
          ],
          where: { year },
          raw: true
        });
        return { year, score: parseFloat(result.avgScore || 0).toFixed(2) };
      })
    );

    // 查询各年份的科研项目数量
    const projectTrend = await Promise.all(
      years.map(async (year) => {
        const count = await researchProjectModel.count({
          where: {
            year,
            status: 1
          }
        });
        return { year, count };
      })
    );

    // 查询各年份的科研经费总额
    const fundTrend = await Promise.all(
      years.map(async (year) => {
        const result = await researchFundModel.findOne({
          attributes: [
            [Sequelize.fn('SUM', Sequelize.col('amount')), 'totalAmount']
          ],
          where: {
            year,
            status: 1
          },
          raw: true
        });
        return { year, amount: parseFloat(result.totalAmount || 0).toFixed(2) };
      })
    );

    return res.status(200).json({
      code: 200,
      message: '获取绩效趋势数据成功',
      data: {
        years,
        scoreTrend,
        projectTrend,
        fundTrend
      }
    });
  } catch (error) {
    console.error('获取绩效趋势数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取绩效趋势数据失败',
      data: null
    });
  }
};

/**
 * 获取部门排名数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDepartmentRank = async (req, res) => {
  try {
    const { year } = req.body;

    // 默认使用当前年份
    const currentYear = year || new Date().getFullYear().toString();

    // 查询各部门的平均得分
    const departmentScores = await scoreRecordModel.findAll({
      attributes: [
        'department',
        [Sequelize.fn('AVG', Sequelize.col('totalScore')), 'avgScore'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        year: currentYear,
        department: {
          [Op.ne]: null
        }
      },
      group: ['department'],
      order: [[Sequelize.literal('avgScore'), 'DESC']],
      raw: true
    });

    // 格式化数据
    const formattedData = departmentScores.map((item, index) => ({
      rank: index + 1,
      department: item.department,
      avgScore: parseFloat(item.avgScore).toFixed(2),
      count: item.count
    }));

    return res.status(200).json({
      code: 200,
      message: '获取部门排名数据成功',
      data: formattedData
    });
  } catch (error) {
    console.error('获取部门排名数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取部门排名数据失败',
      data: null
    });
  }
};

/**
 * 获取首页概览数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getHomeOverview = async (req, res) => {
  try {
    // 获取教师总数
    const teacherCount = await teacherModel.count({
      where: { status: 1 }
    });

    // 获取科研项目数量
    const projectCount = await researchProjectModel.count({
      where: { status: 1 }
    });

    // 获取高水平论文数量
    const paperCount = await highLevelPapersModel.count({
      where: { status: 1 }
    });

    // 获取获奖数量
    const awardCount = await awardModel.count({
      where: { status: 1 }
    });

    // 获取最近活动（从用户操作日志中获取）
    const recentActivities = await userOptLogModel.findAll({
      attributes: ['id', 'operatorId', 'module', 'address', 'createdAt'],
      include: [
        {
          model: userModel,
          attributes: ['id', 'username', 'nickname', 'avatar'],
          as: 'user',
          foreignKey: 'operatorId'
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: 10
    });

    // 格式化最近活动数据
    const formattedActivities = recentActivities.map(activity => {
      const activityData = activity.get({ plain: true });
      return {
        id: activityData.id,
        userId: activityData.userId,
        username: activityData.user ? activityData.user.username : '',
        nickname: activityData.user ? activityData.user.nickname : '',
        avatar: activityData.user ? activityData.user.avatar : '',
        operation: activityData.operation,
        ip: activityData.ip,
        createdAt: activityData.createdAt
      };
    });

    return res.status(200).json({
      code: 200,
      message: '获取首页概览数据成功',
      data: {
        totalTeachers: teacherCount || 0,
        totalProjects: projectCount || 0,
        totalPapers: paperCount || 0,
        totalAwards: awardCount || 0,
        recentActivities: formattedActivities
      }
    });
  } catch (error) {
    console.error('获取首页概览数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取首页概览数据失败',
      data: null
    });
  }
};

/**
 * 获取首页图表数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getHomeCharts = async (req, res) => {
  try {
    // 模拟数据，实际项目中应该从数据库获取
    const chartsData = {
      score_trend: {
        x_axis: ["2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06"],
        series: [
          {
            name: "平均分",
            data: [800, 820, 830, 840, 850, 870]
          }
        ]
      },
      dimension_distribution: {
        dimensions: ["科研项目", "科研经费", "论文", "获奖", "国际交流", "社会服务", "就业质量"],
        data: [250, 100, 150, 100, 50, 20, 90]
      }
    };

    res.json({
      code: 200,
      message: 'success',
      data: chartsData
    });
  } catch (error) {
    console.error('获取首页图表数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取首页图表数据失败',
      error: error.message
    });
  }
};

/**
 * 获取用户综合数据 - 包含个人评分、趋势和提升建议
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserDashboard = async (req, res) => {
  try {
    const { userId } = req.user;
    const { year = new Date().getFullYear() } = req.query;

    // 获取用户基本信息
    const user = await userModel.findByPk(userId, {
      attributes: ['id', 'name', 'department', 'title'],
      raw: true
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 获取用户当前年度评分
    const currentScore = await scoreRecordModel.findOne({
      where: {
        teacherId: userId,
        year: parseInt(year)
      },
      raw: true
    });

    if (!currentScore) {
      return res.status(404).json({
        code: 404,
        message: '未找到当前年度评分记录',
        data: null
      });
    }

    // 获取上一年度评分
    const prevYear = parseInt(year) - 1;
    const prevScore = await scoreRecordModel.findOne({
      where: {
        teacherId: userId,
        year: prevYear
      },
      attributes: ['totalScore'],
      raw: true
    });

    // 计算学院排名
    const schoolRank = await scoreRecordModel.count({
      where: {
        year: parseInt(year),
        totalScore: {
          [Op.gt]: currentScore.totalScore
        }
      }
    }) + 1;

    // 计算部门内排名
    const departmentRank = await scoreRecordModel.count({
      where: {
        year: parseInt(year),
        department: user.department,
        totalScore: {
          [Op.gt]: currentScore.totalScore
        }
      }
    }) + 1;

    // 获取部门排名数据
    const departmentScores = await scoreRecordModel.findAll({
      attributes: [
        'department',
        [Sequelize.fn('AVG', Sequelize.col('totalScore')), 'avgScore'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        year: parseInt(year)
      },
      group: ['department'],
      order: [[Sequelize.literal('avgScore'), 'DESC']],
      raw: true
    });

    // 3. 构建维度得分数据
    const dimensionScores = [
      {
        name: '论文发表',
        count: rankingData.paperCount || 0,
        score: parseFloat(rankingData.paperScore || 0),
        color: '#722ed1',
        icon: 'FileTextOutlined'
      },
      {
        name: '专利',
        count: rankingData.patentCount || 0,
        score: parseFloat(rankingData.patentScore || 0),
        color: '#eb2f96',
        icon: 'SafetyCertificateOutlined'
      },
      {
        name: '教材著作',
        count: rankingData.textbookCount || 0,
        score: parseFloat(rankingData.textbookScore || 0),
        color: '#a0d911',
        icon: 'BookOutlined'
      },
      {
        name: '教学改革项目',
        count: rankingData.teachingProjectCount || 0,
        score: parseFloat(rankingData.teachingProjectScore || 0),
        color: '#13c2c2',
        icon: 'HighlightOutlined'
      },
      {
        name: '学生获奖',
        count: rankingData.awardCount || 0,
        score: parseFloat(rankingData.awardScore || 0),
        color: '#faad14',
        icon: 'TrophyOutlined'
      },
      {
        name: '学生立项',
        count: rankingData.studentProjectCount || 0,
        score: parseFloat(rankingData.studentProjectScore || 0),
        color: '#eb2f96',
        icon: 'FundProjectionScreenOutlined'
      },
      {
        name: '学术会议',
        count: rankingData.conferenceCount || 0,
        score: parseFloat(rankingData.conferenceScore || 0),
        color: '#faad14',
        icon: 'GlobalOutlined'
      },
      {
        name: '学术任职',
        count: rankingData.appointmentCount || 0,
        score: parseFloat(rankingData.appointmentScore || 0),
        color: '#1890ff',
        icon: 'TeamOutlined'
      },
      {
        name: '科研项目',
        count: rankingData.researchCount || 0,
        score: parseFloat(rankingData.researchScore || 0),
        color: '#1890ff',
        icon: 'FundProjectionScreenOutlined'
      },
      {
        name: '教学工作量',
        count: rankingData.teachingWorkloadCount || 0,
        score: parseFloat(rankingData.teachingWorkloadScore || 0),
        color: '#52c41a',
        icon: 'ScheduleOutlined'
      },
      {
        name: '教学科研奖励',
        count: rankingData.teachingResearchAwardCount || 0,
        score: parseFloat(rankingData.teachingResearchAwardScore || 0),
        color: '#f5222d',
        icon: 'TrophyOutlined'
      }
    ];

    // 获取历史评分趋势
    const startYear = parseInt(year) - 4;
    const endYear = parseInt(year);
    const years = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);

    // 获取用户历年评分
    const userScoreTrend = await scoreRecordModel.findAll({
      attributes: ['year', 'totalScore'],
      where: {
        teacherId: userId,
        year: {
          [Op.between]: [startYear, endYear]
        }
      },
      order: [['year', 'ASC']],
      raw: true
    });

    // 将查询结果转换为年份-分数映射
    const userScoreMap = new Map(userScoreTrend.map(item => [item.year, parseFloat(item.totalScore)]));

    // 获取部门历年平均分
    const deptScoreTrend = await scoreRecordModel.findAll({
      attributes: [
        'year',
        [Sequelize.fn('AVG', Sequelize.col('totalScore')), 'avgScore']
      ],
      where: {
        department: user.department,
        year: {
          [Op.between]: [startYear, endYear]
        }
      },
      group: ['year'],
      order: [['year', 'ASC']],
      raw: true
    });

    // 将查询结果转换为年份-分数映射
    const deptScoreMap = new Map(deptScoreTrend.map(item => [item.year, parseFloat(item.avgScore)]));

    // 填充趋势数据
    const scoreTrend = years.map(year => ({
      year,
      userScore: userScoreMap.get(year) || null,
      departmentAvgScore: deptScoreMap.get(year) || null
    }));

    // 生成提升建议
    const improvementSuggestions = [];

    // 根据维度得分生成建议
    dimensionScores.forEach(dimension => {
      if (dimension.percentage < 60) {
        let suggestion = '';
        switch (dimension.name) {
          case '科研项目':
            suggestion = '您的科研项目得分较低，建议申报更高级别的科研项目，参与更多国家级或省部级项目。';
            break;
          case '论文成果':
            suggestion = '您的论文成果得分较低，建议提高论文发表质量，争取在高水平期刊上发表更多论文。';
            break;
          case '专利成果':
            suggestion = '您的专利成果得分较低，建议增加发明专利申请数量，提高专利的转化率。';
            break;
          case '获奖情况':
            suggestion = '您的获奖情况得分较低，建议积极参与各类科研竞赛和评选活动，争取更多奖项。';
            break;
          case '教学情况':
            suggestion = '您的教学情况得分较低，建议提升教学质量和教学创新，申报更多教学改革项目。';
            break;
        }
        improvementSuggestions.push({
          dimension: dimension.name,
          score: dimension.score,
          suggestion
        });
      }
    });

    // 如果没有低分维度，提供一般性建议
    if (improvementSuggestions.length === 0) {
      // 找出得分最低的维度
      const lowestDimension = [...dimensionScores].sort((a, b) => a.percentage - b.percentage)[0];

      improvementSuggestions.push({
        dimension: lowestDimension.name,
        score: lowestDimension.score,
        suggestion: `您的整体表现不错，但${lowestDimension.name}维度仍有提升空间，建议进一步加强该方面的工作。`
      });
    }

    // 构建响应数据
    const responseData = {
      // 基本信息
      basic_info: {
        user_id: userId,
        name: user.name,
        department: user.department,
        title: user.title,
        total_score: parseFloat(currentScore.totalScore),
        previous_score: prevScore ? parseFloat(prevScore.totalScore) : null,
        score_change: prevScore ? parseFloat((currentScore.totalScore - prevScore.totalScore).toFixed(2)) : null,
        school_rank: schoolRank,
        department_rank: departmentRank
      },
      // 维度得分
      dimension_scores: dimensionScores,
      // 历史趋势
      score_trend: scoreTrend,
      // 改进建议
      improvement_suggestions: improvementSuggestions,
      // 部门排名
      department_ranking: departmentScores.map(dept => ({
        department: dept.department,
        avg_score: parseFloat(dept.avgScore),
        teacher_count: parseInt(dept.count)
      }))
    };

    return res.status(200).json({
      code: 200,
      message: '获取用户综合数据成功',
      data: responseData
    });

  } catch (error) {
    console.error('获取用户综合数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户综合数据失败',
      data: null
    });
  }
};

/**
 * 获取教师职称分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherTitles = async (req, res) => {
  try {
    const { department } = req.query;

    // 构建查询条件
    const whereCondition = department ? { department, status: 1 } : { status: 1 };

    // 查询教师职称分布
    const titleDistribution = await teacherModel.findAll({
      attributes: [
        'title',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: whereCondition,
      group: ['title'],
      raw: true
    });

    // 格式化数据
    const formattedData = titleDistribution.map(item => ({
      name: item.title,
      value: parseInt(item.count)
    }));

    return res.status(200).json({
      code: 200,
      message: '获取教师职称分布成功',
      data: formattedData
    });

  } catch (error) {
    console.error('获取教师职称分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师职称分布失败',
      data: null
    });
  }
};

/**
 * 获取项目级别分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectLevels = async (req, res) => {
  try {
    const { year = new Date().getFullYear(), department } = req.query;

    // 构建查询条件
    let whereCondition = {
      startDate: {
        [Op.between]: [`${year}-01-01`, `${year}-12-31`]
      },
      status: 1
    };

    if (department) {
      // 查询该部门的教师
      const teachers = await teacherModel.findAll({
        attributes: ['name'],
        where: { department },
        raw: true
      });

      const teacherNames = teachers.map(t => t.name);

      whereCondition.leader = {
        [Op.in]: teacherNames
      };
    }

    // 查询项目级别分布
    const levelDistribution = await researchProjectModel.findAll({
      attributes: [
        'level',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: whereCondition,
      group: ['level'],
      raw: true
    });

    // 格式化数据
    const formattedData = levelDistribution.map(item => ({
      name: item.level,
      value: parseInt(item.count)
    }));

    return res.status(200).json({
      code: 200,
      message: '获取项目级别分布成功',
      data: formattedData
    });

  } catch (error) {
    console.error('获取项目级别分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目级别分布失败',
      data: null
    });
  }
};

/**
 * 获取院系教师分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDepartmentTeachers = async (req, res) => {
  try {
    const { status = 1 } = req.query;

    // 查询院系教师分布
    const departmentDistribution = await teacherModel.findAll({
      attributes: [
        'department',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        status,
        department: {
          [Op.ne]: null
        }
      },
      group: ['department'],
      raw: true
    });

    // 格式化数据
    const formattedData = departmentDistribution.map(item => ({
      name: item.department,
      value: parseInt(item.count)
    }));

    return res.status(200).json({
      code: 200,
      message: '获取院系教师分布成功',
      data: formattedData
    });

  } catch (error) {
    console.error('获取院系教师分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取院系教师分布失败',
      data: null
    });
  }
};

/**
 * 获取项目年度分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectYears = async (req, res) => {
  try {
    const { start_year = new Date().getFullYear() - 4, end_year = new Date().getFullYear(), department } = req.query;

    const startYear = parseInt(start_year);
    const endYear = parseInt(end_year);
    const years = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);

    // 构建基础查询条件
    let baseWhereCondition = { status: 1 };

    if (department) {
      // 查询该部门的教师
      const teachers = await teacherModel.findAll({
        attributes: ['name'],
        where: { department },
        raw: true
      });

      const teacherNames = teachers.map(t => t.name);

      baseWhereCondition.leader = {
        [Op.in]: teacherNames
      };
    }

    // 查询各年份项目分布
    const projectDistribution = await Promise.all(
      years.map(async (year) => {
        const count = await researchProjectModel.count({
          where: {
            ...baseWhereCondition,
            startDate: {
              [Op.between]: [`${year}-01-01`, `${year}-12-31`]
            }
          }
        });

        return { year, count };
      })
    );

    return res.status(200).json({
      code: 200,
      message: '获取项目年度分布成功',
      data: projectDistribution
    });

  } catch (error) {
    console.error('获取项目年度分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目年度分布失败',
      data: null
    });
  }
};

/**
 * 获取高级排行榜数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRanking = async (req, res) => {
  try {
    const { year = new Date().getFullYear(), type = 'score', department, limit = 10 } = req.query;

    // 构建基础查询条件
    let whereCondition = { year: parseInt(year) };

    if (department) {
      whereCondition.department = department;
    }

    let data = [];

    switch (type) {
      case 'score':
        // 查询总分排名
        const scoreRanking = await scoreRecordModel.findAll({
          attributes: [
            'teacherId',
            'teacher',
            'department',
            'totalScore'
          ],
          where: whereCondition,
          order: [['totalScore', 'DESC']],
          limit: parseInt(limit),
          raw: true
        });

        // 获取教师职称信息
        const teacherIds = scoreRanking.map(item => item.teacherId);
        const teacherInfo = await teacherModel.findAll({
          attributes: ['id', 'title'],
          where: { id: { [Op.in]: teacherIds } },
          raw: true
        });

        const teacherTitleMap = new Map(teacherInfo.map(item => [item.id, item.title]));

        data = scoreRanking.map((item, index) => ({
          rank: index + 1,
          teacher_id: item.teacherId,
          name: item.teacher,
          department: item.department,
          value: parseFloat(item.totalScore),
          title: teacherTitleMap.get(item.teacherId) || '未知'
        }));
        break;

      case 'project':
        // 查询项目数量排名
        const projectCounts = await sequelize.query(`
          SELECT 
            t.id as teacher_id,
            t.name,
            t.department,
            t.title,
            COUNT(rp.id) as project_count
          FROM 
            teachers t
          LEFT JOIN 
            research_projects rp ON t.name = rp.leader AND YEAR(rp.startDate) = :year
          WHERE 
            t.status = 1
            ${department ? "AND t.department = :department" : ""}
          GROUP BY 
            t.id, t.name, t.department, t.title
          ORDER BY 
            project_count DESC
          LIMIT :limit
        `, {
          replacements: {
            year: year,
            department: department,
            limit: parseInt(limit)
          },
          type: Sequelize.QueryTypes.SELECT
        });

        data = projectCounts.map((item, index) => ({
          rank: index + 1,
          teacher_id: item.teacher_id,
          name: item.name,
          department: item.department,
          value: parseInt(item.project_count),
          title: item.title
        }));
        break;

      case 'fund':
        // 查询经费金额排名
        const fundAmounts = await sequelize.query(`
          SELECT 
            t.id as teacher_id,
            t.name,
            t.department,
            t.title,
            SUM(rf.amount) as total_amount
          FROM 
            teachers t
          LEFT JOIN 
            research_funds rf ON t.name = rf.leader AND YEAR(rf.startDate) = :year
          WHERE 
            t.status = 1
            ${department ? "AND t.department = :department" : ""}
          GROUP BY 
            t.id, t.name, t.department, t.title
          HAVING
            total_amount > 0
          ORDER BY 
            total_amount DESC
          LIMIT :limit
        `, {
          replacements: {
            year: year,
            department: department,
            limit: parseInt(limit)
          },
          type: Sequelize.QueryTypes.SELECT
        });

        data = fundAmounts.map((item, index) => ({
          rank: index + 1,
          teacher_id: item.teacher_id,
          name: item.name,
          department: item.department,
          value: parseFloat(item.total_amount).toFixed(2),
          title: item.title
        }));
        break;

      case 'paper':
        // 查询论文数量排名
        const paperCounts = await sequelize.query(`
          SELECT 
            t.id as teacher_id,
            t.name,
            t.department,
            t.title,
            COUNT(hp.id) as paper_count
          FROM 
            teachers t
          LEFT JOIN 
            high_level_papers hp ON (hp.authors LIKE CONCAT('%', t.name, '%') OR hp.correspondingAuthor = t.name)
            AND YEAR(hp.publishDate) = :year
          WHERE 
            t.status = 1
            ${department ? "AND t.department = :department" : ""}
          GROUP BY 
            t.id, t.name, t.department, t.title
          ORDER BY 
            paper_count DESC
          LIMIT :limit
        `, {
          replacements: {
            year: year,
            department: department,
            limit: parseInt(limit)
          },
          type: Sequelize.QueryTypes.SELECT
        });

        data = paperCounts.map((item, index) => ({
          rank: index + 1,
          teacher_id: item.teacher_id,
          name: item.name,
          department: item.department,
          value: parseInt(item.paper_count),
          title: item.title
        }));
        break;

      default:
        return res.status(400).json({
          code: 400,
          message: '不支持的排名类型',
          data: null
        });
    }

    return res.status(200).json({
      code: 200,
      message: '获取高级排行榜数据成功',
      data
    });

  } catch (error) {
    console.error('获取高级排行榜数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取高级排行榜数据失败',
      data: null
    });
  }
};

/**
 * 获取用户统计数据
 * 统计各个模块的数量，可以按用户ID筛选
 * @param {*} req 
 * @param {*} res 
 */
exports.getUserStats = async (req, res) => {
  try {
    const { userId } = req.query;
    console.log("userId:", userId);

    const userInfo = await getUserInfoFromRequest(req);
    const isAdmin = userInfo.role && (userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER');
    console.log("isAdmin:", isAdmin);

    // 构建查询条件
    let whereCondition = {};

    if (!isAdmin) {
      whereCondition.participantId = userId;
    }

    console.log("whereCondition:", whereCondition); 

    // 初始化变量
    let academicAppointments = 0,
        conferences = 0,
        patents = 0,
        studentProjects = 0,
        studentAwards = 0,
        teachingProjects = 0,
        textbooks = 0,
        researchProjects = 0,
        highLevelPapers = 0,
        teachers = 0,
        teachingWorkloads = 0,
        teachingResearchAwards = 0;

    if (isAdmin) {
      // 并行获取各个模块的数据统计
      [
        academicAppointments,
        conferences,
        patents,
        studentProjects,
        studentAwards,
        teachingProjects,
        textbooks,
        researchProjects,
        highLevelPapers,
        teachers,
        teachingWorkloads,
        teachingResearchAwards
      ] = await Promise.all([
        academicAppointmentsModel.count({ where: whereCondition }),
        conferencesModel.count({ where: whereCondition }),
        patentsModel.count({ where: whereCondition }),
        studentProjectsModel.count({ where: whereCondition }),
        studentAwardsModel.count({ where: whereCondition }),
        teachingProjectsModel.count({ where: whereCondition }),
        textbooksModel.count({ where: whereCondition }),
        researchProjectModel.count({ where: whereCondition }),
        highLevelPapersModel.count({ where: whereCondition }),
        userModel.count(),
        teachingWorkloadsModel.count({ where: whereCondition }),
        teachingResearchAwardsModel.count({ where: whereCondition })
      ]);
    } else {
      // 非管理员用户，只获取自己的数据
      [
        academicAppointments,
        conferences,
        patents,
        studentProjects,
        studentAwards,
        teachingProjects,
        textbooks,
        researchProjects,
        highLevelPapers,
        teachers,
        teachingWorkloads,
        teachingResearchAwards
      ] = await Promise.all([
        academicAppointmentsModel.count({ where: {userId: userId} }),
        conferenceParticipantsModel.count({ where: whereCondition }),
        patentParticipantsModel.count({ where: whereCondition }),
        studentProjectGuidanceParticipantsModel.count({ where: {userId: userId} }),
        studentAwardGuidanceParticipantsModel.count({ where: {userId: userId} }),
        teachingReformProjectParticipantsModel.count({ where: {userId: userId} }),
        textbooksModel.count({ where: {userId: userId} }),
        researchProjectParticipantsModel.count({ where: {userId: userId} }),
        highLevelPaperParticipantsModel.count({ where: {userId: userId} }),
        userModel.count(),
        teachingWorkloadParticipantsModel.count({ where: whereCondition }),
        teachingResearchAwardParticipantsModel.count({ where: whereCondition })
      ]);
    }

    // 返回统计结果
    res.json({
      code: 200,
      message: '获取统计数据成功',
      data: {
        // 总计
        totalItems: academicAppointments + conferences + patents + studentProjects +
          studentAwards + teachingProjects + textbooks + researchProjects +
          highLevelPapers,

        // 分类统计
        academicAppointments,
        conferences,
        patents,
        studentProjects,
        studentAwards,
        teachingProjects,
        textbooks,
        researchProjects,
        highLevelPapers,
        teachers,
        teachingWorkloads,
        teachingResearchAwards
      }
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取统计数据失败',
      error: error.message
    });
  }
};

/**
 * 获取用户综合排名数据
 */
exports.getCombinedRanking = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      range = "all",
      reviewStatus = "all",
      isExportAll = false
    } = req.body;

    console.log(`查询参数: page=${page}, limit=${limit}, range=${range}, reviewStatus=${reviewStatus}, isExportAll=${isExportAll}`);

    // 根据range参数选择对应的模型
    let RankingModel;
    switch (range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }

    // 构建查询条件
    const whereConditions = {};

    // 如果有审核状态条件，可以在这里添加
    // 注意：当前模型中可能没有审核状态字段，这里仅作为示例
    // if (reviewStatus !== 'all') {
    //   whereConditions.status = reviewStatus;
    // }

    // 计算总记录数
    const total = await RankingModel.count({
      where: whereConditions
    });

    // 根据是否导出全部决定分页参数
    const offset = isExportAll ? 0 : (page - 1) * limit;
    const rows = isExportAll ? total : parseInt(limit);

    // 查询数据
    const rankings = await RankingModel.findAll({
      where: whereConditions,
      order: [['rank', 'ASC']],
      offset: offset,
      limit: rows
    });

    // 格式化数据，与前端期望的字段名对应
    const formattedRankings = rankings.map((item, index) => {
      return {
        index: item.rank || index + 1 + offset, // 使用数据库中的rank，如果没有则使用计算值
        userId: item.userId,
        nickName: item.nickName || '未知用户',
        studentNumber: item.studentNumber || '--',
        total_score: parseFloat(item.totalScore) || 0,
        total_count: item.totalItemCount || 0,

        // 各项分数
        papers_score: parseFloat(item.paperScore) || 0,
        patents_score: parseFloat(item.patentScore) || 0,
        textbooks_score: parseFloat(item.textbookScore) || 0,
        teaching_projects_score: parseFloat(item.teachingProjectScore) || 0,
        awards_score: parseFloat(item.awardScore) || 0,
        student_projects_score: parseFloat(item.studentProjectScore) || 0,
        conferences_score: parseFloat(item.conferenceScore) || 0,
        appointments_score: parseFloat(item.appointmentScore) || 0,
        research_projects_score: parseFloat(item.researchScore) || 0,
        teaching_workloads_score: parseFloat(item.teachingWorkloadScore) || 0,
        teaching_research_awards_score: parseFloat(item.teachingResearchAwardScore) || 0,

        // 各项数量
        papers_count: item.paperCount || 0,
        patents_count: item.patentCount || 0,
        textbooks_count: item.textbookCount || 0,
        teaching_projects_count: item.teachingProjectCount || 0,
        awards_count: item.awardCount || 0,
        student_projects_count: item.studentProjectCount || 0,
        conferences_count: item.conferenceCount || 0,
        appointments_count: item.appointmentCount || 0,
        research_projects_count: item.researchCount || 0,
        teaching_workloads_count: item.teachingWorkloadCount || 0,
        teaching_research_awards_count: item.teachingResearchAwardCount || 0
      };
    });

    const pages = Math.ceil(total / limit);

    // 返回处理后的结果
    return res.json({
      code: 200,
      message: '获取综合排名数据成功',
      result: {
        records: formattedRankings || [],
        total: parseInt(total),
        size: parseInt(limit),
        current: parseInt(page),
        pages: parseInt(pages)
      }
    });
  } catch (error) {
    console.error('获取综合排名数据失败:', error);
    return res.json({
      code: 500,
      message: `获取综合排名数据失败: ${error.message}`
    });
  }
};

/**
 * 获取我的评分数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getMyScore = async (req, res) => {
  try {
    const { userId } = req.query;
    console.log("req.query:", req.query);
    console.log("req.body:", req.body);

    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数userId',
        data: null
      });
    }

    // 1. 获取用户基本信息
    const user = await userModel.findByPk(userId, {
      attributes: ['id', 'username', 'nickname', 'studentNumber', 'avatar'],
      raw: true
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 2. 获取用户在不同范围内的排名和得分数据
    const [inRangeRanking, outRangeRanking, allRanking] = await Promise.all([
      userRankingReviewedInModel.findOne({
        where: { userId },
        raw: true
      }),
      userRankingReviewedOutModel.findOne({
        where: { userId },
        raw: true
      }),
      userRankingReviewedAllModel.findOne({
        where: { userId },
        raw: true
      })
    ]);

    // 使用allRanking中的数据 (如果没有则使用其他数据)
    const rankingData = inRangeRanking || allRanking || outRangeRanking || {};

    // 3. 构建维度得分数据
    const dimensionScores = [
      {
        name: '论文发表',
        count: rankingData.paperCount || 0,
        score: parseFloat(rankingData.paperScore || 0),
        color: '#722ed1',
        icon: 'FileTextOutlined'
      },
      {
        name: '专利',
        count: rankingData.patentCount || 0,
        score: parseFloat(rankingData.patentScore || 0),
        color: '#eb2f96',
        icon: 'SafetyCertificateOutlined'
      },
      {
        name: '教材著作',
        count: rankingData.textbookCount || 0,
        score: parseFloat(rankingData.textbookScore || 0),
        color: '#a0d911',
        icon: 'BookOutlined'
      },
      {
        name: '教学改革项目',
        count: rankingData.teachingProjectCount || 0,
        score: parseFloat(rankingData.teachingProjectScore || 0),
        color: '#13c2c2',
        icon: 'HighlightOutlined'
      },
      {
        name: '学生获奖',
        count: rankingData.awardCount || 0,
        score: parseFloat(rankingData.awardScore || 0),
        color: '#faad14',
        icon: 'TrophyOutlined'
      },
      {
        name: '学生立项',
        count: rankingData.studentProjectCount || 0,
        score: parseFloat(rankingData.studentProjectScore || 0),
        color: '#eb2f96',
        icon: 'FundProjectionScreenOutlined'
      },
      {
        name: '学术会议',
        count: rankingData.conferenceCount || 0,
        score: parseFloat(rankingData.conferenceScore || 0),
        color: '#faad14',
        icon: 'GlobalOutlined'
      },
      {
        name: '学术任职',
        count: rankingData.appointmentCount || 0,
        score: parseFloat(rankingData.appointmentScore || 0),
        color: '#1890ff',
        icon: 'TeamOutlined'
      },
      {
        name: '科研项目',
        count: rankingData.researchCount || 0,
        score: parseFloat(rankingData.researchScore || 0),
        color: '#1890ff',
        icon: 'FundProjectionScreenOutlined'
      },
      {
        name: '教学工作量',
        count: rankingData.teachingWorkloadCount || 0,
        score: parseFloat(rankingData.teachingWorkloadScore || 0),
        color: '#52c41a',
        icon: 'ScheduleOutlined'
      },
      {
        name: '教学科研奖励',
        count: rankingData.teachingResearchAwardCount || 0,
        score: parseFloat(rankingData.teachingResearchAwardScore || 0),
        color: '#f5222d',
        icon: 'TrophyOutlined'
      }
    ];

    // 用排名数据中的总分，而不是重新计算
    const totalScore = parseFloat(rankingData.totalScore || 0);

    // 用排名数据中的总项目数，而不是重新计算
    const totalProjectCount = rankingData.totalItemCount || 0;

    // 构建响应数据
    const responseData = {
      // 基本信息
      basic_info: {
        user_id: user.id,
        username: user.username,
        nickname: user.nickname || user.username,
        student_number: user.studentNumber,
        avatar: user.avatar,
        total_score: totalScore,
        total_project_count: totalProjectCount
      },
      // 各排名信息
      rankings: {
        in_range_rank: inRangeRanking?.rank || '-',
        out_range_rank: outRangeRanking?.rank || '-',
        total_rank: allRanking?.rank || '-'
      },
      // 各维度得分
      dimension_scores: dimensionScores,
    };

    return res.status(200).json({
      code: 200,
      message: '获取我的评分数据成功',
      data: responseData
    });

  } catch (error) {
    console.error('获取我的评分数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取我的评分数据失败',
      data: null
    });
  }
};

/**
 * 获取对应时间区间
 */
exports.getScoreTimeRange = async (req, res) => {
  try {
    // 引入时间区间模型
    const TimeInterval = require('@models/v1/mapping/timeIntervalModel');
    
    // 获取查询参数
    const { type } = req.query;
    
    // 定义有效的参数列表
    const validTypes = [
      'teachingResearchAwards',
      'highLevelPapers',
      'researchProjects',
      'patents',
      'conferences',
      'academicAppointments',
      'textbooks',
      'studentProjectGuidance',
      'studentAwardGuidance',
      'teachingReformProjects',
      'teachingWorkloads'
    ];
    
    // 如果没有提供类型参数或类型无效，查询所有类型
    let timeRanges = {};
    
    if (!type || !validTypes.includes(type)) {
      // 查询所有类型的时间区间
      const allTimeIntervals = await TimeInterval.findAll({
        where: {
          name: validTypes
        },
        attributes: ['name', 'nameC', 'startTime', 'endTime'],
        raw: true
      });
      
      // 整理所有时间区间数据
      if (allTimeIntervals && allTimeIntervals.length > 0) {
        allTimeIntervals.forEach(interval => {
          timeRanges[interval.name] = {
            name: interval.name,
            nameC: interval.nameC,
            startTime: interval.startTime,
            endTime: interval.endTime,
            timeRange: `${formatDate(interval.startTime)} 至 ${formatDate(interval.endTime)}`
          };
        });
      }
    } else {
      // 查询指定类型的时间区间
      const timeInterval = await TimeInterval.findOne({
        where: {
          name: type
        },
        attributes: ['name', 'nameC', 'startTime', 'endTime'],
        raw: true
      });
      
      if (timeInterval) {
        timeRanges = {
          name: timeInterval.name,
          nameC: timeInterval.nameC,
          startTime: timeInterval.startTime,
          endTime: timeInterval.endTime,
          timeRange: `${formatDate(timeInterval.startTime)} 至 ${formatDate(timeInterval.endTime)}`
        };
      }
    }
    
    // 返回查询结果
    return res.status(200).json({
      code: 200,
      message: '获取时间区间成功',
      data: timeRanges
    });
  } catch (error) { 
    console.error('获取对应时间区间失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取对应时间区间失败',
      data: null
    });
  }
};

// 格式化日期为 YYYY年MM月DD日 格式
function formatDate(date) {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}年${(d.getMonth() + 1).toString().padStart(2, '0')}月${d.getDate().toString().padStart(2, '0')}日`;
}