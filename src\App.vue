<script setup>
import {watchEffect, ref, computed, onMounted} from 'vue'
import {ConfigProvider} from "ant-design-vue";
import {useSettingStore} from '@/stores/setting.js'
import enUS from 'ant-design-vue/es/locale/en_US';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { useUserRole } from '../composables/useUserRole';
import dbUtils from './libs/util.strotage.js';

dayjs.locale('zh');

const settingStore = useSettingStore()
const locale = ref(zhCN);
const { getUserRole, getRoleFromStorage } = useUserRole();

// 监听全局主题状态
watchEffect(() => {
  const theme = settingStore.theme;
  const local = settingStore.local;
  locale.value = local === 'en' ? enUS : zhCN
  ConfigProvider.config({
    theme: theme.value,
  });
});

// 在应用加载时获取用户角色
onMounted(async () => {
  // 检查用户是否已登录
  const token = dbUtils.get('token') || localStorage.getItem('zyadmin-1.0.0-token');
  if (token) {
    console.log('App初始化 - 用户已登录，尝试获取角色信息');
    
    // 获取用户ID
    let userId = null;
    try {
      userId = localStorage.getItem('zyadmin-1.0.0-userId');
      if (!userId) {
        // 尝试从userInfo中获取
        const userInfoStr = localStorage.getItem('zyadmin-1.0.0-userInfo');
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          userId = userInfo.id;
        }
      }
    } catch (e) {
      console.error('App初始化 - 获取用户ID失败:', e);
    }
    
    if (!userId) {
      console.warn('App初始化 - 未找到用户ID，无法获取角色信息');
      return;
    }
    
    // 首先从本地存储获取角色信息
    const cachedRole = getRoleFromStorage();
    if (!cachedRole) {
      console.log('App初始化 - 本地未找到角色信息，从服务器获取');
      // 如果本地没有角色信息，则从服务器获取
      try {
        const roleInfo = await getUserRole(userId);
        console.log('App初始化 - 成功获取用户角色信息:', roleInfo);
      } catch (error) {
        console.error('App初始化 - 获取用户角色信息失败:', error);
      }
    } else {
      console.log('App初始化 - 从本地存储获取到角色信息:', cachedRole);
    }
  } else {
    console.log('App初始化 - 用户未登录');
  }
});

</script>

<template>
  <a-config-provider :locale="locale">
    <RouterView/>
  </a-config-provider>
</template>

<style scoped lang="scss">

</style>
