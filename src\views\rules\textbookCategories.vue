<template>
  <div class="app-container">
    <a-card>
      <div class="table-operations">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>新增
          </a-button>
        </a-space>
      </div>

      <a-table
        :columns="columns"
        :data-source="categoryList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'operation'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm
                title="确定要删除此类别吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
          <template v-else-if="column.dataIndex === 'textbooksCount'">
            <a @click="showTextbooks(record)" v-if="record.textbooksCount > 0">
              {{ record.textbooksCount }}
            </a>
            <span v-else>{{ record.textbooksCount }}</span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      :visible="open"
      :title="title"
      width="500px"
      @ok="submitForm"
      @cancel="cancel"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item label="类别名称" name="categoryAndPosition">
          <a-input v-model:value="form.categoryAndPosition" placeholder="请输入类别名称" />
        </a-form-item>
        <a-form-item label="分数" name="score">
          <a-input-number
            v-model:value="form.score"
            placeholder="请输入分数"
            style="width: 100%"
            :min="0"
            :precision="2"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 使用该类别的教材列表弹窗 -->
    <a-modal
      :visible="textbooksModalVisible"
      title="使用该类别的教材与著作"
      width="800px"
      @cancel="() => textbooksModalVisible = false"
      :footer="null"
    >
      <a-table
        :columns="textbooksColumns"
        :data-source="currentCategoryTextbooks"
        :loading="textbooksLoading"
        :pagination="textbooksPagination"
        @change="handleTextbooksTableChange"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'publishDate'">
            {{ formatDate(record.publishDate) }}
          </template>
          <template v-else-if="column.dataIndex === 'ifReviewer'">
            <a-tag :color="record.ifReviewer ? 'green' : 'orange'">
              {{ record.ifReviewer ? '已审核' : '未审核' }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  getTextbookCategories,
  getTextbookCategoriesWithCount,
  createTextbookCategory,
  updateTextbookCategory,
  deleteTextbookCategory
} from '@/api/modules/api.textbookCategories';
import { PlusOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'TextbookCategories',
  components: {
    PlusOutlined
  },
  setup() {
    // 表格列定义
    const columns = [
      {
        title: '类别名称',
        dataIndex: 'categoryAndPosition',
        key: 'categoryAndPosition',
        width: '40%',
        ellipsis: true
      },
      {
        title: '分数',
        dataIndex: 'score',
        key: 'score',
        width: '20%'
      },
      {
        title: '使用数量',
        dataIndex: 'textbooksCount',
        key: 'textbooksCount',
        width: '20%'
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: '20%',
        fixed: 'right'
      }
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`
    });

    // 数据相关
    const loading = ref(false);
    const categoryList = ref([]);

    // 表单相关
    const formRef = ref(null);
    const open = ref(false);
    const title = ref('');
    const form = reactive({
      id: undefined,
      categoryAndPosition: '',
      score: undefined
    });
    const rules = {
      categoryAndPosition: [{ required: true, message: '请输入类别名称' }],
      score: [{ required: true, message: '请输入分数' }]
    };

    // 教材列表相关
    const textbooksModalVisible = ref(false);
    const textbooksLoading = ref(false);
    const currentCategoryTextbooks = ref([]);
    const currentCategoryId = ref('');
    const textbooksPagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`
    });
    const textbooksColumns = [
      {
        title: '教材与著作名称',
        dataIndex: 'materialName',
        key: 'materialName',
        ellipsis: true
      },
      {
        title: '出版日期',
        dataIndex: 'publishDate',
        key: 'publishDate',
        width: 120
      },
      {
        title: '作者',
        dataIndex: 'user',
        key: 'user',
        ellipsis: true,
        customRender: ({ record }) => record.user ? record.user.nickname : '-'
      },
      {
        title: '工号',
        dataIndex: 'studentNumber',
        key: 'studentNumber',
        width: 120,
        customRender: ({ record }) => record.user ? record.user.studentNumber : '-'
      },
      {
        title: '审核状态',
        dataIndex: 'ifReviewer',
        key: 'ifReviewer',
        width: 100
      }
    ];

    // 初始化
    onMounted(() => {
      getList();
    });

    // 获取类别列表
    const getList = async () => {
      try {
        loading.value = true;
        const res = await getTextbookCategoriesWithCount();
        
        if (res.code === 200) {
          categoryList.value = res.data;
          pagination.total = res.data.length;
        } else {
          message.error(res.message || '获取类别列表失败');
        }
      } catch (error) {
        console.error('获取类别列表失败:', error);
        message.error('获取类别列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 表格分页变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
    };

    // 新增
    const handleAdd = () => {
      resetForm();
      open.value = true;
      title.value = '新增教材与著作类别';
    };

    // 编辑
    const handleEdit = (record) => {
      resetForm();
      open.value = true;
      title.value = '编辑教材与著作类别';
      
      Object.assign(form, {
        id: record.id,
        categoryAndPosition: record.categoryAndPosition,
        score: record.score
      });
    };

    // 提交表单
    const submitForm = async () => {
      try {
        await formRef.value.validate();
        
        const params = {
          ...form
        };
        
        const api = form.id ? updateTextbookCategory : createTextbookCategory;
        const res = await api(params);
        
        if (res.code === 200) {
          message.success(`${form.id ? '修改' : '新增'}成功`);
          open.value = false;
          getList();
        } else {
          message.error(res.message || `${form.id ? '修改' : '新增'}失败`);
        }
      } catch (error) {
        console.error(`${form.id ? '修改' : '新增'}失败:`, error);
        message.error(`${form.id ? '修改' : '新增'}失败`);
      }
    };

    // 删除
    const handleDelete = async (record) => {
      try {
        if (record.textbooksCount > 0) {
          message.error(`该类别已被${record.textbooksCount}个教材与著作记录使用，无法删除`);
          return;
        }
        
        const res = await deleteTextbookCategory({ id: record.id });
        
        if (res.code === 200) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    };

    // 查看使用该类别的教材列表
    const showTextbooks = async (record) => {
      try {
        if (record.textbooksCount === 0) return;
        
        textbooksModalVisible.value = true;
        textbooksLoading.value = true;
        currentCategoryId.value = record.id;
        textbooksPagination.current = 1;
        
        await fetchCategoryTextbooks();
      } catch (error) {
        console.error('获取教材列表失败:', error);
        message.error('获取教材列表失败');
        textbooksLoading.value = false;
      }
    };

    // 获取使用该类别的教材列表
    const fetchCategoryTextbooks = async () => {
      try {
        const params = {
          categoryId: currentCategoryId.value,
          page: textbooksPagination.current,
          pageSize: textbooksPagination.pageSize,
          range: 'all'
        };
        
        const res = await getTextbookList(params);
        
        if (res.code === 200) {
          currentCategoryTextbooks.value = res.data.list;
          textbooksPagination.total = res.data.pagination.total;
        } else {
          message.error(res.message || '获取教材列表失败');
        }
      } catch (error) {
        console.error('获取教材列表失败:', error);
        message.error('获取教材列表失败');
      } finally {
        textbooksLoading.value = false;
      }
    };

    // 教材表格分页变化
    const handleTextbooksTableChange = (pag) => {
      textbooksPagination.current = pag.current;
      textbooksPagination.pageSize = pag.pageSize;
      fetchCategoryTextbooks();
    };

    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields();
      }
      
      Object.assign(form, {
        id: undefined,
        categoryAndPosition: '',
        score: undefined
      });
    };

    // 取消
    const cancel = () => {
      open.value = false;
      resetForm();
    };

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '-';
      return dayjs(date).format('YYYY-MM-DD');
    };

    return {
      // 数据
      columns,
      pagination,
      loading,
      categoryList,
      
      // 表单
      formRef,
      open,
      title,
      form,
      rules,
      
      // 教材列表
      textbooksModalVisible,
      textbooksLoading,
      currentCategoryTextbooks,
      textbooksPagination,
      textbooksColumns,
      
      // 方法
      handleTableChange,
      handleAdd,
      handleEdit,
      handleDelete,
      submitForm,
      cancel,
      showTextbooks,
      handleTextbooksTableChange,
      formatDate
    };
  }
});
</script>

<style scoped>
.table-operations {
  margin-bottom: 16px;
}
</style> 