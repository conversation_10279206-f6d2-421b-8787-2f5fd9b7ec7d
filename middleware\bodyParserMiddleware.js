const bodyParser = require('body-parser');
const { PayloadTooLargeError } = require('../utils/errors');

/**
 * 请求体解析中间件配置
 */
const bodyParserConfig = {
  json: {
    limit: '10mb',
    extended: true
  },
  urlencoded: {
    limit: '10mb',
    extended: true
  }
};

/**
 * 创建请求体解析中间件
 */
const createBodyParser = () => {
  return [
    bodyParser.json(bodyParserConfig.json),
    bodyParser.urlencoded(bodyParserConfig.urlencoded),
    (err, req, res, next) => {
      if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
        next(new PayloadTooLargeError('请求体过大或格式错误'));
      } else {
        next(err);
      }
    }
  ];
};

module.exports = {
  createBodyParser
}; 