<template>
  <div class="departments-management">
    <a-card :bordered="false">
      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="部门名称">
            <a-input 
              v-model:value="searchForm.departmentName" 
              placeholder="请输入部门名称"
              allow-clear
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="部门代码">
            <a-input 
              v-model:value="searchForm.departmentCode" 
              placeholder="请输入部门代码"
              allow-clear
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="状态">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option :value="1">正常</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
              <a-button
                type="primary"
                @click="handleAdd"
              >
                <template #icon><PlusOutlined /></template>
                新增部门
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-dropdown>
              <a-button type="link" size="small" class="action-trigger-btn">
                操作 <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu class="action-dropdown-menu">
                  <a-menu-item key="view" @click="handleView(record)">
                    <EyeOutlined /> 查看
                  </a-menu-item>
                  <a-menu-item
                    key="edit"
                    @click="handleEdit(record)"
                  >
                    <EditOutlined /> 编辑
                  </a-menu-item>
                  <a-menu-divider/>
                  <a-menu-item
                    key="delete"
                    @click="handleDelete(record)"
                    style="color: #ff4d4f"
                  >
                    <DeleteOutlined /> 删除
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      :open="modalVisible"
      :visible="modalVisible"
      :title="modalTitle"
      :width="600"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="部门名称" name="departmentName">
          <a-input v-model:value="formData.departmentName" placeholder="请输入部门名称" />
        </a-form-item>
        
        <a-form-item label="部门代码" name="departmentCode">
          <a-input v-model:value="formData.departmentCode" placeholder="请输入部门代码" />
        </a-form-item>
        
        <a-form-item label="上级部门" name="parentId">
          <a-tree-select
            v-model:value="formData.parentId"
            :tree-data="departmentTree"
            :field-names="{ label: 'departmentName', value: 'id', children: 'children' }"
            placeholder="请选择上级部门（可选，不选择则为根部门）"
            allow-clear
            tree-default-expand-all
          />
          <div style="color: #999; font-size: 12px; margin-top: 4px;">
            不选择上级部门将创建为根部门
          </div>
        </a-form-item>
        
        <a-form-item label="排序" name="sort">
          <a-input-number 
            v-model:value="formData.sort" 
            :min="0" 
            :max="999" 
            placeholder="请输入排序"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">正常</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入部门描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情弹窗 -->
    <a-modal
      :open="viewModalVisible"
      :visible="viewModalVisible"
      title="部门详情"
      :width="600"
      :footer="null"
    >
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="部门名称">{{ viewData.departmentName }}</a-descriptions-item>
        <a-descriptions-item label="部门代码">{{ viewData.departmentCode }}</a-descriptions-item>
        <a-descriptions-item label="上级部门">{{ viewData.parentName || '无' }}</a-descriptions-item>
        <a-descriptions-item label="排序">{{ viewData.sort }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="viewData.status === 1 ? 'green' : 'red'">
            {{ viewData.status === 1 ? '正常' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatTime(viewData.createdAt) }}</a-descriptions-item>
        <a-descriptions-item label="描述" :span="2">{{ viewData.description || '无' }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import {
  getDepartments,
  getDepartmentTree,
  getDepartmentDetail,
  createDepartment,
  updateDepartment,
  deleteDepartment
} from '@/api/modules/api.departments'
// 响应式数据
const loading = ref(false)
const tableData = ref([])
const departmentTree = ref([])
const modalVisible = ref(false)
const viewModalVisible = ref(false)
const modalTitle = ref('')
const formRef = ref()
const isEdit = ref(false)
const currentId = ref('')

// 搜索表单
const searchForm = reactive({
  departmentName: '',
  departmentCode: '',
  status: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '部门名称',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 150
  },
  {
    title: '部门代码',
    dataIndex: 'departmentCode',
    key: 'departmentCode',
    width: 120
  },
  {
    title: '上级部门',
    dataIndex: 'parentName',
    key: 'parentName',
    width: 150
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleString('zh-CN') : ''
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right'
  }
]

// 表单数据
const formData = reactive({
  departmentName: '',
  departmentCode: '',
  parentId: null,
  sort: 0,
  status: 1,
  description: ''
})

// 查看数据
const viewData = reactive({})

// 表单验证规则
const formRules = {
  departmentName: [
    { required: true, message: '请输入部门名称', trigger: 'blur' }
  ],
  departmentCode: [
    { required: true, message: '请输入部门代码', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' }
  ]
}

// 格式化时间
const formatTime = (time) => {
  return time ? new Date(time).toLocaleString('zh-CN') : ''
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getDepartments(params)
    if (response.code === 200) {
      tableData.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0
      pagination.current = response.data.pagination?.page || 1
      pagination.pageSize = response.data.pagination?.pageSize || 10
    }
  } catch (error) {
    console.error('加载部门列表失败:', error)
    message.error('加载部门列表失败')
  } finally {
    loading.value = false
  }
}

// 加载部门树
const loadDepartmentTree = async () => {
  try {
    const response = await getDepartmentTree()
    if (response.code === 200) {
      departmentTree.value = response.data || []
    }
  } catch (error) {
    console.error('加载部门树失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    departmentName: '',
    departmentCode: '',
    status: undefined
  })
  pagination.current = 1
  loadData()
}

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

// 新增
const handleAdd = () => {
  modalTitle.value = '新增部门'
  isEdit.value = false
  modalVisible.value = true

  Object.assign(formData, {
    departmentName: '',
    departmentCode: '',
    parentId: null,
    sort: 0,
    status: 1,
    description: ''
  })
}

// 编辑
const handleEdit = async (record) => {
  try {
    modalTitle.value = '编辑部门'
    isEdit.value = true
    currentId.value = record.id
    modalVisible.value = true
    
    const response = await getDepartmentDetail(record.id)
    if (response.code === 200) {
      Object.assign(formData, response.data)
    }
  } catch (error) {
    console.error('获取部门详情失败:', error)
    message.error('获取部门详情失败')
  }
}

// 查看
const handleView = async (record) => {
  try {
    viewModalVisible.value = true
    const response = await getDepartmentDetail(record.id)
    if (response.code === 200) {
      Object.assign(viewData, response.data)
    }
  } catch (error) {
    console.error('获取部门详情失败:', error)
    message.error('获取部门详情失败')
  }
}

// 删除
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除部门"${record.departmentName}"吗？`,
    onOk: async () => {
      try {
        const response = await deleteDepartment(record.id)
        if (response.code === 200) {
          message.success('删除成功')
          loadData()
        } else {
          message.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除部门失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 处理表单数据，确保空值正确传递
    const submitData = {
      ...formData,
      parentId: formData.parentId || null // 空字符串转换为 null
    }

    const response = isEdit.value
      ? await updateDepartment(currentId.value, submitData)
      : await createDepartment(submitData)

    if (response.code === 200) {
      message.success(isEdit.value ? '更新成功' : '创建成功')
      modalVisible.value = false
      loadData()
      loadDepartmentTree() // 重新加载部门树
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败')
  }
}

// 取消
const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

// 初始化
onMounted(() => {
  loadData()
  loadDepartmentTree()
})
</script>

<style scoped>
.departments-management {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
