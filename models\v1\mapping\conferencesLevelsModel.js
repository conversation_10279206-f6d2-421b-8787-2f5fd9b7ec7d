const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义会议级别模型
const ConferenceLevel = sequelize.define('conferences_levels', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    levelName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '级别名称'
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '描述'
    },
    status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: '状态：0-禁用，1-启用'
    },
    score: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        comment: '分数'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'conferences_levels',
    timestamps: true,
    indexes: [
        {
            name: 'idx_level_name',
            fields: ['levelName']
        },
        {
            name: 'idx_level_status',
            fields: ['status']
        }
    ]
});

module.exports = ConferenceLevel; 