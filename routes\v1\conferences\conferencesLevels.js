const express = require('express');
const router = express.Router();
const conferenceLevelController = require('../../../controllers/v1/conferences/conferencesLevelsController');

/**
 * 获取会议级别列表
 * @route GET /v1/sys/conference-levels/levels
 * @group 会议级别管理 - 会议级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels', conferenceLevelController.getConferenceLevels);

/**
 * 获取所有级别及其会议数量
 * @route GET /v1/sys/conference-levels/levels-with-count
 * @group 会议级别管理 - 会议级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id, levelName, description, conferenceCount},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels-with-count', conferenceLevelController.getLevelsWithCount);

/**
 * 获取会议级别详情
 * @route GET /v1/sys/conference-levels/level/:id
 * @group 会议级别管理 - 会议级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/:id', conferenceLevelController.getLevelDetail);

/**
 * 创建会议级别
 * @route POST /v1/sys/conference-levels/level/create
 * @group 会议级别管理 - 会议级别相关接口
 * @param {string} levelName.body.required - 级别名称
 * @param {string} description.body - 级别描述
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/create', conferenceLevelController.createLevel);

/**
 * 更新会议级别
 * @route POST /v1/sys/conference-levels/level/update
 * @group 会议级别管理 - 会议级别相关接口
 * @param {string} id.body.required - 级别ID
 * @param {string} levelName.body - 级别名称
 * @param {string} description.body - 级别描述
 * @param {number} status.body - 状态：0-禁用，1-启用
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/update', async (req, res) => {
  const { id, ...updateData } = req.body;
  req.params = { id };
  req.body = updateData;
  await conferenceLevelController.updateLevel(req, res);
});

/**
 * 获取会议级别分布数据
 * @route POST /v1/sys/conference-levels/statistics/distribution
 * @group 会议级别统计 - 会议级别统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的会议
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "级别名称", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/distribution', conferenceLevelController.getLevelDistribution);

/**
 * 删除会议级别
 * @route POST /v1/sys/conference-levels/level/delete
 * @group 会议级别管理 - 会议级别相关接口
 * @param {string} id.body.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/delete', async (req, res) => {
  const { id } = req.body;
  req.params = { id };
  await conferenceLevelController.deleteLevel(req, res);
});


module.exports = router; 