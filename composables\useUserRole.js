import { ref } from 'vue';
import { authGetUserRole } from '../src/api/modules/api.auth.js';

/**
 * 用于获取当前登录用户角色的钩子
 * @returns {Object} { role, loading, error, getUserRole }
 */
export function useUserRole() {
  const role = ref(null);
  const loading = ref(false);
  const error = ref(null);

  /**
   * 从服务器获取用户角色信息
   * @param {string} userId - 可选的用户ID，如果不提供则尝试从存储中获取
   * @returns {Promise<Object|null>} 用户角色信息或null
   */
  const getUserRole = async (userId = null) => {
    loading.value = true;
    error.value = null;
    
    try {
      console.log('=== 开始获取用户角色 ===');
      
      // 如果没有提供userId，尝试从存储中获取
      if (!userId) {
        try {
          userId = localStorage.getItem('zyadmin-1.0.0-userId');
          console.log('从localStorage获取userId:', userId);
          
          if (!userId) {
            userId = sessionStorage.getItem('zyadmin-1.0.0-userId');
            console.log('从sessionStorage获取userId:', userId);
          }
          
          // 如果还没有，尝试从userInfo中获取
          if (!userId) {
            const userInfoStr = localStorage.getItem('zyadmin-1.0.0-userInfo');
            if (userInfoStr) {
              const userInfo = JSON.parse(userInfoStr);
              userId = userInfo.id;
              console.log('从userInfo获取userId:', userId);
            }
          }
        } catch (e) {
          console.error('从存储获取userId失败:', e);
        }
      } else {
        console.log('使用传入的userId:', userId);
      }
      
      if (!userId) {
        throw new Error('未能获取到用户ID，请先登录');
      }
      
      // 使用API方法请求角色信息
      console.log('请求参数: userId =', userId);

      // 请求服务器获取角色信息，传递userId参数
      console.time('角色请求耗时');
      const response = await authGetUserRole(userId);
      console.timeEnd('角色请求耗时');

      console.log('角色请求响应数据:', response);

      if (response && response.status === 1) {
        console.log('获取角色成功:', JSON.stringify(response.data, null, 2));
        role.value = response.data;
        
        // 将角色信息保存到localStorage
        try {
          localStorage.setItem('zyadmin-1.0.0-userRole', JSON.stringify(role.value));
          console.log('角色信息已保存到localStorage');
        } catch (e) {
          console.error('保存角色信息到localStorage失败:', e);
        }
        
        console.log('=== 获取用户角色完成 ===');
        return role.value;
      } else {
        throw new Error(response?.message || '获取角色失败');
      }
    } catch (err) {
      console.error('获取用户角色失败:', err);
      console.log('错误详情:', err.message || err);
      error.value = err.message || '获取用户角色失败';
      
      // 尝试从本地存储获取之前缓存的角色信息
      try {
        const cachedRole = JSON.parse(localStorage.getItem('zyadmin-1.0.0-userRole') || 'null');
        if (cachedRole) {
          console.log('从缓存获取用户角色:', cachedRole);
          role.value = cachedRole;
          console.log('=== 从缓存获取用户角色完成 ===');
          return cachedRole;
        }
      } catch (e) {
        console.error('从缓存获取用户角色失败:', e);
      }
      
      console.log('=== 获取用户角色失败 ===');
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 从本地存储获取角色信息
   * @returns {Object|null} 用户角色信息或null
   */
  const getRoleFromStorage = () => {
    try {
      console.log('尝试从localStorage获取角色信息');
      const roleStr = localStorage.getItem('zyadmin-1.0.0-userRole');
      if (roleStr) {
        const roleData = JSON.parse(roleStr);
        role.value = roleData;
        console.log('从localStorage获取到角色信息:', roleData);
        return roleData;
      } else {
        console.log('localStorage中没有角色信息');
      }
    } catch (e) {
      console.error('从本地存储获取角色失败:', e);
    }
    return null;
  };

  /**
   * 清除角色信息（用于登出时）
   */
  const clearRole = () => {
    role.value = null;
    try {
      localStorage.removeItem('zyadmin-1.0.0-userRole');
      console.log('已清除localStorage中的角色信息');
    } catch (e) {
      console.error('清除角色信息失败:', e);
    }
  };

  // 初始化时尝试从本地存储获取角色信息
  getRoleFromStorage();

  return {
    role,
    loading,
    error,
    getUserRole,
    getRoleFromStorage,
    clearRole
  };
}

export default useUserRole; 