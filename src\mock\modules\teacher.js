// mock/modules/teacher.js
export default [
  {
    url: '/teacher/list',
    method: 'post',
    status: 404,
    response: () => {
      return {
        code: 200,
        message: '[Mock数据] 教师列表获取成功',
        data: {
          list: [
            { id: '1', name: '张三', title: '教授', department: '计算机科学系', status: 1 },
            { id: '2', name: '李四', title: '副教授', department: '计算机科学系', status: 1 },
            { id: '3', name: '王五', title: '讲师', department: '软件工程系', status: 1 },
            { id: '4', name: '赵六', title: '教授', department: '软件工程系', status: 1 },
            { id: '5', name: '钱七', title: '副教授', department: '人工智能系', status: 1 },
            { id: '6', name: '孙八', title: '讲师', department: '人工智能系', status: 1 }
          ],
          pagination: {
            total: 6,
            current: 1,
            pageSize: 10
          }
        }
      }
    }
  },
  {
    url: '/teacher/detail',
    method: 'post',
    status: 404,
    response: ({ body }) => {
      const { id } = body
      // 根据id返回对应的教师详情
      const teachers = {
        '1': { id: '1', name: '张三', title: '教授', department: '计算机科学系', status: 1 },
        '2': { id: '2', name: '李四', title: '副教授', department: '计算机科学系', status: 1 },
        '3': { id: '3', name: '王五', title: '讲师', department: '软件工程系', status: 1 },
        '4': { id: '4', name: '赵六', title: '教授', department: '软件工程系', status: 1 },
        '5': { id: '5', name: '钱七', title: '副教授', department: '人工智能系', status: 1 },
        '6': { id: '6', name: '孙八', title: '讲师', department: '人工智能系', status: 1 }
      }
      
      return {
        code: 200,
        message: '获取成功',
        data: teachers[id] || null
      }
    }
  }
] 