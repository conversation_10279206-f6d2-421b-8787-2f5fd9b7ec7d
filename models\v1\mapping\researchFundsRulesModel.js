const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义科研经费核算规则模型
module.exports = sequelize.define('research_funds_rules', // 数据库表名为research_funds_rules
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: '主键，使用 UUID 唯一标识每条记录',
        },
        minAmount: {
            type: DataTypes.DECIMAL(15, 2),
            notNull: true,
            allowNull: false,
            comment: '经费范围下限（单位：元）',
        },
        maxAmount: {
            type: DataTypes.DECIMAL(15, 2),
            allowNull: true,
            comment: '经费范围上限（单位：元），可以为 NULL 表示无上限',
        },
        score: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true,
            comment: '固定核算分数',
        },
        scoreFormula: {
            type: DataTypes.STRING(255),
            allowNull: true,
            comment: '分数计算公式（当经费范围无上限时使用）',
        },
        createdBy: {
            type: DataTypes.UUID,
            notNull: true,
            allowNull: false,
            comment: '创建者 ID（userId）',
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录创建时间',
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录最后修改时间',
        },
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
        indexes: [
            {
                unique: true,
                fields: ['min_amount', 'max_amount'],
                name: 'uk_amount_range'
            }
        ]
    }); 