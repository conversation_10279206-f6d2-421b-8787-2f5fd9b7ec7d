<template>
  <div class="form-submissions">
    <a-card title="表单提交管理" :bordered="false">
      <a-form layout="inline" :model="searchForm" class="search-form">
        <a-form-item label="教师姓名">
          <a-input v-model:value="searchForm.teacherName" placeholder="请输入教师姓名" />
        </a-form-item>
        <a-form-item label="表单类型">
          <a-select v-model:value="searchForm.formType" placeholder="请选择表单类型" style="width: 200px">
            <a-select-option value="research">科研项目</a-select-option>
            <a-select-option value="teaching">教学获奖</a-select-option>
            <a-select-option value="international">国际交流</a-select-option>
            <a-select-option value="social">社会服务</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="提交状态">
          <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 200px">
            <a-select-option value="pending">待审核</a-select-option>
            <a-select-option value="approved">已审核</a-select-option>
            <a-select-option value="rejected">已驳回</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <a-table :columns="columns" :data-source="data" :pagination="pagination" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="viewSubmission(record)">查看</a>
              <a-divider type="vertical" />
              <template v-if="record.status === 'pending'">
                <a @click="handleApprove(record)">通过</a>
                <a-divider type="vertical" />
                <a @click="handleReject(record)">驳回</a>
                <a-divider type="vertical" />
              </template>
              <a-popconfirm
                title="确定要删除该提交记录吗？"
                @confirm="deleteSubmission(record)"
              >
                <a class="text-danger">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="viewModalVisible"
      title="表单详情"
      @ok="handleViewModalOk"
      @cancel="handleViewModalCancel"
      width="800px"
    >
      <template v-if="currentSubmission">
        <div class="submission-info">
          <h3>{{ currentSubmission.formName }}</h3>
          <p class="submission-meta">
            <span>提交人：{{ currentSubmission.teacherName }}</span>
            <span>提交时间：{{ currentSubmission.submitTime }}</span>
            <span>状态：{{ getStatusText(currentSubmission.status) }}</span>
          </p>
          <a-divider />
          <div class="submission-fields">
            <div v-for="(field, index) in currentSubmission.fields" :key="index" class="field-item">
              <label>{{ field.label }}：</label>
              <div class="field-value">
                <template v-if="field.type === 'file'">
                  <a :href="field.value" target="_blank">查看文件</a>
                </template>
                <template v-else>
                  {{ field.value }}
                </template>
              </div>
            </div>
          </div>
          <template v-if="currentSubmission.comment">
            <a-divider />
            <div class="submission-comment">
              <h4>审核意见</h4>
              <p>{{ currentSubmission.comment }}</p>
            </div>
          </template>
        </div>
      </template>
    </a-modal>

    <a-modal
      v-model:visible="rejectModalVisible"
      title="驳回原因"
      @ok="handleRejectModalOk"
      @cancel="handleRejectModalCancel"
      width="500px"
    >
      <a-form :model="rejectForm" :rules="rejectRules" ref="rejectFormRef">
        <a-form-item name="reason" label="驳回原因">
          <a-textarea
            v-model:value="rejectForm.reason"
            :rows="4"
            placeholder="请输入驳回原因"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 搜索表单
const searchForm = reactive({
  teacherName: '',
  formType: undefined,
  status: undefined
})

// 表格列定义
const columns = [
  {
    title: '教师姓名',
    dataIndex: 'teacherName',
    key: 'teacherName',
  },
  {
    title: '表单名称',
    dataIndex: 'formName',
    key: 'formName',
  },
  {
    title: '表单类型',
    dataIndex: 'formType',
    key: 'formType',
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 模拟数据
const data = ref([
  {
    key: '1',
    teacherName: '张三',
    formName: '科研项目评分表',
    formType: 'research',
    submitTime: '2023-12-01 10:00:00',
    status: 'pending',
    fields: [
      { label: '项目名称', type: 'text', value: '基于人工智能的教学评价研究' },
      { label: '项目级别', type: 'select', value: '国家级' },
      { label: '项目金额', type: 'number', value: '500000' },
      { label: '项目成果', type: 'text', value: '发表论文3篇，申请专利1项' }
    ]
  },
  {
    key: '2',
    teacherName: '李四',
    formName: '教学获奖评分表',
    formType: 'teaching',
    submitTime: '2023-12-05 14:30:00',
    status: 'approved',
    fields: [
      { label: '获奖名称', type: 'text', value: '优秀教学成果奖' },
      { label: '获奖级别', type: 'select', value: '省级' },
      { label: '获奖时间', type: 'date', value: '2023-11-15' },
      { label: '获奖证书', type: 'file', value: '/path/to/certificate.pdf' }
    ],
    comment: '材料完整，符合要求'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 2,
  showSizeChanger: true,
  showQuickJumper: true,
})

// 查看相关
const viewModalVisible = ref(false)
const currentSubmission = ref(null)

// 驳回相关
const rejectModalVisible = ref(false)
const rejectFormRef = ref(null)
const rejectForm = reactive({
  reason: ''
})
const rejectRules = {
  reason: [
    { required: true, message: '请输入驳回原因', trigger: 'blur' },
    { min: 5, message: '驳回原因不能少于5个字符', trigger: 'blur' }
  ]
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    pending: 'orange',
    approved: 'green',
    rejected: 'red'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    pending: '待审核',
    approved: '已审核',
    rejected: '已驳回'
  }
  return textMap[status] || status
}

// 查看提交
const viewSubmission = (record) => {
  currentSubmission.value = record
  viewModalVisible.value = true
}

// 通过提交
const handleApprove = (record) => {
  record.status = 'approved'
  message.success('已通过该提交')
}

// 驳回提交
const handleReject = (record) => {
  currentSubmission.value = record
  rejectForm.reason = ''
  rejectModalVisible.value = true
}

// 删除提交
const deleteSubmission = (record) => {
  data.value = data.value.filter(item => item.key !== record.key)
  message.success('提交记录已删除')
}

// 搜索
const handleSearch = () => {
  console.log('Search form:', searchForm)
  // 这里添加搜索逻辑
}

// 重置搜索
const handleReset = () => {
  searchForm.teacherName = ''
  searchForm.formType = undefined
  searchForm.status = undefined
}

// 处理查看模态框确认
const handleViewModalOk = () => {
  viewModalVisible.value = false
}

// 处理查看模态框取消
const handleViewModalCancel = () => {
  viewModalVisible.value = false
}

// 处理驳回模态框确认
const handleRejectModalOk = () => {
  rejectFormRef.value.validate().then(() => {
    currentSubmission.value.status = 'rejected'
    currentSubmission.value.comment = rejectForm.reason
    rejectModalVisible.value = false
    message.success('已驳回该提交')
  }).catch(error => {
    console.log('Validation failed:', error)
  })
}

// 处理驳回模态框取消
const handleRejectModalCancel = () => {
  rejectModalVisible.value = false
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
  console.log('Table changed:', pag, filters, sorter)
}
</script>

<style scoped>
.form-submissions {
  padding: 24px;
}

.search-form {
  margin-bottom: 24px;
}

.submission-info {
  padding: 0 24px;
}

.submission-meta {
  color: #666;
  margin: 10px 0;
}

.submission-meta span {
  margin-right: 20px;
}

.submission-fields {
  margin: 20px 0;
}

.field-item {
  margin-bottom: 16px;
  display: flex;
}

.field-item label {
  width: 120px;
  font-weight: 500;
}

.field-value {
  flex: 1;
}

.submission-comment {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.text-danger {
  color: #ff4d4f;
}
</style> 