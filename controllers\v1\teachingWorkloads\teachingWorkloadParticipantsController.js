const { v4: uuidv4 } = require('uuid');
const teachingWorkloadParticipantsModel = require('../../../models/v1/mapping/teachingWorkloadParticipantsModel');
const teachingWorkloadsModel = require('../../../models/v1/mapping/teachingWorkloadsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const { Op } = require('sequelize');
const { getUserInfoFromRequest } = require('../../../utils/others');

/**
 * 获取教学工作量参与者列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getParticipants = async (req, res) => {
  try {
    const { 
      workloadId,
      participantName,
      page = 1, 
      pageSize = 10 
    } = req.query;
    
    // 构建查询条件
    const where = {};
    
    if (workloadId) {
      where.workloadId = workloadId;
    }
    
    // 分页参数
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);
    
    // 构建include条件
    const includeConditions = [
      {
        model: userModel,
        as: 'participant',
        attributes: ['id', 'nickName', 'studentNumber'],
        required: false
      },
      {
        model: teachingWorkloadsModel,
        as: 'workload',
        attributes: ['id', 'courseName', 'semester'],
        required: false
      }
    ];
    
    // 如果有参与者姓名筛选，添加到include条件中
    if (participantName) {
      includeConditions[0].where = {
        nickName: { [Op.like]: `%${participantName}%` }
      };
      includeConditions[0].required = true;
    }
    
    // 查询参与者列表
    const { count, rows } = await teachingWorkloadParticipantsModel.findAndCountAll({
      where,
      include: includeConditions,
      offset,
      limit,
      order: [['createdAt', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
    
  } catch (error) {
    console.error('获取教学工作量参与者列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教学工作量参与者列表失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 创建教学工作量参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createParticipant = async (req, res) => {
  try {
    const {
      workloadId,
      participantId,
      employeeNumber,
      allocationRatio,
      isLeader
    } = req.body;
    
    // 验证必填字段
    if (!workloadId || !participantId || allocationRatio === undefined) {
      return res.status(400).json({
        code: 400,
        message: '请填写所有必填字段',
        data: null
      });
    }
    
    // 验证工作量是否存在
    const workload = await teachingWorkloadsModel.findByPk(workloadId);
    if (!workload) {
      return res.status(404).json({
        code: 404,
        message: '工作量不存在',
        data: null
      });
    }
    
    // 验证用户是否存在
    const user = await userModel.findByPk(participantId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }
    
    // 检查是否已经是参与者
    const existingParticipant = await teachingWorkloadParticipantsModel.findOne({
      where: {
        workloadId,
        participantId
      }
    });
    
    if (existingParticipant) {
      return res.status(400).json({
        code: 400,
        message: '该用户已经是此工作量的参与者',
        data: null
      });
    }
    
    // 创建参与者记录
    const participant = await teachingWorkloadParticipantsModel.create({
      id: uuidv4(),
      workloadId,
      participantId,
      employeeNumber,
      allocationRatio,
      isLeader: isLeader || false
    });
    
    console.log(`✅ 创建成功: 参与者ID ${participant.id}`);
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: participant
    });
    
  } catch (error) {
    console.error('❌ 创建教学工作量参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: `创建教学工作量参与者失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 更新教学工作量参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateParticipant = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      employeeNumber,
      allocationRatio,
      isLeader
    } = req.body;
    
    // 查询参与者是否存在
    const participant = await teachingWorkloadParticipantsModel.findByPk(id);
    
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '参与者不存在',
        data: null
      });
    }
    
    // 更新参与者信息
    await participant.update({
      employeeNumber,
      allocationRatio,
      isLeader
    });
    
    console.log(`✅ 更新成功: 参与者ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: null
    });
    
  } catch (error) {
    console.error('❌ 更新教学工作量参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: `更新教学工作量参与者失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 删除教学工作量参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteParticipant = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少参与者ID',
        data: null
      });
    }
    
    // 查询参与者是否存在
    const participant = await teachingWorkloadParticipantsModel.findByPk(id);
    
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '参与者不存在',
        data: null
      });
    }
    
    // 删除参与者记录
    await participant.destroy();
    
    console.log(`✅ 删除成功: 参与者ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
    
  } catch (error) {
    console.error('❌ 删除教学工作量参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: `删除教学工作量参与者失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取教学工作量参与者详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getParticipantDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少参与者ID',
        data: null
      });
    }
    
    // 查询参与者详情
    const participant = await teachingWorkloadParticipantsModel.findByPk(id, {
      include: [
        {
          model: userModel,
          as: 'participant',
          attributes: ['id', 'nickName', 'studentNumber'],
          required: false
        },
        {
          model: teachingWorkloadsModel,
          as: 'workload',
          attributes: ['id', 'courseName', 'semester'],
          required: false
        }
      ]
    });
    
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '参与者不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: participant
    });
    
  } catch (error) {
    console.error('获取教学工作量参与者详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教学工作量参与者详情失败: ${error.message}`,
      data: null
    });
  }
};
