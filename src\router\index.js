import {createRouter, createWebHashHistory} from 'vue-router'
import routes from './routes'
import dbUtils from '../libs/util.strotage.js'
import {useSearchStore} from '@/stores/search.js'
import { ZyNotification } from '../libs/util.toast'
import { useUserRole } from '../../composables/useUserRole'

const router = createRouter({
    history: createWebHashHistory(import.meta.env.BASE_URL),
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition;
        } else {
            return {x: 0, y: 0};
        }
    },
    routes
})

function hasPermission(perms, route) {
    if (perms.includes('*')) return true
    if (route.meta && route.meta.perms) {
        // 如果路由对象定义了 meta 属性或者定义 meta.perms 属性，那么就根据权限值来判断是否具有权限
        return perms.some(perm => route.meta.perms.includes(perm))
    } else {
        // 如果路由对象没有定义 meta 属性或者没有定义 meta.perms 属性，那么默认认为具有权限，返回 true。
        return true
    }
}

router.beforeEach(async (to, from, next) => {
    console.log('路由守卫 - 当前路由:', to.fullPath)
    const searchStore = useSearchStore()
    searchStore.showSearchPanel(false)
    
    // 检查是否需要登录
    if (to.meta.requiresAuth) {
        console.log('路由守卫 - 该路由需要认证')
        
        // 尝试从localStorage同步token到dbUtils
        let token = dbUtils.get('token')
        if (!token) {
            console.log('路由守卫 - dbUtils中未找到token，尝试从localStorage获取')
            token = localStorage.getItem(`zyadmin-1.0.0-token`)
            if (token) {
                console.log('路由守卫 - 从localStorage找到token，同步到dbUtils')
                // 确保token有Bearer前缀
                if (!token.startsWith('Bearer ')) {
                    token = 'Bearer ' + token
                }
                dbUtils.set('token', token)
            }
        }
        
        // 重新从dbUtils获取token
        token = dbUtils.get('token')
        
        // 检查用户是否已经登录
        if (token) {
            console.log('路由守卫 - 找到有效token')
            
            // 已经登录 访问登录页则直接跳到主页
            if (to.name === 'login') {
                console.log('路由守卫 - 已登录用户试图访问登录页，重定向到首页')
                return next('/');
            }
            
            // 检查用户信息
            const userInfo = dbUtils.get('userInfo')
            if (!userInfo) {
                console.log('路由守卫 - 找到token但没有用户信息，可能是刚登录或刷新页面')
                // 对于刚登录的用户，可能用户信息还未加载完毕
                // 如果来自登录页，允许继续导航，用户信息会在稍后加载
                if (from.name === 'login') {
                    console.log('路由守卫 - 来自登录页，允许访问:', to.fullPath)
                    return next()
                }
            }
            
            // 获取用户角色信息
            try {
                console.log('路由守卫 - 尝试获取用户角色信息')
                const { role, getRoleFromStorage, getUserRole } = useUserRole()
                
                // 获取用户ID
                let userId = null;
                try {
                    userId = localStorage.getItem('zyadmin-1.0.0-userId');
                    if (!userId && userInfo) {
                        userId = userInfo.id;
                    }
                    
                    if (!userId) {
                        // 尝试从userInfo中获取
                        const userInfoStr = localStorage.getItem('zyadmin-1.0.0-userInfo');
                        if (userInfoStr) {
                            const parsedUserInfo = JSON.parse(userInfoStr);
                            userId = parsedUserInfo.id;
                        }
                    }
                } catch (e) {
                    console.error('路由守卫 - 获取用户ID失败:', e);
                }
                
                if (!userId) {
                    console.warn('路由守卫 - 未找到用户ID，无法获取角色信息');
                    return next();
                }
                
                // 首先从本地存储获取角色信息
                const cachedRole = getRoleFromStorage()
                if (!cachedRole) {
                    console.log('路由守卫 - 本地未找到角色信息，从服务器获取')
                    // 如果本地没有角色信息，则从服务器获取
                    try {
                        await getUserRole(userId)
                        console.log('路由守卫 - 成功从服务器获取角色信息:', role.value)
                    } catch (error) {
                        console.error('路由守卫 - 获取角色信息失败:', error)
                        // 获取失败不阻止导航
                    }
                } else {
                    console.log('路由守卫 - 从本地存储获取到角色信息:', cachedRole)
                }
            } catch (error) {
                console.error('路由守卫 - 角色处理出错:', error)
                // 错误不阻止导航
            }
            
            // 检查权限
            const permissionList = dbUtils.get('perms');
            if (!permissionList || !Array.isArray(permissionList) || permissionList.length === 0) {
                console.log('路由守卫 - 未找到权限信息或权限列表为空，可能是刚登录或会话已过期');
                
                // 如果是刚登录或正在加载首页，允许访问
                if (from.name === 'login' || to.path === '/index' || to.name === 'index') {
                    console.log('路由守卫 - 来自登录页或访问首页，允许访问:', to.fullPath);
                    
                    // 如果权限列表为空，设置默认权限
                    if (!permissionList || !Array.isArray(permissionList) || permissionList.length === 0) {
                        console.log('路由守卫 - 设置默认权限');
                        dbUtils.set('perms', ['index']);
                    }
                    
                    return next();
                }
                
                // 其他情况，清空缓存并重定向到登录页
                console.log('路由守卫 - 清除缓存并重定向到登录页');
                dbUtils.clear();
                ZyNotification.warning('会话已过期，请重新登录');
                // 重定向到登录页
                return next({name: 'login'});
            }
            
            // 检查是否有权限访问
            const hasRoles = hasPermission(permissionList, to)
            if (hasRoles) {
                console.log('路由守卫 - 权限验证通过，允许访问:', to.fullPath)
                // 有权限直接访问
                return next();
            }
            
            console.log('路由守卫 - 权限不足，重定向到401页面')
            // 无权限则重定向到401
            return next({name: '401'});
        }
        
        // 未登录
        console.log('路由守卫 - 未找到token，用户未登录')
        // 清空所有缓存数据
        dbUtils.clear()
        
        // 如果非登录页，显示提示
        if (to.name !== 'login') {
            ZyNotification.warning('请先登录')
        }
        
        // 重定向到登录页
        return next({name: 'login'});
    }
    
    // 无需登录的页面 直接访问即可
    console.log('路由守卫 - 该路由无需认证，允许访问:', to.fullPath)
    next();
});


router.afterEach((to, from) => {
    window.document.title = to.meta.title + " - ZY'Admin";
})

router.onError((error) => {
    // 在这里处理路由错误
    console.error('路由错误:', error);
    ZyNotification.error('页面加载出错: ' + error.message)
});

export default router
