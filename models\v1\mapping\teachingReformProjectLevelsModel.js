const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义教学改革项目级别模型
const TeachingReformProjectLevel = sequelize.define('teaching_reform_projectLevels', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '级别ID'
    },
    levelName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '级别名称（唯一）'
    },
    score: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        comment: '对应分数'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'teaching_reform_projectLevels',
    timestamps: true,
    indexes: [
        {
            name: 'uk_level',
            unique: true,
            fields: ['levelName']
        }
    ]
});

module.exports = TeachingReformProjectLevel;
