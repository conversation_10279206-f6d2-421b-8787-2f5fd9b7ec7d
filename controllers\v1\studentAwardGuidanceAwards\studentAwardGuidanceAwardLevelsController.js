const { Op } = require('sequelize');
const studentAwardGuidanceAwardLevelModel = require('../../../models/v1/mapping/studentAwardGuidanceAwardLevelsModel');
const studentAwardGuidanceParticipantModel = require('../../../models/v1/mapping/studentAwardGuidanceParticipantsModel');
const studentAwardGuidanceAwardModel = require('../../../models/v1/mapping/studentAwardGuidanceAwardsModel');
const { getTimeIntervalByName } = require('../../../utils/others');

/**
 * 获取获奖级别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardLevels = async (req, res) => {
  try {
    const { levelName, page = 1, pageSize = 10 } = req.body;
    
    // 构建查询条件
    const where = {};
    if (levelName) {
      where.levelName = { [Op.like]: `%${levelName}%` };
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 查询数据
    const { count, rows } = await studentAwardGuidanceAwardLevelModel.findAndCountAll({
      where,
      offset,
      limit,
      order: [['score', 'DESC']], // 按分数降序排列
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取获奖级别列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取获奖级别列表失败',
      error: error.message
    });
  }
};

/**
 * 获取所有获奖级别（不分页）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllAwardLevels = async (req, res) => {
  try {
    // 查询所有获奖级别数据
    const awardLevels = await studentAwardGuidanceAwardLevelModel.findAll({
      order: [['score', 'DESC']], // 按分数降序排列
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: awardLevels
    });
  } catch (error) {
    console.error('获取所有获奖级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有获奖级别失败',
      error: error.message
    });
  }
};

/**
 * 创建获奖级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAwardLevel = async (req, res) => {
  try {
    const { levelName, score } = req.body;
    
    // 验证必要字段
    if (!levelName || score === undefined) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要字段',
        data: null
      });
    }
    
    // 创建获奖级别
    const awardLevel = await studentAwardGuidanceAwardLevelModel.create({
      levelName,
      score
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: awardLevel
    });
  } catch (error) {
    console.error('创建获奖级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建获奖级别失败',
      error: error.message
    });
  }
};

/**
 * 更新获奖级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateAwardLevel = async (req, res) => {
  try {
    const { id } = req.params;
    const { levelName, score } = req.body;
    
    // 验证ID
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少获奖级别ID',
        data: null
      });
    }
    
    // 查询获奖级别是否存在
    const awardLevel = await studentAwardGuidanceAwardLevelModel.findByPk(id);
    
    if (!awardLevel) {
      return res.status(404).json({
        code: 404,
        message: '未找到获奖级别',
        data: null
      });
    }
    
    // 更新获奖级别
    const updateData = {};
    if (levelName !== undefined) updateData.levelName = levelName;
    if (score !== undefined) updateData.score = score;
    
    await awardLevel.update(updateData);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: awardLevel
    });
  } catch (error) {
    console.error('更新获奖级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新获奖级别失败',
      error: error.message
    });
  }
};

/**
 * 删除获奖级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAwardLevel = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 验证ID
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少获奖级别ID',
        data: null
      });
    }
    
    // 查询获奖级别是否存在
    const awardLevel = await studentAwardGuidanceAwardLevelModel.findByPk(id);
    
    if (!awardLevel) {
      return res.status(404).json({
        code: 404,
        message: '未找到获奖级别',
        data: null
      });
    }
    
    // 删除获奖级别
    await awardLevel.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除获奖级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除获奖级别失败',
      error: error.message
    });
  }
};

/**
 * 获取所有级别及其获奖数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelsWithCount = async (req, res) => {
  try {
    // 获取所有级别
    const levels = await studentAwardGuidanceAwardLevelModel.findAll({
      order: [['score', 'DESC']] // 按分数降序排列
    });

    // 为每个级别查询相关的获奖记录数量
    const levelsWithCount = await Promise.all(levels.map(async (level) => {
      const count = await studentAwardGuidanceAwardModel.count({
        where: { levelId: level.id }
      });

      const levelData = level.toJSON();
      levelData.awardCount = count;
      return levelData;
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levelsWithCount
    });
  } catch (error) {
    console.error('获取所有级别及其获奖数量失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有级别及其获奖数量失败',
      error: error.message
    });
  }
};

/**
 * 获取获奖级别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少获奖级别ID',
        data: null
      });
    }
    
    // 查询获奖级别详情
    const level = await studentAwardGuidanceAwardLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到获奖级别',
        data: null
      });
    }
    
    // 获取使用该级别的获奖记录数量
    const awardCount = await studentAwardGuidanceAwardModel.count({
      where: { levelId: id }
    });
    
    // 组装返回数据
    const levelData = level.toJSON();
    levelData.awardCount = awardCount;
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levelData
    });
  } catch (error) {
    console.error('获取获奖级别详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取获奖级别详情失败',
      error: error.message
    });
  }
};

/**
 * 创建级别 - 路由别名方法
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createLevel = async (req, res) => {
  return this.createAwardLevel(req, res);
};

/**
 * 更新级别 - 路由别名方法
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateLevel = async (req, res) => {
  return this.updateAwardLevel(req, res);
};

/**
 * 删除级别 - 路由别名方法
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteLevel = async (req, res) => {
  return this.deleteAwardLevel(req, res);
};

/**
 * 获取获奖级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("student_award_guidance");
    
    // 获取所有级别
    const levels = await studentAwardGuidanceAwardLevelModel.findAll({
      order: [['score', 'DESC']] // 按分数降序排列
    });
    
    // 构建查询条件（如果指定了用户ID，则通过参与者表过滤）
    const levelCounts = await Promise.all(levels.map(async (level) => {
      // 构建基本查询条件
      const whereCondition = { 
        levelId: level.id,
      };
      
      // 根据审核状态过滤
      if (reviewStatus === 'reviewed') {
        whereCondition.ifReviewer = 1; // 已通过
      } else if (reviewStatus === 'rejected') {
        whereCondition.ifReviewer = 0; // 已拒绝
      } else if (reviewStatus === 'pending') {
        whereCondition.ifReviewer = null; // 待审核
      }
      
      // 查询符合条件的记录数量
      let count;
      
      if (userId) {
        // 如果指定了userId，则需要查询用户参与的获奖记录
        // 这部分需要通过连接查询实现，或者采用两步查询
        const awards = await studentAwardGuidanceAwardModel.findAll({
          where: whereCondition,
          include: [
            {
              model: studentAwardGuidanceParticipantModel,
              as: 'participants',
              where: { userId },
              required: true
            }
          ]
        });
        
        // 根据时间范围过滤
        if (range !== 'all' && timeInterval) {
          const startTime = new Date(timeInterval.startTime);
          const endTime = new Date(timeInterval.endTime);
          
          const filteredAwards = awards.filter(award => {
            const awardDate = new Date(award.awardDate);
            const isInRange = awardDate >= startTime && awardDate <= endTime;
            return range === 'in' ? isInRange : !isInRange;
          });
          
          count = filteredAwards.length;
        } else {
          count = awards.length;
        }
      } else {
        // 如果未指定userId，则直接查询所有记录
        
        // 根据时间范围构建查询条件
        if (range !== 'all' && timeInterval) {
          if (range === 'in') {
            whereCondition.awardDate = {
              [Op.between]: [timeInterval.startTime, timeInterval.endTime]
            };
          } else if (range === 'out') {
            whereCondition.awardDate = {
              [Op.or]: [
                { [Op.lt]: timeInterval.startTime },
                { [Op.gt]: timeInterval.endTime }
              ]
            };
          }
        }
        
        count = await studentAwardGuidanceAwardModel.count({
          where: whereCondition
        });
      }
      
      return {
        name: level.levelName,
        value: count
      };
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levelCounts
    });
  } catch (error) {
    console.error('获取获奖级别分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取获奖级别分布数据失败',
      error: error.message
    });
  }
}; 