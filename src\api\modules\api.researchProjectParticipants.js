import request from '../server'

/**
 * 获取科研项目参与者列表
 * @param {Object} params - 查询参数
 * @param {string} [params.projectId] - 项目ID
 * @param {string} [params.userId] - 用户ID
 * @param {boolean} [params.isLeader] - 是否负责人
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @returns {Promise} - 请求结果
 */
export function getProjectParticipants(params) {
  return request.post('/sys/research-project-participants/list', params || {})
}

/**
 * 获取项目参与者详情
 * @param {string} id - 参与者ID
 * @returns {Promise} - 请求结果
 */
export function getProjectParticipantDetail(id) {
  return request.get(`/sys/research-project-participants/${id}`)
}

/**
 * 创建项目参与者
 * @param {Object} data - 参与者数据
 * @param {string} data.projectId - 项目ID
 * @param {string} data.userId - 用户ID
 * @param {number} data.allocationRatio - 分配比例（0-1之间）
 * @param {number} [data.participantRank] - 参与者排名
 * @param {boolean} [data.isLeader=false] - 是否负责人
 * @returns {Promise} - 请求结果
 */
export function createProjectParticipant(data) {
  return request.post('/sys/research-project-participants', data)
}

/**
 * 更新项目参与者
 * @param {string} id - 参与者ID
 * @param {Object} data - 参与者数据
 * @param {number} [data.allocationRatio] - 分配比例（0-1之间）
 * @param {number} [data.participantRank] - 参与者排名
 * @param {boolean} [data.isLeader] - 是否负责人
 * @returns {Promise} - 请求结果
 */
export function updateProjectParticipant(id, data) {
  return request.put(`/sys/research-project-participants/${id}`, data)
}

/**
 * 删除项目参与者
 * @param {string} id - 参与者ID
 * @returns {Promise} - 请求结果
 */
export function deleteProjectParticipant(id) {
  return request.del(`/sys/research-project-participants/${id}`)
}

/**
 * 批量添加项目参与者
 * @param {Object} data - 批量添加数据
 * @param {string} data.projectId - 项目ID
 * @param {Array} data.participants - 参与者数组
 * @param {string} data.participants[].userId - 用户ID
 * @param {number} data.participants[].allocationRatio - 分配比例（0-1之间）
 * @param {number} [data.participants[].participantRank] - 参与者排名
 * @param {boolean} [data.participants[].isLeader=false] - 是否负责人
 * @returns {Promise} - 请求结果
 */
export function batchAddProjectParticipants(data) {
  return request.post('/sys/research-project-participants/batch', data)
} 