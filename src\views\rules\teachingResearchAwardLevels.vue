<template>
  <div class="teaching-research-award-levels-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>教学科技奖励级别管理</h2>
      <p class="page-description">管理教学科技奖励的级别分类和对应分数</p>
    </div>

    <!-- 搜索表单 -->
    <a-card class="search-card" :bordered="false">
      <a-form
        ref="searchFormRef"
        :model="searchForm"
        layout="inline"
        class="search-form"
      >
        <a-form-item label="级别名称" name="levelName">
          <a-input
            v-model:value="searchForm.levelName"
            placeholder="请输入级别名称"
            style="width: 200px"
            allow-clear
          />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <SearchOutlined /> 搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined /> 重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作按钮和数据表格 -->
    <a-card :bordered="false">
      <!-- 操作按钮栏 -->
      <div class="action-bar">
        <div class="action-left">
          <a-button
            v-permission="'rule:teachingResearchAwardLevel:create'"
            type="primary"
            @click="handleCreate"
          >
            <PlusOutlined /> 新增级别
          </a-button>
        </div>

        <div class="action-right">
          <a-button
            v-permission="'rule:teachingResearchAwardLevel:export'"
            @click="handleExport"
          >
            <ExportOutlined /> 导出数据
          </a-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 级别名称 -->
        <template #levelName="{ record }">
          <div class="level-name-cell">
            <a-tag :color="getLevelColor(record.score)">
              {{ record.levelName }}
            </a-tag>
          </div>
        </template>

        <!-- 基础分数 -->
        <template #score="{ record }">
          <span class="score-text">{{ record.score }} 分</span>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="record.status === 1 ? 'success' : 'default'">
            {{ record.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space>
            <a-button
              v-permission="'rule:teachingResearchAwardLevel:update'"
              type="link"
              size="small"
              @click="handleEdit(record)"
            >
              <EditOutlined /> 编辑
            </a-button>

            <a-button
              v-permission="'rule:teachingResearchAwardLevel:delete'"
              type="link"
              size="small"
              danger
              @click="handleDelete(record)"
            >
              <DeleteOutlined /> 删除
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      :visible="formVisible"
      :title="isEdit ? '编辑级别' : '新增级别'"
      width="600px"
      :confirm-loading="formLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="级别名称" name="levelName">
          <a-input
            v-model:value="form.levelName"
            placeholder="请输入级别名称"
            :maxlength="100"
            show-count
          />
        </a-form-item>

        <a-form-item label="基础分数" name="score">
          <a-input-number
            v-model:value="form.score"
            placeholder="请输入基础分数"
            :min="0"
            :max="1000"
            :precision="2"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="级别描述" name="description">
          <a-textarea
            v-model:value="form.description"
            placeholder="请输入级别描述"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="form.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExportOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import {
  getAwardLevelsList,
  createAwardLevel,
  updateAwardLevel,
  deleteAwardLevel
} from '@/api/modules/api.teachingResearchAwards'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const formVisible = ref(false)
const formLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  levelName: ''
})

// 表单数据
const formRef = ref()
const form = reactive({
  levelName: '',
  score: 0,
  description: '',
  status: 1
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '级别名称',
    key: 'levelName',
    width: 200,
    slots: { customRender: 'levelName' }
  },
  {
    title: '基础分数',
    key: 'score',
    width: 120,
    slots: { customRender: 'score' }
  },
  {
    title: '级别描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    slots: { customRender: 'action' }
  }
]

// 表单验证规则
const rules = {
  levelName: [
    { required: true, message: '请输入级别名称', trigger: 'blur' }
  ],
  score: [
    { required: true, message: '请输入基础分数', trigger: 'blur' }
  ]
}

// 工具函数
const getLevelColor = (score) => {
  if (score >= 80) return 'red'
  if (score >= 60) return 'orange'
  if (score >= 40) return 'blue'
  if (score >= 20) return 'green'
  return 'default'
}

// 事件处理函数
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  pagination.current = 1
  loadData()
}

const handleCreate = () => {
  currentRecord.value = null
  isEdit.value = false
  resetForm()
  formVisible.value = true
}

const handleEdit = (record) => {
  console.log('编辑记录:', record)
  if (!record || !record.id) {
    message.error('无法获取记录信息')
    return
  }

  currentRecord.value = record
  isEdit.value = true
  Object.assign(form, {
    levelName: record.levelName,
    score: record.score,
    description: record.description || '',
    status: record.status
  })
  formVisible.value = true
}

const handleDelete = (record) => {
  console.log('删除记录:', record)
  if (!record || !record.id) {
    message.error('无法获取记录信息')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除级别"${record.levelName}"吗？`,
    async onOk() {
      try {
        console.log('删除请求数据:', { id: record.id })
        const response = await deleteAwardLevel({ id: record.id })
        if (response && response.code === 200) {
          message.success('删除成功')
          loadData()
        } else {
          message.error(response?.message || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

const handleExport = () => {
  message.info('导出功能开发中...')
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    formLoading.value = true

    let response
    if (isEdit.value) {
      // 验证编辑时必须有ID
      if (!currentRecord.value || !currentRecord.value.id) {
        message.error('无法获取记录ID，请重新选择要编辑的记录')
        return
      }

      console.log('更新数据:', {
        id: currentRecord.value.id,
        ...form
      })

      response = await updateAwardLevel({
        id: currentRecord.value.id,
        ...form
      })
    } else {
      console.log('创建数据:', form)
      response = await createAwardLevel(form)
    }

    if (response && response.code === 200) {
      message.success(isEdit.value ? '更新成功' : '创建成功')
      formVisible.value = false
      loadData()
    } else {
      message.error(response?.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    message.error('请检查表单填写')
  } finally {
    formLoading.value = false
  }
}

const handleCancel = () => {
  formVisible.value = false
  resetForm()
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    levelName: '',
    score: 0,
    description: '',
    status: 1
  })
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      levelName: searchForm.levelName || undefined
    }

    const response = await getAwardLevelsList(params)

    if (response && response.code === 200) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
    } else {
      message.error(response?.message || '获取数据失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.teaching-research-award-levels-container {
  padding: 16px;
}

.page-header {
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #666;
}

.search-card {
  margin-bottom: 16px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.action-left,
.action-right {
  display: flex;
  gap: 8px;
}

.level-name-cell {
  display: flex;
  align-items: center;
}

.score-text {
  color: #1890ff;
  font-weight: 500;
}
</style>
