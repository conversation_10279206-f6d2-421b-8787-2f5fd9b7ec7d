import request from '../server'

/**
 * 获取教材与著作类别列表
 * @returns {Promise} 请求的结果
 */
export function getTextbookCategories() {
  return request.get('/textbookCategories/categories')
}

/**
 * 获取带有数量统计的教材与著作类别列表
 * @returns {Promise} 请求的结果
 */
export function getTextbookCategoriesWithCount() {
  return request.get('/textbookCategories/categories-with-count')
}

/**
 * 获取教材与著作类别详情
 * @param {String} id 类别ID
 * @returns {Promise} 请求的结果
 */
export function getTextbookCategoryDetail(id) {
  return request.get(`/textbookCategories/category/${id}`)
}

/**
 * 创建教材与著作类别
 * @param {Object} params 请求参数
 * @returns {Promise} 请求的结果
 */
export function createTextbookCategory(params) {
  return request.post('/textbookCategories/category/create', params)
}

/**
 * 更新教材与著作类别
 * @param {Object} params 请求参数
 * @returns {Promise} 请求的结果
 */
export function updateTextbookCategory(params) {
  return request.post('/textbookCategories/category/update', params)
}

/**
 * 删除教材与著作类别
 * @param {Object} params 请求参数
 * @returns {Promise} 请求的结果
 */
export function deleteTextbookCategory(params) {
  return request.post('/textbookCategories/category/delete', params)
}