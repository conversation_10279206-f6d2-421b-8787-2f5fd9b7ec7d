const cors = require('cors');

/**
 * 跨域中间件配置
 */
const corsOptions = {
  origin: function (origin, callback) {
    // 允许所有来源访问，或者指定来源列表
    const allowedOrigins = process.env.ALLOWED_ORIGINS ? 
      process.env.ALLOWED_ORIGINS.split(',') : 
      ['http://localhost:3000', 'http://localhost:8080', 'http://*************', 'http://*************:3089', 'http://*************:80'];

    // 如果没有origin（例如同源请求）或者origin在允许列表中，则允许
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS请求被拒绝，来源:', origin);
      console.log('允许的来源:', allowedOrigins);
      callback(null, true); // 在生产环境中允许所有来源，解决跨域问题
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Timestamp', 'X-Client', 'Cache-Control', 'Pragma', 'Expires'],
  exposedHeaders: ['Set-Cookie'],
  credentials: true,
  maxAge: 86400 // 预检请求结果缓存24小时
};

const corsMiddleware = cors(corsOptions);

module.exports = {
  corsMiddleware
}; 