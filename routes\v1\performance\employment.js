const express = require('express');
const employmentController = require('../../../controllers/v1/employment.controller');

const router = express.Router();

// 获取就业质量数据列表
router.post('/list', employmentController.getEmploymentRecords);
// 获取指定就业质量数据
router.post('/detail', employmentController.getEmploymentById);
// 创建就业质量数据
router.post('/create', employmentController.createEmployment);
// 更新就业质量数据
router.post('/update', employmentController.updateEmployment);
// 删除就业质量数据
router.post('/delete', employmentController.deleteEmployment);
// 导入就业质量数据
router.post('/import', employmentController.importEmployment);
// 导出就业质量数据
router.post('/export', employmentController.exportEmployment);

module.exports = router; 