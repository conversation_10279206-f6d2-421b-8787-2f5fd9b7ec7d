<template>
  <div class="data-analysis">
    <a-card title="数据分析" :bordered="false">
      <a-alert
        message="数据分析说明"
        description="本页面用于对收集到的数据进行统计和分析。您可以查看各类数据的统计信息、趋势分析、对比分析等。"
        type="info"
        show-icon
        class="mb-4"
      />

      <a-row :gutter="16" class="mb-4">
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <TeamOutlined />
                教师总数
              </span>
            </template>
            <div class="card-content">
              <span class="number">128</span>
              <span class="unit">人</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <ExperimentOutlined />
                科研项目
              </span>
            </template>
            <div class="card-content">
              <span class="number">256</span>
              <span class="unit">个</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <TrophyOutlined />
                教学获奖
              </span>
            </template>
            <div class="card-content">
              <span class="number">89</span>
              <span class="unit">项</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <GlobalOutlined />
                国际交流
              </span>
            </template>
            <div class="card-content">
              <span class="number">45</span>
              <span class="unit">次</span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="overview" tab="数据概览">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="科研项目分布" :bordered="false">
                <div ref="researchChartRef" style="height: 300px"></div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="教学获奖分布" :bordered="false">
                <div ref="teachingChartRef" style="height: 300px"></div>
              </a-card>
            </a-col>
          </a-row>
          <a-row :gutter="16" class="mt-4">
            <a-col :span="12">
              <a-card title="国际交流趋势" :bordered="false">
                <div ref="internationalChartRef" style="height: 300px"></div>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="社会服务分布" :bordered="false">
                <div ref="socialChartRef" style="height: 300px"></div>
              </a-card>
            </a-col>
          </a-row>
        </a-tab-pane>

        <a-tab-pane key="trend" tab="趋势分析">
          <a-form layout="inline" :model="trendForm" class="search-form">
            <a-form-item label="数据类型">
              <a-select v-model:value="trendForm.dataType" placeholder="请选择数据类型" style="width: 200px">
                <a-select-option value="research">科研项目</a-select-option>
                <a-select-option value="teaching">教学获奖</a-select-option>
                <a-select-option value="international">国际交流</a-select-option>
                <a-select-option value="social">社会服务</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="时间范围">
              <a-range-picker v-model:value="trendForm.dateRange" />
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleTrendSearch">
                  <template #icon><SearchOutlined /></template>
                  分析
                </a-button>
                <a-button @click="handleTrendReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>

          <a-card title="趋势图表" :bordered="false">
            <div ref="trendChartRef" style="height: 400px"></div>
          </a-card>

          <a-card title="趋势数据" :bordered="false" class="mt-4">
            <a-table :columns="trendColumns" :data-source="trendData" :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'change'">
                  <span :class="record.change >= 0 ? 'text-success' : 'text-danger'">
                    {{ record.change >= 0 ? '+' : '' }}{{ record.change }}%
                  </span>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-tab-pane>

        <a-tab-pane key="comparison" tab="对比分析">
          <a-form layout="inline" :model="comparisonForm" class="search-form">
            <a-form-item label="对比类型">
              <a-select v-model:value="comparisonForm.type" placeholder="请选择对比类型" style="width: 200px">
                <a-select-option value="department">院系对比</a-select-option>
                <a-select-option value="title">职称对比</a-select-option>
                <a-select-option value="age">年龄对比</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="数据类型">
              <a-select v-model:value="comparisonForm.dataType" placeholder="请选择数据类型" style="width: 200px">
                <a-select-option value="research">科研项目</a-select-option>
                <a-select-option value="teaching">教学获奖</a-select-option>
                <a-select-option value="international">国际交流</a-select-option>
                <a-select-option value="social">社会服务</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleComparisonSearch">
                  <template #icon><SearchOutlined /></template>
                  分析
                </a-button>
                <a-button @click="handleComparisonReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>

          <a-card title="对比图表" :bordered="false">
            <div ref="comparisonChartRef" style="height: 400px"></div>
          </a-card>

          <a-card title="对比数据" :bordered="false" class="mt-4">
            <a-table :columns="comparisonColumns" :data-source="comparisonData" :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'percentage'">
                  <a-progress :percent="record.percentage" :stroke-color="getProgressColor(record.percentage)" />
                </template>
              </template>
            </a-table>
          </a-card>
        </a-tab-pane>

        <a-tab-pane key="correlation" tab="相关性分析">
          <a-form layout="inline" :model="correlationForm" class="search-form">
            <a-form-item label="分析维度">
              <a-select v-model:value="correlationForm.dimension" placeholder="请选择分析维度" style="width: 200px">
                <a-select-option value="research_teaching">科研与教学</a-select-option>
                <a-select-option value="research_international">科研与国际交流</a-select-option>
                <a-select-option value="teaching_social">教学与社会服务</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleCorrelationSearch">
                  <template #icon><SearchOutlined /></template>
                  分析
                </a-button>
                <a-button @click="handleCorrelationReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>

          <a-card title="相关性热力图" :bordered="false">
            <div ref="correlationChartRef" style="height: 400px"></div>
          </a-card>

          <a-card title="相关性数据" :bordered="false" class="mt-4">
            <a-table :columns="correlationColumns" :data-source="correlationData" :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'correlation'">
                  <span :class="getCorrelationClass(record.correlation)">
                    {{ record.correlation }}
                  </span>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  TeamOutlined,
  ExperimentOutlined,
  TrophyOutlined,
  GlobalOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import * as echarts from 'echarts'

// 当前激活的标签页
const activeTab = ref('overview')

// 图表引用
const researchChartRef = ref(null)
const teachingChartRef = ref(null)
const internationalChartRef = ref(null)
const socialChartRef = ref(null)
const trendChartRef = ref(null)
const comparisonChartRef = ref(null)
const correlationChartRef = ref(null)

// 趋势分析表单
const trendForm = reactive({
  dataType: undefined,
  dateRange: []
})

// 趋势分析表格列定义
const trendColumns = [
  {
    title: '时间',
    dataIndex: 'time',
    key: 'time',
  },
  {
    title: '数量',
    dataIndex: 'count',
    key: 'count',
  },
  {
    title: '环比变化',
    dataIndex: 'change',
    key: 'change',
  },
  {
    title: '同比变化',
    dataIndex: 'yearOnYear',
    key: 'yearOnYear',
  },
]

// 趋势分析数据
const trendData = ref([
  {
    key: '1',
    time: '2023-01',
    count: 25,
    change: 8.7,
    yearOnYear: 12.5,
  },
  {
    key: '2',
    time: '2023-02',
    count: 28,
    change: 12.0,
    yearOnYear: 15.8,
  },
  {
    key: '3',
    time: '2023-03',
    count: 32,
    change: 14.3,
    yearOnYear: 18.2,
  },
])

// 对比分析表单
const comparisonForm = reactive({
  type: undefined,
  dataType: undefined
})

// 对比分析表格列定义
const comparisonColumns = [
  {
    title: '对比项',
    dataIndex: 'item',
    key: 'item',
  },
  {
    title: '数量',
    dataIndex: 'count',
    key: 'count',
  },
  {
    title: '占比',
    dataIndex: 'percentage',
    key: 'percentage',
  },
]

// 对比分析数据
const comparisonData = ref([
  {
    key: '1',
    item: '计算机学院',
    count: 45,
    percentage: 35,
  },
  {
    key: '2',
    item: '经济学院',
    count: 38,
    percentage: 30,
  },
  {
    key: '3',
    item: '医学院',
    count: 25,
    percentage: 20,
  },
  {
    key: '4',
    item: '其他院系',
    count: 20,
    percentage: 15,
  },
])

// 相关性分析表单
const correlationForm = reactive({
  dimension: undefined
})

// 相关性分析表格列定义
const correlationColumns = [
  {
    title: '指标1',
    dataIndex: 'indicator1',
    key: 'indicator1',
  },
  {
    title: '指标2',
    dataIndex: 'indicator2',
    key: 'indicator2',
  },
  {
    title: '相关系数',
    dataIndex: 'correlation',
    key: 'correlation',
  },
  {
    title: '显著性',
    dataIndex: 'significance',
    key: 'significance',
  },
]

// 相关性分析数据
const correlationData = ref([
  {
    key: '1',
    indicator1: '科研项目数',
    indicator2: '教学获奖数',
    correlation: 0.75,
    significance: '显著',
  },
  {
    key: '2',
    indicator1: '科研项目数',
    indicator2: '国际交流次数',
    correlation: 0.82,
    significance: '显著',
  },
  {
    key: '3',
    indicator1: '教学获奖数',
    indicator2: '社会服务次数',
    correlation: 0.45,
    significance: '一般',
  },
])

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#52c41a'
  if (percentage >= 60) return '#1890ff'
  if (percentage >= 40) return '#faad14'
  return '#ff4d4f'
}

// 获取相关性样式类
const getCorrelationClass = (correlation) => {
  if (correlation >= 0.8) return 'text-success'
  if (correlation >= 0.5) return 'text-warning'
  return 'text-danger'
}

// 趋势分析搜索
const handleTrendSearch = () => {
  if (!trendForm.dataType) {
    message.warning('请选择数据类型')
    return
  }
  if (!trendForm.dateRange || trendForm.dateRange.length === 0) {
    message.warning('请选择时间范围')
    return
  }
  // 这里添加趋势分析逻辑
  initTrendChart()
}

// 趋势分析重置
const handleTrendReset = () => {
  trendForm.dataType = undefined
  trendForm.dateRange = []
}

// 对比分析搜索
const handleComparisonSearch = () => {
  if (!comparisonForm.type) {
    message.warning('请选择对比类型')
    return
  }
  if (!comparisonForm.dataType) {
    message.warning('请选择数据类型')
    return
  }
  // 这里添加对比分析逻辑
  initComparisonChart()
}

// 对比分析重置
const handleComparisonReset = () => {
  comparisonForm.type = undefined
  comparisonForm.dataType = undefined
}

// 相关性分析搜索
const handleCorrelationSearch = () => {
  if (!correlationForm.dimension) {
    message.warning('请选择分析维度')
    return
  }
  // 这里添加相关性分析逻辑
  initCorrelationChart()
}

// 相关性分析重置
const handleCorrelationReset = () => {
  correlationForm.dimension = undefined
}

// 初始化科研项目分布图表
const initResearchChart = () => {
  const chart = echarts.init(researchChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['国家级', '省级', '市级', '校级']
    },
    series: [
      {
        name: '项目级别',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 35, name: '国家级' },
          { value: 45, name: '省级' },
          { value: 25, name: '市级' },
          { value: 15, name: '校级' }
        ]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化教学获奖分布图表
const initTeachingChart = () => {
  const chart = echarts.init(teachingChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['国家级', '省级', '市级', '校级']
    },
    series: [
      {
        name: '获奖级别',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 15, name: '国家级' },
          { value: 30, name: '省级' },
          { value: 25, name: '市级' },
          { value: 19, name: '校级' }
        ]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化国际交流趋势图表
const initInternationalChart = () => {
  const chart = echarts.init(internationalChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['学术访问', '国际会议', '合作研究']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '学术访问',
        type: 'line',
        data: [3, 4, 5, 6, 4, 3]
      },
      {
        name: '国际会议',
        type: 'line',
        data: [2, 3, 4, 3, 5, 4]
      },
      {
        name: '合作研究',
        type: 'line',
        data: [1, 2, 3, 4, 3, 2]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化社会服务分布图表
const initSocialChart = () => {
  const chart = echarts.init(socialChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['社会培训', '技术咨询', '社会调研', '志愿服务']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '社会培训',
        type: 'bar',
        stack: 'total',
        data: [5, 6, 7, 8, 6, 5]
      },
      {
        name: '技术咨询',
        type: 'bar',
        stack: 'total',
        data: [3, 4, 5, 4, 6, 5]
      },
      {
        name: '社会调研',
        type: 'bar',
        stack: 'total',
        data: [2, 3, 4, 3, 4, 3]
      },
      {
        name: '志愿服务',
        type: 'bar',
        stack: 'total',
        data: [4, 5, 6, 5, 7, 6]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = () => {
  const chart = echarts.init(trendChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['数量', '环比', '同比']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '变化率(%)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '数量',
        type: 'bar',
        data: [25, 28, 32, 35, 30, 28]
      },
      {
        name: '环比',
        type: 'line',
        yAxisIndex: 1,
        data: [0, 12, 14, 9, -14, -7]
      },
      {
        name: '同比',
        type: 'line',
        yAxisIndex: 1,
        data: [12, 15, 18, 20, 15, 12]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化对比图表
const initComparisonChart = () => {
  const chart = echarts.init(comparisonChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['数量', '占比']
    },
    xAxis: {
      type: 'category',
      data: ['计算机学院', '经济学院', '医学院', '其他院系']
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '占比(%)',
        position: 'right',
        max: 100
      }
    ],
    series: [
      {
        name: '数量',
        type: 'bar',
        data: [45, 38, 25, 20]
      },
      {
        name: '占比',
        type: 'line',
        yAxisIndex: 1,
        data: [35, 30, 20, 15]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化相关性图表
const initCorrelationChart = () => {
  const chart = echarts.init(correlationChartRef.value)
  const option = {
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: ['科研项目', '教学获奖', '国际交流', '社会服务'],
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: ['科研项目', '教学获奖', '国际交流', '社会服务'],
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: -1,
      max: 1,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [{
      name: '相关系数',
      type: 'heatmap',
      data: [
        [0, 0, 1],
        [0, 1, 0.75],
        [0, 2, 0.82],
        [0, 3, 0.35],
        [1, 0, 0.75],
        [1, 1, 1],
        [1, 2, 0.45],
        [1, 3, 0.65],
        [2, 0, 0.82],
        [2, 1, 0.45],
        [2, 2, 1],
        [2, 3, 0.28],
        [3, 0, 0.35],
        [3, 1, 0.65],
        [3, 2, 0.28],
        [3, 3, 1]
      ],
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  chart.setOption(option)
}

// 组件挂载后初始化图表
onMounted(() => {
  initResearchChart()
  initTeachingChart()
  initInternationalChart()
  initSocialChart()
})
</script>

<style scoped>
.data-analysis {
  padding: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-content {
  text-align: center;
}

.card-content .number {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.card-content .unit {
  margin-left: 4px;
  color: #666;
}

.search-form {
  margin-bottom: 16px;
}

.text-success {
  color: #52c41a;
}

.text-warning {
  color: #faad14;
}

.text-danger {
  color: #ff4d4f;
}
</style> 