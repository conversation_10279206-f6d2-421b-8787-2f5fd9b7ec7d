<template>
  <div class="header-user">
    <a-dropdown v-if="userInfo?.nickname" class="header-user-dropdown">
      <div class="header-user-logo">
        <a-avatar :size="28" :src="userInfo?.avatar">
          <template #icon><UserOutlined /></template>
        </a-avatar>
        <span class="header-user-name">
           {{ userInfo?.nickname }}
        </span>
      </div>
      <template #overlay>
        <a-menu>
          <a-menu-item @click="logout">
            <span>
              <IconFont type="icon-guanbi"/>
              退出系统
            </span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <span v-else class="username" @click="noLoginHandel">未登录</span>
  </div>
</template>

<script setup>
import {
  UserOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import {Modal} from 'ant-design-vue';
import {createVNode, ref, onMounted, onUnmounted} from 'vue';
import {useRouter} from 'vue-router';
import {useAuthStore} from '../../../stores/auth.js';
import dbUtils from '../../../libs/util.strotage.js'
import ZyLogo from "comps/common/ZyLogo.vue";

const router = useRouter()
const authStore = useAuthStore()

// 使用响应式引用来存储用户信息
const userInfo = ref(null)

// 从localStorage获取用户信息并更新响应式引用
const updateUserInfo = () => {
  // 尝试从两个可能的位置获取token
  const tokenFromUtils = dbUtils.get('token')
  const tokenFromStorage = localStorage.getItem(`zyadmin-1.0.0-token`)
  const hasToken = tokenFromUtils || tokenFromStorage
  
  // 获取用户信息
  const storedUserInfo = dbUtils.get('userInfo')
  console.log('header-user 状态检查:', {
    tokenFromUtils: tokenFromUtils ? '存在' : '不存在',
    tokenFromStorage: tokenFromStorage ? '存在' : '不存在',
    userInfo: storedUserInfo ? '存在' : '不存在'
  })
  
  // 更新响应式引用
  if (storedUserInfo) {
    userInfo.value = storedUserInfo
    console.log('header-user 用户信息已更新:', storedUserInfo.nickname)
  } else if (hasToken) {
    // 有token但无用户信息，可能是刷新页面后localStorage中的数据丢失
    console.log('header-user 发现token但无用户信息，可能需要重新获取')
    // 在这种情况下，显示一个默认值，后续会自动获取完整信息
    userInfo.value = {
      nickname: '加载中...',
      avatar: '/assets/default-avatar.png'
    }
  } else {
    console.log('header-user 无token和用户信息，显示未登录状态')
    userInfo.value = null
  }
}

// 创建定时器检查用户信息
let checkTimer = null

onMounted(() => {
  // 初始获取用户信息
  updateUserInfo()
  
  // 定期检查用户信息是否已加载到localStorage
  checkTimer = setInterval(() => {
    updateUserInfo()
    
    // 如果已经获取到用户信息，则停止定时器
    if (userInfo.value && userInfo.value.nickname) {
      console.log('header-user 用户信息已加载，停止检查')
      clearInterval(checkTimer)
      checkTimer = null
    }
  }, 1000)
})

onUnmounted(() => {
  // 组件卸载时清除定时器
  if (checkTimer) {
    clearInterval(checkTimer)
  }
})

const logout = () => {
  Modal.confirm({
    title: '确认退出系统?',
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      authStore.logout()
    },
  });
}

const noLoginHandel = () => {
  authStore.logout()
}

</script>

<style lang="scss" scoped>
.header-user {
  cursor: pointer;
  transition: all .2s linear;
  box-sizing: border-box;
  padding: 0 8px;
  display: flex;
  justify-content: center;
  align-items: center;


  .header-user-logo {
    display: flex;
    justify-content: center;
    align-items: center;

    .header-user-name {
      margin-left: 8px;
      transition: all .2s linear;
      font-size: .9rem;

      &:hover {
        color: $color-primary;
      }
    }
  }


}
</style>
