import service from '../server'

// 获取首页数据概览
export const getHomeOverview = () => {
  return service.get('/home/<USER>')
}

// 获取首页图表数据
export const getHomeCharts = () => {
  return service.post('/home/<USER>')
}

// 获取用户统计数据
export const getUserStats = (userId = null) => {
  const params = userId ? { userId } : {};
  return service.get('/home/<USER>', { params });
}

// 获取用户综合排名数据
export const getCombinedRanking = (params) => {
  return service.post('/home/<USER>', params)
}

// 获取我的评分数据
export const getMyScore = (userId) => {
  return service.get('/home/<USER>', { userId })
} 

// 获取对应时间区间
export const getScoreTimeRange = (type) => {
  return service.get('/home/<USER>', { type })
} 