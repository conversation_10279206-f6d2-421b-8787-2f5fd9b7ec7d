/**
 *<AUTHOR>
 *@date 2023/7/3
 *@Description:权限相关的接口
 */

const express = require('express');
const router = express.Router();
const authController = require('@controllers/v1/auth/authController');
const { authMiddleware } = require('@middleware/authMiddleware');

/****************************************************************************/


/**
 * 验证码
 * @route GET /v1/sys/auth/captcha
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200
 * @returns {Error}  default - Unexpected error
 */

router.get('/captcha', authController.captcha);


/**
 * 登录
 * @route POST /v1/sys/auth/login
 * @group 权限验证 - 登录注册相关
 * @param {string} username 用户名
 * @param {string} password 密码
 * @param {string} code 密码
 * @returns {object} 200 - {"status": 1,"message": "登录成功.","data": {...},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */

router.post('/login', authController.login);

/**
 *  码云登录
 * @route POST /v1/sys/auth/giteeLogin
 * @group 权限验证 - 登录注册相关
 */
router.get('/giteeLogin', authController.giteeLogin);
/**
 *  Github登录
 * @route POST /v1/sys/auth/githubLogin
 * @group 权限验证 - 登录注册相关
 */
router.get('/githubLogin', authController.githubLogin);

/**
 * 注册
 * @route POST /v1/sys/auth/register
 * @group 权限验证 - 登录注册相关
 * @param {string} username 用户名
 * @param {string} password 密码
 * @param {string} nickname 昵称
 * @returns {object} 200 - {"status": 1,"message": "...","data": {...},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */
router.post('/register', authController.register);

/**
 * 检验验证码
 * @route GET /v1/sys/auth/verifyConfirm
 * @group 权限验证 - 登录注册相关
 * @param {string} email 邮箱
 * @param {string} code 验证码
 * @returns {object} 200 - {"status": 1,"message": "...","data": {...},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */
router.get('/verifyConfirm', authController.verifyConfirm);

/**
 * 重发验证码
 * @route GET /v1/sys/auth/resendConfirmCode
 * @group 权限验证 - 登录注册相关
 * @param {string} email 邮箱
 * @returns {object} 200 - {"status": 1,"message": "...","data": {...},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */
router.get('/resendConfirmCode', authController.resendConfirmCode);

/**
 * 登出
 * @route POST /v1/sys/auth/logout
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "登出成功","data": null,"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
router.post('/logout', authMiddleware, authController.logout);

/**
 * 获取当前用户信息
 * @route GET /v1/sys/auth/currentUser
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "获取成功","data": {用户信息},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
router.get('/currentUser', authMiddleware, authController.getCurrentUser);

/**
 * 刷新token
 * @route POST /v1/sys/auth/refreshToken
 * @group 权限验证 - 登录注册相关
 * @param {string} refreshToken.body.required - 刷新令牌
 * @returns {object} 200 - {"status": 1,"message": "刷新成功","data": {token, refreshToken},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */
router.post('/refreshToken', authController.refreshToken);

/**
 * 修改密码
 * @route POST /v1/sys/auth/changePassword
 * @group 权限验证 - 登录注册相关
 * @param {string} oldPassword.body.required - 旧密码
 * @param {string} newPassword.body.required - 新密码
 * @param {string} confirmPassword.body.required - 确认新密码
 * @returns {object} 200 - {"status": 1,"message": "密码修改成功","data": null,"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
router.post('/changePassword', authMiddleware, authController.changePassword);

/**
 * 重置密码
 * @route POST /v1/sys/auth/resetPassword
 * @group 权限验证 - 登录注册相关
 * @param {string} email.body.required - 邮箱
 * @param {string} code.body.required - 验证码
 * @param {string} newPassword.body.required - 新密码
 * @returns {object} 200 - {"status": 1,"message": "密码重置成功","data": null,"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 */
router.post('/resetPassword', authController.resetPassword);

/**
 * 获取用户权限
 * @route GET /v1/sys/auth/permissions
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "获取成功","data": {权限列表},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
router.get('/permissions', authMiddleware, authController.getUserPermissions);

/**
 * 获取用户菜单
 * @route GET /v1/sys/auth/menus
 * @group 权限验证 - 登录注册相关
 * @returns {object} 200 - {"status": 1,"message": "获取成功","data": {菜单列表},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
router.get('/menus', authMiddleware, authController.getUserMenus);

/**
 * 获取用户角色
 * @route GET /v1/auth/role
 * @group 权限验证 - 登录注册相关
 * @param {string} userId.query - 用户ID（可选，不传则获取当前用户角色）
 * @returns {object} 200 - {"status": 1,"message": "获取成功","data": {角色信息},"time": 1680598858753}
 * @returns {Error}  default - Unexpected error
 * @security JWT
 */
router.get('/role', authMiddleware, authController.getUserRole);

module.exports = router;
