import request from '../server'

/**
 * 获取权重列表
 * @returns {Promise} 响应结果
 */
export function getScoreWeights() {
  return request.get('/scoreWeights/weights')
}

/**
 * 获取所有权重及使用情况
 * @returns {Promise} 响应结果
 */
export function getAllScoreWeights() {
  return request.get('/scoreWeights/weights-with-usage')
}

/**
 * 获取权重详情
 * @param {String} id - 权重ID
 * @returns {Promise} 响应结果
 */
export function getScoreWeightDetail(id) {
  return request.get(`/scoreWeights/weight/${id}`)
}

/**
 * 创建权重
 * @param {Object} data - 权重数据
 * @returns {Promise} 响应结果
 */
export function createScoreWeight(data) {
  return request.post('/scoreWeights/weight/create', data || {})
}

/**
 * 更新权重
 * @param {String} id - 权重ID
 * @param {Object} data - 权重数据
 * @returns {Promise} 响应结果
 */
export function updateScoreWeight(id, data) {
  return request.post('/scoreWeights/weight/update', { id, ...data })
}

/**
 * 删除权重
 * @param {String} id - 权重ID
 * @returns {Promise} 响应结果
 */
export function deleteScoreWeight(id) {
  return request.post('/scoreWeights/weight/delete', { id })
}