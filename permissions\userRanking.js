/**
 * 用户排名模块权限配置
 * 包含基础排名管理和绩效分析权限
 */
module.exports = {
  // ==================== 基础排名管理权限 ====================
  
  // 排名列表查看权限
  list: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 排名详情查看权限
  detail: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 排名更新权限（仅管理员）
  update: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以更新排名，无需教师访问控制
  },
  
  // 排名刷新权限（仅管理员）
  refresh: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以刷新排名数据，无需教师访问控制
  },
  
  // 排名统计信息权限
  stats: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 统计信息对所有角色开放，不涉及个人数据
  },
  
  // 用户排名查询权限
  user: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // ==================== 绩效分析权限 ====================
  
  // 雷达图分析权限
  analysis: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 雷达图数据权限
  radar: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 分布图数据权限
  distribution: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 趋势图数据权限
  trend: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 部门对比权限
  department: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 部门对比数据对所有角色开放，不涉及个人隐私
  },
  
  // 分析报告权限
  report: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // ==================== 导出权限 ====================
  
  // 排名数据导出权限
  export: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 分析报告导出权限
  exportReport: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // ==================== 高级分析权限 ====================
  
  // 多用户对比分析权限（仅管理员）
  comparison: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 多用户对比涉及其他用户数据，仅管理员可访问
  },
  
  // 全校统计分析权限（仅管理员）
  globalStats: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 全校统计数据仅管理员可访问
  },
  
  // 历史趋势分析权限
  historicalTrend: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 预测分析权限（仅管理员）
  prediction: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 预测分析功能仅管理员可访问
  },
  
  // ==================== 数据管理权限 ====================
  
  // 排名数据备份权限（仅超级管理员）
  backup: {
    roles: ['SUPER']
    // 数据备份仅超级管理员可操作
  },
  
  // 排名数据恢复权限（仅超级管理员）
  restore: {
    roles: ['SUPER']
    // 数据恢复仅超级管理员可操作
  },
  
  // 排名算法配置权限（仅超级管理员）
  algorithm: {
    roles: ['SUPER']
    // 算法配置仅超级管理员可修改
  },
  
  // ==================== 审计权限 ====================
  
  // 排名变更日志查看权限
  auditLog: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 审计日志仅管理员可查看
  },
  
  // 异常数据检测权限
  anomalyDetection: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 异常检测仅管理员可访问
  }
};
