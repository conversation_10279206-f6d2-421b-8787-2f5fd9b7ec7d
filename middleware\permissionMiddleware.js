const { ForbiddenError } = require('../utils/errors');
const { getUserInfoFromRequest } = require('../utils/others');

/**
 * 权限检查中间件 - 基于权限标识
 * @param {Array} requiredPermissions - 需要的权限列表
 */
const checkPermissions = (requiredPermissions) => {
  return async (req, res, next) => {
    try {
      // 使用后端验证函数获取用户信息
      const userInfo = await getUserInfoFromRequest(req);
      
      if (!userInfo) {
        return next(new ForbiddenError('用户未认证'));
      }
      
      const userPermissions = userInfo.permissions || [];
      
      const hasPermission = requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      );
      
      if (!hasPermission) {
        return next(new ForbiddenError('权限不足'));
      }
      
      // 将验证过的用户信息添加到请求中
      req.verifiedUser = userInfo;
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * 角色检查中间件 - 基于角色
 * @param {Array} allowedRoles - 允许访问的角色列表
 */
const checkRoles = (allowedRoles) => {
  return async (req, res, next) => {
    try {
      // 使用后端验证函数获取用户信息
      const userInfo = await getUserInfoFromRequest(req);
      
      if (!userInfo) {
        return next(new ForbiddenError('用户未认证'));
      }
      
      const userRole = userInfo.role?.roleAuth || '';
      
      if (!allowedRoles.includes(userRole)) {
        return next(new ForbiddenError('角色权限不足'));
      }
      
      // 将验证过的用户信息添加到请求中
      req.verifiedUser = userInfo;
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * 复合权限检查中间件 - 支持角色和权限标识组合验证
 * 从后端验证用户身份和权限
 * @param {Object} options - 权限配置选项
 * @param {Array} options.permissions - 所需权限标识列表（可选）
 * @param {Array} options.roles - 允许的角色列表（可选）
 * @param {Boolean} options.requireAll - 是否需要同时满足角色和权限要求，默认为false
 */
const checkAccess = (options = {}) => {
  return async (req, res, next) => {
    try {
      const { permissions = [], roles = [], requireAll = false } = options;
      
      // 使用后端验证函数获取用户信息
      const userInfo = await getUserInfoFromRequest(req);
      
      if (!userInfo) {
        return next(new ForbiddenError('用户未认证'));
      }
      
      // 从获取的用户信息中提取权限和角色
      const userPermissions = userInfo.permissions || [];
      const userRole = userInfo.role?.roleAuth || '';
      
      // 验证权限标识
      const hasPermission = permissions.length === 0 || 
        permissions.some(permission => userPermissions.includes(permission));
      
      // 验证角色
      const hasRole = roles.length === 0 || roles.includes(userRole);
      
      // 根据requireAll决定是否需要同时满足两种条件
      const isAuthorized = requireAll 
        ? hasPermission && hasRole 
        : (permissions.length === 0 && roles.length === 0) || hasPermission || hasRole;
      
      if (!isAuthorized) {
        return next(new ForbiddenError('权限不足'));
      }
      
      // 将验证过的用户信息添加到请求中，便于后续使用
      req.verifiedUser = userInfo;
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * 检查教师角色数据权限 - 确保TEACHER-LV1只能访问自己的数据
 * @param {Object} options - 配置选项
 * @param {Array} options.requiredParams - 必须包含的用户ID参数名列表，如['userId', 'submitterId']
 * @param {Boolean} options.checkBodyOnly - 是否只检查请求体，默认false (同时检查query, params和body)
 */
const checkTeacherDataAccess = (options = {}) => {
  const { requiredParams = ['userId'], checkBodyOnly = false } = options;
  
  return async (req, res, next) => {
    try {
      // 获取验证后的用户信息
      const userInfo = await getUserInfoFromRequest(req);
      
      if (!userInfo) {
        return next(new ForbiddenError('用户未认证'));
      }
      
      // 如果不是TEACHER-LV1角色，直接通过
      if (userInfo.role?.roleAuth !== 'TEACHER-LV1') {
        req.verifiedUser = userInfo;
        return next();
      }
      
      // 如果是TEACHER-LV1角色，必须验证是否访问自己的数据
      const teacherId = userInfo.id;
      
      // 检查请求是否包含所需的用户ID参数
      let hasValidParam = false;
      
      // 确定要检查的请求对象
      const toCheck = checkBodyOnly 
        ? [req.body] 
        : [req.query, req.params, req.body];
      
      // 检查每个请求对象中是否包含有效的用户ID参数
      for (const reqObj of toCheck) {
        for (const param of requiredParams) {
          if (reqObj && reqObj[param] && reqObj[param] === teacherId) {
            hasValidParam = true;
            break;
          }
        }
        if (hasValidParam) break;
      }
      
      // 如果没有找到有效的用户ID参数，拒绝访问
      if (!hasValidParam) {
        return next(new ForbiddenError('教师角色只能访问自己的数据'));
      }
      
      // 验证通过，添加验证用户信息
      req.verifiedUser = userInfo;
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * 增强的复合权限检查 - 基于角色验证，并自动添加教师ID
 */
const checkAccessAndData = (options = {}) => {
  return async (req, res, next) => {
    try {
      
      const { roles = [], teacherAccess = { enforce: false, params: ['userId'] } } = options;
      
      // 使用后端验证函数获取用户信息
      const userInfo = await getUserInfoFromRequest(req);
      
      if (!userInfo) {
        console.log('权限检查失败: 用户未认证');
        return next(new ForbiddenError('用户未认证'));
      }
      
      // 获取用户角色
      const userRole = userInfo.role?.roleAuth || '';
      console.log(`用户角色: ${userRole}`);
      
      // 仅验证角色
      const hasRole = roles.length === 0 || roles.includes(userRole);
      
      if (!hasRole) {
        console.log('权限检查失败: 角色权限不足');
        return next(new ForbiddenError('您没有权限访问此资源'));
      }
      
      // 如果是教师角色且需要强制数据访问控制，自动添加用户ID到请求
      if (userRole === 'TEACHER-LV1' && teacherAccess.enforce) {
        const teacherId = userInfo.id;
        
        // 检查请求是否已经包含了userId参数
        let hasUserIdParam = false;
        
        // 检查请求体中是否已有userId
        if (req.body && req.body.userId) {
          hasUserIdParam = true;
        }
        
        // 检查查询参数中是否已有userId
        if (req.query && req.query.userId) {
          hasUserIdParam = true;
        }
        
        // 如果没有userId参数，则添加
        if (!hasUserIdParam) {
          // 添加到查询参数
          if (!req.query) req.query = {};
          req.query.userId = teacherId;
          
          // 添加到请求体 (对POST/PUT请求)
          if (req.body && typeof req.body === 'object') {
            req.body.userId = teacherId;
          }
          
          console.log('已自动为教师角色添加用户ID');
        } else {
        }
      }
      
      // 将验证过的用户信息添加到请求中，便于后续使用
      req.verifiedUser = userInfo;
      next();
    } catch (error) {
      console.error(`权限检查发生错误: ${error.message}`);
      next(error);
    }
  };
};

module.exports = {
  checkPermissions,
  checkRoles,
  checkAccess,
  checkTeacherDataAccess,
  checkAccessAndData
}; 