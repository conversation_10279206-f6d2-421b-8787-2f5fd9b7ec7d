const express = require('express');
const logController = require('../../../controllers/v1/sys/userOptLogController');

const router = express.Router();

/**
 * 获取日志列表
 * @route POST /v1/sys/log/list
 * @group 日志管理 - 系统日志相关接口
 * @param {number} page.body - 页码，默认1
 * @param {number} limit.body - 每页数量，默认10
 * @param {string} startTime.body - 开始时间
 * @param {string} endTime.body - 结束时间
 * @param {string} type.body - 日志类型
 * @param {string} operator.body - 操作人
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', logController.getLogs);

/**
 * 获取指定日志详情
 * @route POST /v1/sys/log/detail
 * @group 日志管理 - 系统日志相关接口
 * @param {string} id.body.required - 日志ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {日志详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/detail', logController.getLogById);

/**
 * 清空日志
 * @route POST /v1/sys/log/clear
 * @group 日志管理 - 系统日志相关接口
 * @param {string} type.body - 日志类型，不传则清空所有
 * @returns {object} 200 - {code: 200, message: "清空成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/clear', logController.clearLogs);

/**
 * 批量删除日志
 * @route POST /v1/sys/log/batch-delete
 * @group 日志管理 - 系统日志相关接口
 * @param {array} ids.body.required - 日志ID数组
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/batch-delete', logController.batchDeleteLogs);

/**
 * 获取日志统计信息
 * @route POST /v1/sys/log/stats
 * @group 日志管理 - 系统日志相关接口
 * @param {string} startTime.body - 开始时间
 * @param {string} endTime.body - 结束时间
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {stats: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats', logController.getLogStats);

module.exports = router; 