const Award = require('../../../models/v1/mapping/awardsModel');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');
const { getUserInfoFromRequest } = require('../../../utils/others');


/**
 * 获取奖项列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwards = async (req, res) => {
  try {
    const { name, type, level, teacherName, awardDate, status, page = 1, pageSize = 10, userOnly, userId, ifReviewer } = req.query;
    
    console.log('获取奖项列表，参数:', req.query);
    
    // 构建查询条件
    const where = {};
    if (name) where.name = { [Op.like]: `%${name}%` };
    if (type) where.type = type;
    if (level) where.level = level;
    if (teacherName) where.teachers = { [Op.like]: `%${teacherName}%` };
    if (awardDate) {
      const year = new Date(awardDate).getFullYear();
      where.awardDate = { [Op.like]: `${year}%` };
    }
    if (status !== undefined) where.status = status;
    
    // 添加审核状态筛选条件
    if (ifReviewer !== undefined) {
      // 转换字符串'true'/'false'为布尔值
      const reviewerValue = ifReviewer === 'true' || ifReviewer === true;
      where.ifReviewer = reviewerValue;
      console.log(`筛选审核状态: ${reviewerValue}`);
    }
    
    // 如果请求个人数据，添加用户筛选条件
    if (userOnly === 'true' && userId) {
      where.teachers = { [Op.like]: `%${userId}%` };
    }
    
    console.log('查询条件:', where);
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await Award.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['createdAt', 'DESC']],
      include: [
        {
          association: 'user',
          attributes: ['id', 'nickname', 'username']
        }
      ]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取奖项列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取奖项列表失败',
      data: null
    });
  }
};

/**
 * 获取奖项详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const award = await Award.findByPk(id, {
      include: [
        {
          association: 'user',
          attributes: ['id', 'nickname', 'username']
        }
      ]
    });
    if (!award) {
      return res.status(404).json({
        code: 404,
        message: '奖项不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: award
    });
  } catch (error) {
    console.error('获取奖项详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取奖项详情失败',
      data: null
    });
  }
};

/**
 * 创建奖项
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAward = async (req, res) => {
  try {
    const { name, type, level, teachers, awardDate, awardUnit, awardRank, score, status, remark } = req.body;
    
    // 校验必填参数
    if (!name || !type || !level || !teachers || !awardDate) {
      return res.status(400).json({
        code: 400,
        message: '奖项名称、类型、级别、获奖教师和获奖时间不能为空',
        data: null
      });
    }
    
    // 创建奖项
    const award = await Award.create({
      id: uuidv4(),
      name,
      type,
      level,
      teachers: typeof teachers === 'string' ? teachers : JSON.stringify(teachers),
      awardDate,
      awardUnit: awardUnit || '',
      awardRank: awardRank || 0,
      score: score || 0,
      status: status !== undefined ? status : 1,
      remark: remark || ''
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: award
    });
  } catch (error) {
    console.error('创建奖项失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建奖项失败',
      data: null
    });
  }
};

/**
 * 更新奖项
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateAward = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type, level, teachers, awardDate, awardUnit, awardRank, score, status, remark } = req.body;
    
    // 校验必填参数
    if (!name || !type || !level || !teachers || !awardDate) {
      return res.status(400).json({
        code: 400,
        message: '奖项名称、类型、级别、获奖教师和获奖时间不能为空',
        data: null
      });
    }
    
    // 查找奖项
    const award = await Award.findByPk(id);
    if (!award) {
      return res.status(404).json({
        code: 404,
        message: '奖项不存在',
        data: null
      });
    }
    
    // 更新奖项
    await award.update({
      name,
      type,
      level,
      teachers: typeof teachers === 'string' ? teachers : JSON.stringify(teachers),
      awardDate,
      awardUnit: awardUnit || '',
      awardRank: awardRank || 0,
      score: score || 0,
      status: status !== undefined ? status : award.status,
      remark: remark || ''
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: award
    });
  } catch (error) {
    console.error('更新奖项失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新奖项失败',
      data: null
    });
  }
};

/**
 * 删除奖项
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAward = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找奖项
    const award = await Award.findByPk(id);
    if (!award) {
      return res.status(404).json({
        code: 404,
        message: '奖项不存在',
        data: null
      });
    }
    
    // 删除奖项
    await award.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除奖项失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除奖项失败',
      data: null
    });
  }
};

/**
 * 导入奖项数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importAwards = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请上传文件',
        data: null
      });
    }
    
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = xlsx.utils.sheet_to_json(worksheet);
    
    if (data.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '文件内容为空',
        data: null
      });
    }
    
    // 验证并处理数据
    const awards = [];
    const errors = [];
    const requiredFields = ['奖项名称', '奖项类型', '奖项级别', '获奖教师', '获奖时间'];
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNum = i + 2; // Excel 行号从1开始，且有表头
      
      // 检查必填字段
      const missingFields = requiredFields.filter(field => !row[field] && row[field] !== 0);
      if (missingFields.length > 0) {
        errors.push(`第${rowNum}行: ${missingFields.join(', ')}不能为空`);
        continue;
      }
      
      awards.push({
        id: uuidv4(),
        name: row['奖项名称'],
        type: row['奖项类型'],
        level: row['奖项级别'],
        teachers: row['获奖教师'],
        awardDate: row['获奖时间'],
        awardUnit: row['获奖单位'] || '',
        awardRank: row['获奖排名'] || 0,
        score: row['得分'] || 0,
        status: row['状态'] !== undefined ? row['状态'] : 1,
        remark: row['备注'] || ''
      });
    }
    
    // 批量创建奖项
    if (awards.length > 0) {
      await Award.bulkCreate(awards);
    }
    
    // 删除临时文件
    fs.unlinkSync(filePath);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: {
        total: data.length,
        success: awards.length,
        failed: errors.length,
        errors
      }
    });
  } catch (error) {
    console.error('导入奖项失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导入奖项失败',
      data: null
    });
  }
};

/**
 * 导出奖项数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportAwards = async (req, res) => {
  try {
    const { name, type, level, teacherName, awardDate, status } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) where.name = { [Op.like]: `%${name}%` };
    if (type) where.type = type;
    if (level) where.level = level;
    if (teacherName) where.teachers = { [Op.like]: `%${teacherName}%` };
    if (awardDate) {
      const year = new Date(awardDate).getFullYear();
      where.awardDate = { [Op.like]: `${year}%` };
    }
    if (status !== undefined) where.status = status;
    
    // 查询数据
    const awards = await Award.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });
    
    // 转换为Excel格式
    const excelData = awards.map(award => ({
      '奖项名称': award.name,
      '奖项类型': award.type,
      '奖项级别': award.level,
      '获奖教师': award.teachers,
      '获奖时间': award.awardDate,
      '获奖单位': award.awardUnit,
      '获奖排名': award.awardRank,
      '得分': award.score,
      '状态': award.status,
      '备注': award.remark,
      '创建时间': award.createdAt.toLocaleString(),
      '更新时间': award.updatedAt.toLocaleString()
    }));
    
    // 创建工作簿和工作表
    const worksheet = xlsx.utils.json_to_sheet(excelData);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, '奖项数据');
    
    // 生成Excel文件
    const fileName = `awards_${Date.now()}.xlsx`;
    const filePath = path.join(__dirname, '..', '..', '..', 'uploads', fileName);
    
    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 写入文件
    xlsx.writeFile(workbook, filePath);
    
    // 发送文件
    return res.download(filePath, fileName, (err) => {
      if (err) {
        console.error('文件下载失败:', err);
        return res.status(500).json({
          code: 500,
          message: '文件下载失败',
          data: null
        });
      }
      
      // 下载完成后删除临时文件
      fs.unlinkSync(filePath);
    });
  } catch (error) {
    console.error('导出奖项失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出奖项失败',
      data: null
    });
  }
};

/**
 * 获取个人奖项统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPersonalAwardStats = async (req, res) => {
  try {
    const { userId } = req.query;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '用户ID不能为空',
        data: null
      });
    }
    
    // 查询用户所有获奖记录
    const awards = await Award.findAll({
      where: {
        teachers: { [Op.like]: `%${userId}%` }
      },
      order: [['awardDate', 'DESC']]
    });
    
    // 计算总分
    const totalScore = awards.reduce((sum, award) => sum + parseFloat(award.score || 0), 0);
    
    // 获奖类型分布
    const typeDistribution = {};
    awards.forEach(award => {
      if (!typeDistribution[award.type]) {
        typeDistribution[award.type] = 0;
      }
      typeDistribution[award.type]++;
    });
    
    // 获奖级别分布
    const levelDistribution = {};
    awards.forEach(award => {
      if (!levelDistribution[award.level]) {
        levelDistribution[award.level] = 0;
      }
      levelDistribution[award.level]++;
    });
    
    // 获奖时间分布
    const timeDistribution = {};
    const currentYear = new Date().getFullYear();
    // 初始化最近5年的数据
    for (let i = 0; i < 5; i++) {
      timeDistribution[currentYear - i] = 0;
    }
    
    awards.forEach(award => {
      if (award.awardDate) {
        const year = new Date(award.awardDate).getFullYear();
        if (timeDistribution[year] !== undefined) {
          timeDistribution[year]++;
        }
      }
    });
    
    // 计算年度分数趋势
    const yearlyScores = {};
    // 初始化最近5年的数据
    for (let i = 0; i < 5; i++) {
      yearlyScores[currentYear - i] = {
        total: 0,
        count: 0
      };
    }
    
    awards.forEach(award => {
      if (award.awardDate && award.score) {
        const year = new Date(award.awardDate).getFullYear();
        if (yearlyScores[year]) {
          yearlyScores[year].total += parseFloat(award.score || 0);
          yearlyScores[year].count++;
        }
      }
    });
    
    // 转换为平均分
    const scoreTrend = {};
    Object.keys(yearlyScores).forEach(year => {
      scoreTrend[year] = yearlyScores[year].count > 0
        ? (yearlyScores[year].total / yearlyScores[year].count).toFixed(2)
        : 0;
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        awardCount: awards.length,
        totalScore,
        typeDistribution,
        levelDistribution,
        timeDistribution,
        scoreTrend,
        recentAwards: awards.slice(0, 5) // 最近5条获奖记录
      }
    });
  } catch (error) {
    console.error('获取个人奖项统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取个人奖项统计失败',
      data: null
    });
  }
};

/**
 * 获取所有用户获奖得分排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllUsersAwardScore = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, nickname } = req.body;
    
    console.log('获取所有用户获奖得分排名，参数:', { page, pageSize, nickname });
    
    // 获取所有获奖记录
    const awards = await Award.findAll({
      attributes: ['id', 'teachers', 'score', 'type', 'level', 'awardDate'],
      where: { status: 1 },
      include: [
        {
          association: 'user',
          attributes: ['id', 'nickname', 'username']
        }
      ]
    });
    
    console.log(`查询到 ${awards.length} 条获奖记录`);
    
    // 用户得分统计
    const userScores = {};
    
    // 统计每个用户的得分
    awards.forEach(award => {
      try {
        const score = parseFloat(award.score || 0);
        if (isNaN(score)) return;
        
        // 优先使用user关联的nickname
        if (award.user && award.user.nickname) {
          const teacherId = award.user.id;
          const nickname = award.user.nickname;
          
          if (!userScores[teacherId]) {
            userScores[teacherId] = {
              userId: teacherId,
              nickname: nickname,
              totalScore: 0,
              awardCount: 0,
              awards: []
            };
          }
          
          userScores[teacherId].totalScore += score;
          userScores[teacherId].awardCount += 1;
          
          // 添加获奖详情
          userScores[teacherId].awards.push({
            id: award.id,
            type: award.type,
            level: award.level,
            score: score,
            date: award.awardDate
          });
          
          return; // 已经处理完成，跳过下面的逻辑
        }
        
        // 如果没有user关联，则使用teachers字段
        let teachers = [];
        if (typeof award.teachers === 'string') {
          try {
            // 尝试解析JSON
            teachers = JSON.parse(award.teachers);
            if (!Array.isArray(teachers)) {
              teachers = award.teachers.split(',').map(t => t.trim());
            }
          } catch (e) {
            // 不是JSON，可能是逗号分隔的字符串
            teachers = award.teachers.split(',').map(t => t.trim());
          }
        } else if (Array.isArray(award.teachers)) {
          teachers = award.teachers;
        }
        
        if (!Array.isArray(teachers) || teachers.length === 0) {
          console.warn(`获奖记录 ${award.id} 没有有效的教师列表:`, award.teachers);
          return;
        }
        
        // 平均分配得分给所有教师
        const scorePerTeacher = score / teachers.length;
        
        teachers.forEach(teacher => {
          if (!teacher) return;
          
          const teacherId = typeof teacher === 'object' ? (teacher.id || teacher.value || JSON.stringify(teacher)) : teacher;
          
          if (!userScores[teacherId]) {
            userScores[teacherId] = {
              userId: teacherId,
              nickname: typeof teacher === 'object' ? (teacher.name || teacher.nickname || teacherId) : teacherId,
              totalScore: 0,
              awardCount: 0,
              awards: []
            };
          }
          
          userScores[teacherId].totalScore += scorePerTeacher;
          userScores[teacherId].awardCount += 1;
          
          // 添加获奖详情
          userScores[teacherId].awards.push({
            id: award.id,
            type: award.type,
            level: award.level,
            score: scorePerTeacher,
            date: award.awardDate
          });
        });
      } catch (err) {
        console.error('处理获奖数据时出错:', err, '获奖ID:', award.id);
      }
    });
    
    // 将统计结果转换为数组
    let userScoreList = Object.values(userScores);
    
    // 如果有指定昵称，进行过滤
    if (nickname) {
      userScoreList = userScoreList.filter(user => 
        (user.nickname && user.nickname.toLowerCase().includes(nickname.toLowerCase())) ||
        user.userId.toLowerCase().includes(nickname.toLowerCase())
      );
    }
    
    // 按总分降序排序
    userScoreList.sort((a, b) => b.totalScore - a.totalScore);
    
    // 添加排名
    userScoreList.forEach((user, index) => {
      user.rank = index + 1;
      user.totalScore = parseFloat(user.totalScore.toFixed(2));
    });
    
    // 计算总数和总页数
    const totalCount = userScoreList.length;
    const totalPages = Math.ceil(totalCount / parseInt(pageSize));
    
    // 分页
    const startIndex = (parseInt(page) - 1) * parseInt(pageSize);
    const endIndex = startIndex + parseInt(pageSize);
    const paginatedList = userScoreList.slice(startIndex, endIndex);
    
    console.log(`总共 ${totalCount} 位用户，当前页 ${page}/${totalPages}`);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: paginatedList,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: totalCount,
          totalPages
        }
      }
    });
  } catch (error) {
    console.error('获取所有用户获奖得分排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有用户获奖得分排名失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 审核奖项
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewAward = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log(`审核奖项，ID: ${id}`);
    
    // 参数验证
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少奖项ID参数',
        data: null
      });
    }
    
    // 获取用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 权限检查：只有管理员或项目负责人可以编辑项目
    if (userInfo.role.roleAuth !== 'ADMIN-LV2' && userInfo.role.roleAuth !== 'SUPER') {
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核奖项',
        data: null
      });
    }

    // 查找奖项
    const award = await Award.findByPk(id);
    if (!award) {
      return res.status(404).json({
        code: 404,
        message: '奖项不存在',
        data: null
      });
    }
    
    // 如果已经审核过，返回提示
    if (award.ifReviewer) {
      return res.status(400).json({
        code: 400,
        message: `奖项已审核`,
        data: null
      });
    }
    
    // 更新奖项审核状态
    await award.update({
      ifReviewer: true,
      reviewer: userInfo.id // 保存审核人ID
    });
    
    console.log(`奖项 ${id} 审核成功，审核人ID: ${userInfo.id}, 审核人昵称: ${userInfo.nickname || userInfo.username}`);
    
    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: null
    });
  } catch (error) {
    console.error('审核奖项失败:', error);
    return res.status(500).json({
      code: 500,
      message: '审核奖项失败: ' + error.message,
      data: null
    });
  }
}; 