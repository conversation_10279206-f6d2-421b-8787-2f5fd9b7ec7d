// jn-jxpd-server/models/v1/mapping/highLevelPaperParticipantsModel.js
const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const userModel = require('./userModel');
const highLevelPapersModel = require('./highLevelPapersModel');

// 定义高水平论文参与人员模型
const HighLevelPaperParticipant = sequelize.define('high_level_paper_participants', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    defaultValue: DataTypes.UUIDV4,
    comment: '记录ID'
  },
  paperId: {
    type: DataTypes.CHAR(36),
    allowNull: false,
    comment: '论文ID'
  },
  userId: {
    type: DataTypes.CHAR(36),
    allowNull: false,
    comment: '用户ID'
  },
  allocationRatio: {
    type: DataTypes.DECIMAL(4, 2),
    allowNull: false,
    comment: '分配比例（0.00-1.00）'
  },
  authorRank: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '作者排名'
  },
  isFirstAuthor: {
    type: DataTypes.TINYINT(1),
    allowNull: false,
    defaultValue: 0,
    comment: '是否第一作者'
  },
  isCorrespondingAuthor: {
    type: DataTypes.TINYINT(1),
    allowNull: false,
    defaultValue: 0,
    comment: '是否通讯作者'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'high_level_paper_participants',
  timestamps: true,
  indexes: [
    {
      unique: true,
      name: 'uk_participant',
      fields: ['paperId', 'userId']
    },
    {
      name: 'idx_paper_participant_paper',
      fields: ['paperId']
    },
    {
      name: 'idx_paper_participant_user',
      fields: ['userId']
    },
    {
      name: 'idx_paper_participant_first_author',
      fields: ['isFirstAuthor']
    },
    {
      name: 'idx_paper_participant_corresponding',
      fields: ['isCorrespondingAuthor']
    }
  ]
});

// 添加关联关系
HighLevelPaperParticipant.belongsTo(highLevelPapersModel, {
  foreignKey: 'paperId',
  as: 'paper'
});

HighLevelPaperParticipant.belongsTo(userModel, {
  foreignKey: 'userId',
  as: 'user'
});

// 反向关联
highLevelPapersModel.hasMany(HighLevelPaperParticipant, {
  foreignKey: 'paperId',
  as: 'participants'
});

module.exports = HighLevelPaperParticipant;