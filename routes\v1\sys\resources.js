// routes/userRoutes.js
const express = require('express');
const resourcesController = require('../../../controllers/v1/system/resourcesController');

const router = express.Router();

/**
 * 获取所有资源列表
 * @route POST /v1/sys/resources/list
 * @group 资源管理 - 系统资源相关接口
 * @param {number} page.body - 页码，默认1
 * @param {number} limit.body - 每页数量，默认10
 * @param {string} sort.body - 排序字段
 * @param {string} order.body - 排序方式（asc/desc）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', resourcesController.getAll);

/**
 * 创建新资源
 * @route POST /v1/sys/resources/create
 * @group 资源管理 - 系统资源相关接口
 * @param {string} name.body.required - 资源名称
 * @param {string} code.body.required - 资源编码
 * @param {string} type.body.required - 资源类型
 * @param {string} path.body.required - 资源路径
 * @param {string} description.body - 资源描述
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "资源ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', resourcesController.create);

/**
 * 更新资源信息
 * @route POST /v1/sys/resources/update
 * @group 资源管理 - 系统资源相关接口
 * @param {string} id.body.required - 资源ID
 * @param {string} name.body - 资源名称
 * @param {string} code.body - 资源编码
 * @param {string} type.body - 资源类型
 * @param {string} path.body - 资源路径
 * @param {string} description.body - 资源描述
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update', resourcesController.update);

/**
 * 删除资源
 * @route POST /v1/sys/resources/delete
 * @group 资源管理 - 系统资源相关接口
 * @param {string} id.body.required - 资源ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', resourcesController.delete);

module.exports = router;
