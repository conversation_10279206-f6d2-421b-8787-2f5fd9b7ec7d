const { Op } = require('sequelize');
const studentProjectGuidanceProjectLevelModel = require('../../../models/v1/mapping/studentProjectGuidanceProjectLevelsModel');
const studentProjectGuidanceProjectModel = require('../../../models/v1/mapping/studentProjectGuidanceProjectsModel');
const studentProjectGuidanceParticipantModel = require('../../../models/v1/mapping/studentProjectGuidanceParticipantsModel');
const { getTimeIntervalByName } = require('../../../utils/others');

/**
 * 获取立项级别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectLevels = async (req, res) => {
  try {
    const { levelName, page = 1, pageSize = 10 } = req.body;
    
    // 构建查询条件
    const where = {};
    if (levelName) {
      where.levelName = { [Op.like]: `%${levelName}%` };
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 查询数据
    const { count, rows } = await studentProjectGuidanceProjectLevelModel.findAndCountAll({
      where,
      offset,
      limit,
      order: [['score', 'DESC']], // 按分数降序排列
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取立项级别列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取立项级别列表失败',
      error: error.message
    });
  }
};

/**
 * 获取所有立项级别（不分页）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllProjectLevels = async (req, res) => {
  try {
    // 查询所有立项级别数据
    const projectLevels = await studentProjectGuidanceProjectLevelModel.findAll({
      order: [['score', 'DESC']], // 按分数降序排列
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: projectLevels
    });
  } catch (error) {
    console.error('获取所有立项级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有立项级别失败',
      error: error.message
    });
  }
};

/**
 * 创建立项级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createProjectLevel = async (req, res) => {
  try {
    const { levelName, score } = req.body;
    
    // 验证必要字段
    if (!levelName || score === undefined) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要字段',
        data: null
      });
    }
    
    // 创建立项级别
    const projectLevel = await studentProjectGuidanceProjectLevelModel.create({
      levelName,
      score
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: projectLevel
    });
  } catch (error) {
    console.error('创建立项级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建立项级别失败',
      error: error.message
    });
  }
};

/**
 * 更新立项级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateProjectLevel = async (req, res) => {
  try {
    const { id } = req.params;
    const { levelName, score } = req.body;
    
    // 验证ID
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少立项级别ID',
        data: null
      });
    }
    
    // 查询立项级别是否存在
    const projectLevel = await studentProjectGuidanceProjectLevelModel.findByPk(id);
    
    if (!projectLevel) {
      return res.status(404).json({
        code: 404,
        message: '未找到立项级别',
        data: null
      });
    }
    
    // 更新立项级别
    const updateData = {};
    if (levelName !== undefined) updateData.levelName = levelName;
    if (score !== undefined) updateData.score = score;
    
    await projectLevel.update(updateData);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: projectLevel
    });
  } catch (error) {
    console.error('更新立项级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新立项级别失败',
      error: error.message
    });
  }
};

/**
 * 删除立项级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteProjectLevel = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 验证ID
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少立项级别ID',
        data: null
      });
    }
    
    // 查询立项级别是否存在
    const projectLevel = await studentProjectGuidanceProjectLevelModel.findByPk(id);
    
    if (!projectLevel) {
      return res.status(404).json({
        code: 404,
        message: '未找到立项级别',
        data: null
      });
    }
    
    // 删除立项级别
    await projectLevel.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除立项级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除立项级别失败',
      error: error.message
    });
  }
};

/**
 * 获取所有级别及其立项数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelsWithCount = async (req, res) => {
  try {
    // 获取所有级别
    const levels = await studentProjectGuidanceProjectLevelModel.findAll({
      order: [['score', 'DESC']] // 按分数降序排列
    });

    // 为每个级别查询相关的立项记录数量
    const levelsWithCount = await Promise.all(levels.map(async (level) => {
      const count = await studentProjectGuidanceProjectModel.count({
        where: { levelId: level.id }
      });

      const levelData = level.toJSON();
      levelData.projectCount = count;
      return levelData;
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levelsWithCount
    });
  } catch (error) {
    console.error('获取所有级别及其立项数量失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有级别及其立项数量失败',
      error: error.message
    });
  }
};

/**
 * 获取立项级别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少立项级别ID',
        data: null
      });
    }
    
    // 查询立项级别详情
    const level = await studentProjectGuidanceProjectLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到立项级别',
        data: null
      });
    }
    
    // 获取使用该级别的立项记录数量
    const projectCount = await studentProjectGuidanceProjectModel.count({
      where: { levelId: id }
    });
    
    // 组装返回数据
    const levelData = level.toJSON();
    levelData.projectCount = projectCount;
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levelData
    });
  } catch (error) {
    console.error('获取立项级别详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取立项级别详情失败',
      error: error.message
    });
  }
};

/**
 * 创建级别 - 路由别名方法
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createLevel = async (req, res) => {
  return this.createProjectLevel(req, res);
};

/**
 * 更新级别 - 路由别名方法
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateLevel = async (req, res) => {
  return this.updateProjectLevel(req, res);
};

/**
 * 删除级别 - 路由别名方法
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteLevel = async (req, res) => {
  return this.deleteProjectLevel(req, res);
};

/**
 * 获取立项级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
  try {
    const { range = 'all', userId } = req.body;
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("studentProjectGuidance");
    
    // 获取所有级别
    const levels = await studentProjectGuidanceProjectLevelModel.findAll({
      order: [['score', 'DESC']] // 按分数降序排列
    });
    
    // 构建查询条件（如果指定了用户ID，则通过参与者表过滤）
    const levelCounts = await Promise.all(levels.map(async (level) => {
      // 构建基本查询条件
      const whereCondition = { 
        levelId: level.id,
        ifReviewer: 1 // 只统计已审核的记录
      };
      
      // 查询符合条件的记录数量
      let count;
      
      if (userId) {
        // 如果指定了userId，则需要查询用户参与的立项记录
        const projects = await studentProjectGuidanceProjectModel.findAll({
          where: whereCondition,
          include: [
            {
              model: studentProjectGuidanceParticipantModel,
              as: 'participants',
              where: { userId },
              required: true
            }
          ]
        });
        
        // 根据时间范围过滤
        if (range !== 'all' && timeInterval) {
          const startTime = new Date(timeInterval.startTime);
          const endTime = new Date(timeInterval.endTime);
          
          const filteredProjects = projects.filter(project => {
            const approvalDate = new Date(project.approvalDate);
            const isInRange = approvalDate >= startTime && approvalDate <= endTime;
            return range === 'in' ? isInRange : !isInRange;
          });
          
          count = filteredProjects.length;
        } else {
          count = projects.length;
        }
      } else {
        // 如果未指定userId，则直接查询所有记录
        
        // 根据时间范围构建查询条件
        if (range !== 'all' && timeInterval) {
          if (range === 'in') {
            whereCondition.approvalDate = {
              [Op.between]: [timeInterval.startTime, timeInterval.endTime]
            };
          } else if (range === 'out') {
            whereCondition.approvalDate = {
              [Op.or]: [
                { [Op.lt]: timeInterval.startTime },
                { [Op.gt]: timeInterval.endTime }
              ]
            };
          }
        }
        
        count = await studentProjectGuidanceProjectModel.count({
          where: whereCondition
        });
      }
      
      return {
        name: level.levelName,
        value: count
      };
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levelCounts
    });
  } catch (error) {
    console.error('获取立项级别分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取立项级别分布数据失败',
      error: error.message
    });
  }
};