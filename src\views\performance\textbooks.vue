<template>
  <div class="performance-container textbooks-container">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable style="margin-bottom: 16px;" />

    <!-- 统计概览 -->
    <a-card title="教材与著作管理" :bordered="false" class="performance-card" style="margin-bottom: 20px">
      <template #extra>
        <a-space>
          <a-upload
            name="file"
            :showUploadList="false"
            :beforeUpload="beforeExcelUpload"
            :customRequest="handleExcelToJsonConvert"
          >
            <a-button type="primary" v-permission="'score:textbooks:admin:update'">
              <template #icon><FileExcelOutlined /></template>
              Excel数据导入
            </a-button>
          </a-upload>
          <!-- <a-upload
            name="file"
            :showUploadList="false"
            :beforeUpload="beforeImportUpload"
            :customRequest="handleImport"
          >
            <a-button type="primary">
              <template #icon><UploadOutlined /></template>
              导入数据
            </a-button>
          </a-upload> -->
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            添加教材与著作
          </a-button>
          <a-button :type="showPersonalTextbooks ? 'default' : 'primary'" @click="togglePersonalTextbooks" v-permission="'score:textbooks:admin'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalTextbooks ? '查看全部教材与著作' : '查看我的教材与著作' }}
          </a-button>
        </a-space>
      </template>
      <!-- <a-spin :spinning="statisticsLoading">
        <a-row :gutter="24">
          <a-col :span="6">
            <div style="text-align: center">
              <p style="font-size: 14px; color: rgba(0, 0, 0, 0.45); margin-bottom: 8px">教材与著作总数</p>
              <p style="font-size: 24px; margin: 0">{{ statisticsData.totalTextbooks }}</p>
            </div>
          </a-col>
          <a-col :span="6">
            <div style="text-align: center">
              <p style="font-size: 14px; color: rgba(0, 0, 0, 0.45); margin-bottom: 8px">统计范围内数量</p>
              <p style="font-size: 24px; margin: 0">{{ statisticsData.inRangeTextbooks }}</p>
            </div>
          </a-col>
          <a-col :span="6">
            <div style="text-align: center">
              <p style="font-size: 14px; color: rgba(0, 0, 0, 0.45); margin-bottom: 8px">平均分数</p>
              <p style="font-size: 24px; margin: 0">{{ Number(statisticsData.averageScore || 0).toFixed(2) }}</p>
            </div>
          </a-col>
          <a-col :span="6">
            <div style="text-align: center">
              <p style="font-size: 14px; color: rgba(0, 0, 0, 0.45); margin-bottom: 8px">审核完成率</p>
              <p style="font-size: 24px; margin: 0">{{ Number(statisticsData.reviewCompletionRate || 0).toFixed(2) }}%</p>
            </div>
          </a-col>
        </a-row>
      </a-spin> -->
    </a-card>

    <!-- 添加教材与著作说明区域 -->
    <a-card title="教材与著作填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
      <a-alert
        class="mb-16"
        message="教材与著作统计时间范围"
        :description="`统计时间：${timeRangeText || '加载中...'}`"
        type="info"
        show-icon
      />
      <div class="rule-content">
        <p><strong>填写说明：</strong></p>
        <ol class="detail-list">
          <li>出版时间范围在统计时间内</li>
          <li>审核需要提供教材和专著的封面、扉页等证明材料</li>
          <li>丛书主编不重复计分</li>
          <li>学术著作与本人岗位所从事的专业对口的本专业著作才能计分</li>
          <li>"类别及任职"已内置下拉菜单，选择即可</li>
        </ol>
      </div>
    </a-card>

    <!-- 图表区域 -->
    <a-row :gutter="16" style="margin-bottom: 24px">
      <!-- 审核状态概览 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card :bordered="false" title="审核状态概览" size="small" class="performance-card chart-container">
          <template #extra>
            <a-select
              v-model:value="reviewStatusChartRange"
              size="small"
              style="width: 100px;"
              @change="(value) => changeChartRange('reviewStatus', value)"
            >
              <a-select-option value="in">范围内</a-select-option>
              <a-select-option value="out">范围外</a-select-option>
              <a-select-option value="all">全部</a-select-option>
            </a-select>
          </template>
          <div ref="reviewStatusChartRef" class="chart-wrapper"></div>
        </a-card>
      </a-col>

      <!-- 教材类别分布 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card :bordered="false" title="教材类别分布" size="small" class="performance-card chart-container">
          <template #extra>
            <a-select
              v-model:value="categoryChartRange"
              size="small"
              style="width: 100px;"
              @change="(value) => changeChartRange('category', value)"
            >
              <a-select-option value="in">范围内</a-select-option>
              <a-select-option value="out">范围外</a-select-option>
              <a-select-option value="all">全部</a-select-option>
            </a-select>
          </template>
          <div ref="categoryChartRef" class="chart-wrapper"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" style="margin-bottom: 10px">
      <!-- 教材数量统计 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card :bordered="false" title="教材与著作数量统计" size="small" class="performance-card chart-container">
          <template #extra>
            <a-select
              v-model:value="countChartRange"
              size="small"
              style="width: 100px;"
              @change="(value) => changeChartRange('count', value)"
            >
              <a-select-option value="in">范围内</a-select-option>
              <a-select-option value="out">范围外</a-select-option>
              <a-select-option value="all">全部</a-select-option>
            </a-select>
          </template>
          <div ref="countChartRef" class="chart-wrapper"></div>
        </a-card>
      </a-col>

      <!-- 出版日期分布 -->
      <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <a-card :bordered="false" title="出版日期分布" size="small" class="performance-card chart-container">
          <template #extra>
            <a-select
              v-model:value="dateChartRange"
              size="small"
              style="width: 100px;"
              @change="(value) => changeChartRange('date', value)"
            >
              <a-select-option value="in">范围内</a-select-option>
              <a-select-option value="out">范围外</a-select-option>
              <a-select-option value="all">全部</a-select-option>
            </a-select>
          </template>
          <div ref="dateChartRef" class="chart-wrapper"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 在统计图表区域后添加教师排行表组件 -->
    <a-row :gutter="24" style="margin-bottom: 20px">
      <a-col :span="24">
        <a-card title="教师教材数量排行" :bordered="false" style="margin-top: 24px;">
          <template #extra>
            <a-space>
              <a-input-search
                v-model:value="authorRankingPagination.nickname"
                v-permission="'score:textbooks:admin:list'"
                placeholder="用户名称"
                style="width: 150px;"
                @search="fetchAuthorRanking"
                @pressEnter="fetchAuthorRanking"
              />
              <a-select
                v-model:value="authorRankingRange"
                style="width: 150px;"
                @change="handleAuthorRankingRangeChange"
              >
                <a-select-option value="in">统计范围内</a-select-option>
                <a-select-option value="out">统计范围外</a-select-option>
                <a-select-option value="all">全部数据</a-select-option>
              </a-select>
              <a-button type="primary" @click="exportAuthorRanking" :loading="authorRankingExportLoading" v-permission="'score:textbooks:admin:list'">
                <template #icon><DownloadOutlined /></template>
                导出
              </a-button>
            </a-space>
          </template>
          <a-table
            :columns="authorRankColumns"
            :data-source="authorRankingData"
            :pagination="currentRole && (currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2') ? authorRankingPagination : false"
            :loading="authorRankLoading"
            rowKey="userId"
            :bordered="true"
            @change="handleAuthorRankingTableChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'rank'">
                <a-tag :color="record.rank <= 3 ? ['#f50', '#fa8c16', '#faad14'][record.rank - 1] : ''">
                  {{ record.rank }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'totalScore'">
                <span style="font-weight: bold; color: #1890ff;">{{ Number(record.totalScore || 0).toFixed(2) }}分</span>
              </template>
              <template v-else-if="column.key === 'details'">
                <a-button type="link" size="small" @click="showAuthorTextbooksDetails(record)"
                v-if="currentRole && (currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId)"
                >
                  查看详情
                </a-button>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索表单 -->
    <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
      <a-form :model="queryParams" @finish="handleQuery" layout="vertical" class="performance-form">
        <a-row :gutter="[12, 8]">
          <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
            <a-form-item label="教材名称" name="materialName">
              <a-input
                v-model:value="queryParams.materialName"
                placeholder="请输入教材名称"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
            <a-form-item label="教材类别" name="categoryId">
              <a-select
                v-model:value="queryParams.categoryId"
                placeholder="请选择教材类别"
                style="width: 100%"
                allow-clear
              >
                <a-select-option v-for="category in categoryOptions" :key="category.id" :value="category.id">
                  {{ category.categoryAndPosition }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
            <a-form-item label="出版日期" name="dateRange">
              <a-range-picker
                v-model:value="dateRange"
                :format="'YYYY-MM-DD'"
                style="width: 100%"
                :placeholder="['开始日期', '结束日期']"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
            <a-form-item label="审核状态" name="reviewStatus">
              <a-select
                v-model:value="queryParams.reviewStatus"
                style="width: 100%"
                allow-clear
              >
                <a-select-option value="all">全部状态</a-select-option>
                <a-select-option value="reviewed">已审核</a-select-option>
                <a-select-option value="rejected">已拒绝</a-select-option>
                <a-select-option value="pending">待审核</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
            <a-form-item label="统计范围" name="range">
              <a-select
                v-model:value="queryParams.range"
                style="width: 100%"
                allow-clear
              >
                <a-select-option value="in">统计范围内</a-select-option>
                <a-select-option value="out">统计范围外</a-select-option>
                <a-select-option value="all">全部教材</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
            <a-form-item label=" " style="margin-bottom: 0;">
              <div class="search-actions-inline">
                <a-button type="primary" html-type="submit" size="default">
                  <template #icon><SearchOutlined /></template>
                  搜索
                </a-button>
                <a-button @click="resetQuery" size="default">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
                <a-button type="default" @click="handleExportFiltered" :loading="exportLoading" size="default">
                  <template #icon><DownloadOutlined /></template>
                  导出
                </a-button>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 数据表格 -->
    <a-card :bordered="false" class="performance-card">
      <a-alert
        v-if="errorMessage"
        type="error"
        :message="errorMessage"
        style="margin-bottom: 16px"
        show-icon
        banner
      />

      <!-- 删除单独的统计范围切换区域 -->
      <!-- <div class="range-filter">
        <a-form-item label="统计范围">
          <a-select
            v-model:value="queryParams.range"
            style="width: 150px"
            @change="handleRangeChange"
          >
            <a-select-option value="in">统计范围内</a-select-option>
            <a-select-option value="out">统计范围外</a-select-option>
            <a-select-option value="all">全部</a-select-option>
          </a-select>
        </a-form-item>
      </div> -->

      <!-- 数据表格 -->
      <a-alert
        v-if="errorMessage"
        type="error"
        :message="errorMessage"
        style="margin-bottom: 16px"
        show-icon
        banner
      />

      <div class="performance-table">
        <a-table
          id="textbooks-main-table"
          ref="mainDataTable"
          :loading="loading"
          :columns="columns"
          :dataSource="textbooksList"
          :pagination="pagination"
          @change="handleTableChange"
          rowKey="id"
          :scroll="{ x: 1200 }"
          :bordered="true"
          class="no-hover-table main-data-table"
        >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'categoryId'">
            {{ categoryOptions.find(cat => cat.id === record.categoryId)?.categoryAndPosition || '-' }}
          </template>
          <template v-else-if="column.key === 'publishDate'">
            {{ formatDate(record.publishDate) }}
          </template>
          <template v-else-if="column.key === 'ifReviewer'">
            <a-tag :color="record.ifReviewer === true ? 'success' : (record.ifReviewer === false ? 'error' : 'warning')">
              {{ record.ifReviewer === true ? '已审核' : (record.ifReviewer === false ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'score'">
            {{ categoryOptions.find(cat => cat.id === record.categoryId)?.score || '-' }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown placement="bottomRight" :trigger="['click']">
              <template #overlay>
                <a-menu class="action-dropdown-menu">
                  <!-- 编辑选项 -->
                  <a-menu-item
                    key="edit"
                    v-if="record.ifReviewer != 1 && hasPerms(showPersonalTextbooks ? 'score:textbooks:self:update' : 'score:textbooks:admin:update')"
                  >
                    <a @click="handleEdit(record)" class="action-menu-item">
                      <EditOutlined />
                      <span>编辑</span>
                    </a>
                  </a-menu-item>

                  <!-- 重新提交审核选项 -->
                  <a-menu-item
                    key="resubmit"
                    v-if="record.ifReviewer === false && hasPerms('score:textbooks:self:reapply')"
                  >
                    <a @click="handleResubmit(record)" class="action-menu-item">
                      <ReloadOutlined />
                      <span>重新提交审核</span>
                    </a>
                  </a-menu-item>

                  <!-- 审核选项 - 仅管理员视图显示 -->
                  <a-menu-item
                    key="review"
                    v-if="!showPersonalTextbooks && !record.ifReviewer && hasPerms('score:textbooks:admin:review')"
                  >
                    <a @click="handleReview(record)" class="action-menu-item">
                      <AuditOutlined />
                      <span>审核</span>
                    </a>
                  </a-menu-item>

                  <a-menu-divider v-if="record.ifReviewer != 1 || (!showPersonalTextbooks && !record.ifReviewer)" />

                  <!-- 删除选项 -->
                  <a-menu-item
                    key="delete"
                    v-if="hasPerms(showPersonalTextbooks ? 'score:textbooks:self:delete' : 'score:textbooks:admin:delete')"
                  >
                    <a @click="confirmDelete(record)" class="action-menu-item text-danger">
                      <DeleteOutlined />
                      <span>删除</span>
                    </a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small" class="action-trigger-btn">
                操作
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
        </a-table>
      </div>

      <div class="table-footer">
        <p class="total-score">当前页总分: {{ totalScore }}</p>
      </div>
    </a-card>

    <!-- 添加/编辑表单 -->
    <a-modal
      :title="title"
      :visible="open"
      @ok="submitForm"
      @cancel="cancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="form"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item name="materialName" label="教材名称">
          <a-input v-model:value="form.materialName" placeholder="请输入教材名称" />
        </a-form-item>
        <a-form-item name="userId" label="作者">
          <a-select
            v-model:value="form.userId"
            placeholder="请选择作者"
            :loading="userSearchLoading"
            show-search
            :filter-option="false"
            @search="handleUserSearch"
            :not-found-content="userSearchLoading ? '加载中...' : (userOptions.length === 0 ? '未找到' : null)"
          >
            <a-select-option v-for="user in userOptions" :key="user.id" :value="user.id">
              {{ user.nickname || user.username }} ({{ user.studentNumber || '无工号' }})
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="publishDate" label="出版日期">
          <a-date-picker
            v-model:value="form.publishDate"
            style="width: 100%"
            format="YYYY-MM-DD"
            placeholder="请选择出版日期"
          />
        </a-form-item>
        <a-form-item name="categoryId" label="教材类别">
          <a-select v-model:value="form.categoryId" placeholder="请选择教材类别">
            <a-select-option v-for="category in categoryOptions" :key="category.id" :value="category.id">
              {{ category.categoryAndPosition }} ({{ category.score }}分)
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="remark" label="备注">
          <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="4" />
        </a-form-item>
        <a-form-item name="files" label="相关附件">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <a-upload
              v-model:file-list="fileList"
              :customRequest="handleFileUpload"
              :before-upload="beforeUpload"
              multiple
              :show-upload-list="false"
            >
              <a-button type="primary">
                <template #icon><UploadOutlined /></template>
                选择文件
              </a-button>
            </a-upload>
            <span style="margin-left: 16px; color: #666; font-size: 12px;">
              支持上传文档、图片或压缩文件，单个文件不超过10MB，总大小不超过50MB
            </span>
          </div>
          
          <!-- 使用表格显示已上传文件 -->
          <a-table
            :columns="fileColumns"
            :data-source="fileList"
            :pagination="false"
            size="small"
            style="margin-top: 8px;"
            rowKey="uid"
            v-if="fileList.length > 0"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'fileName'">
                <span :title="record.name">{{ record.originalFileName || record.name }}</span>
              </template>
              <template v-if="column.key === 'fileSize'">
                {{ formatFileSize(record.size || (record.data && record.data.size)) }}
              </template>
              <template v-if="column.key === 'status'">
                <div v-if="record.status === 'uploading'">
                  <a-progress :percent="record.percent || 0" size="small" />
                </div>
                <a-tag v-else :color="record.status === 'done' ? 'success' : (record.status === 'error' ? 'error' : 'processing')">
                  {{ record.status === 'done' ? '已上传' : (record.status === 'error' ? '上传失败' : '上传中') }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="previewFile(record)" v-if="record.status === 'done'">
                    <template #icon><EyeOutlined /></template>
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="downloadFile(record)" v-if="record.status === 'done'">
                    <template #icon><DownloadOutlined /></template>
                    下载
                  </a-button>
                  <a-popconfirm
                    title="确定要删除该文件吗？此操作将同时删除服务器上的文件"
                    @confirm="confirmDeleteFile(record)"
                    okText="确认"
                    cancelText="取消"
                  >
                    <a-button type="link" danger size="small">
                      <template #icon><DeleteOutlined /></template>
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
          
          <!-- 显示已有的文件 -->
          <a-divider v-if="fileList.length > 0 && existingFileList.length > 0" style="margin: 12px 0" />
          
          <a-table
            :columns="fileColumns"
            :data-source="existingFileList"
            :pagination="false"
            size="small"
            style="margin-top: 8px;"
            rowKey="id"
            v-if="existingFileList.length > 0"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'fileName'">
                <span :title="record.originalName">{{ record.originalName || record.fileName }}</span>
              </template>
              <template v-if="column.key === 'fileSize'">
                {{ formatFileSize(record.fileSize) }}
              </template>
              <template v-if="column.key === 'status'">
                <a-tag color="success">已上传</a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="previewExistingFile(record)">
                    <template #icon><EyeOutlined /></template>
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="downloadExistingFile(record)">
                    <template #icon><DownloadOutlined /></template>
                    下载
                  </a-button>
                  <a-popconfirm
                    title="确定要删除该文件吗？"
                    @confirm="() => removeExistingFile(record.id)"
                    okText="确认"
                    cancelText="取消"
                  >
                    <a-button type="link" danger size="small">
                      <template #icon><DeleteOutlined /></template>
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      title="教材与著作详情"
      :visible="detailOpen"
      @cancel="detailOpen = false"
      :footer="null"
    >
      <a-descriptions bordered :column="1">
        <a-descriptions-item label="教材名称">{{ detailForm.materialName }}</a-descriptions-item>
        <a-descriptions-item label="作者">{{ detailForm.user?.nickname || '-' }}</a-descriptions-item>
        <a-descriptions-item label="工号">{{ detailForm.user?.studentNumber || '-' }}</a-descriptions-item>
        <a-descriptions-item label="教材类别">{{ detailForm.category?.categoryAndPosition || '-' }}</a-descriptions-item>
        <a-descriptions-item label="分数">{{ detailForm.category?.score || '-' }}</a-descriptions-item>
        <a-descriptions-item label="出版日期">{{ formatDate(detailForm.publishDate) }}</a-descriptions-item>
        <a-descriptions-item label="审核状态">
          <a-tag :color="detailForm.ifReviewer === true ? 'success' : (detailForm.ifReviewer === false ? 'error' : 'warning')">
            {{ detailForm.ifReviewer === true ? '已审核' : (detailForm.ifReviewer === false ? '已拒绝' : '待审核') }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="审核人">{{ detailForm.reviewer?.nickname || '-' }}</a-descriptions-item>
        <a-descriptions-item label="备注">{{ detailForm.remark || '-' }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDate(detailForm.createdAt) }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ formatDate(detailForm.updatedAt) }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 作者教材列表弹窗 -->
    <a-modal
      :title="authorDetailTitle"
      :visible="authorDetailOpen"
      @cancel="authorDetailOpen = false"
      width="1200px"
      :footer="null"
      :autofocus="false"
      :focusTriggerAfterClose="false"
      centered
    >
      <a-table
        :loading="authorDetailLoading"
        :columns="authorDetailColumns"
        :dataSource="authorDetailList"
        :pagination="authorDetailPagination"
        rowKey="id"
        :bordered="true"
        @change="handleAuthorDetailPaginationChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'categoryId'">
            <div>
              <a-tag
                v-if="categoryOptions.find(cat => cat.id === record.categoryId)?.categoryAndPosition"
                :color="'blue'"
                style="max-width: 100%; white-space: normal; height: auto;"
              >
                {{ categoryOptions.find(cat => cat.id === record.categoryId)?.categoryAndPosition || '-' }}
              </a-tag>
            </div>
          </template>
          <template v-else-if="column.key === 'publishDate'">
            {{ formatDate(record.publishDate) }}
          </template>
          <template v-else-if="column.key === 'ifReviewer'">
            <a-tag :color="record.ifReviewer === true ? 'success' : (record.ifReviewer === false ? 'error' : 'warning')">
              {{ record.ifReviewer === true ? '已审核' : (record.ifReviewer === false ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'score'">
            <span style="font-weight: bold; color: #1890ff;">
              {{ categoryOptions.find(cat => cat.id === record.categoryId)?.score || '-' }}
            </span>
          </template>
        </template>
      </a-table>
      <div style="margin-top: 16px; text-align: right; font-weight: bold;">
        总得分: {{ typeof authorDetailTotalScore === 'number' ? authorDetailTotalScore.toFixed(2) : '0.00' }}分
      </div>
    </a-modal>

    <!-- 教材导入预览模态框 -->
    <a-modal
      title="教材导入预览"
      :visible="importPreviewVisible"
      width="90%"
      :maskClosable="false"
      :footer="null"
      @cancel="handleCancelImportPreview"
    >
      <template v-if="importPreviewLoading">
        <div style="text-align: center; padding: 40px;">
          <a-spin size="large" />
          <p style="margin-top: 20px;">正在解析数据，请稍候...</p>
        </div>
      </template>
      <template v-else>
        <div style="margin-bottom: 16px;">
          <a-alert
            :type="userIdCheckResults.notFound > 0 ? 'warning' : 'success'"
            :message="userIdCheckResults.notFound > 0 ? 
              `存在${userIdCheckResults.notFound}个用户ID未找到，这些记录可能导入失败` : 
              '所有用户ID均已找到'"
            show-icon
          />
          <div style="margin-top: 8px;">
            <a-space>
              <span>共找到 <b>{{ importPreviewData.length }}</b> 条记录</span>
              <a-button type="primary" @click="handleStartImport" :loading="importInProgress">
                开始导入
              </a-button>
              <a-button @click="handleCancelImportPreview">
                取消
              </a-button>
              <a-button type="primary" @click="handleDownloadJson">
                <template #icon><DownloadOutlined /></template>
                下载JSON
              </a-button>
            </a-space>
          </div>
        </div>
        
        <a-table
          :columns="previewColumns"
          :dataSource="importPreviewData.map((item, index) => ({
            ...item,
            index: index + 1,
            key: index
          }))"
          :pagination="{ pageSize: 10 }"
          :rowClassName="(record) => record.userIdCheckStatus === 'notFound' ? 'import-row-error' : ''"
          :scroll="{ x: 1200 }"
          size="small"
          bordered
          class="import-result-table"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'userIdCheckStatus'">
              <a-tag v-if="text === 'checking'" color="blue">检查中</a-tag>
              <a-tag v-else-if="text === 'found'" color="green">已找到</a-tag>
              <a-tag v-else color="red">未找到</a-tag>
            </template>
          </template>
        </a-table>
      </template>
    </a-modal>

    <!-- 教材导入结果模态框 -->
    <a-modal
      title="教材导入结果"
      :visible="importResultVisible"
      :width="800"
      :footer="null"
      :maskClosable="false"
      :closable="!importInProgress"
    >
      <div style="margin-bottom: 16px;">
        <a-progress 
          :percent="importResults.total > 0 ? Math.floor((importResults.current / importResults.total) * 100) : 0" 
          :status="importInProgress ? 'active' : (importResults.failed > 0 ? 'exception' : 'success')" 
        />
        <div style="margin-top: 16px; display: flex; justify-content: space-between;">
          <span>总记录数: <b>{{ importResults.total }}</b></span>
          <span>已处理: <b>{{ importResults.current }}</b></span>
          <span>成功: <b style="color: #52c41a">{{ importResults.success }}</b></span>
          <span>失败: <b style="color: #ff4d4f">{{ importResults.failed }}</b></span>
        </div>
      </div>
      
      <a-table
        :dataSource="importResults.details"
        :columns="resultColumns"
        rowKey="index"
        :pagination="{ pageSize: 10 }"
        :rowClassName="(record) => record.status === 'error' ? 'import-row-error' : ''"
        size="small"
        bordered
        class="import-result-table"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag v-if="text === 'success'" color="success">成功</a-tag>
            <a-tag v-else color="error">失败</a-tag>
          </template>
        </template>
      </a-table>
      
      <div style="margin-top: 16px; display: flex; justify-content: flex-end;">
        <a-space>
          <a-button 
            @click="exportFailedRecords" 
            :disabled="importInProgress || importResults.failed === 0"
            type="danger"
          >
            <template #icon><DownloadOutlined /></template>
            导出失败记录
          </a-button>
          <a-button 
            type="primary" 
            @click="importResultVisible = false"
            :disabled="importInProgress"
          >
            完成
          </a-button>
        </a-space>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick, watch, h } from 'vue';
import { message, Radio, Input, Progress, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import * as echarts from 'echarts';
import {
  getTextbookList,
  createTextbook,
  updateTextbook,
  deleteTextbook,
  reviewTextbook,
  getTextbookDetail,
  importTextbooks,
  getTextbookStatistics,
  getTextbookCategoryDistribution,
  getTextbookReviewStatusOverview,
  getTextbookCountStatistics,
  getTextbookDateDistribution,
  getAuthorRanking,
  reapplyReview
} from '@/api/modules/api.textbooks';
import { getTextbookCategories } from '@/api/modules/api.textbookCategories';
import { usersSearch } from '@/api/modules/api.users';
import { uploadFiles } from '@/api/modules/api.file'; // 添加文件上传API
import { SearchOutlined, ReloadOutlined, PlusOutlined, UploadOutlined, DownloadOutlined, UserOutlined, EyeOutlined, DeleteOutlined, FileExcelOutlined, EditOutlined, AuditOutlined, DownOutlined } from '@ant-design/icons-vue';
import useUserId from '@/composables/useUserId';
import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole } = useUserRole();
import { downloadJson } from '@/utils/fileUtils'
import { hasPerms } from '@/libs/util.common';
import { getScoreTimeRange } from '@/api/modules/api.home';
// 使用useUserId组合式函数
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId();

// 添加在其他状态变量附近
const currentRole = ref(null);
const currentUserId = ref('');

const isAdmin = computed(() => {
  // 根据当前用户角色判断是否为管理员
  return currentRole.value && (
    currentRole.value.roleAuth === 'SUPER' ||
    currentRole.value.roleAuth === 'ADMIN-LV2'
  );
});

// 错误状态
const errorMessage = ref('');

// 审核状态筛选
const reviewStatus = ref('reviewed'); // 默认显示已审核的数据

// 统计数据
const statisticsData = reactive({
  totalTextbooks: 0,
  inRangeTextbooks: 0,
  averageScore: 0,
  reviewCompletionRate: 0
});
const statisticsLoading = ref(false);

// 图表引用
const categoryChartRef = ref(null);
const reviewStatusChartRef = ref(null);
const countChartRef = ref(null);
const dateChartRef = ref(null);

// 图表实例
let categoryChart = null;
let reviewStatusChart = null;
let countChart = null;
let dateChart = null;

// 图表范围选择
const categoryChartRange = ref('in');
const reviewStatusChartRange = ref('in');
const countChartRange = ref('in');
const dateChartRange = ref('in');

// 图表审核状态筛选
const categoryChartReviewStatus = ref('reviewed');
const countChartReviewStatus = ref('reviewed');
const dateChartReviewStatus = ref('reviewed');
const authorRankChartRange = ref('in');
const authorRankChartReviewStatus = ref('reviewed');

// 查询条件
const queryParams = reactive({
  materialName: '',
  categoryId: undefined,
  startDate: undefined,
  endDate: undefined,
  reviewStatus: 'reviewed',
  range: 'in',
  page: 1,
  pageSize: 10,
  userId: undefined
});

// 计算总分
const totalScore = computed(() => {
  return textbooksList.value.reduce((sum, item) => {
    return sum + (parseFloat(item.category?.score) || 0);
  }, 0);
});

// 作者排行相关
const authorRankLoading = ref(false);
const authorRankColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    align: 'center'
  },
  {
    title: '教师姓名',
    dataIndex: 'nickname',
    key: 'nickname'
  },
  {
    title: '工号',
    dataIndex: 'studentNumber',
    key: 'studentNumber'
  },
  {
    title: '教材数量',
    key: 'textbooksCount',
    dataIndex: 'textbooksCount',
    align: 'center'
  },
  {
    title: '总分数',
    key: 'totalScore',
    align: 'center'
  },
  {
    title: '操作',
    key: 'details',
    align: 'center'
  }
];

// 是否显示个人教材与著作
const showPersonalTextbooks = ref(false);

// 添加组件卸载标志
const isUnmounted = ref(false);

// 初始化时设置userId
onMounted(async () => {
  try {
    // 获取当前用户角色
    try {
      currentRole.value = await getUserRole();
      console.log("当前用户角色:", currentRole.value);
      console.log("roleAuth===", currentRole.value ? currentRole.value.roleAuth : 'undefined');
    } catch (error) {
      console.error("获取用户角色失败:", error);
      if (!isUnmounted.value) {
        message.error("获取用户角色信息失败，某些功能可能受限");
      }
    }

    // 检查组件是否已卸载
    if (isUnmounted.value) return;

    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true);
      console.log("当前用户ID:", currentUserId.value);
    } catch (error) {
      console.error("获取用户ID失败:", error);
    }

    // 检查组件是否已卸载
    if (isUnmounted.value) return;

    // 如果是教师角色，默认显示个人教材
    if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
      showPersonalTextbooks.value = true;
      console.log('用户为教师角色，默认显示个人教材');
    }

    // 加载列表数据和分类选项
    try {
      console.log('开始加载数据...');
      await getCategoryOptions();
      console.log('分类选项加载完成');

      await getList();
      console.log('列表数据加载完成');

      await fetchStatistics();
      console.log('统计数据加载完成');
    } catch (error) {
      console.error('数据加载过程中出错:', error);
      if (!isUnmounted.value) {
        message.error('数据加载失败: ' + (error.message || '未知错误'));
      }
    }

    // 等待DOM更新后再初始化图表
    nextTick(() => {
      if (!isUnmounted.value) {
        initCharts();
      }
    });

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', handleWindowResize);

    // 加载作者排行数据
    // 移除条件判断，确保无论什么角色都加载教师排行数据
    fetchAuthorRanking();
    console.log('教师教材数量排行数据加载完成');
  } catch (error) {
    console.error('初始化失败:', error);
    if (!isUnmounted.value) {
      message.error('初始化失败');
    }
  }
});

// 组件卸载前清理
onBeforeUnmount(() => {
  try {
    // 设置卸载标志
    isUnmounted.value = true;

    // 清理图表实例
    if (categoryChart) {
      categoryChart.dispose();
      categoryChart = null;
    }
    if (reviewStatusChart) {
      reviewStatusChart.dispose();
      reviewStatusChart = null;
    }
    if (countChart) {
      countChart.dispose();
      countChart = null;
    }
    if (dateChart) {
      dateChart.dispose();
      dateChart = null;
    }

    // 清理定时器
    if (userSearchTimeout.value) {
      clearTimeout(userSearchTimeout.value);
      userSearchTimeout.value = null;
    }

    // 停止watch监听器
    if (stopDateRangeWatch) {
      stopDateRangeWatch();
    }

    // 清理所有消息提示
    message.destroy();

    // 移除窗口resize事件监听器
    window.removeEventListener('resize', handleWindowResize);

    // 重置状态变量
    convertingExcel.value = false;
    importInProgress.value = false;
    userSearchLoading.value = false;

  } catch (error) {
    console.error('清理组件资源时出错:', error);
  }
});

// 窗口resize处理函数
const handleWindowResize = () => {
  try {
    categoryChart && categoryChart.resize();
    reviewStatusChart && reviewStatusChart.resize();
    countChart && countChart.resize();
    dateChart && dateChart.resize();
  } catch (error) {
    console.error('调整图表大小出错:', error);
  }
};

// 日期范围
const dateRange = ref([]);

// 监听日期范围变化
const stopDateRangeWatch = watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    queryParams.startDate = newVal[0] ? dayjs(newVal[0]).format('YYYY-MM-DD') : undefined;
    queryParams.endDate = newVal[1] ? dayjs(newVal[1]).format('YYYY-MM-DD') : undefined;
  } else {
    queryParams.startDate = undefined;
    queryParams.endDate = undefined;
  }
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条`
});

// 表格列定义
const columns = [
  {
    title: '教材名称',
    dataIndex: 'materialName',
    key: 'materialName',
    ellipsis: true
  },
  {
    title: '出版日期',
    dataIndex: 'publishDate',
    key: 'publishDate',
    width: 120
  },
  {
    title: '类别',
    dataIndex: 'categoryId',
    key: 'categoryId',
    ellipsis: true
  },
  {
    title: '作者',
    dataIndex: 'user',
    key: 'user',
    ellipsis: true,
    customRender: ({ record }) => record.user ? record.user.nickname : '-'
  },
  {
    title: '分数',
    dataIndex: 'score',
    key: 'score',
    width: 80
  },
  {
    title: '审核状态',
    dataIndex: 'ifReviewer',
    key: 'ifReviewer',
    width: 100
  },
  {
    title: '审核建议',
    dataIndex: 'reviewComment',
    key: 'reviewComment',
    width: 150,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
    align: 'center'
  }
];

// 数据相关
const loading = ref(false);
const textbooksList = ref([]);
const categoryOptions = ref([]);
const userOptions = ref([]);
const userSearchLoading = ref(false);
const userSearchTimeout = ref(null);

// 表单相关
const formRef = ref(null);
const open = ref(false);
const title = ref('');

// 主数据表格引用
const mainDataTable = ref(null);
const form = reactive({
  id: undefined,
  materialName: '',
  userId: undefined,
  publishDate: null,
  categoryId: undefined,
  remark: '',
  fileIds: [],
  existingFiles: [],
  deletedFileIds: []
});
const rules = {
  materialName: [{ required: true, message: '请输入教材名称' }],
  userId: [{ required: true, message: '请选择作者' }],
  publishDate: [{ required: true, message: '请选择出版日期' }],
  categoryId: [{ required: true, message: '请选择类别' }]
};

// 详情相关
const detailOpen = ref(false);
const detailForm = reactive({});

// 作者教材详情相关
const authorDetailOpen = ref(false);
const authorDetailTitle = ref('');
const authorDetailList = ref([]);
const authorDetailLoading = ref(false);
const authorDetailTotalScore = ref(0);

// 文件列表相关
const fileList = ref([]);
const existingFileList = ref([]);
const fileColumns = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    key: 'fileName',
    ellipsis: true
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
];

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(2)} ${units[index]}`;
};

// 文件上传前检查，用于附件上传
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
    return false;
  }
  return true;
};

// 导入Excel前检查
const beforeImportUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传Excel文件!');
    return false;
  }
  return true;
};

// 处理文件上传
const handleFileUpload = ({ file, onSuccess, onError, onProgress }) => {
  utilUploadFile({
    file,
    uploadApi: uploadFiles,
    id: form.id || 'temp_' + Date.now(),
    relatedId: form.id,
    class: 'textbooks',
    onProgress,
    onSuccess,
    onError,
    fileList: fileList.value,
    formState: form
  });
};

import { downloadFileById, previewFileById,  deleteFileById, uploadFile as utilUploadFile } from '@/utils/others';


// 文件预览函数 - 改用ID方式
const previewFile = (file) => {
  console.log("预览文件对象:", file);
  
  // 尝试从不同位置获取文件ID
  const fileId = file.response?.file?.uid;
  
  if (fileId) {
    previewFileById(fileId);
  } else {
    message.warning('无法获取文件ID，预览失败');
  }
};

// 文件下载函数 - 改用ID方式
const downloadFile = (file) => {
  console.log("下载文件对象:", file);
  
  // 尝试从不同位置获取文件ID
  const fileId = file.response?.file?.uid;
  
  // 尝试获取文件名
  const fileName = file.name || file.originalFileName || file.response?.fileInfo?.originalName || '下载文件';
  
  if (fileId) {
    downloadFileById(fileId, fileName);
  } else {
    message.warning('无法获取文件ID，下载失败');
  }
};

// 删除上传的文件 - 改用ID方式
const confirmDeleteFile = (record) => {
  // 尝试从不同位置获取文件ID
  const fileId = record.response?.file?.uid;
  
  if (fileId) {
    deleteFileById(fileId, {
      onSuccess: () => {
        // 从文件列表中移除
        const index = fileList.value.findIndex(item => 
          item.uid === record.uid || 
          item.id === fileId
        );
        
        if (index !== -1) {
          fileList.value.splice(index, 1);
          message.success('文件已删除');
        }
      },
      onError: (errorMsg) => {
        message.error(`删除失败: ${errorMsg}`);
      }
    });
  } else {
    // 如果没有文件ID，只是从列表中移除
    const index = fileList.value.findIndex(item => item.uid === record.uid);
    if (index !== -1) {
      fileList.value.splice(index, 1);
      message.success('文件已从列表中移除');
    } else {
      message.warning('无法找到要删除的文件');
    }
  }
};

// 预览已有文件 - 改用ID方式
const previewExistingFile = (record) => {
  console.log('预览已有文件:', record);
  
  // 直接获取文件ID
  const fileId = record.id;
  
  if (fileId) {
    previewFileById(fileId);
  } else {
    message.warning('无法获取文件ID，预览失败');
  }
};

// 下载已有文件 - 改用ID方式
const downloadExistingFile = (record) => {
  console.log('下载已有文件:', record);
  
  // 直接获取文件ID
  const fileId = record.id;
  
  // 尝试获取文件名
  const fileName = record.originalName || record.fileName || '下载文件';
  
  if (fileId) {
    downloadFileById(fileId, fileName);
  } else {
    message.warning('无法获取文件ID，下载失败');
  }
};

// 删除已有的文件 - 改用ID方式
const removeExistingFile = (fileId) => {
  if (!fileId) {
    message.warning('文件ID不能为空');
    return;
  }
  
  deleteFileById(fileId, {
    onSuccess: () => {
      // 从form中添加到待删除列表
      if (!form.deletedFileIds) form.deletedFileIds = [];
      if (!form.deletedFileIds.includes(fileId)) {
        form.deletedFileIds.push(fileId);
      }
      
      // 从文件ID列表中移除
      form.fileIds = form.fileIds.filter(id => id !== fileId);
      
      // 从现有文件列表中移除
      const index = existingFileList.value.findIndex(file => file.id === fileId);
      if (index !== -1) {
        existingFileList.value.splice(index, 1);
      }
      
      message.success('文件已删除');
    },
    onError: (errorMsg) => {
      message.error(`删除失败: ${errorMsg}`);
    }
  });
};

// 处理图表范围筛选
const changeChartRange = async (type, range) => {
  try {
    // 更新相应图表的范围状态
    if (type === 'category') {
      categoryChartRange.value = range;
      await initCategoryChart();
    } else if (type === 'reviewStatus') {
      reviewStatusChartRange.value = range;
      await initReviewStatusChart();
    } else if (type === 'count') {
      countChartRange.value = range;
      await initCountChart();
    } else if (type === 'date') {
      dateChartRange.value = range;
      await initDateChart();
    } else if (type === 'authorRank') {
      authorRankChartRange.value = range;
      await fetchAuthorRanking();
    }
  } catch (error) {
    console.error(`更新${type}图表出错:`, error);
    message.error('更新图表数据失败');
  }
};

// 处理统计图表审核状态筛选
const changeChartReviewStatus = async (type, status) => {
  try {
    // 更新相应图表的审核状态
    if (type === 'category') {
      categoryChartReviewStatus.value = status;
      await initCategoryChart();
    } else if (type === 'count') {
      countChartReviewStatus.value = status;
      await initCountChart();
    } else if (type === 'date') {
      dateChartReviewStatus.value = status;
      await initDateChart();
    } else if (type === 'authorRank') {
      authorRankChartReviewStatus.value = status;
      await fetchAuthorRanking();
    }
  } catch (error) {
    console.error(`更新${type}图表审核状态出错:`, error);
    message.error('更新图表数据失败');
  }
};

// 初始化所有图表
const initCharts = () => {
  initCategoryChart();
  initReviewStatusChart();
  initCountChart();
  initDateChart();
  if (isAdmin.value) {
    fetchAuthorRanking();
  }
};

// 获取统计数据
const fetchStatistics = async () => {
  if (isUnmounted.value) return;

  statisticsLoading.value = true;

  try {
    const params = {};

    console.log('开始获取统计数据...');

    // 如果是个人视图，添加userId参数
    if (showPersonalTextbooks.value) {
      console.log('个人视图模式，获取当前用户ID...');
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        params.userId = currentUserId;
        console.log('设置统计用户ID参数:', currentUserId);
      } else {
        console.warn('无法获取当前用户ID用于统计');
      }
    }

    console.log('统计数据请求参数:', params);
    const response = await getTextbookStatistics(params);

    if (isUnmounted.value) return;

    console.log('统计数据API响应:', response);

    if (response && response.code === 200) {
      if (response.data && typeof response.data === 'object') {
        statisticsData.totalTextbooks = response.data.totalTextbooks || 0;
        statisticsData.inRangeTextbooks = response.data.inRangeTextbooks || 0;
        statisticsData.averageScore = response.data.averageScore || 0;
        statisticsData.reviewCompletionRate = response.data.reviewCompletionRate || 0;
        console.log('统计数据设置成功:', statisticsData);
      } else {
        console.error('统计数据格式错误:', response.data);
        message.error('统计数据格式错误');
      }
    } else {
      console.error('获取统计数据失败:', response);
      message.error('获取统计数据失败: ' + (response?.message || '未知错误'));
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败: ' + (error.message || '未知错误'));
    }
  } finally {
    if (!isUnmounted.value) {
      statisticsLoading.value = false;
    }
  }
};

// 初始化类别分布图表
const initCategoryChart = async () => {
  if (isUnmounted.value || !categoryChartRef.value || !categoryChartRef.value.offsetParent) return;

  try {
    // 如果图表实例已存在，则销毁
    if (categoryChart) {
      categoryChart.dispose();
      categoryChart = null;
    }

    // 初始化echarts实例
    categoryChart = echarts.init(categoryChartRef.value);
    if (!categoryChart || isUnmounted.value) return;

    categoryChart.showLoading();

    // 构建请求参数
    const params = {
      range: categoryChartRange.value,
      reviewStatus: categoryChartReviewStatus.value !== 'all' ? categoryChartReviewStatus.value : undefined
    };

    // 如果是个人视图，添加userId参数
    if (showPersonalTextbooks.value) {
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        params.userId = currentUserId;
      }
    }

    // 调用API获取数据
    const response = await getTextbookCategoryDistribution(params);

    if (isUnmounted.value) return;

    if (categoryChart) {
      categoryChart.hideLoading();
    }

    if (response && response.code === 200 && categoryChart && !isUnmounted.value) {
      const data = response.data;

      // 设置图表配置
      categoryChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '教材类别',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      });
    } else {
      message.error('获取教材与著作类别分布数据失败');
    }
  } catch (error) {
    console.error('初始化教材与著作类别分布图表失败:', error);
    message.error('加载类别分布图表失败');
  }
};

// 初始化审核状态图表
const initReviewStatusChart = async () => {
  if (isUnmounted.value || !reviewStatusChartRef.value || !reviewStatusChartRef.value.offsetParent) return;

  try {
    // 如果图表实例已存在，则销毁
    if (reviewStatusChart) {
      reviewStatusChart.dispose();
      reviewStatusChart = null;
    }

    // 初始化echarts实例
    reviewStatusChart = echarts.init(reviewStatusChartRef.value);
    if (!reviewStatusChart || isUnmounted.value) return;

    reviewStatusChart.showLoading();

    // 构建请求参数
    const params = {
      range: reviewStatusChartRange.value
      // 审核状态图表不需要传递reviewStatus参数，因为它本身就是显示审核状态的分布
    };

    // 如果是个人视图，添加userId参数
    if (showPersonalTextbooks.value) {
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        params.userId = currentUserId;
      }
    }

    // 调用API获取数据
    const response = await getTextbookReviewStatusOverview(params);

    if (isUnmounted.value) return;

    reviewStatusChart.hideLoading();
    
    if (response && response.code === 200) {
      const data = response.data;

      // 构建图表数据，包含所有状态
      const chartData = [];
      const legendData = [];

      if (data.reviewed > 0) {
        chartData.push({ value: data.reviewed, name: '已审核', itemStyle: { color: '#52c41a' } });
        legendData.push('已审核');
      }

      if (data.pending > 0) {
        chartData.push({ value: data.pending, name: '待审核', itemStyle: { color: '#faad14' } });
        legendData.push('待审核');
      }

      if (data.unreviewed > 0) {
        chartData.push({ value: data.unreviewed, name: '已拒绝', itemStyle: { color: '#ff4d4f' } });
        legendData.push('已拒绝');
      }

      // 设置图表配置
      reviewStatusChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          data: legendData,
          orient: 'vertical',
          left: 10,
          bottom: 10
        },
        series: [
          {
            name: '审核状态',
            type: 'pie',
            radius: '50%',
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      });
      
      // 删除这行代码，避免覆盖fetchStatistics中获取的值
      // statisticsData.reviewCompletionRate = data.reviewedRate || 0;
    } else {
      message.error('获取审核状态概览数据失败');
    }
  } catch (error) {
    console.error('初始化审核状态概览图表失败:', error);
    message.error('加载审核状态图表失败');
  }
};

// 初始化数量统计图表
const initCountChart = async () => {
  if (isUnmounted.value || !countChartRef.value || !countChartRef.value.offsetParent) return;

  try {
    // 如果图表实例已存在，则销毁
    if (countChart) {
      countChart.dispose();
      countChart = null;
    }

    // 初始化echarts实例
    countChart = echarts.init(countChartRef.value);
    if (!countChart || isUnmounted.value) return;

    countChart.showLoading();

    // 构建请求参数
    const params = {
      range: countChartRange.value,
      reviewStatus: countChartReviewStatus.value !== 'all' ? countChartReviewStatus.value : undefined
    };

    // 如果是个人视图，添加userId参数
    if (showPersonalTextbooks.value) {
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        params.userId = currentUserId;
      }
    }

    // 调用API获取数据
    const response = await getTextbookCountStatistics(params);

    if (isUnmounted.value) return;

    countChart.hideLoading();
    
    if (response && response.code === 200) {
      const data = response.data;
      
      // 设置图表配置
      countChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: data.map(item => item.name),
          top: 'top'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '数量',
            type: 'bar',
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: data.map(item => ({
              value: item.value,
              name: item.name,
              itemStyle: {
                color: `rgb(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)})`
              }
            }))
          }
        ]
      });
    } else {
      message.error('获取教材与著作数量统计数据失败');
    }
  } catch (error) {
    console.error('初始化教材与著作数量统计图表失败:', error);
    message.error('加载数量统计图表失败');
  }
};

// 初始化出版日期分布图表
const initDateChart = async () => {
  if (isUnmounted.value || !dateChartRef.value || !dateChartRef.value.offsetParent) return;

  try {
    // 如果图表实例已存在，则销毁
    if (dateChart) {
      dateChart.dispose();
      dateChart = null;
    }

    // 初始化echarts实例
    dateChart = echarts.init(dateChartRef.value);
    if (!dateChart || isUnmounted.value) return;

    dateChart.showLoading();

    // 构建请求参数
    const params = {
      range: dateChartRange.value,
      reviewStatus: dateChartReviewStatus.value !== 'all' ? dateChartReviewStatus.value : undefined
    };

    // 如果是个人视图，添加userId参数
    if (showPersonalTextbooks.value) {
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        params.userId = currentUserId;
      }
    }

    // 调用API获取数据
    const response = await getTextbookDateDistribution(params);

    if (isUnmounted.value) return;

    dateChart.hideLoading();
    
    if (response && response.code === 200) {
      const data = response.data;
      
      // 设置图表配置
      dateChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['教材与著作数量', '累计数量'],
          top: 'top'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.years,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '数量',
            position: 'left'
          },
          {
            type: 'value',
            name: '累计数量',
            position: 'right'
          }
        ],
        series: [
          {
            name: '教材与著作数量',
            type: 'bar',
            data: data.counts,
            itemStyle: {
              color: '#1890ff'
            }
          },
          {
            name: '累计数量',
            type: 'line',
            yAxisIndex: 1,
            data: data.cumulativeCounts,
            itemStyle: {
              color: '#52c41a'
            }
          }
        ]
      });
    } else {
      message.error('获取教材与著作出版日期分布数据失败');
    }
  } catch (error) {
    console.error('初始化教材与著作出版日期分布图表失败:', error);
    message.error('加载出版日期分布图表失败');
  }
};

// 获取教材与著作列表
const getList = async () => {
  if (isUnmounted.value) return;

  try {
    loading.value = true;
    errorMessage.value = '';

    const params = {
      ...queryParams,
      page: pagination.current,
      pageSize: pagination.pageSize
    };

    console.log('开始获取教材列表，参数:', params);

    // 如果是个人视图，添加userId参数
    if (showPersonalTextbooks.value) {
      console.log('个人视图模式，获取当前用户ID...');
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        params.userId = currentUserId;
        console.log('设置用户ID参数:', currentUserId);
      } else {
        console.warn('无法获取当前用户ID');
      }
    }

    console.log('最终请求参数:', params);
    const res = await getTextbookList(params);

    if (isUnmounted.value) return;

    console.log('教材列表API响应:', res);

    if (res && res.code === 200) {
      if (res.data && Array.isArray(res.data.list)) {
        textbooksList.value = res.data.list;
        pagination.total = res.data.pagination?.total || 0;
        console.log('教材列表设置成功:', res.data.list.length, '条记录');
      } else {
        console.error('教材列表数据格式错误:', res.data);
        textbooksList.value = [];
        pagination.total = 0;
      }
    } else {
      console.error('获取教材列表失败:', res);
      message.error(res?.message || '获取教材与著作列表失败');
      errorMessage.value = '获取教材与著作列表失败：' + (res?.message || '未知错误');
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('获取教材与著作列表失败:', error);
      message.error('获取教材与著作列表失败: ' + (error.message || '未知错误'));
      errorMessage.value = '获取教材与著作列表失败：' + (error.message || '未知错误');
    }
  } finally {
    if (!isUnmounted.value) {
      loading.value = false;
    }
  }
};

// 获取类别选项
const getCategoryOptions = async () => {
  if (isUnmounted.value) return;

  try {
    console.log('开始获取教材分类选项...');
    const res = await getTextbookCategories();

    if (isUnmounted.value) return;

    console.log('教材分类API响应:', res);

    if (res && res.code === 200) {
      if (Array.isArray(res.data)) {
        categoryOptions.value = res.data;
        console.log('教材分类选项设置成功:', res.data.length, '个分类');
      } else {
        console.error('教材分类数据格式错误:', res.data);
        categoryOptions.value = [];
      }
    } else {
      console.error('获取教材分类失败:', res);
      categoryOptions.value = [];
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('获取类别选项失败:', error);
      message.error('获取类别选项失败: ' + (error.message || '未知错误'));
    }
    categoryOptions.value = [];
  }
};

// 实现用户搜索功能
const handleUserSearch = (value) => {
  if (isUnmounted.value) return;

  if (userSearchTimeout.value) {
    clearTimeout(userSearchTimeout.value);
  }

  if (!value || value.trim() === '') {
    userOptions.value = [];
    return;
  }

  userSearchTimeout.value = setTimeout(async () => {
    if (isUnmounted.value) return;

    userSearchLoading.value = true;
    try {
      const response = await usersSearch({ keyword: value });

      if (isUnmounted.value) return;

      if (response && response.code === 200) {
        if (Array.isArray(response.data)) {
          userOptions.value = response.data;
        } else if (response.data && Array.isArray(response.data.list)) {
          userOptions.value = response.data.list;
        } else {
          console.error('搜索教师返回的数据结构异常:', response.data);
          userOptions.value = [];
        }
      } else {
        userOptions.value = [];
        console.error('搜索教师失败:', response?.message || '未知错误');
      }
    } catch (error) {
      if (!isUnmounted.value) {
        console.error('搜索教师出错:', error);
        userOptions.value = [];
      }
    } finally {
      if (!isUnmounted.value) {
        userSearchLoading.value = false;
      }
    }
  }, 500);
};

// 表格分页变化
const handleTableChange = (pag) => {
  // 只更新必要的分页属性，保持其他配置不变
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // 确保其他配置属性保持不变
  pagination.showSizeChanger = true;

  pagination.pageSizeOptions = ['10', '20', '50'];
  pagination.showTotal = (total) => `共 ${total} 条`;
  getList();
};

// 查询
const handleQuery = () => {
  pagination.current = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  Object.assign(queryParams, {
    materialName: '',
    categoryId: undefined,
    startDate: undefined,
    endDate: undefined,
    reviewStatus: undefined,
    range: 'in'
  });
  dateRange.value = [];
  pagination.current = 1;
  getList();
};

// 统计范围变化
const handleRangeChange = () => {
  // 不再立即触发数据获取
  // pagination.current = 1;
  // getList();
  // fetchStatistics();
  
  // 只更新图表
  initCharts();
};

// 新增
const handleAdd = async () => {
  if (isUnmounted.value) return;

  resetForm();
  open.value = true;
  title.value = '新增教材与著作';
  if (!isAdmin.value) {
    const currentUserId = await getUserId(true);
    if (!isUnmounted.value) {
      form.userId = currentUserId;
    }
  }
};

// 编辑
const handleEdit = async (record) => {
  resetForm();
  open.value = true;
  title.value = '编辑教材与著作';
  
  Object.assign(form, {
    id: record.id,
    materialName: record.materialName,
    userId: record.userId,
    publishDate: record.publishDate ? dayjs(record.publishDate) : undefined,
    categoryId: record.categoryId,
    remark: record.remark
  });
  
  // 获取已上传的文件信息
  try {
    // 查询详情以获取文件列表
    const res = await getTextbookDetail(record.id);
    if (res.code === 200) {
      // 加载作者信息到下拉框
      if (res.data.user) {
        const userData = res.data.user;
        userOptions.value = [{
          id: userData.id,
          nickname: userData.nickname || userData.username,
          username: userData.username,
          studentNumber: userData.studentNumber
        }];
      }
      
      // 加载文件信息
      if (res.data.attachments && res.data.attachments.length > 0) {
        form.fileIds = res.data.attachments.map(file => file.id);
        // 转换为表格可用的格式，使用name作为显示名称
        existingFileList.value = res.data.attachments.map(file => ({
          ...file,
          fileName: file.name,
          originalName: file.name,
          fileSize: file.size,
          status: 'done'
        }));
      }
    }
  } catch (error) {
    console.error('获取文件信息失败:', error);
  }
};

// 提交表单
const submitForm = async () => {
  if (isUnmounted.value) return;

  try {
    await formRef.value.validate();

    // 创建提交数据
    const formData = {
      ...form,
      publishDate: form.publishDate ? dayjs(form.publishDate).format('YYYY-MM-DD') : undefined
    };

    // 如果有fileIds，传递文件ID数组
    if (form.fileIds && form.fileIds.length > 0) {
      formData.fileIds = JSON.stringify(form.fileIds);
    }

    // 如果有需要删除的文件ID
    if (form.deletedFileIds && form.deletedFileIds.length > 0) {
      formData.deletedFileIds = JSON.stringify(form.deletedFileIds);
    }

    // 根据是否有ID决定是创建还是更新
    const api = form.id ? updateTextbook : createTextbook;
    const res = await api(formData);

    if (isUnmounted.value) return;

    if (res.code === 200) {
      message.success(`${form.id ? '修改' : '新增'}成功`);
      open.value = false;
      getList();
      fetchStatistics();
      initCharts();
    } else {
      message.error(res.message || `${form.id ? '修改' : '新增'}失败`);
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error(`${form.id ? '修改' : '新增'}失败:`, error);
      message.error(`${form.id ? '修改' : '新增'}失败`);
    }
  }
};

// 审核
const handleReview = async (record) => {
  if (isUnmounted.value) return;

  try {
    // 创建一个ref存储审核信息
    const reviewInfo = ref({
      reviewStatus: null, // 默认通过
      reviewComment: '', // 审核意见
      attachments: [] // 存储附件
    });

    // 显示加载中
    message.loading('正在加载详情...', 0.5);

    // 获取教材详情，包括附件
    try {
      // 获取详情
      const response = await getTextbookDetail(record.id);

      if (isUnmounted.value) return;

      if (response && response.code === 200 && response.data) {
        // 设置附件
        reviewInfo.value.attachments = (response.data.attachments || []).map((file, index) => ({
          uid: `-${index}`,
          name: file.name || file.originalName || `附件${index + 1}`,
          status: 'done',
          url: file.url,
          response: { file: { id: file.id } },
          data: file
        }));
        console.log('教材附件:', reviewInfo.value.attachments);
      }
    } catch (error) {
      if (!isUnmounted.value) {
        console.error('获取教材详情失败:', error);
        // 不中断流程，继续显示审核对话框
      }
    }
    
    // 确认审核操作
    Modal.confirm({
      title: '审核教材与著作',
      content: () => h('div', {}, [
        h('p', { style: { marginBottom: '10px' } }, `您正在审核"${record.materialName}"教材与著作`),
        h('div', { style: { marginBottom: '10px' } }, [
          h('span', { style: { display: 'inline-block', width: '80px' } }, '审核结果：'),
          h(Radio.Group, {
            value: reviewInfo.value.reviewStatus,
            onChange: (e) => { reviewInfo.value.reviewStatus = e.target.value }
          }, () => [
            h(Radio, { value: 1 }, () => '通过'),
            h(Radio, { value: 0 }, () => '拒绝')
          ])
        ]),
        h('div', { style: { marginBottom: '15px' } }, [
          h('span', { style: { display: 'inline-block', width: '80px', verticalAlign: 'top' } }, '审核意见：'),
          h(Input.TextArea, {
            value: reviewInfo.value.reviewComment,
            onChange: (e) => { reviewInfo.value.reviewComment = e.target.value },
            rows: 3,
            placeholder: '请输入审核意见'
          })
        ]),
        // 添加附件列表
        reviewInfo.value.attachments && reviewInfo.value.attachments.length > 0 
          ? h('div', {}, [
              h('div', { style: { fontWeight: 'bold', marginBottom: '10px' } }, '教材附件:'),
              h('div', { style: { maxHeight: '200px', overflow: 'auto' } },
                h('table', { style: { width: '100%', borderCollapse: 'collapse' } }, [
                  h('thead', {}, 
                    h('tr', { style: { backgroundColor: '#f5f5f5' } }, [
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8' } }, '文件名'),
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8', width: '120px' } }, '大小'),
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8', width: '150px' } }, '操作')
                    ])
                  ),
                  h('tbody', {}, 
                    reviewInfo.value.attachments.map(file => 
                      h('tr', { key: file.uid, style: { borderBottom: '1px solid #e8e8e8' } }, [
                        h('td', { style: { padding: '8px', textAlign: 'left' } }, file.name),
                        h('td', { style: { padding: '8px', textAlign: 'left' } }, formatFileSize(file.data?.size || 0)),
                        h('td', { style: { padding: '8px', textAlign: 'left' } },
                          h('div', { style: { display: 'flex', gap: '8px' }}, [
                            h('a', { 
                              style: { color: '#1890ff', cursor: 'pointer' },
                              onClick: () => previewFileById(file.data?.id)
                            }, '预览'),
                            h('a', { 
                              style: { color: '#1890ff', cursor: 'pointer' },
                              onClick: () => downloadFileById(file.data?.id, file.name)
                            }, '下载')
                          ])
                        )
                      ])
                    )
                  )
                ])
              )
            ])
          : h('div', { style: { color: '#999', marginTop: '10px' } }, '该教材没有附件')
      ]),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 获取当前用户ID作为审核人
          await getUserId(true);
          
          if (!userId.value) {
            message.error('无法获取用户ID，请重新登录');
            return;
          }
          
          console.log('审核人ID:', userId.value);
          
          // 调用审核API
          const submitData = {
            id: record.id,
            reviewer: userId.value,
            reviewStatus: reviewInfo.value.reviewStatus,
            reviewComment: reviewInfo.value.reviewComment
          };
          
          const response = await reviewTextbook(submitData);
          
          if (response && response.code === 200) {
            message.success('审核成功');
            getList();
            fetchStatistics();
            initReviewStatusChart();
          } else {
            message.error(response?.message || '审核失败');
          }
        } catch (error) {
          console.error('审核失败:', error);
          message.error('审核失败: ' + (error.message || '未知错误'));
        }
      },
      width: 600
    });
  } catch (error) {
    console.error('打开审核对话框失败:', error);
    message.error('操作失败: ' + (error.message || '未知错误'));
  }
};

// 导入
const handleImport = ({ file }) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传Excel文件!');
    return false;
  }

  const formData = new FormData();
  formData.append('file', file);
  
  loading.value = true;
  importTextbooks(formData)
    .then(res => {
      if (res.code === 200) {
        message.success(`导入成功，成功${res.data.success}条，失败${res.data.failed}条`);
        if (res.data.failed > 0 && res.data.errors.length > 0) {
          console.log('导入错误:', res.data.errors);
        }
        getList();
        fetchStatistics();
        initCharts();
      } else {
        message.error(res.message || '导入失败');
      }
    })
    .catch(error => {
      console.error('导入失败:', error);
      message.error('导入失败');
    })
    .finally(() => {
      loading.value = false;
    });
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  
  Object.assign(form, {
    id: undefined,
    materialName: '',
    userId: undefined,
    publishDate: null,
    categoryId: undefined,
    remark: '',
    fileIds: [],
    deletedFileIds: []
  });
  
  // 清空文件列表
  fileList.value = [];
  existingFileList.value = [];
};

// 取消
const cancel = () => {
  open.value = false;
  resetForm();
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD');
};

// 获取排名颜色
const getRankColor = (rank) => {
  if (rank === 1) return 'gold';
  if (rank === 2) return 'silver';
  if (rank === 3) return '#cd7f32'; // bronze
  return 'blue';
};

// 查看教师的教材详情
const showAuthorTextbooksDetails = async (record) => {
  if (isUnmounted.value) return;

  try {
    authorDetailLoading.value = true;
    authorDetailOpen.value = true;
    authorDetailTitle.value = `教师的的教材与著作列表`;

    // 重置分页到第一页
    authorDetailPagination.current = 1;
    authorDetailPagination.pageSize = 10;

    // 调用获取教师教材详情的函数
    await fetchAuthorTextbooksDetails(record.userId, 1, 10);
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('获取教师教材列表失败:', error);
      message.error('获取教师教材列表失败');
    }
  }
};

// 获取教师教材详情数据
const fetchAuthorTextbooksDetails = async (userId, page = 1, pageSize = 10) => {
  if (isUnmounted.value) return;

  try {
    authorDetailLoading.value = true;

    // 获取该教师的所有教材
    const params = {
      userId: userId,
      page: page,
      pageSize: pageSize,
      range: authorRankingRange.value, // 使用相同的范围筛选
      reviewStatus: authorRankingReviewStatus.value !== 'all' ? authorRankingReviewStatus.value : undefined // 添加审核状态筛选
    };

    const res = await getTextbookList(params);

    if (isUnmounted.value) return;

    if (res.code === 200) {
      authorDetailList.value = res.data.list || [];
      authorDetailPagination.total = res.data.pagination?.total || 0;

      // 计算总分
      authorDetailTotalScore.value = authorDetailList.value.reduce((sum, item) => {
        const score = parseFloat(categoryOptions.value.find(cat => cat.id === item.categoryId)?.score || 0);
        return sum + score;
      }, 0);
    } else {
      message.error(res.message || '获取教师教材列表失败');
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('获取教师教材列表失败:', error);
      message.error('获取教师教材列表失败');
    }
  } finally {
    if (!isUnmounted.value) {
      authorDetailLoading.value = false;
    }
  }
};

// 处理审核状态变更
const handleReviewStatusChange = (status) => {
  reviewStatus.value = status;
  // 重新获取表格数据
  getList();
  // 重新加载统计数据
  fetchStatistics();
  // 不需要在这里重新初始化图表，因为图表现在有自己的审核状态筛选
};

// 切换个人/全部教材视图
const togglePersonalTextbooks = () => {
  showPersonalTextbooks.value = !showPersonalTextbooks.value;
  getList();
  fetchStatistics();

  // 切换视图也需要刷新图表
  nextTick(() => {
    initCharts();
  });
};

// 确认删除
const confirmDelete = (record) => {
  handleDelete(record)
}

// 删除
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定删除"${record.materialName}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      if (isUnmounted.value) return;

      try {
        const res = await deleteTextbook({ id: record.id });

        if (isUnmounted.value) return;

        if (res.code === 200) {
          message.success('删除成功');
          getList();
          fetchStatistics();
          initCharts();
        } else {
          message.error(res.message || '删除失败');
        }
      } catch (error) {
        if (!isUnmounted.value) {
          console.error('删除失败:', error);
        }
      }
    }
  });
};

// 在script中添加authorDetailColumns
const authorDetailColumns = [
  {
    title: '教材名称',
    dataIndex: 'materialName',
    key: 'materialName',
    width: '25%',
    ellipsis: true
  },
  {
    title: '出版日期',
    dataIndex: 'publishDate',
    key: 'publishDate',
    width: '10%'
  },
  {
    title: '类别',
    dataIndex: 'categoryId',
    key: 'categoryId',
    width: '17%'
  },
  {
    title: '作者',
    dataIndex: 'user',
    key: 'user',
    width: '5%',
    ellipsis: true,
    customRender: ({ record }) => record.user ? record.user.nickname : '-'
  },
  {
    title: '分数',
    key: 'score',
    width: '5%'
  }
];

// 添加查看审核详情函数
const showReviewDetails = (record) => {
  Modal.info({
    title: '审核详情',
    width: 600,
    content: h('div', {}, [
      h('p', { style: { marginBottom: '8px' } }, `教材名称: ${record.materialName}`),
      h('p', { style: { marginBottom: '8px' } }, `审核状态: ${record.ifReviewer === true ? '已审核' : (record.ifReviewer === false ? '已拒绝' : '待审核')}`),
      h('p', { style: { marginBottom: '8px' } }, `审核人: ${record.reviewer?.nickname || '暂无'}`),
      h('p', { style: { marginBottom: '8px' } }, `审核时间: ${record.reviewTime ? dayjs(record.reviewTime).format('YYYY-MM-DD HH:mm:ss') : '暂无'}`),
      h('p', { style: { marginBottom: '8px' } }, `审核意见: ${record.reviewComment || '暂无'}`),
    ]),
    okText: '关闭'
  });
};

// 添加导出状态变量
const exportLoading = ref(false);

// 根据当前筛选条件导出数据
const handleExportFiltered = async () => {
  if (isUnmounted.value) return;

  try {
    exportLoading.value = true;

    // 构建查询参数，与搜索相同但设置isExport为true
    const exportParams = {
      ...queryParams,
      isExport: true // 告知后端这是导出操作
    };

    // 调用API获取数据
    const response = await getTextbookList(exportParams);

    if (isUnmounted.value) return;

    if (response && response.code === 200 && response.data && response.data.list) {
      const data = response.data.list;

      // 处理数据为Excel格式
      const excelData = data.map(item => {
        return {
          '教材名称': item.materialName || '',
          '出版日期': formatDate(item.publishDate) || '',
          '类别': categoryOptions.value.find(cat => cat.id === item.categoryId)?.categoryAndPosition || '',
          '作者': item.user?.nickname || '',
          '工号': item.user?.studentNumber || '',
          '分数': item.category?.score || '',
          '审核状态': item.ifReviewer === true ? '已审核' : (item.ifReviewer === false ? '已拒绝' : '待审核'),
          '是否在统计范围内': item.isInTimeRange ? '是' : '否',
          '备注': item.remark || '',
          '创建时间': formatDate(item.createdAt) || ''
        };
      });

      // 导出Excel
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '教材与著作数据');

      // 设置列宽
      const columnWidths = [
        { wch: 30 }, // 教材名称
        { wch: 12 }, // 出版日期
        { wch: 15 }, // 类别
        { wch: 12 }, // 作者
        { wch: 12 }, // 工号
        { wch: 8 },  // 分数
        { wch: 10 }, // 审核状态
        { wch: 15 }, // 是否在统计范围内
        { wch: 20 }, // 备注
        { wch: 20 }  // 创建时间
      ];
      worksheet['!cols'] = columnWidths;

      // 生成文件名
      const fileName = `教材与著作数据_${dayjs().format('YYYY-MM-DD')}.xlsx`;

      // 下载文件
      XLSX.writeFile(workbook, fileName);
      message.success('导出成功');
    } else {
      message.error('导出失败：未找到符合条件的记录');
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('导出失败:', error);
      message.error('导出失败: ' + (error.message || '未知错误'));
    }
  } finally {
    if (!isUnmounted.value) {
      exportLoading.value = false;
    }
  }
};

// 添加XLSX导入
import * as XLSX from 'xlsx';

// 添加状态变量
const authorRankingExportLoading = ref(false);
const authorRankingRange = ref('in');
const authorRankingReviewStatus = ref('reviewed');
const authorRankingPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
});
// 添加作者搜索参数对象
const authorSearchParams = reactive({
  nickname: ''
});
const authorRankingData = ref([]);

// 获取教师教材数量排行
const fetchAuthorRanking = async () => {
  if (isUnmounted.value) return;

  try {
    authorRankLoading.value = true;

    const params = {
      range: authorRankingRange.value,
      reviewStatus: authorRankingReviewStatus.value,
      page: authorRankingPagination.current,
      pageSize: authorRankingPagination.pageSize,
      isExport: false,
      nickname: authorSearchParams.nickname
    };

    const response = await getAuthorRanking(params);

    if (isUnmounted.value) return;

    if (response && response.code === 200) {
      authorRankingData.value = response.data.list;
      authorRankingPagination.current = response.data.pagination.page;
      authorRankingPagination.pageSize = response.data.pagination.pageSize;
      authorRankingPagination.total = response.data.pagination.total;
    } else {
      message.error(response?.message || '获取教师教材数量排行失败');
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('获取教师教材数量排行失败:', error);
      message.error('获取教师教材数量排行失败');
    }
  } finally {
    if (!isUnmounted.value) {
      authorRankLoading.value = false;
    }
  }
};

// 导出教师教材数量排行
const exportAuthorRanking = async () => {
  if (isUnmounted.value) return;

  try {
    authorRankingExportLoading.value = true;

    const params = {
      range: authorRankingRange.value,
      reviewStatus: authorRankingReviewStatus.value,
      isExport: true
    };

    const response = await getAuthorRanking(params);

    if (isUnmounted.value) return;

    if (response && response.code === 200 && response.data && response.data.list) {
      const data = response.data.list;

      // 处理数据为Excel格式
      const excelData = data.map(item => {
        return {
          '排名': item.rank,
          '教师姓名': item.nickname || '',
          '工号': item.studentNumber || '',
          '教材数量': item.textbooksCount || 0,
          '总分': Number(item.totalScore || 0).toFixed(2)
        };
      });

      // 导出Excel
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '教师教材数量排行');

      // 设置列宽
      const columnWidths = [
        { wch: 8 },   // 排名
        { wch: 15 },  // 教师姓名
        { wch: 15 },  // 工号
        { wch: 10 },  // 教材数量
        { wch: 10 }   // 总分
      ];
      worksheet['!cols'] = columnWidths;

      // 生成文件名
      const fileName = `教师教材数量排行_${dayjs().format('YYYY-MM-DD')}.xlsx`;

      // 下载文件
      XLSX.writeFile(workbook, fileName);
      message.success('导出成功');
    } else {
      message.error('导出失败：未找到数据');
    }
  } catch (error) {
    if (!isUnmounted.value) {
      console.error('导出失败:', error);
      message.error('导出失败: ' + (error.message || '未知错误'));
    }
  } finally {
    if (!isUnmounted.value) {
      authorRankingExportLoading.value = false;
    }
  }
};



// 处理表格分页变化
const handleAuthorRankingTableChange = (pagination) => {
  // 只更新必要的分页属性，保持其他配置不变
  authorRankingPagination.current = pagination.current;
  authorRankingPagination.pageSize = pagination.pageSize;
  // 确保其他配置属性保持不变
  authorRankingPagination.showSizeChanger = true;

  authorRankingPagination.pageSizeOptions = ['10', '20', '50'];
  authorRankingPagination.showTotal = total => `共 ${total} 条`;
  fetchAuthorRanking();
};

// 添加筛选条件变化的处理函数
const handleAuthorRankingRangeChange = () => {
  // 重置分页到第一页
  authorRankingPagination.current = 1;
  // 重新加载数据
  fetchAuthorRanking();
};

const handleAuthorRankingReviewStatusChange = () => {
  // 重置分页到第一页
  authorRankingPagination.current = 1;
  // 重新加载数据
  fetchAuthorRanking();
};

// 添加分页配置
const authorDetailPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条`
});

// 处理分页变化
const handleAuthorDetailPaginationChange = (pagination) => {
  // 只更新必要的分页属性，保持其他配置不变
  authorDetailPagination.current = pagination.current;
  authorDetailPagination.pageSize = pagination.pageSize;
  // 确保其他配置属性保持不变
  authorDetailPagination.showSizeChanger = true;

  authorDetailPagination.pageSizeOptions = ['10', '20', '50'];
  authorDetailPagination.showTotal = (total) => `共 ${total} 条`;
  // 获取当前选中的教师ID - 从详情标题中提取或使用其他方式
  // 这里需要一个更可靠的方式来获取当前查看的教师ID
  // 暂时跳过分页功能，避免错误
  console.warn('教师详情分页功能需要重新实现');
};

// 添加导入Excel预览和导入结果相关变量
const importPreviewData = ref([])
const importPreviewLoading = ref(false)
const importPreviewVisible = ref(false)
const importResultVisible = ref(false)
const importInProgress = ref(false)
const convertingExcel = ref(false)
const userIdCheckResults = ref({
  total: 0,
  found: 0,
  notFound: 0
})

// 预览表格列定义
const previewColumns = [
  { title: '序号', dataIndex: 'index', width: 60, fixed: 'left' },
  { title: '教材名称', dataIndex: 'materialName', ellipsis: true, width: 200, fixed: 'left' },
  { title: '作者', dataIndex: 'author', width: 100 },
  { title: '出版日期', dataIndex: 'publishDate', width: 120 },
  { title: '类别', dataIndex: 'categoryName', width: 150 },
  { title: '备注', dataIndex: 'remark', width: 150 },
  { title: '用户ID状态', dataIndex: 'userIdCheckStatus', width: 120, fixed: 'right' }
]

// 结果表格列定义
const resultColumns = [
  { title: '序号', dataIndex: 'index', width: 60 },
  { title: '教材名称', dataIndex: 'materialName', ellipsis: true },
  { title: '状态', dataIndex: 'status', width: 80 },
  { title: '结果信息', dataIndex: 'message', ellipsis: true }
]

// 导入结果数据
const importResults = reactive({
  total: 0,
  current: 0,
  success: 0,
  failed: 0,
  details: []
})

// 验证Excel文件上传
const beforeExcelUpload = (file) => {
  // 检查文件类型
  const isExcel = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
    file.type === 'application/vnd.ms-excel' || 
    file.name.endsWith('.csv')
  
  if (!isExcel) {
    message.error('只能上传Excel文件 (.xlsx, .xls, .csv)!')
    return false
  }
  
  // 文件大小限制：20MB
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    message.error('文件大小不能超过20MB!')
    return false
  }
  
  return true
}

// 处理Excel到JSON转换
const handleExcelToJsonConvert = async ({ file }) => {
  if (isUnmounted.value) return;

  // 创建唯一的消息ID
  const messageKey = `excel_convert_${Date.now()}`

  try {
    convertingExcel.value = true

    // 显示正在处理的提示
    message.loading({
      content: '正在解析Excel文件，请稍候...',
      key: messageKey,
      duration: 0
    })

    // 获取文件基本名（不含扩展名）
    const fileName = file.name.split('.').slice(0, -1).join('.') || 'textbook_export'

    // 默认配置：表头在第3行 (从0开始计数，索引为2)
    const options = {
      headerRow: 2, // 第3行作为表头
      sheetName: null // 默认使用第一个工作表
    }

    // 导入excelToTextbooksJson函数
    const { excelToTextbooksJson } = await import('@/utils/fileUtils')

    if (isUnmounted.value) return;

    // 转换Excel为JSON
    const textbooksData = await excelToTextbooksJson(file, options)

    if (isUnmounted.value) return;

    // 更新消息为处理完成
    message.loading({
      content: '数据解析完成...',
      key: messageKey,
      duration: 1
    })

    if (textbooksData.length === 0) {
      message.warning('未从Excel文件中提取到任何有效数据', 3)
      return
    }

    console.log(`解析成功，共 ${textbooksData.length} 条教材数据`)

    // 处理预览数据
    prepareTextbooksForPreview(textbooksData)

  } catch (error) {
    if (!isUnmounted.value) {
      console.error('Excel转JSON处理失败:', error)
      // 确保任何loading消息都被清除
      message.destroy(messageKey)
      message.error(`Excel文件处理失败: ${error.message || '未知错误'}`, 5)
    }
  } finally {
    if (!isUnmounted.value) {
      // 最后确保状态复位
      convertingExcel.value = false

      // 延迟0.5秒后销毁所有可能存在的消息
      setTimeout(() => {
        message.destroy(messageKey)
      }, 500)
    }
  }
}

// 准备教材数据进行预览
const prepareTextbooksForPreview = async (textbooks) => {
  if (isUnmounted.value) return;

  importPreviewLoading.value = true
  importPreviewData.value = []
  userIdCheckResults.value = {
    total: textbooks.length,
    found: 0,
    notFound: 0
  }

  // 处理教材数据，准备预览
  const previewData = textbooks.map((textbook, index) => {
    return {
      key: index,
      materialName: textbook.materialName || '',
      author: textbook.author || '',
      personnelId: textbook.personnelId || '',
      publishDate: textbook.publishDate || '',
      categoryName: textbook.categoryName || '',
      remark: textbook.remark || '',
      userIdCheckStatus: 'checking', // 初始状态为检查中
      rawData: textbook // 保存原始数据
    }
  })

  if (isUnmounted.value) return;

  importPreviewData.value = previewData
  importPreviewVisible.value = true

  // 检查用户ID
  await checkAuthorUserId()

  if (!isUnmounted.value) {
    importPreviewLoading.value = false
  }
}

// 检查作者用户ID
const checkAuthorUserId = async () => {
  if (isUnmounted.value) return;

  const textbooksData = importPreviewData.value
  let foundCount = 0
  let notFoundCount = 0

  for (let i = 0; i < textbooksData.length; i++) {
    if (isUnmounted.value) return;

    const textbook = textbooksData[i]
    let found = false

    // 如果有人事编号或作者姓名，则进行搜索
    if (textbook.personnelId || textbook.author) {
      try {
        // 优先使用人事编号搜索
        const searchKeyword = textbook.personnelId || textbook.author
        // 搜索用户ID
        const response = await usersSearch({ keyword: searchKeyword })

        if (isUnmounted.value) return;

        if (response && response.data && response.data.length > 0) {
          // 找到匹配的用户
          textbook.userId = response.data[0].id
          textbook.rawData.userId = response.data[0].id
          found = true
          foundCount++
        } else {
          // 如果用人事编号没找到，尝试使用作者姓名搜索
          if (textbook.personnelId && textbook.author) {
            const nameResponse = await usersSearch({ keyword: textbook.author })

            if (isUnmounted.value) return;

            if (nameResponse && nameResponse.data && nameResponse.data.length > 0) {
              textbook.userId = nameResponse.data[0].id
              textbook.rawData.userId = nameResponse.data[0].id
              found = true
              foundCount++
            } else {
              textbook.userId = null
              textbook.rawData.userId = null
              notFoundCount++
            }
          } else {
            textbook.userId = null
            textbook.rawData.userId = null
            notFoundCount++
          }
        }
      } catch (error) {
        if (!isUnmounted.value) {
          console.error(`查找用户"${textbook.author}"失败:`, error)
        }
        textbook.userId = null
        textbook.rawData.userId = null
        notFoundCount++
      }
    } else {
      // 如果既没有人事编号也没有作者姓名，则标记为未找到
      textbook.userId = null
      textbook.rawData.userId = null
      notFoundCount++
    }

    // 更新检查状态
    textbook.userIdCheckStatus = found ? 'found' : 'notFound'
  }

  if (isUnmounted.value) return;

  // 更新结果统计
  userIdCheckResults.value = {
    total: foundCount + notFoundCount,
    found: foundCount,
    notFound: notFoundCount
  }
}

// 取消导入预览
const handleCancelImportPreview = () => {
  importPreviewVisible.value = false
  importPreviewData.value = []
}

// 开始导入教材数据
const handleStartImport = async () => {
  if (importPreviewData.value.length === 0) {
    message.warning('没有可导入的教材数据')
    return
  }
  
  // 重置导入结果
  importResults.total = importPreviewData.value.length
  importResults.success = 0
  importResults.failed = 0
  importResults.current = 0
  importResults.details = []
  
  // 显示结果模态框
  importResultVisible.value = true
  importInProgress.value = true
  
  try {
    for (let i = 0; i < importPreviewData.value.length; i++) {
      if (isUnmounted.value) return;

      importResults.current = i + 1
      const textbook = importPreviewData.value[i].rawData

      try {
        // 构建请求数据
        const textbookData = {
          userId: textbook.userId,
          materialName: textbook.materialName,
          publishDate: textbook.publishDate,
          remark: textbook.remark
        }

        // 查找教材分类ID
        if (textbook.categoryName) {
          // 在分类选项中查找匹配的分类
          const category = categoryOptions.value.find(
            c => c.categoryName === textbook.categoryName ||
                 c.categoryAndPosition === textbook.categoryName ||
                 (c.categoryName && c.categoryName.includes(textbook.categoryName)) ||
                 (c.categoryAndPosition && c.categoryAndPosition.includes(textbook.categoryName))
          )

          if (category) {
            textbookData.categoryId = category.id
          } else {
            throw new Error(`未找到教材分类: ${textbook.categoryName}`)
          }
        } else {
          throw new Error('教材分类不能为空')
        }

        // 发送创建教材请求
        const response = await createTextbook(textbookData)

        if (isUnmounted.value) return;

        if (response && response.code === 200) {
          importResults.success++
          importResults.details.push({
            index: importResults.details.length + 1,
            materialName: textbook.materialName,
            message: '导入成功',
            status: 'success'
          })
        } else {
          throw new Error(response?.message || '创建教材失败')
        }
      } catch (error) {
        if (!isUnmounted.value) {
          console.error(`导入教材"${textbook.materialName}"失败:`, error)
        }
        importResults.failed++
        importResults.details.push({
          index: importResults.details.length + 1,
          materialName: textbook.materialName,
          message: `导入失败: ${error.message || '未知错误'}`,
          status: 'error'
        })
      }

      // 等待短暂延迟，让UI有时间更新
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    if (isUnmounted.value) return;

    // 导入完成后重新获取数据
    getList()
    fetchStatistics()
    initCharts()

  } catch (error) {
    if (!isUnmounted.value) {
      console.error('导入过程中发生错误:', error)
      message.error(`导入过程中发生错误: ${error.message || '未知错误'}`)
    }
  } finally {
    if (!isUnmounted.value) {
      importInProgress.value = false
      importPreviewVisible.value = false
    }
  }
}

// 导出失败记录
const exportFailedRecords = async () => {
  const failedRecords = importResults.details
    .filter(item => item.status === 'error')
    .map((item, index) => ({
      '序号': index + 1,
      '教材名称': item.materialName,
      '失败原因': item.message
    }))
    
  if (failedRecords.length === 0) {
    message.info('没有失败记录需要导出')
    return
  }
  
  try {
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(failedRecords)
    
    // 创建工作簿
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, '导入失败记录')
    
    // 生成Excel文件名称
    const fileName = `教材导入失败记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
    
    // 下载文件
    XLSX.writeFile(workbook, fileName)
    
    message.success('导出失败记录成功')
  } catch (error) {
    console.error('导出失败记录出错:', error)
    message.error(`导出失败记录出错: ${error.message || '未知错误'}`)
  }
}

// 下载预览数据为JSON文件
const handleDownloadJson = async () => {
  try {
    if (!importPreviewData.value || importPreviewData.value.length === 0) {
      message.warning('没有可下载的数据')
      return
    }
    
    // 提取原始数据
    const originalData = importPreviewData.value.map(item => item.rawData)
    
    // 下载为JSON文件
    await downloadJson(originalData, `教材数据_${dayjs().format('YYYY-MM-DD')}`)
    message.success('JSON文件下载成功')
  } catch (error) {
    console.error('下载JSON文件失败:', error)
    message.error('下载失败: ' + (error.message || '未知错误'))
  }
}

// 处理重新提交审核
const handleResubmit = (record) => {
  Modal.confirm({
    title: '确认重新提交审核',
    content: '是否确认将该教材重新提交审核？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await resubmitForReview(record.id);
    }
  });
};

// 重新提交审核接口调用
const resubmitForReview = async (id) => {
  try {
    loading.value = true;
    const response = await reapplyReview({ id });

    if (response && response.code === 200) {
      message.success('重新提交审核成功');
      getList(); // 刷新数据
      fetchStatistics(); // 刷新统计数据

      // 刷新图表数据
      if (!showPersonalTextbooks.value) {
        initCharts();
      }
    } else {
      message.error(response?.message || '重新提交审核失败');
    }
  } catch (error) {
    console.error('重新提交审核失败:', error);
    message.error('重新提交审核失败: ' + (error.message || error));
  } finally {
    loading.value = false;
  }
};

// 重置作者排行搜索
const resetAuthorSearch = () => {
  authorSearchParams.nickname = '';
  fetchAuthorRanking();
};

// 添加时间范围文本
const timeRangeText = ref('');

// 获取时间范围
const getTimeRange = () => {
  // 调用API获取时间范围
  getScoreTimeRange('textbooks').then(res => {
    if (res.code === 200 && res.data) {
      timeRangeText.value = res.data.timeRange || '';
    } else {
      timeRangeText.value = '暂无时间范围数据';
    }
  }).catch(error => {
    console.error('获取时间范围失败:', error);
    timeRangeText.value = '获取时间范围失败';
  });
};

// 在created钩子中调用获取时间范围
onMounted(() => {
  getTimeRange();
});
</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';

// 页面特定样式（已移除重复的公共样式）

/* 错误行样式 */
:deep(.import-row-error) {
  background-color: rgba(245, 34, 45, 0.05);
}

:deep(.import-row-error:hover > td) {
  background-color: rgba(245, 34, 45, 0.1) !important;
}
</style>