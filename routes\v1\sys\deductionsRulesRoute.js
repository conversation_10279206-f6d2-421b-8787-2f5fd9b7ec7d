const express = require('express');
const router = express.Router();
const deductionsRulesController = require('../../../controllers/v1/rules/deductionsRulesController');

/**
 * 获取扣减规则列表
 * @route GET /v1/sys/deductions-rules/list
 * @group 扣减规则管理 - 扣减规则相关接口
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 0, page: 1, pageSize: 10, list: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list', deductionsRulesController.getDeductionsRules);

/**
 * 获取扣减规则详情
 * @route GET /v1/sys/deductions-rules/detail
 * @group 扣减规则管理 - 扣减规则相关接口
 * @param {string} id.query - 规则ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {规则详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail', deductionsRulesController.getDeductionsRuleDetail);

/**
 * 创建扣减规则
 * @route POST /v1/sys/deductions-rules/create
 * @group 扣减规则管理 - 扣减规则相关接口
 * @param {number} unTimelyDegreeCount.body - 未按时获得学位的研究生数
 * @param {number} unTimelyDegreeDeduction.body - 未按时获得学位的扣减分数
 * @param {string} unemploymentDate.body - 未就业的截止日期
 * @param {number} unemploymentDeduction.body - 未就业的扣减分数
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {创建的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', deductionsRulesController.createDeductionsRule);

/**
 * 更新扣减规则
 * @route PUT /v1/sys/deductions-rules/update
 * @group 扣减规则管理 - 扣减规则相关接口
 * @param {string} id.body.required - 规则ID
 * @param {number} unTimelyDegreeCount.body - 未按时获得学位的研究生数
 * @param {number} unTimelyDegreeDeduction.body - 未按时获得学位的扣减分数
 * @param {string} unemploymentDate.body - 未就业的截止日期
 * @param {number} unemploymentDeduction.body - 未就业的扣减分数
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {更新后的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/update', deductionsRulesController.updateDeductionsRule);

/**
 * 删除扣减规则
 * @route DELETE /v1/sys/deductions-rules/delete
 * @group 扣减规则管理 - 扣减规则相关接口
 * @param {string} id.query.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete', deductionsRulesController.deleteDeductionsRule);

module.exports = router; 