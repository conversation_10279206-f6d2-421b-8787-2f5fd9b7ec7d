// src/api/modules/api.file.js
import request from '../server'
import config from '../../config'

/**
 * 上传单个或多个文件
 * @param {Object} options - 上传选项
 * @param {FileList|File[]} options.files - 要上传的文件
 * @param {String} options.id - 用于创建文件夹的唯一ID
 * @param {String} [options.description] - 文件描述
 * @param {String} [options.relatedId] - 关联ID，默认使用id参数
 * @param {String} [options.class] - 文件分类，也用作存储路径名称
 * @returns {Promise} - 上传结果
 */
export function uploadFiles(options) {
  const { files, id, description, relatedId, class: classType } = options
  const formData = new FormData()

  // 添加单个或多个文件
  if (files instanceof FileList || Array.isArray(files)) {
    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i])
    }
  } else if (files instanceof File) {
    formData.append('files', files)
  } else {
    return Promise.reject(new Error('文件参数无效'))
  }

  // 添加其他参数
  formData.append('id', id)
  if (classType) {
    formData.append('class', classType)
  }
  if (description) formData.append('description', description)
  if (relatedId) formData.append('relatedId', relatedId)

  return request.post('/sys/file/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传科研项目附件
 * @param {Object} options - 上传选项
 * @param {FileList|File[]} options.files - 要上传的文件
 * @param {String} options.projectId - 项目ID
 * @param {String} [options.description] - 文件描述
 * @returns {Promise} - 上传结果
 */
export function uploadProjectAttachments(options) {
  const { files, projectId, description } = options
  const formData = new FormData()

  // 添加文件
  if (files instanceof FileList || Array.isArray(files)) {
    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i])
    }
  } else if (files instanceof File) {
    formData.append('files', files)
  } else {
    return Promise.reject(new Error('文件参数无效'))
  }

  // 添加其他参数
  formData.append('projectId', projectId)
  if (description) formData.append('description', description)

  return request.post('/file/upload-project-attachments', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 下载文件
 * @param {String} fileId - 文件ID
 * @param {Boolean} [openInNewTab=false] - 是否在新标签页中打开
 * @returns {Promise|void} - 如果openInNewTab为true则无返回，否则返回文件Blob
 */
export function downloadFile(fileId, openInNewTab = false) {
  const url = `${config.baseUrl}/api/v1/file/download/${fileId}`

  if (openInNewTab) {
    window.open(url, '_blank')
    return
  }

  return request.get(`/file/download/${fileId}`, null, 'blob')
}

/**
 * 删除文件
 * @param {String} fileId - 文件ID
 * @returns {Promise} - 删除结果
 */
export function deleteFile(fileId) {
  return request.delete(`/sys/file/delete/${fileId}`)
}

/**
 * 获取关联文件列表
 * @param {Object} params - 查询参数
 * @param {String} params.relatedId - 关联ID
 * @param {String} params.relatedType - 关联类型
 * @returns {Promise} - 文件列表
 */
export function getRelatedFiles(params) {
  return request.post('/file/related-files', params)
}

/**
 * 获取文件下载URL
 * @param {String} fileId - 文件ID
 * @returns {String} - 文件下载URL
 */
export function getFileUrl(fileId) {
  return `${config.baseUrl}/api/v1/file/download/${fileId}`
}