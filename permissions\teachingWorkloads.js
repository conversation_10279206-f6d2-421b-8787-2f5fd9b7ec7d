/**
 * 教学工作量模块权限配置
 * 简化版 - 角色验证 + 教师数据自动填充
 */
module.exports = {
  // 基本权限配置
  list: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  create: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 创建时不限制教师，允许自由创建
  },

  update: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  delete: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  review: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以审核，无需教师访问控制
  },

  import: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以导入，无需教师访问控制
  },

  export: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  detail: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  // 统计相关权限
  stats: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  typeDistribution: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  levelDistribution: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  reviewStatusOverview: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  userTotalScore: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  allUsersTotalScore: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  userWorkloadDetails: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  workloadsTotalScore: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  userWorkloadsDetail: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  // 重新提交审核权限
  reapply: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  // 级别管理权限
  levels: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  levelsList: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  levelsDetail: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  levelsCreate: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  levelsUpdate: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  levelsDelete: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 导出导入权限
  export: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  import: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 统计分析权限
  statistics: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  userSummary: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  departmentRanking: {
    roles: ['ADMIN-LV2', 'SUPER']
  },

  // 参与者管理权限
  participants: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  }
};
