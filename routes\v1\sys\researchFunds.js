const express = require('express');
const router = express.Router();
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const researchFundController = require('../../../controllers/v1/sys/researchFundController');
const { Op } = require('sequelize');

/**
 * @swagger
 * /research-funds:
 *   get:
 *     summary: 获取科研经费列表
 *     tags: [科研管理]
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: 经费名称
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: 经费类型
 *       - in: query
 *         name: amount
 *         schema:
 *           type: number
 *         description: 经费金额
 *       - in: query
 *         name: leader
 *         schema:
 *           type: string
 *         description: 负责人
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *         description: 开始时间
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *         description: 结束时间
 *       - in: query
 *         name: userOnly
 *         schema:
 *           type: boolean
 *         description: 是否只看当前用户参与的项目
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: 用户ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *         description: 每页记录数
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/', (req, res, next) => {
  const { userOnly, userId } = req.query;
  
  // 如果是查询用户参与的项目
  if (userOnly === 'true' && userId) {
    req.query.where = {
      [Op.or]: [
        { leader: userId },
        { members: { [Op.like]: `%${userId}%` } }
      ]
    };
  }
  
  next();
}, researchFundController.getResearchFunds);

/**
 * @swagger
 * /research-funds/{id}:
 *   get:
 *     summary: 获取科研经费详情
 *     tags: [科研管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 科研经费ID
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/:id', researchFundController.getResearchFundDetail);

/**
 * @swagger
 * /research-funds:
 *   post:
 *     summary: 创建科研经费
 *     tags: [科研管理]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 经费名称
 *               type:
 *                 type: string
 *                 description: 经费类型
 *               amount:
 *                 type: number
 *                 description: 经费金额
 *               leader:
 *                 type: string
 *                 description: 负责人
 *               members:
 *                 type: array
 *                 description: 成员
 *               startDate:
 *                 type: string
 *                 description: 开始时间
 *               endDate:
 *                 type: string
 *                 description: 结束时间
 *               description:
 *                 type: string
 *                 description: 经费描述
 *               score:
 *                 type: number
 *                 description: 得分
 *               status:
 *                 type: integer
 *                 description: 状态
 *     responses:
 *       200:
 *         description: 创建成功
 */
router.post('/', researchFundController.createResearchFund);

/**
 * @swagger
 * /research-funds/{id}:
 *   put:
 *     summary: 更新科研经费
 *     tags: [科研管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 科研经费ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 经费名称
 *               type:
 *                 type: string
 *                 description: 经费类型
 *               amount:
 *                 type: number
 *                 description: 经费金额
 *     responses:
 *       200:
 *         description: 更新成功
 */
router.put('/:id', researchFundController.updateResearchFund);

/**
 * @swagger
 * /research-funds/{id}:
 *   delete:
 *     summary: 删除科研经费
 *     tags: [科研管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 科研经费ID
 *     responses:
 *       200:
 *         description: 删除成功
 */
router.delete('/:id', researchFundController.deleteResearchFund);

/**
 * @swagger
 * /research-funds/import:
 *   post:
 *     summary: 导入科研经费数据
 *     tags: [科研管理]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Excel文件
 *     responses:
 *       200:
 *         description: 导入成功
 */
router.post('/import', upload.single('file'), researchFundController.importResearchFunds);

/**
 * @swagger
 * /research-funds/export:
 *   get:
 *     summary: 导出科研经费数据
 *     tags: [科研管理]
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: 经费名称
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: 经费类型
 *       - in: query
 *         name: amount
 *         schema:
 *           type: number
 *         description: 经费金额
 *       - in: query
 *         name: leader
 *         schema:
 *           type: string
 *         description: 负责人
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *         description: 开始时间
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *         description: 结束时间
 *     responses:
 *       200:
 *         description: 导出成功
 */
router.get('/export', researchFundController.exportResearchFunds);

module.exports = router; 