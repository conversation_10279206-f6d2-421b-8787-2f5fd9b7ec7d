import request from '../server'

/**
 * 获取学术任职级别列表
 * @returns {Promise} - 请求结果
 */
export function getAssociationLevels() {
  return request.get('/associationLevels/levels')
}

/**
 * 获取所有级别及其学术任职数量
 * @returns {Promise} - 请求结果
 */
export function getLevelsWithCount() {
  return request.get('/associationLevels/levels-with-count')
}

/**
 * 获取学术任职级别详情
 * @param {string} id - 级别ID
 * @returns {Promise} - 请求结果
 */
export function getAssociationLevelDetail(id) {
  return request.get(`/associationLevels/level/${id}`)
}

/**
 * 创建学术任职级别
 * @param {Object} data - 级别数据
 * @returns {Promise} - 请求结果
 */
export function createAssociationLevel(data) {
  return request.post('/associationLevels/level/create', data)
}

/**
 * 更新学术任职级别
 * @param {string} id - 级别ID
 * @param {Object} data - 更新数据
 * @returns {Promise} - 请求结果
 */
export function updateAssociationLevel(id, data) {
  return request.post('/associationLevels/level/update', { id, ...data })
}

/**
 * 删除学术任职级别
 * @param {string} id - 级别ID
 * @returns {Promise} - 请求结果
 */
export function deleteAssociationLevel(id) {
  return request.post('/associationLevels/level/delete', { id })
}