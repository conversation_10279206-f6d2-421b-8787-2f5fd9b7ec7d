<template>
  <div class="notification-settings">
    <a-card title="通知设置" :bordered="false">
      <a-form
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-divider>通知接收方式</a-divider>
        
        <a-form-item label="系统通知" name="system">
          <a-switch v-model:checked="formState.system.enabled" />
          <div class="item-description">接收系统弹窗提醒</div>
        </a-form-item>
        
        <a-form-item label="电子邮件" name="email">
          <a-switch v-model:checked="formState.email.enabled" />
          <div class="item-description">接收电子邮件通知</div>
          
          <div v-if="formState.email.enabled" class="sub-form">
            <a-input
              v-model:value="formState.email.address"
              placeholder="请输入接收通知的邮箱地址"
              style="margin-top: 8px;"
            />
          </div>
        </a-form-item>
        
        <a-form-item label="短信通知" name="sms">
          <a-switch v-model:checked="formState.sms.enabled" />
          <div class="item-description">接收手机短信通知</div>
          
          <div v-if="formState.sms.enabled" class="sub-form">
            <a-input
              v-model:value="formState.sms.phoneNumber"
              placeholder="请输入接收通知的手机号码"
              style="margin-top: 8px;"
            />
          </div>
        </a-form-item>
        
        <a-form-item label="OA系统" name="oa">
          <a-switch v-model:checked="formState.oa.enabled" />
          <div class="item-description">转发通知到OA系统</div>
        </a-form-item>
        
        <a-divider>通知类型设置</a-divider>
        
        <a-form-item label="绩效评分活动" name="performance">
          <a-checkbox-group v-model:value="formState.notificationTypes.performance">
            <a-checkbox value="start">评分开始提醒</a-checkbox>
            <a-checkbox value="reminder">填报临期提醒</a-checkbox>
            <a-checkbox value="end">评分结束提醒</a-checkbox>
            <a-checkbox value="result">成绩发布提醒</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="数据收集活动" name="dataCollection">
          <a-checkbox-group v-model:value="formState.notificationTypes.dataCollection">
            <a-checkbox value="start">收集开始提醒</a-checkbox>
            <a-checkbox value="reminder">填报临期提醒</a-checkbox>
            <a-checkbox value="end">收集结束提醒</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="系统公告" name="announcement">
          <a-checkbox-group v-model:value="formState.notificationTypes.announcement">
            <a-checkbox value="important">重要公告</a-checkbox>
            <a-checkbox value="normal">一般公告</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-divider>提醒频率设置</a-divider>
        
        <a-form-item label="临期提醒时间" name="reminderTime">
          <a-select
            v-model:value="formState.reminderTime"
            style="width: 200px"
          >
            <a-select-option value="1">截止前1天</a-select-option>
            <a-select-option value="3">截止前3天</a-select-option>
            <a-select-option value="5">截止前5天</a-select-option>
            <a-select-option value="7">截止前7天</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="消息提醒方式" name="reminderMode">
          <a-radio-group v-model:value="formState.reminderMode">
            <a-radio value="once">只提醒一次</a-radio>
            <a-radio value="daily">每天提醒</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item :wrapper-col="{ offset: 6, span: 14 }">
          <a-button type="primary" @click="saveSettings">保存设置</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  getUserNotificationSettings,
  updateUserNotificationSettings
} from '@/api/modules/api.notifications'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 表单数据
const formState = reactive({
  system: {
    enabled: true
  },
  email: {
    enabled: false,
    address: ''
  },
  sms: {
    enabled: false,
    phoneNumber: ''
  },
  oa: {
    enabled: false
  },
  notificationTypes: {
    performance: ['start', 'reminder', 'end', 'result'],
    dataCollection: ['start', 'reminder', 'end'],
    announcement: ['important', 'normal']
  },
  reminderTime: '3',
  reminderMode: 'once'
})

// 加载用户通知设置
const loadSettings = async () => {
  try {
    const userId = authStore.userInfo?.id
    if (!userId) {
      message.error('用户信息获取失败')
      return
    }

    const response = await getUserNotificationSettings(userId)
    if (response.status === 1 && response.data) {
      // 合并设置数据
      Object.assign(formState, response.data)
    }
  } catch (error) {
    console.error('加载通知设置失败:', error)
    // 如果是404错误，说明用户还没有设置，使用默认值
    if (error.response?.status !== 404) {
      message.error('加载通知设置失败')
    }
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    const userId = authStore.userInfo?.id
    if (!userId) {
      message.error('用户信息获取失败')
      return
    }

    const response = await updateUserNotificationSettings(userId, formState)
    if (response.status === 1) {
      message.success('设置已保存')
    } else {
      message.error(response.message || '保存设置失败')
    }
  } catch (error) {
    console.error('保存通知设置失败:', error)
    message.error('保存设置失败，请重试')
  }
}

// 初始化加载设置
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.notification-settings {
  padding: 24px;
}

.item-description {
  color: #666;
  font-size: 12px;
  margin-top: 4px;
}

.sub-form {
  margin-top: 8px;
  padding-left: 22px;
}
</style> 