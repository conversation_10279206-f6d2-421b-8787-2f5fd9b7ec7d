const express = require('express');
const notificationTemplatesController = require('../../../controllers/v1/sys/notificationTemplatesController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

const router = express.Router();

// 创建通知模板权限中间件函数
const notificationTemplatesPermission = (action) => createModulePermission('notificationTemplates', action);

/**
 * 获取通知模板列表
 * @route GET /v1/sys/notifications/templates
 * @group 通知模板管理 - 通知模板相关接口
 * @param {number} page.query - 页码
 * @param {number} pageSize.query - 每页条数
 * @param {string} type.query - 模板类型
 * @returns {object} 200 - 成功返回模板列表
 * @security JWT
 */
router.get('/',
    authMiddleware,
    notificationTemplatesPermission('list'),
    notificationTemplatesController.getNotificationTemplates
);

/**
 * 获取通知模板详情
 * @route GET /v1/sys/notifications/templates/:id
 * @group 通知模板管理 - 通知模板相关接口
 * @param {string} id.path - 模板ID
 * @returns {object} 200 - 成功返回模板详情
 * @security JWT
 */
router.get('/:id',
    authMiddleware,
    notificationTemplatesPermission('detail'),
    notificationTemplatesController.getNotificationTemplate
);

/**
 * 创建通知模板
 * @route POST /v1/sys/notifications/templates
 * @group 通知模板管理 - 通知模板相关接口
 * @param {string} name.body - 模板名称
 * @param {string} type.body - 通知类型
 * @param {string} title.body - 模板标题
 * @param {string} content.body - 模板内容
 * @param {string} description.body - 使用说明
 * @returns {object} 200 - 成功创建模板
 * @security JWT
 */
router.post('/',
    authMiddleware,
    notificationTemplatesPermission('create'),
    notificationTemplatesController.createNotificationTemplate
);

/**
 * 更新通知模板
 * @route PUT /v1/sys/notifications/templates/:id
 * @group 通知模板管理 - 通知模板相关接口
 * @param {string} id.path - 模板ID
 * @param {string} name.body - 模板名称
 * @param {string} type.body - 通知类型
 * @param {string} title.body - 模板标题
 * @param {string} content.body - 模板内容
 * @param {string} description.body - 使用说明
 * @returns {object} 200 - 成功更新模板
 * @security JWT
 */
router.put('/:id',
    authMiddleware,
    notificationTemplatesPermission('update'),
    notificationTemplatesController.updateNotificationTemplate
);

/**
 * 删除通知模板
 * @route DELETE /v1/sys/notifications/templates/:id
 * @group 通知模板管理 - 通知模板相关接口
 * @param {string} id.path - 模板ID
 * @returns {object} 200 - 成功删除模板
 * @security JWT
 */
router.delete('/:id',
    authMiddleware,
    notificationTemplatesPermission('delete'),
    notificationTemplatesController.deleteNotificationTemplate
);

module.exports = router;
