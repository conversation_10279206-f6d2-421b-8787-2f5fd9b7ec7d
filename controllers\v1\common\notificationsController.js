const { Op } = require('sequelize');
const notificationsModel = require('@models/v1/mapping/notificationsModel');
const userModel = require('@models/v1/mapping/userModel');
const { getUserInfoFromRequest } = require('@utils/others');

/**
 * 获取通知列表
 * @route GET /v1/sys/notifications
 * @group 通知管理
 * @param {boolean} is_read.query - 是否已读
 * @param {string} user_id.query - 用户ID
 * @returns {object} 200 - 成功返回通知列表
 */
exports.getNotifications = async (req, res) => {
    try {
        const { page = 1, pageSize = 10 } = req.query;
        const currentUser = await getUserInfoFromRequest(req);

        console.log('当前用户信息:', {
            id: currentUser.id,
            departmentId: currentUser.departmentId,
            userLevelId: currentUser.userLevelId,
            roleAuth: currentUser.role.roleAuth
        });

        // 检查用户权限
        const isAdmin = currentUser.role.roleAuth === 'TEACHER-LV1' ||
                       currentUser.role.roleAuth === 'SUPER' ||
                       currentUser.role.roleAuth === 'ADMIN-LV2';

        let where = { status: 1 };

        // 管理员可以查看所有通知，普通用户只能查看发送给自己的通知
        if (!isAdmin) {
            // 普通用户：只能看到发送给自己的通知
            where[Op.or] = [
                // 全员通知
                { sendMode: 'all' },
                // 按部门发送的通知（只有当用户有部门ID时才添加此条件）
                ...(currentUser.departmentId ? [{
                    sendMode: 'department',
                    targetDepartmentId: currentUser.departmentId
                }] : []),
                // 按用户级别发送的通知（只有当用户有级别ID时才添加此条件）
                ...(currentUser.userLevelId ? [{
                    sendMode: 'user_level',
                    targetUserLevelId: currentUser.userLevelId
                }] : []),
                // 单独发送给该用户的通知
                {
                    sendMode: 'single',
                    userId: currentUser.id
                }
            ];
        }
        // 管理员：where 只有 status: 1，可以查看所有通知

        console.log('用户权限:', isAdmin ? '管理员' : '普通用户');
        console.log('查询条件:', JSON.stringify(where, null, 2));

        // 分页参数
        const pageNum = parseInt(page);
        const pageSizeNum = parseInt(pageSize);
        const offset = (pageNum - 1) * pageSizeNum;

        // 执行查询
        const result = await notificationsModel.findAndCountAll({
            where,
            include: [
                {
                    model: userModel,
                    as: 'user',
                    attributes: ['id', 'username', 'nickname'],
                    required: false
                },
                {
                    model: userModel,
                    as: 'initiator',
                    attributes: ['id', 'username', 'nickname'],
                    required: false
                },
                {
                    model: require('@models/v1/mapping/departmentsModel'),
                    as: 'targetDepartment',
                    attributes: ['id', 'departmentName'],
                    required: false
                },
                {
                    model: require('@models/v1/mapping/userLevelsModel'),
                    as: 'targetUserLevel',
                    attributes: ['id', 'levelName'],
                    required: false
                }
            ],
            order: [['createdAt', 'DESC']],
            offset,
            limit: pageSizeNum
        });

        console.log(`查询成功: 共${result.count}条记录`);
        console.log('第一条记录的详细信息:', JSON.stringify(result.rows[0], null, 2));

        return res.status(200).json({
            code: 200,
            message: '获取通知列表成功',
            data: {
                list: result.rows,
                pagination: {
                    total: result.count,
                    page: pageNum,
                    pageSize: pageSizeNum,
                    totalPages: Math.ceil(result.count / pageSizeNum)
                }
            }
        });
    } catch (error) {
        console.error('获取通知列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取通知列表失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 获取通知详情
 * @route GET /v1/sys/notifications/:id
 * @group 通知管理
 * @param {integer} id.path - 通知ID
 * @returns {object} 200 - 成功返回通知详情
 */
exports.getNotificationById = async (req, res) => {
    try {
        const currentUser = await getUserInfoFromRequest(req);

        // 检查用户权限
        const isAdmin = currentUser.role.roleAuth === 'TEACHER-LV1' ||
                       currentUser.role.roleAuth === 'SUPER' ||
                       currentUser.role.roleAuth === 'ADMIN-LV2';

        const notification = await notificationsModel.findByPk(req.params.id, {
            include: [
                {model: userModel, as: 'user', attributes: ['id', 'username', 'nickname']},
                {model: userModel, as: 'initiator', attributes: ['id', 'username', 'nickname']}
            ]
        });

        if (!notification) {
            return res.status(404).json({
                code: 404,
                message: '通知不存在',
                data: null
            });
        }

        // 普通用户需要检查是否有权限查看此通知
        if (!isAdmin) {
            const canView =
                notification.sendMode === 'all' ||
                (notification.sendMode === 'department' && notification.targetDepartmentId === currentUser.departmentId) ||
                (notification.sendMode === 'user_level' && notification.targetUserLevelId === currentUser.userLevelId) ||
                (notification.sendMode === 'single' && notification.userId === currentUser.id);

            if (!canView) {
                return res.status(403).json({
                    code: 403,
                    message: '无权限查看此通知',
                    data: null
                });
            }
        }

        return res.status(200).json({
            code: 200,
            message: '获取通知详情成功',
            data: notification
        });
    } catch (error) {
        console.error('获取通知详情失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取通知详情失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 创建通知（仅管理员可用，支持三种发送方式）
 * @route POST /v1/sys/notifications
 * @group 通知管理
 * @param {string} title.body - 通知标题
 * @param {string} content.body - 通知内容
 * @param {string} abstract.body - 通知摘要
 * @param {string} target_type.body - 发送目标类型（all/department/user_level）
 * @param {string} department.body - 部门名称（按部门发送时使用）
 * @param {string} user_level.body - 用户级别（按用户级别发送时使用）
 * @param {string} initiator_id.body - 发起人ID
 * @returns {object} 200 - 成功创建通知
 */
exports.createNotification = async (req, res) => {
    try {
        console.log('接收到的请求数据:', req.body);

        // 获取当前用户信息
        const userInfo = await getUserInfoFromRequest(req);
        console.log('当前用户信息:', userInfo);

        const { type, title, content, sendMode, targetDepartmentId, targetUserLevelId, userId, ...notificationData } = req.body;

        console.log('解构后的字段:', { type, title, content, sendMode, targetDepartmentId, targetUserLevelId, userId });

        // 验证必要字段
        if (!title || !content || !sendMode) {
            return res.status(400).json({
                code: 400,
                message: '缺少必要字段: title, content, sendMode',
                data: null
            });
        }

        // 验证发送模式和目标字段的一致性
        switch (sendMode) {
            case 'department':
                if (!targetDepartmentId) {
                    return res.status(400).json({
                        code: 400,
                        message: '按部门发送时目标部门ID不能为空',
                        data: null
                    });
                }
                break;

            case 'user_level':
                if (!targetUserLevelId) {
                    return res.status(400).json({
                        code: 400,
                        message: '按用户级别发送时目标用户级别ID不能为空',
                        data: null
                    });
                }
                break;

            case 'single':
                if (!userId) {
                    return res.status(400).json({
                        code: 400,
                        message: '单独发送时目标用户ID不能为空',
                        data: null
                    });
                }
                break;

            case 'all':
                // 全员发送不需要额外验证
                break;

            default:
                return res.status(400).json({
                    code: 400,
                    message: '无效的发送模式，只支持：all、department、user_level、single',
                    data: null
                });
        }

        // 获取接收者数量统计
        let recipientCount = 0;
        const userWhere = { status: 1 };

        switch (sendMode) {
            case 'all':
                // 所有用户
                recipientCount = await userModel.count({ where: userWhere });
                break;
            case 'department':
                userWhere.departmentId = targetDepartmentId;
                recipientCount = await userModel.count({ where: userWhere });
                break;
            case 'user_level':
                userWhere.userLevelId = targetUserLevelId;
                recipientCount = await userModel.count({ where: userWhere });
                break;
            case 'single':
                userWhere.id = userId;
                recipientCount = await userModel.count({ where: userWhere });
                break;
            default:
                return res.status(400).json({
                    code: 400,
                    message: '无效的发送模式',
                    data: null
                });
        }

        if (recipientCount === 0) {
            return res.status(400).json({
                code: 400,
                message: '没有找到有效的接收者',
                data: null
            });
        }

        // 创建单条通知记录
        const notificationCreateData = {
            title,
            content,
            sendMode,
            targetDepartmentId,
            targetUserLevelId,
            userId: sendMode === 'single' ? userId : null,
            initiatorId: userInfo.id, // 使用真实的用户ID
            status: 1,
            ...notificationData // 其他额外字段
        };

        // 如果有 type 字段，则添加到创建数据中
        if (type) {
            notificationCreateData.type = type;
        }

        console.log('创建通知数据:', notificationCreateData);

        const notification = await notificationsModel.create(notificationCreateData);

        return res.status(200).json({
            code: 200,
            message: `成功创建通知，将发送给 ${recipientCount} 个用户`,
            data: {
                notification,
                recipientCount
            }
        });
    } catch (error) {
        console.error('创建通知失败:', error);
        return res.status(500).json({
            code: 500,
            message: '创建通知失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 更新通知
 * @route PUT /v1/sys/notifications/:id
 * @group 通知管理
 * @param {integer} id.path - 通知ID
 * @param {boolean} is_read.body - 是否已读
 * @returns {object} 200 - 成功更新通知
 */
exports.updateNotification = async (req, res) => {
    try {
        const { id } = req.params;

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: '通知ID不能为空',
                data: null
            });
        }

        const notification = await notificationsModel.findByPk(id);
        if (!notification) {
            return res.status(404).json({
                code: 404,
                message: '通知不存在',
                data: null
            });
        }

        await notification.update(req.body);

        return res.status(200).json({
            code: 200,
            message: '更新通知成功',
            data: notification
        });
    } catch (error) {
        console.error('更新通知失败:', error);
        return res.status(500).json({
            code: 500,
            message: '更新通知失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 删除通知
 * @route DELETE /v1/sys/notifications/:id
 * @group 通知管理
 * @param {integer} id.path - 通知ID
 * @returns {object} 200 - 成功删除通知
 */
exports.deleteNotification = async (req, res) => {
    try {
        const { id } = req.params;

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: '通知ID不能为空',
                data: null
            });
        }

        const notification = await notificationsModel.findByPk(id);
        if (!notification) {
            return res.status(404).json({
                code: 404,
                message: '通知不存在',
                data: null
            });
        }

        await notification.destroy();

        return res.status(200).json({
            code: 200,
            message: '删除通知成功',
            data: null
        });
    } catch (error) {
        console.error('删除通知失败:', error);
        return res.status(500).json({
            code: 500,
            message: '删除通知失败: ' + error.message,
            data: null
        });
    }
};



/**
 * 获取部门列表
 * @route GET /v1/sys/notifications/departments
 * @group 通知管理
 * @returns {object} 200 - 成功返回部门列表
 */
exports.getDepartments = async (_req, res) => {
    try {
        // 引入部门模型
        const departmentsModel = require('@models/v1/mapping/departmentsModel');

        const departments = await departmentsModel.findAll({
            where: { status: 1 },
            attributes: ['id', 'departmentName', 'departmentCode'],
            order: [['sort', 'ASC'], ['departmentName', 'ASC']]
        });

        return res.status(200).json({
            code: 200,
            message: '获取部门列表成功',
            data: departments
        });
    } catch (error) {
        console.error('获取部门列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取部门列表失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 获取用户级别列表
 * @route GET /v1/sys/notifications/user-levels
 * @group 通知管理
 * @returns {object} 200 - 成功返回用户级别列表
 */
exports.getUserLevels = async (_req, res) => {
    try {
        // 引入用户级别模型
        const userLevelsModel = require('@models/v1/mapping/userLevelsModel');

        const userLevels = await userLevelsModel.findAll({
            where: { status: 1 },
            attributes: ['id', 'levelName', 'description'],
            order: [['sort', 'ASC'], ['createdAt', 'ASC']]
        });

        return res.status(200).json({
            code: 200,
            message: '获取用户级别列表成功',
            data: userLevels
        });
    } catch (error) {
        console.error('获取用户级别列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取用户级别列表失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 批量标记为已读（暂时简化实现，后续需要创建用户通知读取状态表）
 * @route PUT /v1/sys/notifications/batch/read
 * @group 通知管理
 * @param {array} notification_ids.body - 通知ID数组
 * @returns {object} 200 - 成功批量标记为已读
 */
exports.batchMarkAsRead = async (req, res) => {
    try {
        const { notification_ids } = req.body;

        if (!notification_ids || !Array.isArray(notification_ids) || notification_ids.length === 0) {
            return res.status(400).json({
                code: 400,
                message: '通知ID数组不能为空',
                data: null
            });
        }

        // 注意：这里需要创建用户通知读取状态表来跟踪用户的阅读状态
        // 暂时返回成功，实际应该在用户通知读取状态表中记录
        return res.status(200).json({
            code: 200,
            message: '标记操作已记录',
            data: {
                updatedCount: notification_ids.length,
                message: '需要创建用户通知读取状态表来完整实现此功能'
            }
        });
    } catch (error) {
        console.error('批量标记已读失败:', error);
        return res.status(500).json({
            code: 500,
            message: '批量标记已读失败: ' + error.message,
            data: null
        });
    }
};