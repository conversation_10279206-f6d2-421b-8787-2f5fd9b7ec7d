const express = require('express');
const router = express.Router();
const academicAppointmentsController = require('../../../controllers/v1/academicAppointments/academicAppointmentsController');
const multer = require('multer');
const path = require('path');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建学术任职权限中间件函数
const appointmentsPermission = (action) => createModulePermission('academicAppointments', action);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/academicAppointments/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'academicAppointments-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

/**
 * 获取学术任职列表
 * @route POST /v1/sys/academic-appointments/list
 * @group 学术任职管理 - 学术任职相关接口
 * @param {string} associationName - 协会/期刊名称（模糊搜索）
 * @param {string} levelId - 级别ID
 * @param {string} startYear - 起始年份
 * @param {string} endYear - 结束年份
 * @param {string} userId - 用户ID（可选，如果提供则获取特定用户的学术任职）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 统计范围筛选，可选值：'in'(范围内), 'out'(范围外), 'all'(全部)，默认'all'
 * @param {string} reviewStatus - 审核状态筛选，可选值：'all'(全部), 'reviewed'(已审核), 'pending'(待审核), 'rejected'(已拒绝)，默认'all'
 * @param {string} sortField - 排序字段，默认createdAt
 * @param {string} sortOrder - 排序方式，默认desc
 * @param {string} query - 关键词搜索
 * @param {boolean} isExport - 是否导出，如果为true则不分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, page: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', 
  authMiddleware, 
  appointmentsPermission('list'), 
  academicAppointmentsController.getAppointments
);

/**
 * 导入学术任职数据
 * @route POST /v1/sys/academic-appointments/projects/import
 * @group 学术任职管理 - 学术任职相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects/import', 
  authMiddleware, 
  appointmentsPermission('import'), 
  upload.single('file'), 
  academicAppointmentsController.importAppointments
);

/**
 * 导出学术任职数据
 * @route POST /v1/sys/academic-appointments/projects/export
 * @group 学术任职管理 - 学术任职相关接口
 * @param {string} associationName - 协会/期刊名称（模糊搜索）
 * @param {string} levelId - 级别ID
 * @param {string} startYear - 起始年份
 * @param {string} endYear - 结束年份
 * @param {string} userId - 用户ID（可选）
 * @param {string} range - 范围筛选，可选值：all, in, out
 * @param {string} reviewStatus - 审核状态筛选
 * @param {string} fileName - 导出文件名
 * @returns {object} 200 - {code: 200, message: "导出成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects/export', 
  authMiddleware, 
  appointmentsPermission('export'), 
  academicAppointmentsController.exportAppointments
);

/**
 * 创建学术任职
 * @route POST /v1/sys/academic-appointments/project/create
 * @group 学术任职管理 - 学术任职相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} associationName.body.required - 协会/期刊名称
 * @param {string} position.body.required - 职务名称
 * @param {string} levelId.body.required - 级别ID
 * @param {string} startYear.body.required - 起始年份
 * @param {string} endYear.body - 结束年份（不填为至今）
 * @param {string} remark.body - 备注
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/create', 
  authMiddleware, 
  appointmentsPermission('create'), 
  upload.array('files', 5), 
  academicAppointmentsController.createAppointment
);

/**
 * 更新学术任职
 * @route POST /v1/sys/academic-appointments/project/update
 * @group 学术任职管理 - 学术任职相关接口
 * @param {string} id.body.required - 学术任职ID
 * @param {string} associationName.body - 协会/期刊名称
 * @param {string} position.body - 职务名称
 * @param {string} levelId.body - 级别ID
 * @param {string} startYear.body - 起始年份
 * @param {string} endYear.body - 结束年份
 * @param {string} remark.body - 备注
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/update', 
  authMiddleware, 
  appointmentsPermission('update'), 
  upload.array('files', 5), 
  async (req, res) => {
    const { id, ...updateData } = req.body;
    req.params = { id };
    req.body = updateData;
    await academicAppointmentsController.updateAppointment(req, res);
  }
);

/**
 * 删除学术任职
 * @route POST /v1/sys/academic-appointments/project/delete
 * @group 学术任职管理 - 学术任职相关接口
 * @param {string} id.body.required - 学术任职ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/delete', 
  authMiddleware, 
  appointmentsPermission('delete'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await academicAppointmentsController.deleteAppointment(req, res);
  }
);

/**
 * 获取学术任职详情
 * @route POST /v1/sys/academic-appointments/project/detail
 * @group 学术任职管理 - 学术任职相关接口
 * @param {string} id.body.required - 学术任职ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/detail', 
  authMiddleware, 
  appointmentsPermission('detail'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await academicAppointmentsController.getAppointmentDetail(req, res);
  }
);

/**
 * 获取学术任职时间分布数据
 * @route POST /v1/sys/academic-appointments/statistics/time-distribution
 * @group 学术任职统计 - 学术任职统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的学术任职
 * @param {string} reviewStatus.body - 审核状态: 'all', 'rejected', 'pending', 'reviewed'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {months: ["YYYY-MM",...], data: [数量,...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/time-distribution', 
  authMiddleware, 
  appointmentsPermission('timeDistribution'), 
  academicAppointmentsController.getAppointmentYearlyTrend
);

/**
 * 审核学术任职
 * @route POST /v1/sys/academic-appointments/project/review
 * @group 学术任职管理 - 学术任职相关接口
 * @param {string} id.body.required - 学术任职ID
 * @param {boolean} reviewStatus.body.required - 审核状态
 * @param {string} reviewComment.body - 审核意见
 * @param {string} reviewer.body.required - 审核人ID
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/review', 
  authMiddleware, 
  appointmentsPermission('review'), 
  academicAppointmentsController.reviewAppointment
);

/**
 * 获取学术任职级别分布数据
 * @route POST /v1/sys/academic-appointments/statistics/level-distribution
 * @group 学术任职统计 - 学术任职级别分布统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} userId - 用户ID，可选
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{levelName: '级别名称', count: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/level-distribution', 
  authMiddleware, 
  appointmentsPermission('levelDistribution'), 
  academicAppointmentsController.getAppointmentLevelDistribution
);

/**
 * 获取教师学术任职排名数据
 * @route POST /v1/sys/academic-appointments/statistics/teacher-ranking
 * @group 学术任职统计 - 教师学术任职排名统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} reviewStatus - 审核状态: 'all', 'rejected', 'pending', 'reviewed'
 * @param {number} page - 页码
 * @param {number} pageSize - 每页记录数
 * @param {boolean} isExport - 是否导出所有数据，导出时不应用分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{userId, userName, studentNumber, totalAppointments, totalScore}], pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-ranking', 
  authMiddleware, 
  appointmentsPermission('teacherRanking'), 
  academicAppointmentsController.getUserActiveAppointmentsRanking
);

/**
 * 获取教师学术任职详情
 * @route POST /v1/sys/academic-appointments/statistics/teacher-project-details
 * @group 学术任职统计 - 教师学术任职详情
 * @param {string} userId - 用户ID
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 数据范围
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], totalScore: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-project-details', 
  authMiddleware, 
  appointmentsPermission('teacherAppointmentDetails'), 
  academicAppointmentsController.getUserAppointmentDetails
);

/**
 * 获取学术任职统计概览数据
 * @route POST /v1/sys/academic-appointments/statistics/overview
 * @group 学术任职统计 - 学术任职统计概览
 * @param {string} userId - 用户ID，可选
 * @param {string} range - 数据范围，可选值：'in', 'out', 'all'
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalAppointments, activeAppointments, averageScore, reviewCompletionRate}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/overview', 
  authMiddleware, 
  appointmentsPermission('overview'), 
  academicAppointmentsController.getStatistics
);

/**
 * 获取审核状态概览
 * @route POST /v1/sys/academic-appointments/statistics/review-status-overview
 * @group 学术任职统计 - 审核状态概览
 * @param {string} userId - 用户ID，可选
 * @param {string} range - 数据范围，可选值：'in', 'out', 'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {rejected, pending, reviewed, total, reviewedRate}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/review-status-overview', 
  authMiddleware, 
  appointmentsPermission('reviewStatusOverview'), 
  academicAppointmentsController.getReviewStatusOverview
);

/**
 * 获取学术任职总分统计
 * @route POST /v1/sys/academic-appointments/statistics/appointments-total-score
 * @group 学术任职统计 - 学术任职分数统计相关接口
 * @param {string} range - 数据范围: 'in' (统计范围内), 'out' (统计范围外), 'all' (全部，默认)
 * @param {string} reviewStatus - 审核状态: 'rejected' (已拒绝), 'pending' (待审核), 'reviewed' (已审核), 'all' (全部，默认)
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {levelStats: [{levelId, levelName, count, totalScore}], overallStats: {totalAppointments, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/appointments-total-score', 
  authMiddleware, 
  appointmentsPermission('totalScore'), 
  academicAppointmentsController.getAppointmentsTotalScore
);

/**
 * 获取用户学术任职详情
 * @route POST /v1/sys/academic-appointments/user/appointment-details
 * @group 学术任职统计 - 用户学术任职详情相关接口
 * @param {string} userId.required - 用户ID
 * @param {string} range - 数据范围: 'in' (统计范围内), 'out' (统计范围外), 'all' (全部，默认)
 * @param {string} reviewStatus - 审核状态: 'rejected' (已拒绝), 'pending' (待审核), 'reviewed' (已审核), 'all' (全部，默认)
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{任职详情}], totalCount: 总记录数, statistics: {totalAppointments, totalScore, currentAppointments, approvedAppointments, activeAppointments}, timeInterval: {startTime, endTime, name}, pagination: {page, pageSize, total}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/appointment-details', 
  authMiddleware, 
  appointmentsPermission('userDetails'), 
  academicAppointmentsController.getUserAppointmentsDetail
);

/**
 * 重新提交学术任命审核
 * @route POST /v1/sys/academic-appointments/reapply
 * @group 学术任命管理 - 学术任命相关接口
 * @param {string} id.body.required - 记录ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/reapply', 
  authMiddleware, 
  appointmentsPermission('reapply'),
  academicAppointmentsController.reapply
);

module.exports = router; 