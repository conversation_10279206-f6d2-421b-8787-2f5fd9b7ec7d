const { ValidationError } = require('../utils/errors');

/**
 * 请求验证中间件
 * @param {Object} schema - Joi验证模式
 */
const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      next(new ValidationError('请求参数验证失败', errors));
    } else {
      next();
    }
  };
};

module.exports = {
  validateRequest
}; 