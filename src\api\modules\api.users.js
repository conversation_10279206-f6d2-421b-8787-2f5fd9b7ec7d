import service from '../server'

export const usersList = (data) => {
    return service.post('/user/list', data)
}
export const usersCreate = (data) => {
    return service.post('/user/create', data)
}
export const usersUpdate = (data) => {
    return service.post('/user/update', data)
}
export const usersDelete = (data) => {
    return service.post('/user/delete', data)
}
// 重置密码
export const usersReset = (data) => {
    return service.post('/user/reset', data)
}
// 获取用户信息
export const usersFindOne = (data) => {
    return service.post('/user/findOne', data)
}
// 搜索用户
export const usersSearch = (data) => {
    return service.post('/user/search', data)
}

// 获取用户级别列表
export const getUserLevels = () => {
    return service.get('/user/user-levels')
}

// 获取部门列表
export const getDepartments = () => {
    return service.get('/user/departments')
}

// 获取用户职称记录
export const getUserLevelRecords = (data) => {
    return service.post('/user/level-records', data)
}

// 保存用户职称记录
export const saveUserLevelRecords = (data) => {
    return service.post('/user/save-level-records', data)
}

