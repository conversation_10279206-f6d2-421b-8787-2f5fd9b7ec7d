/**
 * 用户个人通知路由
 */

const express = require('express');
const router = express.Router();
const userNotificationController = require('../../../controllers/v1/common/userNotificationController');

// 获取用户个人通知列表
router.get('/user-notifications', userNotificationController.getUserNotifications);

// 标记通知为已读
router.post('/mark-read', userNotificationController.markNotificationAsRead);

// 获取未读通知数量
router.get('/unread-count', userNotificationController.getUnreadNotificationCount);

// 获取通知详情
router.get('/detail/:id', userNotificationController.getNotificationDetail);

// 批量标记通知为已读
router.post('/batch-read', userNotificationController.batchMarkAsRead);

module.exports = router;
