const express = require('express');
const router = express.Router();
const socialServiceController = require('../../../controllers/v1/sys/socialServiceController');
const multer = require('multer');
const path = require('path');
const { downloadFile } = require('../../../utils/files');

// 配置 multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../../uploads'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

/**
 * 获取社会服务列表
 * @route GET /v1/sys/social-service/list
 * @group 社会服务管理 - 社会服务相关接口
 * @param {string} name.query - 服务名称（模糊搜索）
 * @param {string} type.query - 服务类型
 * @param {string} target.query - 服务对象（模糊搜索）
 * @param {string} teacher.query - 参与教师（模糊搜索）
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @param {number} status.query - 服务状态（1:正常, 0:已删除）
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @param {string} userId.query - 用户ID（筛选个人服务）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 0, page: 1, pageSize: 10, totalPages: 0, list: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list', socialServiceController.getServices);

/**
 * 导入社会服务数据
 * @route POST /v1/sys/social-service/import
 * @group 社会服务管理 - 社会服务相关接口
 * @param {file} file.body.required - Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', upload.single('file'), socialServiceController.importServices);

/**
 * 导出社会服务数据
 * @route GET /v1/sys/social-service/export
 * @group 社会服务管理 - 社会服务相关接口
 * @param {string} name.query - 服务名称（模糊搜索）
 * @param {string} type.query - 服务类型
 * @param {string} target.query - 服务对象（模糊搜索）
 * @param {string} teacher.query - 参与教师（模糊搜索）
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @param {number} status.query - 服务状态（1:正常, 0:已删除）
 * @param {string} userId.query - 用户ID（筛选个人服务）
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/export', socialServiceController.exportServices);

/**
 * 获取服务类型分布
 * @route GET /v1/sys/social-service/stats/type-distribution
 * @group 社会服务管理 - 社会服务统计接口
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @param {string} userId.query - 用户ID（筛选个人服务）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{type: "会议", count: 10}]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/type-distribution', socialServiceController.getServiceTypeDistribution);

/**
 * 获取学术会议参与分布
 * @route GET /v1/sys/social-service/stats/conference-distribution
 * @group 社会服务管理 - 社会服务统计接口
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @param {string} userId.query - 用户ID（筛选个人服务）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {internationalOral: 5, internationalPoster: 3,...}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/conference-distribution', socialServiceController.getConferenceDistribution);

/**
 * 获取学生竞赛指导分布
 * @route GET /v1/sys/social-service/stats/competition-distribution
 * @group 社会服务管理 - 社会服务统计接口
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @param {string} userId.query - 用户ID（筛选个人服务）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{level: "国家级", count: 5},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/competition-distribution', socialServiceController.getCompetitionDistribution);

/**
 * 获取教师得分排名
 * @route GET /v1/sys/social-service/stats/teacher-ranking
 * @group 社会服务管理 - 社会服务统计接口
 * @param {string} startDate.query - 开始时间
 * @param {string} endDate.query - 结束时间
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{teacherName: "张三", score: 25.5},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/stats/teacher-ranking', socialServiceController.getTeacherRanking);

/**
 * 创建社会服务
 * @route POST /v1/sys/social-service
 * @group 社会服务管理 - 社会服务相关接口
 * @param {string} name.body.required - 服务名称
 * @param {string} type.body.required - 服务类型
 * @param {string} target.body.required - 服务对象
 * @param {string} teacher.body.required - 参与教师
 * @param {string} startDate.body.required - 开始时间
 * @param {string} endDate.body.required - 结束时间
 * @param {string} content.body - 服务内容（可选，文本类型）
 * @param {string} result.body - 服务成果
 * @param {number} score.body - 得分
 * @param {number} status.body - 服务状态（1:正常, 0:已删除）
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {服务详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/', socialServiceController.createService);

/**
 * 获取社会服务详情
 * @route GET /v1/sys/social-service/:id
 * @group 社会服务管理 - 社会服务相关接口
 * @param {string} id.path.required - 服务ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {服务详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/:id', socialServiceController.getServiceById);

/**
 * 更新社会服务
 * @route PUT /v1/sys/social-service/:id
 * @group 社会服务管理 - 社会服务相关接口
 * @param {string} id.path.required - 服务ID
 * @param {string} name.body.required - 服务名称
 * @param {string} type.body.required - 服务类型
 * @param {string} target.body.required - 服务对象
 * @param {string} teacher.body.required - 参与教师
 * @param {string} startDate.body.required - 开始时间
 * @param {string} endDate.body.required - 结束时间
 * @param {string} content.body - 服务内容（可选，文本类型）
 * @param {string} result.body - 服务成果
 * @param {number} score.body - 得分
 * @param {number} status.body - 服务状态（1:正常, 0:已删除）
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {服务详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/:id', socialServiceController.updateService);

/**
 * 删除社会服务
 * @route DELETE /v1/sys/social-service/:id
 * @group 社会服务管理 - 社会服务相关接口
 * @param {string} id.path.required - 服务ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/:id', socialServiceController.deleteService);

module.exports = router; 