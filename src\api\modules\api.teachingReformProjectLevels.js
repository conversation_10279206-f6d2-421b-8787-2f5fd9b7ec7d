import request from '../server'

// 获取项目级别列表
export function getProjectLevels() {
  return request.get('/teachingReformProjectLevels/levels')
}

// 获取级别及其项目数量
export function getLevelsWithCount() {
  return request.get('/teachingReformProjectLevels/levels-with-count')
}

// 获取项目级别详情
export function getProjectLevelDetail(id) {
  return request.get(`/teachingReformProjectLevels/level/${id}`)
}

// 创建项目级别
export function createProjectLevel(data) {
  return request.post('/teachingReformProjectLevels/level/create', data || {})
}

// 更新项目级别
export function updateProjectLevel(id, data) {
  return request.post('/teachingReformProjectLevels/level/update', { id, ...data })
}

// 删除项目级别
export function deleteProjectLevel(id) {
  return request.post('/teachingReformProjectLevels/level/delete', { id })
}

/**
 * 获取项目级别分布数据
 * @param {Object} data - 请求参数
 * @param {String} data.range - 数据范围: 'in', 'out', 'all'
 * @param {String} data.userId - 用户ID，可选，用于过滤特定用户的数据
 * @returns {Promise}
 */
export function getLevelDistribution(data) {
  return request.post('/teachingReformProjectLevels/statistics/distribution', data)
}

/**
 * 获取指定级别下的项目列表
 * @param {String} levelId - 级别ID
 * @returns {Promise}
 */
export function getProjectsByLevel(levelId) {
  return request.get(`/teachingReformProjectLevels/level/${levelId}/projects`)
}