const { Op } = require('sequelize');
const awardsRulesModel = require('../../../models/v1/mapping/awardsRulesModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取奖项规则列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardsRules = async (req, res) => {
  try {
    console.log('🔍 获取奖项规则列表 - 请求参数:', req.query);
    
    // 获取查询参数
    const { page = 1, pageSize = 10, awardLevel } = req.query;
    
    // 确保 page 和 pageSize 是有效的数字，否则使用默认值
    const pageNum = page ? parseInt(page) : 1;
    const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

    // 如果 page 或 pageSize 是无效数字，返回默认值
    const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
    const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;
    
    console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

    // 构建查询条件
    const where = {};
    if (awardLevel) {
      where.awardLevel = { [Op.like]: `%${awardLevel}%` };
    }
    
    console.log('🔍 最终查询条件:', JSON.stringify(where));

    // 分页查询
    const offset = (validPage - 1) * validPageSize;
    try {
      console.log('📚 执行数据库查询...');
      const { count, rows } = await awardsRulesModel.findAndCountAll({
        where,
        offset,
        limit: validPageSize,
        order: [['createdAt', 'DESC']] // 默认按时间倒序
      });
      
      console.log(`✅ 查询成功: 共${count}条记录`);
      
      const totalPages = Math.ceil(count / validPageSize);
      
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          total: count,
          page: validPage,
          pageSize: validPageSize,
          totalPages,
          list: rows
        }
      });
    } catch (dbError) {
      console.error('❌ 数据库查询失败:', dbError);
      return res.status(500).json({
        code: 500,
        message: `数据库查询失败: ${dbError.message}`,
        data: null
      });
    }
  } catch (error) {
    console.error('❌ 获取奖项规则列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取奖项规则列表失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取奖项规则详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardsRuleDetail = async (req, res) => {
  try {
    const { id } = req.query;
    console.log('🔍 获取奖项规则详情 - ID:', id);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    const rule = await awardsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }
    
    console.log(`✅ 查询成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 获取奖项规则详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取奖项规则详情失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 创建奖项规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAwardsRule = async (req, res) => {
  try {
    const { awardLevel, score } = req.body;
    console.log('📝 创建奖项规则 - 请求数据:', { awardLevel, score });
    
    // 校验必填参数
    if (!awardLevel || score === undefined) {
      return res.status(400).json({
        code: 400,
        message: '奖项级别和基础分数不能为空',
        data: null
      });
    }
    
    // 检查奖项级别是否已存在
    const existingRule = await awardsRulesModel.findOne({
      where: { awardLevel }
    });

    if (existingRule) {
      return res.status(400).json({
        code: 400,
        message: '该奖项级别已存在',
        data: null
      });
    }
    
    // 创建规则
    const rule = await awardsRulesModel.create({
      id: uuidv4(),
      awardLevel,
      score,
      createdBy: req.user.id
    });
    
    console.log(`✅ 创建成功: 规则ID ${rule.id}`);
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 创建奖项规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `创建奖项规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 更新奖项规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateAwardsRule = async (req, res) => {
  try {
    const { id, awardLevel, score } = req.body;
    console.log('📝 更新奖项规则 - 请求数据:', { id, awardLevel, score });

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    // 查找规则
    const rule = await awardsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }

    // 如果更新了奖项级别，检查是否与其他记录重复
    if (awardLevel && awardLevel !== rule.awardLevel) {
      const existingRule = await awardsRulesModel.findOne({
        where: {
          id: { [Op.ne]: id },
          awardLevel
        }
      });

      if (existingRule) {
        return res.status(400).json({
          code: 400,
          message: '该奖项级别已存在',
          data: null
        });
      }
    }

    // 更新规则
    await rule.update({
      awardLevel: awardLevel || rule.awardLevel,
      score: score !== undefined ? score : rule.score
    });
    
    console.log(`✅ 更新成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: rule
    });
  } catch (error) {
    console.error('❌ 更新奖项规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `更新奖项规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 删除奖项规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAwardsRule = async (req, res) => {
  try {
    const { id } = req.query;
    console.log('🗑️ 删除奖项规则 - ID:', id);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: 'ID不能为空',
        data: null
      });
    }

    const rule = await awardsRulesModel.findByPk(id);
    if (!rule) {
      return res.status(404).json({
        code: 404,
        message: '未找到该规则',
        data: null
      });
    }

    await rule.destroy();
    
    console.log(`✅ 删除成功: 规则ID ${id}`);
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('❌ 删除奖项规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `删除奖项规则失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 批量删除奖项规则
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteAwardsRules = async (req, res) => {
  try {
    const { ids } = req.body;
    console.log('🗑️ 批量删除奖项规则 - IDs:', ids);

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: 'ID列表不能为空',
        data: null
      });
    }

    await awardsRulesModel.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });
    
    console.log(`✅ 批量删除成功: 共${ids.length}条记录`);
    
    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: null
    });
  } catch (error) {
    console.error('❌ 批量删除奖项规则失败:', error);
    return res.status(500).json({
      code: 500,
      message: `批量删除奖项规则失败: ${error.message}`,
      data: null
    });
  }
}; 