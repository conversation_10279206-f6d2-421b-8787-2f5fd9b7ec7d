const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const studentProjectGuidanceProjectModel = require('./studentProjectGuidanceProjectsModel');
const userModel = require('./userModel');

// 定义指导学生立项参与者模型
const StudentProjectGuidanceParticipant = sequelize.define('student_project_guidance_participants', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '记录ID'
    },
    projectId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '项目ID'
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '用户ID'
    },
    allocationRatio: {
        type: DataTypes.DECIMAL(4, 2),
        allowNull: false,
        comment: '分配比例（0.00-1.00）'
    },
    isLeader: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 0,
        comment: '是否主持人'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'student_project_guidance_participants',
    timestamps: true,
    indexes: [
        {
            name: 'uk_participant',
            unique: true,
            fields: ['projectId', 'userId']
        },
        {
            name: 'idx_project_participant_project',
            fields: ['projectId']
        },
        {
            name: 'idx_project_participant_user',
            fields: ['userId']
        },
        {
            name: 'idx_project_participant_leader',
            fields: ['isLeader']
        }
    ]
});

// 建立与用户的关联关系
StudentProjectGuidanceParticipant.belongsTo(userModel, {
    foreignKey: 'userId',
    as: 'user'
});

module.exports = StudentProjectGuidanceParticipant; 