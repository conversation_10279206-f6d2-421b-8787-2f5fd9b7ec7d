const express = require('express');
const router = express.Router();
const userRankingController = require('../../../controllers/v1/sys/userRankingController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建用户排名权限中间件函数
const rankingPermission = (action) => createModulePermission('userRanking', action);

// ==================== 基础排名管理接口 ====================

/**
 * 获取总体排名列表
 * @route POST /v1/sys/ranking/all/list
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/all/list',
  authMiddleware,
  rankingPermission('list'),
  userRankingController.getAllRankings
);

/**
 * 获取统计时间内排名列表
 * @route POST /v1/sys/ranking/in/list
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/in/list',
  authMiddleware,
  rankingPermission('list'),
  userRankingController.getInRankings
);

/**
 * 获取统计时间外排名列表
 * @route POST /v1/sys/ranking/out/list
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/out/list',
  authMiddleware,
  rankingPermission('list'),
  userRankingController.getOutRankings
);

/**
 * 获取单个总体排名详情
 * @route POST /v1/sys/ranking/all/detail
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/all/detail',
  authMiddleware,
  rankingPermission('detail'),
  userRankingController.getAllRankingDetail
);

/**
 * 获取单个校内排名详情
 * @route POST /v1/sys/ranking/in/detail
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/in/detail',
  authMiddleware,
  rankingPermission('detail'),
  userRankingController.getInRankingDetail
);

/**
 * 获取单个统计范围排名详情
 * @route POST /v1/sys/ranking/out/detail
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/out/detail',
  authMiddleware,
  rankingPermission('detail'),
  userRankingController.getOutRankingDetail
);

/**
 * 更新总体排名信息
 * @route POST /v1/sys/ranking/all/update
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/all/update',
  authMiddleware,
  rankingPermission('update'),
  userRankingController.updateAllRanking
);

/**
 * 更新统计范围内排名信息
 * @route POST /v1/sys/ranking/in/update
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/in/update',
  authMiddleware,
  rankingPermission('update'),
  userRankingController.updateInRanking
);

/**
 * 更新统计范围外排名信息
 * @route POST /v1/sys/ranking/out/update
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/out/update',
  authMiddleware,
  rankingPermission('update'),
  userRankingController.updateOutRanking
);

/**
 * 手动刷新排名数据
 * @route POST /v1/sys/ranking/refresh
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/refresh',
  authMiddleware,
  rankingPermission('refresh'),
  userRankingController.refreshRankings
);

/**
 * 获取用户排名统计信息
 * @route POST /v1/sys/ranking/stats
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/stats',
  authMiddleware,
  rankingPermission('stats'),
  userRankingController.getRankingStats
);

/**
 * 获取用户排名详情（按用户ID）
 * @route POST /v1/sys/ranking/user
 * @group 用户排名管理 - 用户排名管理接口
 */
router.post('/user',
  authMiddleware,
  rankingPermission('user'),
  userRankingController.getUserRanking
);

// ==================== 绩效分析接口 ====================

/**
 * 获取用户综合绩效雷达图数据
 * @route POST /v1/sys/ranking/radar
 * @group 绩效分析 - 绩效分析相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {雷达图数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/radar',
  authMiddleware,
  rankingPermission('radar'),
  userRankingController.getRadarData
);

/**
 * 获取用户绩效分布图数据
 * @route POST /v1/sys/ranking/distribution
 * @group 绩效分析 - 绩效分析相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {分布图数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/distribution',
  authMiddleware,
  rankingPermission('distribution'),
  userRankingController.getDistributionData
);

/**
 * 获取排名趋势对比数据
 * @route POST /v1/sys/ranking/trend
 * @group 绩效分析 - 绩效分析相关接口
 * @param {string} userId.body.required - 用户ID
 * @returns {object} 200 - {status: "success", message: "Success.", data: {排名趋势数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/trend',
  authMiddleware,
  rankingPermission('trend'),
  userRankingController.getRankingTrend
);

/**
 * 获取部门绩效对比数据
 * @route POST /v1/sys/ranking/department-comparison
 * @group 绩效分析 - 绩效分析相关接口
 * @param {string} range.body - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {部门对比数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/department-comparison',
  authMiddleware,
  rankingPermission('department'),
  userRankingController.getDepartmentComparison
);

/**
 * 获取用户绩效分析报告
 * @route POST /v1/sys/ranking/analysis-report
 * @group 绩效分析 - 绩效分析相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {分析报告数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/analysis-report',
  authMiddleware,
  rankingPermission('report'),
  userRankingController.getAnalysisReport
);

module.exports = router;
