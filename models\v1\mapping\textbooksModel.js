const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const userModel = require('./userModel');
const textbookCategoriesModel = require('./textbookCategoriesModel');

const textbooksModel = sequelize.define('textbooks', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
    comment: '教材与著作 ID'
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    comment: '用户 ID（外键）',
    references: {
      model: 'user',
      key: 'id'
    }
  },
  materialName: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '教材与著作名称'
  },
  publishDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '出版日期'
  },
  categoryId: {
    type: DataTypes.UUID,
    allowNull: false,
    comment: '类别 ID',
    references: {
      model: 'textbook_categories',
      key: 'id'
    }
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  },
  ifReviewer: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    comment: '审核状态（0，拒审核 1，审核，null未审核）'
  },
  attachmentUrl: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '附件URL'
  },
  reviewComment: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核意见'
  },
  reviewerId: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: '审核人 ID'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'textbooks',
  timestamps: true
});

// 建立模型关联
textbooksModel.belongsTo(userModel, { foreignKey: 'userId', as: 'user' });
textbooksModel.belongsTo(textbookCategoriesModel, { foreignKey: 'categoryId', as: 'category' });
// 添加与审核人的关联关系
textbooksModel.belongsTo(userModel, { foreignKey: 'reviewerId', as: 'reviewer' });

module.exports = textbooksModel;