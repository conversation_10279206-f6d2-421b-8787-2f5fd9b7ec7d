// controllers/v1/sys/userRankingController.js
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userModel = require('../../../models/v1/mapping/userModel');
const departmentsModel = require('../../../models/v1/mapping/departmentsModel');
const {body, validationResult} = require('express-validator');
const apiResponse = require('../../../utils/apiResponse');
const {Op, Sequelize} = require('sequelize');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取总体排名列表
 * @route POST /v1/sys/ranking/all/list
 * @group 用户排名管理 - 用户排名管理接口
 * @param {object} query.body - 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'rank', order: 'ascend'}}
 * @returns {object} 200 - {status: "success", message: "Success.", data: {result: [], current: 1, pageSize: 15, total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getAllRankings = [
    async (req, res, next) => {
        try {
            // 从请求中获取分页、排序和查询参数
            let query = req.body;
            let params = query.params || {};
            let current = Number(query.pagination?.current || 1) || 1;
            let pageSize = Number(query.pagination?.pageSize || 15) || 15;
            let sortColumn = query.sort?.columnKey || 'rank';
            let sortOrder = query.sort?.order === 'ascend' ? 'ASC' : 'DESC';

            // 构建查询条件
            let whereConditions = {};
            
            if (params.userId) {
                whereConditions.userId = {[Op.like]: `%${params.userId}%`};
            }
            
            if (params.nickName) {
                whereConditions.nickName = {[Op.like]: `%${params.nickName}%`};
            }
            
            if (params.studentNumber) {
                whereConditions.studentNumber = {[Op.like]: `%${params.studentNumber}%`};
            }
            
            if (params.minScore) {
                whereConditions.totalScore = {
                    ...whereConditions.totalScore,
                    [Op.gte]: params.minScore
                };
            }
            
            if (params.maxScore) {
                whereConditions.totalScore = {
                    ...whereConditions.totalScore,
                    [Op.lte]: params.maxScore
                };
            }

            // 查询数据库获取总记录数
            const totalCount = await userRankingReviewedAllModel.count({
                where: whereConditions
            });
            
            // 查询数据库
            const rankings = await userRankingReviewedAllModel.findAll({
                where: whereConditions,
                order: [[sortColumn, sortOrder]],
                offset: (current - 1) * pageSize,
                limit: pageSize,
            });

            return apiResponse.successResponseWithData(res, "Success.", {
                result: rankings,
                current,
                pageSize,
                total: totalCount
            });
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取统计时间内排名列表
 * @route POST /v1/sys/ranking/in/list
 * @group 用户排名管理 - 用户排名管理接口
 * @param {object} query.body - 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'rank', order: 'ascend'}}
 * @returns {object} 200 - {status: "success", message: "Success.", data: {result: [], current: 1, pageSize: 15, total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getInRankings = [
    async (req, res, next) => {
        try {
            // 从请求中获取分页、排序和查询参数
            let query = req.body;
            let params = query.params || {};
            let current = Number(query.pagination?.current || 1) || 1;
            let pageSize = Number(query.pagination?.pageSize || 15) || 15;
            let sortColumn = query.sort?.columnKey || 'rank';
            let sortOrder = query.sort?.order === 'ascend' ? 'ASC' : 'DESC';

            // 构建查询条件
            let whereConditions = {};
            
            if (params.userId) {
                whereConditions.userId = {[Op.like]: `%${params.userId}%`};
            }
            
            if (params.nickName) {
                whereConditions.nickName = {[Op.like]: `%${params.nickName}%`};
            }
            
            if (params.studentNumber) {
                whereConditions.studentNumber = {[Op.like]: `%${params.studentNumber}%`};
            }
            
            if (params.minScore) {
                whereConditions.totalScore = {
                    ...whereConditions.totalScore,
                    [Op.gte]: params.minScore
                };
            }
            
            if (params.maxScore) {
                whereConditions.totalScore = {
                    ...whereConditions.totalScore,
                    [Op.lte]: params.maxScore
                };
            }

            // 查询数据库获取总记录数
            const totalCount = await userRankingReviewedInModel.count({
                where: whereConditions
            });
            
            // 查询数据库
            const rankings = await userRankingReviewedInModel.findAll({
                where: whereConditions,
                order: [[sortColumn, sortOrder]],
                offset: (current - 1) * pageSize,
                limit: pageSize,
            });

            return apiResponse.successResponseWithData(res, "Success.", {
                result: rankings,
                current,
                pageSize,
                total: totalCount
            });
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取统计时间外排名列表
 * @route POST /v1/sys/ranking/out/list
 * @group 用户排名管理 - 用户排名管理接口
 * @param {object} query.body - 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'rank', order: 'ascend'}}
 * @returns {object} 200 - {status: "success", message: "Success.", data: {result: [], current: 1, pageSize: 15, total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getOutRankings = [
    async (req, res, next) => {
        try {
            // 从请求中获取分页、排序和查询参数
            let query = req.body;
            let params = query.params || {};
            let current = Number(query.pagination?.current || 1) || 1;
            let pageSize = Number(query.pagination?.pageSize || 15) || 15;
            let sortColumn = query.sort?.columnKey || 'rank';
            let sortOrder = query.sort?.order === 'ascend' ? 'ASC' : 'DESC';

            // 构建查询条件
            let whereConditions = {};
            
            if (params.userId) {
                whereConditions.userId = {[Op.like]: `%${params.userId}%`};
            }
            
            if (params.nickName) {
                whereConditions.nickName = {[Op.like]: `%${params.nickName}%`};
            }
            
            if (params.studentNumber) {
                whereConditions.studentNumber = {[Op.like]: `%${params.studentNumber}%`};
            }
            
            if (params.minScore) {
                whereConditions.totalScore = {
                    ...whereConditions.totalScore,
                    [Op.gte]: params.minScore
                };
            }
            
            if (params.maxScore) {
                whereConditions.totalScore = {
                    ...whereConditions.totalScore,
                    [Op.lte]: params.maxScore
                };
            }

            // 查询数据库获取总记录数
            const totalCount = await userRankingReviewedOutModel.count({
                where: whereConditions
            });
            
            // 查询数据库
            const rankings = await userRankingReviewedOutModel.findAll({
                where: whereConditions,
                order: [[sortColumn, sortOrder]],
                offset: (current - 1) * pageSize,
                limit: pageSize,
            });

            return apiResponse.successResponseWithData(res, "Success.", {
                result: rankings,
                current,
                pageSize,
                total: totalCount
            });
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取单个总体排名详情
 * @route POST /v1/sys/ranking/all/detail
 * @group 用户排名管理 - 用户排名管理接口
 * @param {number} id.body.required - 排名ID
 * @returns {object} 200 - {status: "success", message: "Success.", data: {排名信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getAllRankingDetail = [
    body("id").notEmpty().withMessage('排名ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            
            const id = req.body.id;
            const ranking = await userRankingReviewedAllModel.findByPk(id);

            if (!ranking) {
                return apiResponse.notFoundResponse(res, "排名记录不存在.");
            }

            return apiResponse.successResponseWithData(res, "Success.", ranking);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取单个校内排名详情
 * @route POST /v1/sys/ranking/in/detail
 * @group 用户排名管理 - 用户排名管理接口
 * @param {number} id.body.required - 排名ID
 * @returns {object} 200 - {status: "success", message: "Success.", data: {排名信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getInRankingDetail = [
    body("id").notEmpty().withMessage('排名ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            
            const id = req.body.id;
            const ranking = await userRankingReviewedInModel.findByPk(id);

            if (!ranking) {
                return apiResponse.notFoundResponse(res, "排名记录不存在.");
            }

            return apiResponse.successResponseWithData(res, "Success.", ranking);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取单个统计范围排名详情
 * @route POST /v1/sys/ranking/out/detail
 * @group 用户排名管理 - 用户排名管理接口
 * @param {number} id.body.required - 排名ID
 * @returns {object} 200 - {status: "success", message: "Success.", data: {排名信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getOutRankingDetail = [
    body("id").notEmpty().withMessage('排名ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            
            const id = req.body.id;
            const ranking = await userRankingReviewedOutModel.findByPk(id);

            if (!ranking) {
                return apiResponse.notFoundResponse(res, "排名记录不存在.");
            }

            return apiResponse.successResponseWithData(res, "Success.", ranking);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 更新总体排名信息
 * @route POST /v1/sys/ranking/all/update
 * @group 用户排名管理 - 用户排名管理接口
 * @param {number} id.body.required - 排名ID
 * @param {object} updateData.body.required - 更新数据
 * @returns {object} 200 - {status: "success", message: "排名更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.updateAllRanking = [
    body("id").notEmpty().withMessage('排名ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            
            const id = req.body.id;
            const ranking = await userRankingReviewedAllModel.findByPk(id);

            if (!ranking) {
                return apiResponse.notFoundResponse(res, "排名记录不存在.");
            }

            // 更新排名信息
            await ranking.update(req.body);

            return apiResponse.successResponse(res, "排名更新成功.");
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 更新统计范围内排名信息
 * @route POST /v1/sys/ranking/in/update
 * @group 用户排名管理 - 用户排名管理接口
 * @param {number} id.body.required - 排名ID
 * @param {object} updateData.body.required - 更新数据
 * @returns {object} 200 - {status: "success", message: "排名更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.updateInRanking = [
    body("id").notEmpty().withMessage('排名ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            
            const id = req.body.id;
            const ranking = await userRankingReviewedInModel.findByPk(id);

            if (!ranking) {
                return apiResponse.notFoundResponse(res, "排名记录不存在.");
            }

            // 更新排名信息
            await ranking.update(req.body);

            return apiResponse.successResponse(res, "排名更新成功.");
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 更新统计范围外排名信息
 * @route POST /v1/sys/ranking/out/update
 * @group 用户排名管理 - 用户排名管理接口
 * @param {number} id.body.required - 排名ID
 * @param {object} updateData.body.required - 更新数据
 * @returns {object} 200 - {status: "success", message: "排名更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.updateOutRanking = [
    body("id").notEmpty().withMessage('排名ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            
            const id = req.body.id;
            const ranking = await userRankingReviewedOutModel.findByPk(id);

            if (!ranking) {
                return apiResponse.notFoundResponse(res, "排名记录不存在.");
            }

            // 更新排名信息
            await ranking.update(req.body);

            return apiResponse.successResponse(res, "排名更新成功.");
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 手动刷新排名数据
 * @route POST /v1/sys/ranking/refresh
 * @group 用户排名管理 - 用户排名管理接口
 * @param {string} type.body.required - 排名类型 (all/in/out)
 * @returns {object} 200 - {status: "success", message: "排名数据刷新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.refreshRankings = [
    body("type").notEmpty().withMessage('排名类型不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const type = req.body.type;
            let sequelize;

            // 获取Sequelize实例
            if (userRankingReviewedAllModel.sequelize) {
                sequelize = userRankingReviewedAllModel.sequelize;
            } else {
                throw new Error("无法获取数据库连接实例");
            }

            // 调用存储过程来刷新排名数据
            switch (type) {
                case 'all':
                    await sequelize.query('CALL refresh_ranking_all()');
                    break;
                case 'in':
                    await sequelize.query('CALL refresh_ranking_in()');
                    break;
                case 'out':
                    await sequelize.query('CALL refresh_ranking_out()');
                    break;
                default:
                    return apiResponse.validationErrorWithData(res, "参数错误.", "排名类型无效.");
            }

            return apiResponse.successResponse(res, "排名数据刷新成功.");
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取用户排名统计信息
 * @route POST /v1/sys/ranking/stats
 * @group 用户排名管理 - 用户排名管理接口
 * @returns {object} 200 - {status: "success", message: "Success.", data: {统计信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getRankingStats = [
    async (req, res, next) => {
        try {
            // 获取Sequelize实例
            let sequelize;
            if (userRankingReviewedAllModel.sequelize) {
                sequelize = userRankingReviewedAllModel.sequelize;
            } else {
                throw new Error("无法获取数据库连接实例");
            }

            // 统计所有类型的用户数量
            const [allCount, inCount, outCount] = await Promise.all([
                userRankingReviewedAllModel.count(),
                userRankingReviewedInModel.count(),
                userRankingReviewedOutModel.count()
            ]);

            // 计算平均分数
            const [allAvgScore, inAvgScore, outAvgScore] = await Promise.all([
                userRankingReviewedAllModel.findOne({
                    attributes: [
                        [sequelize.fn('AVG', sequelize.col('totalScore')), 'avgScore']
                    ],
                    raw: true
                }),
                userRankingReviewedInModel.findOne({
                    attributes: [
                        [sequelize.fn('AVG', sequelize.col('totalScore')), 'avgScore']
                    ],
                    raw: true
                }),
                userRankingReviewedOutModel.findOne({
                    attributes: [
                        [sequelize.fn('AVG', sequelize.col('totalScore')), 'avgScore']
                    ],
                    raw: true
                })
            ]);

            // 获取分数分布
            const scoreRanges = [
                { range: '0-60', min: 0, max: 60 },
                { range: '60-70', min: 60, max: 70 },
                { range: '70-80', min: 70, max: 80 },
                { range: '80-90', min: 80, max: 90 },
                { range: '90-100', min: 90, max: Number.MAX_SAFE_INTEGER }
            ];

            const scoreDistribution = await Promise.all(
                scoreRanges.map(async ({ range, min, max }) => {
                    const count = await userRankingReviewedAllModel.count({
                        where: {
                            totalScore: {
                                [Op.gte]: min,
                                [Op.lt]: max
                            }
                        }
                    });
                    return { range, count };
                })
            );

            const statsData = {
                userCounts: {
                    all: allCount,
                    in: inCount,
                    out: outCount
                },
                averageScores: {
                    all: parseFloat(allAvgScore.avgScore || 0).toFixed(2),
                    in: parseFloat(inAvgScore.avgScore || 0).toFixed(2),
                    out: parseFloat(outAvgScore.avgScore || 0).toFixed(2)
                },
                scoreDistribution,
                lastUpdated: new Date()
            };

            return apiResponse.successResponseWithData(res, "Success.", statsData);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取用户排名详情（按用户ID）
 * @route POST /v1/sys/ranking/user
 * @group 用户排名管理 - 用户排名管理接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} type.body.required - 排名类型 (all/in/out)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {排名信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getUserRanking = [
    body("userId").notEmpty().withMessage('用户ID不能为空.'),
    body("type").notEmpty().withMessage('排名类型不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            
            const userId = req.body.userId;
            const type = req.body.type;
            
            let rankingModel;
            switch (type) {
                case 'all':
                    rankingModel = userRankingReviewedAllModel;
                    break;
                case 'in':
                    rankingModel = userRankingReviewedInModel;
                    break;
                case 'out':
                    rankingModel = userRankingReviewedOutModel;
                    break;
                default:
                    return apiResponse.validationErrorWithData(res, "参数错误.", "排名类型无效.");
            }
            
            const ranking = await rankingModel.findOne({
                where: { userId }
            });

            if (!ranking) {
                return apiResponse.notFoundResponse(res, "未找到该用户的排名记录.");
            }

            return apiResponse.successResponseWithData(res, "Success.", ranking);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取用户综合绩效雷达图数据
 * @route POST /v1/sys/ranking/radar
 * @group 用户排名管理 - 绩效分析相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {雷达图数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getRadarData = [
    body('userId').notEmpty().withMessage('用户ID不能为空'),
    body('range').optional().isIn(['in', 'out', 'all']).withMessage('统计范围参数无效'),

    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const { userId, range = 'all' } = req.body;

            // 根据range选择对应的总分表
            const rankingModel = getRankingModel(range);

            // 查询用户绩效数据
            const userData = await rankingModel.findOne({
                where: { userId }
            });

            if (!userData) {
                return apiResponse.notFoundResponse(res, "未找到用户绩效数据.");
            }

            // 构建雷达图数据
            const radarData = await buildRadarData(userData, range);

            return apiResponse.successResponseWithData(res, "Success.", radarData);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取用户绩效分布图数据
 * @route POST /v1/sys/ranking/distribution
 * @group 用户排名管理 - 绩效分析相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {分布图数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getDistributionData = [
    body('userId').notEmpty().withMessage('用户ID不能为空'),
    body('range').optional().isIn(['in', 'out', 'all']).withMessage('统计范围参数无效'),

    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const { userId, range = 'all' } = req.body;

            // 获取用户数据和对比分析
            const distributionData = await getDistributionAnalysis(userId, range);

            return apiResponse.successResponseWithData(res, "Success.", distributionData);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取排名趋势对比数据
 * @route POST /v1/sys/ranking/trend
 * @group 用户排名管理 - 绩效分析相关接口
 * @param {string} userId.body.required - 用户ID
 * @returns {object} 200 - {status: "success", message: "Success.", data: {排名趋势数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getRankingTrend = [
    body('userId').notEmpty().withMessage('用户ID不能为空'),

    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const { userId } = req.body;

            // 获取三个范围的排名数据
            const [inData, outData, allData] = await Promise.all([
                userRankingReviewedInModel.findOne({ where: { userId } }),
                userRankingReviewedOutModel.findOne({ where: { userId } }),
                userRankingReviewedAllModel.findOne({ where: { userId } })
            ]);

            const trendData = {
                categories: ['统计范围内', '统计范围外', '全部数据'],
                series: [
                    {
                        name: '总分',
                        data: [
                            inData?.totalScore || 0,
                            outData?.totalScore || 0,
                            allData?.totalScore || 0
                        ]
                    },
                    {
                        name: '排名',
                        data: [
                            inData?.rank || 0,
                            outData?.rank || 0,
                            allData?.rank || 0
                        ]
                    }
                ],
                analysis: {
                    bestRange: getBestPerformanceRange(inData, outData, allData),
                    improvement: calculateImprovement(inData, outData, allData)
                }
            };

            return apiResponse.successResponseWithData(res, "Success.", trendData);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取部门绩效对比数据
 * @route POST /v1/sys/ranking/department-comparison
 * @group 用户排名管理 - 绩效分析相关接口
 * @param {string} range.body - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {部门对比数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getDepartmentComparison = [
    body('range').optional().isIn(['in', 'out', 'all']).withMessage('统计范围参数无效'),

    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const { range = 'all' } = req.body;
            const rankingModel = getRankingModel(range);

            // 先获取所有排名数据
            const allRankings = await rankingModel.findAll({
                attributes: ['userId', 'totalScore'],
                raw: true
            });

            // 获取所有用户的部门信息
            const userDepartments = await userModel.findAll({
                attributes: ['id', 'departmentId'],
                include: [{
                    model: departmentsModel,
                    as: 'department',
                    attributes: ['id', 'departmentName'],
                    where: { status: 1 },
                    required: true
                }],
                raw: true,
                nest: true
            });

            // 创建用户ID到部门的映射
            const userToDepartment = {};
            userDepartments.forEach(user => {
                userToDepartment[user.id] = {
                    departmentId: user.department.id,
                    departmentName: user.department.departmentName
                };
            });

            // 按部门分组统计
            const departmentStats = {};
            allRankings.forEach(ranking => {
                const userDept = userToDepartment[ranking.userId];
                if (userDept) {
                    const deptName = userDept.departmentName;
                    if (!departmentStats[deptName]) {
                        departmentStats[deptName] = {
                            scores: [],
                            userCount: 0
                        };
                    }
                    departmentStats[deptName].scores.push(ranking.totalScore);
                    departmentStats[deptName].userCount++;
                }
            });

            // 计算每个部门的统计数据
            const departments = Object.keys(departmentStats).map(deptName => {
                const scores = departmentStats[deptName].scores;
                const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
                const maxScore = Math.max(...scores);
                const minScore = Math.min(...scores);

                return {
                    name: deptName,
                    avgScore: parseFloat(avgScore || 0).toFixed(1),
                    userCount: departmentStats[deptName].userCount,
                    maxScore: parseFloat(maxScore || 0).toFixed(1),
                    minScore: parseFloat(minScore || 0).toFixed(1)
                };
            }).sort((a, b) => parseFloat(b.avgScore) - parseFloat(a.avgScore));

            const comparisonData = {
                departments,
                summary: {
                    totalDepartments: departments.length,
                    range: range,
                    timestamp: new Date().toISOString()
                }
            };

            return apiResponse.successResponseWithData(res, "Success.", comparisonData);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取用户绩效分析报告
 * @route POST /v1/sys/ranking/analysis-report
 * @group 用户排名管理 - 绩效分析相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部，默认)
 * @returns {object} 200 - {status: "success", message: "Success.", data: {分析报告数据}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getAnalysisReport = [
    body('userId').notEmpty().withMessage('用户ID不能为空'),
    body('range').optional().isIn(['in', 'out', 'all']).withMessage('统计范围参数无效'),

    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const { userId, range = 'all' } = req.body;

            // 获取用户在三个范围的数据
            const [inData, outData, allData] = await Promise.all([
                userRankingReviewedInModel.findOne({ where: { userId } }),
                userRankingReviewedOutModel.findOne({ where: { userId } }),
                userRankingReviewedAllModel.findOne({ where: { userId } })
            ]);

            const currentData = getRankingModel(range).findOne({ where: { userId } });

            if (!currentData) {
                return apiResponse.notFoundResponse(res, "未找到用户数据.");
            }

            // 生成分析报告
            const report = generateAnalysisReport(userId, inData, outData, allData, range);

            return apiResponse.successResponseWithData(res, "Success.", report);
        } catch (err) {
            next(err);
        }
    }
];

// ==================== 工具函数 ====================

/**
 * 根据range参数选择对应的总分表模型
 */
function getRankingModel(range) {
    switch (range) {
        case 'in':
            return userRankingReviewedInModel;
        case 'out':
            return userRankingReviewedOutModel;
        default:
            return userRankingReviewedAllModel;
    }
}

/**
 * 构建雷达图数据
 */
async function buildRadarData(userData, range) {
    // 获取用户基本信息（包括部门）
    let userInfo = null;
    try {
        userInfo = await userModel.findOne({
            where: { id: userData.userId },
            attributes: ['id', 'nickname', 'studentNumber', 'departmentId'],
            include: [{
                model: departmentsModel,
                as: 'department',
                attributes: ['id', 'departmentName'],
                required: false
            }]
        });
    } catch (error) {
        console.log('获取用户基本信息失败:', error);
    }

    return {
        // 十一个维度的分数（增加了教学工作量和教学科研奖励）
        dimensions: [
            '论文发表', '科研项目', '专利申请', '教学改革',
            '学术任职', '指导获奖', '指导立项', '教材著作', 
            '会议组织', '教学工作量', '教学科研奖励'
        ],
        values: [
            userData.highLevelPapersScore || userData.paperScore || 0,
            userData.researchProjectsScore || userData.researchScore || 0,
            userData.patentsScore || 0,
            userData.teachingReformProjectsScore || userData.teachingProjectScore || 0,
            userData.academicAppointmentsScore || userData.appointmentScore || 0,
            userData.studentAwardGuidanceAwardsScore || userData.awardScore || 0,
            userData.studentProjectGuidanceProjectsScore || userData.studentProjectScore || 0,
            userData.textbooksScore || 0,
            userData.conferencesScore || 0,
            userData.teachingWorkloadScore || 0,
            userData.teachingResearchAwardScore || 0
        ],
        // 用户基本信息
        userInfo: {
            userId: userData.userId,
            nickname: userInfo?.nickname || userData.nickName || '未知',
            department: userInfo?.department?.departmentName || '未知',
            studentNumber: userInfo?.studentNumber || userData.studentNumber || '未知'
        },
        // 总分和排名信息
        summary: {
            totalScore: userData.totalScore || 0,
            rank: userData.rank || 0,
            range: range,
            timestamp: new Date().toISOString()
        }
    };
}

/**
 * 获取分布分析数据
 */
async function getDistributionAnalysis(userId, range) {
    const rankingModel = getRankingModel(range);

    // 获取用户信息和部门信息
    const userInfo = await userModel.findOne({
        where: { id: userId },
        attributes: ['id', 'nickname', 'departmentId'],
        include: [{
            model: departmentsModel,
            as: 'department',
            attributes: ['id', 'departmentName'],
            required: false
        }]
    });

    if (!userInfo) {
        throw new Error('未找到用户信息');
    }

    // 获取用户排名数据
    const userData = await rankingModel.findOne({
        where: { userId }
    });

    if (!userData) {
        throw new Error('未找到用户绩效数据');
    }

    // 获取同部门用户数据用于对比（如果部门信息存在）
    let departmentData = [];
    if (userInfo.departmentId) {
        // 先获取同部门的所有用户ID
        const departmentUsers = await userModel.findAll({
            where: {
                departmentId: userInfo.departmentId,
                departmentId: { [Op.ne]: null }
            },
            attributes: ['id']
        });

        const departmentUserIds = departmentUsers.map(user => user.id);

        // 然后获取这些用户的排名数据
        if (departmentUserIds.length > 0) {
            departmentData = await rankingModel.findAll({
                where: {
                    userId: { [Op.in]: departmentUserIds }
                },
                order: [['totalScore', 'DESC']]
            });
        }
    } else {
        // 如果用户没有部门信息，只返回该用户自己的数据
        departmentData = [userData];
    }

    // 获取全校数据用于排名分析
    const allUsersData = await rankingModel.findAll({
        attributes: ['totalScore', 'rank', 'userId'],
        order: [['totalScore', 'DESC']]
    });

    // 计算分布数据
    const distribution = calculateDistribution(userData, departmentData, allUsersData);

    return distribution;
}

/**
 * 计算分布数据
 */
function calculateDistribution(userData, departmentData, allUsersData) {
    // 分数段分布
    const scoreRanges = [
        { min: 0, max: 50, label: '0-50分' },
        { min: 50, max: 100, label: '50-100分' },
        { min: 100, max: 200, label: '100-200分' },
        { min: 200, max: 500, label: '200-500分' },
        { min: 500, max: Infinity, label: '500分以上' }
    ];

    const scoreDistribution = scoreRanges.map(range => {
        const count = allUsersData.filter(user =>
            user.totalScore >= range.min && user.totalScore < range.max
        ).length;

        return {
            label: range.label,
            count,
            percentage: ((count / allUsersData.length) * 100).toFixed(1),
            isUserRange: userData.totalScore >= range.min && userData.totalScore < range.max
        };
    });

    // 部门排名
    const departmentRank = departmentData.findIndex(user => user.userId === userData.userId) + 1;
    const departmentTotal = departmentData.length;

    // 各维度得分占比
    const dimensionScores = [
        { name: '论文发表', score: userData.highLevelPapersScore || userData.paperScore || 0 },
        { name: '科研项目', score: userData.researchProjectsScore || userData.researchScore || 0 },
        { name: '专利申请', score: userData.patentsScore || 0 },
        { name: '教学改革', score: userData.teachingReformProjectsScore || userData.teachingProjectScore || 0 },
        { name: '学术任职', score: userData.academicAppointmentsScore || userData.appointmentScore || 0 },
        { name: '指导获奖', score: userData.studentAwardGuidanceAwardsScore || userData.awardScore || 0 },
        { name: '指导立项', score: userData.studentProjectGuidanceProjectsScore || userData.studentProjectScore || 0 },
        { name: '教材著作', score: userData.textbooksScore || 0 },
        { name: '会议组织', score: userData.conferencesScore || 0 },
        { name: '教学工作量', score: userData.teachingWorkloadScore || 0 },
        { name: '教学科研奖励', score: userData.teachingResearchAwardScore || 0 }
    ];

    const totalDimensionScore = dimensionScores.reduce((sum, dim) => sum + dim.score, 0);
    const dimensionPercentages = dimensionScores.map(dim => ({
        ...dim,
        percentage: totalDimensionScore > 0 ? ((dim.score / totalDimensionScore) * 100).toFixed(1) : 0
    }));

    return {
        userInfo: {
            totalScore: userData.totalScore,
            globalRank: userData.rank,
            departmentRank,
            departmentTotal
        },
        scoreDistribution,
        dimensionAnalysis: dimensionPercentages,
        comparison: {
            departmentAverage: calculateAverage(departmentData.map(u => u.totalScore)),
            globalAverage: calculateAverage(allUsersData.map(u => u.totalScore)),
            topPercentile: calculatePercentile(allUsersData.map(u => u.totalScore), userData.totalScore)
        }
    };
}

/**
 * 分析最佳表现范围
 */
function getBestPerformanceRange(inData, outData, allData) {
    const scores = [
        { range: 'in', score: inData?.totalScore || 0 },
        { range: 'out', score: outData?.totalScore || 0 },
        { range: 'all', score: allData?.totalScore || 0 }
    ];

    return scores.reduce((best, current) =>
        current.score > best.score ? current : best
    );
}

/**
 * 计算改进情况
 */
function calculateImprovement(inData, outData, allData) {
    const inScore = inData?.totalScore || 0;
    const outScore = outData?.totalScore || 0;

    if (outScore === 0) return { rate: 0, direction: 'stable' };

    const rate = ((inScore - outScore) / outScore * 100).toFixed(1);
    const direction = rate > 0 ? 'up' : rate < 0 ? 'down' : 'stable';

    return { rate: Math.abs(rate), direction };
}

/**
 * 生成绩效分析报告
 */
function generateAnalysisReport(userId, inData, outData, allData, currentRange) {
    const currentData = currentRange === 'in' ? inData :
                       currentRange === 'out' ? outData : allData;

    // 强项分析
    const strengths = analyzeStrengths(currentData);

    // 弱项分析
    const weaknesses = analyzeWeaknesses(currentData);

    // 改进建议
    const suggestions = generateSuggestions(strengths, weaknesses, inData, outData);

    // 趋势分析
    const trends = analyzeTrends(inData, outData, allData);

    return {
        userId,
        range: currentRange,
        summary: {
            totalScore: currentData?.totalScore || 0,
            rank: currentData?.rank || 0,
            level: getPerformanceLevel(currentData?.totalScore || 0)
        },
        strengths,
        weaknesses,
        suggestions,
        trends,
        generatedAt: new Date().toISOString()
    };
}

/**
 * 分析用户强项
 */
function analyzeStrengths(userData) {
    const dimensions = [
        { name: '论文发表', score: userData?.highLevelPapersScore || userData?.paperScore || 0 },
        { name: '科研项目', score: userData?.researchProjectsScore || userData?.researchScore || 0 },
        { name: '专利申请', score: userData?.patentsScore || 0 },
        { name: '教学改革', score: userData?.teachingReformProjectsScore || userData?.teachingProjectScore || 0 },
        { name: '学术任职', score: userData?.academicAppointmentsScore || userData?.appointmentScore || 0 },
        { name: '指导获奖', score: userData?.studentAwardGuidanceAwardsScore || userData?.awardScore || 0 },
        { name: '指导立项', score: userData?.studentProjectGuidanceProjectsScore || userData?.studentProjectScore || 0 },
        { name: '教材著作', score: userData?.textbooksScore || 0 },
        { name: '会议组织', score: userData?.conferencesScore || 0 },
        { name: '教学工作量', score: userData?.teachingWorkloadScore || 0 },
        { name: '教学科研奖励', score: userData?.teachingResearchAwardScore || 0 }
    ];

    return dimensions
        .filter(dim => dim.score > 0)
        .sort((a, b) => b.score - a.score)
        .slice(0, 3)
        .map(dim => ({
            dimension: dim.name,
            score: dim.score,
            level: getScoreLevel(dim.score)
        }));
}

/**
 * 分析用户弱项
 */
function analyzeWeaknesses(userData) {
    const dimensions = [
        { name: '论文发表', score: userData?.highLevelPapersScore || userData?.paperScore || 0 },
        { name: '科研项目', score: userData?.researchProjectsScore || userData?.researchScore || 0 },
        { name: '专利申请', score: userData?.patentsScore || 0 },
        { name: '教学改革', score: userData?.teachingReformProjectsScore || userData?.teachingProjectScore || 0 },
        { name: '学术任职', score: userData?.academicAppointmentsScore || userData?.appointmentScore || 0 },
        { name: '指导获奖', score: userData?.studentAwardGuidanceAwardsScore || userData?.awardScore || 0 },
        { name: '指导立项', score: userData?.studentProjectGuidanceProjectsScore || userData?.studentProjectScore || 0 },
        { name: '教材著作', score: userData?.textbooksScore || 0 },
        { name: '会议组织', score: userData?.conferencesScore || 0 },
        { name: '教学工作量', score: userData?.teachingWorkloadScore || 0 },
        { name: '教学科研奖励', score: userData?.teachingResearchAwardScore || 0 }
    ];

    return dimensions
        .sort((a, b) => a.score - b.score)
        .slice(0, 3)
        .map(dim => ({
            dimension: dim.name,
            score: dim.score,
            improvementPotential: getImprovementPotential(dim.name, dim.score)
        }));
}

/**
 * 生成改进建议
 */
function generateSuggestions(strengths, weaknesses, inData, outData) {
    const suggestions = [];

    // 基于弱项生成建议
    weaknesses.forEach(weakness => {
        if (weakness.score === 0) {
            let specificSuggestion = `建议在${weakness.dimension}方面加强投入，这是提升总分的重要机会。`;
            
            // 为特定维度提供更具体的建议
            switch(weakness.dimension) {
                case '教学工作量':
                    specificSuggestion = '建议增加教学工作量投入，可通过承担更多课程、开发新课程或参与教学改革来提高该项得分。';
                    break;
                case '教学科研奖励':
                    specificSuggestion = '建议积极申报各类教学科研奖项，参与教学竞赛，或指导学生参加教学相关比赛以提高该项得分。';
                    break;
                case '论文发表':
                    specificSuggestion = '建议提高论文发表数量和质量，尤其是在高水平期刊上发表研究成果。';
                    break;
                case '科研项目':
                    specificSuggestion = '建议积极申报各级科研项目，特别是国家级和省部级项目。';
                    break;
                case '专利申请':
                    specificSuggestion = '建议加强科研成果转化，增加专利申请数量。';
                    break;
                case '教学改革':
                    specificSuggestion = '建议参与各类教学改革项目，提升教学创新能力。';
                    break;
                case '学术任职':
                    specificSuggestion = '建议积极参与学术组织和期刊任职，提升学术影响力。';
                    break;
                case '指导获奖':
                    specificSuggestion = '建议加强对学生的指导，提高学生在各类比赛中的获奖率。';
                    break;
                case '指导立项':
                    specificSuggestion = '建议指导更多学生申请科研项目和创新创业项目。';
                    break;
                case '教材著作':
                    specificSuggestion = '建议参与编写教材或出版学术著作，提升教学研究成果。';
                    break;
                case '会议组织':
                    specificSuggestion = '建议参与组织或主持学术会议，扩大学术影响力。';
                    break;
            }
            
            suggestions.push({
                type: 'improvement',
                dimension: weakness.dimension,
                suggestion: specificSuggestion
            });
        }
    });

    // 基于范围内外对比生成建议
    const inScore = inData?.totalScore || 0;
    const outScore = outData?.totalScore || 0;

    if (inScore > outScore) {
        suggestions.push({
            type: 'strategy',
            suggestion: '您在统计范围内的表现更好，建议继续保持当前的研究方向和策略。'
        });
    } else if (outScore > inScore) {
        suggestions.push({
            type: 'strategy',
            suggestion: '您在统计范围外的表现更好，可以考虑将部分精力转向统计范围内的项目。'
        });
    }

    return suggestions;
}

/**
 * 趋势分析
 */
function analyzeTrends(inData, outData, allData) {
    return {
        inRange: {
            score: inData?.totalScore || 0,
            rank: inData?.rank || 0
        },
        outRange: {
            score: outData?.totalScore || 0,
            rank: outData?.rank || 0
        },
        overall: {
            score: allData?.totalScore || 0,
            rank: allData?.rank || 0
        }
    };
}

/**
 * 工具函数：计算平均值
 */
function calculateAverage(scores) {
    return scores.length > 0 ? (scores.reduce((sum, score) => sum + score, 0) / scores.length).toFixed(1) : 0;
}

/**
 * 工具函数：计算百分位
 */
function calculatePercentile(allScores, userScore) {
    const sortedScores = allScores.sort((a, b) => b - a);
    const rank = sortedScores.findIndex(score => score <= userScore) + 1;
    return ((rank / sortedScores.length) * 100).toFixed(1);
}

/**
 * 工具函数：获取绩效等级
 */
function getPerformanceLevel(score) {
    if (score >= 500) return '优秀';
    if (score >= 200) return '良好';
    if (score >= 100) return '中等';
    if (score >= 50) return '一般';
    return '待提升';
}

/**
 * 工具函数：获取分数等级
 */
function getScoreLevel(score) {
    if (score >= 100) return '突出';
    if (score >= 50) return '良好';
    if (score >= 20) return '一般';
    return '较弱';
}

/**
 * 工具函数：获取改进潜力
 */
function getImprovementPotential(dimension, score) {
    const potentials = {
        '论文发表': score === 0 ? '高' : '中',
        '科研项目': score === 0 ? '高' : '中',
        '专利申请': score === 0 ? '中' : '低',
        '教学改革': score === 0 ? '中' : '低',
        '学术任职': score === 0 ? '低' : '低',
        '指导获奖': score === 0 ? '中' : '低',
        '指导立项': score === 0 ? '中' : '低',
        '教材著作': score === 0 ? '中' : '低',
        '会议组织': score === 0 ? '低' : '低',
        '教学工作量': score === 0 ? '高' : '中',
        '教学科研奖励': score === 0 ? '高' : '中'
    };

    return potentials[dimension] || '中';
}