NODE_ENV='development'

# 项目服务地址配置
URL=http://localhost
PORT=3089

# 项目Swagger UI’ base url配置
SWA_HOST=localhost
SWA_PORT=3089


DB_HOST=************
DB_NAME=jxpd # 数据库名称
DB_USERNAME=zsh # 数据库用户名
DB_PASSWORD='213yuUINI@#zbxy78' # 数据库密码

Authorization='Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2NDJiZDdiYzc0NjM4MmY3ZDY0NjIzOGMiLCJ1c2VybmFtZSI6IuiKkuaenOW_q-mAnzMiLCJlbWFpbCI6IjE4NDAzNTQwOTJAcXEuY29tIiwiaWF0IjoxNjgwNTk4ODU4LCJleHAiOjE2ODA4NTgwNTh9.7zB7O8BWcz20O2TjEeKiqXYjTeKdTOKSdXIaACJeVlg'

# 自定义Token签名密钥 ：密钥AE对称加过密
SIGN_KEY = 'gLR+JUuKR/R5KrA1gr4ukg==';
