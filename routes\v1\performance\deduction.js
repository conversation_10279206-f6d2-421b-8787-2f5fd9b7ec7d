const express = require('express');
const deductionController = require('../../../controllers/v1/deduction.controller');

const router = express.Router();

// 获取扣分项列表
router.post('/list', deductionController.getDeductions);
// 获取指定扣分项
router.post('/detail', deductionController.getDeductionById);
// 创建扣分项
router.post('/create', deductionController.createDeduction);
// 更新扣分项
router.post('/update', deductionController.updateDeduction);
// 删除扣分项
router.post('/delete', deductionController.deleteDeduction);
// 导入扣分项
router.post('/import', deductionController.importDeductions);
// 导出扣分项
router.post('/export', deductionController.exportDeductions);

module.exports = router; 