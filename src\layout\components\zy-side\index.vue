<template>
  <section class="menu-side" :style="themeStyle">
    <ZyHeaderLogo :isSidebarOpen="isSidebarOpen" v-if="showLogo" :title="webSiteTitle"/>
    <a-menu class="side-menu-list"
            :selectedKeys="selectedKeys"
            :openKeys="openKeys"
            mode="inline"
    >
      <template v-for="item in menuListData">
        <template v-if="!item.children">
          <a-menu-item :key="item.path" @click="handleMenuItem(item)">
            <span>{{ item.meta.title }}</span>
          </a-menu-item>
        </template>
        <template v-else>
          <RecursiveMenuItem
              :key="item.path"
              :menu-info="item"
              @menu-click="handleMenuItem"
          />
        </template>
      </template>
    </a-menu>
  </section>
</template>

<script setup>
import {UserOutlined} from '@ant-design/icons-vue';
import {ref, computed, watch, watchEffect} from 'vue';
import {useRouter} from 'vue-router';

import {useMenuStore} from '@/stores/menu.js';
import {useDbStore} from '@/stores/db.js';
import {menuList} from '@/libs/util.menu.js';
import storage from '@/libs/util.strotage.js'
import {useTabsStore} from '@/stores/tabs.js';
import {useSettingStore} from '@/stores/setting.js';
import RecursiveMenuItem from "./RecursiveMenuItem.vue";
import ZyHeaderLogo from "comps/common/ZyHeaderLogo.vue";
import {adjustColorOpacity} from "../../../libs/util.common";
import setting from '@/setting.js';
// 使用 useMenuStore 获取菜单存储实例
const menuStore = useMenuStore();
const dbStore = useDbStore();
const tabsStore = useTabsStore();
const settingStore = useSettingStore();
// 使用 useRouter 获取路由实例
const router = useRouter();

// 使用 ref 创建响应式的变量
const themeStyle = ref({});
const menuListData = ref([]);
const selectedKeys = ref(menuStore.selectedKeys);
const openKeys = ref(menuStore.openKeys)
const webSiteTitle = ref(setting.websiteInfo.name)

// 初始化菜单 并且联动初始化tabs激活项
const initMenus = () => {
  try {
    // 获取菜单数据
    const menus = menuList();
    
    // 检查菜单数据是否有效
    if (!menus || menus.length === 0) {
      console.warn('初始化菜单失败: 未获取到有效的菜单数据');
      
      // 使用默认菜单或空数组
      menuListData.value = [];
      
      // 等待一秒后重试一次
      setTimeout(() => {
        console.log('重试加载菜单...');
        try {
          const retryMenus = menuList();
          if (retryMenus && retryMenus.length > 0) {
            console.log('重试成功，加载到', retryMenus.length, '个菜单项');
            menuListData.value = retryMenus;
          } else {
            console.error('重试加载菜单失败，菜单数据仍然为空');
          }
        } catch (retryError) {
          console.error('重试加载菜单出错:', retryError);
        }
      }, 1000);
    } else {
      menuListData.value = menus;
    }
  } catch (error) {
    console.error('初始化菜单出错:', error);
    menuListData.value = [];
  }
  
  // 设置当前激活的标签页
  let tR = router.currentRoute.value;
  tabsStore.currentSet(tR.path);
};

// 计算属性，用于判断是否展开侧边栏
const isSidebarOpen = computed(() => {
  return !menuStore.isSidebarOpen;
});
const showLogo = computed(() => {
  return settingStore.showLogo
})


/*****其他工具函数*****/

// 路由有父元素则返回该路由和父元素
function findItemWithParent(data, itemId) {
  for (const item of data) {
    if (item.path === itemId) {
      // 找到目标项，返回它的父元素
      return item; // 如果根节点也是目标项，可以返回 null 或其他合适的值
    }
    if (item.children) {
      const parent = findItemWithParent(item.children, itemId);
      if (parent) {
        // 找到目标项的父元素，将其返回
        return {...item}; // 这里根据实际情况返回父元素的副本或其他数据处理
      }
    }
  }
  return null; // 没有找到目标项及其父元素，返回 null 或其他合适的值
}

/********************/

watchEffect(() => {
  const currentOpenKeys = menuStore.openKeys;
  const currentSelectedKeys = menuStore.selectedKeys;
  const currentMenuParent = findItemWithParent(router.options.routes[0].children, currentOpenKeys[0]);
  
  // 添加安全检查，确保 currentMenuParent 不为 null
  if (currentMenuParent) {
    openKeys.value = Array.from(new Set([currentMenuParent.path, currentOpenKeys[0]]));
  } else {
    // 如果找不到对应的菜单项，只使用当前打开的键
    openKeys.value = Array.from(new Set([currentOpenKeys[0]].filter(Boolean)));
  }

  selectedKeys.value = [currentSelectedKeys[0]];
  let theme = settingStore.theme
  themeStyle.value.backgroundColor = adjustColorOpacity(theme.value.primaryColor, 10)

  // 菜单数据持久化
  dbStore.setSysInfo({selectedKeys: selectedKeys.value, openKeys: openKeys.value});
});


// 导航到指定路由
const navigateTo = (key) => {
  router.push(key);
};

// 处理菜单项点击事件
const handleMenuItem = (item) => {
  let {path, meta} = item
  
  // 数据大屏特殊处理，直接打开窗口
  if (meta && meta.isDataScreen) {
    try {
      const url = `${window.location.origin}/#/dataCenter`
      const screenWindow = window.open(url, '_blank', 'fullscreen=yes,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,status=no')
      
      if (!screenWindow) {
        // 浏览器可能拦截了窗口
        window.$message?.error('数据大屏窗口被浏览器拦截，请允许弹出窗口后重试')
      } else {
        window.$message?.success('数据大屏已在新窗口中打开')
      }
    } catch (error) {
      console.error('打开数据大屏时发生错误:', error)
      window.$message?.error('打开数据大屏失败: ' + error.message)
    }
    
    // 保持当前选中状态不变
    return
  }
  
  selectedKeys.value = [path]
  if (meta && meta.link) {
    window.open(path, '_blank')
  } else {
    tabsStore.addTabs(item)
    tabsStore.currentSet(path)
  }
};


// 初始化菜单数据
initMenus();

</script>


<style lang="scss" scoped>
.menu-side {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: $color-bg;

  .side-menu-list {
    height: calc(100% - 60px);
    overflow: hidden;
    overflow-y: auto;
    background-color: transparent;
    border: none;
    
    :deep(.ant-menu-item) {
      font-size: 15px;
      padding-left: 24px !important;
    }
    
    :deep(.ant-menu-submenu-title) {
      font-size: 15px;
      padding-left: 24px !important;
    }
    
    /* 一级子菜单标题缩进 */
    :deep(.ant-menu-submenu > .ant-menu-submenu-title) {
      padding-left: 24px !important;
    }
    
    /* 二级子菜单标题缩进 */
    :deep(.ant-menu-submenu .ant-menu-submenu > .ant-menu-submenu-title) {
      padding-left: 44px !important;
    }
    
    /* 三级子菜单标题缩进 */
    :deep(.ant-menu-submenu .ant-menu-submenu .ant-menu-submenu > .ant-menu-submenu-title) {
      padding-left: 64px !important;
    }
    
    /* 二级菜单项缩进 */
    :deep(.ant-menu-submenu .ant-menu-item) {
      padding-left: 44px !important;
    }
    
    /* 三级菜单项缩进 */
    :deep(.ant-menu-submenu .ant-menu-submenu .ant-menu-item) {
      padding-left: 64px !important;
    }
    
    /* 四级菜单项缩进 */
    :deep(.ant-menu-submenu .ant-menu-submenu .ant-menu-submenu .ant-menu-item) {
      padding-left: 84px !important;
    }
  }
}
</style>
