// 排名表格专用样式
// 用于绩效排名表格的样式定义

// 排名表格主样式
.ranking-table {
  .ant-table {
    border-radius: 8px;
    overflow: hidden;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      border-bottom: 2px solid #e6f7ff;
      color: #262626;
      text-align: center;
      padding: 16px 12px;
      font-size: 14px;
      
      &:first-child {
        border-top-left-radius: 8px;
      }
      
      &:last-child {
        border-top-right-radius: 8px;
      }
      
      &:hover {
        background: #f0f0f0;
      }
    }
    
    .ant-table-tbody > tr {
      transition: all 0.3s ease;
      
      &:nth-child(odd) {
        background: #fafafa;
      }
      
      &:nth-child(even) {
        background: #fff;
      }
      
      // 前三名特殊样式
      &:nth-child(1) {
        background: #fff7e6;

        td:first-child {
          background: #ffd700;
          color: #fff;
          font-weight: bold;
        }
      }

      &:nth-child(2) {
        background: #f6ffed;

        td:first-child {
          background: #c0c0c0;
          color: #fff;
          font-weight: bold;
        }
      }

      &:nth-child(3) {
        background: #fff0f6;

        td:first-child {
          background: #cd7f32;
          color: #fff;
          font-weight: bold;
        }
      }
      
      > td {
        border-bottom: 1px solid #f0f0f0;
        vertical-align: middle;
        padding: 12px 8px;
        
        // 排名列样式
        &:first-child {
          text-align: center;
          font-weight: 600;
          width: 80px;
          border-radius: 4px;
          margin: 4px;
        }
        
        // 姓名列样式
        &:nth-child(2) {
          font-weight: 500;
          color: #262626;
          width: 120px;
          text-align: center;
        }
        
        // 分数列样式
        &:nth-child(3) {
          padding: 12px 8px;
          width: auto;
        }
        
        // 详情列样式
        &:last-child {
          text-align: center;
          width: 60px;
        }
      }
    }
  }
}

// 分数条样式
.score-bars {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.score-bar-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  padding: 2px 0;
}

.score-bar-label {
  min-width: 60px;
  font-weight: 500;
  color: #666;
  text-align: right;
  font-size: 11px;
}

.score-bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  height: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.score-bar {
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  min-width: 2px;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
    border-radius: 8px;
  }
  
  &:hover {
    filter: brightness(1.1);
  }
}

.score-bar-text {
  font-size: 11px;
  font-weight: 600;
  color: #666;
  min-width: 30px;
  text-align: right;
  background: #fff;
  padding: 1px 4px;
  border-radius: 3px;
  border: 1px solid #e9ecef;
}

// 个人评分卡片样式
.score-overview-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .card-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #262626;
  }
  
  .total-score {
    text-align: center;
    padding: 20px 0;
    background: #f6ffed;
    margin: -20px -20px 20px -20px;
    border-bottom: 1px solid #e6f7ff;
    
    .score-number {
      font-size: 36px;
      font-weight: bold;
      color: #1890ff;
      line-height: 1;
      margin-bottom: 8px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    
    .score-text {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }
  
  .score-rank {
    .rank-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f5f5f5;
      transition: all 0.3s ease;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background: #f8f9fa;
        border-radius: 4px;
      }
      
      .rank-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
      
      .rank-value {
        font-weight: 600;
        color: #262626;
        
        .ant-tag {
          border-radius: 4px;
          font-weight: 500;
        }
      }
    }
  }
}

// 排名标签颜色
.rank-tag {
  &.rank-1 {
    background: #ffd700;
    color: #fff;
    border: none;
  }

  &.rank-2 {
    background: #c0c0c0;
    color: #fff;
    border: none;
  }

  &.rank-3 {
    background: #cd7f32;
    color: #fff;
    border: none;
  }

  &.rank-other {
    background: #f0f0f0;
    color: #666;
    border: 1px solid #d9d9d9;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ranking-table {
    .ant-table-tbody > tr > td {
      padding: 10px 6px;
      
      &:nth-child(2) {
        width: 100px;
      }
    }
  }
  
  .score-bar-label {
    min-width: 50px;
    font-size: 10px;
  }
}

@media (max-width: 768px) {
  .ranking-table {
    .ant-table {
      .ant-table-thead > tr > th {
        padding: 12px 8px;
        font-size: 13px;
      }
      
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
        
        &:first-child {
          width: 60px;
        }
        
        &:nth-child(2) {
          width: 80px;
          font-size: 13px;
        }
        
        &:last-child {
          width: 40px;
        }
      }
    }
  }
  
  .score-bars {
    gap: 3px;
    padding: 6px;
  }
  
  .score-bar-wrapper {
    font-size: 11px;
    gap: 6px;
  }
  
  .score-bar-label {
    min-width: 45px;
    font-size: 10px;
  }
  
  .score-bar-container {
    height: 14px;
  }
  
  .score-bar-text {
    font-size: 10px;
    min-width: 25px;
    padding: 1px 3px;
  }
  
  .score-overview-card {
    .total-score {
      padding: 16px 0;
      margin: -16px -16px 16px -16px;
      
      .score-number {
        font-size: 28px;
      }
      
      .score-text {
        font-size: 13px;
      }
    }
    
    .score-rank {
      .rank-item {
        padding: 6px 0;
        
        .rank-label {
          font-size: 13px;
        }
        
        .rank-value {
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .ranking-table {
    .ant-table {
      .ant-table-thead > tr > th {
        padding: 8px 4px;
        font-size: 12px;
      }
      
      .ant-table-tbody > tr > td {
        padding: 6px 2px;
        font-size: 12px;
        
        &:first-child {
          width: 50px;
        }
        
        &:nth-child(2) {
          width: 70px;
        }
      }
    }
  }
  
  .score-bar-label {
    min-width: 40px;
    font-size: 9px;
  }
  
  .score-bar-container {
    height: 12px;
  }
  
  .score-bar-text {
    font-size: 9px;
    min-width: 20px;
  }
}
