const jwt = require('jsonwebtoken');
const { JWT_SECRET } = require('../config');
const { User, Role, Permission } = require('../models');
const chalk = require('chalk');
const apiResponse = require('../utils/apiResponse');
const logger = require('../utils/logger');
const { roleModel, permissionsModel } = require('../models/v1');
const userModel = require('../models/v1/mapping/userModel');

/**
 * 认证中间件
 * 验证用户是否登录及token有效性
 */
const authMiddleware = async (req, res, next) => {
  try {
    // 从请求头获取token
    const authHeader = req.headers.authorization;

    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return apiResponse.unauthorizedResponse(res, '未提供认证Token');
    }
    
    const token = authHeader.split(' ')[1];
    
    // 使用备选密钥确保token可以正确验证
    const jwtSecret = JWT_SECRET || 'your-jwt-secret-key';
    
    try {
      // 验证token
      const decoded = jwt.verify(token, jwtSecret);
      
      // 将解码后的用户信息存储到请求对象中
      req.user = decoded;
      
      // 验证用户是否存在
      const user = await userModel.findOne({ 
        where: { id: decoded.id },
        attributes: ['id', 'username', 'nickname', 'roleId', 'status'] 
      });
      
      if (!user) {
        return apiResponse.unauthorizedResponse(res, '用户不存在');
      }
      
      // 检查用户状态
      if (!user.status) {
        return apiResponse.unauthorizedResponse(res, '用户已被禁用');
      }
      
      // 验证通过，进入下一个中间件
      next();
    } catch (error) {
      console.error('认证中间件 - Token验证错误:', error);
      console.log('认证中间件 - 错误类型:', error.name);
      console.log('认证中间件 - 错误消息:', error.message);
      
      if (error.name === 'TokenExpiredError') {
        return apiResponse.unauthorizedResponse(res, '登录已过期，请重新登录');
      }
      
      return apiResponse.unauthorizedResponse(res, '无效的认证Token: ' + error.message);
    }
  } catch (error) {
    console.error('认证中间件 - 捕获到异常:', error);
    return apiResponse.errorResponse(res, '服务器认证过程发生错误: ' + error.message);
  }
};

/**
 * 权限检查中间件
 * @param {string|string[]} requiredPermissions 需要的权限
 * @returns {function} 中间件函数
 */
const checkPermission = (requiredPermissions) => {
  return (req, res, next) => {
    try {
      // 如果用户是超级管理员，直接通过
      if (req.userRoles.includes('admin')) {
        return next();
      }
      
      // 权限检查
      const permissions = Array.isArray(requiredPermissions) 
        ? requiredPermissions 
        : [requiredPermissions];
      
      const hasPermission = permissions.some(permission => 
        req.userPermissions.includes(permission)
      );
      
      if (!hasPermission) {
        return res.status(403).json({
          code: 403,
          message: '权限不足，无法执行此操作',
          data: null
        });
      }
      
      next();
    } catch (error) {
      console.error('权限检查中间件错误:', error);
      return res.status(500).json({
        code: 500,
        message: '服务器权限检查过程发生错误',
        data: null
      });
    }
  };
};

/**
 * 基于API的权限检查中间件
 * @param {string} auth - 所需权限的键。
 * @returns {Function} Express中间件函数。
 */
const checkApiPermission = (auth) => {
  return async (req, res, next) => {
    try {
      // 查找经过身份验证的用户的角色权限信息。
      // req.user 是在 tokenAuthentication 中间件解析出来的用户信息
      const roleInfo = await roleModel.findByPk(req.user.roleId, {
        include: [{
          model: permissionsModel,
          attributes: ['key'], // 只包含 key 字段
          through: {attributes: []} // 不包括中间表
        }],
      });
      if (!roleInfo) {
        return apiResponse.notFoundResponse(res, "该用户还未分配角色.");
      }

      roleInfo.dataValues.permissions = roleInfo.permissions.map(permission => permission.key);

      if (roleInfo) {
        if (!roleInfo.status && !roleInfo.dataValues.permissions.includes('*')) {
          apiResponse.unauthorizedResponse(res, '您的角色已被禁用,请联系管理员')
          return false
        }
        // 对超级管理员或其他
        if (roleInfo.dataValues.permissions.includes('*') || roleInfo.dataValues.permissions.includes(auth)) {
          const permissionInfo = await permissionsModel.findOne({key: auth})
          // 权限已被禁用
          if (!roleInfo.dataValues.permissions.includes('*') && !(!!permissionInfo.status)) {
            console.error(chalk.red('【权限已被禁用】: ' + req.method + req.baseUrl))
            return apiResponse.unauthorizedResponse(res, '您访问的权限已被禁用，请联系管理员')
          }
          // 接口验证通过，继续下一步中间件或处理程序
          return next();
        } else {
          console.error(chalk.bold.red('*********************************'))
          console.error(chalk.red('【OPERATOR】: ' + req.user.nickname))
          console.error(chalk.red('【权限未通过】: ' + req.method + req.baseUrl))
          console.error(chalk.red('【    DATE】: ' + new Date().toLocaleString()))
          console.error(chalk.bold.red('*********************************'))
          return apiResponse.unauthorizedResponse(res, '您暂时没有权限访问,请联系管理员')
        }
      }

    } catch (err) {
      console.error(chalk.bold.red('*********************************'))
      console.error(err)
      console.error(chalk.red('【    DATE】: ' + new Date().toLocaleString()))
      console.error(chalk.bold.red('*********************************'))
      return apiResponse.unauthorizedResponse(res, '接口权限验证错误')
    }
  };
};

/**
 * 设置指定的角色访问
 * @param {Array} allowedRoles - 允许的角色列表
 * @returns {Function} - Express中间件函数
 */
const checkUserRole = (allowedRoles) => {
  return (req, res, next) => {
    // 从 req.users 中获取用户信息，假设用户信息中有一个字段 role 表示用户角色
    const userRole = req.user.roleId; // 47914c7b-0fa5-485c-bc44-b5d571e1e89e  只要超级管理员可以操作
    if (userRole !== '47914c7b-0fa5-485c-bc44-b5d571e1e89e') {
      return apiResponse.unauthorizedResponse(res, '您未被授权此操作，请联系管理员.');
    }
    // 用户角色验证通过，继续下一步中间件或处理程序
    next();
  };
};

/**
 * 检查用户权限
 * @param {Array} allowedPermissions - 允许的权限列表
 * @returns {Function} - Express中间件函数
 */
const checkUserPermission = (allowedPermissions) => {
  return (req, res, next) => {
    // 当前实现暂时直接放行
    next();
  };
};

/**
 * 验证菜单权限
 * @param {Array} userRoles - 用户角色列表
 * @param {String} menuRoute - 菜单路由
 * @returns {Boolean} - 是否有权限
 */
const hasMenuPermission = (userRoles, menuRoute) => {
  // 为将来扩展保留接口
  return true;
};

/**
 * 验证按钮权限
 * @param {Array} userRoles - 用户角色列表
 * @param {String} buttonRoute - 按钮路由
 * @returns {Boolean} - 是否有权限
 */
const hasButtonPermission = (userRoles, buttonRoute) => {
  // 为将来扩展保留接口
  return true;
};

module.exports = {
  authMiddleware,
  checkPermission,
  checkApiPermission,
  checkUserRole,
  checkUserPermission,
  hasMenuPermission,
  hasButtonPermission
}; 