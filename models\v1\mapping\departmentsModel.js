const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

/**
 * 部门模型
 * 用于管理组织架构中的部门信息
 */
const departmentsModel = sequelize.define('departments', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: '部门ID，使用UUID'
  },
  departmentName: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '部门名称'
  },
  departmentCode: {
    type: DataTypes.STRING(50),
    allowNull: true,
    unique: true,
    comment: '部门代码'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '部门描述'
  },
  parentId: {
    type: DataTypes.CHAR(36),
    allowNull: true,
    comment: '上级部门ID（支持层级结构）'
  },
  sort: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '排序字段，数字越小排序越靠前'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    defaultValue: 1,
    comment: '状态：1-启用，0-禁用'
  },
  createdBy: {
    type: DataTypes.CHAR(36),
    allowNull: false,
    comment: '创建者ID'
  }
}, {
  tableName: 'departments',
  freezeTableName: true,
  timestamps: true,
  comment: '部门表'
});

/**
 * 实例方法：获取完整的部门路径
 */
departmentsModel.prototype.getFullPath = async function() {
  let path = [this.departmentName];
  let current = this;
  
  while (current.parentId) {
    const parent = await departmentsModel.findByPk(current.parentId);
    if (parent) {
      path.unshift(parent.departmentName);
      current = parent;
    } else {
      break;
    }
  }
  
  return path.join(' > ');
};

/**
 * 静态方法：获取部门树形结构
 */
departmentsModel.getTree = async function(parentId = null) {
  const departments = await departmentsModel.findAll({
    where: {
      parentId: parentId,
      status: 1
    },
    order: [['sort', 'ASC'], ['departmentName', 'ASC']]
  });

  const tree = [];
  for (const dept of departments) {
    const children = await departmentsModel.getTree(dept.id);
    tree.push({
      ...dept.toJSON(),
      children: children
    });
  }

  return tree;
};

/**
 * 静态方法：获取所有子部门ID（包括自身）
 */
departmentsModel.getAllChildrenIds = async function(departmentId) {
  const ids = [departmentId];
  
  const children = await departmentsModel.findAll({
    where: { parentId: departmentId },
    attributes: ['id']
  });

  for (const child of children) {
    const childIds = await departmentsModel.getAllChildrenIds(child.id);
    ids.push(...childIds);
  }

  return ids;
};

/**
 * 静态方法：验证部门名称唯一性
 */
departmentsModel.validateUniqueName = async function(departmentName, excludeId = null) {
  const { Op } = require('sequelize');
  const where = { departmentName };
  if (excludeId) {
    where.id = { [Op.ne]: excludeId };
  }
  
  const existing = await departmentsModel.findOne({ where });
  return !existing;
};

/**
 * 静态方法：验证部门代码唯一性
 */
departmentsModel.validateUniqueCode = async function(departmentCode, excludeId = null) {
  if (!departmentCode) return true;
  
  const { Op } = require('sequelize');
  const where = { departmentCode };
  if (excludeId) {
    where.id = { [Op.ne]: excludeId };
  }
  
  const existing = await departmentsModel.findOne({ where });
  return !existing;
};

module.exports = departmentsModel;
