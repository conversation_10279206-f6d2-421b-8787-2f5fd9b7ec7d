const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const awardsRulesModel = require('../../../models/v1/mapping/awardsRulesModel');
const userModel = require('../../../models/v1/mapping/userModel');
/**
 * 获奖模型
 * @module models/v1/mapping/awardModel
 */
const Award = sequelize.define('awards', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: 'ID'
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '获奖名称'
  },
  awardsRuleId: {
    type: DataTypes.STRING(255),
    comment: '获奖规则对应id'
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '获奖类型'
  },
  level: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '获奖级别'
  },
  awardDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '获奖时间'
  },
  awardUnit: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '获奖单位'
  },
  awardRank: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '获奖排名'
  },
  ifReviewer: {
    type: DataTypes.TINYINT,
    allowNull: true,
    comment: '是否审核'
  },
  reviewer: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '审核人'
  },
  teachers: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '获奖教师'
  },
  score: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '得分'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    defaultValue: 1,
    comment: '状态'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'awards',
  timestamps: true,
  indexes: [
    {
      name: 'idx_award_type',
      fields: ['type']
    },
    {
      name: 'idx_award_level',
      fields: ['level']
    },
    {
      name: 'idx_award_status',
      fields: ['status']
    }
  ]
});

Award.belongsTo(awardsRulesModel, {
  foreignKey: 'awardsRuleId',
  as: 'awardsRule',
});

Award.belongsTo(userModel, {
  foreignKey: 'teachers',
  as: 'user',
});

module.exports = Award; 