const express = require('express');
const router = express.Router();
const conferenceParticipantController = require('../../../controllers/v1/conferences/conferenceParticipantsController');

/**
 * 获取用户会议参与统计
 * @route POST /v1/sys/conference-participant/statistics/user-statistics
 * @group 会议参与者统计 - 会议参与者统计相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {object} timeRange.body - 可选的自定义时间范围，包含startDate和endDate字段
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {userId: "用户ID", totalCount: 总数, list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/user-statistics', conferenceParticipantController.getUserStatistics);

/**
 * 获取所有用户会议参与统计
 * @route POST /v1/sys/conference-participant/statistics/all-users-statistics
 * @group 会议参与者统计 - 会议参与者统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {object} timeRange.body - 可选的自定义时间范围，包含startDate和endDate字段
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @param {string} sortField.body - 排序字段，可选值：'conferenceCount'(会议数),'leaderConferenceCount'(负责会议数)，默认'conferenceCount'
 * @param {string} sortOrder.body - 排序方向，可选值：'asc'(升序),'desc'(降序)，默认'desc'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/all-users-statistics', conferenceParticipantController.getAllUsersStatistics);

module.exports = router; 