const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义扣减规则模型
module.exports = sequelize.define('deductions_rules', {
    id: {
        type: DataTypes.CHAR(36),
        notNull: true,
        primaryKey: true,
        comment: '主键，使用 UUID 唯一标识每条记录',
    },
    unTimelyDegreeCount: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '未按时获得学位的研究生数',
    },
    unTimelyDegreeDeduction: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '未按时获得学位的扣减分数',
    },
    unemploymentDate: {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: '未就业的截止日期（如 8 月 31 日、12 月 31 日）',
    },
    unemploymentDeduction: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '未就业的扣减分数',
    },
    createdBy: {
        type: DataTypes.CHAR(36),
        notNull: true,
        allowNull: false,
        comment: '创建者 ID（与 id 一致的 UUID）',
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '记录创建时间',
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '记录最后修改时间',
    }
}, {
    freezeTableName: true, // 禁止表名自动复数化
    timestamps: true, // 启用时间戳
    updatedAt: true, // 启用更新时间戳
    createdAt: true, // 启用创建时间戳
}); 