const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const multer = require('multer');
const Teacher = require('../../../models/v1/mapping/teacherModel');
const { Op } = require('sequelize');
const upload = multer({ dest: 'uploads/' });

/**
 * 获取教师列表
 * @route POST /v1/sys/teacher/list
 * @group 教师管理 - 教师信息管理接口
 * @param {string} name.body - 教师姓名（模糊搜索）
 * @param {string} title.body - 职称
 * @param {string} department.body - 所属部门
 * @param {number} status.body - 状态（1-在职，0-离职）
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, current: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getTeachers = async (req, res) => {
  try {
    const { name, title, department, status, page = 1, pageSize = 10 } = req.body;
    
    // 构建查询条件
    const where = {};
    
    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }
    
    if (title) {
      where.title = title;
    }
    
    if (department) {
      where.department = department;
    }
    
    if (status !== undefined) {
      where.status = parseInt(status);
    }
    
    // 查询数据库
    const { count, rows } = await Teacher.findAndCountAll({
      where,
      offset: (page - 1) * pageSize,
      limit: parseInt(pageSize),
      order: [['createdAt', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: "获取成功",
      data: {
        list: rows,
        pagination: {
          total: count,
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取教师列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师列表失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 获取教师详情
 * @route POST /v1/sys/teacher/detail
 * @group 教师管理 - 教师信息管理接口
 * @param {number} id.body.required - 教师ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {教师信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getTeacherById = async (req, res) => {
  try {
    const { id } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '教师ID不能为空',
        data: null
      });
    }
    
    const teacher = await Teacher.findByPk(id);
    
    if (!teacher) {
      return res.status(404).json({
        code: 404,
        message: '教师不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: teacher
    });
  } catch (error) {
    console.error('获取教师详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师详情失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 创建教师
 * @route POST /v1/sys/teacher/create
 * @group 教师管理 - 教师信息管理接口
 * @param {string} name.body.required - 教师姓名
 * @param {string} title.body - 职称，默认"教师"
 * @param {string} department.body - 所属部门，默认"未分配"
 * @param {number} status.body - 状态（1-在职，0-离职），默认1
 * @param {string} remark.body - 备注
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {教师信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.createTeacher = async (req, res) => {
  try {
    const { name, title, department, status, remark } = req.body;
    
    // 校验必填项
    if (!name) {
      return res.status(400).json({
        code: 400,
        message: '教师姓名不能为空',
        data: null
      });
    }
    
    // 创建新教师
    const newTeacher = await Teacher.create({
      name,
      title: title || '教师',
      department: department || '未分配',
      status: status !== undefined ? parseInt(status) : 1,
      remark: remark || ''
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: newTeacher
    });
  } catch (error) {
    console.error('创建教师失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建教师失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 更新教师
 * @route POST /v1/sys/teacher/update
 * @group 教师管理 - 教师信息管理接口
 * @param {number} id.body.required - 教师ID
 * @param {string} name.body - 教师姓名
 * @param {string} title.body - 职称
 * @param {string} department.body - 所属部门
 * @param {number} status.body - 状态（1-在职，0-离职）
 * @param {string} remark.body - 备注
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {教师信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.updateTeacher = async (req, res) => {
  try {
    const { id, name, title, department, status, remark } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '教师ID不能为空',
        data: null
      });
    }
    
    // 查找教师
    const teacher = await Teacher.findByPk(id);
    
    if (!teacher) {
      return res.status(404).json({
        code: 404,
        message: '教师不存在',
        data: null
      });
    }
    
    // 更新教师信息
    const updates = {};
    if (name) updates.name = name;
    if (title) updates.title = title;
    if (department) updates.department = department;
    if (status !== undefined) updates.status = parseInt(status);
    if (remark !== undefined) updates.remark = remark;
    
    await teacher.update(updates);
    
    // 获取更新后的教师
    const updatedTeacher = await Teacher.findByPk(id);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: updatedTeacher
    });
  } catch (error) {
    console.error('更新教师失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新教师失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 删除教师
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteTeacher = async (req, res) => {
  try {
    const { id } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '教师ID不能为空',
        data: null
      });
    }
    
    // 查找教师
    const teacher = await Teacher.findByPk(id);
    
    if (!teacher) {
      return res.status(404).json({
        code: 404,
        message: '教师不存在',
        data: null
      });
    }
    
    // 删除教师
    await teacher.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除教师失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除教师失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 导入教师
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importTeachers = (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请上传Excel文件',
        data: null
      });
    }
    
    const filePath = path.join(process.cwd(), req.file.path);
    
    const workbook = new ExcelJS.Workbook();
    return workbook.xlsx.readFile(filePath)
      .then(async () => {
        const worksheet = workbook.getWorksheet(1);
        
        const teachers = [];
        const errors = [];
        
        worksheet.eachRow({ includeEmpty: false }, async (row, rowNumber) => {
          // 跳过标题行
          if (rowNumber === 1) return;
          
          const teacherData = {
            name: row.getCell(1).value,
            title: row.getCell(2).value,
            department: row.getCell(3).value,
            status: row.getCell(4).value === '在职' ? 1 : 0,
            remark: row.getCell(5).value || ''
          };
          
          // 校验数据
          if (!teacherData.name) {
            errors.push(`第${rowNumber}行：教师姓名不能为空`);
            return;
          }
          
          teachers.push(teacherData);
        });
        
        // 如果有错误，返回错误信息
        if (errors.length > 0) {
          return res.status(400).json({
            code: 400,
            message: '导入失败，请修正以下错误',
            data: { errors }
          });
        }
        
        // 批量创建教师
        const importedTeachers = await Teacher.bulkCreate(teachers);
        
        // 删除临时文件
        fs.unlinkSync(filePath);
        
        return res.status(200).json({
          code: 200,
          message: `成功导入${importedTeachers.length}条教师数据`,
          data: { count: importedTeachers.length }
        });
      })
      .catch(error => {
        console.error('解析Excel文件失败:', error);
        return res.status(500).json({
          code: 500,
          message: '导入失败: ' + error.message,
          data: null
        });
      });
  } catch (error) {
    console.error('导入教师失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导入教师失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 导出教师
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportTeachers = (req, res) => {
  try {
    const { name, title, department, status } = req.body;
    
    // 构建查询条件
    const where = {};
    
    if (name) {
      where.name = { [Op.like]: `%${name}%` };
    }
    
    if (title) {
      where.title = title;
    }
    
    if (department) {
      where.department = department;
    }
    
    if (status !== undefined) {
      where.status = parseInt(status);
    }
    
    // 查询数据库
    return Teacher.findAll({
      where,
      order: [['createdAt', 'DESC']]
    })
      .then(teachers => {
        // 创建工作簿和工作表
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('教师列表');
        
        // 添加表头
        worksheet.addRow(['姓名', '职称', '所属院系', '状态', '备注']);
        
        // 添加数据
        teachers.forEach(teacher => {
          worksheet.addRow([
            teacher.name,
            teacher.title,
            teacher.department,
            teacher.status ? '在职' : '离职',
            teacher.remark || ''
          ]);
        });
        
        // 设置列宽
        worksheet.columns.forEach(column => {
          column.width = 20;
        });
        
        // 设置文件名
        const filename = `教师列表_${new Date().toISOString().split('T')[0]}.xlsx`;
        const filepath = path.join(process.cwd(), 'uploads', filename);
        
        // 写入文件
        return workbook.xlsx.writeFile(filepath)
          .then(() => {
            // 发送文件
            res.download(filepath, filename, err => {
              if (err) {
                console.error('文件下载失败:', err);
                return res.status(500).json({
                  code: 500,
                  message: '文件下载失败',
                  data: null
                });
              }
              
              // 删除临时文件
              fs.unlinkSync(filepath);
            });
          });
      })
      .catch(error => {
        console.error('导出教师失败:', error);
        return res.status(500).json({
          code: 500,
          message: '导出教师失败: ' + error.message,
          data: null
        });
      });
  } catch (error) {
    console.error('导出教师失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出教师失败: ' + error.message,
      data: null
    });
  }
}; 