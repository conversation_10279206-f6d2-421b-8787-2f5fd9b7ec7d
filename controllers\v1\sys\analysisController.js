const { Op } = require('sequelize');
const Sequelize = require('sequelize');
const sequelize = require('../../../config/db');

// 引入模型
const teacherModel = require('../../../models/v1/mapping/teacherModel');
const researchProjectModel = require('../../../models/v1/mapping/researchProjectModel');
const researchFundModel = require('../../../models/v1/mapping/researchFundModel');
const highLevelPaperModel = require('../../../models/v1/mapping/highLevelPapersModel');
const scoreRecordModel = require('../../../models/v1/mapping/scoreRecordModel');

/**
 * 获取绩效趋势数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPerformanceTrend = async (req, res) => {
  try {
    const { year = new Date().getFullYear(), department } = req.query;
    
    // 计算获取5年的数据，包括当前年份
    const startYear = parseInt(year) - 4;
    const endYear = parseInt(year);
    const years = Array.from({ length: 5 }, (_, i) => startYear + i);
    
    // 构建查询条件
    const whereCondition = department ? { year: { [Op.between]: [startYear, endYear] }, department } : { year: { [Op.between]: [startYear, endYear] } };
    
    // 查询各年份平均绩效得分
    const scoreTrend = await Promise.all(
      years.map(async (year) => {
        const result = await scoreRecordModel.findOne({
          attributes: [
            [Sequelize.fn('AVG', Sequelize.col('totalScore')), 'avgScore']
          ],
          where: { ...whereCondition, year },
          raw: true
        });
        
        return { 
          year, 
          score: result?.avgScore ? parseFloat(result.avgScore).toFixed(0) : 0 
        };
      })
    );
    
    // 查询各年份科研项目数量
    const projectTrend = await Promise.all(
      years.map(async (year) => {
        const count = await researchProjectModel.count({
          where: { 
            ...whereCondition,
            startDate: {
              [Op.between]: [`${year}-01-01`, `${year}-12-31`]
            }
          }
        });
        
        return { year, count };
      })
    );
    
    // 查询各年份科研经费总额
    const fundTrend = await Promise.all(
      years.map(async (year) => {
        const result = await researchFundModel.findOne({
          attributes: [
            [Sequelize.fn('SUM', Sequelize.col('amount')), 'totalAmount']
          ],
          where: { 
            ...whereCondition,
            startDate: {
              [Op.between]: [`${year}-01-01`, `${year}-12-31`]
            }
          },
          raw: true
        });
        
        return { 
          year, 
          amount: result?.totalAmount ? parseFloat(result.totalAmount).toFixed(0) : 0 
        };
      })
    );
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        years,
        scoreTrend,
        projectTrend,
        fundTrend
      }
    });
    
  } catch (error) {
    console.error('获取绩效趋势数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取绩效趋势数据失败',
      data: null
    });
  }
};

/**
 * 获取教师职称分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherTitles = async (req, res) => {
  try {
    const { department } = req.query;
    
    // 构建查询条件
    const whereCondition = department ? { department, status: 1 } : { status: 1 };
    
    // 查询教师职称分布
    const titleDistribution = await teacherModel.findAll({
      attributes: [
        'title',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: whereCondition,
      group: ['title'],
      raw: true
    });
    
    // 格式化数据
    const formattedData = titleDistribution.map(item => ({
      name: item.title,
      value: parseInt(item.count)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: formattedData
    });
    
  } catch (error) {
    console.error('获取教师职称分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师职称分布失败',
      data: null
    });
  }
};

/**
 * 获取项目级别分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectLevels = async (req, res) => {
  try {
    const { year = new Date().getFullYear(), department } = req.query;
    
    // 构建查询条件
    let whereCondition = {
      startDate: {
        [Op.between]: [`${year}-01-01`, `${year}-12-31`]
      },
      status: 1
    };
    
    if (department) {
      // 查询该部门的教师
      const teachers = await teacherModel.findAll({
        attributes: ['name'],
        where: { department },
        raw: true
      });
      
      const teacherNames = teachers.map(t => t.name);
      
      whereCondition.leader = {
        [Op.in]: teacherNames
      };
    }
    
    // 查询项目级别分布
    const levelDistribution = await researchProjectModel.findAll({
      attributes: [
        'level',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: whereCondition,
      group: ['level'],
      raw: true
    });
    
    // 格式化数据
    const formattedData = levelDistribution.map(item => ({
      name: item.level,
      value: parseInt(item.count)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: formattedData
    });
    
  } catch (error) {
    console.error('获取项目级别分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目级别分布失败',
      data: null
    });
  }
};

/**
 * 获取院系教师分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDepartmentTeachers = async (req, res) => {
  try {
    const { status = 1 } = req.query;
    
    // 查询院系教师分布
    const departmentDistribution = await teacherModel.findAll({
      attributes: [
        'department',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: { 
        status,
        department: {
          [Op.ne]: null
        }
      },
      group: ['department'],
      raw: true
    });
    
    // 格式化数据
    const formattedData = departmentDistribution.map(item => ({
      name: item.department,
      value: parseInt(item.count)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: formattedData
    });
    
  } catch (error) {
    console.error('获取院系教师分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取院系教师分布失败',
      data: null
    });
  }
};

/**
 * 获取项目年度分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectYears = async (req, res) => {
  try {
    const { start_year = new Date().getFullYear() - 4, end_year = new Date().getFullYear(), department } = req.query;
    
    const startYear = parseInt(start_year);
    const endYear = parseInt(end_year);
    const years = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);
    
    // 构建基础查询条件
    let baseWhereCondition = { status: 1 };
    
    if (department) {
      // 查询该部门的教师
      const teachers = await teacherModel.findAll({
        attributes: ['name'],
        where: { department },
        raw: true
      });
      
      const teacherNames = teachers.map(t => t.name);
      
      baseWhereCondition.leader = {
        [Op.in]: teacherNames
      };
    }
    
    // 查询各年份项目分布
    const projectDistribution = await Promise.all(
      years.map(async (year) => {
        const count = await researchProjectModel.count({
          where: { 
            ...baseWhereCondition,
            startDate: {
              [Op.between]: [`${year}-01-01`, `${year}-12-31`]
            }
          }
        });
        
        return { year, count };
      })
    );
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: projectDistribution
    });
    
  } catch (error) {
    console.error('获取项目年度分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目年度分布失败',
      data: null
    });
  }
};

/**
 * 获取高级排行榜数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getRanking = async (req, res) => {
  try {
    const { year = new Date().getFullYear(), type = 'score', department, limit = 10 } = req.query;
    
    // 构建基础查询条件
    let whereCondition = { year: parseInt(year) };
    
    if (department) {
      whereCondition.department = department;
    }
    
    let data = [];
    
    switch (type) {
      case 'score':
        // 查询总分排名
        const scoreRanking = await scoreRecordModel.findAll({
          attributes: [
            'teacherId',
            'teacher',
            'department',
            'totalScore'
          ],
          where: whereCondition,
          order: [['totalScore', 'DESC']],
          limit: parseInt(limit),
          raw: true
        });
        
        // 获取教师职称信息
        const teacherIds = scoreRanking.map(item => item.teacherId);
        const teacherInfo = await teacherModel.findAll({
          attributes: ['id', 'title'],
          where: { id: { [Op.in]: teacherIds } },
          raw: true
        });
        
        const teacherTitleMap = new Map(teacherInfo.map(item => [item.id, item.title]));
        
        data = scoreRanking.map((item, index) => ({
          rank: index + 1,
          teacher_id: item.teacherId,
          name: item.teacher,
          department: item.department,
          value: parseFloat(item.totalScore),
          title: teacherTitleMap.get(item.teacherId) || '未知'
        }));
        break;
        
      case 'project':
        // 查询项目数量排名
        const projectCounts = await sequelize.query(`
          SELECT 
            t.id as teacher_id,
            t.name,
            t.department,
            t.title,
            COUNT(rp.id) as project_count
          FROM 
            teachers t
          LEFT JOIN 
            research_projects rp ON t.name = rp.leader AND YEAR(rp.startDate) = :year
          WHERE 
            t.status = 1
            ${department ? "AND t.department = :department" : ""}
          GROUP BY 
            t.id, t.name, t.department, t.title
          ORDER BY 
            project_count DESC
          LIMIT :limit
        `, {
          replacements: { 
            year: year,
            department: department,
            limit: parseInt(limit)
          },
          type: Sequelize.QueryTypes.SELECT
        });
        
        data = projectCounts.map((item, index) => ({
          rank: index + 1,
          teacher_id: item.teacher_id,
          name: item.name,
          department: item.department,
          value: parseInt(item.project_count),
          title: item.title
        }));
        break;
        
      case 'fund':
        // 查询经费金额排名
        const fundAmounts = await sequelize.query(`
          SELECT 
            t.id as teacher_id,
            t.name,
            t.department,
            t.title,
            SUM(rf.amount) as total_amount
          FROM 
            teachers t
          LEFT JOIN 
            research_funds rf ON t.name = rf.leader AND YEAR(rf.startDate) = :year
          WHERE 
            t.status = 1
            ${department ? "AND t.department = :department" : ""}
          GROUP BY 
            t.id, t.name, t.department, t.title
          HAVING
            total_amount > 0
          ORDER BY 
            total_amount DESC
          LIMIT :limit
        `, {
          replacements: { 
            year: year,
            department: department,
            limit: parseInt(limit)
          },
          type: Sequelize.QueryTypes.SELECT
        });
        
        data = fundAmounts.map((item, index) => ({
          rank: index + 1,
          teacher_id: item.teacher_id,
          name: item.name,
          department: item.department,
          value: parseFloat(item.total_amount).toFixed(2),
          title: item.title
        }));
        break;
        
      case 'paper':
        // 查询论文数量排名
        const paperCounts = await sequelize.query(`
          SELECT 
            t.id as teacher_id,
            t.name,
            t.department,
            t.title,
            COUNT(hp.id) as paper_count
          FROM 
            teachers t
          LEFT JOIN 
            high_level_papers hp ON (hp.authors LIKE CONCAT('%', t.name, '%') OR hp.correspondingAuthor = t.name)
            AND YEAR(hp.publishDate) = :year
          WHERE 
            t.status = 1
            ${department ? "AND t.department = :department" : ""}
          GROUP BY 
            t.id, t.name, t.department, t.title
          ORDER BY 
            paper_count DESC
          LIMIT :limit
        `, {
          replacements: { 
            year: year,
            department: department,
            limit: parseInt(limit)
          },
          type: Sequelize.QueryTypes.SELECT
        });
        
        data = paperCounts.map((item, index) => ({
          rank: index + 1,
          teacher_id: item.teacher_id,
          name: item.name,
          department: item.department,
          value: parseInt(item.paper_count),
          title: item.title
        }));
        break;
        
      default:
        return res.status(400).json({
          code: 400,
          message: '不支持的排名类型',
          data: null
        });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data
    });
    
  } catch (error) {
    console.error('获取高级排行榜数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取高级排行榜数据失败',
      data: null
    });
  }
}; 