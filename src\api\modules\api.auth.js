import service from '../server'
// 验证码
export const authCaptcha = (data) => {
    return service.get('/auth/captcha', data)
}
// 登录
export const authLogin = (data) => {
    return service.post('/auth/login', data)
}
// 注册
export const authRegister = (data) => {
    return service.post('/auth/register', data)
}
// 获取用户角色
export const authGetUserRole = (userId) => {
    const params = userId ? { userId } : {};
    return service.get('/auth/role', params)
}

