const {DataTypes} = require('sequelize');
const sequelize = require('@config/dbConfig');

/**
 * 通知模型
 * 表名使用下划线命名法: notifications
 * 字段名使用下划线命名法: created_at, user_id 等
 */
const Notifications = sequelize.define('notifications',
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            comment: '唯一标识每条记录的主键'
        },
        type: {
            type: DataTypes.STRING(255),
            allowNull: true,
            comment: '记录的类型，例如research_project'
        },
        title: {
            type: DataTypes.STRING(255),
            allowNull: false,
            comment: '项目的标题'
        },
        content: {
            type: DataTypes.TEXT,
            allowNull: false,
            comment: '项目的详细内容'
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            comment: '记录创建的时间戳'
        },
        abstract: {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: '与通知相关的摘要信息'
        },
        userId: {
            type: DataTypes.CHAR(36),
            allowNull: true,
            comment: '与记录关联的用户ID，使用UUID格式'
        },
        initiatorId: {
            type: DataTypes.CHAR(36),
            allowNull: true,
            comment: '通知的发起人ID，使用UUID格式'
        },
        sendMode: {
            type: DataTypes.STRING(20),
            allowNull: true,
            defaultValue: 'single',
            comment: '发送模式：all/department/user_level'
        },
        targetDepartmentId: {
            type: DataTypes.CHAR(36),
            allowNull: true,
            comment: '目标部门ID（当send_mode为department时使用）'
        },
        targetUserLevelId: {
            type: DataTypes.CHAR(36),
            allowNull: true,
            comment: '目标用户级别ID（当send_mode为user_level时使用）'
        },
        status: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
            comment: '通知状态：1-正常，0-已删除'
        }
    },
    {
        freezeTableName: true,
        tableName: 'notifications',
        timestamps: true,
        createdAt: 'createdAt',
        updatedAt: false, // 禁用 updatedAt，因为数据库表中没有这个字段
        comment: '通知表'
    });

// 导入用户模型
const User = require('./userModel');

// 定义与用户模型的关联关系
Notifications.belongsTo(User, {
    foreignKey: 'userId',
    targetKey: 'id',
    as: 'user'
});

Notifications.belongsTo(User, {
    foreignKey: 'initiatorId',
    targetKey: 'id',
    as: 'initiator'
});

// 设置与部门的关联关系
const setupDepartmentAssociation = () => {
    const departmentsModel = require('./departmentsModel');
    Notifications.belongsTo(departmentsModel, {
        foreignKey: 'targetDepartmentId',
        targetKey: 'id',
        as: 'targetDepartment'
    });
};

// 设置与用户级别的关联关系
const setupUserLevelAssociation = () => {
    const userLevelsModel = require('./userLevelsModel');
    Notifications.belongsTo(userLevelsModel, {
        foreignKey: 'targetUserLevelId',
        targetKey: 'id',
        as: 'targetUserLevel'
    });
};

// 延迟设置关联以避免循环依赖
setTimeout(() => {
    setupDepartmentAssociation();
    setupUserLevelAssociation();
}, 0);

// 移除静态方法，将查询逻辑移到控制器中

module.exports = Notifications;