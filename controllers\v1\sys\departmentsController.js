const { body, param, query, validationResult } = require('express-validator');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const { getUserInfoFromRequest } = require('@utils/others');
const departmentsModel = require('@models/v1/mapping/departmentsModel');

/**
 * 获取部门列表（分页）
 * @route GET /v1/sys/departments
 * @group 部门管理
 * @param {number} page.query - 页码
 * @param {number} pageSize.query - 每页条数
 * @param {string} departmentName.query - 部门名称（模糊搜索）
 * @param {number} status.query - 状态筛选
 * @returns {object} 200 - 成功返回部门列表
 */
exports.getDepartments = [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页条数必须是1-100之间的整数'),
    query('departmentName').optional().isString().withMessage('部门名称必须是字符串'),
    query('status').optional().isInt({ min: 0, max: 1 }).withMessage('状态必须是0或1'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const { page = 1, pageSize = 10, departmentName, status } = req.query;
            const offset = (page - 1) * pageSize;
            const limit = parseInt(pageSize);

            // 构建查询条件
            const where = {};
            if (departmentName) {
                where.departmentName = { [Op.like]: `%${departmentName}%` };
            }
            if (status !== undefined) {
                where.status = parseInt(status);
            }

            const { count, rows } = await departmentsModel.findAndCountAll({
                where,
                order: [['sort', 'ASC'], ['createdAt', 'ASC']],
                offset,
                limit
            });

            // 手动添加父级部门名称
            const departmentsWithParent = await Promise.all(rows.map(async (dept) => {
                const deptData = dept.toJSON();
                if (deptData.parentId) {
                    const parent = await departmentsModel.findByPk(deptData.parentId, {
                        attributes: ['id', 'departmentName']
                    });
                    deptData.parentName = parent ? parent.departmentName : null;
                } else {
                    deptData.parentName = null;
                }
                return deptData;
            }));

            return res.status(200).json({
                code: 200,
                message: '获取部门列表成功',
                data: {
                    list: departmentsWithParent,
                    pagination: {
                        total: count,
                        page: parseInt(page),
                        pageSize: limit,
                        totalPages: Math.ceil(count / limit)
                    }
                }
            });
        } catch (error) {
            console.error('获取部门列表失败:', error);
            return res.status(500).json({
                code: 500,
                message: '获取部门列表失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 获取所有部门（不分页，用于下拉选择）
 * @route GET /v1/sys/departments/all
 * @group 部门管理
 * @returns {object} 200 - 成功返回所有部门
 */
exports.getAllDepartments = async (req, res) => {
    try {
        const departments = await departmentsModel.findAll({
            where: { status: 1 },
            order: [['sort', 'ASC'], ['departmentName', 'ASC']],
            attributes: ['id', 'departmentName', 'departmentCode', 'parentId', 'sort']
        });

        return res.status(200).json({
            code: 200,
            message: '获取所有部门成功',
            data: departments
        });
    } catch (error) {
        console.error('获取所有部门失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取所有部门失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 获取部门树形结构
 * @route GET /v1/sys/departments/tree
 * @group 部门管理
 * @returns {object} 200 - 成功返回部门树
 */
exports.getDepartmentTree = async (req, res) => {
    try {
        const tree = await departmentsModel.getTree();
        return res.status(200).json({
            code: 200,
            message: '获取部门树成功',
            data: tree
        });
    } catch (error) {
        console.error('获取部门树失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取部门树失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 获取部门详情
 * @route GET /v1/sys/departments/:id
 * @group 部门管理
 * @param {string} id.path - 部门ID
 * @returns {object} 200 - 成功返回部门详情
 */
exports.getDepartmentDetail = [
    param('id').notEmpty().withMessage('部门ID不能为空'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const department = await departmentsModel.findByPk(req.params.id);

            if (!department) {
                return res.status(404).json({
                    code: 404,
                    message: '部门不存在',
                    data: null
                });
            }

            // 手动获取父级部门信息
            const deptData = department.toJSON();
            if (deptData.parentId) {
                const parent = await departmentsModel.findByPk(deptData.parentId, {
                    attributes: ['id', 'departmentName']
                });
                deptData.parentName = parent ? parent.departmentName : null;
            } else {
                deptData.parentName = null;
            }

            // 手动获取子部门信息
            const children = await departmentsModel.findAll({
                where: { parentId: req.params.id },
                attributes: ['id', 'departmentName', 'status']
            });
            deptData.children = children;

            return res.status(200).json({
                code: 200,
                message: '获取部门详情成功',
                data: deptData
            });
        } catch (error) {
            console.error('获取部门详情失败:', error);
            return res.status(500).json({
                code: 500,
                message: '获取部门详情失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 创建部门
 * @route POST /v1/sys/departments
 * @group 部门管理
 * @param {string} departmentName.body - 部门名称
 * @param {string} departmentCode.body - 部门代码
 * @param {string} description.body - 部门描述
 * @param {string} parentId.body - 上级部门ID
 * @param {number} sort.body - 排序
 * @returns {object} 200 - 成功创建部门
 */
exports.createDepartment = [
    body('departmentName').notEmpty().withMessage('部门名称不能为空').isLength({ max: 100 }).withMessage('部门名称不能超过100个字符'),
    body('departmentCode').optional().isLength({ max: 50 }).withMessage('部门代码不能超过50个字符'),
    body('description').optional().isLength({ max: 500 }).withMessage('部门描述不能超过500个字符'),
    body('parentId').optional().custom((value) => {
        if (value === null || value === undefined || value === '') {
            return true; // 允许空值
        }
        // 检查是否为有效的UUID格式
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(value)) {
            throw new Error('上级部门ID格式不正确');
        }
        return true;
    }),
    body('sort').optional().isInt({ min: 0 }).withMessage('排序必须是非负整数'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const { departmentName, departmentCode, description, parentId, sort = 0 } = req.body;
            const currentUser = await getUserInfoFromRequest(req);

            // 检查部门名称是否已存在
            const nameExists = await departmentsModel.validateUniqueName(departmentName);
            if (!nameExists) {
                return res.status(400).json({
                    code: 400,
                    message: '该部门名称已存在',
                    data: null
                });
            }

            // 检查部门代码是否已存在
            if (departmentCode) {
                const codeExists = await departmentsModel.validateUniqueCode(departmentCode);
                if (!codeExists) {
                    return res.status(400).json({
                        code: 400,
                        message: '该部门代码已存在',
                        data: null
                    });
                }
            }

            // 如果有上级部门，检查是否存在
            if (parentId) {
                const parentDepartment = await departmentsModel.findByPk(parentId);
                if (!parentDepartment) {
                    return res.status(400).json({
                        code: 400,
                        message: '上级部门不存在',
                        data: null
                    });
                }
            }

            const department = await departmentsModel.create({
                id: uuidv4(),
                departmentName,
                departmentCode,
                description,
                parentId,
                sort,
                status: 1,
                createdBy: currentUser.id
            });

            return res.status(200).json({
                code: 200,
                message: '创建部门成功',
                data: department
            });
        } catch (error) {
            console.error('创建部门失败:', error);
            return res.status(500).json({
                code: 500,
                message: '创建部门失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 更新部门
 * @route PUT /v1/sys/departments/:id
 * @group 部门管理
 * @param {string} id.path - 部门ID
 * @param {string} departmentName.body - 部门名称
 * @param {string} departmentCode.body - 部门代码
 * @param {string} description.body - 部门描述
 * @param {string} parentId.body - 上级部门ID
 * @param {number} sort.body - 排序
 * @param {number} status.body - 状态
 * @returns {object} 200 - 成功更新部门
 */
exports.updateDepartment = [
    param('id').notEmpty().withMessage('部门ID不能为空'),
    body('departmentName').optional().isLength({ max: 100 }).withMessage('部门名称不能超过100个字符'),
    body('departmentCode').optional().isLength({ max: 50 }).withMessage('部门代码不能超过50个字符'),
    body('description').optional().isLength({ max: 500 }).withMessage('部门描述不能超过500个字符'),
    body('parentId').optional().custom((value) => {
        if (value === null || value === undefined || value === '') {
            return true; // 允许空值
        }
        // 检查是否为有效的UUID格式
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(value)) {
            throw new Error('上级部门ID格式不正确');
        }
        return true;
    }),
    body('sort').optional().isInt({ min: 0 }).withMessage('排序必须是非负整数'),
    body('status').optional().isInt({ min: 0, max: 1 }).withMessage('状态必须是0或1'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const department = await departmentsModel.findByPk(req.params.id);
            if (!department) {
                return res.status(404).json({
                    code: 404,
                    message: '部门不存在',
                    data: null
                });
            }

            const { departmentName, departmentCode, description, parentId, sort, status } = req.body;

            // 如果修改部门名称，检查是否与其他部门重复
            if (departmentName && departmentName !== department.departmentName) {
                const nameExists = await departmentsModel.validateUniqueName(departmentName, req.params.id);
                if (!nameExists) {
                    return res.status(400).json({
                        code: 400,
                        message: '该部门名称已存在',
                        data: null
                    });
                }
            }

            // 如果修改部门代码，检查是否与其他部门重复
            if (departmentCode && departmentCode !== department.departmentCode) {
                const codeExists = await departmentsModel.validateUniqueCode(departmentCode, req.params.id);
                if (!codeExists) {
                    return res.status(400).json({
                        code: 400,
                        message: '该部门代码已存在',
                        data: null
                    });
                }
            }

            // 如果修改上级部门，检查是否存在且不能设置为自己或自己的子部门
            if (parentId && parentId !== department.parentId) {
                if (parentId === req.params.id) {
                    return res.status(400).json({
                        code: 400,
                        message: '不能将自己设置为上级部门',
                        data: null
                    });
                }

                const parentDepartment = await departmentsModel.findByPk(parentId);
                if (!parentDepartment) {
                    return res.status(400).json({
                        code: 400,
                        message: '上级部门不存在',
                        data: null
                    });
                }

                // 检查是否会形成循环引用
                const childrenIds = await departmentsModel.getAllChildrenIds(req.params.id);
                if (childrenIds.includes(parentId)) {
                    return res.status(400).json({
                        code: 400,
                        message: '不能将子部门设置为上级部门',
                        data: null
                    });
                }
            }

            const updateData = {};
            if (departmentName !== undefined) updateData.departmentName = departmentName;
            if (departmentCode !== undefined) updateData.departmentCode = departmentCode;
            if (description !== undefined) updateData.description = description;
            if (parentId !== undefined) updateData.parentId = parentId;
            if (sort !== undefined) updateData.sort = sort;
            if (status !== undefined) updateData.status = status;

            await department.update(updateData);

            return res.status(200).json({
                code: 200,
                message: '更新部门成功',
                data: department
            });
        } catch (error) {
            console.error('更新部门失败:', error);
            return res.status(500).json({
                code: 500,
                message: '更新部门失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 删除部门
 * @route DELETE /v1/sys/departments/:id
 * @group 部门管理
 * @param {string} id.path - 部门ID
 * @returns {object} 200 - 成功删除部门
 */
exports.deleteDepartment = [
    param('id').notEmpty().withMessage('部门ID不能为空'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const department = await departmentsModel.findByPk(req.params.id);
            if (!department) {
                return res.status(404).json({
                    code: 404,
                    message: '部门不存在',
                    data: null
                });
            }

            // 检查是否有子部门
            const childrenCount = await departmentsModel.count({
                where: { parentId: req.params.id }
            });

            if (childrenCount > 0) {
                return res.status(400).json({
                    code: 400,
                    message: `该部门下还有${childrenCount}个子部门，无法删除`,
                    data: null
                });
            }

            // 检查是否有用户使用此部门
            const userModel = require('@models/v1/mapping/userModel');
            const userCount = await userModel.count({
                where: { departmentId: req.params.id }
            });

            if (userCount > 0) {
                return res.status(400).json({
                    code: 400,
                    message: `该部门下还有${userCount}个用户，无法删除`,
                    data: null
                });
            }

            await department.destroy();

            return res.status(200).json({
                code: 200,
                message: '删除部门成功',
                data: null
            });
        } catch (error) {
            console.error('删除部门失败:', error);
            return res.status(500).json({
                code: 500,
                message: '删除部门失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 获取部门统计信息
 * @route GET /v1/sys/departments/statistics
 * @group 部门管理
 * @returns {object} 200 - 成功返回统计信息
 */
exports.getDepartmentStatistics = async (req, res) => {
    try {
        const userModel = require('@models/v1/mapping/userModel');

        const departments = await departmentsModel.findAll({
            where: { status: 1 },
            attributes: ['id', 'departmentName', 'sort'],
            order: [['sort', 'ASC'], ['departmentName', 'ASC']]
        });

        const statistics = await Promise.all(departments.map(async (department) => {
            const userCount = await userModel.count({
                where: { departmentId: department.id }
            });

            return {
                id: department.id,
                departmentName: department.departmentName,
                sort: department.sort,
                userCount
            };
        }));

        return res.status(200).json({
            code: 200,
            message: '获取部门统计成功',
            data: statistics
        });
    } catch (error) {
        console.error('获取部门统计失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取部门统计失败: ' + error.message,
            data: null
        });
    }
};
