const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const { getTimeIntervalByName } = require('../../../utils/others');
const conferenceModel = require('../../../models/v1/mapping/conferencesModel');
const conferenceLevelModel = require('../../../models/v1/mapping/conferencesLevelsModel');
const conferenceParticipantModel = require('../../../models/v1/mapping/conferenceParticipantsModel');

/**
 * 获取会议级别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getConferenceLevels = async (req, res) => {
  try {
    const levels = await conferenceLevelModel.findAll({
      where: { status: 1 }, // 只返回启用的级别
      order: [['createdAt', 'ASC']]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levels
    });
  } catch (error) {
    console.error('获取会议级别列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议级别列表失败',
      error: error.message
    });
  }
};

/**
 * 获取会议级别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDetail = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }

    // 查询级别详情
    const level = await conferenceLevelModel.findByPk(id);

    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到会议级别',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: level
    });
  } catch (error) {
    console.error('获取会议级别详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议级别详情失败',
      error: error.message
    });
  }
};

/**
 * 创建会议级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createLevel = async (req, res) => {
  try {
    const { levelName, description } = req.body;

    // 验证必要字段
    if (!levelName) {
      return res.status(400).json({
        code: 400,
        message: '级别名称不能为空',
        data: null
      });
    }

    // 检查级别名称是否已存在
    const existingLevel = await conferenceLevelModel.findOne({
      where: {
        levelName: levelName
      }
    });

    if (existingLevel) {
      return res.status(400).json({
        code: 400,
        message: '级别名称已存在',
        data: null
      });
    }

    // 创建会议级别
    const level = await conferenceLevelModel.create({
      id: uuidv4(),
      levelName,
      description,
      status: 1
    });

    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: level
    });
  } catch (error) {
    console.error('创建会议级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建会议级别失败',
      error: error.message
    });
  }
};

/**
 * 更新会议级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateLevel = async (req, res) => {
  try {
    const { id } = req.params;
    const { levelName, description, status } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }

    // 查询级别是否存在
    const level = await conferenceLevelModel.findByPk(id);

    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到会议级别',
        data: null
      });
    }

    // 如果要更新级别名称，检查新名称是否已存在
    if (levelName && levelName !== level.levelName) {
      const existingLevel = await conferenceLevelModel.findOne({
        where: {
          levelName: levelName,
          id: { [Op.ne]: id }
        }
      });

      if (existingLevel) {
        return res.status(400).json({
          code: 400,
          message: '级别名称已存在',
          data: null
        });
      }
    }

    // 更新级别
    const updateData = {};
    if (levelName !== undefined) updateData.levelName = levelName;
    if (description !== undefined) updateData.description = description;
    if (status !== undefined) updateData.status = status;

    await level.update(updateData);

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: await conferenceLevelModel.findByPk(id)
    });
  } catch (error) {
    console.error('更新会议级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新会议级别失败',
      error: error.message
    });
  }
};

/**
 * 删除会议级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteLevel = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }

    // 查询级别是否存在
    const level = await conferenceLevelModel.findByPk(id);

    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到会议级别',
        data: null
      });
    }

    // 检查是否有会议使用此级别
    const conferencesCount = await conferenceModel.count({
      where: { levelId: id }
    });

    if (conferencesCount > 0) {
      return res.status(400).json({
        code: 400,
        message: `该级别已被${conferencesCount}个会议使用，无法删除`,
        data: null
      });
    }

    // 删除级别
    await level.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除会议级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除会议级别失败',
      error: error.message
    });
  }
};

/**
 * 获取所有级别及其会议数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelsWithCount = async (req, res) => {
  try {
    // 获取所有级别
    const levels = await conferenceLevelModel.findAll({
      where: { status: 1 },
      order: [['createdAt', 'ASC']]
    });

    // 获取每个级别的会议数量
    const result = await Promise.all(levels.map(async (level) => {
      const count = await conferenceModel.count({
        where: { levelId: level.id }
      });

      return {
        ...level.toJSON(),
        conferenceCount: count
      };
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取级别及会议数量失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取级别及会议数量失败',
      error: error.message
    });
  }
};

/**
 * 获取会议级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus } = req.body; // range: 'in', 'out', 'all'

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("conferences");

    // 构建查询条件
    let conferenceIds = [];
    let whereCondition = {};

    // 根据审核状态构建查询条件
    if (reviewStatus) {
      if (reviewStatus === 'pending') {
        // 待审核
        whereCondition.ifReviewer = null;
      } else if (reviewStatus === 'reviewed') {
        // 已通过
        whereCondition.ifReviewer = true;
      } else if (reviewStatus === 'rejected') {
        // 已拒绝
        whereCondition.ifReviewer = false;
      }
    }

    // 如果提供了userId，只统计该用户参与的会议
    if (userId) {
      const participations = await conferenceParticipantModel.findAll({
        where: { participantId: userId },
        attributes: ['conferenceId']
      });

      conferenceIds = participations.map(p => p.conferenceId);

      if (conferenceIds.length === 0) {
        // 该用户没有参与会议
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
    }

    // 合并会议ID和审核状态条件
    if (conferenceIds.length > 0) {
      whereCondition.id = { [Op.in]: conferenceIds };
    }

    // 查询所有会议
    const conferences = await conferenceModel.findAll({
      where: whereCondition,
      include: [
        {
          model: conferenceLevelModel,
          as: 'level',
          attributes: ['id', 'levelName'],
          required: true,
        },
      ]
    });

    // 初始化级别数据
    const levelData = {};

    // 统计会议级别分布
    conferences.forEach(conference => {
      const conferenceJson = conference.toJSON();

      // 检查会议是否在时间范围内
      const isInRange = timeInterval && conferenceJson.holdTime ?
        isDateInTimeRange(conferenceJson.holdTime, timeInterval.startTime, timeInterval.endTime) :
        false;

      // 根据查询范围筛选会议
      if ((range === 'in' && isInRange) ||
        (range === 'out' && !isInRange) ||
        range === 'all') {

        const levelName = conferenceJson.level.levelName;
        levelData[levelName] = (levelData[levelName] || 0) + 1;
      }
    });

    // 转换为前端期望的格式：[{name, value}]
    const result = Object.entries(levelData).map(([name, value]) => ({ name, value }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取会议级别分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取会议级别分布数据失败',
      error: error.message
    });
  }
};

/**
 * 判断日期是否在时间范围内
 * @param {string} dateStr - 日期字符串
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isDateInTimeRange(dateStr, startDate, endDate) {
  if (!dateStr || !startDate || !endDate) return false;

  // 将日期字符串转换为日期对象
  const dateObj = new Date(dateStr);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);

  // 日期必须在开始日期和结束日期之间
  return dateObj >= startDateObj && dateObj <= endDateObj;
} 