const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义指导学生获奖级别模型
const StudentAwardGuidanceAwardLevel = sequelize.define('student_award_guidance_awardLevels', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '级别ID'
    },
    levelName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '级别名称（唯一）'
    },
    score: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        comment: '对应分数'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'student_award_guidance_awardLevels',
    timestamps: true,
    indexes: [
        {
            name: 'uk_level',
            unique: true,
            fields: ['levelName']
        }
    ]
});

module.exports = StudentAwardGuidanceAwardLevel; 