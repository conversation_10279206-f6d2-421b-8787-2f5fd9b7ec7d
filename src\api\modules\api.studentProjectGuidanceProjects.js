import request from '../server'

/**
 * 获取立项记录列表
 * @param {Object} params - 查询参数，包括userId（可选，传入则获取用户参与的立项）
 * @returns {Promise} - 请求结果
 */
export function getProjects(params) {
  return request.post('/studentProjectGuidanceProjects/list', params)
}

/**
 * 获取所有立项记录列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getAllProjects(params) {
  return request.post('/studentProjectGuidanceProjects/project-projects-all', params)
}

/**
 * 获取立项详情
 * @param {string} id - 立项ID
 * @returns {Promise} - 请求结果
 */
export function getProjectDetail(id) {
  return request.post('/studentProjectGuidanceProjects/project/detail', { id })
}

/**
 * 创建立项
 * @param {Object} data - 立项数据
 * @returns {Promise} - 请求结果
 */
export function createProject(data) {
  return request.post('/studentProjectGuidanceProjects/project/create', data)
}

/**
 * 更新立项
 * @param {Object} data - 包含id和其他更新数据的对象
 * @returns {Promise} - 请求结果
 */
export function updateProject(data) {
  return request.post('/studentProjectGuidanceProjects/project/update', data)
}

/**
 * 删除立项
 * @param {string} id - 立项ID
 * @returns {Promise} - 请求结果
 */
export function deleteProject(id) {
  return request.post('/studentProjectGuidanceProjects/project/delete', { id })
}

/**
 * 审核立项
 * @param {Object} data - 审核数据
 * @returns {Promise} - 请求结果
 */
export function reviewProject(data) {
  return request.post('/studentProjectGuidanceProjects/project/review', data)
}

/**
 * 重新提交审核
 * @param {Object} params - 包含id的参数对象
 * @returns {Promise} - 请求结果
 */
export function reapplyReview(params) {
  return request.post('/studentProjectGuidanceProjects/project/reapply', params)
}

/**
 * 导入立项数据
 * @param {File} file - 文件对象
 * @returns {Promise} - 请求结果
 */
export function importProjects(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/studentProjectGuidanceProjects/project-projects/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出立项数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function exportProjects(params) {
  return request.get('/studentProjectGuidanceProjects/project-projects/export', {
    params,
    responseType: 'blob'
  })
}

/**
 * 获取立项时间分布数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getTimeDistribution(params = {}) {
  return request.post('/studentProjectGuidanceProjects/statistics/time-distribution', params)
}

/**
 * 获取教师立项排名数据
 * @param {Object} params - 查询参数，包括range、reviewStatus和limit
 * @returns {Promise} - 请求结果
 */
export function getTeacherProjectRanking(params = {}) {
  return request.post('/studentProjectGuidanceProjects/statistics/teacher-ranking', params)
}

/**
 * 获取项目级别分布数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getLevelDistribution(params = {}) {
  return request.post('/studentProjectGuidanceProjects/statistics/level-distribution', params)
}

/**
 * 获取教师项目详情
 * @param {Object} data
 * @returns Promise
 */
export function getTeacherProjectDetails(data) {
  return request.post('/studentProjectGuidanceProjects/statistics/teacher-project-details', data)
}

/**
 * 获取项目总分统计
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getProjectTotalScore(params) {
  return request.post('/studentProjectGuidanceProjects/statistics/projects-total-score', params);
}

/**
 * 获取用户项目详情
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getUserProjectDetails(params) {
  return request.post('/studentProjectGuidanceProjects/user/details', params);
}



/**
 * 获取审核状态概览
 * @param {Object} params - 查询参数
 * @returns {Promise} - 请求结果
 */
export function getReviewStatusOverview(params) {
  return request.post('/studentProjectGuidanceProjects/statistics/review-status-overview', params)
}
