<template>
  <div class="social-service-container">
    <!-- 添加错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
    
    <a-card title="F社会服务评分管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-upload
            :customRequest="handleImport"
            :show-upload-list="false"
            :before-upload="beforeUpload"
          >
            <a-button type="primary" v-permission="'score:F:admin:update'">
              <template #icon><UploadOutlined /></template>
              导入数据
            </a-button>
          </a-upload>
          <a-button type="primary" @click="handleExport" v-permission="'score:F:admin:list'">
            <template #icon><DownloadOutlined /></template>
            导出数据
          </a-button>
          <a-button type="primary" @click="showAddModal" v-permission="showPersonalServices ? 'score:F:self:create' : 'score:F:admin:create'">
            <template #icon><PlusOutlined /></template>
            添加服务
          </a-button>
          <a-button :type="showPersonalServices ? 'default' : 'primary'" @click="togglePersonalServices">
            <template #icon><UserOutlined /></template>
            {{ showPersonalServices ? '查看全部服务' : '查看我的服务' }}
          </a-button>
        </a-space>
      </template>

      <a-alert
        message="评分说明"
        description="统计时间：上年度7月1日-本年度6月30日。根据服务类型、时长和影响力确定分值，包括成果转化、学术会议参与、学术期刊参与、科学普及、学会职务、招生与培养、学生竞赛指导、研究生参与学术会议等方面。"
        type="success"
        show-icon
        style="margin-bottom: 16px; background-color: #f0f9f4; border-color: #ccebd7; color: #52937b;"
      />

      <!-- 个人数据统计卡片，仅在个人服务视图下显示 -->
      <a-row :gutter="16" style="margin-bottom: 24px" v-if="showPersonalServices">
        <a-col :span="24">
          <a-card :bordered="false">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic 
                  title="我的总得分" 
                  :value="totalPersonalScore || 0" 
                  :precision="2"
                  :value-style="{ fontSize: '24px', color: '#3f8600' }"
                >
                  <template #prefix>
                    <trophy-outlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="8">
                <a-statistic 
                  title="服务总数" 
                  :value="personalServicesCount || 0"
                  :value-style="{ fontSize: '24px', color: '#1890ff' }"
                >
                  <template #prefix>
                    <file-outlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="8">
                <a-statistic 
                  title="最高级别" 
                  :value="highestLevel || '无'"
                  :value-style="{ fontSize: '24px', color: '#722ed1' }"
                >
                  <template #prefix>
                    <crown-outlined />
                  </template>
                </a-statistic>
              </a-col>
            </a-row>
            
            <!-- 最近服务记录 -->
            <a-divider orientation="left">最近服务记录</a-divider>
            <a-list
              size="small"
              :data-source="recentServices"
              :pagination="false"
              :locale="{emptyText: '暂无服务记录'}"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.name"
                    :description="`${typeNameMap[item.type] || item.type} | ${getLevelName(item) || '无级别'} | ${formatDate(item.startDate)}`"
                  >
                    <template #avatar>
                      <a-avatar :style="{ backgroundColor: getAvatarColor(item) }">
                        {{ getAvatarText(item) }}
                      </a-avatar>
                    </template>
                  </a-list-item-meta>
                  <div>
                    <a-tag :color="getScoreColor(item.score)">{{ item.score }}分</a-tag>
                  </div>
                </a-list-item>
              </template>
              <template #footer v-if="personalServicesCount > 5">
                <div style="text-align: center;">
                  <a-button type="link" @click="showMoreServices">查看更多</a-button>
                </div>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
      
      <!-- 统计图表区域 -->
      <template v-if="!showPersonalServices">
      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="12">
          <a-card title="服务类型分布" :bordered="false">
            <div ref="typeChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="学术会议参与分布" :bordered="false">
            <div ref="conferenceChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="12">
          <a-card title="学生竞赛指导分布" :bordered="false">
            <div ref="competitionChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="教师得分排名" :bordered="false">
            <div ref="teacherRankChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>
      </template>
      
      <!-- 个人视图图表 -->
      <template v-if="showPersonalServices">
        <a-row :gutter="16" style="margin-bottom: 24px;">
          <a-col :span="12">
            <a-card title="我的服务类型分布" :bordered="false">
              <div ref="personalTypeChartRef" style="height: 300px;"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="我的服务时间分布" :bordered="false">
              <div ref="timeDistributionChartRef" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" style="margin-bottom: 24px;">
          <a-col :span="12">
            <a-card title="我的服务得分分布" :bordered="false">
              <div ref="scoreDistributionChartRef" style="height: 300px;"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="我的历年得分趋势" :bordered="false">
              <div ref="yearScoreTrendChartRef" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>
      </template>

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="isLoading"
        rowKey="id"
        @change="handleTableChange"
        :scroll="{ x: 1400 }"
        :bordered="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <span>{{ typeNameMap[record.type] || record.type }}</span>
          </template>
          <template v-else-if="column.key === 'target'">
            <span>{{ record.target }}</span>
          </template>
          <template v-else-if="column.key === 'usernameList'">
            <span>{{ record.usernameList }}</span>
          </template>
          <template v-else-if="column.key === 'startDate'">
            <span>{{ formatDate(record.startDate) }}</span>
          </template>
          <template v-else-if="column.key === 'endDate'">
            <span>{{ formatDate(record.endDate) }}</span>
          </template>
          <template v-else-if="column.key === 'score'">
            <span v-if="isServiceInScoreRange(record)" style="font-weight: bold; color: #1890ff;">{{ formatScore(record.score) }}</span>
            <span v-else style="color: #999999;">不计分</span>
          </template>
          <template v-else-if="column.key === 'status'">
            <span>{{ record.status === 1 ? '正常' : '已删除' }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)" v-permission="showPersonalServices ? 'score:F:self:update' : 'score:F:admin:update'">编辑</a>
              <a-divider type="vertical" />
              <a @click="confirmDeleteRecord(record)" class="text-danger" v-permission="showPersonalServices ? 'score:F:self:delete' : 'score:F:admin:delete'">删除</a>
            </a-space>
          </template>
        </template>
      </a-table>

      <div class="table-footer">
        <div class="total-score">
          <span>总分：{{ totalScore.toFixed(2) }}分</span>
        </div>
      </div>

      <!-- 使用ZyModal替代a-modal -->
      <ZyModal
        v-model:show="modalVisible"
        :title="isEdit ? '编辑社会服务' : '添加社会服务'"
        :min-width="600"
        :min-height="600"
        :mask-close="true"
        @close="handleModalCancel"
      >
        <div class="service-form">
          <a-form
            :model="formState"
            :rules="rules"
            ref="formRef"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="服务名称" name="name">
              <a-input v-model:value="formState.name" placeholder="请输入服务名称" />
            </a-form-item>

            <a-form-item label="服务类型" name="type">
              <a-select v-model:value="formState.type" placeholder="请选择服务类型" @change="handleTypeChange">
                <a-select-option value="conference">学术会议参与</a-select-option>
                <a-select-option value="journal">学术期刊参与</a-select-option>
                <a-select-option value="science">科学普及</a-select-option>
                <a-select-option value="society">学会职务</a-select-option>
                <a-select-option value="admission">招生与培养</a-select-option>
                <a-select-option value="competition">学生竞赛指导</a-select-option>
                <a-select-option value="student_conference">研究生参与学术会议</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="服务对象" name="target">
              <a-input v-model:value="formState.target" placeholder="请输入服务对象" />
            </a-form-item>

            <a-form-item label="参与用户" name="usernameList">
              <a-input v-model:value="formState.usernameList" placeholder="请输入参与用户，多个用户用逗号分隔" />
            </a-form-item>

            <a-form-item label="服务时间" name="startDate">
              <a-range-picker
                v-model:value="dateRange"
                style="width: 100%"
                @change="handleDateChange"
              />
            </a-form-item>

            <a-form-item label="服务内容" name="content">
              <a-textarea
                v-model:value="formState.content"
                :rows="4"
                placeholder="请输入服务内容"
              />
            </a-form-item>

            <a-form-item label="服务成果" name="result">
              <a-textarea
                v-model:value="formState.result"
                :rows="4"
                placeholder="请输入服务成果"
              />
            </a-form-item>

            <a-form-item label="得分" name="score">
              <a-input-number
                v-model:value="formState.score"
                :min="0"
                :max="50"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="formState.status">
                <a-radio :value="1">正常</a-radio>
                <a-radio :value="0">已删除</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
              <a-space>
                <a-button type="primary" @click="handleSubmit" :loading="confirmLoading">
                  提交
                </a-button>
                <a-button @click="handleModalCancel">
                  取消
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </ZyModal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick, h } from 'vue'
import { UploadOutlined, DownloadOutlined, PlusOutlined, UserOutlined, TrophyOutlined, FileOutlined, CrownOutlined } from '@ant-design/icons-vue'
import { message, Modal, Radio, Input } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'

// 添加组件路径调试
console.log('当前组件路径:', import.meta.url)
// 修复Vue未定义的错误，不再尝试访问Vue全局变量
console.log('当前环境:', import.meta.env.MODE)

// 直接导入组件
import ZyModal from '@/components/common/ZyModal.vue'
import useUserId from '@/composables/useUserId'

// 直接导入API函数
import { 
  getSocialServices, 
  getSocialServiceDetail, 
  addSocialService, 
  updateSocialService, 
  deleteSocialService,
  importSocialServices,
  exportSocialServices,
  getServiceTypeDistribution,
  getConferenceDistribution,
  getCompetitionDistribution,
  getTeacherRanking
} from '@/api/modules/api.social-service.js'

// 添加基本的错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason)
})

// 添加调试信息
console.log('Component loading started')
console.log('Imported components:', {
  ZyModal,
  useUserId,
  echarts
})
console.log('Imported API functions:', {
  getSocialServices,
  getSocialServiceDetail,
  addSocialService,
  updateSocialService,
  deleteSocialService,
  importSocialServices,
  exportSocialServices,
  getServiceTypeDistribution,
  getConferenceDistribution,
  getCompetitionDistribution,
  getTeacherRanking
})

// 使用useUserId composable
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()
console.log('User ID composable:', { userId: userId.value, loading: loadingUserId.value, error: userIdError.value })

// 错误信息
const errorMessage = ref('')

// 表格列定义
const columns = [
  {
    title: '服务名称',
    dataIndex: 'name',
    key: 'name',
    width: '15%',
  },
  {
    title: '服务类型',
    dataIndex: 'type',
    key: 'type',
    width: '10%',
    customRender: ({ text }) => typeNameMap[text] || text
  },
  {
    title: '服务对象',
    dataIndex: 'target',
    key: 'target',
    width: '15%',
  },
  {
    title: '参与用户',
    dataIndex: 'usernameList',
    key: 'usernameList',
    width: '10%',
  },
  {
    title: '服务时间',
    dataIndex: 'startDate',
    key: 'startDate',
    width: '15%',
    customRender: ({ text, record }) => `${formatDate(text)} 至 ${formatDate(record.endDate)}`
  },
  {
    title: '服务内容',
    dataIndex: 'content',
    key: 'content',
    width: '20%',
    ellipsis: true
  },
  {
    title: '得分',
    dataIndex: 'score',
    key: 'score',
    width: '8%',
    sorter: true,
    customRender: ({ text }) => formatScore(text)
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '7%',
    customRender: ({ text }) => text === 1 ? '正常' : '已删除'
  },
  {
    title: '操作',
    key: 'action',
    width: '10%',
    fixed: 'right',
  },
]

// 服务类型名称映射
const typeNameMap = {
  'conference': '学术会议参与',
  'journal': '学术期刊参与',
  'science': '科学普及',
  'society': '学会职务',
  'admission': '招生与培养',
  'competition': '学生竞赛指导',
  'student_conference': '研究生参与学术会议'
}

// 会议级别分数映射
const conferenceScoreMap = {
  'international': {
    'oral': 10,
    'poster': 5
  },
  'national': {
    'oral': 6,
    'poster': 3
  },
  'provincial': {
    'oral': 4,
    'poster': 2
  }
}

// 研究生会议级别分数映射
const studentConferenceScoreMap = {
  'international': {
    'oral': 15,
    'poster': 8
  },
  'national': {
    'oral': 9,
    'poster': 5
  },
  'provincial': {
    'oral': 6,
    'poster': 3
  }
}

// 期刊职务分数映射
const journalPositionScoreMap = {
  'editor': 10,
  'associate_editor': 5,
  'editorial_board': 1
}

// 竞赛级别分数映射
const competitionLevelScoreMap = {
  'national': 10,
  'provincial': 6,
  'school': 4
}

// 数据源
const dataSource = ref([])
const isLoading = ref(false)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
})

// 总分计算
const totalScore = computed(() => {
  // 计算所有服务的总分（不再根据时间范围过滤）
  const sum = dataSource.value.reduce((sum, item) => {
    try {
      // 尝试将分数转换为数字
      const scoreValue = parseFloat(item.score || 0)
      return sum + (isNaN(scoreValue) ? 0 : scoreValue)
    } catch (e) {
      console.error('计算分数出错:', e, item.score)
      return sum
    }
  }, 0)
  
  // 保留两位小数
  return parseFloat(sum.toFixed(2))
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)
const formRef = ref(null)

// 表单数据
const formState = reactive({
  name: '',
  type: '',
  target: '',
  usernameList: '',
  userIdList: '',
  level: '',
  reportType: '',
  position: '',
  competitionLevel: '',
  serviceDate: null,
  content: '',
  result: '',
  score: 0,
  status: 1,
  endDate: null,
  startDate: null,
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
  target: [{ required: true, message: '请输入服务对象', trigger: 'blur' }],
  usernameList: [{ required: true, message: '请输入参与用户', trigger: 'blur' }],
  level: [{ required: true, message: '请选择会议级别', trigger: 'change' }],
  reportType: [{ required: true, message: '请选择报告类型', trigger: 'change' }],
  position: [{ required: true, message: '请选择职务', trigger: 'change' }],
  competitionLevel: [{ required: true, message: '请选择竞赛级别', trigger: 'change' }],
  serviceDate: [{ required: true, message: '请选择服务时间', trigger: 'change' }],
  content: [{ required: true, message: '请输入服务内容', trigger: 'blur' }],
}

// 图表引用
const typeChartRef = ref(null)
const conferenceChartRef = ref(null)
const competitionChartRef = ref(null)
const teacherRankChartRef = ref(null)

// 个人视图图表引用
const personalTypeChartRef = ref(null)
const timeDistributionChartRef = ref(null)
const scoreDistributionChartRef = ref(null)
const yearScoreTrendChartRef = ref(null)

// 图表实例
let typeChart = null
let conferenceChart = null
let competitionChart = null
let teacherRankChart = null

// 个人视图图表实例
let personalTypeChart = null
let timeDistributionChart = null
let scoreDistributionChart = null
let yearScoreTrendChart = null

// 在 setup 中添加日期范围值
const dateRange = ref([])

// 获取当前统计时间范围
const getStatisticalTimeRange = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  return {
    startDate: `${currentYear - 1}-07-01`,
    endDate: `${currentYear}-06-30`
  }
}

// 判断服务是否在统计时间范围内
const isServiceInTimeRange = (serviceDate, startDate, endDate) => {
  // 将日期字符串转换为日期对象进行比较
  const serviceDateObj = new Date(serviceDate)
  const startDateObj = new Date(startDate)
  const endDateObj = new Date(endDate)
  
  // 服务日期必须在统计开始日期和结束日期之间
  return serviceDateObj >= startDateObj && serviceDateObj <= endDateObj
}

// 判断服务是否在当前统计分数范围内
const isServiceInScoreRange = (record) => {
  // 不进行日期判断，所有记录都显示分数
  return true;
}

// 计算得分
const calculateScore = () => {
  let score = 0
  
  switch (formState.type) {
    case 'conference':
      if (formState.level && formState.reportType) {
        score = conferenceScoreMap[formState.level]?.[formState.reportType] || 0
      }
      break
    case 'student_conference':
      if (formState.level && formState.reportType) {
        score = studentConferenceScoreMap[formState.level]?.[formState.reportType] || 0
      }
      break
    case 'journal':
      if (formState.position) {
        score = journalPositionScoreMap[formState.position] || 0
      }
      break
    case 'competition':
      if (formState.competitionLevel) {
        score = competitionLevelScoreMap[formState.competitionLevel] || 0
      }
      break
    case 'admission':
      // 招生与培养的分数需要根据具体任务计算
      score = 0
      break
    case 'science':
    case 'society':
      // 科学普及和学会职务的分数需要根据具体成果计算
      score = 0
      break
  }
  
  // 检查服务是否在统计时间范围内
  // 修复dateRange使用，确保是正确的日期对象
  if (dateRange.value && dateRange.value.length === 2 && dateRange.value[0]) {
    try {
      // 获取开始日期作为服务日期
      const startDateStr = dateRange.value[0].format ? 
        dateRange.value[0].format('YYYY-MM-DD') : 
        dayjs(dateRange.value[0]).format('YYYY-MM-DD')
        
      const { startDate: rangeStart, endDate: rangeEnd } = getStatisticalTimeRange()
      
      if (isServiceInTimeRange(startDateStr, rangeStart, rangeEnd)) {
        formState.score = score
      } else {
        formState.score = 0
        message.warning(`该服务不在本年度统计时间范围内（${rangeStart}至${rangeEnd}），不计入分数`)
      }
    } catch (error) {
      console.error('日期处理出错:', error)
      formState.score = score // 出错时仍设置初始计算的分数
    }
  } else {
    formState.score = score
  }
}

// 处理服务类型变更
const handleTypeChange = (value) => {
  // 可以在这里添加服务类型变更时的额外逻辑
  console.log('服务类型变更为:', value)
}

// 处理日期变更
const handleDateChange = (dates) => {
  console.log('日期变更:', dates)
  
  if (dates && dates.length === 2) {
    try {
      formState.startDate = dates[0]
      formState.endDate = dates[1]
    } catch (error) {
      console.error('处理日期变更时出错:', error)
      message.error('处理日期时出错')
    }
  } else {
    formState.startDate = null
    formState.endDate = null
  }
}

// 初始化图表
const initCharts = () => {
  console.log('Initializing charts...')
  
  // 首先检查DOM元素是否存在
  if (!typeChartRef.value || !conferenceChartRef.value || !competitionChartRef.value || !teacherRankChartRef.value) {
    console.warn('某些图表DOM引用尚未准备好，将在下一个tick尝试初始化')
    
    // 使用nextTick确保DOM已经渲染
    nextTick(() => {
      console.log('在nextTick中尝试初始化图表')
      initChartsInternal()
    })
    return
  }
  
  // 实际的初始化逻辑
  initChartsInternal()
}

// 分离图表初始化内部逻辑，便于nextTick调用
const initChartsInternal = () => {
  console.log('Chart refs:', {
    typeChartRef: typeChartRef.value,
    conferenceChartRef: conferenceChartRef.value,
    competitionChartRef: competitionChartRef.value,
    teacherRankChartRef: teacherRankChartRef.value,
    personalTypeChartRef: personalTypeChartRef.value,
    timeDistributionChartRef: timeDistributionChartRef.value,
    scoreDistributionChartRef: scoreDistributionChartRef.value,
    yearScoreTrendChartRef: yearScoreTrendChartRef.value
  })
  
  // 使用try-catch分别处理每个图表的初始化，避免一个错误影响其他图表
  // 【全局视图】服务类型分布图
  if (typeChartRef.value) {
    try {
      // 如果已经初始化过，先销毁
      if (typeChart) {
        typeChart.dispose()
      }
      typeChart = echarts.init(typeChartRef.value)
      console.log('Type chart initialized successfully')
      
    const typeOption = {
      title: {
        text: '服务类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        bottom: 10,
        data: Object.values(typeNameMap)
      },
      series: [
        {
          name: '服务类型',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    }
    typeChart.setOption(typeOption)
    } catch (error) {
      console.error('Failed to initialize type chart:', error)
      typeChart = null // 清空引用以便后续重试
    }
  } else {
    console.warn('Type chart ref is not available')
  }

  // 【全局视图】学术会议参与分布图
  if (conferenceChartRef.value) {
    try {
      // 如果已经初始化过，先销毁
      if (conferenceChart) {
        conferenceChart.dispose()
      }
      conferenceChart = echarts.init(conferenceChartRef.value)
      console.log('Conference chart initialized successfully')
      
    const conferenceOption = {
      title: {
        text: '学术会议参与分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['国际会议', '全国性会议', '省部级会议'],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['口头报告', '壁报展示']
      },
      yAxis: {
        type: 'value',
        name: '参与次数'
      },
      series: [
        {
          name: '国际会议',
          type: 'bar',
          data: [0, 0]
        },
        {
          name: '全国性会议',
          type: 'bar',
          data: [0, 0]
        },
        {
          name: '省部级会议',
          type: 'bar',
          data: [0, 0]
        }
      ]
    }
    conferenceChart.setOption(conferenceOption)
    } catch (error) {
      console.error('Failed to initialize conference chart:', error)
      conferenceChart = null // 清空引用以便后续重试
    }
  } else {
    console.warn('Conference chart ref is not available')
  }

  // 【全局视图】学生竞赛指导分布图
  if (competitionChartRef.value) {
    try {
      // 如果已经初始化过，先销毁
      if (competitionChart) {
        competitionChart.dispose()
      }
      competitionChart = echarts.init(competitionChartRef.value)
      console.log('Competition chart initialized successfully')
      
    const competitionOption = {
      title: {
        text: '学生竞赛指导分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        bottom: 10,
        data: ['国家级', '省级', '校院级']
      },
      series: [
        {
          name: '竞赛级别',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    }
    competitionChart.setOption(competitionOption)
    } catch (error) {
      console.error('Failed to initialize competition chart:', error)
      competitionChart = null // 清空引用以便后续重试
    }
  } else {
    console.warn('Competition chart ref is not available')
  }

  // 【全局视图】教师得分排名图
  if (teacherRankChartRef.value) {
    try {
      // 如果已经初始化过，先销毁
      if (teacherRankChart) {
        teacherRankChart.dispose()
      }
      teacherRankChart = echarts.init(teacherRankChartRef.value)
      console.log('Teacher rank chart initialized successfully')
      
    const teacherRankOption = {
      title: {
        text: '教师得分排名',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        name: '得分'
      },
      yAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      series: [
        {
          name: '得分',
          type: 'bar',
          data: [],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }
      ]
    }
    teacherRankChart.setOption(teacherRankOption)
    } catch (error) {
      console.error('Failed to initialize teacher rank chart:', error)
      teacherRankChart = null // 清空引用以便后续重试
    }
  } else {
    console.warn('Teacher rank chart ref is not available')
  }

  // 【个人视图】我的服务类型分布图
  if (personalTypeChartRef.value) {
    try {
      // 如果已经初始化过，先销毁
      if (personalTypeChart) {
        personalTypeChart.dispose()
      }
      personalTypeChart = echarts.init(personalTypeChartRef.value)
      console.log('Personal type chart initialized successfully')
      
      const personalTypeOption = {
        title: {
          text: '我的服务类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: Object.values(typeNameMap)
        },
        series: [
          {
            name: '服务类型',
            type: 'pie',
            radius: '50%',
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              formatter: '{b}: {c} ({d}%)'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            data: []
          }
        ]
      }
      personalTypeChart.setOption(personalTypeOption)
    } catch (error) {
      console.error('Failed to initialize personal type chart:', error)
      personalTypeChart = null
    }
  } else {
    console.warn('Personal type chart ref is not available')
  }

  // 【个人视图】我的服务时间分布图
  if (timeDistributionChartRef.value) {
    try {
      // 如果已经初始化过，先销毁
      if (timeDistributionChart) {
        timeDistributionChart.dispose()
      }
      timeDistributionChart = echarts.init(timeDistributionChartRef.value)
      console.log('Time distribution chart initialized successfully')
      
      const timeDistributionOption = {
        title: {
          text: '我的服务时间分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value',
          name: '服务次数'
        },
        series: [
          {
            name: '服务次数',
            type: 'bar',
            data: [],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2378f7' },
                  { offset: 0.7, color: '#2378f7' },
                  { offset: 1, color: '#83bff6' }
                ])
              }
            }
          }
        ]
      }
      timeDistributionChart.setOption(timeDistributionOption)
    } catch (error) {
      console.error('Failed to initialize time distribution chart:', error)
      timeDistributionChart = null
    }
  } else {
    console.warn('Time distribution chart ref is not available')
  }

  // 【个人视图】我的服务得分分布图
  if (scoreDistributionChartRef.value) {
    try {
      // 如果已经初始化过，先销毁
      if (scoreDistributionChart) {
        scoreDistributionChart.dispose()
      }
      scoreDistributionChart = echarts.init(scoreDistributionChartRef.value)
      console.log('Score distribution chart initialized successfully')
      
      const scoreDistributionOption = {
        title: {
          text: '我的服务得分分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: '得分',
          nameLocation: 'end'
        },
        yAxis: {
          type: 'category',
          data: []
        },
        series: [
          {
            name: '服务得分',
            type: 'bar',
            data: [],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#ffb400' },
                { offset: 0.5, color: '#ff7300' },
                { offset: 1, color: '#ff3c00' }
              ])
            }
          }
        ]
      }
      scoreDistributionChart.setOption(scoreDistributionOption)
    } catch (error) {
      console.error('Failed to initialize score distribution chart:', error)
      scoreDistributionChart = null
    }
  } else {
    console.warn('Score distribution chart ref is not available')
  }

  // 【个人视图】我的历年得分趋势图
  if (yearScoreTrendChartRef.value) {
    try {
      // 如果已经初始化过，先销毁
      if (yearScoreTrendChart) {
        yearScoreTrendChart.dispose()
      }
      yearScoreTrendChart = echarts.init(yearScoreTrendChartRef.value)
      console.log('Year score trend chart initialized successfully')
      
      const yearScoreTrendOption = {
        title: {
          text: '我的历年得分趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}分'
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value',
          name: '得分',
          nameLocation: 'end'
        },
        series: [
          {
            name: '年度得分',
            type: 'line',
            data: [],
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: {
              width: 3
            },
            itemStyle: {
              color: '#3aa1ff'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(58,161,255,0.6)' },
                { offset: 1, color: 'rgba(58,161,255,0.1)' }
              ])
            },
            markPoint: {
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' }
              ]
            }
          }
        ]
      }
      yearScoreTrendChart.setOption(yearScoreTrendOption)
    } catch (error) {
      console.error('Failed to initialize year score trend chart:', error)
      yearScoreTrendChart = null
    }
  } else {
    console.warn('Year score trend chart ref is not available')
  }
}

// 个人服务数据统计
const totalPersonalScore = ref(0)
const personalServicesCount = ref(0)
const highestLevel = ref('无')
const recentServices = ref([])

// 计算最高级别
const calculateHighestLevel = (services) => {
  // 定义级别优先级
  const levelPriority = {
    'international': 5, // 国际级
    'national': 4,      // 国家级
    'provincial': 3,    // 省级
    'city': 2,          // 市级
    'school': 1         // 校级
  }
  
  let highestPriority = 0
  let highestLevelKey = ''
  
  services.forEach(service => {
    let levelKey = ''
    
    if (service.type === 'conference' || service.type === 'student_conference') {
      levelKey = service.level
    } else if (service.type === 'competition') {
      levelKey = service.competitionLevel
    }
    
    if (levelKey && levelPriority[levelKey] && levelPriority[levelKey] > highestPriority) {
      highestPriority = levelPriority[levelKey]
      highestLevelKey = levelKey
    }
  })
  
  // 转换为中文显示
  const levelMap = {
    'international': '国际级',
    'national': '国家级',
    'provincial': '省级',
    'city': '市级',
    'school': '校级'
  }
  
  return levelMap[highestLevelKey] || '无'
}

// 获取级别颜色
const getLevelColor = (level) => {
  const levelColorMap = {
    'international': '#722ed1', // 紫色
    'national': '#eb2f96',      // 粉红色
    'provincial': '#1890ff',    // 蓝色
    'city': '#faad14',          // 黄色
    'school': '#52c41a'         // 绿色
  }
  
  return levelColorMap[level] || '#d9d9d9'
}

// 获取服务级别名称
const getLevelName = (service) => {
  if (service.type === 'conference' || service.type === 'student_conference') {
    const levelMap = {
      'international': '国际级',
      'national': '国家级',
      'provincial': '省级'
    }
    return levelMap[service.level] || service.level
  } else if (service.type === 'competition') {
    const levelMap = {
      'national': '国家级',
      'provincial': '省级',
      'school': '校院级'
    }
    return levelMap[service.competitionLevel] || service.competitionLevel
  }
  return ''
}

// 更新个人统计数据
const updatePersonalStats = async () => {
  if (!showPersonalServices.value || !userId.value) return
  
  try {
    // 获取所有个人服务数据
    const { startDate, endDate } = getStatisticalTimeRange()
    const response = await getSocialServices({
      userId: userId.value,
      pageSize: 1000, // 大页数以获取所有记录
      page: 1
    })
    
    if (response && response.data && response.data.list) {
      const services = response.data.list
      
      // 统计总得分
      const totalScore = services.reduce((sum, service) => {
        return sum + (parseFloat(service.score) || 0)
      }, 0)
      
      totalPersonalScore.value = totalScore
      personalServicesCount.value = services.length
      highestLevel.value = calculateHighestLevel(services)
      
      // 获取最近的服务记录（最多5条）
      recentServices.value = [...services]
        .sort((a, b) => new Date(b.startDate) - new Date(a.startDate))
        .slice(0, 5)
    }
  } catch (error) {
    console.error('获取个人统计数据失败:', error)
    errorMessage.value = `获取个人统计数据失败: ${error.message || '未知错误'}`
  }
}

// 切换个人/全部服务
const showPersonalServices = ref(false)
const togglePersonalServices = () => {
  showPersonalServices.value = !showPersonalServices.value
  fetchData()
  
  // 如果切换到个人服务视图，更新个人统计数据
  if (showPersonalServices.value) {
    updatePersonalStats()
  }
  
  // 更新图表数据以反映个人/全部数据的切换
  nextTick(() => {
    updateCharts()
    message.success(`已切换至${showPersonalServices.value ? '我的' : '全部'}社会服务数据`)
  })
}

// 更新图表数据，添加更多错误处理
const updateCharts = async () => {
  // 获取统计时间范围
  const { startDate, endDate } = getStatisticalTimeRange()
  
  // 准备查询参数
  const params = {
    startDate,
    endDate,
    userId: showPersonalServices.value ? userId.value : undefined
  }
  
  console.log('Updating charts with params:', params)
  
  // 更新图表标题以区分全部/个人数据
  const titlePrefix = showPersonalServices.value ? '我的' : ''
  
  if (showPersonalServices.value) {
    // 个人视图图表更新
    // 确保个人视图图表实例存在
    if (!personalTypeChart || !timeDistributionChart || !scoreDistributionChart || !yearScoreTrendChart) {
      console.warn('个人视图图表未初始化，尝试初始化')
      try {
        initCharts()
      } catch (error) {
        console.error('初始化个人视图图表失败:', error)
        errorMessage.value = '个人视图图表初始化失败，请刷新页面重试'
        return
      }
  }
  
  try {
    // 获取服务类型分布数据
    const typeResponse = await getServiceTypeDistribution(params)
      if (typeResponse && typeResponse.data) {
        const typeData = typeResponse.data.map(item => ({
          name: typeNameMap[item.type] || item.type,
          value: item.count
        }))
        
        personalTypeChart?.setOption({
          series: [{
            data: typeData
          }]
        })
      }
      
      // 获取个人服务列表数据，用于生成其他图表
      const servicesResponse = await getSocialServices({
        ...params,
        pageSize: 1000, // 设置较大的页大小，确保获取所有数据
        page: 1
      })
      
      if (servicesResponse && servicesResponse.data && servicesResponse.data.list) {
        const servicesData = servicesResponse.data.list
        
        // 生成服务时间分布数据
        const timeDistributionData = generateTimeDistribution(servicesData)
        timeDistributionChart?.setOption({
          xAxis: {
            data: timeDistributionData.months
          },
          series: [{
            data: timeDistributionData.counts
          }]
        })
        
        // 生成服务得分分布数据
        const scoreDistributionData = generateScoreDistribution(servicesData)
        scoreDistributionChart?.setOption({
          yAxis: {
            data: scoreDistributionData.types
          },
          series: [{
            data: scoreDistributionData.scores
          }]
        })
        
        // 生成历年得分趋势数据
        const yearScoreData = generateYearScoreData(servicesData)
        yearScoreTrendChart?.setOption({
          xAxis: {
            data: yearScoreData.years
          },
          series: [{
            data: yearScoreData.scores
          }]
        })
      }
    } catch (error) {
      console.error('更新个人视图图表数据失败:', error)
      errorMessage.value = `更新个人视图图表数据失败: ${error.message || '未知错误'}`
    }
  } else {
    // 全局视图图表更新
    // 确保全局视图图表实例存在
    if (!typeChart || !conferenceChart || !competitionChart || !teacherRankChart) {
      console.warn('全局视图图表未初始化，尝试初始化')
      try {
        initCharts()
      } catch (error) {
        console.error('初始化全局视图图表失败:', error)
        errorMessage.value = '全局视图图表初始化失败，请刷新页面重试'
        return
      }
    }
    
    try {
      // 服务类型分布
      try {
        console.log('Fetching service type distribution')
        const typeResponse = await getServiceTypeDistribution(params)
        console.log('Type distribution response:', typeResponse)
    if (typeResponse && typeResponse.data) {
      const typeData = typeResponse.data.map(item => ({
        name: typeNameMap[item.type] || item.type,
        value: item.count
      }))
      
      typeChart?.setOption({
            title: {
              text: `${titlePrefix}服务类型分布`
            },
        series: [{
          data: typeData
        }]
      })
        }
      } catch (typeError) {
        console.error('Failed to get service type distribution:', typeError)
        errorMessage.value = `服务类型分布数据获取失败: ${typeError.message}`
    }
    
      // 学术会议参与分布
      try {
        console.log('Fetching conference distribution')
    const conferenceResponse = await getConferenceDistribution(params)
        console.log('Conference distribution response:', conferenceResponse)
    if (conferenceResponse && conferenceResponse.data) {
      const conferenceData = conferenceResponse.data
      conferenceChart?.setOption({
            title: {
              text: `${titlePrefix}学术会议参与分布`
            },
        series: [
          {
            name: '国际会议',
            data: [conferenceData.internationalOral || 0, conferenceData.internationalPoster || 0]
          },
          {
            name: '全国性会议',
            data: [conferenceData.nationalOral || 0, conferenceData.nationalPoster || 0]
          },
          {
            name: '省部级会议',
            data: [conferenceData.provincialOral || 0, conferenceData.provincialPoster || 0]
          }
        ]
      })
        }
      } catch (confError) {
        console.error('Failed to get conference distribution:', confError)
    }
    
      // 学生竞赛指导分布
      try {
        console.log('Fetching competition distribution')
    const competitionResponse = await getCompetitionDistribution(params)
        console.log('Competition distribution response:', competitionResponse)
    if (competitionResponse && competitionResponse.data) {
      const competitionData = competitionResponse.data.map(item => ({
        name: item.level,
        value: item.count
      }))
      
      competitionChart?.setOption({
            title: {
              text: `${titlePrefix}学生竞赛指导分布`
            },
        series: [{
          data: competitionData
        }]
      })
        }
      } catch (compError) {
        console.error('Failed to get competition distribution:', compError)
    }
    
      // 教师得分排名
      try {
        console.log('Fetching teacher ranking')
    const teacherResponse = await getTeacherRanking(params)
        console.log('Teacher ranking response:', teacherResponse)
    if (teacherResponse && teacherResponse.data) {
      const teacherData = teacherResponse.data
      
      teacherRankChart?.setOption({
            title: {
              text: '教师得分排名'
            },
        yAxis: {
              type: 'category',
          data: teacherData.map(item => item.teacherName)
        },
        series: [{
              type: 'bar',
          data: teacherData.map(item => item.score)
        }]
      })
        }
      } catch (teacherError) {
        console.error('Failed to get teacher ranking:', teacherError)
    }
  } catch (error) {
      console.error('更新全局视图图表数据失败:', error)
      errorMessage.value = `更新全局视图图表数据失败: ${error.message || '未知错误'}`
    }
  }
}

// 监听数据变化时，使用nextTick确保DOM已更新
watch(() => dataSource.value, (newValue) => {
  console.log('Data source changed:', {
    newLength: newValue.length,
    firstItem: newValue[0]
  })
  
  // 使用nextTick确保DOM已更新
  nextTick(() => {
  updateCharts()
  })
}, { deep: true })

// 改进监听窗口大小变化的处理
const handleResize = () => {
  console.log('Window resized')
  
  // 添加延迟以确保DOM已经调整完毕
  setTimeout(() => {
    // 全局视图图表大小调整
    if (typeChart) {
      try {
        typeChart.resize()
      } catch (error) {
        console.error('Type chart resize failed:', error)
      }
    }
    
    if (conferenceChart) {
      try {
        conferenceChart.resize()
      } catch (error) {
        console.error('Conference chart resize failed:', error)
      }
    }
    
    if (competitionChart) {
      try {
        competitionChart.resize()
      } catch (error) {
        console.error('Competition chart resize failed:', error)
      }
    }
    
    if (teacherRankChart) {
      try {
        teacherRankChart.resize()
      } catch (error) {
        console.error('Teacher rank chart resize failed:', error)
      }
    }
    
    // 个人视图图表大小调整
    if (personalTypeChart) {
      try {
        personalTypeChart.resize()
      } catch (error) {
        console.error('Personal type chart resize failed:', error)
      }
    }
    
    if (timeDistributionChart) {
      try {
        timeDistributionChart.resize()
      } catch (error) {
        console.error('Time distribution chart resize failed:', error)
      }
    }
    
    if (scoreDistributionChart) {
      try {
        scoreDistributionChart.resize()
      } catch (error) {
        console.error('Score distribution chart resize failed:', error)
      }
    }
    
    if (yearScoreTrendChart) {
      try {
        yearScoreTrendChart.resize()
      } catch (error) {
        console.error('Year score trend chart resize failed:', error)
      }
    }
  }, 300)
}

// 确保组件卸载时清理所有图表实例和事件监听器
onMounted(() => {
  console.log('Component mounted')
  
  // 添加一个延迟，确保DOM已经完全渲染
  setTimeout(() => {
  try {
    initCharts()
      console.log('Charts initialized with delay')
      
      // 在初始化图表后再获取数据
      fetchData().then(() => {
        console.log('Data fetched successfully')
        
        // 如果是个人服务视图，需要更新个人统计数据
        if (showPersonalServices.value) {
          updatePersonalStats()
        }
      }).catch((fetchError) => {
        console.error('Data fetching failed:', fetchError)
        errorMessage.value = `数据获取失败：${fetchError.message || '未知错误'}`
      })
  } catch (error) {
    console.error('Failed to initialize component:', error)
    errorMessage.value = '组件初始化失败，请刷新页面重试'
    }
  }, 300)
  
  // 正确的方式添加事件监听器
  window.addEventListener('resize', handleResize)
})

// 添加onUnmounted钩子清理资源
onUnmounted(() => {
  console.log('Component unmounted, cleaning up resources')
  
  // 移除事件监听器
  window.removeEventListener('resize', handleResize)
  
  // 销毁全局视图图表实例
  if (typeChart) {
    typeChart.dispose()
    typeChart = null
  }
  
  if (conferenceChart) {
    conferenceChart.dispose()
    conferenceChart = null
  }
  
  if (competitionChart) {
    competitionChart.dispose()
    competitionChart = null
  }
  
  if (teacherRankChart) {
    teacherRankChart.dispose()
    teacherRankChart = null
  }
  
  // 销毁个人视图图表实例
  if (personalTypeChart) {
    personalTypeChart.dispose()
    personalTypeChart = null
  }
  
  if (timeDistributionChart) {
    timeDistributionChart.dispose()
    timeDistributionChart = null
  }
  
  if (scoreDistributionChart) {
    scoreDistributionChart.dispose()
    scoreDistributionChart = null
  }
  
  if (yearScoreTrendChart) {
    yearScoreTrendChart.dispose()
    yearScoreTrendChart = null
  }
})

// 获取数据
const fetchData = async (params = {}) => {
  isLoading.value = true
  errorMessage.value = ''
  try {
    const queryParams = {
      page: params.current || pagination.current,
      pageSize: params.pageSize || pagination.pageSize,
      userId: showPersonalServices.value ? userId.value : undefined,
      sortField: params.sortField,
      sortOrder: params.sortOrder
    }
    
    console.log('Fetching data with params:', queryParams)
    
    try {
      const response = await getSocialServices(queryParams)
      console.log('API response:', response)
      
      if (response && response.code === 200 && response.data) {
        dataSource.value = response.data.list.map(item => ({
          ...item,
          key: item.id // 确保每行数据都有唯一的key
        }))
        pagination.total = response.data.total || 0
        
        // 添加数据调试信息
        console.log('===== 数据调试信息 =====');
        if (dataSource.value.length > 0) {
          const firstItem = dataSource.value[0];
          console.log('第一条数据示例:', firstItem);
          console.log('分数类型:', typeof firstItem.score);
          console.log('开始日期类型:', typeof firstItem.startDate);
          console.log('开始日期值:', firstItem.startDate);
          
          // 检查时间范围
          const { startDate: rangeStart, endDate: rangeEnd } = getStatisticalTimeRange();
          console.log('统计时间范围:', rangeStart, '至', rangeEnd);
          
          // 检查是否在计分范围
          const inRange = isServiceInScoreRange(firstItem);
          console.log('是否在计分范围:', inRange);
          
          // 检查日期转换
          if (firstItem.startDate) {
            const dateObj = new Date(firstItem.startDate);
            console.log('日期对象:', dateObj);
            console.log('日期对象是否有效:', !isNaN(dateObj.getTime()));
          }
        } else {
          console.log('没有数据返回');
        }
        console.log('========================');
      } else {
        console.warn('Invalid API response format:', response)
        dataSource.value = []
        pagination.total = 0
        errorMessage.value = `获取数据失败: ${response?.message || '未知错误'}`
      }
    } catch (error) {
      console.error('Data fetch error:', error)
      dataSource.value = []
      pagination.total = 0
      errorMessage.value = `获取数据失败: ${error?.message || '未知错误'}`
    }
  } catch (error) {
    console.error('Fetch data outer error:', error)
    errorMessage.value = `处理数据过程中发生错误: ${error?.message || '未知错误'}`
  } finally {
    isLoading.value = false
  }
}

// 确认删除
const confirmDeleteRecord = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDelete(record)
  })
}

// 处理删除
const handleDelete = async (record) => {
  try {
    await deleteSocialService(record.id)
    message.success('删除成功')
    fetchData()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败，请稍后重试')
  }
}

// 文件上传前的处理
const beforeUpload = (file) => {
  // 检查文件类型
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    message.error('只能上传Excel文件!')
    return false
  }
  
  // 检查文件大小
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('文件大小不能超过2MB!')
    return false
  }
  
  return true
}

// 处理导入
const handleImport = async (options) => {
  try {
    const { file } = options
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await importSocialServices(file)
    if (response.code === 200) {
      message.success('导入成功')
      fetchData() // 刷新数据
    } else {
      message.error(response.message || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败，请稍后重试')
  }
}

// 处理导出
const handleExport = async () => {
  try {
    const params = {
      userId: showPersonalServices.value ? userId.value : undefined
    }
    
    const response = await exportSocialServices(params)
    
    // 创建下载链接
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `社会服务数据_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请稍后重试')
  }
}

// 在 script setup 部分添加以下方法
const getLevelDisplay = (record) => {
  if (record.type === 'conference' || record.type === 'student_conference') {
    const levelMap = {
      'international': '国际会议',
      'national': '全国性会议',
      'provincial': '省部级会议'
    }
    const reportMap = {
      'oral': '口头报告',
      'poster': '壁报展示'
    }
    return `${levelMap[record.level] || record.level} - ${reportMap[record.reportType] || record.reportType}`
  } else if (record.type === 'journal') {
    const positionMap = {
      'editor': '主编',
      'associate_editor': '副主编',
      'editorial_board': '编委'
    }
    return positionMap[record.position] || record.position
  } else if (record.type === 'competition') {
    const levelMap = {
      'national': '国家级',
      'provincial': '省级',
      'school': '校院级'
    }
    return levelMap[record.competitionLevel] || record.competitionLevel
  }
  return record.level || '-'
}

const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD')
}

const formatScore = (score) => {
  if (score === undefined || score === null) return '0.00分'
  
  // 尝试将分数转换为数字，并格式化
  try {
    const numScore = parseFloat(score)
    if (isNaN(numScore)) return '0.00分'
    return numScore.toFixed(2) + '分'
  } catch (e) {
    console.error('格式化分数出错:', e, score)
    return '0.00分'
  }
}

// 生成服务时间分布数据
const generateTimeDistribution = (servicesData) => {
  // 按月份统计服务次数
  const monthCounts = {}
  const monthNames = []
  
  // 获取所有数据中最早和最晚日期，确定时间范围
  let earliestDate = new Date()
  let latestDate = new Date('2000-01-01')
  
  servicesData.forEach(service => {
    if (service.startDate) {
      const serviceDate = new Date(service.startDate)
      if (serviceDate < earliestDate) earliestDate = serviceDate
      if (serviceDate > latestDate) latestDate = serviceDate
    }
  })
  
  // 生成月份范围（最多显示24个月）
  const startYear = earliestDate.getFullYear()
  const startMonth = earliestDate.getMonth()
  const endYear = latestDate.getFullYear()
  const endMonth = latestDate.getMonth()
  
  let currentYear = startYear
  let currentMonth = startMonth
  
  while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {
    const monthKey = `${currentYear}-${(currentMonth + 1).toString().padStart(2, '0')}`
    monthNames.push(`${currentYear}年${currentMonth + 1}月`)
    monthCounts[monthKey] = 0
    
    currentMonth++
    if (currentMonth > 11) {
      currentMonth = 0
      currentYear++
    }
    
    // 限制最多显示24个月
    if (Object.keys(monthCounts).length >= 24) break
  }
  
  // 统计每个月的服务次数
  servicesData.forEach(service => {
    if (service.startDate) {
      const serviceDate = new Date(service.startDate)
      const year = serviceDate.getFullYear()
      const month = serviceDate.getMonth() + 1
      const monthKey = `${year}-${month.toString().padStart(2, '0')}`
      
      if (monthCounts[monthKey] !== undefined) {
        monthCounts[monthKey]++
      }
    }
  })
  
  // 转换为数组格式
  const months = []
  const counts = []
  
  Object.keys(monthCounts).forEach(key => {
    const index = Object.keys(monthCounts).indexOf(key)
    months[index] = monthNames[index]
    counts[index] = monthCounts[key]
  })
  
  return { months, counts }
}

// 生成服务得分分布数据
const generateScoreDistribution = (servicesData) => {
  // 按服务类型统计得分
  const scoreMap = {}
  
  Object.keys(typeNameMap).forEach(type => {
    scoreMap[type] = 0
  })
  
  // 统计每种类型的总得分
  servicesData.forEach(service => {
    if (service.type && service.score) {
      if (scoreMap[service.type] !== undefined) {
        scoreMap[service.type] += parseFloat(service.score) || 0
      }
    }
  })
  
  // 转换为数组格式并排序
  const types = []
  const scores = []
  
  Object.entries(scoreMap)
    .filter(([_, score]) => score > 0) // 只包含得分大于0的类型
    .sort((a, b) => b[1] - a[1]) // 按得分降序排序
    .forEach(([type, score]) => {
      types.push(typeNameMap[type] || type)
      scores.push(parseFloat(score).toFixed(2))
    })
  
  return { types, scores }
}

// 生成历年得分趋势数据
const generateYearScoreData = (servicesData) => {
  // 按年度统计得分
  const yearScores = {}
  
  // 获取过去5年的年份范围
  const currentYear = new Date().getFullYear()
  for (let i = 4; i >= 0; i--) {
    const year = currentYear - i
    yearScores[year] = 0
  }
  
  // 统计每年的总得分
  servicesData.forEach(service => {
    if (service.startDate && service.score) {
      const year = new Date(service.startDate).getFullYear()
      if (yearScores[year] !== undefined) {
        yearScores[year] += parseFloat(service.score) || 0
      }
    }
  })
  
  // 转换为数组格式
  const years = Object.keys(yearScores).map(year => `${year}年`)
  const scores = Object.values(yearScores).map(score => parseFloat(score).toFixed(2))
  
  return { years, scores }
}

// 获取头像背景颜色
const getAvatarColor = (service) => {
  let level = ''
  
  if (service.type === 'conference' || service.type === 'student_conference') {
    level = service.level
  } else if (service.type === 'competition') {
    level = service.competitionLevel
  }
  
  const colorMap = {
    'international': '#722ed1', // 紫色
    'national': '#eb2f96',      // 粉红色
    'provincial': '#1890ff',    // 蓝色
    'city': '#faad14',          // 黄色
    'school': '#52c41a'         // 绿色
  }
  
  return colorMap[level] || '#87d068'
}

// 获取头像显示文本
const getAvatarText = (service) => {
  if (service.type) {
    // 返回服务类型的首字母
    const typeText = typeNameMap[service.type] || service.type
    return typeText.charAt(0)
  }
  return 'S'
}

// 获取分数颜色
const getScoreColor = (score) => {
  const scoreNum = parseFloat(score || 0)
  
  if (scoreNum >= 20) return '#f50' // 高分橙红色
  if (scoreNum >= 10) return '#87d068' // 中高分绿色
  if (scoreNum >= 5) return '#108ee9' // 中分蓝色
  if (scoreNum > 0) return '#2db7f5' // 低分浅蓝色
  return '#d9d9d9' // 无分灰色
}

// 查看更多服务
const showMoreServices = () => {
  // 通知用户我们正在展示所有他们的服务
  message.info('正在查看您的所有服务记录')
  // 重置过滤条件，显示所有该用户的服务
  fetchData({
    current: 1,
    pageSize: pagination.pageSize,
    userId: userId.value
  })
}

// 监听usernameList的变化，同步到userIdList
watch(() => formState.usernameList, (newValue) => {
  console.log('usernameList changed, syncing to userIdList:', newValue);
  formState.userIdList = newValue;
}, { immediate: true });

// 表单提交处理函数
const handleSubmit = () => {
  // 记录初始状态
  console.log('提交前的状态 - usernameList:', formState.usernameList);
  console.log('提交前的状态 - userIdList:', formState.userIdList);
  
  if (formRef.value) {
    formRef.value.validate().then(async () => {
      confirmLoading.value = true;
      
      try {
        // 直接创建一个新对象，不使用对象展开运算符，避免引用问题
        const submitData = {
          name: formState.name,
          type: formState.type,
          target: formState.target,
          usernameList: formState.usernameList,
          userIdList: formState.usernameList, // 直接使用usernameList的值
          level: formState.level,
          reportType: formState.reportType,
          position: formState.position,
          competitionLevel: formState.competitionLevel,
          content: formState.content,
          result: formState.result,
          score: formState.score,
          status: formState.status,
          startDate: formState.startDate ? dayjs(formState.startDate).format('YYYY-MM-DD') : null,
          endDate: formState.endDate ? dayjs(formState.endDate).format('YYYY-MM-DD') : null,
        };
        
        console.log('准备提交的数据:');
        console.log('- userIdList:', submitData.userIdList);
        console.log('- usernameList:', submitData.usernameList);
        
        let response;
        if (isEdit.value) {
          console.log('更新社会服务记录, ID:', currentRecord.value.id);
          response = await updateSocialService(currentRecord.value.id, submitData);
        } else {
          console.log('添加社会服务记录');
          response = await addSocialService(submitData);
        }
        
        console.log('提交响应:', response);
        
        if (response && response.code === 200) {
          message.success(isEdit.value ? '更新成功' : '添加成功');
          modalVisible.value = false;
          fetchData();
        } else {
          message.error(response?.message || (isEdit.value ? '更新失败' : '添加失败'));
        }
      } catch (error) {
        console.error('保存失败:', error);
        message.error('保存失败，请稍后重试: ' + (error.message || '未知错误'));
      } finally {
        confirmLoading.value = false;
      }
    }).catch(validationError => {
      console.error('表单验证失败:', validationError);
      message.error('表单验证失败，请检查输入');
    });
  } else {
    message.error('表单引用不存在，无法提交');
  }
};

// 打开添加模态框
const showAddModal = () => {
  isEdit.value = false;
  currentRecord.value = null;
  
  // 重置表单
  Object.keys(formState).forEach(key => {
    formState[key] = key === 'status' ? 1 : (key === 'score' ? 0 : '');
  });
  dateRange.value = [];
  
  // 显示模态框
  modalVisible.value = true;
};

// 处理编辑
const handleEdit = async (record) => {
  isEdit.value = true;
  currentRecord.value = record;
  
  try {
    // 获取详情数据
    const response = await getSocialServiceDetail(record.id);
    
    if (response && response.code === 200 && response.data) {
      const detail = response.data;
      
      // 填充表单数据
      Object.keys(formState).forEach(key => {
        if (key in detail) {
          formState[key] = detail[key];
        }
      });
      
      // 确保userIdList和usernameList一致
      formState.userIdList = formState.usernameList;
      
      // 设置日期范围
      if (detail.startDate && detail.endDate) {
        dateRange.value = [
          dayjs(detail.startDate),
          dayjs(detail.endDate)
        ];
      } else {
        dateRange.value = [];
      }
      
      // 显示模态框
      modalVisible.value = true;
    } else {
      message.error('获取详情失败：' + (response?.message || '未知错误'));
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    message.error('获取详情失败，请稍后重试');
  }
};

// 关闭模态框
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 处理审核
const handleReview = async (record) => {
  try {
    // 创建一个ref存储审核信息
    const reviewInfo = ref({
      reviewStatus: 1, // 默认通过
      reviewComment: '', // 审核意见
      attachments: [] // 存储附件
    });
    
    // 显示加载中
    message.loading('正在加载社会服务详情...', 0.5);
    
    // 获取项目详情，包括附件
    try {
      // 获取项目详情
      const response = await getSocialServiceDetail(record.id);
      if (response && response.code === 200 && response.data) {
        // 设置附件
        reviewInfo.value.attachments = (response.data.attachments || []).map((file, index) => ({
          uid: `-${index}`,
          name: file.name || file.originalName || `附件${index + 1}`,
          status: 'done',
          url: file.url,
          response: { file: { id: file.id } },
          data: file
        }));
      }
    } catch (error) {
      console.error('获取社会服务详情失败:', error);
      // 不中断流程，继续显示审核对话框
    }
    
    // 确认审核操作
    Modal.confirm({
      title: '审核社会服务',
      content: h('div', {}, [
        h('p', { style: { marginBottom: '10px' } }, `您正在审核"${record.serviceName || record.title}"社会服务`),
        h('div', { style: { marginBottom: '10px' } }, [
          h('span', { style: { display: 'inline-block', width: '80px' } }, '审核结果：'),
          h(Radio.Group, {
            value: reviewInfo.value.reviewStatus,
            onChange: (e) => { reviewInfo.value.reviewStatus = e.target.value }
          }, () => [
            h(Radio, { value: 1 }, () => '通过'),
            h(Radio, { value: 0 }, () => '拒绝')
          ])
        ]),
        h('div', { style: { marginBottom: '15px' } }, [
          h('span', { style: { display: 'inline-block', width: '80px', verticalAlign: 'top' } }, '审核意见：'),
          h(Input.TextArea, {
            value: reviewInfo.value.reviewComment,
            onChange: (e) => { reviewInfo.value.reviewComment = e.target.value },
            rows: 3,
            placeholder: '请输入审核意见'
          })
        ]),
        // 添加附件列表
        reviewInfo.value.attachments && reviewInfo.value.attachments.length > 0 
          ? h('div', {}, [
              h('div', { style: { fontWeight: 'bold', marginBottom: '10px' } }, '相关附件:'),
              h('div', { style: { maxHeight: '200px', overflow: 'auto' } },
                h('table', { style: { width: '100%', borderCollapse: 'collapse' } }, [
                  h('thead', {}, 
                    h('tr', { style: { backgroundColor: '#f5f5f5' } }, [
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8' } }, '文件名'),
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8', width: '120px' } }, '大小'),
                      h('th', { style: { padding: '8px', textAlign: 'left', borderBottom: '1px solid #e8e8e8', width: '150px' } }, '操作')
                    ])
                  ),
                  h('tbody', {}, 
                    reviewInfo.value.attachments.map(file => 
                      h('tr', { key: file.uid, style: { borderBottom: '1px solid #e8e8e8' } }, [
                        h('td', { style: { padding: '8px', textAlign: 'left' } }, file.name),
                        h('td', { style: { padding: '8px', textAlign: 'left' } }, formatFileSize(file.data?.size || 0)),
                        h('td', { style: { padding: '8px', textAlign: 'left' } },
                          h('div', { style: { display: 'flex', gap: '8px' }}, [
                            h('a', { 
                              style: { color: '#1890ff', cursor: 'pointer' },
                              onClick: () => handlePreview(file)
                            }, '预览'),
                            h('a', { 
                              style: { color: '#1890ff', cursor: 'pointer' },
                              onClick: () => handleDownload(file)
                            }, '下载')
                          ])
                        )
                      ])
                    )
                  )
                ])
              )
            ])
          : h('div', { style: { color: '#999', marginTop: '10px' } }, '该社会服务没有附件')
      ]),
      okText: '确认',
      cancelText: '取消',
      width: 600,
      onOk: async () => {
        try {
          // 获取当前用户ID作为审核人
          await getUserId(true);
          
          if (!userId.value) {
            message.error('无法获取用户ID，请重新登录');
            return;
          }
          
          // 调用审核API
          const submitData = {
            id: record.id,
            reviewer: userId.value,
            reviewStatus: reviewInfo.value.reviewStatus,
            reviewComment: reviewInfo.value.reviewComment
          };
          
          const response = await reviewSocialService(record.id, submitData);
          
          if (response && response.code === 200) {
            message.success('审核成功');
            fetchData(); // 刷新数据
          } else {
            message.error(response?.message || '审核失败');
          }
        } catch (error) {
          console.error('审核社会服务失败:', error);
          message.error('审核社会服务失败: ' + (error.message || '未知错误'));
        }
      }
    });
  } catch (error) {
    console.error('打开审核对话框失败:', error);
    message.error('操作失败: ' + (error.message || '未知错误'));
  }
};

// 文件大小格式化
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 文件预览
const handlePreview = (file) => {
  if (file && (file.url || file.preview)) {
    previewImage.value = file.url || file.preview;
    previewVisible.value = true;
    previewTitle.value = file.name || '文件预览';
  } else {
    message.warning('无法预览该文件');
  }
};

// 文件下载
const handleDownload = (file) => {
  try {
    if (file && (file.url || file.preview)) {
      const link = document.createElement('a');
      link.href = file.url || file.preview;
      link.download = file.name || '下载文件';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.warning('无法下载该文件');
    }
  } catch (error) {
    console.error('文件下载出错:', error);
    message.error('文件下载失败');
  }
};
</script>

<style scoped>
.social-service-container {
  border: 1px solid #f0f0f0;
  margin: 24px;
}

.table-footer {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.total-score {
  font-size: 16px;
  font-weight: bold;
}

.text-danger {
  color: #ff4d4f;
}

.statistics-cards {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
}

.stat-title {
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-content {
  font-size: 18px;
  font-weight: bold;
}

.recent-service-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
  border-left: 3px solid #1890ff;
}

.recent-service-info {
  flex: 1;
}

.service-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.service-detail {
  display: flex;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.service-score {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
  padding-left: 16px;
  border-left: 1px solid #f0f0f0;
}
</style> 