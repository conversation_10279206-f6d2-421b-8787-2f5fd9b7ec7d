const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

/**
 * 扣分模型
 * @module models/v1/mapping/deductionModel
 */
const Deduction = sequelize.define('deductions', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: 'ID'
  },
  username: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '用户名'
  },
  userId: {
    type: DataTypes.CHAR(255),
    allowNull: false,
    comment: '用户ID'
  },
  deductionType: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '扣分类型'
  },
  deductionDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '扣分时间'
  },
  deductionScore: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '扣分分值'
  },
  deductionReason: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '扣分原因'
  },
  handleResult: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '处理结果'
  },
  ifReviewer: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    comment: '审核状态（0，拒审核 1，审核，null未审核）'
  },
  attachmentUrl: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '附件URL'
  },
  reviewComment: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核意见'
  },
  reviewerId: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: '审核人 ID'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    defaultValue: 1,
    comment: '状态'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'deductions',
  timestamps: true,
  indexes: [
    {
      name: 'idx_deduction_userId',
      fields: ['userId']
    },
    {
      name: 'idx_deduction_type',
      fields: ['deductionType']
    },
    {
      name: 'idx_deduction_date',
      fields: ['deductionDate']
    },
    {
      name: 'idx_deduction_status',
      fields: ['status']
    }
  ]
});

module.exports = Deduction; 