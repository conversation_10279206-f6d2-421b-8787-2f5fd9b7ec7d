<template>
  <a-modal
    :visible="visible"
    title="教学科技奖励详情"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else-if="awardDetail" class="detail-container">
      <!-- 基本信息 -->
      <a-descriptions
        title="基本信息"
        :column="2"
        bordered
        size="small"
      >
        <a-descriptions-item label="获奖名称" :span="2">
          <span class="award-name">{{ awardDetail.awardName }}</span>
        </a-descriptions-item>

        <a-descriptions-item label="奖励级别">
          <a-tag :color="getLevelColor(awardDetail.awardLevel?.score)">
            {{ awardDetail.awardLevel?.levelName }}
          </a-tag>
          <span class="score-text">{{ awardDetail.awardLevel?.score }}分</span>
        </a-descriptions-item>

        <a-descriptions-item label="获奖时间">
          {{ formatDate(awardDetail.awardTime) }}
        </a-descriptions-item>

        <a-descriptions-item label="第一负责人">
          <div class="user-info">
            <div class="user-name">{{ awardDetail.firstResponsible?.realName }}</div>
            <div class="user-number">{{ awardDetail.firstResponsible?.employeeNumber }}</div>
          </div>
        </a-descriptions-item>

        <a-descriptions-item label="系/教研室">
          {{ awardDetail.department || '-' }}
        </a-descriptions-item>

        <a-descriptions-item label="审核状态">
          <a-tag :color="getReviewStatusColor(awardDetail.ifReviewer)">
            {{ getReviewStatusText(awardDetail.ifReviewer) }}
          </a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="创建时间">
          {{ formatDateTime(awardDetail.createdAt) }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 参与人员 -->
      <a-divider orientation="left">参与人员</a-divider>

      <div v-if="awardDetail.participants && awardDetail.participants.length > 0">
        <a-table
          :columns="participantColumns"
          :data-source="awardDetail.participants"
          :pagination="false"
          size="small"
          row-key="id"
        >
          <template #participant="{ record }">
            <div class="user-info">
              <div class="user-name">{{ record.participant?.realName }}</div>
              <div class="user-number">{{ record.participant?.employeeNumber }}</div>
            </div>
          </template>

          <template #allocationRatio="{ record }">
            <span class="ratio-text">{{ record.allocationRatio }}%</span>
          </template>
        </a-table>
      </div>

      <div v-else class="no-data">
        <a-empty description="暂无参与人员" />
      </div>

      <!-- 附件信息 -->
      <a-divider orientation="left">附件信息</a-divider>

      <div v-if="awardDetail.attachmentUrl" class="attachment-section">
        <a-button type="link" @click="downloadAttachment">
          <PaperClipOutlined /> 下载附件
        </a-button>
      </div>

      <div v-else class="no-data">
        <a-empty description="暂无附件" />
      </div>

      <!-- 备注信息 -->
      <a-divider orientation="left">备注信息</a-divider>

      <div v-if="awardDetail.remark" class="remark-section">
        <p>{{ awardDetail.remark }}</p>
      </div>

      <div v-else class="no-data">
        <a-empty description="暂无备注" />
      </div>

      <!-- 审核信息 -->
      <a-divider orientation="left">审核信息</a-divider>

      <div v-if="awardDetail.ifReviewer !== 2" class="review-section">
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="审核人">
            {{ awardDetail.reviewer?.realName || '-' }}
          </a-descriptions-item>

          <a-descriptions-item label="审核时间">
            {{ formatDateTime(awardDetail.reviewTime) }}
          </a-descriptions-item>

          <a-descriptions-item label="审核意见">
            {{ awardDetail.reviewComment || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <div v-else class="no-data">
        <a-empty description="待审核" />
      </div>
    </div>

    <div v-else class="no-data">
      <a-empty description="暂无数据" />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PaperClipOutlined } from '@ant-design/icons-vue'

// 工具函数
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}
// 暂时移除API调用，使用模拟数据

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  awardId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const loading = ref(false)
const awardDetail = ref(null)

// 参与人员表格列配置
const participantColumns = [
  {
    title: '参与人员',
    key: 'participant',
    slots: { customRender: 'participant' }
  },
  {
    title: '工号',
    dataIndex: ['participant', 'employeeNumber'],
    key: 'employeeNumber'
  },
  {
    title: '分配比例',
    key: 'allocationRatio',
    slots: { customRender: 'allocationRatio' }
  }
]

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.awardId) {
    loadAwardDetail()
  }
})

// 加载奖励详情
const loadAwardDetail = async () => {
  try {
    loading.value = true
    // 使用模拟数据
    setTimeout(() => {
      awardDetail.value = {
        id: props.awardId,
        awardName: '国家级教学成果奖一等奖',
        awardLevel: { levelName: '国家级一等奖', score: 100 },
        firstResponsible: { realName: '张教授', employeeNumber: '20240001' },
        awardTime: '2024-06-01',
        department: '基础医学系',
        ifReviewer: 1,
        remark: '这是一个重要的教学成果奖励，体现了我校在教学改革方面的突出成就。',
        attachmentUrl: '/uploads/award-certificate.pdf',
        participants: [
          {
            id: '1',
            participant: { realName: '李教授', employeeNumber: '20240002' },
            allocationRatio: 30
          },
          {
            id: '2',
            participant: { realName: '王教授', employeeNumber: '20240003' },
            allocationRatio: 20
          }
        ],
        reviewer: { realName: '院长' },
        reviewTime: '2024-06-10',
        reviewComment: '符合奖励标准，同意通过。',
        createdAt: '2024-05-20'
      }
      loading.value = false
    }, 500)
  } catch (error) {
    message.error('获取详情失败')
    loading.value = false
  }
}

// 工具函数
const getLevelColor = (score) => {
  if (score >= 80) return 'red'
  if (score >= 60) return 'orange'
  if (score >= 40) return 'blue'
  if (score >= 20) return 'green'
  return 'default'
}

const getReviewStatusColor = (status) => {
  switch (status) {
    case 1: return 'success'
    case 0: return 'error'
    case 2: return 'warning'
    default: return 'default'
  }
}

const getReviewStatusText = (status) => {
  switch (status) {
    case 1: return '已审核'
    case 0: return '已拒绝'
    case 2: return '待审核'
    default: return '未知'
  }
}

// 下载附件
const downloadAttachment = () => {
  if (awardDetail.value?.attachmentUrl) {
    // 这里应该调用实际的下载接口
    window.open(awardDetail.value.attachmentUrl, '_blank')
  }
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
  awardDetail.value = null
}
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-container {
  max-height: 600px;
  overflow-y: auto;
}

.award-name {
  font-size: 16px;
  font-weight: 500;
  color: #1890ff;
}

.score-text {
  margin-left: 8px;
  color: #1890ff;
  font-weight: 500;
}

.user-info .user-name {
  font-weight: 500;
}

.user-info .user-number {
  color: #666;
  font-size: 12px;
}

.ratio-text {
  color: #1890ff;
  font-weight: 500;
}

.attachment-section,
.remark-section,
.review-section {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.remark-section p {
  margin: 0;
  line-height: 1.6;
}

.no-data {
  text-align: center;
  padding: 20px;
}
</style>
