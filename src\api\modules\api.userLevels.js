import service from '@/utils/request'

/**
 * 用户级别管理相关API
 */

/**
 * 获取用户级别列表（分页）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页条数
 * @param {string} params.levelName - 级别名称（模糊搜索）
 * @param {number} params.status - 状态筛选
 * @returns {Promise} 用户级别列表
 */
export const getUserLevels = (params) => {
  return service.get('/sys/user-levels', { params }).then(response => {
    return response
  })
}

/**
 * 获取所有用户级别（不分页，用于下拉选择）
 * @returns {Promise} 所有用户级别列表
 */
export const getAllUserLevels = () => {
  return service.get('/sys/user-levels/all').then(response => {
    return response
  })
}

/**
 * 获取用户级别详情
 * @param {string|number} id - 用户级别ID
 * @returns {Promise} 用户级别详情
 */
export const getUserLevelDetail = (id) => {
  return service.get(`/sys/user-levels/${id}`).then(response => {
    return response
  })
}

/**
 * 创建用户级别
 * @param {Object} data - 用户级别数据
 * @param {string} data.levelName - 级别名称
 * @param {string} data.description - 级别描述
 * @param {number} data.sort - 排序
 * @param {number} data.level - 级别数值
 * @returns {Promise} 创建结果
 */
export const createUserLevel = (data) => {
  return service.post('/sys/user-levels', data).then(response => {
    return response
  })
}

/**
 * 更新用户级别
 * @param {string|number} id - 用户级别ID
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
export const updateUserLevel = (id, data) => {
  return service.put(`/sys/user-levels/${id}`, data).then(response => {
    return response
  })
}

/**
 * 删除用户级别
 * @param {string|number} id - 用户级别ID
 * @returns {Promise} 删除结果
 */
export const deleteUserLevel = (id) => {
  return service.delete(`/sys/user-levels/${id}`).then(response => {
    return response
  })
}

/**
 * 获取用户级别统计信息
 * @returns {Promise} 统计信息
 */
export const getUserLevelStatistics = () => {
  return service.get('/sys/user-levels/statistics').then(response => {
    return response
  })
}

/**
 * 批量删除用户级别
 * @param {Array} ids - 用户级别ID数组
 * @returns {Promise} 删除结果
 */
export const batchDeleteUserLevels = (ids) => {
  return service.delete('/sys/user-levels/batch', { data: { ids } })
}

/**
 * 导出用户级别列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 导出结果
 */
export const exportUserLevels = (params) => {
  return service.get('/sys/user-levels/export', {
    params,
    responseType: 'blob'
  })
}
