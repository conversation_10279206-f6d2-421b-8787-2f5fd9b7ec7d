const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const { getTimeIntervalByName } = require('../../../utils/others');
const patentModel = require('../../../models/v1/mapping/patentsModel');
const patentCategoryModel = require('../../../models/v1/mapping/patentCategoriesModel');
const patentParticipantModel = require('../../../models/v1/mapping/patentParticipantsModel');

/**
 * 获取专利分类列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPatentCategories = async (req, res) => {
  try {
    const categories = await patentCategoryModel.findAll({
      where: { status: 1 }, // 只返回启用的分类
      order: [['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: categories
    });
  } catch (error) {
    console.error('获取专利分类列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利分类列表失败',
      error: error.message
    });
  }
};

/**
 * 获取专利分类详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCategoryDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少分类ID',
        data: null
      });
    }
    
    // 查询分类详情
    const category = await patentCategoryModel.findByPk(id);
    
    if (!category) {
      return res.status(404).json({
        code: 404,
        message: '未找到专利分类',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: category
    });
  } catch (error) {
    console.error('获取专利分类详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利分类详情失败',
      error: error.message
    });
  }
};

/**
 * 创建专利分类
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createCategory = async (req, res) => {
  try {
    const { categoryName, score, description } = req.body;
    
    // 验证必要字段
    if (!categoryName || score === undefined) {
      return res.status(400).json({
        code: 400,
        message: '分类名称和分数不能为空',
        data: null
      });
    }
    
    // 检查分类名称是否已存在
    const existingCategory = await patentCategoryModel.findOne({
      where: {
        categoryName: categoryName
      }
    });
    
    if (existingCategory) {
      return res.status(400).json({
        code: 400,
        message: '分类名称已存在',
        data: null
      });
    }
    
    // 创建专利分类
    const category = await patentCategoryModel.create({
      id: uuidv4(),
      categoryName,
      score,
      description,
      status: 1
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: category
    });
  } catch (error) {
    console.error('创建专利分类失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建专利分类失败',
      error: error.message
    });
  }
};

/**
 * 更新专利分类
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { categoryName, score, description, status } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少分类ID',
        data: null
      });
    }
    
    // 查询分类是否存在
    const category = await patentCategoryModel.findByPk(id);
    
    if (!category) {
      return res.status(404).json({
        code: 404,
        message: '未找到专利分类',
        data: null
      });
    }
    
    // 如果要更新分类名称，检查新名称是否已存在
    if (categoryName && categoryName !== category.categoryName) {
      const existingCategory = await patentCategoryModel.findOne({
        where: {
          categoryName: categoryName,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingCategory) {
        return res.status(400).json({
          code: 400,
          message: '分类名称已存在',
          data: null
        });
      }
    }
    
    // 更新分类
    const updateData = {};
    if (categoryName !== undefined) updateData.categoryName = categoryName;
    if (score !== undefined) updateData.score = score;
    if (description !== undefined) updateData.description = description;
    if (status !== undefined) updateData.status = status;
    
    await category.update(updateData);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: await patentCategoryModel.findByPk(id)
    });
  } catch (error) {
    console.error('更新专利分类失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新专利分类失败',
      error: error.message
    });
  }
};

/**
 * 删除专利分类
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少分类ID',
        data: null
      });
    }
    
    // 查询分类是否存在
    const category = await patentCategoryModel.findByPk(id);
    
    if (!category) {
      return res.status(404).json({
        code: 404,
        message: '未找到专利分类',
        data: null
      });
    }
    
    // 检查是否有专利使用此分类
    const patentsCount = await patentModel.count({
      where: { categoryId: id }
    });
    
    if (patentsCount > 0) {
      return res.status(400).json({
        code: 400,
        message: `该分类已被${patentsCount}项专利使用，无法删除`,
        data: null
      });
    }
    
    // 删除分类
    await category.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除专利分类失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除专利分类失败',
      error: error.message
    });
  }
};

/**
 * 获取专利分类分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCategoryDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body; // range: 'in', 'out', 'all'; reviewStatus: 'all', 'reviewed', 'reject', 'pending '
    console.log("req.body", req.body);
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("patents");
    
    // 构建查询条件
    let patentIds = [];
    let participantWhere = {};
    
    // 如果提供了userId，只统计该用户参与的专利
    if (userId) {
      participantWhere.participantId = userId;
      const participations = await patentParticipantModel.findAll({
        where: participantWhere,
        attributes: ['patentId']
      });
      
      patentIds = participations.map(p => p.patentId);
      
      if (patentIds.length === 0) {
        // 该用户没有参与专利
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
    }
    
    // 构建专利查询条件
    const patentWhere = patentIds.length > 0 ? { id: { [Op.in]: patentIds } } : {};
    
    // 根据reviewStatus参数添加查询条件
    if (reviewStatus === 'reviewed') {
      patentWhere.ifReviewer = 1; // 已通过
    } else if (reviewStatus === 'reject') {
      patentWhere.ifReviewer = 0; // 已拒绝
    } else if (reviewStatus === 'pending') {
      patentWhere.ifReviewer = { [Op.is]: null }; // 待审核
    }
    // 'all' 状态不添加筛选条件
    
    // 查询所有专利
    const patents = await patentModel.findAll({
      where: patentWhere,
      include: [
        {
          model: patentCategoryModel,
          as: 'category',
          attributes: ['id', 'categoryName'],
          required: true,
        },
      ]
    });
    
    // 初始化分类数据
    const categoryData = {};
    
    // 统计专利分类分布
    patents.forEach(patent => {
      const patentJson = patent.toJSON();
      
      // 检查专利是否在时间范围内
      const isInRange = timeInterval && patentJson.authorizationDate ? 
        isDateInTimeRange(patentJson.authorizationDate, timeInterval.startTime, timeInterval.endTime) : 
        false;
      
      // 根据查询范围筛选专利
      if ((range === 'in' && isInRange) || 
          (range === 'out' && !isInRange) || 
          range === 'all') {
        
        const categoryName = patentJson.category.categoryName;
        categoryData[categoryName] = (categoryData[categoryName] || 0) + 1;
      }
    });
    
    // 转换为前端期望的格式：[{name, value}]
    const result = Object.entries(categoryData).map(([name, value]) => ({ name, value }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取专利分类分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专利分类分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取所有分类及其专利数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCategoriesWithCount = async (req, res) => {
  try {
    // 获取所有分类
    const categories = await patentCategoryModel.findAll({
      where: { status: 1 },
      order: [['createdAt', 'ASC']]
    });
    
    // 获取每个分类的专利数量
    const result = await Promise.all(categories.map(async (category) => {
      const count = await patentModel.count({
        where: { categoryId: category.id }
      });
      
      return {
        ...category.toJSON(),
        patentCount: count
      };
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取分类及专利数量失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取分类及专利数量失败',
      error: error.message
    });
  }
};

/**
 * 判断日期是否在时间范围内
 * @param {string} dateStr - 日期字符串
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isDateInTimeRange(dateStr, startDate, endDate) {
  if (!dateStr || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const dateObj = new Date(dateStr);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 日期必须在开始日期和结束日期之间
  return dateObj >= startDateObj && dateObj <= endDateObj;
} 