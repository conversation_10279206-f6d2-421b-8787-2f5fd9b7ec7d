{"name": "servers", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "SET NODE_ENV=production&& node app.js --mode production", "dev": "SET NODE_ENV=development&& nodemon app.js --mode development", "dev:simple": "SET NODE_ENV=development&& node app.js --mode development"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "chalk": "^4.0.0", "compression": "^1.8.0", "connect-session-sequelize": "^7.1.7", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-jwt": "^8.4.1", "express-rate-limit": "^7.5.1", "express-session": "^1.17.3", "express-swagger-generator": "^1.1.17", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "log4js": "^6.9.1", "memory-cache": "^0.2.0", "module-alias": "^2.2.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.3", "node-cache": "^5.1.2", "node-cron": "^4.2.1", "nodemailer": "^6.9.7", "querystring": "^0.2.1", "request": "^2.88.2", "sanitize-html": "^2.17.0", "sequelize": "^6.35.0", "svg-captcha": "^1.4.0", "ua-parser-js": "^1.0.37", "uuid": "^9.0.1", "validator": "^13.15.15", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2"}}