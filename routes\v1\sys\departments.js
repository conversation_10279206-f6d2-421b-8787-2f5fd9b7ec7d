const express = require('express');
const departmentsController = require('../../../controllers/v1/sys/departmentsController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

const router = express.Router();

// 创建部门权限中间件函数
const departmentsPermission = (action) => createModulePermission('departments', action);

/**
 * 获取部门列表
 * @route GET /v1/sys/departments
 * @group 部门管理 - 部门相关接口
 * @param {string} departmentName.query - 部门名称（模糊搜索）
 * @param {number} status.query - 状态筛选
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @returns {object} 200 - 成功返回部门列表
 * @security JWT
 */
router.get('/', 
  authMiddleware, 
  departmentsPermission('list'), 
  departmentsController.getDepartments
);

/**
 * 获取所有部门（不分页）
 * @route GET /v1/sys/departments/all
 * @group 部门管理 - 部门相关接口
 * @returns {object} 200 - 成功返回所有部门
 * @security JWT
 */
router.get('/all', 
  authMiddleware, 
  departmentsPermission('all'), 
  departmentsController.getAllDepartments
);

/**
 * 获取部门树形结构
 * @route GET /v1/sys/departments/tree
 * @group 部门管理 - 部门相关接口
 * @returns {object} 200 - 成功返回部门树
 * @security JWT
 */
router.get('/tree', 
  authMiddleware, 
  departmentsPermission('tree'), 
  departmentsController.getDepartmentTree
);

/**
 * 获取部门统计信息
 * @route GET /v1/sys/departments/statistics
 * @group 部门管理 - 部门相关接口
 * @returns {object} 200 - 成功返回统计信息
 * @security JWT
 */
router.get('/statistics', 
  authMiddleware, 
  departmentsPermission('statistics'), 
  departmentsController.getDepartmentStatistics
);

/**
 * 获取部门详情
 * @route GET /v1/sys/departments/:id
 * @group 部门管理 - 部门相关接口
 * @param {string} id.path - 部门ID
 * @returns {object} 200 - 成功返回部门详情
 * @security JWT
 */
router.get('/:id', 
  authMiddleware, 
  departmentsPermission('detail'), 
  departmentsController.getDepartmentDetail
);

/**
 * 创建部门
 * @route POST /v1/sys/departments
 * @group 部门管理 - 部门相关接口
 * @param {string} departmentName.body - 部门名称
 * @param {string} departmentCode.body - 部门代码
 * @param {string} description.body - 部门描述
 * @param {string} parentId.body - 上级部门ID
 * @param {number} sort.body - 排序
 * @returns {object} 200 - 成功创建部门
 * @security JWT
 */
router.post('/', 
  authMiddleware, 
  departmentsPermission('create'), 
  departmentsController.createDepartment
);

/**
 * 更新部门
 * @route PUT /v1/sys/departments/:id
 * @group 部门管理 - 部门相关接口
 * @param {string} id.path - 部门ID
 * @param {string} departmentName.body - 部门名称
 * @param {string} departmentCode.body - 部门代码
 * @param {string} description.body - 部门描述
 * @param {string} parentId.body - 上级部门ID
 * @param {number} sort.body - 排序
 * @param {number} status.body - 状态
 * @returns {object} 200 - 成功更新部门
 * @security JWT
 */
router.put('/:id', 
  authMiddleware, 
  departmentsPermission('update'), 
  departmentsController.updateDepartment
);

/**
 * 删除部门
 * @route DELETE /v1/sys/departments/:id
 * @group 部门管理 - 部门相关接口
 * @param {string} id.path - 部门ID
 * @returns {object} 200 - 成功删除部门
 * @security JWT
 */
router.delete('/:id', 
  authMiddleware, 
  departmentsPermission('delete'), 
  departmentsController.deleteDepartment
);

module.exports = router;
