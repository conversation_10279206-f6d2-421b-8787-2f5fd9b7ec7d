const { Op } = require('sequelize');
const { getTimeIntervalByName } = require('../../../utils/others');
const patentModel = require('../../../models/v1/mapping/patentsModel');
const patentCategoryModel = require('../../../models/v1/mapping/patentCategoriesModel');
const patentParticipantModel = require('../../../models/v1/mapping/patentParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');

/**
 * 获取用户专利总得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserTotalScore = async (req, res) => {
  try {
    const { 
      userId, 
      range = 'all',
      page = 1, 
      pageSize = 10 
    } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '请提供用户ID',
        data: null
      });
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    let startDate, endDate;
    
    if (req.body.timeRange && req.body.timeRange.startDate && req.body.timeRange.endDate) {
      // 使用前端传入的自定义时间范围
      startDate = req.body.timeRange.startDate;
      endDate = req.body.timeRange.endDate;
    } else {
      // 使用系统默认的时间区间
      const timeInterval = await getTimeIntervalByName("patents");
      startDate = timeInterval ? timeInterval.startTime : null;
      endDate = timeInterval ? timeInterval.endTime : null;
    }
    
    // 查询用户参与的专利
    const participations = await patentParticipantModel.findAll({
      where: { participantId: userId },
      include: [
        {
          model: patentModel,
          as: 'patent',
          include: [
            {
              model: patentCategoryModel,
              as: 'category',
              attributes: ['id', 'categoryName', 'score'],
              required: true,
            }
          ],
          required: true,
        }
      ]
    });
    
    // 统计专利得分和详情
    let totalScore = 0;
    const patentDetails = [];
    
    participations.forEach(participation => {
      const patent = participation.patent;
      const category = patent.category;
      
      // 检查专利是否在时间范围内
      const isInRange = startDate && endDate && patent.authorizationDate ? 
        isDateInTimeRange(patent.authorizationDate, startDate, endDate) : 
        false;
      
      // 根据查询范围筛选专利
      if ((range === 'in' && isInRange) || (range === 'out' && !isInRange) || range === 'all') {
        const patentScore = parseFloat(category.score) || 0;
        const userRatio = participation.allocationRatio || 0;
        const userScore = patentScore * userRatio;
        
        totalScore += userScore;
        
        patentDetails.push({
          id: patent.id,
          patentName: patent.patentName,
          categoryName: category.categoryName,
          authorizationDate: patent.authorizationDate,
          conversionDate: patent.conversionDate,
          totalScore: patentScore,
          userScore: userScore,
          role: participation.isLeader === 1 ? 'leader' : 'member',
          allocationRatio: userRatio
        });
      }
    });
    
    // 按用户得分降序排序
    patentDetails.sort((a, b) => b.userScore - a.userScore);
    
    // 计算总数
    const totalPatents = patentDetails.length;
    
    // 应用分页
    const pagedPatents = patentDetails.slice(offset, offset + limit);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        userId: userId,
        totalScore: totalScore,
        list: pagedPatents,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: totalPatents,
          totalPages: Math.ceil(totalPatents / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户专利总得分失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户专利总得分失败',
      error: error.message
    });
  }
};

/**
 * 获取所有用户专利总分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllUsersTotalScore = async (req, res) => {
  try {
    const { 
      range = 'all',
      page = 1, 
      pageSize = 10,
      sortField = 'totalScore',
      sortOrder = 'desc',
      nickname = ''
    } = req.body;
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    let startDate, endDate;
    
    if (req.body.timeRange && req.body.timeRange.startDate && req.body.timeRange.endDate) {
      // 使用前端传入的自定义时间范围
      startDate = req.body.timeRange.startDate;
      endDate = req.body.timeRange.endDate;
    } else {
      // 使用系统默认的时间区间
      const timeInterval = await getTimeIntervalByName("patents");
      startDate = timeInterval ? timeInterval.startTime : null;
      endDate = timeInterval ? timeInterval.endTime : null;
    }
    
    // 构建用户查询条件
    const userWhere = {
      status: 1 // 只获取状态正常的用户
    };
    
    // 添加昵称模糊搜索
    if (nickname) {
      userWhere.nickname = { [Op.like]: `%${nickname}%` };
    }
    
    // 查询所有用户
    const users = await userModel.findAll({
      attributes: ['id', 'username', 'nickname', 'studentNumber'],
      where: userWhere
    });
    
    // 查询所有专利参与记录
    const allParticipations = await patentParticipantModel.findAll({
      include: [
        {
          model: patentModel,
          as: 'patent',
          include: [
            {
              model: patentCategoryModel,
              as: 'category',
              attributes: ['id', 'categoryName', 'score'],
              required: true,
            }
          ],
          required: true,
        }
      ]
    });
    
    // 用户总分统计
    const userScores = [];
    
    // 计算每个用户的总分和专利统计
    for (const user of users) {
      const userId = user.id;
      let totalScore = 0;
      let patentCount = 0;
      let leaderPatentCount = 0;
      
      // 过滤出该用户的参与记录
      const userParticipations = allParticipations.filter(p => p.participantId === userId);
      
      // 统计每个专利中用户的得分
      userParticipations.forEach(participation => {
        const patent = participation.patent;
        const category = patent.category;
        
        // 检查专利是否在时间范围内
        const isInRange = startDate && endDate && patent.authorizationDate ? 
          isDateInTimeRange(patent.authorizationDate, startDate, endDate) : 
          false;
        
        // 根据查询范围筛选专利
        if ((range === 'in' && isInRange) || (range === 'out' && !isInRange) || range === 'all') {
          const patentScore = parseFloat(category.score) || 0;
          const userRatio = participation.allocationRatio || 0;
          const userScore = patentScore * userRatio;
          
          totalScore += userScore;
          patentCount++;
          
          if (participation.isLeader === 1) {
            leaderPatentCount++;
          }
        }
      });
      
      // 只添加有参与专利的用户
      if (patentCount > 0) {
        userScores.push({
          userId: userId,
          userName: user.nickname || user.username,
          studentNumber: user.studentNumber || '',
          totalScore: parseFloat(totalScore.toFixed(2)),
          patentCount: patentCount,
          leaderPatentCount: leaderPatentCount
        });
      }
    }
    
    // 根据排序字段和顺序排序
    userScores.sort((a, b) => {
      if (sortField === 'totalScore') {
        return sortOrder === 'desc' ? b.totalScore - a.totalScore : a.totalScore - b.totalScore;
      } else if (sortField === 'patentCount') {
        return sortOrder === 'desc' ? b.patentCount - a.patentCount : a.patentCount - b.patentCount;
      } else if (sortField === 'leaderPatentCount') {
        return sortOrder === 'desc' ? b.leaderPatentCount - a.leaderPatentCount : a.leaderPatentCount - b.leaderPatentCount;
      }
      return 0;
    });
    
    // 计算总用户数
    const totalUsers = userScores.length;
    
    // 应用分页
    const pagedUsers = userScores.slice(offset, offset + limit);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: pagedUsers,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: totalUsers,
          totalPages: Math.ceil(totalUsers / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取所有用户专利总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有用户专利总分统计失败',
      error: error.message
    });
  }
};

/**
 * 判断日期是否在时间范围内
 * @param {string} dateStr - 日期字符串
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isDateInTimeRange(dateStr, startDate, endDate) {
  if (!dateStr || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const dateObj = new Date(dateStr);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 日期必须在开始日期和结束日期之间
  return dateObj >= startDateObj && dateObj <= endDateObj;
} 