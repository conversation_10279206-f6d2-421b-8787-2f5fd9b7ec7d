<template>
  <div class="performance-container reform-projects-container">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />

    <a-card title="教学改革项目管理" :bordered="false" class="performance-card">
      <template #extra>
        <a-space>
          <!-- <a-upload
            :customRequest="handleImport"
            :show-upload-list="false"
            :before-upload="beforeUpload"
          >
            <a-button type="primary">
              <template #icon><UploadOutlined /></template>
              导入数据
            </a-button>
          </a-upload> -->
          <a-upload
            :customRequest="handleExcelToJsonConvert"
            :show-upload-list="false"
            :before-upload="beforeExcelUpload"
            accept=".xlsx,.xls,.csv"
          >
            <a-button type="primary" v-permission="'score:teachingReformProjects:admin:update'">
              <template #icon><FileExcelOutlined /></template>
              Excel数据导入
            </a-button>
          </a-upload>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            添加项目
          </a-button>
          <a-button :type="showPersonalProjects ? 'default' : 'primary'" @click="togglePersonalProjects" v-permission="'score:teachingReformProjects:admin'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalProjects ? '查看全部项目' : '查看我的项目' }}
          </a-button>
        </a-space>
      </template>
        
        <!-- 教学改革项目填写说明区域 -->
        <a-card title="教学改革项目填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
          <a-alert
            class="mb-16"
            message="教学改革项目统计时间范围"
            :description="`统计时间：${timeRangeText || '加载中...'}`"
            type="info"
            show-icon
          />
          <div class="rule-content">
            <p><strong>填写说明：</strong></p>
            <ol class="detail-list">
              <li>教改项目的获批时间需在统计时间内</li>
              <li>项目以批件为准</li>
              <li>此表由主持人为我院的教职工填写，各承担人比例由主持人分配</li>
              <li>此表中出现的姓名全部为我院教职工</li>
              <li>分配比例请填写百分数，如：30%、50%等</li>
              <li>"级别"、"执行年限区间"已设置下拉菜单</li>
              <li>总分配比例应为100%</li>
            </ol>
          </div>
        </a-card>

        <!-- 图表区域 -->
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="审核状态分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="reviewStatusChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('reviewStatus', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="reviewStatusChartRef" id="reviewStatusChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="项目级别分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="levelChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('level', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="levelChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-bottom: 10px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="项目时间分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="timeChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('time', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="timeChartRef" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

      <!-- 教师项目数量排行 -->
      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="24">
          <a-card title="教师项目数量排行" :bordered="false">
            <template #extra>
              <a-space>
                <a-input-search
                  v-model:value="authorRankingPagination.nickname"
                  v-permission="'score:textbooks:admin:list'"
                  placeholder="用户名称"
                  style="width: 150px;"
                  @search="fetchTeacherRanking"
                  @pressEnter="fetchTeacherRanking"
                />
                <a-select
                  v-model:value="teacherRankChartRange"
                  style="width: 150px;"
                  @change="(value) => changeChartRange('teacherRank', value)"
                >
                  <a-select-option value="in">统计范围内</a-select-option>
                  <a-select-option value="out">统计范围外</a-select-option>
                  <a-select-option value="all">全部项目</a-select-option>
                </a-select>
                <a-button type="primary" @click="exportTeacherRanking" :loading="teacherRankExportLoading" v-permission="'score:teachingReformProjects:admin:list'">
                  <template #icon><DownloadOutlined /></template>
                  导出
                </a-button>
              </a-space>
            </template>
            <a-table
              :columns="teacherRankColumns"
              :data-source="teacherRankData"
              :pagination="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' ? teacherRankPagination : false"
              :loading="teacherRankLoading"
              rowKey="userId"
              :bordered="true"
              @change="handleTeacherRankTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'rank'">
                  <a-tag :color="getRankColor(record.rank)">{{ record.rank }}</a-tag>
                </template>
                <template v-else-if="column.key === 'totalProjects'">
                  <a-badge :count="record.totalProjects" :number-style="{ backgroundColor: '#52c41a' }" />
                </template>
                <template v-else-if="column.key === 'totalScore'">
                  <span style="font-weight: bold; color: #1890ff;">{{ parseFloat(record.totalScore).toFixed(2) }}分</span>
                </template>
                <template v-else-if="column.key === 'details'">
                  <a-button type="link" @click="showTeacherProjectsDetails(record)"
                  v-if="currentRole.roleAuth === 'SUPER' || currentRole.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId"
                  >
                    查看详情
                  </a-button>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>

        <!-- 搜索表单 -->
        <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
          <a-form :model="searchForm" @finish="handleSearch" layout="vertical" class="performance-form">
            <a-row :gutter="[12, 8]">
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="项目名称" name="projectName">
                  <a-input
                    v-model:value="searchForm.projectName"
                    placeholder="请输入项目名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="项目级别" name="levelId">
                  <a-select
                    v-model:value="searchForm.levelId"
                    placeholder="请选择项目级别"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option v-for="level in levelOptions" :key="level.id" :value="level.id">
                      {{ level.levelName }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="获批日期" name="dateRange">
                  <a-range-picker
                    v-model:value="searchForm.dateRange"
                    :format="'YYYY-MM-DD'"
                    style="width: 100%"
                    :placeholder="['开始日期', '结束日期']"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="审核状态" name="reviewStatus">
                  <a-select
                    v-model:value="searchForm.reviewStatus"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部状态</a-select-option>
                    <a-select-option value="reviewed">已审核</a-select-option>
                    <a-select-option value="rejected">已拒绝</a-select-option>
                    <a-select-option value="pending">待审核</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="统计范围" name="range">
                  <a-select
                    v-model:value="searchForm.range"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部范围</a-select-option>
                    <a-select-option value="in">统计范围内</a-select-option>
                    <a-select-option value="out">统计范围外</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
                <a-form-item label=" " style="margin-bottom: 0;">
                  <div class="search-actions-inline">
                    <a-button type="primary" html-type="submit" size="default">
                      <template #icon><SearchOutlined /></template>
                      搜索
                    </a-button>
                    <a-button @click="resetSearch" size="default">
                      <template #icon><ReloadOutlined /></template>
                      重置
                    </a-button>
                    <a-button type="default" @click="exportProjects" :loading="exportLoading" size="default">
                      <template #icon><DownloadOutlined /></template>
                      导出
                    </a-button>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

      <!-- 教师项目详情弹窗 -->
      <a-modal
        v-model:visible="teacherDetailsVisible"
        :title="`${'教师'}的教学改革项目详情`"
        width="1200px"
        :footer="null"
        :autofocus="false"
        :focusTriggerAfterClose="false"
        centered
      >
        <a-table
          :columns="teacherProjectDetailColumns"
          :data-source="teacherProjectDetails"
          :pagination="teacherDetailsPagination"
          :loading="teacherDetailsLoading"
          rowKey="id"
          :scroll="{ x: 1100 }"
          :bordered="true"
          @change="handleTeacherDetailsPaginationChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'levelName'">
              <div>
                <a-tag
                  v-if="record.level?.levelName"
                  :color="record.level?.levelColor || record.levelColor || 'blue'"
                  style="max-width: 100%; white-space: normal; height: auto;"
                >
                  {{ record.level?.levelName || record.levelName || '-' }}
                </a-tag>
              </div>
            </template>
            <template v-else-if="column.key === 'score'">
              <span style="font-weight: bold; color: #1890ff;">
                {{ record.level?.score || 0 }}分
              </span>
            </template>
            <template v-else-if="column.key === 'allocationRatio'">
              <span>{{ (record.allocationRatio * 100).toFixed(2) }}%</span>
            </template>
          </template>
        </a-table>
        <div style="margin-top: 16px; text-align: right; font-weight: bold;">
          总得分: {{ typeof teacherDetailsTotalScore === 'number' ? teacherDetailsTotalScore.toFixed(2) : '0.00' }}分
        </div>
      </a-modal>

        <!-- 数据表格 -->
        <div class="performance-table">
          <a-table
            id="teaching-reform-projects-main-table"
            ref="mainDataTable"
            :columns="columns"
            :data-source="dataSource"
            :pagination="pagination"
            :loading="isLoading"
            rowKey="id"
            @change="handleTableChange"
            :scroll="{ x: 1400 }"
            :bordered="true"
            class="no-hover-table main-data-table"
          >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'score'">
            <span v-if="record.score > 0" style="font-weight: bold; color: #1890ff;">{{ record.score }}分</span>
            <span v-else style="color: #999999;">不计分</span>
          </template>
          <template v-else-if="column.key === 'participants'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ formatParticipantsWithAllocation(record.participants, record) }}</span>
          </template>
          <template v-else-if="column.key === 'leader'">
            <span v-if="record.leader">
              {{ record.leader.nickname || record.leader.username }}
            </span>
            <span v-else>-</span>
          </template>
          <template v-else-if="column.key === 'projectName'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ record.projectName }}</span>
          </template>
          <template v-else-if="column.key === 'levelName'">
            <a-tag color="blue">{{ record.levelName }}</a-tag>
          </template>
          <template v-else-if="column.key === 'ifReviewer'">
            <a-tag :color="record.ifReviewer == true ? 'success' : (record.ifReviewer == false ? 'error' : 'warning')">
              {{ record.ifReviewer === 1 ? '已审核' : (record.ifReviewer === false ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'reviewComment'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ record.reviewComment }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown placement="bottomRight" :trigger="['click']">
              <template #overlay>
                <a-menu class="action-dropdown-menu">
                  <!-- 编辑选项 -->
                  <a-menu-item
                    key="edit"
                    v-if="record.ifReviewer != 1 && hasPerms(showPersonalProjects ? 'score:teachingReformProjects:self:update' : 'score:teachingReformProjects:admin:update')"
                  >
                    <a @click="handleEdit(record)" class="action-menu-item">
                      <EditOutlined />
                      <span>编辑</span>
                    </a>
                  </a-menu-item>

                  <!-- 重新提交审核选项 -->
                  <a-menu-item
                    key="resubmit"
                    v-if="record.ifReviewer === false && hasPerms('score:teachingReformProjects:self:reapply')"
                  >
                    <a @click="handleResubmit(record)" class="action-menu-item">
                      <ReloadOutlined />
                      <span>重新提交审核</span>
                    </a>
                  </a-menu-item>

                  <!-- 审核选项 - 仅管理员视图显示 -->
                  <a-menu-item
                    key="review"
                    v-if="!showPersonalProjects && !record.ifReviewer && hasPerms('score:teachingReformProjects:admin:review')"
                  >
                    <a @click="handleReview(record)" class="action-menu-item">
                      <AuditOutlined />
                      <span>审核</span>
                    </a>
                  </a-menu-item>

                  <a-menu-divider v-if="record.ifReviewer != 1 || (!showPersonalProjects && !record.ifReviewer)" />

                  <!-- 删除选项 -->
                  <a-menu-item
                    key="delete"
                    v-if="hasPerms(showPersonalProjects ? 'score:teachingReformProjects:self:delete' : 'score:teachingReformProjects:admin:delete')"
                  >
                    <a @click="confirmDelete(record)" class="action-menu-item text-danger">
                      <DeleteOutlined />
                      <span>删除</span>
                    </a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small" class="action-trigger-btn">
                操作 <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
          </a-table>
        </div>

        <div class="table-footer">
          <div class="total-score">
            <span>总分：{{ totalScore.toFixed(2) }}分</span>
          </div>
        </div>
      </a-card>

    <!-- 添加/编辑项目模态框 -->
    <ZyModal
      :show="modalVisible"
      :title="isEdit ? '编辑教学改革项目' : '添加教学改革项目'"
      :min-width="600"
      :min-height="400"
      @close="handleModalCancel"
    >
      <div class="project-form">
        <a-form 
          ref="formRef" 
          :model="formState" 
          :rules="rules" 
          :label-col="{ span: 6 }" 
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item name="projectName" label="项目名称">
            <a-input v-model:value="formState.projectName" placeholder="请输入项目名称" />
          </a-form-item>
          
          <a-form-item name="projectNumber" label="项目编号">
            <a-input v-model:value="formState.projectNumber" placeholder="请输入项目编号" />
          </a-form-item>
          
          <a-form-item name="approvalDepartment" label="下达部门">
            <a-input v-model:value="formState.approvalDepartment" placeholder="请输入下达部门" />
          </a-form-item>
          
          <a-form-item name="approvalDate" label="批准日期">
            <a-date-picker 
              v-model:value="formState.approvalDate" 
              style="width: 100%;"
              format="YYYY-MM-DD"
              placeholder="请选择批准日期"
            />
          </a-form-item>
          
          <a-form-item name="approvalFund" label="批准经费">
            <a-input-number 
              v-model:value="formState.approvalFund" 
              style="width: 100%;" 
              :min="0"
              placeholder="请输入批准经费"
            />
          </a-form-item>
          
          <a-form-item name="levelId" label="项目级别">
            <a-select
              v-model:value="formState.levelId"
              placeholder="请选择项目级别"
              style="width: 100%;"
            >
              <a-select-option v-for="level in levelOptions" :key="level.id" :value="level.id">
                {{ level.levelName }} ({{ level.score }}分)
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item name="startYear" label="执行起始年">
            <a-input-number 
              v-model:value="formState.startYear" 
              style="width: 100%;" 
              :min="1900"
              :max="2100"
              placeholder="请输入执行起始年"
            />
          </a-form-item>

          <a-form-item name="endYear" label="执行结束年">
            <a-input-number 
              v-model:value="formState.endYear" 
              style="width: 100%;" 
              :min="formState.startYear || 1900"
              :max="2100"
              placeholder="请输入执行结束年"
            />
          </a-form-item>

          <a-form-item name="participants" label="参与者" required>
            <a-form-item-rest>
              <div class="members-container">
                <!-- 成员添加区域 -->
                <a-row :gutter="8" style="margin-bottom: 8px;">
                  <a-col :span="12">
                    <a-select
                      v-model:value="currentMember.displayName"
                      placeholder="请选择参与人员"
                      :filter-option="false"
                      show-search
                      allow-clear
                      :loading="participantSearchLoading"
                      @search="fetchParticipants"
                      :not-found-content="participantSearchLoading ? undefined : '未找到匹配结果'"
                      @change="handleCurrentMemberChange"
                    >
                      <a-select-option v-for="(option, index) in participantOptions" :key="option.id || index" :value="option.nickname || option.username" :data="option">
                        {{ option.nickname || option.username || '未知' }} ({{ option.studentNumber || '无工号' }})
                      </a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="8">
                    <a-input-number
                      v-model:value="currentMember.allocationRatio"
                      placeholder="分配比例(%)"
                      :min="0"
                      :max="100"
                      :step="1"
                      :precision="1"
                      style="width: 100%"
                      addon-after="%"
                      @change="() => validateTotalAllocation()"
                    />
                  </a-col>
                  <a-col :span="4">
                    <a-button type="primary" @click="addParticipant">
                      <template #icon><PlusOutlined /></template>
                      添加
                    </a-button>
                  </a-col>
                </a-row>
                
                <!-- 已添加成员列表 -->
                <a-divider v-if="formState.participants && formState.participants.length > 0" style="margin: 8px 0">已添加参与人员</a-divider>
                <div v-if="formState.participants && formState.participants.length > 0">
                  <p style="color: #666; font-size: 12px; margin-bottom: 8px;">已添加 {{ formState.participants.length }} 位参与人员</p>
                  <a-list 
                    :data-source="formState.participants" 
                    size="small"
                    bordered
                  >
                    <template #renderItem="{ item, index }">
                      <a-list-item>
                        <a-row style="width: 100%">
                          <a-col :span="8">
                            {{ item.nickname || item.username || item.participantId }}
                          </a-col>
                          <a-col :span="4">
                            <a-switch 
                              :checked="item.isLeader" 
                              @change="(checked) => toggleLeader(index, checked)"
                              checkedChildren="主持人" 
                              unCheckedChildren="参与者"
                            />
                          </a-col>
                          <a-col :span="8">
                            <a-input-number
                              v-model:value="item.allocationRatio"
                              :min="0"
                              :max="100"
                              :step="1"
                              :precision="1"
                              style="width: 90%"
                              addon-after="%"
                              @change="validateTotalAllocation"
                            />
                          </a-col>
                          <a-col :span="4" style="text-align: right">
                            <a-button type="link" danger @click="() => removeParticipant(index)">删除</a-button>
                          </a-col>
                        </a-row>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>
                <div v-else style="color: #999; text-align: center; padding: 10px; border: 1px dashed #ddd; border-radius: 4px;">
                  还没有添加参与人员，请先选择参与人员并点击"添加"按钮
                </div>
              </div>

              <div v-if="formState.participants.length > 0" style="margin-top: 8px; color: #ff4d4f;">
                {{ allocationMsg }}
              </div>
            </a-form-item-rest>
          </a-form-item>

          <a-form-item name="remark" label="备注">
            <a-textarea 
              v-model:value="formState.remark" 
              placeholder="请输入备注信息" 
              :rows="3" 
            />
          </a-form-item>
          
          <!-- 文件上传 -->
          <a-form-item name="files" label="相关附件">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
              <a-upload
                v-model:file-list="fileList"
                :customRequest="handleFileUpload"
                :before-upload="beforeUpload"
                multiple
                :show-upload-list="false"
              >
                <a-button type="primary">
                  <template #icon><UploadOutlined /></template>
                  选择文件
                </a-button>
              </a-upload>
              <span style="margin-left: 16px; color: #666; font-size: 12px;">
                支持上传文档、图片或压缩文件，单个文件不超过10MB
              </span>
            </div>
            
            <!-- 已上传的文件列表 -->
            <a-table
              :columns="fileColumns"
              :data-source="fileList"
              :pagination="false"
              size="small"
              style="margin-top: 8px;"
              rowKey="uid"
              v-if="fileList.length > 0"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fileName'">
                  <span :title="record.name">{{ record.name }}</span>
                </template>
                <template v-if="column.key === 'fileSize'">
                  {{ formatFileSize(record.size) }}
                </template>
                <template v-if="column.key === 'status'">
                  <div v-if="record.status === 'uploading'">
                    <a-progress :percent="record.percent || 0" size="small" />
                  </div>
                  <a-tag v-else :color="record.status === 'done' ? 'success' : (record.status === 'error' ? 'error' : 'processing')">
                    {{ record.status === 'done' ? '已上传' : (record.status === 'error' ? '上传失败' : '上传中') }}
                  </a-tag>
                </template>
                <template v-if="column.key === 'action'">
                  <a-space>
                    <a-button type="link" size="small" @click="previewFile(record)" v-if="record.status === 'done'">
                      <template #icon><EyeOutlined /></template>
                      预览
                    </a-button>
                    <a-button type="link" size="small" @click="downloadFile(record)" v-if="record.status === 'done'">
                      <template #icon><DownloadOutlined /></template>
                      下载
                    </a-button>
                    <a-popconfirm
                      title="确定要删除该文件吗？"
                      @confirm="confirmDeleteFile(record)"
                      okText="确认"
                      cancelText="取消"
                    >
                      <a-button type="link" danger size="small">
                        <template #icon><DeleteOutlined /></template>
                        删除
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
            
                      <!-- 显示已有的文件 -->
          <a-divider v-if="fileList.length > 0 && existingFileList.length > 0" style="margin: 12px 0" />
          
          <a-table
            :columns="fileColumns"
            :data-source="existingFileList"
            :pagination="false"
            size="small"
            style="margin-top: 8px;"
            rowKey="id"
            v-if="existingFileList.length > 0"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'fileName'">
                <span :title="record.originalName">{{ record.originalName || record.fileName }}</span>
              </template>
              <template v-if="column.key === 'fileSize'">
                {{ formatFileSize(record.size) }}
              </template>
              <template v-if="column.key === 'status'">
                <a-tag color="success">已上传</a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="previewFile(record)">
                    <template #icon><EyeOutlined /></template>
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="previewFile(record)">
                    <template #icon><DownloadOutlined /></template>
                    下载
                  </a-button>
                  <a-popconfirm
                    title="确定要删除该文件吗？"
                    @confirm="() => removeExistingFile(record.id)"
                    okText="确认"
                    cancelText="取消"
                  >
                    <a-button type="link" danger size="small">
                      <template #icon><DeleteOutlined /></template>
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
          </a-form-item>
          
          <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
            <a-space>
              <a-button type="primary" @click="handleModalOk" :loading="confirmLoading">
                提交
              </a-button>
              <a-button @click="handleModalCancel">
                取消
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
    </ZyModal>

    <ProjectReviewModal
      :visible="reviewModalVisible"
      :record="currentReviewRecord"
      :attachments="reviewAttachments"
      @cancel="reviewModalVisible = false"
      @submit="handleReviewSubmit"
    />

    <!-- 项目导入结果模态框 -->
    <a-modal
      title="项目导入结果"
      :visible="importResultVisible"
      @cancel="() => importResultVisible = false"
      :footer="null"
      width="800px"
      :maskClosable="false"
    >
      <a-result
        :status="importResults.failed > 0 ? 'warning' : 'success'"
        :title="importResults.failed > 0 ? '部分导入成功' : '导入完成'"
        :sub-title="`总共导入: ${importResults.total}条，成功: ${importResults.success}条，失败: ${importResults.failed}条`"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="importResultVisible = false">
              确定
            </a-button>
            <a-button 
              type="primary" 
              danger 
              v-if="importResults.failed > 0" 
              @click="exportFailedRecords"
            >
              <template #icon><DownloadOutlined /></template>
              导出失败记录
            </a-button>
          </a-space>
        </template>
      </a-result>
      
      <a-divider />
      
      <a-list
        itemLayout="horizontal"
        :dataSource="importResults.details"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta
              :title="item.projectName"
              :description="item.message"
            >
              <template #avatar>
                <a-badge :status="item.status === 'success' ? 'success' : 'error'" />
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-modal>

    <!-- 项目导入预览模态框 -->
    <a-modal
      title="项目导入预览"
      :visible="importPreviewVisible"
      :width="1200"
      :maskClosable="false"
      :footer="null"
      @cancel="handleCancelImportPreview"
    >
      <div style="margin-bottom: 16px;">
        <a-alert type="info" show-icon>
          <template #message>共发现 {{ importPreviewData.length }} 条待导入数据，请确认后开始导入</template>
          <template #description>
            导入前请确认数据格式是否正确，红色标记表示未找到有效的用户ID，这些记录可能会导入失败
          </template>
        </a-alert>
      </div>
      
      <div v-if="userIdCheckResults.notFound > 0" style="margin-bottom: 16px;">
        <a-alert type="warning" show-icon>
          <template #message>存在用户ID检查问题</template>
          <template #description>
            发现 {{ userIdCheckResults.notFound }} 条记录中的用户无法找到对应ID，这可能导致导入失败。
          </template>
        </a-alert>
      </div>
      
      <div style="margin-bottom: 16px;" v-if="importInProgress">
        <a-progress :percent="Math.round((importResults.current / importResults.total) * 100)" />
        <p>正在导入第 {{ importResults.current }} 条，共 {{ importResults.total }} 条</p>
      </div>
      
      <div v-if="!importInProgress">
        <a-table
          :columns="[
            { title: '序号', dataIndex: 'index', width: 60, fixed: 'left' },
            { title: '项目名称', dataIndex: 'projectName', ellipsis: true, width: 200, fixed: 'left' },
            { title: '项目编号', dataIndex: 'projectNumber', width: 150 },
            { title: '审批部门', dataIndex: 'approvalDepartment', width: 120 },
            { title: '批准日期', dataIndex: 'approvalDate', width: 120 },
            { title: '批准经费', dataIndex: 'approvalFund', width: 100 },
            { title: '级别', dataIndex: 'levelName', width: 150 },
            { title: '执行起始年', dataIndex: 'startYear', width: 100 },
            { title: '执行结束年', dataIndex: 'endYear', width: 100 },
            { title: '备注', dataIndex: 'remark', width: 150 },
            { title: '参与者信息', dataIndex: 'participants', width: 250,
              customRender: ({ record }) => {
                const participants = record.participants;
                if (participants && participants.length) {
                  return h('div', { style: 'max-height: 100px; overflow-y: auto;' }, 
                    participants.map((p, idx) => {
                      return h('div', { 
                        key: idx, 
                        style: p.isLeader ? 'font-weight: bold; color: #1890ff;' : '' 
                      }, [
                        `${p.name || '未命名'} ` +
                        `${p.isLeader ? '(主持人)' : ''}` +
                        `${p.allocationRatio ? `/分配比例:${p.allocationRatio}` : ''}`
                      ]);
                    })
                  );
                }
                return '-';
              }
            },
            { title: '用户ID状态', dataIndex: 'userIdStatus', width: 120, fixed: 'right',
              customRender: ({ record }) => {
                if (record.userIdCheckStatus === 'checking') {
                  return h('span', { style: { color: '#1890ff' } }, [
                    h(LoadingOutlined, { style: { marginRight: '8px' } }),
                    '检查中...'
                  ]);
                } else if (record.userIdCheckStatus === 'found') {
                  return h('span', { style: { color: '#52c41a' } }, [
                    h(CheckOutlined, { style: { marginRight: '8px' } }),
                    '已找到'
                  ]);
                } else {
                  return h('span', { style: { color: '#f5222d' } }, [
                    h(CloseOutlined, { style: { marginRight: '8px' } }),
                    '未找到'
                  ]);
                }
              }
            }
          ]"
          :dataSource="importPreviewData.map((item, index) => ({ 
            ...item, 
            index: index + 1,
            key: index
          }))"
          :pagination="{ pageSize: 40 }"
          size="small"
          :scroll="{ x: 1800, y: 600 }"
          :loading="importPreviewLoading"
          :rowClassName="(record) => record.userIdCheckStatus === 'notFound' ? 'error-row' : ''"
        />

        <a-typography-text type="secondary" style="display: block; margin-top: 8px;">
          注意: 标记为红色的行表示未找到用户ID，请检查用户名是否正确。
        </a-typography-text>
      </div>
      
      <div style="margin-top: 16px; text-align: right;">
        <a-space>
          <a-button @click="handleCancelImportPreview">取消</a-button>
          <a-button type="primary" @click="handleDownloadJson" :disabled="lastConvertedExcelData.length === 0">
            <template #icon><DownloadOutlined /></template>
            下载JSON
          </a-button>
          <a-button type="primary" @click="handleStartImport" :loading="importInProgress" :disabled="importPreviewData.length === 0">
            开始导入
          </a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 存储最近转换的Excel数据 -->
    <div ref="lastConvertedExcelDataRef" style="display: none;"></div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  UploadOutlined,
  DownloadOutlined,
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  UserOutlined,
  EyeOutlined,
  DeleteOutlined,
  FileExcelOutlined,
  CheckOutlined,
  CloseOutlined,
  LoadingOutlined,
  EditOutlined,
  AuditOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import ZyModal from '@/components/common/ZyModal.vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import useUserId from '@/composables/useUserId'
import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole } = useUserRole();
import ProjectReviewModal from '@/components/review/ProjectReviewModal.vue'
import { debounce } from 'lodash'
import * as XLSX from 'xlsx'

// 导入API
import { 
  getProjects, 
  getProjectDetail, 
  addProject, 
  updateProject, 
  deleteProject, 
  reviewProject, 
  getLevelDistribution,
  getTimeDistribution,
  getTeacherProjectRanking,
  getTeacherProjectDetails,
  reapplyReview,
  getReviewStatusOverview
} from '@/api/modules/api.teachingReformProjects'

import { getProjectLevels } from '@/api/modules/api.teachingReformProjectLevels'
// 添加用户搜索API导入
import { usersSearch } from '@/api/modules/api.users'
import { uploadFiles } from '@/api/modules/api.file'
import {uploadFile as utilUploadFile,previewFileById,downloadFileById,deleteFileById } from '@/utils/others';

import { downloadJson, excelToTeachingReformProjectsJson } from '@/utils/fileUtils'
import { hasPerms } from '@/libs/util.common'
import { h } from 'vue'
import { getScoreTimeRange } from '@/api/modules/api.home';
// 用户数据 - 使用 useUserId 替代 useUserStore
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()

// 添加在其他状态变量附近
const currentRole = ref('');
const currentUserId = ref('');

// 窗口大小变化处理函数
const resizeHandler = debounce(() => {
  try {
    if (levelChart && !levelChart.isDisposed()) {
      levelChart.resize()
    }
    if (timeChart && !timeChart.isDisposed()) {
      timeChart.resize()
    }
  } catch (error) {
    console.error('调整图表大小出错:', error)
  }
}, 200);

// 状态变量
const errorMessage = ref('')
const isLoading = ref(false)
const confirmLoading = ref(false)
const showPersonalProjects = ref(false)
const dataSource = ref([])
const levelOptions = ref([])

// 图表引用
const levelChartRef = ref(null)
const reviewStatusChartRef = ref(null)
const timeChartRef = ref(null)
let levelChart = null
let reviewStatusChart = null
let timeChart = null

// 主数据表格引用
const mainDataTable = ref(null)

// 图表数据范围
const levelChartRange = ref('in')
const reviewStatusChartRange = ref('in')
const timeChartRange = ref('in')
const levelChartReviewStatus = ref('reviewed')
const reviewStatusChartReviewStatus = ref('reviewed')
const timeChartReviewStatus = ref('reviewed')

// 审核状态
const reviewStatus = ref('reviewed')
// 项目范围
const projectRange = ref('in')

// 教师排名数据
const teacherRankData = ref([])
const teacherRankLoading = ref(false)
const teacherRankChartRange = ref('in')
const teacherRankChartReviewStatus = ref('reviewed')
const teacherRankExportLoading = ref(false)

// 教师排名分页
const teacherRankPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条`
})

// 教师项目详情
const teacherProjectDetails = ref([])
const teacherDetailsVisible = ref(false)
const teacherDetailsLoading = ref(false)
const selectedTeacherDetailId = ref(null)
const selectedTeacherDetailName = ref('')
const teacherDetailsTotalScore = ref(0)

// 模态框相关
const modalVisible = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)

// 表单引用
const formRef = ref(null)

const fileList = ref([]);
const existingFileList = ref([]);

// 参与者相关
const participantOptions = ref([])
const participantSearchLoading = ref(false)

// 参与者搜索防抖定时器
const participantSearchTimeout = ref(null)

// 新增：分配比例信息
const allocationMsg = ref('')

// 总分计算
const totalScore = computed(() => {
  // 计算表格中所有记录的总分
  const sum = dataSource.value.reduce((acc, item) => {
    return acc + (parseFloat(item.level?.score) || 0)
  }, 0)
  return sum
})

// 处理文件上传
const handleFileUpload = ({ file, onSuccess, onError, onProgress }) => {
  utilUploadFile({
    file,
    uploadApi: uploadFiles,
    id: formState.id || 'temp_' + Date.now(),
    relatedId: formState.id,
    class: 'teaching_reform_projects',
    onProgress,
    onSuccess: (res) => {
      if (res && res.code === 200 && res.data) {
        // 将文件ID添加到formState.fileIds中
        if (!formState.fileIds) formState.fileIds = [];
        formState.fileIds.push(res.data.id);
        
        // 如果有文件路径，添加到attachmentUrl
        if (res.data.filePath) {
          if (!formState.attachmentUrl) formState.attachmentUrl = [];
          formState.attachmentUrl.push(res.data.filePath);
        }
      }
      onSuccess(res);
    },
    onError,
    fileList: fileList.value,
    formState: formState
  });
  console.log("fileList",fileList);
  console.log("formState",formState); 
};

// 预览文件
const previewFile = (file) => {
  console.log("file===",file);
  
  const fileId = file.response?.file?.uid;
  if (fileId) {
    previewFileById(fileId);
  } else {
    message.warning('无法获取文件ID，预览失败');
  }
};

// 下载文件
const downloadFile = (file) => {
  const fileId = file.response?.file?.uid;
  const fileName = file.name || file.originalFileName || '下载文件';
  if (fileId) {
    downloadFileById(fileId, fileName);
  } else {
    message.warning('无法获取文件ID，下载失败');
  }
};

// 删除文件
const confirmDeleteFile = (record) => {
  const fileId = record.response?.file?.uid;
  if (fileId) {
    deleteFileById(fileId, {
      onSuccess: () => {
        // 从文件列表中移除
        const index = fileList.value.findIndex(item => 
          item.uid === record.uid || 
          item.id === fileId
        );
        
        if (index !== -1) {
          fileList.value.splice(index, 1);
        }
        
        // 从fileIds中移除
        const idIndex = formState.fileIds.indexOf(fileId);
        if (idIndex !== -1) {
          formState.fileIds.splice(idIndex, 1);
        }
        
        message.success('文件已删除');
      },
      onError: (errorMsg) => {
        message.error(`删除失败: ${errorMsg}`);
      }
    });
  } else {
    // 如果没有文件ID，只是从列表中移除
    const index = fileList.value.findIndex(item => item.uid === record.uid);
    if (index !== -1) {
      fileList.value.splice(index, 1);
      message.success('文件已从列表中移除');
    }
  }
};

// 在适当位置添加文件大小格式化函数
const formatFileSize = (bytes) => {
  if (bytes === undefined || bytes === null) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
})

// 搜索表单
const searchForm = reactive({
  projectName: '',
  levelId: undefined,
  dateRange: [],
  reviewStatus: 'reviewed',
  range: 'in'
})


// 表单数据
const formState = reactive({
  projectNumber: '',
  projectName: '',
  approvalDepartment: '',
  approvalDate: null,
  approvalFund: 0,
  levelId: undefined,
  startYear: dayjs().year(),
  endYear: dayjs().year() + 2,
  remark: '',
  participants: [],
  fileIds: [], // 存储文件ID
  attachmentUrl: [], // 存储文件路径
})

// 表单验证规则
const rules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 255, message: '项目名称长度在2-255个字符之间', trigger: 'blur' }
  ],
  approvalDepartment: [
    { required: true, message: '请输入下达部门', trigger: 'blur' }
  ],
  approvalDate: [
    { required: true, message: '请选择获批日期', trigger: 'change' }
  ],
  levelId: [
    { required: true, message: '请选择项目级别', trigger: 'change' }
  ],
  startYear: [
    { required: true, message: '请输入执行起始年', trigger: 'change' }
  ],
  endYear: [
    { required: true, message: '请输入执行结束年', trigger: 'change' }
  ],
  participants: [
    { required: true, type: 'array', message: '请至少添加一名参与者', trigger: 'change' }
  ]
}


// 新增：当前选择的成员
const currentMember = reactive({
  id: '',
  userId: '',
  displayName: '',
  nickname: '',
  username: '',
  studentNumber: '',
  allocationRatio: 10, // 默认10%
  isLeader: false
})

// 初始化函数
onMounted(async () => {
  try {
    // 获取当前用户角色
    try {
      currentRole.value = await getUserRole();
      console.log("当前用户角色:", currentRole.value);
      console.log("roleAuth===", currentRole.value ? currentRole.value.roleAuth : 'undefined');
    } catch (error) {
      console.error("获取用户角色失败:", error);
      message.error("获取用户角色信息失败，某些功能可能受限");
    }

    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true);
      console.log("当前用户ID:", currentUserId.value);
    } catch (error) {
      console.error("获取用户ID失败:", error);
    }

    // 如果是教师角色，默认显示个人教改项目
    if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
      showPersonalProjects.value = true;
      console.log('用户为教师角色，默认显示个人教改项目');
    }

    fetchData()
    fetchLevels()
    initCharts()
    fetchStatistics()

    // 如果不是个人视图，获取教师排名
    if (!showPersonalProjects.value) {
      fetchTeacherRanking()
    }

    // 监听窗口大小变化
    window.addEventListener('resize', resizeHandler)
  } catch (error) {
    console.error('组件初始化错误:', error)
    message.error('初始化失败，请刷新页面重试')
  }
})

// 监听个人/全部项目切换
watch(showPersonalProjects, () => {
  pagination.current = 1
  fetchData()
  initCharts()
  fetchStatistics()
  
  // 如果不是个人视图，获取教师排名
  if (!showPersonalProjects.value) {
    fetchTeacherRanking()
  }
})

// 获取数据列表
const fetchData = async () => {
  try {
    isLoading.value = true
    errorMessage.value = ''

    // 构造查询参数
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortField: 'approvalDate',
      sortOrder: 'desc',
      range: searchForm.range,
      reviewStatus: searchForm.reviewStatus
    }

    // 添加搜索条件
    if (searchForm.projectName) {
      params.projectName = searchForm.projectName
    }

    if (searchForm.levelId) {
      params.levelId = searchForm.levelId
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.approvalDateStart = searchForm.dateRange[0].format('YYYY-MM-DD')
      params.approvalDateEnd = searchForm.dateRange[1].format('YYYY-MM-DD')
    }

    // 如果是个人项目视图，添加用户ID
    if (showPersonalProjects.value) {
      // 获取当前用户ID
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      } else {
        message.error('无法获取用户ID，请重新登录')
        return
      }
    }

    // 调用统一的API获取数据
    const response = await getProjects(params)

    if (response && response.code === 200) {
      // 处理数据
      const data = response.data
      dataSource.value = processProjectData(data.list || [])
      pagination.total = data.pagination?.total || 0

      // 计算总分
      calculateTotalScore()
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = '获取项目列表失败：' + (response?.message || '未知错误')
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    message.error('获取项目列表失败: ' + (error.message || '未知错误'))
    errorMessage.value = '获取项目列表失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 处理项目数据
const processProjectData = (data) => {
  return data.map(item => {
    // 处理级别信息
    const level = item.level || {}
    
    // 获取主持人
    const leader = item.participants?.find(p => p.isLeader)?.user || null
    
    return {
      ...item,
      levelName: level.levelName || '未知级别',
      score: level.score || 0,
      leader,
    }
  })
}

// 计算总分
const calculateTotalScore = () => {
  totalScore.value = dataSource.value.reduce((sum, item) => {
    // 如果是个人项目，计算按分配比例的分数
    if (showPersonalProjects.value) {
      // 获取当前用户ID
      const currentUserId = userId.value
      if (currentUserId) {
        const participant = item.participants?.find(p => p.userId === currentUserId)
        if (participant) {
          const ratio = participant.allocationRatio
          return sum + (item.score * ratio)
        }
      }
      return sum
    }
    
    // 如果是全部项目，直接累加分数
    return sum + item.score
  }, 0)
}

// 获取项目级别列表
const fetchLevels = async () => {
  try {
    const response = await getProjectLevels()
    
    if (response && response.code === 200) {
      levelOptions.value = response.data || []
    } else {
      console.error('获取项目级别列表失败', response?.message)
    }
  } catch (error) {
    console.error('获取项目级别列表失败:', error)
  }
}

// 初始化图表
const initCharts = () => {
  initLevelChart()
  initReviewStatusChart(reviewStatusChartRange.value)
  initTimeChart()
}

// 初始化级别分布图
const initLevelChart = async () => {
  try {
    if (!levelChartRef.value) return
    
    // 如果已经有图表实例，销毁它
    if (levelChart) {
      levelChart.dispose()
    }
    
    // 创建图表实例
    levelChart = echarts.init(levelChartRef.value)
    
    // 设置加载状态
    levelChart.showLoading()
    
    // 构造请求参数
    const params = {
      range: levelChartRange.value,
      reviewStatus: levelChartReviewStatus.value !== 'all' ? levelChartReviewStatus.value : undefined
    }
    
    // 如果是个人视图，添加用户ID
    if (showPersonalProjects.value) {
      // 使用 getUserId 替代 userStore.userId
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    // 调用后端API获取数据
    const response = await getLevelDistribution(params)
    
    // 隐藏加载状态
    levelChart.hideLoading()
    
    if (response && response.code === 200) {
      const data = response.data || []
      
      // 处理数据
      const seriesData = data.map(item => ({
        value: item.count,
        name: item.levelName
      }))
      
      // 配置图表选项
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          bottom: 10,
          data: data.map(item => item.levelName)
        },
        series: [
          {
            name: '项目级别分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: seriesData
          }
        ]
      }
      
      // 设置图表选项
      levelChart.setOption(option)
    } else {
      message.error('获取级别分布数据失败')
    }
  } catch (error) {
    console.error('初始化级别分布图表失败:', error)
    if (levelChart) {
      levelChart.hideLoading()
    }
  }
}

// 初始化时间分布图
const initTimeChart = async () => {
  try {
    if (!timeChartRef.value) return
    
    // 如果已经有图表实例，销毁它
    if (timeChart) {
      timeChart.dispose()
    }
    
    // 创建图表实例
    timeChart = echarts.init(timeChartRef.value)
    
    // 设置加载状态
    timeChart.showLoading()
    
    // 构造请求参数
    const params = {
      range: timeChartRange.value,
      reviewStatus: timeChartReviewStatus.value !== 'all' ? timeChartReviewStatus.value : undefined
    }
    
    // 如果是个人视图，添加用户ID
    if (showPersonalProjects.value) {
      // 使用 getUserId 替代 userStore.userId
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    // 调用后端API获取数据
    const response = await getTimeDistribution(params)
    
    // 隐藏加载状态
    timeChart.hideLoading()
    
    if (response && response.code === 200) {
      const data = response.data || []
      
      // 处理数据
      const years = data.map(item => item.year)
      const counts = data.map(item => item.count)
      
      // 配置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: years,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '项目数量',
            type: 'bar',
            barWidth: '60%',
            data: counts
          }
        ]
      }
      
      // 设置图表选项
      timeChart.setOption(option)
    } else {
      message.error('获取时间分布数据失败')
    }
  } catch (error) {
    console.error('初始化时间分布图表失败:', error)
    if (timeChart) {
      timeChart.hideLoading()
    }
  }
}

// 初始化审核状态分布图
const initReviewStatusChart = async (range) => {
  try {
    console.log('初始化审核状态分布图...')
    // 检查DOM元素是否存在
    if (!reviewStatusChartRef.value) {
      console.error('审核状态分布图DOM元素不存在')
      return
    }

    // 构建请求参数
    const params = {
      range: range || 'in'
    }

    // 如果是个人视图，添加userId参数
    if (showPersonalProjects.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }

    console.log('获取审核状态分布数据参数:', params)

    // 请求审核状态分布数据
    const response = await getReviewStatusOverview(params)

    if (response && response.code === 200) {
      const { reviewed, pending, rejected } = response.data
      console.log('获取到的审核状态分布数据:', response.data)

      // 初始化图表
      if (reviewStatusChart) {
        reviewStatusChart.dispose()
      }
      reviewStatusChart = echarts.init(reviewStatusChartRef.value)

      // 设置图表配置
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          left: 'left',
          bottom: 0,
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: '审核状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            {
              value: reviewed,
              name: '已审核',
              itemStyle: { color: '#52c41a' }
            },
            {
              value: pending,
              name: '待审核',
              itemStyle: { color: '#faad14' }
            },
            {
              value: rejected,
              name: '已拒绝',
              itemStyle: { color: '#ff4d4f' }
            }
          ]
        }]
      }

      reviewStatusChart.setOption(option)
      console.log('审核状态分布图表已渲染')
    } else {
      console.error('获取审核状态分布数据失败:', response)
    }
  } catch (error) {
    console.error('审核状态分布图初始化失败:', error)
  }
}

// 改变图表范围
const changeChartRange = (chartType, range) => {
  if (chartType === 'level') {
    levelChartRange.value = range
    initLevelChart()
  } else if (chartType === 'reviewStatus') {
    reviewStatusChartRange.value = range
    initReviewStatusChart(range)
  } else if (chartType === 'time') {
    timeChartRange.value = range
    initTimeChart()
  } else if (chartType === 'teacherRank') {
    teacherRankChartRange.value = range
    fetchTeacherRanking()
  }
}

// 改变图表审核状态筛选
const changeChartReviewStatus = (chartType, status) => {
  if (chartType === 'teacherRank') {
    teacherRankChartReviewStatus.value = status
    fetchTeacherRanking()
  } else if (chartType === 'level') {
    levelChartReviewStatus.value = status
    initLevelChart()
  } else if (chartType === 'time') {
    timeChartReviewStatus.value = status
    initTimeChart()
  }
}

// 获取排名标签颜色
const getRankColor = (rank) => {
  if (rank <= 3) {
    return ['#f5222d', '#fa8c16', '#faad14'][rank - 1]
  }
  return ''
}

// 显示教师项目详情
const showTeacherProjectsDetails = async (teacher) => {
  teacherDetailsVisible.value = true;
  selectedTeacherDetailId.value = teacher.userId;
  selectedTeacherDetailName.value = teacher.userName;
  
  // 重置分页到第一页
  teacherDetailsPagination.current = 1;
  teacherDetailsPagination.pageSize = 10;
  
  // 加载第一页数据
  await fetchTeacherProjectDetails(1, 10);
};

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.projectName = ''
  searchForm.levelId = undefined
  searchForm.dateRange = []
  searchForm.reviewStatus = 'reviewed'
  searchForm.range = 'in'
  pagination.current = 1
  fetchData()
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
  // 只更新必要的分页属性，保持其他配置不变
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  // 确保其他配置属性保持不变
  pagination.showSizeChanger = true

  pagination.pageSizeOptions = ['10', '20', '50']
  pagination.showTotal = total => `共 ${total} 条`

  // 处理排序
  if (sorter.field) {
    fetchData()
  }
}

// 切换个人/全部项目
const togglePersonalProjects = () => {
  showPersonalProjects.value = !showPersonalProjects.value
  // watch会自动调用fetchData和initCharts
}

// 判断是否有审核权限 - 修改为统一允许审核未审核的项目
const hasPermissionToReview = (record) => {
  // 如果项目已经审核，则不显示审核按钮
  if (record.ifReviewer) {
    return false
  }
  // 其他情况都允许审核
  return true
}

// 添加项目
const showAddModal = () => {
  isEdit.value = false
  currentRecord.value = null
  resetForm()
  fetchParticipants('')
  modalVisible.value = true
  // 防止Vue开发工具输出组件信息
  return undefined
}

// 编辑项目
const handleEdit = async (record) => {
  try {
    isEdit.value = true
    currentRecord.value = record
    resetForm()
    
    const response = await getProjectDetail(record.id)
    
    if (response && response.code === 200) {
      const detail = response.data
      
      // 填充表单数据
      formState.projectNumber = detail.projectNumber || ''
      formState.projectName = detail.projectName || ''
      formState.approvalDepartment = detail.approvalDepartment || ''
      formState.approvalDate = detail.approvalDate ? dayjs(detail.approvalDate) : null
      formState.approvalFund = detail.approvalFund || 0
      formState.levelId = detail.levelId
      formState.startYear = detail.startYear
      formState.endYear = detail.endYear
      formState.remark = detail.remark || ''
      
      // 处理参与者 - 修改为新的数据结构
      formState.participants = detail.participants?.map(p => ({
        id: p.id,
        userId: p.userId,
        nickname: p.user?.nickname || '',
        username: p.user?.username || '',
        studentNumber: p.user?.studentNumber || '',
        // 将小数转换为百分比显示
        allocationRatio: p.allocationRatio * 100,
        isLeader: p.isLeader
      })) || []
      
      // 加载文件列表
      if (detail.attachments && detail.attachments.length > 0) {
        // 存储附件ID
        formState.fileIds = detail.attachments.map(file => file.id)
        
        // 构建已有文件列表
        existingFileList.value = detail.attachments.map(file => ({
          ...file,
          fileName: file.originalName || file.name,
          originalName: file.originalName || file.name,
          size: file.size,
          status: 'done'
        }))
      }
      console.log("existingFileList",existingFileList);
      
      // 立即验证总分配比例
      validateTotalAllocation()
      
      modalVisible.value = true
    } else {
      message.error(response?.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取项目详情失败:', error)
    message.error('获取项目详情失败: ' + (error.message || '未知错误'))
  }
  // 防止Vue开发工具输出组件信息
  return undefined
}

// 审核项目
const handleReview = async (record) => {
  try {
    // 显示加载中
    message.loading('正在加载详情...', 0.5);
    currentReviewRecord.value = record;
    
    // 获取项目详情，包括附件
    try {
      const response = await getProjectDetail(record.id);
      if (response && response.code === 200 && response.data) {
        // 处理附件数据
        reviewAttachments.value = (response.data.attachments || []).map((file, index) => ({
          uid: `-${index}`,
          name: file.name || file.originalName || `附件${index + 1}`,
          status: 'done',
          url: file.url,
          response: { file: { id: file.id } },
          data: file
        }));
        console.log('项目附件:', reviewAttachments.value);
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
      message.error('获取项目详情失败，但将继续审核流程');
    }
    
    // 显示审核模态框
    reviewModalVisible.value = true;
    
  } catch (error) {
    console.error('打开审核对话框失败:', error);
    message.error('操作失败: ' + (error.message || '未知错误'));
  }
};

// 确认删除
const confirmDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDelete(record)
  })
}

// 删除项目
const handleDelete = async (record) => {
  try {
    const response = await deleteProject(record.id)

    if (response && response.code === 200) {
      message.success('删除成功')
      fetchData()
      initCharts()
    } else {
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('删除项目失败:', error)
    message.error('删除项目失败: ' + (error.message || '未知错误'))
  }
  // 防止输出调试信息
  return undefined
}

// 提交表单
const handleModalOk = () => {
  formRef.value.validate().then(async () => {
    try {
      confirmLoading.value = true
      
      // 校验分配比例
      if (!validateTotalAllocation()) {
        message.error('参与人员分配比例总和必须为100%')
        confirmLoading.value = false
        return
      }
      
      // 校验是否有主持人
      if (formState.participants.length > 0 && !formState.participants.some(p => p.isLeader)) {
        message.error('必须指定至少一名主持人')
        confirmLoading.value = false
        return
      }
      
      // 准备提交数据
      const submitData = {
        projectNumber: formState.projectNumber,
        projectName: formState.projectName,
        approvalDepartment: formState.approvalDepartment,
        approvalDate: formState.approvalDate ? formState.approvalDate.format('YYYY-MM-DD') : null,
        approvalFund: formState.approvalFund,
        levelId: formState.levelId,
        startYear: formState.startYear,
        endYear: formState.endYear,
        remark: formState.remark,
        participants: formState.participants.map(p => ({
          userId: p.userId,
          // 将百分比转换为小数提交给后端
          allocationRatio: p.allocationRatio / 100,
          isLeader: p.isLeader
        }))
      }
      
      // 处理文件ID
      if (formState.fileIds && formState.fileIds.length > 0) {
        submitData.fileIds = JSON.stringify(formState.fileIds)
      }
      
      // 如果有附件URL
      if (formState.attachmentUrl) {
        submitData.attachmentUrl = JSON.stringify(formState.attachmentUrl)
      }
      
      let response
      if (isEdit.value) {
        response = await updateProject(currentRecord.value.id, submitData)
      } else {
        response = await addProject(submitData)
      }
      
      if (response && response.code === 200) {
        message.success(isEdit.value ? '更新成功' : '添加成功')
        modalVisible.value = false
        fetchData()
        initCharts()
      } else {
        message.error(response?.message || '操作失败')
      }
    } catch (error) {
      console.error('提交项目数据失败:', error)
      message.error('提交项目数据失败: ' + (error.message || '未知错误'))
    } finally {
      confirmLoading.value = false
    }
  }).catch(errors => {
    // 替换console.log为更清晰的错误提示
    if (errors && Object.keys(errors).length > 0) {
      message.error('表单验证失败，请检查输入项')
    }
  })
  // 防止输出调试信息
  return undefined
}

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  // 防止输出调试信息
  return undefined
}

// 重置表单
const resetForm = () => {
  formState.projectNumber = ''
  formState.projectName = ''
  formState.approvalDepartment = ''
  formState.approvalDate = null
  formState.approvalFund = 0
  formState.levelId = undefined
  formState.startYear = dayjs().year()
  formState.endYear = dayjs().year() + 2
  formState.remark = ''
  formState.participants = []
  formState.fileIds = [] // 清空文件ID
  formState.attachmentUrl = [] // 清空文件路径
  
  // 清空文件列表
  fileList.value = []
  existingFileList.value = []
  
  // 重置当前成员
  currentMember.id = ''
  currentMember.userId = ''
  currentMember.displayName = ''
  currentMember.nickname = ''
  currentMember.username = ''
  currentMember.studentNumber = ''
  currentMember.allocationRatio = 10 // 设为10%
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 导入项目数据
const handleImport = async ({ file, onSuccess, onError }) => {
  try {
    message.loading('正在导入数据...')
    const res = await importProjects(file)
    
    if (res && res.code === 200) {
      message.success(`导入成功：${res.data.success}条，失败：${res.data.failed}条`)
      onSuccess()
      fetchData() // 重新获取数据
      initCharts() // 刷新图表
    } else {
      message.error(res?.message || '导入失败')
      onError()
    }
  } catch (error) {
    console.error('导入文件失败:', error)
    message.error('导入文件失败: ' + (error.message || '未知错误'))
    onError()
  }
}

// 处理上传前检查
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
    return false;
  }
  return true;
};

// 格式化参与者信息（用于表格显示）
const formatParticipantsWithAllocation = (participants, record) => {
  if (!participants || participants.length === 0) return '-'
  
  return participants.map(p => {
    const user = p.user || {}
    // 确保allocationRatio是数字类型，并处理可能的null/undefined/NaN情况
    const ratio = typeof p.allocationRatio === 'number' ? p.allocationRatio :
                 (parseFloat(p.allocationRatio) || 0);
    return `${user.nickname || user.username || '未知'}(${(ratio * 100).toFixed(0)}%)${p.isLeader ? ' [主持]' : ''}`
  }).join('; ')
}

// 搜索参与人员
const fetchParticipants = (value) => {
  // 清除之前的定时器
  if (participantSearchTimeout.value) {
    clearTimeout(participantSearchTimeout.value)
  }
  
  // 如果输入为空，清空选项
  if (!value || value.trim() === '') {
    participantOptions.value = []
    return
  }
  
  // 设置500ms延迟，避免频繁请求
  participantSearchTimeout.value = setTimeout(async () => {
    participantSearchLoading.value = true
    try {
      // 调用搜索接口
      const response = await usersSearch({ keyword: value })
      
      if (response && response.code === 200) {
        // 检查返回的数据结构
        if (Array.isArray(response.data)) {
          participantOptions.value = response.data
        } else if (response.data && Array.isArray(response.data.list)) {
          // 如果返回的是包含list属性的对象
          participantOptions.value = response.data.list
        } else {
          // 如果数据结构不是预期的格式
          console.error('搜索用户返回的数据结构异常:', response.data)
          participantOptions.value = []
        }
      } else {
        participantOptions.value = []
        console.error('搜索用户失败:', response?.message || '未知错误')
      }
    } catch (error) {
      console.error('搜索用户出错:', error)
      participantOptions.value = []
    } finally {
      participantSearchLoading.value = false
    }
  }, 500)
}

// 新增：当选择成员时更新当前成员信息
const handleCurrentMemberChange = (value, option) => {
  // 在vue3的a-select中，option参数是一个包含data属性的对象
  const selectedOption = option ? 
    (option.data ? option.data : 
      (option.option ? option.option.data : null)) : null
  
  if (selectedOption) {
    // 直接使用选项数据
    currentMember.id = selectedOption.id || ''
    currentMember.userId = selectedOption.id || ''
    currentMember.displayName = selectedOption.nickname || selectedOption.username || value
    currentMember.nickname = selectedOption.nickname || ''
    currentMember.username = selectedOption.username || ''
    currentMember.studentNumber = selectedOption.studentNumber || ''
    
    // 默认分配比例设为10%
    if (!currentMember.allocationRatio) {
      currentMember.allocationRatio = 10
    }
  } else if (value) {
    // 尝试从选项列表中找到匹配的选项
    const found = participantOptions.value.find(opt => 
      (opt.nickname || opt.username) === value
    )
    
    if (found) {
      currentMember.id = found.id || ''
      currentMember.userId = found.id || ''
      currentMember.displayName = found.nickname || found.username || value
      currentMember.nickname = found.nickname || ''
      currentMember.username = found.username || ''
      currentMember.studentNumber = found.studentNumber || ''
      
      // 默认分配比例设为10%
      if (!currentMember.allocationRatio) {
        currentMember.allocationRatio = 10
      }
    } else {
      // 清空选择
      currentMember.id = ''
      currentMember.userId = ''
      currentMember.displayName = ''
      currentMember.nickname = ''
      currentMember.username = ''
      currentMember.studentNumber = ''
      currentMember.allocationRatio = 10
    }
  }
}

// 新增：添加参与者
const addParticipant = () => {
  // 验证成员数据
  if (!currentMember.userId) {
    message.error('请选择参与人员')
    return
  }
  
  // 检查分配比例
  if (currentMember.allocationRatio === undefined || 
      currentMember.allocationRatio === null || 
      isNaN(parseFloat(currentMember.allocationRatio))) {
    message.error('请输入有效的分配比例')
    return
  }
  
  // 检查是否已存在该成员
  const exists = formState.participants.some(p => p.userId === currentMember.userId)
  
  if (exists) {
    message.warning('该参与人员已添加')
    return
  }
  
  // 创建新成员对象，按照原有数据结构创建
  const newMember = {
    userId: currentMember.userId,
    nickname: currentMember.nickname,
    username: currentMember.username,
    studentNumber: currentMember.studentNumber,
    allocationRatio: currentMember.allocationRatio, // 百分比形式的比例
    isLeader: formState.participants.length === 0 // 第一个添加的成员默认为主持人
  }
  
  // 添加到参与人员列表
  formState.participants.push(newMember)
  
  // 重置当前成员数据
  currentMember.displayName = ''
  
  // 验证总比例
  validateTotalAllocation()
  
  message.success('参与人员添加成功')
}

// 新增：验证总分配比例
const validateTotalAllocation = () => {
  if (!formState.participants || formState.participants.length === 0) {
    allocationMsg.value = ''
    return true
  }

  // 计算百分比形式比例之和(0-100)
  const total = formState.participants.reduce((sum, p) => sum + (parseFloat(p.allocationRatio) || 0), 0)
  const isValid = Math.abs(total - 100) < 1 // 允许1%的误差

  allocationMsg.value = isValid
    ? '分配比例总和: 100%'
    : `分配比例总和: ${total.toFixed(0)}%，需要调整为100%`

  return isValid
}

// 新增：设置主持人逻辑
const toggleLeader = (index, checked) => {
  // 如果设置为主持人，先将其他所有人设为非主持人
  if (checked) {
    formState.participants.forEach((p, i) => {
      if (i !== index) {
        p.isLeader = false
      }
    })
  }
  
  // 修改指定参与人的主持人状态
  formState.participants[index].isLeader = checked
}

// 新增：删除参与者
const removeParticipant = (index) => {
  // 检查是否是主持人
  const isLeader = formState.participants[index].isLeader
  
  // 删除参与者
  formState.participants.splice(index, 1)
  
  // 如果删除的是主持人且还有其他参与者，设置第一个为主持人
  if (isLeader && formState.participants.length > 0) {
    formState.participants[0].isLeader = true
  }
  
  // 调整分配比例
  adjustParticipantRatios()
  
  // 验证总比例
  validateTotalAllocation()
}

// 调整参与者分配比例
const adjustParticipantRatios = () => {
  if (formState.participants.length === 0) return

  // 平均分配比例
  const ratio = 100 / formState.participants.length
  formState.participants.forEach(p => {
    p.allocationRatio = parseFloat(ratio.toFixed(1))
  })
}

// 新增：获取统计数据
const fetchStatistics = async () => {
  try {
    const params = {}
    
    // 如果是个人视图，添加用户ID
    if (showPersonalProjects.value) {
      const currentUserId = await getUserId(true)
      if (currentUserId) {
        params.userId = currentUserId
      }
    }
    
    // 使用现有的 getProjects API 来获取统计数据
    const response = await getProjects(params)
    
    if (response && response.code === 200) {
      // 处理统计数据，这里可以根据接口返回的数据结构进行调整
      // 直接使用返回的数据，不需要输出到控制台
      // 这里可以添加显示统计数据的逻辑
    } else {
      console.error('获取统计数据失败', response?.message)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const authorRankingPagination = reactive({
  nickname: ''
});


// 获取教师项目排名
const fetchTeacherRanking = async () => {
  teacherRankLoading.value = true
  
  try {
    // 构建请求参数
    const params = {
      page: teacherRankPagination.current,
      pageSize: teacherRankPagination.pageSize,
      range: teacherRankChartRange.value,
      nickname: authorRankingPagination.nickname,
      reviewStatus: teacherRankChartReviewStatus.value !== 'all' ? teacherRankChartReviewStatus.value : undefined
    }
    
    const response = await getTeacherProjectRanking(params)
    
    if (response && response.code === 200) {
      teacherRankData.value = response.data.list || []
      teacherRankPagination.total = response.data.pagination?.total || 0
    } else {
      message.error('获取教师排名数据失败')
    }
  } catch (error) {
    console.error('获取教师排名数据失败:', error)
  } finally {
    teacherRankLoading.value = false
  }
}

// 表格列定义
const columns = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
    ellipsis: true,
    width: 300,
  },
  {
    title: '项目编号',
    dataIndex: 'projectNumber',
    key: 'projectNumber',
    width: 150,
  },
  {
    title: '级别',
    dataIndex: 'levelName',
    key: 'levelName',
    width: 200,
  },
  {
    title: '批准日期',
    dataIndex: 'approvalDate',
    key: 'approvalDate',
    width: 120,
    sorter: true,
  },
  {
    title: '下达部门',
    dataIndex: 'approvalDepartment',
    key: 'approvalDepartment',
    width: 150,
  },
  {
    title: '参与人员',
    key: 'participants',
    width: 200,
  },
  {
    title: '学分',
    dataIndex: 'score',
    key: 'score',
    width: 100,
    sorter: true,
  },
  {
    title: '审核状态',
    key: 'ifReviewer',
    width: 120,
    filters: [
      { text: '已审核', value: true },
      { text: '未审核', value: false }
    ],
    filterMultiple: false,
    onFilter: (value, record) => record.ifReviewer === value,
  },
  {
    title: '审核建议',
    dataIndex: 'reviewComment',
    key: 'reviewComment',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
  },
]

// 教师排名相关
const teacherRankColumns = [
  {
    title: '排名',
    key: 'rank',
    width: '80px',
    align: 'center',
  },
  {
    title: '教师',
    dataIndex: 'userName',
    key: 'userName',
    width: '150px',
  },
  {
    title: '工号',
    dataIndex: 'studentNumber',
    key: 'studentNumber',
    width: '120px',
  },
  {
    title: '总项目数',
    dataIndex: 'totalProjects',
    key: 'totalProjects',
    width: '100px',
    align: 'center',
    sorter: true,
  },
  {
    title: '总得分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: '100px',
    align: 'center',
    sorter: true,
  },
  {
    title: '操作',
    key: 'details',
    width: '100px',
    align: 'center',
    fixed: 'right',
  },
]

// 教师项目详情列
const teacherProjectDetailColumns = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
    width: '25%',
    ellipsis: false,
  },
  {
    title: '项目编号',
    dataIndex: 'projectNumber',
    key: 'projectNumber',
    width: '10%',
    ellipsis: true,
  },
  {
    title: '级别',
    key: 'levelName',
    width: '20%',
  },
  {
    title: '获批日期',
    dataIndex: 'approvalDate',
    key: 'approvalDate',
    width: '10%',
  },
  {
    title: '分配比例',
    dataIndex: 'allocationRatio',
    key: 'allocationRatio',
    width: '8%',
  },
  {
    title: '分数',
    key: 'score',
    width: '8%',
  },

]

// 在适当位置添加文件列表表格列定义
const fileColumns = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    key: 'fileName', 
    ellipsis: true
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 240
  }
];

// 项目审核相关状态
const reviewModalVisible = ref(false);
const currentReviewRecord = ref({});
const reviewAttachments = ref([]);

// 处理审核提交
const handleReviewSubmit = async (formData) => {
  try {
    console.log('接收到的表单数据:', formData);
    
    // 获取当前用户ID作为审核人
    await getUserId(true);
    
    if (!userId.value) {
      message.error('无法获取用户ID，请重新登录');
      reviewModalVisible.value = false;
      return;
    }
    
    // 调用审核API
    const submitData = {
      id: formData.id,
      reviewer: userId.value,
      reviewStatus: formData.reviewStatus,
      reviewComment: formData.reviewComment
    };
    
    console.log('发送到API的数据:', submitData);
    
    const response = await reviewProject(submitData);
    
    if (response && response.code === 200) {
      message.success('审核成功');
      reviewModalVisible.value = false;
      fetchData();
      fetchStatistics();
      initCharts();
    } else {
      message.error(response?.message || '审核失败');
    }
  } catch (error) {
    console.error('审核失败:', error);
    message.error('审核失败: ' + (error.message || '未知错误'));
  }
};

// 教师排行表格变化处理
const handleTeacherRankTableChange = (pagination) => {
  // 只更新必要的分页属性，保持其他配置不变
  teacherRankPagination.current = pagination.current
  teacherRankPagination.pageSize = pagination.pageSize
  // 确保其他配置属性保持不变
  teacherRankPagination.showSizeChanger = true

  teacherRankPagination.pageSizeOptions = ['10', '20', '50']
  teacherRankPagination.showTotal = (total) => `共 ${total} 条`
  fetchTeacherRanking()
}

// 导出教师排行
const exportTeacherRanking = async () => {
  try {
    teacherRankExportLoading.value = true
    
    // 构建导出请求参数
    const params = {
      range: teacherRankChartRange.value,
      reviewStatus: teacherRankChartReviewStatus.value !== 'all' ? teacherRankChartReviewStatus.value : undefined,
      isExport: true, // 标记为导出，返回所有数据
      page: 1,
      pageSize: 10000 // 获取足够多的数据
    }
    
    const response = await getTeacherProjectRanking(params)
    
    if (response && response.code === 200 && response.data.list && response.data.list.length > 0) {
      // 准备导出数据
      const data = response.data.list.map(item => ({
        '排名': item.rank,
        '教师姓名': item.userName || '未知',
        '工号': item.studentNumber || '无工号',
        '项目数量': item.totalProjects || 0,
        '主持项目数': item.leaderProjectCount || 0,
        '总得分': parseFloat(item.totalScore || 0).toFixed(2)
      }))
      
      // 使用XLSX直接创建表格
      const worksheet = XLSX.utils.json_to_sheet(data)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '教学改革项目教师排行')
      
      // 设置列宽
      const columnWidths = [
        { wch: 8 },   // 排名
        { wch: 12 },  // 教师姓名
        { wch: 12 },  // 工号
        { wch: 10 },  // 项目数量
        { wch: 10 },  // 主持项目数
        { wch: 10 }   // 总得分
      ]
      worksheet['!cols'] = columnWidths
      
      // 生成文件名
      const fileName = `教学改革项目教师排行_${dayjs().format('YYYY-MM-DD')}.xlsx`
      
      // 下载文件
      XLSX.writeFile(workbook, fileName)
      message.success('导出成功')
    } else {
      message.error(response?.message || '导出失败: 未获取到数据')
    }
  } catch (error) {
    console.error('导出教师排行失败:', error)
    message.error('导出教师排行失败: ' + (error.message || '未知错误'))
  } finally {
    teacherRankExportLoading.value = false
  }
}

// 在 const 变量声明部分添加
const exportLoading = ref(false);

// 添加导出函数
const exportProjects = async () => {
  try {
    exportLoading.value = true;
    
    // 构造导出参数 - 使用当前的筛选条件
    const params = {
      // 使用当前搜索表单的条件
      projectName: searchForm.projectName,
      levelId: searchForm.levelId,
      range: searchForm.range,
      reviewStatus: searchForm.reviewStatus,
      sortField: 'approvalDate',
      sortOrder: 'desc',
      isExport: true // 标记为导出，返回所有数据
    };
    
    // 添加日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.approvalDateStart = searchForm.dateRange[0].format('YYYY-MM-DD');
      params.approvalDateEnd = searchForm.dateRange[1].format('YYYY-MM-DD');
    }
    
    // 如果是个人项目视图，添加用户ID
    if (showPersonalProjects.value) {
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        params.userId = currentUserId;
      } else {
        message.error('无法获取用户ID，请重新登录');
        exportLoading.value = false;
        return;
      }
    }
    
    // 调用API获取数据
    const response = await getProjects(params);
    
    if (response && response.code === 200 && response.data && response.data.list) {
      // 准备导出数据
      const data = response.data.list.map(item => {
        // 格式化参与者信息
        const participants = (item.participants || [])
          .map(p => {
            const user = p.user || {};
            const ratio = typeof p.allocationRatio === 'number' ? p.allocationRatio : 
                          (parseFloat(p.allocationRatio) || 0);
            return `${user.nickname || user.username || '未知'}(${(ratio * 100).toFixed(0)}%)${p.isLeader ? '[主持]' : ''}`;
          })
          .join('; ');
          
        return {
          '项目名称': item.projectName || '',
          '项目编号': item.projectNumber || '',
          '项目级别': item.level?.levelName || '',
          '批准日期': item.approvalDate || '',
          '下达部门': item.approvalDepartment || '',
          '参与人员': participants,
          '学分': item.level?.score || 0,
          '审核状态': item.ifReviewer === true ? '已审核' : (item.ifReviewer === false ? '已拒绝' : '待审核'),
          '备注': item.remark || ''
        };
      });
      
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, '教学改革项目');
      
      // 设置列宽
      const columnWidths = [
        { wch: 30 }, // 项目名称
        { wch: 15 }, // 项目编号
        { wch: 12 }, // 项目级别
        { wch: 12 }, // 批准日期
        { wch: 15 }, // 下达部门
        { wch: 40 }, // 参与人员
        { wch: 10 }, // 学分
        { wch: 12 }, // 审核状态
        { wch: 20 }  // 备注
      ];
      worksheet['!cols'] = columnWidths;
      
      // 导出文件
      const fileName = `教学改革项目_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      
      message.success('导出成功');
    } else {
      message.error(response?.message || '导出失败: 未获取到数据');
    }
  } catch (error) {
    console.error('导出教学改革项目失败:', error);
    message.error('导出失败: ' + (error.message || '未知错误'));
  } finally {
    exportLoading.value = false;
  }
};

// 教师项目详情分页
const teacherDetailsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,

  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
});

// 处理教师项目详情分页变化
const handleTeacherDetailsPaginationChange = (pagination) => {
  // 只更新必要的分页属性，保持其他配置不变
  teacherDetailsPagination.current = pagination.current;
  teacherDetailsPagination.pageSize = pagination.pageSize;
  // 确保其他配置属性保持不变
  teacherDetailsPagination.showSizeChanger = true

  teacherDetailsPagination.pageSizeOptions = ['10', '20', '50']
  teacherDetailsPagination.showTotal = total => `共 ${total} 条`
  fetchTeacherProjectDetails(pagination.current, pagination.pageSize);
};

// 获取教师项目详情数据
const fetchTeacherProjectDetails = async (page = 1, pageSize = 10) => {
  try {
    teacherDetailsLoading.value = true;
    
    const response = await getTeacherProjectDetails({
      userId: selectedTeacherDetailId.value,
      page: page,
      pageSize: pageSize,
      range: teacherRankChartRange.value,
      reviewStatus: teacherRankChartReviewStatus.value !== 'all' ? teacherRankChartReviewStatus.value : undefined
    });
    
    if (response && response.code === 200) {
      teacherProjectDetails.value = response.data.list || [];
      teacherDetailsTotalScore.value = parseFloat(response.data.totalScore || 0);
      teacherDetailsPagination.total = response.data.total || 0;
    } else {
      message.error(response?.message || '获取教师项目详情失败');
    }
  } catch (error) {
    console.error('获取教师项目详情失败:', error);
    message.error('获取教师项目详情失败: ' + (error.message || '未知错误'));
  } finally {
    teacherDetailsLoading.value = false;
  }
};

// Excel转JSON相关状态
const convertingExcel = ref(false)
const importPreviewVisible = ref(false)
const importPreviewLoading = ref(false)
const importPreviewData = ref([])
const importInProgress = ref(false)
const importResultVisible = ref(false)
const importResults = ref({
  total: 0,
  success: 0,
  failed: 0,
  current: 0,
  details: []
})
const userIdCheckResults = ref({
  total: 0,
  found: 0,
  notFound: 0
})

// Excel文件上传前检查
const beforeExcelUpload = (file) => {
  // 检查文件类型
  const isExcel = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
    file.type === 'application/vnd.ms-excel' || 
    file.name.endsWith('.csv');
  
  if (!isExcel) {
    message.error('只能上传Excel文件 (.xlsx, .xls, .csv)!');
    return false;
  }
  
  // 文件大小限制：20MB
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    message.error('文件大小不能超过20MB!');
    return false;
  }
  
  console.log('Excel文件检查通过，准备开始转换');
  return true; // 返回true允许上传
};

// 处理Excel到JSON转换
const handleExcelToJsonConvert = async ({ file }) => {
  // 创建唯一的消息ID
  const messageKey = `excel_convert_${Date.now()}`;
  
  try {
    convertingExcel.value = true;
    
    // 显示正在处理的提示，使用key可以后续精确销毁
    message.loading({ 
      content: '正在解析Excel文件，请稍候...', 
      key: messageKey,
      duration: 0 // 不自动消失
    });
    
    // 获取文件基本名（不含扩展名）
    const fileName = file.name.split('.').slice(0, -1).join('.') || 'teaching_reform_export';
    
    // 默认配置：表头在第3行 (从0开始计数，索引为2)
    const options = {
      headerRow: 2, // 第3行作为表头（索引从0开始，所以是2）
      sheetName: null // 默认使用第一个工作表
    };
    
    console.log('开始转换Excel文件:', fileName);
    
    // 转换Excel为JSON
    const projectsData = await excelToTeachingReformProjectsJson(file, options);
    
    // 更新消息为处理完成
    message.loading({ 
      content: '数据解析完成，准备校验用户ID...', 
      key: messageKey,
      duration: 1 // 1秒后自动消失
    });
    
    if (projectsData.length === 0) {
      message.warning('未从Excel文件中提取到任何有效数据', 3);
      return;
    }
    
    console.log(`解析成功，共 ${projectsData.length} 条项目数据`);
    
    // 保存解析后的数据以便下载
    lastConvertedExcelData.value = projectsData;
    
    // 准备数据预览
    await prepareProjectsForPreview(projectsData);
    
    // 显示导入预览对话框
    importPreviewVisible.value = true;
    
  } catch (error) {
    console.error('Excel转JSON处理失败:', error);
    // 确保任何loading消息都被清除
    message.destroy(messageKey);
    message.error(`Excel文件处理失败: ${error.message || '未知错误'}`, 5);
  } finally {
    // 最后确保状态复位
    convertingExcel.value = false;
    
    // 延迟0.5秒后销毁所有可能存在的消息，确保不会有消息一直存在
    setTimeout(() => {
      message.destroy(messageKey);
    }, 500);
  }
};

// 准备项目数据用于预览
const prepareProjectsForPreview = async (projects) => {
  importPreviewLoading.value = true;
  importPreviewData.value = [];
  
  try {
    // 重置用户ID检查结果
    userIdCheckResults.value = {
      total: 0,
      found: 0,
      notFound: 0
    };
    
    // 处理每个项目
    const processedProjects = [];
    
    for (const project of projects) {
      // 创建预览项
      const previewItem = {
        ...project,
        rawData: project,
        key: Math.random().toString(36).substring(2),
        userIdCheckStatus: 'checking' // 初始状态为"检查中"
      };
      
      processedProjects.push(previewItem);
    }
    
    // 更新预览数据
    importPreviewData.value = processedProjects;
    
    // 对每个项目的参与者进行用户ID检查
    userIdCheckResults.value.total = processedProjects.length;
    
    for (const project of processedProjects) {
      if (project.participants && project.participants.length > 0) {
        let allFound = true;
        
        for (const participant of project.participants) {
          try {
            // 查询用户ID
            const response = await usersSearch({ keyword: participant.name });
            
            if (response.code === 200 && response.data && response.data.length > 0) {
              // 找到匹配的用户
              const user = response.data[0];
              participant.participantId = user.id;
              participant.userId = user.id;
              participant.studentNumber = user.studentNumber || participant.studentNumber;
            } else {
              // 未找到用户
              allFound = false;
            }
          } catch (error) {
            console.error(`查询用户 ${participant.name} 失败:`, error);
            allFound = false;
          }
        }
        
        // 更新项目的用户ID检查状态
        project.userIdCheckStatus = allFound ? 'found' : 'notFound';
        
        // 更新统计数据
        if (allFound) {
          userIdCheckResults.value.found++;
        } else {
          userIdCheckResults.value.notFound++;
        }
      }
    }
    
    console.log('用户ID检查完成:', userIdCheckResults.value);
    
  } catch (error) {
    console.error('准备预览数据时出错:', error);
    message.error('准备预览数据时出错: ' + (error.message || '未知错误'));
  } finally {
    importPreviewLoading.value = false;
  }
};

// 取消导入预览
const handleCancelImportPreview = () => {
  importPreviewVisible.value = false;
  importPreviewData.value = [];
  importInProgress.value = false;
};

// 开始导入
const handleStartImport = async () => {
  if (importPreviewData.value.length === 0) {
    message.warning('没有可导入的数据');
    return;
  }
  
  try {
    importInProgress.value = true;
    
    // 初始化导入结果
    importResults.value = {
      total: importPreviewData.value.length,
      success: 0,
      failed: 0,
      current: 0,
      details: []
    };
    
    for (let i = 0; i < importPreviewData.value.length; i++) {
      const project = importPreviewData.value[i];
      importResults.value.current = i + 1;
      
      try {
        // 获取级别名称并查找对应的ID
        const levelName = project.categoryName || project.levelName;
        const level = levelOptions.value.find(l => l.levelName === levelName);
        const levelId = level ? level.id : undefined;
        
        if (!levelId) {
          console.warn(`未找到级别 "${levelName}" 对应的ID`);
        }
        
        // 准备导入数据 - 直接使用Excel中的原始数据，不做任何计算
        const projectData = {
          projectName: project.projectName,
          projectNumber: project.projectNumber || '',
          approvalDepartment: project.approvalDepartment || '默认部门',
          approvalDate: project.approvalDate,
          approvalFund: project.approvalFund || 0,
          levelId: levelId, // 使用查找到的levelId而不是levelName
          startYear: project.startYear || new Date().getFullYear(),
          endYear: project.endYear || (new Date().getFullYear() + 2),
          remark: project.remark || '',
          // 直接使用Excel中读取的参与者数据，不进行任何分配比例的计算
          participants: project.participants.map(p => ({
            userId: p.userId || p.participantId,
            allocationRatio: p.allocationRatio, // 直接使用Excel中的原始比例
            isLeader: p.isLeader // 保持Excel中的主持人标记
          }))
        };
        
        // 发送请求添加项目
        const response = await addProject(projectData);
        
        if (response.code === 200) {
          importResults.value.success++;
          importResults.value.details.push({
            projectName: project.projectName,
            status: 'success',
            message: '导入成功'
          });
        } else {
          importResults.value.failed++;
          importResults.value.details.push({
            projectName: project.projectName,
            status: 'error',
            message: response.message || '导入失败'
          });
        }
      } catch (error) {
        console.error(`导入项目 ${project.projectName} 失败:`, error);
        importResults.value.failed++;
        importResults.value.details.push({
          projectName: project.projectName,
          status: 'error',
          message: error.message || '导入过程中发生错误'
        });
      }
    }
    
    // 显示导入结果
    importPreviewVisible.value = false;
    importResultVisible.value = true;
    
    // 重新获取数据
    fetchData();
    
  } catch (error) {
    console.error('导入过程中发生错误:', error);
    message.error('导入过程中发生错误: ' + (error.message || '未知错误'));
  } finally {
    importInProgress.value = false;
  }
};

// 导出失败记录
const exportFailedRecords = async () => {
  try {
    const failedRecords = importResults.value.details
      .filter(item => item.status === 'error')
      .map(item => {
        const record = importPreviewData.value.find(p => p.projectName === item.projectName);
        return {
          ...record.rawData,
          errorMessage: item.message
        };
      });
    
    if (failedRecords.length === 0) {
      message.info('没有失败记录可导出');
      return;
    }
    
    await downloadJson(failedRecords, '导入失败项目记录');
    message.success('导出失败记录成功');
  } catch (error) {
    console.error('导出失败记录时出错:', error);
    message.error('导出失败记录时出错: ' + (error.message || '未知错误'));
  }
};

// 下载转换后的JSON文件
const handleDownloadJson = async () => {
  if (!lastConvertedExcelData.value || lastConvertedExcelData.value.length === 0) {
    message.warning('没有可下载的数据');
    return;
  }
  
  try {
    await downloadJson(lastConvertedExcelData.value, '教学改革项目数据');
    message.success('JSON文件下载成功');
  } catch (error) {
    console.error('下载JSON文件失败:', error);
    message.error('下载JSON文件失败: ' + (error.message || '未知错误'));
  }
};

// 存储最近转换的Excel数据
const lastConvertedExcelData = ref([]);

// 处理重新提交审核
const handleResubmit = (record) => {
  Modal.confirm({
    title: '确认重新提交审核',
    content: '是否确认将该项目重新提交审核？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await resubmitForReview(record.id);
    }
  });
};

// 重新提交审核接口调用
const resubmitForReview = async (id) => {
  try {
    isLoading.value = true;
    const response = await reapplyReview({ id });
    
    if (response && response.code === 200) {
      message.success('重新提交审核成功');
      fetchData(); // 刷新数据
    } else {
      message.error(response?.message || '重新提交审核失败');
    }
  } catch (error) {
    console.error('重新提交审核失败:', error);
    message.error('重新提交审核失败: ' + (error.message || error));
  } finally {
    isLoading.value = false;
  }
};

// 组件卸载时清理
onBeforeUnmount(() => {
  try {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', resizeHandler)

    // 清理图表实例
    if (levelChart && !levelChart.isDisposed()) {
      levelChart.dispose()
    }
    levelChart = null

    if (reviewStatusChart && !reviewStatusChart.isDisposed()) {
      reviewStatusChart.dispose()
    }
    reviewStatusChart = null

    if (timeChart && !timeChart.isDisposed()) {
      timeChart.dispose()
    }
    timeChart = null
  } catch (error) {
    console.error('清理图表实例失败:', error)
  }
});

// 添加时间范围文本
const timeRangeText = ref('');

// 获取时间范围
const getTimeRange = () => {
  // 调用API获取时间范围
  getScoreTimeRange('teachingReformProjects').then(res => {
    if (res.code === 200 && res.data) {
      timeRangeText.value = res.data.timeRange || '';
    } else {
      timeRangeText.value = '暂无时间范围数据';
    }
  }).catch(error => {
    console.error('获取时间范围失败:', error);
    timeRangeText.value = '获取时间范围失败';
  });
};

// 在onMounted中调用
onMounted(() => {
  getTimeRange();
});

</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';

.error-row {
  background-color: #fff1f0;
}
</style>