const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const ExcelJS = require('exceljs');
const multer = require('multer');
const { Op } = require('sequelize');
const Sequelize = require('sequelize');
const { isProjectInTimeRange, isYearRangeOverlap, getTimeIntervalByName, getUserInfoFromRequest, convertStoredProcResultToArray, getSequelizeInstance } = require('../../../utils/others');
const academicAppointmentModel = require('../../../models/v1/mapping/academicAppointmentsModel');
const associationLevelModel = require('../../../models/v1/mapping/associationLevelsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const fileModel = require('../../../models/v1/mapping/fileModel');
const upload = multer({ dest: 'uploads/' });
const { updateUserRankings } = require('../../../utils/rankingUtils');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');

/**
 * 获取学术任职列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAppointments = async (req, res) => {
  try {
    const {
      associationName,
      levelId,
      startYear,
      endYear,
      userId,
      page = 1,
      pageSize = 10,
      range = 'in',
      reviewStatus = 'all',
      sortField = 'createdAt', // 排序字段，默认按创建时间排序
      sortOrder = 'desc', // 排序方式，默认降序
      query, // 关键词搜索
      isExport = false // 是否导出，如果为true则不分页
    } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("academicAppointments");

    // 构建查询条件
    const whereCondition = {};

    // 如果提供了userId，添加到查询条件
    if (userId) {
      whereCondition.userId = userId;
    }

    // 根据range参数筛选数据 - 使用isYearRangeOverlap函数优化
    if (range == 'in' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围内：有效任职（与时间区间有交集）
      whereCondition[Op.and] = [
        {
          [Op.or]: [
            // 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
            {
              [Op.and]: [
                { startYear: { [Op.lte]: intervalEndYear } },
                {
                  [Op.or]: [
                    { endYear: null },
                    { endYear: { [Op.gte]: intervalStartYear } }
                  ]
                }
              ]
            }
          ]
        }
      ];
    } else if (range == 'out' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围外：无效任职（与时间区间无交集）
      whereCondition[Op.or] = [
        // 开始年份 > 区间结束年份
        { startYear: { [Op.gt]: intervalEndYear } },
        // 结束年份不为空 AND 结束年份 < 区间开始年份
        {
          [Op.and]: [
            { endYear: { [Op.ne]: null } },
            { endYear: { [Op.lt]: intervalStartYear } }
          ]
        }
      ];
    }
    // range == 'all' 不添加额外筛选条件

    // 根据审核状态过滤
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }

    // 添加其他筛选条件
    if (associationName) {
      whereCondition.associationName = { [Op.like]: `%${associationName}%` };
    }

    if (levelId) {
      whereCondition.levelId = levelId;
    }

    if (startYear) {
      whereCondition.startYear = { [Op.gte]: startYear };
    }

    if (endYear) {
      whereCondition.endYear = { [Op.lte]: endYear };
    }

    // 关键词搜索
    if (query) {
      whereCondition[Op.or] = [
        { associationName: { [Op.like]: `%${query}%` } },
        { position: { [Op.like]: `%${query}%` } },
        { remark: { [Op.like]: `%${query}%` } }
      ];
    }

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;

    // 查询数据 - 如果是导出，不使用分页限制
    const queryOptions = {
      where: whereCondition,
      order: [[sortField, sortOrder]],
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ]
    };

    // 仅在非导出模式下应用分页
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }

    const { count, rows } = await academicAppointmentModel.findAndCountAll(queryOptions);

    // 处理数据
    const processedAppointments = [];

    for (const appointment of rows) {
      const appointmentJson = appointment.toJSON();

      // 查询关联的文件
      const files = await fileModel.findAll({
        where: {
          projectId: appointment.id,
          relatedId: appointment.id,
          relatedType: 'academicAppointments',
          isDeleted: 0
        }
      });

      // 处理文件信息，添加URL和其他前端需要的信息
      appointmentJson.attachments = files.map(file => {
        const fileData = file.toJSON();
        // 构造文件URL
        const filePath = fileData.filePath;
        const url = filePath ? (filePath.startsWith('/') ? filePath : `/${filePath}`) : '';

        return {
          id: fileData.id,
          name: fileData.originalName,
          fileName: fileData.fileName,
          size: fileData.fileSize,
          type: fileData.mimeType,
          extension: fileData.extension,
          url: url,
          filePath: fileData.filePath,
          uploadTime: fileData.createdAt
        };
      });

      // 添加是否在时间范围内的标记
      if (timeInterval) {
        appointmentJson.isInTimeRange = isYearRangeOverlap(
          appointmentJson.startYear,
          appointmentJson.endYear,
          timeInterval.startTime,
          timeInterval.endTime
        );
      } else {
        appointmentJson.isInTimeRange = false;
      }

      processedAppointments.push(appointmentJson);
    }

    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: processedAppointments,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取学术任职列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取学术任职列表失败',
      error: error.message
    });
  }
};

/**
 * 获取学术任职详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAppointmentDetail = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少学术任职ID',
        data: null
      });
    }

    // 查询学术任职详情
    const appointment = await academicAppointmentModel.findByPk(id, {
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });

    if (!appointment) {
      return res.status(404).json({
        code: 404,
        message: '未找到学术任职',
        data: null
      });
    }

    // 将任职数据转换为JSON对象
    const appointmentData = appointment.toJSON();

    // 查询任职关联的文件列表
    const files = await fileModel.findAll({
      where: {
        projectId: id,
        relatedId: id,
        relatedType: 'academicAppointments'
      },
      attributes: [
        'id',
        'fileName',
        'originalName',
        'filePath',
        'fileSize',
        'mimeType',
        'extension',
        'uploaderId',
        'relatedId',
        'relatedType',
        'createdAt',
        'updatedAt'
      ],
      order: [['createdAt', 'DESC']]
    });

    // 处理文件信息，添加URL和其他前端需要的信息
    appointmentData.attachments = files.map(file => {
      const fileData = file.toJSON();
      // 构造文件URL
      const filePath = fileData.filePath;
      const url = filePath ? (filePath.startsWith('/') ? filePath : `/${filePath}`) : '';

      return {
        id: fileData.id,
        name: fileData.originalName,
        fileName: fileData.fileName,
        size: fileData.fileSize,
        type: fileData.mimeType,
        extension: fileData.extension,
        url: url,
        filePath: fileData.filePath,
        uploadTime: fileData.createdAt
      };
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: appointmentData
    });
  } catch (error) {
    console.error('获取学术任职详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取学术任职详情失败',
      error: error.message
    });
  }
};

/**
 * 创建学术任职
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAppointment = async (req, res) => {
  // 获取数据库连接实例
  let dbSequelize;
  let transactionCompleted = false; // 添加变量跟踪事务状态
  try {
    dbSequelize = getSequelizeInstance(academicAppointmentModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }

  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();

  try {
    const {
      userId,
      associationName,
      position,
      levelId,
      startYear,
      endYear,
      remark,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl    // 接收前端传递的文件路径
    } = req.body;

    // 验证必要字段
    if (!userId || !associationName || !position || !levelId || !startYear) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少必要字段',
        data: null
      });
    }

    // 创建学术任职
    const appointmentId = uuidv4();
    const appointment = await academicAppointmentModel.create({
      id: appointmentId,
      userId,
      associationName,
      position,
      levelId,
      startYear,
      endYear,
      remark,
      ifReviewer: null,
      status: 1,
      attachmentUrl: null, // 初始设置为null，稍后更新
      createdAt: new Date(),
      updatedAt: new Date()
    }, { transaction });

    // 处理关联文件 - 使用前端传递的文件ID而非重新创建文件记录
    let processedFileIds = [];
    if (fileIds) {
      let fileIdArray = [];

      // 解析文件ID数组
      if (typeof fileIds == 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }

      // 解析attachmentUrl
      let attachmentUrlArray = [];
      if (attachmentUrl) {
        if (typeof attachmentUrl == 'string') {
          try {
            attachmentUrlArray = JSON.parse(attachmentUrl);
          } catch (error) {
            console.error('解析文件路径出错:', error);
            attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
          }
        } else if (Array.isArray(attachmentUrl)) {
          attachmentUrlArray = attachmentUrl;
        }
      }

      // 如果有文件ID，关联到任职
      if (fileIdArray.length > 0) {
        // 为每个文件ID更新关联关系
        for (let i = 0; i < fileIdArray.length; i++) {
          const fileId = fileIdArray[i];
          const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;

          const updateData = {
            projectId: appointment.id,
            relatedId: appointment.id,
            relatedType: 'academicAppointments'
          };

          // 如果存在文件路径，添加到更新数据中
          if (filePath) {
            updateData.filePath = filePath;
          }

          await fileModel.update(
            updateData,
            {
              where: { id: fileId },
              transaction
            }
          );

          processedFileIds.push(fileId);
        }
      }
    } else if (req.files && req.files.length > 0) {
      // 处理通过multer上传的文件
      const uploadedFiles = req.files;

      for (const file of uploadedFiles) {
        // 创建文件记录
        const fileRecord = await fileModel.create({
          id: uuidv4(),
          originalName: file.originalname,
          filePath: file.path,
          mimeType: file.mimetype,
          size: file.size,
          projectId: appointment.id,
          relatedId: appointment.id,
          relatedType: 'academicAppointments'
        }, { transaction });

        processedFileIds.push(fileRecord.id);
      }
    }

    // 提交事务
    await transaction.commit();
    transactionCompleted = true; // 标记事务已完成

    // 事务提交成功后，异步移动文件到指定的任职目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'academicAppointments'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${appointment.id}/`;

        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }

        // 新增变量跟踪已移动的文件路径
        const movedFilePaths = [];

        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId }
          });

          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }

          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);

          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }

              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: appointment.id,
                relatedId: appointment.id,
                relatedType: storagePath
              });

              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }

        // 更新任职的attachmentUrl为任职文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            // 使用标准格式的任职文件夹路径，确保不包含文件名
            const appointmentFolderPath = `uploads\\academicAppointments\\${appointment.id}\\`;

            await appointment.update({
              attachmentUrl: appointmentFolderPath
            });
            console.log(`已更新任职 ${appointment.id} 的attachmentUrl为: ${appointmentFolderPath}`);

          } catch (updateError) {
            console.error('更新任职attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响任职创建的返回结果，仅记录错误
        console.error('移动文件到任职目录失败:', moveError);
      }
    }

    // 查询创建的任职详情
    try {
      const createdAppointment = await academicAppointmentModel.findByPk(appointmentId, {
        include: [
          {
            model: associationLevelModel,
            as: 'level',
            attributes: ['id', 'levelName', 'score']
          },
          {
            model: userModel,
            as: 'user',
            attributes: ['id', 'nickname', 'username', 'studentNumber']
          }
        ]
      });

      // 查询关联的文件
      const files = await fileModel.findAll({
        where: {
          projectId: appointmentId,
          relatedId: appointmentId,
          relatedType: 'academicAppointments'
        }
      });

      // 添加文件信息到响应数据
      const responseData = createdAppointment.toJSON();
      responseData.attachments = files.map(file => file.toJSON());

      return res.status(201).json({
        code: 200,
        message: '创建成功',
        data: responseData
      });
    } catch (queryError) {
      console.error('查询任职详情失败:', queryError);
      // 即使查询详情失败，任职仍然已创建成功
      return res.status(201).json({
        code: 200,
        message: '创建成功，但获取详情失败',
        data: {
          id: appointmentId
        },
        error: queryError.message
      });
    }
  } catch (error) {

    // 只有在事务未完成时才尝试回滚
    if (!transactionCompleted) {
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        console.error('回滚事务失败:', rollbackError);
      }
    }

    console.error('创建学术任职失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建学术任职失败',
      error: error.message
    });
  }
};

/**
 * 更新学术任职
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateAppointment = async (req, res) => {
  // 获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(academicAppointmentModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }

  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();

  try {
    const { id } = req.params;
    const userInfo = await getUserInfoFromRequest(req);

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少学术任职ID',
        data: null
      });
    }

    const {
      userId,
      associationName,
      position,
      levelId,
      startYear,
      endYear,
      remark,
      fileIds,         // 接收前端传递的文件ID数组
      attachmentUrl    // 接收前端传递的文件路径
    } = req.body;

    // 查询学术任职是否存在 - 获取旧数据以便后续比较
    const oldAppointment = await academicAppointmentModel.findByPk(id, { 
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        }
      ],
      transaction 
    });

    if (!oldAppointment) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到学术任职',
        data: null
      });
    }

    // 保存旧数据，用于后续更新排名
    const oldLevelId = oldAppointment.levelId;
    const oldLevelScore = oldAppointment.level ? oldAppointment.level.score : 0;
    const oldUserId = oldAppointment.userId;
    const oldStartYear = oldAppointment.startYear;
    const oldEndYear = oldAppointment.endYear;
    const oldIfReviewer = oldAppointment.ifReviewer;

    // 权限检查：管理员或者自己的记录可以编辑
    const isAdmin = userInfo.role && (userInfo.role.roleAuth == 'ADMIN-LV2' || userInfo.role.roleAuth == 'SUPER');
    const isOwner = oldAppointment.userId == userInfo.id;

    if (!isAdmin && !isOwner) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限编辑该学术任职',
        data: null
      });
    }

    // 如果已经审核，非管理员不能编辑
    if (oldAppointment.ifReviewer == 1 && !isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '该学术任职已审核，无法编辑',
        data: null
      });
    }

    // 更新学术任职
    const updateData = {};
    if (userId !== undefined) updateData.userId = userId;
    if (associationName !== undefined) updateData.associationName = associationName;
    if (position !== undefined) updateData.position = position;
    if (levelId !== undefined) updateData.levelId = levelId;
    if (startYear !== undefined) updateData.startYear = startYear;
    if (endYear !== undefined) updateData.endYear = endYear;
    if (remark !== undefined) updateData.remark = remark;
    // 设置标准化的附件文件夹路径
    updateData.attachmentUrl = `uploads\\academicAppointments\\${id}\\`;
    updateData.updatedAt = new Date();

    await oldAppointment.update(updateData, { transaction });

    // 处理文件关联
    let processedFileIds = [];
    let fileIdArray = [];

    // 解析文件ID数组
    if (fileIds) {
      if (typeof fileIds == 'string') {
        try {
          fileIdArray = JSON.parse(fileIds);
        } catch (error) {
          console.error('解析文件ID出错:', error);
          fileIdArray = [fileIds]; // 如果解析失败但传的是单个ID字符串
        }
      } else if (Array.isArray(fileIds)) {
        fileIdArray = fileIds;
      }
    } else {
      fileIdArray = [];
    }

    // 解析attachmentUrl
    let attachmentUrlArray = [];
    if (attachmentUrl) {
      if (typeof attachmentUrl == 'string') {
        try {
          attachmentUrlArray = JSON.parse(attachmentUrl);
        } catch (error) {
          console.error('解析文件路径出错:', error);
          attachmentUrlArray = [attachmentUrl]; // 如果解析失败但传的是单个路径字符串
        }
      } else if (Array.isArray(attachmentUrl)) {
        attachmentUrlArray = attachmentUrl;
      }
    }

    // 获取当前任职关联的所有未删除文件
    const existingFiles = await fileModel.findAll({
      where: {
        projectId: id,
        relatedType: 'academicAppointments',
        relatedId: id,
      },
      attributes: ['id'],
      transaction
    });

    // 找出需要删除的文件ID（存在于现有文件但不在fileIds中的文件）
    if (existingFiles.length > 0) {
      const existingFileIds = existingFiles.map(file => file.id);
      const filesToDelete = existingFileIds.filter(fileId => !fileIdArray.includes(fileId));

      // 删除文件记录和物理文件
      if (filesToDelete.length > 0) {
        console.log(`将删除以下文件: ${filesToDelete.join(', ')}`);

        // 首先获取这些文件的完整信息，包括filePath
        const filesInfo = await fileModel.findAll({
          where: {
            id: { [Op.in]: filesToDelete }
          },
          transaction
        });

        // 删除物理文件（记录路径以便后面删除）
        const physicalFilesToDelete = filesInfo.map(file => file.filePath).filter(Boolean);

        // 从数据库中标记文件记录为已删除
        await fileModel.update(
          { isDeleted: 1 },
          {
            where: {
              id: { [Op.in]: filesToDelete }
            },
            transaction
          }
        );

        // 添加物理文件路径到事务后要删除的文件列表中
        req.filesToDeleteAfterCommit = req.filesToDeleteAfterCommit || [];
        req.filesToDeleteAfterCommit.push(...physicalFilesToDelete);
      }
    }

    // 处理文件关联
    if (fileIdArray.length > 0) {
      // 为每个文件ID更新关联关系
      for (let i = 0; i < fileIdArray.length; i++) {
        const fileId = fileIdArray[i];
        const filePath = i < attachmentUrlArray.length ? attachmentUrlArray[i] : null;

        const updateData = {
          projectId: oldAppointment.id,
          relatedId: oldAppointment.id,
          relatedType: 'academicAppointments'
        };

        // 如果存在文件路径，添加到更新数据中
        if (filePath) {
          updateData.filePath = filePath;
        }

        await fileModel.update(
          updateData,
          {
            where: { id: fileId },
            transaction
          }
        );

        processedFileIds.push(fileId);
      }
    }

    // 处理新上传的文件
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        // 创建文件记录
        const fileRecord = await fileModel.create({
          id: uuidv4(),
          originalName: file.originalname,
          filePath: file.path,
          mimeType: file.mimetype,
          size: file.size,
          projectId: oldAppointment.id,
          relatedId: oldAppointment.id,
          relatedType: 'academicAppointments',
          isDeleted: 0
        }, { transaction });

        processedFileIds.push(fileRecord.id);
      }
    }

    // 提交事务
    await transaction.commit();

    // 事务提交成功后，删除标记为删除的物理文件
    if (req.filesToDeleteAfterCommit && req.filesToDeleteAfterCommit.length > 0) {
      try {
        for (const filePath of req.filesToDeleteAfterCommit) {
          if (filePath && fs.existsSync(filePath)) {
            try {
              fs.unlinkSync(filePath);
              console.log(`成功删除物理文件: ${filePath}`);
            } catch (deleteError) {
              console.error(`删除物理文件失败: ${filePath}`, deleteError);
            }
          }
        }
      } catch (error) {
        console.error('删除物理文件时出错:', error);
      }
    }

    // 事务提交成功后，异步移动文件到指定的任职目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'academicAppointments'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${oldAppointment.id}/`;

        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }

        // 跟踪已移动的文件路径
        const movedFilePaths = [];

        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId, isDeleted: 0 }
          });

          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }

          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);

          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }

              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: oldAppointment.id,
                relatedId: oldAppointment.id,
                relatedType: storagePath
              });

              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }

        // 更新任职的attachmentUrl为任职文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            const appointmentFolderPath = `uploads\\academicAppointments\\${oldAppointment.id}\\`;

            await oldAppointment.update({
              attachmentUrl: appointmentFolderPath
            });
            console.log(`已更新任职 ${oldAppointment.id} 的attachmentUrl为: ${appointmentFolderPath}`);
          } catch (updateError) {
            console.error('更新任职attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响任职更新的返回结果，仅记录错误
        console.error('移动文件到任职目录失败:', moveError);
      }
    }

    // 获取更新后的学术任职数据（包括新级别信息）
    const updatedAppointment = await academicAppointmentModel.findByPk(id, {
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        }
      ],
      transaction
    });

    // 判断是否需要更新排名
    // 只有已审核的任职才需要更新排名，并且只在以下情况更新：
    // 1. 级别发生变化（分数可能不同）
    // 2. 用户发生变化（需要更新不同用户的排名）
    // 3. 年份范围发生变化（可能影响是否在统计时间范围内）
    if (oldAppointment.ifReviewer == 1) {
      const newLevelId = updatedAppointment.levelId;
      const newLevelScore = updatedAppointment.level ? updatedAppointment.level.score : 0;
      const newUserId = updatedAppointment.userId;
      const newStartYear = updatedAppointment.startYear;
      const newEndYear = updatedAppointment.endYear;

      // 检查是否需要更新排名
      const needUpdateRanking = 
        oldLevelId !== newLevelId || 
        oldLevelScore !== newLevelScore ||
        oldUserId !== newUserId ||
        oldStartYear !== newStartYear ||
        oldEndYear !== newEndYear;

      if (needUpdateRanking) {
        try {
          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("academicAppointments");
          
          // 判断旧记录是否在时间区间内
          const oldIsInTimeRange = isYearRangeOverlap(
            oldStartYear, 
            oldEndYear, 
            new Date(timeInterval.startTime).getFullYear(),
            new Date(timeInterval.endTime).getFullYear()
          );

          // 判断新记录是否在时间区间内
          const newIsInTimeRange = isYearRangeOverlap(
            newStartYear,
            newEndYear,
            new Date(timeInterval.startTime).getFullYear(),
            new Date(timeInterval.endTime).getFullYear()
          );

          // 定义将要更新的排名表
          let oldRankingTables = [];
          let newRankingTables = [];
          
          // 确定旧记录应该更新的排名表
          if (oldIsInTimeRange) {
            oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
          } else {
            oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
          }
          
          // 确定新记录应该更新的排名表
          if (newIsInTimeRange) {
            newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
          } else {
            newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
          }

          // 如果用户ID发生变化，需要对两个用户分别处理
          if (oldUserId !== newUserId) {
            // 先减去旧用户的分数
            for (const table of oldRankingTables) {
              await updateUserRankings(
                [oldUserId],
                table,
                'academicAppointments',
                [1], // 减少一个计数
                [oldLevelScore],
                transaction,
                "subtract" // 减分操作
              );
            }
            
            // 再为新用户添加分数
            for (const table of newRankingTables) {
              await updateUserRankings(
                [newUserId],
                table,
                'academicAppointments',
                [1], // 增加一个计数
                [newLevelScore],
                transaction,
                "add" // 加分操作
              );
            }
          } 
          // 如果用户ID没变，但其他条件变了
          else if (needUpdateRanking) {
            // 如果时间范围导致的表变更
            if (JSON.stringify(oldRankingTables) !== JSON.stringify(newRankingTables)) {
              // 从旧表中减去
              for (const table of oldRankingTables) {
                await updateUserRankings(
                  [oldUserId],
                  table,
                  'academicAppointments',
                  [1], // 减少一个计数
                  [oldLevelScore],
                  transaction,
                  "subtract" // 减分操作
                );
              }
              
              // 向新表中添加
              for (const table of newRankingTables) {
                await updateUserRankings(
                  [newUserId],
                  table,
                  'academicAppointments',
                  [1], // 增加一个计数
                  [newLevelScore],
                  transaction,
                  "add" // 加分操作
                );
              }
            } 
            // 如果只是级别/分数变更但表没变
            else if (oldLevelScore !== newLevelScore) {
              // 对相同的表进行分数调整
              for (const table of newRankingTables) {
                // 先减去旧分数
                await updateUserRankings(
                  [newUserId],
                  table,
                  'academicAppointments',
                  [0], // 不改变计数
                  [oldLevelScore],
                  transaction,
                  "subtract" // 减分操作
                );
                
                // 再添加新分数
                await updateUserRankings(
                  [newUserId],
                  table,
                  'academicAppointments',
                  [0], // 不改变计数
                  [newLevelScore],
                  transaction,
                  "add" // 加分操作
                );
              }
            }
          }
          
          console.log('用户排名更新成功');
        } catch (rankingError) {
          console.error('更新排名失败:', rankingError);
          await transaction.rollback();
          throw new Error(`更新排名失败: ${rankingError.message}`);
        }
      }
    }

    // 提交事务
    await transaction.commit();

    // 事务提交成功后，删除标记为删除的物理文件
    if (req.filesToDeleteAfterCommit && req.filesToDeleteAfterCommit.length > 0) {
      try {
        for (const filePath of req.filesToDeleteAfterCommit) {
          if (filePath && fs.existsSync(filePath)) {
            try {
              fs.unlinkSync(filePath);
              console.log(`成功删除物理文件: ${filePath}`);
            } catch (deleteError) {
              console.error(`删除物理文件失败: ${filePath}`, deleteError);
            }
          }
        }
      } catch (error) {
        console.error('删除物理文件时出错:', error);
      }
    }

    // 事务提交成功后，异步移动文件到指定的任职目录
    if (processedFileIds.length > 0) {
      try {
        const storagePath = 'academicAppointments'; // 使用标准存储路径
        const targetDir = `uploads/${storagePath}/${oldAppointment.id}/`;

        // 确保目标目录存在
        if (!fs.existsSync(targetDir)) {
          fs.mkdirSync(targetDir, { recursive: true });
        }

        // 跟踪已移动的文件路径
        const movedFilePaths = [];

        // 对每个文件进行处理
        for (const fileId of processedFileIds) {
          // 查找文件记录
          const fileRecord = await fileModel.findOne({
            where: { id: fileId, isDeleted: 0 }
          });

          if (!fileRecord) {
            console.warn(`文件ID ${fileId} 不存在或已删除`);
            continue;
          }

          // 获取文件当前路径和文件名
          const currentPath = fileRecord.filePath;
          const fileName = path.basename(currentPath);
          const newPath = path.join(targetDir, fileName);

          try {
            // 移动文件
            if (fs.existsSync(currentPath)) {
              try {
                // 尝试直接移动
                fs.renameSync(currentPath, newPath);
              } catch (moveError) {
                console.error('移动文件失败:', moveError);
                // 如果移动失败，尝试复制后删除
                fs.copyFileSync(currentPath, newPath);
                fs.unlinkSync(currentPath);
                console.log(`文件已复制并删除源文件: ${currentPath} -> ${newPath}`);
              }

              // 更新数据库记录
              await fileRecord.update({
                filePath: newPath,
                projectId: oldAppointment.id,
                relatedId: oldAppointment.id,
                relatedType: storagePath
              });

              // 记录已移动的文件路径
              movedFilePaths.push(newPath);
            }
          } catch (error) {
            console.error(`处理文件 ${fileId} 时出错:`, error);
          }
        }

        // 更新任职的attachmentUrl为任职文件夹路径
        if (movedFilePaths.length > 0) {
          try {
            const appointmentFolderPath = `uploads\\academicAppointments\\${oldAppointment.id}\\`;

            await oldAppointment.update({
              attachmentUrl: appointmentFolderPath
            });
            console.log(`已更新任职 ${oldAppointment.id} 的attachmentUrl为: ${appointmentFolderPath}`);
          } catch (updateError) {
            console.error('更新任职attachmentUrl时出错:', updateError);
          }
        }
      } catch (moveError) {
        // 文件移动失败不影响任职更新的返回结果，仅记录错误
        console.error('移动文件到任职目录失败:', moveError);
      }
    }

    // 查询更新后的数据
    const finalUpdatedAppointment = await academicAppointmentModel.findByPk(id, {
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });

    // 查询关联的文件
    const files = await fileModel.findAll({
      where: {
        projectId: id,
        relatedId: id,
        relatedType: 'academicAppointments',
        isDeleted: 0
      }
    });

    // 添加文件信息到响应数据
    const responseData = finalUpdatedAppointment.toJSON();
    responseData.attachments = files.map(file => file.toJSON());

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: responseData
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();

    console.error('更新学术任职失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新学术任职失败',
      error: error.message
    });
  }
};

/**
 * 删除学术任职
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAppointment = async (req, res) => {
  // 获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(academicAppointmentModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }

  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();

  try {
    const { id } = req.params;
    console.log("req.params", req.params);
    console.log("req.body", req.body);

    const userInfo = await getUserInfoFromRequest(req);

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少学术任职ID',
        data: null
      });
    }

    // 查询学术任职是否存在，并获取详细信息（包括级别信息）
    const appointment = await academicAppointmentModel.findByPk(id, { 
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        }
      ],
      transaction 
    });

    if (!appointment) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '未找到学术任职',
        data: null
      });
    }

    // 权限检查：管理员或者自己的记录可以删除
    const isAdmin = userInfo.role && (userInfo.role.roleAuth == 'ADMIN-LV2' || userInfo.role.roleAuth == 'SUPER');
    const isOwner = appointment.userId == userInfo.id;

    if (!isAdmin && !isOwner) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限删除该学术任职',
        data: null
      });
    }

    // 如果已经审核，非管理员不能删除
    if (!isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '该学术任职已审核，无法删除',
        data: null
      });
    }

    // 收集任职文件夹路径
    let appointmentFolderPaths = new Set();

    // 处理任职附件 - 检查是否有附件模型并进行处理
    try {
      const appointmentFiles = await fileModel.findAll({
        where: {
          projectId: id,
          relatedId: id,
          relatedType: 'academicAppointments'
        },
        transaction
      });

      if (appointmentFiles && appointmentFiles.length > 0) {
        console.log(`找到${appointmentFiles.length}个与任职关联的文件记录`);

        // 首先获取这些文件的完整信息，包括filePath
        const filesToDelete = appointmentFiles.map(file => file.id);

        // 从文件路径中提取任职文件夹路径
        for (const file of appointmentFiles) {
          if (file.filePath) {
            // 从filePath中提取任职文件夹路径
            const folderPath = file.filePath.substring(0, file.filePath.lastIndexOf('/'));
            if (folderPath.includes('academicAppointments')) {
              appointmentFolderPaths.add(folderPath);
            }
          }
        }

        // 删除物理文件（记录路径以便后面删除）
        const physicalFilesToDelete = appointmentFiles.map(file => file.filePath).filter(Boolean);

        // 从数据库中删除文件记录，而不是仅标记为已删除
        await fileModel.destroy({
          where: {
            id: { [Op.in]: filesToDelete }
          },
          transaction
        });

        // 添加物理文件路径到事务后要删除的文件列表中
        req.filesToDeleteAfterCommit = req.filesToDeleteAfterCommit || [];
        req.filesToDeleteAfterCommit.push(...physicalFilesToDelete);
      } else {
        console.log('未找到与任职关联的文件记录');
      }
    } catch (attachmentError) {
      console.warn('处理任职文件时出错:', attachmentError);
      // 继续执行，不因附件删除失败而中断整个操作
    }

    // 如果任职已审核，需要更新排名
    if (appointment.ifReviewer == 1) {
      try {
        // 获取必要的数据
        const userId = appointment.userId;
        const baseScore = appointment.level ? appointment.level.score : 0;
        const startYear = appointment.startYear;
        const endYear = appointment.endYear;
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("academicAppointments");
        
        // 判断任职是否在时间区间内
        const isInTimeRange = isYearRangeOverlap(
          startYear, 
          endYear, 
          new Date(timeInterval.startTime).getFullYear(),
          new Date(timeInterval.endTime).getFullYear()
        );
        
        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }
        
        // 从排名表中减去分数和计数
        for (const table of rankingTables) {
          await updateUserRankings(
            [userId],
            table,
            'academicAppointments',
            [1], // 删除一个计数
            [baseScore],
            transaction,
            "subtract" // 减分操作
          );
        }
        
        console.log(`已从排名表 ${rankingTables.join(', ')} 中减去分数: ${baseScore}`);
      } catch (rankingError) {
        console.error('更新排名表失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }

    // 从数据库中删除任职记录，而不是仅标记为已删除
    await appointment.destroy({ transaction });

    // 提交事务
    await transaction.commit();

    // 事务提交成功后，执行文件系统删除操作（这些操作不能回滚，所以放在事务之外）
    try {
      // 如果有物理文件需要删除
      if (req.filesToDeleteAfterCommit && req.filesToDeleteAfterCommit.length > 0) {
        try {
          for (const filePath of req.filesToDeleteAfterCommit) {
            if (filePath && fs.existsSync(filePath)) {
              try {
                fs.unlinkSync(filePath);
                console.log(`成功删除物理文件: ${filePath}`);
              } catch (deleteError) {
                console.error(`删除物理文件失败: ${filePath}`, deleteError);
              }
            }
          }
        } catch (error) {
          console.error('删除物理文件时出错:', error);
        }
      }

      // 直接使用任职ID构造任职文件夹路径
      const appointmentFolderPath = `uploads/academicAppointments/${id}`;
      console.log(`尝试删除任职文件夹: ${appointmentFolderPath}`);

      // 如果fileController中有deleteDirectoryUtil方法，使用它删除文件夹
      if (typeof fileController !== 'undefined' && typeof fileController.deleteDirectoryUtil == 'function') {
        await fileController.deleteDirectoryUtil(appointmentFolderPath);
      } else {
        // 否则使用fs模块直接删除
        if (fs.existsSync(appointmentFolderPath)) {
          fs.rmdirSync(appointmentFolderPath, { recursive: true });
          console.log(`成功删除任职文件夹: ${appointmentFolderPath}`);
        }
      }
    } catch (fsError) {
      console.warn('删除文件系统中的文件时出错:', fsError);
      // 数据库事务已提交成功，文件系统操作失败不影响API返回结果
    }

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();

    console.error('删除学术任职失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除学术任职失败',
      error: error.message
    });
  }
};

/**
 * 导出学术任职数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportAppointments = async (req, res) => {
  try {
    const {
      associationName,
      levelId,
      startYear,
      endYear,
      userId,
      range = 'all',
      reviewStatus = 'all',
      fileName = '学术任职数据.xlsx'
    } = req.body;

    // 构建查询条件
    const whereCondition = {};

    // 根据审核状态过滤
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }

    if (associationName) {
      whereCondition.associationName = { [Op.like]: `%${associationName}%` };
    }

    if (levelId) {
      whereCondition.levelId = levelId;
    }

    if (startYear) {
      whereCondition.startYear = { [Op.gte]: startYear };
    }

    if (endYear) {
      whereCondition.endYear = { [Op.lte]: endYear };
    }

    if (userId) {
      whereCondition.userId = userId;
    }

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("academicAppointments");

    // 根据range参数筛选数据 - 使用优化后的年份区间交集判断逻辑
    if (range == 'in' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围内：有效任职（与时间区间有交集）
      whereCondition[Op.and] = [
        {
          [Op.or]: [
            // 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
            {
              [Op.and]: [
                { startYear: { [Op.lte]: intervalEndYear } },
                {
                  [Op.or]: [
                    { endYear: null },
                    { endYear: { [Op.gte]: intervalStartYear } }
                  ]
                }
              ]
            }
          ]
        }
      ];
    } else if (range == 'out' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围外：无效任职（与时间区间无交集）
      whereCondition[Op.or] = [
        // 开始年份 > 区间结束年份
        { startYear: { [Op.gt]: intervalEndYear } },
        // 结束年份不为空 AND 结束年份 < 区间开始年份
        {
          [Op.and]: [
            { endYear: { [Op.ne]: null } },
            { endYear: { [Op.lt]: intervalStartYear } }
          ]
        }
      ];
    }
    // range == 'all' 不添加额外筛选条件

    // 查询数据
    const appointments = await academicAppointmentModel.findAll({
      where: whereCondition,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false,
        }
      ],
    });

    // 处理数据，添加时间范围标记
    const processedAppointments = [];

    for (const appointment of appointments) {
      const appointmentJson = appointment.toJSON();

      // 检查是否在统计时间范围内
      let isInTimeRange = false;
      if (timeInterval) {
        isInTimeRange = isYearRangeOverlap(
          appointmentJson.startYear,
          appointmentJson.endYear,
          timeInterval.startTime,
          timeInterval.endTime
        );
      }

      appointmentJson.isInTimeRange = isInTimeRange;

      processedAppointments.push(appointmentJson);
    }

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('学术任职列表');

    // 定义表头
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: '用户', key: 'user', width: 15 },
      { header: '协会/期刊名称', key: 'associationName', width: 30 },
      { header: '职务名称', key: 'position', width: 20 },
      { header: '级别', key: 'levelName', width: 20 },
      { header: '分数', key: 'score', width: 10 },
      { header: '起始年份', key: 'startYear', width: 10 },
      { header: '结束年份', key: 'endYear', width: 10 },
      { header: '统计范围', key: 'inTimeRange', width: 15 },
      { header: '备注', key: 'remark', width: 30 },
      { header: '审核状态', key: 'reviewStatus', width: 10 },
      { header: '审核人', key: 'reviewer', width: 15 },
      { header: '创建时间', key: 'createdAt', width: 20 },
      { header: '更新时间', key: 'updatedAt', width: 20 }
    ];

    // 添加数据行
    processedAppointments.forEach((appointment) => {
      worksheet.addRow({
        id: appointment.id,
        user: appointment.user?.nickname || appointment.user?.username || '',
        associationName: appointment.associationName,
        position: appointment.position,
        levelName: appointment.level?.levelName || '',
        score: appointment.level?.score || '',
        startYear: appointment.startYear,
        endYear: appointment.endYear || '至今',
        inTimeRange: appointment.isInTimeRange ? '范围内' : '范围外',
        remark: appointment.remark || '',
        reviewStatus: appointment.ifReviewer == 1 ? '已通过' :
          (appointment.ifReviewer == 0 ? '已拒绝' : '待审核'),
        reviewer: appointment.reviewer?.nickname || appointment.reviewer?.username || '',
        createdAt: appointment.createdAt,
        updatedAt: appointment.updatedAt
      });
    });

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(fileName)}`);

    // 将工作簿写入响应
    await workbook.xlsx.write(res);

    // 结束响应
    res.end();
  } catch (error) {
    console.error('导出学术任职数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出学术任职数据失败',
      error: error.message
    });
  }
};

/**
 * 导入学术任职数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importAppointments = async (req, res) => {
  try {
    // 检查是否存在文件
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未上传文件',
        data: null
      });
    }

    // 读取上传的Excel文件
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(req.file.path);
    const worksheet = workbook.getWorksheet(1);

    // 准备结果统计
    const result = {
      total: 0,
      success: 0,
      failed: 0,
      errors: []
    };

    // 获取表头映射
    const headers = {};
    worksheet.getRow(1).eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value;
    });

    // 处理每一行数据
    const promises = [];

    worksheet.eachRow((row, rowNumber) => {
      // 跳过表头
      if (rowNumber == 1) return;

      result.total++;

      const processRow = async () => {
        try {
          const appointmentData = {};

          row.eachCell((cell, colNumber) => {
            const fieldName = headers[colNumber];
            if (fieldName) {
              // 根据字段名称设置对应的数据
              switch (fieldName) {
                case '用户':
                  appointmentData.userName = cell.value;
                  break;
                case '协会/期刊名称':
                  appointmentData.associationName = cell.value;
                  break;
                case '职务名称':
                  appointmentData.position = cell.value;
                  break;
                case '级别':
                  appointmentData.levelName = cell.value;
                  break;
                case '起始年份':
                  appointmentData.startYear = cell.value;
                  break;
                case '结束年份':
                  appointmentData.endYear = cell.value;
                  break;
                case '备注':
                  appointmentData.remark = cell.value;
                  break;
                default:
                  break;
              }
            }
          });

          // 验证必填字段
          if (!appointmentData.userName) {
            throw new Error('用户不能为空');
          }

          if (!appointmentData.associationName) {
            throw new Error('协会/期刊名称不能为空');
          }

          if (!appointmentData.position) {
            throw new Error('职务名称不能为空');
          }

          if (!appointmentData.levelName) {
            throw new Error('级别不能为空');
          }

          if (!appointmentData.startYear) {
            throw new Error('起始年份不能为空');
          }

          // 查询用户
          const user = await userModel.findOne({
            where: {
              [Op.or]: [
                { nickname: appointmentData.userName },
                { username: appointmentData.userName }
              ]
            }
          });

          if (!user) {
            throw new Error(`未找到用户: ${appointmentData.userName}`);
          }

          // 查询级别
          const level = await associationLevelModel.findOne({
            where: { levelName: appointmentData.levelName }
          });

          if (!level) {
            throw new Error(`未找到级别: ${appointmentData.levelName}`);
          }

          // 创建学术任职
          await academicAppointmentModel.create({
            id: uuidv4(),
            userId: user.id,
            associationName: appointmentData.associationName,
            position: appointmentData.position,
            levelId: level.id,
            startYear: appointmentData.startYear,
            endYear: appointmentData.endYear !== '至今' ? appointmentData.endYear : null,
            remark: appointmentData.remark,
            ifReviewer: 0,
            status: 1
          });

          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            row: rowNumber,
            message: error.message
          });
        }
      };

      promises.push(processRow());
    });

    // 等待所有处理完成
    await Promise.all(promises);

    // 删除临时文件
    fs.unlinkSync(req.file.path);

    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: result
    });
  } catch (error) {
    console.error('导入学术任职数据失败:', error);

    // 如果文件存在，删除临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    return res.status(500).json({
      code: 500,
      message: '导入学术任职数据失败',
      error: error.message
    });
  }
};

/**
 * 审核学术任职
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewAppointment = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let dbSequelize;
  try {
    dbSequelize = getSequelizeInstance(academicAppointmentModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }

  // 创建事务，确保数据一致性
  const transaction = await dbSequelize.transaction();

  try {
    const { reviewStatus, reviewComment, reviewer, id } = req.body;

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少学术任职ID',
        data: null
      });
    }

    if (!reviewer) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少审核人ID',
        data: null
      });
    }

    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);

    // 检查任职是否存在
    const appointment = await academicAppointmentModel.findByPk(id, { 
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false,
        }
      ],
      transaction 
    });
    
    if (!appointment) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '学术任职不存在',
        data: null
      });
    }

    // 权限检查 - 只有管理员可以审核
    const hasPermission = userInfo.role.roleAuth == 'ADMIN-LV2' || userInfo.role.roleAuth == 'SUPER';

    if (!hasPermission) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核学术任职',
        data: null
      });
    }

    // 更新审核状态
    const updateData = {
      reviewerId: reviewer,
      ifReviewer: reviewStatus ? 1 : 0,
      reviewedAt: new Date()
    };

    // 添加审核意见（如果有）
    if (reviewComment !== undefined) {
      updateData.reviewComment = reviewComment;
    }

    await appointment.update(updateData, { transaction });

    // 任职审核通过后，更新用户排名数据
    if (reviewStatus) {
      try {
        // 获取学术任职级别对应的分数
        if (!appointment.level) {
          console.error(`未找到学术任职级别信息，levelId: ${appointment.levelId}`);
          throw new Error('未找到学术任职级别信息');
        }
        
        const baseScore = appointment.level.score || 0;
        
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("academicAppointments");
        
        // 判断任职是否在时间区间内
        const isInTimeRange = timeInterval ? 
          isYearRangeOverlap(
            appointment.startYear,
            appointment.endYear,
            timeInterval.startTime,
            timeInterval.endTime
          ) : false;
        
        console.log(`学术任职ID ${id} 是否在统计时间区间内: ${isInTimeRange}`);
        console.log(`基础分数: ${baseScore}`);
        
        // 根据任职是否在时间区间内，更新不同的排名表
        let rankingTables = [];
        
        if (isInTimeRange) {
          // 在区间内：更新范围内表和全部表
          rankingTables = [
            'user_ranking_reviewed_in', 
            'user_ranking_reviewed_all'
          ];
          console.log(`更新范围内排名表和全部排名表`);
        } else {
          // 不在区间内：更新范围外表和全部表
          rankingTables = [
            'user_ranking_reviewed_out', 
            'user_ranking_reviewed_all'
          ];
          console.log(`更新范围外排名表和全部排名表`);
        }
        
        try {
          for (const table of rankingTables) {
            // 更新用户的排名数据：计数+1，分数增加基础分
            await updateUserRankings(
              [appointment.userId],     // 用户ID数组
              table,                    // 表名
              'academicAppointments',   // 类型名
              [1],                      // 计数+1
              [baseScore],              // 得分数组
              transaction,              // 传递事务对象
              "add"                     // 操作类型：加分
            );
          }
        } catch (rankingError) {
          console.error('更新排名表失败:', rankingError);
          // 对于所有错误，都应当回滚事务以保证数据一致性
          await transaction.rollback();
          throw new Error(`更新排名失败: ${rankingError.message}`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 检查是否已经回滚，如果没有则回滚事务
        if (!error.message || !error.message.includes('更新排名失败')) {
          await transaction.rollback();
          throw error; // 将错误向上传播
        }
      }
    }

    // 提交事务
    await transaction.commit();

    // 查询更新后的学术任职详情
    const updatedAppointment = await academicAppointmentModel.findByPk(id, {
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score']
        },
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber']
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickname', 'username', 'studentNumber']
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: updatedAppointment
    });
  } catch (error) {
    // 回滚事务
    try {
      await transaction.rollback();
    } catch (rollbackError) {
      console.error('回滚事务失败:', rollbackError);
    }

    console.error('学术任职审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '学术任职审核失败: ' + error.message,
      error: error.message
    });
  }
};

/**
 * 获取统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getStatistics = async (req, res) => {
  try {
    const { userId, range = 'in', reviewStatus = 'all' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("academicAppointments");

    // 构建查询条件
    const whereCondition = {};

    // 根据审核状态过滤
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }

    // 如果有用户ID，限定为该用户的数据
    if (userId) {
      whereCondition.userId = userId;
    }

    // 根据range参数筛选数据 - 使用优化后的年份区间交集判断逻辑
    if (range == 'in' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围内：有效任职（与时间区间有交集）
      whereCondition[Op.and] = [
        {
          [Op.or]: [
            // 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
            {
              [Op.and]: [
                { startYear: { [Op.lte]: intervalEndYear } },
                {
                  [Op.or]: [
                    { endYear: null },
                    { endYear: { [Op.gte]: intervalStartYear } }
                  ]
                }
              ]
            }
          ]
        }
      ];
    } else if (range == 'out' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围外：无效任职（与时间区间无交集）
      whereCondition[Op.or] = [
        // 开始年份 > 区间结束年份
        { startYear: { [Op.gt]: intervalEndYear } },
        // 结束年份不为空 AND 结束年份 < 区间开始年份
        {
          [Op.and]: [
            { endYear: { [Op.ne]: null } },
            { endYear: { [Op.lt]: intervalStartYear } }
          ]
        }
      ];
    }
    // range == 'all' 不添加额外筛选条件

    // 查询总任职数量
    const totalAppointments = await academicAppointmentModel.count({
      where: whereCondition
    });

    // 查询当前有效任职数量（与时间区间有交集）
    const activeWhereCondition = {
      ...whereCondition
    };

    // 如果range是'all'，还需要单独计算活跃任职
    if (range == 'all' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 添加时间区间筛选条件
      activeWhereCondition[Op.and] = [
        {
          [Op.or]: [
            // 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
            {
              [Op.and]: [
                { startYear: { [Op.lte]: intervalEndYear } },
                {
                  [Op.or]: [
                    { endYear: null },
                    { endYear: { [Op.gte]: intervalStartYear } }
                  ]
                }
              ]
            }
          ]
        }
      ];
    }

    const activeAppointments = range == 'out' ? 0 : await academicAppointmentModel.count({
      where: activeWhereCondition
    });

    // 获取所有任职及其级别信息
    const appointments = await academicAppointmentModel.findAll({
      where: whereCondition,
      include: [{
        model: associationLevelModel,
        as: 'level',
        attributes: ['score']
      }]
    });

    // 计算平均分
    let totalScore = 0;
    let appointmentsWithScore = 0;

    appointments.forEach(appointment => {
      if (appointment.level && appointment.level.score) {
        totalScore += parseFloat(appointment.level.score);
        appointmentsWithScore++;
      }
    });

    const averageScore = appointmentsWithScore > 0 ? totalScore / appointmentsWithScore : 0;

    // 查询各审核状态的任职数量
    const approvedAppointments = await academicAppointmentModel.count({
      where: {
        ...whereCondition,
        ifReviewer: 1
      }
    });

    const pendingAppointments = await academicAppointmentModel.count({
      where: {
        ...whereCondition,
        ifReviewer: null
      }
    });

    const rejectedAppointments = await academicAppointmentModel.count({
      where: {
        ...whereCondition,
        ifReviewer: 0
      }
    });

    // 计算审核完成率
    const reviewCompletionRate = totalAppointments > 0 ? ((approvedAppointments + rejectedAppointments) / totalAppointments) * 100 : 0;

    return res.status(200).json({
      code: 200,
      message: '获取统计数据成功',
      data: {
        totalAppointments,
        activeAppointments,
        averageScore,
        reviewCompletionRate,
        approvedAppointments,
        pendingAppointments,
        rejectedAppointments,
        totalScore
      }
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取统计数据失败',
      error: error.message
    });
  }
};

/**
 * 获取任职级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAppointmentLevelDistribution = async (req, res) => {
  try {
    const { userId, range = 'in', reviewStatus = 'all' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("academicAppointments");

    // 构建查询条件
    const whereCondition = {};

    // 根据审核状态过滤
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }

    // 如果有用户ID，限定为该用户的数据
    if (userId) {
      whereCondition.userId = userId;
    }

    // 根据range参数筛选数据 - 使用优化后的年份区间交集判断逻辑
    if (range == 'in' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围内：有效任职（与时间区间有交集）
      whereCondition[Op.and] = [
        {
          [Op.or]: [
            // 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
            {
              [Op.and]: [
                { startYear: { [Op.lte]: intervalEndYear } },
                {
                  [Op.or]: [
                    { endYear: null },
                    { endYear: { [Op.gte]: intervalStartYear } }
                  ]
                }
              ]
            }
          ]
        }
      ];
    } else if (range == 'out' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围外：无效任职（与时间区间无交集）
      whereCondition[Op.or] = [
        // 开始年份 > 区间结束年份
        { startYear: { [Op.gt]: intervalEndYear } },
        // 结束年份不为空 AND 结束年份 < 区间开始年份
        {
          [Op.and]: [
            { endYear: { [Op.ne]: null } },
            { endYear: { [Op.lt]: intervalStartYear } }
          ]
        }
      ];
    }
    // range == 'all' 不添加额外筛选条件

    // 查询所有任职记录及其级别信息
    const appointments = await academicAppointmentModel.findAll({
      where: whereCondition,
      include: [{
        model: associationLevelModel,
        as: 'level',
        attributes: ['levelName', 'score']
      }]
    });

    // 初始化级别数据
    const levelData = {};

    // 统计各级别任职数量
    appointments.forEach(appointment => {
      if (appointment.level && appointment.level.levelName) {
        const levelName = appointment.level.levelName;
        levelData[levelName] = (levelData[levelName] || 0) + 1;
      }
    });

    // 转换为符合前端期望的格式 [{name, value}]
    const result = Object.entries(levelData)
      .map(([name, value]) => ({ name, value }))
      .filter(item => item.value > 0); // 过滤掉数量为0的级别

    return res.status(200).json({
      code: 200,
      message: '获取任职级别分布数据成功',
      data: result
    });
  } catch (error) {
    console.error('获取任职级别分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取任职级别分布数据失败',
      error: error.message
    });
  }
};

/**
 * 获取年度新增任职趋势数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAppointmentYearlyTrend = async (req, res) => {
  try {
    const { userId, range = 'in', reviewStatus = 'all' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("academicAppointments");

    // 构建查询条件
    const whereCondition = {};

    // 根据审核状态过滤
    if (reviewStatus == 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus == 'pending') {
      whereCondition.ifReviewer = null; // 待审核
    } else if (reviewStatus == 'rejected') {
      whereCondition.ifReviewer = 0; // 已拒绝
    }

    // 如果有用户ID，限定为该用户的数据
    if (userId) {
      whereCondition.userId = userId;
    }

    // 根据range参数筛选数据 - 使用优化后的年份区间交集判断逻辑
    if (range == 'in' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围内：有效任职（与时间区间有交集）
      whereCondition[Op.and] = [
        {
          [Op.or]: [
            // 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
            {
              [Op.and]: [
                { startYear: { [Op.lte]: intervalEndYear } },
                {
                  [Op.or]: [
                    { endYear: null },
                    { endYear: { [Op.gte]: intervalStartYear } }
                  ]
                }
              ]
            }
          ]
        }
      ];
    } else if (range == 'out' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围外：无效任职（与时间区间无交集）
      whereCondition[Op.or] = [
        // 开始年份 > 区间结束年份
        { startYear: { [Op.gt]: intervalEndYear } },
        // 结束年份不为空 AND 结束年份 < 区间开始年份
        {
          [Op.and]: [
            { endYear: { [Op.ne]: null } },
            { endYear: { [Op.lt]: intervalStartYear } }
          ]
        }
      ];
    }
    // range == 'all' 不添加额外筛选条件

    // 获取所有任职数据
    const appointments = await academicAppointmentModel.findAll({
      where: whereCondition,
      attributes: ['startYear'],
      order: [['startYear', 'ASC']]
    });

    // 如果没有数据，返回空结果
    if (appointments.length == 0) {
      return res.status(200).json({
        code: 200,
        message: '获取年度新增任职趋势数据成功',
        data: {
          years: [],
          newAppointments: [],
          cumulativeAppointments: []
        }
      });
    }

    // 获取起始年份和结束年份
    const startYear = Math.min(...appointments.map(a => a.startYear));
    const endYear = Math.max(...appointments.map(a => a.startYear));

    // 生成年份序列
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
      years.push(year);
    }

    // 计算每年新增数量
    const yearCounts = {};
    appointments.forEach(appointment => {
      const year = appointment.startYear;
      yearCounts[year] = (yearCounts[year] || 0) + 1;
    });

    // 生成新增任职数据和累计数据
    const newAppointments = [];
    const cumulativeAppointments = [];
    let cumulative = 0;

    years.forEach(year => {
      const count = yearCounts[year] || 0;
      newAppointments.push(count);
      cumulative += count;
      cumulativeAppointments.push(cumulative);
    });

    return res.status(200).json({
      code: 200,
      message: '获取年度新增任职趋势数据成功',
      data: {
        years,
        newAppointments,
        cumulativeAppointments
      }
    });
  } catch (error) {
    console.error('获取年度新增任职趋势数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取年度新增任职趋势数据失败',
      error: error.message
    });
  }
};

/**
 * 获取审核状态概览数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReviewStatusOverview = async (req, res) => {
  try {
    const { userId, range = 'in' } = req.body;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("academicAppointments");

    // 构建查询条件
    const whereCondition = {};

    // 如果有用户ID，限定为该用户的数据
    if (userId) {
      whereCondition.userId = userId;
    }

    // 根据range参数筛选数据
    if (range == 'in') {
      // 统计范围内：有效任职（与时间区间有交集）
      whereCondition[Op.or] = [
        // 开始时间在区间内
        { startYear: { [Op.between]: [timeInterval.startTime, timeInterval.endTime] } },
        // 结束时间在区间内
        {
          [Op.and]: [
            { endYear: { [Op.not]: null } },
            { endYear: { [Op.between]: [timeInterval.startTime, timeInterval.endTime] } }
          ]
        },
        // 区间完全包含任职时间
        {
          [Op.and]: [
            { startYear: { [Op.lte]: timeInterval.startTime } },
            {
              [Op.or]: [
                { endYear: { [Op.gte]: timeInterval.endTime } },
                { endYear: { [Op.eq]: null } }
              ]
            }
          ]
        }
      ];
    } else if (range == 'out') {
      // 统计范围外：无效任职（与时间区间无交集）
      whereCondition[Op.and] = [
        {
          [Op.or]: [
            { startYear: { [Op.gt]: timeInterval.endTime } },
            {
              [Op.and]: [
                { endYear: { [Op.not]: null } },
                { endYear: { [Op.lt]: timeInterval.startTime } }
              ]
            }
          ]
        }
      ];
    }
    // range == 'all' 不添加额外筛选条件

    // 查询各审核状态的任职数量
    const reviewed = await academicAppointmentModel.count({
      where: {
        ...whereCondition,
        ifReviewer: 1
      }
    });

    const pending = await academicAppointmentModel.count({
      where: {
        ...whereCondition,
        ifReviewer: null
      }
    });

    const rejected = await academicAppointmentModel.count({
      where: {
        ...whereCondition,
        ifReviewer: 0
      }
    });

    // 计算总数和审核率
    const total = reviewed + pending + rejected;
    const reviewedRate = total > 0 ? ((reviewed + rejected) / total) * 100 : 0;
    const approvedRate = total > 0 ? (reviewed / total) * 100 : 0;
    const rejectedRate = total > 0 ? (rejected / total) * 100 : 0;
    const pendingRate = total > 0 ? (pending / total) * 100 : 0;

    return res.status(200).json({
      code: 200,
      message: '获取审核状态概览数据成功',
      data: {
        reviewed,
        pending,
        rejected,
        total,
        reviewedRate,
        approvedRate,
        rejectedRate,
        pendingRate
      }
    });
  } catch (error) {
    console.error('获取审核状态概览数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取审核状态概览数据失败',
      error: error.message
    });
  }
};

/**
 * 获取用户有效任职数量排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserActiveAppointmentsRanking = async (req, res) => {
  try {
    const {
      limit = 10,
      page = 1,
      pageSize = 10,
      range = 'in',
      reviewStatus = 'all',
      nickname = '',
      isExport = false
    } = req.body;

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize || limit);
    const offset = (pageNum - 1) * pageSizeNum;
    
    // 根据range选择对应的排名表模型
    let RankingModel;
    switch(range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }
    
    // 查询条件：按学术任职总分降序排序
    const queryOptions = {
      order: [['appointmentScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'appointmentCount',
        'appointmentScore',
        'totalScore'
      ]
    };
    
    // 如果提供了昵称，添加筛选条件
    if (nickname) {
      queryOptions.where = {
        nickName: {
          [Op.like]: `%${nickname}%`
        }
      };
    }

    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }
    
    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);
    
    // 格式化返回数据
    const rankingData = rows.map((item, index) => ({
      rank: item.rank || index + 1 + offset,
      userId: item.userId,
      userName: item.nickName,
      studentNumber: item.studentNumber,
      activeAppointments: item.appointmentCount || 0,
      totalAppointments: item.appointmentCount || 0,
      totalScore: parseFloat(item.appointmentScore || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取用户有效任职数量排名成功',
      data: {
        list: rankingData,
        pagination: {
          total: count,
          page: pageNum,
          pageSize: pageSizeNum,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户有效任职数量排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户有效任职数量排名失败',
      error: error.message
    });
  }
};

/**
 * 获取用户任职详情数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserAppointmentDetails = async (req, res) => {
  try {
    const { userId, page = 1, pageSize = 10, range = 'in', reviewStatus = 'all' } = req.body;

    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }

    // 查询用户
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("academicAppointments");

    // 构建查询条件
    const whereCondition = { userId };

    // 根据range参数筛选数据 - 使用优化后的年份区间交集判断逻辑
    if (range == 'in' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围内：有效任职（与时间区间有交集）
      whereCondition[Op.and] = [
        {
          [Op.or]: [
            // 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
            {
              [Op.and]: [
                { startYear: { [Op.lte]: intervalEndYear } },
                {
                  [Op.or]: [
                    { endYear: null },
                    { endYear: { [Op.gte]: intervalStartYear } }
                  ]
                }
              ]
            }
          ]
        }
      ];
    } else if (range == 'out' && timeInterval) {
      // 获取时间区间的年份
      const intervalStartYear = new Date(timeInterval.startTime).getFullYear();
      const intervalEndYear = new Date(timeInterval.endTime).getFullYear();
      
      // 统计范围外：无效任职（与时间区间无交集）
      whereCondition[Op.or] = [
        // 开始年份 > 区间结束年份
        { startYear: { [Op.gt]: intervalEndYear } },
        // 结束年份不为空 AND 结束年份 < 区间开始年份
        {
          [Op.and]: [
            { endYear: { [Op.ne]: null } },
            { endYear: { [Op.lt]: intervalStartYear } }
          ]
        }
      ];
    }
    // range == 'all' 不添加额外筛选条件

    // 查询用户的所有任职
    const { count, rows } = await academicAppointmentModel.findAndCountAll({
      where: whereCondition,
      offset,
      limit,
      order: [['startYear', 'DESC']],
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['id', 'levelName', 'score'],
          required: false
        }
      ]
    });

    // 计算总得分
    let totalScore = 0;

    // 查询用户的所有任职（不分页）以计算总分
    // 使用相同的范围条件
    const allAppointments = await academicAppointmentModel.findAll({
      where: whereCondition,
      include: [
        {
          model: associationLevelModel,
          as: 'level',
          attributes: ['score'],
          required: false
        }
      ]
    });

    allAppointments.forEach(appointment => {
      if (appointment.level && appointment.level.score) {
        totalScore += parseFloat(appointment.level.score);
      }
    });

    // 判断任职是否有效
    const appointmentDetails = rows.map(appointment => {
      // 判断是否与时间区间有交集
      let isActive = false;
      if (timeInterval) {
        isActive = isYearRangeOverlap(
          appointment.startYear,
          appointment.endYear,
          timeInterval.startTime,
          timeInterval.endTime
        );
      }

      return {
        ...appointment.toJSON(),
        isActive
      };
    });

    return res.status(200).json({
      code: 200,
      message: '获取用户任职详情成功',
      data: {
        list: appointmentDetails,
        totalScore,
        user: {
          id: user.id,
          name: user.nickname || user.username,
          studentNumber: user.studentNumber
        },
        pagination: {
          total: count,
          page: pageNum,
          pageSize: pageSizeNum,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户任职详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户任职详情失败',
      error: error.message
    });
  }
};

/**
 * 获取学术任职总分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAppointmentsTotalScore = async (req, res) => {
  try {
    const { range = 'all', reviewStatus = 'all' } = req.body;

    // 查找数据库连接，通过已有模型获取sequelize实例
    let sequelize;
    if (academicAppointmentModel.sequelize) {
      sequelize = academicAppointmentModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }

    // 注意：这个方法使用了存储过程来获取数据
    // 存储过程内部应该使用与我们一致的年份区间交集判断逻辑：
    // 判断两个年份区间是否有交集的条件：
    // 1. 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
    
    let userId = null; 
    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    // 调用存储过程 - 使用RAW类型
    const results = await sequelize.query(
      'CALL get_academic_appointments_total_score(?, ?, ?)',
      {
        replacements: [range || 'all', reviewStatus || 'all', userId],
        type: sequelize.QueryTypes.RAW,
        raw: true,
        nest: true
      }
    );

    console.log('存储过程原始返回结果:', JSON.stringify(results));

    // 处理存储过程的结果
    let levelStats = [];
    let overallStats = { totalAppointments: 0, totalScore: 0 };
    let timeInterval = null;

    // 根据实际返回结果格式处理数据
    if (Array.isArray(results) && results.length > 0) {
      // 检查返回的是单层数组(直接就是结果数组)还是嵌套数组(多个结果集)
      const isNestedArray = Array.isArray(results[0]) && !results[0].hasOwnProperty('levelId') && !results[0].hasOwnProperty('levelName');
      
      if (isNestedArray) {
        // 处理嵌套数组格式 (传统格式)
        // 第一个结果集：按级别统计
        if (results[0] && Array.isArray(results[0])) {
          levelStats = results[0].map(item => ({
            levelId: item.levelId,
            levelName: item.levelName,
            count: parseInt(item.count || 0),
            totalScore: parseFloat(item.totalScore || 0)
          }));
        }

        // 第二个结果集：总体统计
        if (results.length > 1 && Array.isArray(results[1]) && results[1].length > 0) {
          overallStats = {
            totalAppointments: parseInt(results[1][0].totalAppointments || 0),
            totalScore: parseFloat(results[1][0].totalScore || 0)
          };
        }

        // 第三个结果集：时间区间
        if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
          timeInterval = {
            startTime: results[2][0].startTime,
            endTime: results[2][0].endTime,
            name: results[2][0].name
          };
        }
      } else {
        // 处理单层数组格式 (当前情况)
        // 将results直接作为levelStats使用
        levelStats = results.map(item => ({
          levelId: item.levelId,
          levelName: item.levelName,
          count: parseInt(item.count || 0),
          totalScore: parseFloat(item.totalScore || 0)
        }));
        
        // 根据levelStats计算总体统计
        if (levelStats.length > 0) {
          overallStats = {
            totalAppointments: levelStats.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0),
            totalScore: parseFloat(
              levelStats.reduce((sum, item) => sum + (parseFloat(item.totalScore) || 0), 0).toFixed(2)
            )
          };
        }
      }
    }

    // 如果没有获取到时间区间信息，则尝试使用工具函数获取
    if (!timeInterval) {
      try {
        timeInterval = await getTimeIntervalByName("academicAppointments");
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        levelStats,
        overallStats,
        timeInterval
      }
    });
  } catch (error) {
    console.error('获取学术任职总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取学术任职总分统计失败',
      error: error.message
    });
  }
};

/**
 * 获取用户学术任职详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserAppointmentsDetail = async (req, res) => {
  try {
    const {
      userId,
      range = 'all',
      reviewStatus = 'all',
      page = 1,
      pageSize = 10
    } = req.body;

    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID',
        data: null
      });
    }

    // 查询用户是否存在
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 获取数据库连接
    const dbSequelize = getSequelizeInstance();

    // 注意：这个方法使用了存储过程来获取数据
    // 存储过程内部应该使用与我们一致的年份区间交集判断逻辑：
    // 判断两个年份区间是否有交集的条件：
    // 1. 开始年份 <= 区间结束年份 AND (结束年份为空 OR 结束年份 >= 区间开始年份)
    // 2. 范围外：开始年份 > 区间结束年份 OR (结束年份不为空 AND 结束年份 < 区间开始年份)
    
    const [totalCount, details, statistics, timeInterval] = await dbSequelize.query(
      'CALL get_user_academic_appointments_detail(?, ?, ?, ?, ?)',
      {
        replacements: [userId, range, reviewStatus, pageSize, page],
        type: Sequelize.QueryTypes.RAW,
      }
    );

    // 处理结果集
    const totalCountValue = convertStoredProcResultToArray(totalCount)[0]?.totalCount || 0;
    const detailsList = convertStoredProcResultToArray(details);
    const userStatistics = convertStoredProcResultToArray(statistics)[0] || {
      totalAppointments: 0,
      totalScore: 0,
      currentAppointments: 0,
      approvedAppointments: 0,
      activeAppointments: 0
    };
    const timeIntervalInfo = convertStoredProcResultToArray(timeInterval)[0] ||
      await getTimeIntervalByName('academicAppointments');

    // 确保详情列表中的数值类型正确
    const processedDetails = detailsList.map(item => ({
      ...item,
      score: parseFloat(item.score) || 0,
      ifReviewer: item.ifReviewer == null ? null : parseInt(item.ifReviewer, 10)
    }));

    // 确保统计数据中的数值类型正确
    userStatistics.totalAppointments = parseInt(userStatistics.totalAppointments, 10) || 0;
    userStatistics.totalScore = parseFloat(userStatistics.totalScore) || 0;
    userStatistics.currentAppointments = parseInt(userStatistics.currentAppointments, 10) || 0;
    userStatistics.approvedAppointments = parseInt(userStatistics.approvedAppointments, 10) || 0;
    userStatistics.activeAppointments = parseInt(userStatistics.activeAppointments, 10) || 0;

    // 返回结果
    return res.status(200).json({
      code: 200,
      message: '获取用户学术任职详情成功',
      data: {
        list: processedDetails,
        totalCount: parseInt(totalCountValue, 10),
        statistics: userStatistics,
        timeInterval: timeIntervalInfo,
        pagination: {
          page: parseInt(page, 10),
          pageSize: parseInt(pageSize, 10),
          total: parseInt(totalCountValue, 10)
        }
      }
    });
  } catch (error) {
    console.error('获取用户学术任职详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户学术任职详情失败: ' + error.message,
      error: error.message
    });
  }
};

/**
 * 重新提交学术任命审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapply = async (req, res) => {
  // 使用工具函数获取数据库连接实例
  let sequelize;
  try {
    sequelize = getSequelizeInstance(academicAppointmentsModel);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: '无法获取数据库连接实例',
      error: error.message
    });
  }
  
  // 创建事务，确保数据一致性
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.body;
    
    // 验证必要参数
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少记录ID',
        data: null
      });
    }
    
    // 获取当前用户信息，用于权限检查
    const userInfo = await getUserInfoFromRequest(req);
    
    // 查找记录
    const record = await academicAppointmentsModel.findByPk(id, { transaction });
    
    if (!record) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '记录不存在',
        data: null
      });
    }
    
    // 检查记录所有权或管理员权限
    const isOwner = record.userId === userInfo.id;
    const isAdmin = userInfo.role.roleAuth === 'TEACHER-LV1' || userInfo.role.roleAuth === 'SUPER' || userInfo.role.roleAuth === 'ADMIN-LV2';
    
    if (!isOwner && !isAdmin) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限重新提交该记录',
        data: null
      });
    }
    
    // 检查当前审核状态，只有被拒绝的记录可以重新提交
    if (record.ifReviewer != 0) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '只有被拒绝的记录可以重新提交审核',
        data: null
      });
    }
    
    // 更新记录状态为待审核
    await record.update({
      ifReviewer: null,  // 设置为待审核状态
      reviewComment: null, // 清空之前的审核意见
      reviewerId: null // 清空之前的审核人
    }, { transaction });
    
    // 提交事务
    await transaction.commit();
    
    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: null
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    console.error('重新提交审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: '重新提交审核失败',
      error: error.message
    });
  }
};