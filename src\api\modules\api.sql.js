import service from '../server'

/**
 * 更新排名缓存表
 * @description 创建三个排名缓存表并重新计算所有用户的排名数据
 * @returns {Promise} 包含操作结果的Promise对象
 */
export const updateRankingTables = () => {
  return service.post('/sql/update-ranking-tables')
}

/**
 * 更新用户排名数据
 * @description 在审核、删除、修改完毕后更新用户在排名表中的数据
 * @param {Object} data 更新参数对象
 * @param {string} data.tableName 要更新的表名
 * @param {Array<string>} data.userIds 需要更新的用户ID数组
 * @param {string} data.typeName 数据类型名称
 * @param {number} data.totalDelta 计数变化量
 * @param {number} data.scoreDelta 分数变化量
 * @returns {Promise} 包含操作结果的Promise对象
 */
export const updateUserRankings = (data) => {
  return service.post('/sql/update-user-rankings', data)
}

/**
 * 获取用户排名数据
 * @description 获取用户排名数据，支持分页和筛选
 * @param {Object} params 查询参数对象
 * @param {string} params.range 统计范围筛选，可选值：'in'(范围内), 'out'(范围外), 'all'(全部)
 * @param {number} params.page 页码
 * @param {number} params.limit 每页条数
 * @param {string} [params.userId] 用户ID（可选）
 * @returns {Promise} 包含排名数据的Promise对象
 */
export const getUserRankings = (params) => {
  return service.get('/sql/get-user-rankings', { params })
}

/**
 * 搜索用户
 * @description 按关键词搜索用户
 * @param {string} query 搜索关键词
 * @returns {Promise} 包含搜索结果的Promise对象
 */
export const searchUsers = (query) => {
  return service.get('/user/search', { params: { query } })
}
