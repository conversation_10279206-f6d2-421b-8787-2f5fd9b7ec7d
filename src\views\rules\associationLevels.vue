<template>
  <div class="association-levels">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
    
    <a-card title="学术任职级别管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            添加级别
          </a-button>
        </a-space>
      </template>

      <a-alert
        message="级别说明"
        description="学术任职级别用于对学术任职进行归类和管理，不同级别的学术任职对应不同的重要程度和分数。"
        type="info"
        show-icon
        style="margin-bottom: 16px;"
      />

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="isLoading"
        rowKey="id"
        @change="handleTableChange"
        :scroll="{ x: 800 }"
        :bordered="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'score'">
            <span style="font-weight: bold; color: #1890ff;">{{ record.score }}分</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这个级别吗？如果有学术任职使用此级别将无法删除"
                @confirm="handleDelete(record)"
              >
                <a class="text-danger">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 使用ZyModal替代a-modal -->
      <ZyModal
        :show="modalVisible"
        :title="isEdit ? '编辑学术任职级别' : '添加学术任职级别'"
        :min-width="500"
        :min-height="300"
        @close="handleModalCancel"
      >
        <div class="level-form">
          <a-form
            :model="formState"
            :rules="rules"
            ref="formRef"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="级别名称" name="levelName">
              <a-input v-model:value="formState.levelName" placeholder="请输入级别名称" />
            </a-form-item>
            
            <a-form-item label="分数" name="score">
              <a-input-number
                v-model:value="formState.score"
                :min="0"
                style="width: 100%"
              />
            </a-form-item>
            
            <a-form-item :wrapper-col="{ span: 16, offset: 6 }">
              <a-space>
                <a-button type="primary" @click="handleSubmit" :loading="confirmLoading">
                  提交
                </a-button>
                <a-button @click="handleModalCancel">
                  取消
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </ZyModal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import ZyModal from '@/components/common/ZyModal.vue'
import { 
  getAssociationLevels,
  getLevelsWithCount,
  getAssociationLevelDetail,
  createAssociationLevel,
  updateAssociationLevel,
  deleteAssociationLevel
} from '@/api/modules/api.associationLevels'

// 表格列定义
const columns = [
  {
    title: '级别名称',
    dataIndex: 'levelName',
    key: 'levelName',
    width: '200px',
  },
  {
    title: '分数',
    dataIndex: 'score',
    key: 'score',
    width: '100px',
  },
  {
    title: '操作',
    key: 'action',
    width: '150px',
    fixed: 'right',
  },
]

// 数据源
const dataSource = ref([])
const isLoading = ref(false)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)

// 表单引用
const formRef = ref(null)

// 表单数据
const formState = reactive({
  levelName: '',
  score: 0,
})

// 表单验证规则
const rules = {
  levelName: [
    { required: true, message: '请输入级别名称', trigger: 'blur' },
    { min: 2, max: 50, message: '级别名称长度在2-50个字符之间', trigger: 'blur' }
  ],
  score: [
    { required: true, message: '请输入级别分数', trigger: 'change' },
    { type: 'number', message: '级别分数必须为数字', trigger: 'change' }
  ]
}

// 添加错误状态
const errorMessage = ref('')

// 获取列表数据
const fetchData = async () => {
  isLoading.value = true
  errorMessage.value = ''
  
  try {
    // 获取带有学术任职数量的级别列表
    const response = await getLevelsWithCount()
    
    if (response && response.code === 200) {
      dataSource.value = response.data || []
      pagination.total = dataSource.value.length
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = '获取级别列表失败：' + (response?.message || '未知错误')
    }
  } catch (error) {
    console.error('获取学术任职级别列表失败:', error)
    message.error('获取学术任职级别列表失败: ' + (error.message || '未知错误'))
    errorMessage.value = '获取级别列表失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 处理表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// 添加新级别
const showAddModal = () => {
  isEdit.value = false
  currentRecord.value = null
  resetForm()
  modalVisible.value = true
}

// 编辑级别
const handleEdit = async (record) => {
  isEdit.value = true
  currentRecord.value = record
  resetForm()
  
  try {
    const response = await getAssociationLevelDetail(record.id)
    
    if (response && response.code === 200) {
      const detail = response.data
      
      formState.levelName = detail.levelName
      formState.score = detail.score
      
      modalVisible.value = true
    } else {
      message.error(response?.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取级别详情失败:', error)
    message.error('获取级别详情失败: ' + (error.message || '未知错误'))
  }
}

// 处理删除
const handleDelete = async (record) => {
  if (record.appointmentCount && record.appointmentCount > 0) {
    message.error(`该级别下有${record.appointmentCount}个学术任职，无法删除`)
    return
  }
  
  try {
    const response = await deleteAssociationLevel(record.id)
    
    if (response && response.code === 200) {
      message.success('删除成功')
      fetchData()
    } else {
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('删除学术任职级别失败:', error)
    message.error('删除学术任职级别失败: ' + (error.message || '未知错误'))
  }
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate().then(async () => {
    try {
      confirmLoading.value = true
      
      const formData = {
        levelName: formState.levelName,
        score: formState.score
      }
      
      let response
      if (isEdit.value) {
        response = await updateAssociationLevel(currentRecord.value.id, formData)
      } else {
        response = await createAssociationLevel(formData)
      }
      
      if (response && response.code === 200) {
        message.success(isEdit.value ? '更新成功' : '添加成功')
        modalVisible.value = false
        fetchData()
      } else {
        message.error(response?.message || (isEdit.value ? '更新失败' : '添加失败'))
      }
    } catch (error) {
      console.error(isEdit.value ? '更新学术任职级别失败:' : '添加学术任职级别失败:', error)
      message.error(isEdit.value ? '更新学术任职级别失败: ' + (error.message || '未知错误') : '添加学术任职级别失败: ' + (error.message || '未知错误'))
    } finally {
      confirmLoading.value = false
    }
  }).catch(errors => {
    console.log('表单验证失败', errors)
    const errorFields = errors.errorFields || []
    errorFields.forEach(field => {
      message.error(`${field.name.join('.')}：${field.errors.join(', ')}`)
    })
  })
}

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
}

// 重置表单
const resetForm = () => {
  formState.levelName = ''
  formState.score = 0
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 加载数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.association-levels {
  margin: 24px;
  border: 1px solid #f0f0f0;
}

.text-danger {
  color: #ff4d4f;
}

.level-form {
  padding: 20px;
}
</style> 