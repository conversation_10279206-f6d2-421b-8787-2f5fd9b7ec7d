const { Op } = require('sequelize');
const researchProjectParticipantsModel = require('../../../models/v1/mapping/researchProjectParticipantsModel');
const researchProjectModel = require('../../../models/v1/mapping/researchProjectModel');
const userModel = require('../../../models/v1/mapping/userModel');
const { getUserInfoFromRequest } = require('../../../utils/others');

/**
 * 获取科研项目参与者列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectParticipants = async (req, res) => {
  try {
    console.log('🔍 获取科研项目参与者列表 - 请求参数:', req.body);
    
    const { 
      projectId, 
      userId, 
      isLeader,
      page = 1, 
      pageSize = 10
    } = req.body;
    
    // 构建查询条件
    const where = {};
    
    if (projectId) {
      where.projectId = projectId;
    }
    
    if (userId) {
      where.userId = userId;
    }
    
    if (isLeader !== undefined) {
      where.isLeader = isLeader;
    }
    
    // 确保 page 和 pageSize 是有效的数字
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    console.log('查询条件:', where);
    
    // 查询数据
    const { count, rows } = await researchProjectParticipantsModel.findAndCountAll({
      where,
      offset,
      limit,
      order: [
        ['participantRank', 'ASC'], // 按排名升序
        ['createdAt', 'DESC']
      ],
      include: [
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: researchProjectModel,
          as: 'project',
          attributes: ['id', 'projectId', 'name'],
          required: false
        }
      ]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取科研项目参与者列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取科研项目参与者列表失败',
      error: error.message
    });
  }
};

/**
 * 获取项目参与者详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectParticipantDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少参与者ID',
        data: null
      });
    }
    
    // 查询参与者详情
    const participant = await researchProjectParticipantsModel.findByPk(id, {
      include: [
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: researchProjectModel,
          as: 'project',
          attributes: ['id', 'projectId', 'name', 'levelId', 'type', 'startDate', 'endDate'],
          required: false
        }
      ]
    });
    
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '未找到参与者',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: participant
    });
  } catch (error) {
    console.error('获取项目参与者详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目参与者详情失败',
      error: error.message
    });
  }
};

/**
 * 创建项目参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createProjectParticipant = async (req, res) => {
  try {
    const { 
      projectId, 
      userId, 
      allocationRatio, 
      participantRank,
      isLeader = false 
    } = req.body;
    
    // 验证必要字段
    if (!projectId || !userId || allocationRatio === undefined) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要字段',
        data: null
      });
    }
    
    // 验证分配比例是否在有效范围内 (0-1)
    if (allocationRatio < 0 || allocationRatio > 1) {
      return res.status(400).json({
        code: 400,
        message: '分配比例必须在0到1之间',
        data: null
      });
    }
    
    // 检查项目是否存在
    const project = await researchProjectModel.findByPk(projectId);
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }
    
    // 检查用户是否存在
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }
    
    // 检查是否已存在相同的参与记录
    const existingParticipant = await researchProjectParticipantsModel.findOne({
      where: {
        projectId,
        userId
      }
    });
    
    if (existingParticipant) {
      return res.status(400).json({
        code: 400,
        message: '该用户已是项目参与者',
        data: null
      });
    }
    
    // 如果是负责人，检查项目是否已有负责人
    if (isLeader) {
      const existingLeader = await researchProjectParticipantsModel.findOne({
        where: {
          projectId,
          isLeader: true
        }
      });
      
      if (existingLeader) {
        return res.status(400).json({
          code: 400,
          message: '该项目已有负责人',
          data: null
        });
      }
    }
    
    // 创建参与者记录
    const participant = await researchProjectParticipantsModel.create({
      projectId,
      userId,
      allocationRatio,
      participantRank: participantRank || null,
      isLeader
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: participant
    });
  } catch (error) {
    console.error('创建项目参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建项目参与者失败',
      error: error.message
    });
  }
};

/**
 * 更新项目参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateProjectParticipant = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      allocationRatio, 
      participantRank,
      isLeader 
    } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少参与者ID',
        data: null
      });
    }
    
    // 查询参与者是否存在
    const participant = await researchProjectParticipantsModel.findByPk(id);
    
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '未找到参与者',
        data: null
      });
    }
    
    // 如果更新为负责人，需要检查项目是否已有其他负责人
    if (isLeader && !participant.isLeader) {
      const existingLeader = await researchProjectParticipantsModel.findOne({
        where: {
          projectId: participant.projectId,
          isLeader: true,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingLeader) {
        return res.status(400).json({
          code: 400,
          message: '该项目已有负责人',
          data: null
        });
      }
    }
    
    // 验证分配比例是否在有效范围内 (0-1)
    if (allocationRatio !== undefined && (allocationRatio < 0 || allocationRatio > 1)) {
      return res.status(400).json({
        code: 400,
        message: '分配比例必须在0到1之间',
        data: null
      });
    }
    
    // 更新参与者
    const updateData = {};
    if (allocationRatio !== undefined) updateData.allocationRatio = allocationRatio;
    if (participantRank !== undefined) updateData.participantRank = participantRank;
    if (isLeader !== undefined) updateData.isLeader = isLeader;
    
    await participant.update(updateData);
    
    // 重新加载数据以返回最新信息
    const updatedParticipant = await researchProjectParticipantsModel.findByPk(id, {
      include: [
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: researchProjectModel,
          as: 'project',
          attributes: ['id', 'projectId', 'name'],
          required: false
        }
      ]
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: updatedParticipant
    });
  } catch (error) {
    console.error('更新项目参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新项目参与者失败',
      error: error.message
    });
  }
};

/**
 * 删除项目参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteProjectParticipant = async (req, res) => {
  try {
    const { id } = req.params;
    const userInfo = await getUserInfoFromRequest(req);
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少参与者ID',
        data: null
      });
    }
    
    // 查询参与者是否存在
    const participant = await researchProjectParticipantsModel.findByPk(id);
    
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '未找到参与者',
        data: null
      });
    }
    
    // 检查权限：只有管理员或项目负责人可以删除参与者
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      // 查找当前用户是否是项目负责人
      const isProjectLeader = await researchProjectParticipantsModel.findOne({
        where: {
          projectId: participant.projectId,
          userId: userInfo.id,
          isLeader: true
        }
      });
      
      if (!isProjectLeader) {
        return res.status(403).json({
          code: 403,
          message: '您没有权限删除该参与者',
          data: null
        });
      }
    }
    
    // 删除参与者
    await participant.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除项目参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除项目参与者失败',
      error: error.message
    });
  }
};

/**
 * 批量添加项目参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchAddProjectParticipants = async (req, res) => {
  try {
    const { projectId, participants } = req.body;
    
    if (!projectId || !participants || !Array.isArray(participants) || participants.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '参数错误',
        data: null
      });
    }
    
    // 检查项目是否存在
    const project = await researchProjectModel.findByPk(projectId);
    if (!project) {
      return res.status(404).json({
        code: 404,
        message: '项目不存在',
        data: null
      });
    }
    
    // 检查是否已有负责人
    const existingLeader = await researchProjectParticipantsModel.findOne({
      where: {
        projectId,
        isLeader: true
      }
    });
    
    // 处理结果统计
    const result = {
      success: 0,
      failed: 0,
      errors: []
    };
    
    // 批量添加参与者
    for (const participant of participants) {
      try {
        // 检查必要字段
        if (!participant.userId || participant.allocationRatio === undefined) {
          result.failed++;
          result.errors.push({
            userId: participant.userId,
            message: '用户ID和分配比例不能为空'
          });
          continue;
        }
        
        // 检查用户是否存在
        const user = await userModel.findByPk(participant.userId);
        if (!user) {
          result.failed++;
          result.errors.push({
            userId: participant.userId,
            message: '用户不存在'
          });
          continue;
        }
        
        // 检查是否已存在相同的参与记录
        const existingParticipant = await researchProjectParticipantsModel.findOne({
          where: {
            projectId,
            userId: participant.userId
          }
        });
        
        if (existingParticipant) {
          result.failed++;
          result.errors.push({
            userId: participant.userId,
            message: '该用户已是项目参与者'
          });
          continue;
        }
        
        // 处理负责人逻辑
        if (participant.isLeader && existingLeader) {
          result.failed++;
          result.errors.push({
            userId: participant.userId,
            message: '该项目已有负责人'
          });
          continue;
        }
        
        // 创建参与者记录
        await researchProjectParticipantsModel.create({
          projectId,
          userId: participant.userId,
          allocationRatio: participant.allocationRatio,
          participantRank: participant.participantRank || null,
          isLeader: participant.isLeader || false
        });
        
        result.success++;
      } catch (error) {
        result.failed++;
        result.errors.push({
          userId: participant.userId,
          message: error.message
        });
      }
    }
    
    return res.status(200).json({
      code: 200,
      message: '批量添加完成',
      data: result
    });
  } catch (error) {
    console.error('批量添加项目参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '批量添加项目参与者失败',
      error: error.message
    });
  }
}; 