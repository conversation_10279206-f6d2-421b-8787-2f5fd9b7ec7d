const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义教材出版核算规则模型
module.exports = sequelize.define('textbook_publishing_rules', // 数据库表名为textbook_publishing_rules
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: '主键，使用 UUID 唯一标识每条记录',
        },
        category: {
            type: DataTypes.STRING(255),
            notNull: true,
            allowNull: false,
            comment: '类别（如国家级规划教材、国家级课程等）',
        },
        role: {
            type: DataTypes.STRING(255),
            notNull: true,
            allowNull: false,
            comment: '角色（如主编、副主编、编委等）',
        },
        score: {
            type: DataTypes.DECIMAL(10, 2),
            notNull: true,
            allowNull: false,
            comment: '核算分数',
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: '类别的详细描述（如国家级规划教材、国家级课程等）',
        },
        additionalInfo: {
            type: DataTypes.TEXT,
            allowNull: true,
            comment: '额外信息（如需提供转让合同或应用证明等）',
        },
        createdBy: {
            type: DataTypes.UUID,
            notNull: true,
            allowNull: false,
            comment: '创建者 ID（userId）',
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录创建时间',
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录最后修改时间',
        },
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
        indexes: [
            {
                unique: true,
                fields: ['category', 'role'],
                name: 'uk_category_role'
            }
        ]
    }); 