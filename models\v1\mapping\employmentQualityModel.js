const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义就业质量模型
const EmploymentQuality = sequelize.define('employment_quality', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: 'ID'
  },
  userId: {
    type: DataTypes.CHAR(36),
    allowNull: true,
    comment: '用户ID'
  },
  username: {
    type: DataTypes.STRING(255),
    allowNull: false,
    defaultValue: '',
    comment: '用户名称'
  },
  major: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '专业名称'
  },
  year: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '统计年份'
  },
  employmentRate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    comment: '就业率(%)'
  },
  employmentIndustry: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '就业行业'
  },
  employmentRegion: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '就业地区'
  },
  averageSalary: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '平均薪资(元/月)'
  },
  majorMatchRate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: '专业对口率(%)'
  },
  employmentSatisfaction: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: '就业满意度(%)'
  },
  score: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '得分'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  },
  ifReviewer: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    comment: '审核状态（0，拒审核 1，审核，null未审核）'
  },
  attachmentUrl: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '附件URL'
  },
  reviewComment: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核意见'
  },
  reviewerId: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: '审核人 ID'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    defaultValue: 1,
    comment: '状态'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'employment_quality',
  timestamps: true,
  indexes: [
    {
      name: 'idx_employment_quality_major',
      fields: ['major']
    },
    {
      name: 'idx_employment_quality_year',
      fields: ['year']
    },
    {
      name: 'idx_employment_quality_status',
      fields: ['status']
    },
    {
      name: 'idx_employment_quality_user_id',
      fields: ['userId']
    }
  ]
});

module.exports = EmploymentQuality; 