// routes/userRoutes.js
const express = require('express');
const permissionsController = require('../../../controllers/v1/system/permissionsController');

const router = express.Router();

/**
 * 获取所有权限
 * @route POST /v1/sys/permissions/list
 * @group 权限管理 - 系统权限相关接口
 * @param {number} page.body - 页码，默认1
 * @param {number} limit.body - 每页数量，默认10
 * @param {string} name.body - 权限名称（模糊搜索）
 * @param {string} code.body - 权限编码（模糊搜索）
 * @param {number} status.body - 状态（0-停用，1-启用）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', permissionsController.getAll);

/**
 * 创建权限
 * @route POST /v1/sys/permissions/create
 * @group 权限管理 - 系统权限相关接口
 * @param {string} name.body.required - 权限名称
 * @param {string} code.body.required - 权限编码
 * @param {string} type.body.required - 权限类型
 * @param {string} parentId.body - 父级ID
 * @param {string} description.body - 权限描述
 * @param {number} status.body - 状态（0-停用，1-启用），默认1
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "权限ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', permissionsController.create);

/**
 * 获取指定权限
 * @route POST /v1/sys/permissions/findOne
 * @group 权限管理 - 系统权限相关接口
 * @param {string} id.body.required - 权限ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {权限详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/findOne', permissionsController.findOne);

/**
 * 更新权限
 * @route POST /v1/sys/permissions/update
 * @group 权限管理 - 系统权限相关接口
 * @param {string} id.body.required - 权限ID
 * @param {string} name.body - 权限名称
 * @param {string} code.body - 权限编码
 * @param {string} type.body - 权限类型
 * @param {string} parentId.body - 父级ID
 * @param {string} description.body - 权限描述
 * @param {number} status.body - 状态（0-停用，1-启用）
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update', permissionsController.update);

/**
 * 删除权限
 * @route POST /v1/sys/permissions/delete
 * @group 权限管理 - 系统权限相关接口
 * @param {string} id.body.required - 权限ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', permissionsController.delete);

/**
 * 停用权限
 * @route POST /v1/sys/permissions/stop
 * @group 权限管理 - 系统权限相关接口
 * @param {string} id.body.required - 权限ID
 * @returns {object} 200 - {code: 200, message: "停用成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stop', permissionsController.stop);

/**
 * 获取权限树
 * @route POST /v1/sys/permissions/tree
 * @group 权限管理 - 系统权限相关接口
 * @param {number} status.body - 状态（0-停用，1-启用），不传则返回所有
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {tree: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/tree', permissionsController.tree);

module.exports = router;
