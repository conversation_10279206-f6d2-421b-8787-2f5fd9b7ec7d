<template>
  <div class="notification-template">
    <a-card title="通知模板管理" :bordered="false">
      <template #extra>
        <a-button type="primary" @click="handleAddTemplate">
          <template #icon><PlusOutlined /></template>
          新建模板
        </a-button>
      </template>

      <a-table
        :columns="columns"
        :data-source="templates"
        :pagination="pagination"
        :loading="loading"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeName(record.type) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEditTemplate(record)">编辑</a>
              <a-divider type="vertical" />
              <a @click="handleUseTemplate(record)">使用</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这个模板吗？"
                @confirm="handleDeleteTemplate(record)"
              >
                <a class="text-danger">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑模板' : '新建模板'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :width="700"
    >
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="模板名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入模板名称" />
        </a-form-item>

        <a-form-item label="通知类型" name="type">
          <a-select
            v-model:value="formState.type"
            placeholder="请选择通知类型"
            :options="typeOptions"
          />
        </a-form-item>

        <a-form-item label="模板标题" name="title">
          <a-input v-model:value="formState.title" placeholder="请输入通知标题" />
        </a-form-item>

        <a-form-item label="模板内容" name="content">
          <a-textarea
            v-model:value="formState.content"
            :rows="8"
            placeholder="请输入通知内容模板"
          />
        </a-form-item>

        <a-form-item label="使用说明" name="description">
          <a-textarea
            v-model:value="formState.description"
            :rows="3"
            placeholder="请输入使用说明"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  getNotificationTemplates,
  createNotificationTemplate,
  updateNotificationTemplate,
  deleteNotificationTemplate
} from '@/api/modules/api.notifications'

const router = useRouter()
const formRef = ref(null)

// 表格列定义
const columns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '通知类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '模板标题',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    sorter: true,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleString('zh-CN') : ''
    }
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 通知类型选项
const typeOptions = [
  { label: '系统通知', value: 'system' },
  { label: '重要通知', value: 'important' },
  { label: '提醒通知', value: 'reminder' },
  { label: '普通通知', value: 'normal' },
  { label: '结果通知', value: 'result' }
]

// 数据管理
const templates = ref([])
const loading = ref(false)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  total: 0,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    loadTemplates()
  }
})

// 加载模板列表
const loadTemplates = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    const response = await getNotificationTemplates(params)

    if (response.status === 1) {
      templates.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('加载模板列表失败:', error)
    message.error('加载模板列表失败')
  } finally {
    loading.value = false
  }
}

// 模态框相关
const modalVisible = ref(false)
const isEdit = ref(false)
const currentTemplate = ref(null)

const formState = reactive({
  name: '',
  type: 'normal',
  title: '',
  content: '',
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在2-50个字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择通知类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入模板标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度在2-50个字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入模板内容', trigger: 'blur' },
    { min: 10, message: '内容不能少于10个字符', trigger: 'blur' }
  ]
}

// 新建模板
const handleAddTemplate = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

// 编辑模板
const handleEditTemplate = (record) => {
  isEdit.value = true
  currentTemplate.value = record
  
  formState.name = record.name
  formState.type = record.type
  formState.title = record.title
  formState.content = record.content
  formState.description = record.description || ''
  
  modalVisible.value = true
}

// 使用模板
const handleUseTemplate = (record) => {
  // 跳转到发送通知页面并带上模板数据
  router.push({
    path: '/notification/send',
    query: {
      templateId: record.id
    }
  })
}

// 删除模板
const handleDeleteTemplate = async (record) => {
  try {
    const response = await deleteNotificationTemplate(record.id)
    if (response.status === 1) {
      message.success('模板已删除')
      loadTemplates() // 重新加载列表
    } else {
      message.error(response.message || '删除模板失败')
    }
  } catch (error) {
    console.error('删除模板失败:', error)
    message.error('删除模板失败')
  }
}

// 模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()

    const templateData = {
      name: formState.name,
      type: formState.type,
      title: formState.title,
      content: formState.content,
      description: formState.description
    }

    let response
    if (isEdit.value) {
      // 编辑模式
      response = await updateNotificationTemplate(currentTemplate.value.id, templateData)
    } else {
      // 新建模式
      response = await createNotificationTemplate(templateData)
    }

    if (response.status === 1) {
      message.success(isEdit.value ? '模板已更新' : '模板已创建')
      modalVisible.value = false
      loadTemplates() // 重新加载列表
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('保存模板失败:', error)
    if (error.response?.data?.message) {
      message.error(error.response.data.message)
    } else {
      message.error('保存模板失败，请重试')
    }
  }
}

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
}

// 重置表单
const resetForm = () => {
  formState.name = ''
  formState.type = 'normal'
  formState.title = ''
  formState.content = ''
  formState.description = ''
}

// 获取通知类型名称
const getTypeName = (type) => {
  const typeMap = {
    'system': '系统通知',
    'important': '重要通知',
    'reminder': '提醒通知',
    'normal': '普通通知',
    'result': '结果通知'
  }
  return typeMap[type] || '未知类型'
}

// 获取通知类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    'system': 'cyan',
    'important': 'red',
    'reminder': 'orange',
    'normal': 'blue',
    'result': 'green'
  }
  return colorMap[type] || 'default'
}

// 初始化加载模板列表
onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.notification-template {
  padding: 24px;
}

.text-danger {
  color: #ff4d4f;
}
</style> 