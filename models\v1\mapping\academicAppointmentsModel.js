const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const associationLevelModel = require('./associationLevelsModel');
const userModel = require('./userModel');

// 定义学术任职模型
const AcademicAppointment = sequelize.define('academicAppointments', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '记录ID'
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '用户ID（外键）'
    },
    associationName: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '协会/期刊名称'
    },
    position: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '职务名称'
    },
    levelId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '级别ID（外键）'
    },
    startYear: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '起始年份'
    },
    endYear: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '结束年份（空为至今）'
    },
    remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注'
    },
    ifReviewer: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        comment: '审核状态（0，拒审核 1，审核，null未审核）'
      },
      attachmentUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '附件URL'
      },
      reviewComment: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '审核意见'
      },
    reviewerId: {
        type: DataTypes.CHAR(36),
        allowNull: true,
        comment: '审核人ID'
    },
    status: {
        type: DataTypes.TINYINT,
        allowNull: true,
        defaultValue: 1,
        comment: '状态（1-有效）'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'academicAppointments',
    timestamps: true,
    indexes: [
        {
            name: 'idx_user',
            fields: ['userId']
        },
        {
            name: 'idx_association',
            fields: ['associationName']
        },
        {
            name: 'idx_level_id',
            fields: ['levelId']
        }
    ]
});

// 建立与学术任职级别的关联关系
AcademicAppointment.belongsTo(associationLevelModel, {
    foreignKey: 'levelId',
    as: 'level'
});

// 建立与用户的关联关系
AcademicAppointment.belongsTo(userModel, {
    foreignKey: 'userId',
    as: 'user'
});

// 建立与审核人的关联关系
AcademicAppointment.belongsTo(userModel, {
    foreignKey: 'reviewerId',
    as: 'reviewer'
});

module.exports = AcademicAppointment; 