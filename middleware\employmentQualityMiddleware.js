/**
 * 就业质量中间件
 * 用于处理就业质量相关请求的数据验证和预处理
 */
const { body, param, query, validationResult } = require('express-validator');
const EmploymentQuality = require('@/models/v1/mapping/employmentQualityModel');

// 验证就业质量记录创建和更新请求
exports.validateEmploymentQuality = [
  body('major')
    .notEmpty().withMessage('专业名称不能为空')
    .isLength({ max: 50 }).withMessage('专业名称长度不能超过50个字符'),
  
  body('year')
    .notEmpty().withMessage('年份不能为空')
    .isInt({ min: 2000, max: new Date().getFullYear() }).withMessage('年份必须是有效的年份'),
  
  body('employmentRate')
    .notEmpty().withMessage('就业率不能为空')
    .isFloat({ min: 0, max: 100 }).withMessage('就业率必须在0-100之间'),
  
  body('employmentIndustry')
    .notEmpty().withMessage('就业行业不能为空')
    .isLength({ max: 50 }).withMessage('就业行业长度不能超过50个字符'),
  
  body('employmentRegion')
    .notEmpty().withMessage('就业地区不能为空')
    .isLength({ max: 50 }).withMessage('就业地区长度不能超过50个字符'),
  
  body('averageSalary')
    .optional()
    .isFloat({ min: 0 }).withMessage('平均薪资必须大于等于0'),
  
  body('majorMatchRate')
    .optional()
    .isFloat({ min: 0, max: 100 }).withMessage('专业对口率必须在0-100之间'),
  
  body('employmentSatisfaction')
    .optional()
    .isFloat({ min: 0, max: 100 }).withMessage('就业满意度必须在0-100之间'),
  
  body('score')
    .optional()
    .isFloat({ min: 0, max: 100 }).withMessage('得分必须在0-100之间'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: errors.array()[0].msg,
        data: null
      });
    }
    next();
  }
];

// 验证路径参数ID
exports.validatePathId = [
  param('id')
    .notEmpty().withMessage('ID不能为空'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: errors.array()[0].msg,
        data: null
      });
    }
    next();
  }
];

// 验证ID参数 (查询参数userId)
exports.validateEmploymentQualityId = [
  query('userId')
    .notEmpty().withMessage('ID不能为空'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: errors.array()[0].msg,
        data: null
      });
    }
    next();
  }
];

// 验证分页参数
exports.validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 }).withMessage('页码必须是大于等于1的整数'),
  
  query('pageSize')
    .optional()
    .isInt({ min: 1, max: 100 }).withMessage('每页记录数必须是1-100之间的整数'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: errors.array()[0].msg,
        data: null
      });
    }
    next();
  }
];

// 验证就业率趋势参数
exports.validateEmploymentRateTrend = [
  query('startYear')
    .optional()
    .isInt({ min: 2000, max: new Date().getFullYear() }).withMessage('开始年份必须是有效的年份'),
  
  query('endYear')
    .optional()
    .isInt({ min: 2000, max: new Date().getFullYear() }).withMessage('结束年份必须是有效的年份'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: errors.array()[0].msg,
        data: null
      });
    }
    
    // 如果同时提供了开始年份和结束年份，检查开始年份是否小于等于结束年份
    const { startYear, endYear } = req.query;
    if (startYear && endYear && parseInt(startYear) > parseInt(endYear)) {
      return res.status(400).json({
        code: 400,
        message: '开始年份不能大于结束年份',
        data: null
      });
    }
    
    next();
  }
];

// 验证就业质量年度分析参数
exports.validateEmploymentAnalysis = [
  query('year')
    .notEmpty().withMessage('年份不能为空')
    .isInt({ min: 2000, max: new Date().getFullYear() }).withMessage('年份必须是有效的年份'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: errors.array()[0].msg,
        data: null
      });
    }
    next();
  }
];

// 验证就业行业和地区分布参数
exports.validateDistributionParams = [
  query('year')
    .optional()
    .isInt({ min: 2000, max: new Date().getFullYear() }).withMessage('年份必须是有效的年份'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: errors.array()[0].msg,
        data: null
      });
    }
    next();
  }
];

// 检查用户权限
exports.checkEmploymentQualityPermission = async (req, res, next) => {
  return next(); // 暂时关闭权限检查 默认放行， 后续在auth中间件中添加
  try {
    // 检查模型是否正确导入
    if (!EmploymentQuality) {
      console.error('错误: EmploymentQuality 模型未定义');
      return res.status(500).json({
        code: 500,
        message: '服务器错误：数据模型未定义',
        data: null
      });
    }
    
    // 获取当前用户ID和角色
    const userId = req.user?.id;
    const role = req.user?.role;
    
    // 如果是管理员角色，直接通过
    if (role === 'admin') {
      return next();
    }
    
    // 如果是查看个人数据相关接口，必须登录
    if (req.path.includes('/personal') && !userId) {
      return res.status(401).json({
        code: 401,
        message: '未授权，请先登录',
        data: null
      });
    }
    
    // 对于其他操作，如果不是管理员，只能操作自己的数据
    if (req.method !== 'GET' && req.params.id) {
      const { id } = req.params;
      
      try {
        const record = await EmploymentQuality.findByPk(id);
        
        if (record && record.userId !== userId) {
          return res.status(403).json({
            code: 403,
            message: '无权操作此数据',
            data: null
          });
        }
      } catch (dbError) {
        console.error('数据库查询失败:', dbError);
        return res.status(500).json({
          code: 500, 
          message: '查询记录失败',
          data: null
        });
      }
    }
    
    next();
  } catch (error) {
    console.error('检查就业质量权限失败:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误',
      data: null
    });
  }
}; 