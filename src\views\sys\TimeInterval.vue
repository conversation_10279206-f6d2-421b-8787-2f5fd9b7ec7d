<template>
  <div class="time-interval-container">
    <div class="search-bar">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="类型">
          <a-input v-model:value="searchForm.category" placeholder="请输入类型" allowClear />
        </a-form-item>
        <a-form-item label="名称">
          <a-input v-model:value="searchForm.nameC" placeholder="请输入名称" allowClear />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <template #icon><SearchOutlined /></template>
            查询
          </a-button>
          <a-button style="margin-left: 8px" @click="handleReset">
            <template #icon><ReloadOutlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </div>

    <div class="operation-bar">
      <a-button type="primary" @click="handleAdd">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
      <a-button 
        danger 
        :disabled="selectedRowKeys.length === 0"
        @click="handleBatchDelete"
        style="margin-left: 8px"
      >
        <template #icon><DeleteOutlined /></template>
        批量删除
      </a-button>
    </div>

    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-space>
            <a @click="handleEdit(record)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定要删除这条记录吗？"
              @confirm="handleDelete(record)"
            >
              <a class="danger-text">删除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :mask-closable="false"
      :destroy-on-close="true"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="类型" name="category">
          <a-input v-model:value="formData.category" placeholder="请输入类型" />
        </a-form-item>
        <a-form-item label="名称" name="nameC">
          <a-input v-model:value="formData.nameC" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item label="开始时间" name="startTime">
          <a-date-picker
            v-model:value="formData.startTime"
            style="width: 100%"
            placeholder="请选择开始时间"
            :format="dateFormat"
          />
        </a-form-item>
        <a-form-item label="结束时间" name="endTime">
          <a-date-picker
            v-model:value="formData.endTime"
            style="width: 100%"
            placeholder="请选择结束时间"
            :format="dateFormat"
          />
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  getTimeIntervals,
  createTimeInterval,
  updateTimeInterval,
  deleteTimeInterval,
  batchDeleteTimeIntervals
} from '@/api/timeInterval'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

export default defineComponent({
  name: 'TimeInterval',
  components: {
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    DeleteOutlined
  },
  setup() {
    const formRef = ref()
    const dateFormat = 'YYYY-MM-DD'
    
    const searchForm = reactive({
      category: '',
      nameC: ''
    })
    const formData = reactive({
      id: '',
      category: '',
      nameC: '',
      startTime: null,
      endTime: null,
      description: ''
    })
    const rules = {
      category: [{ required: true, message: '请输入类型', trigger: 'blur' }],
      nameC: [{ required: true, message: '请输入名称', trigger: 'blur' }],
      startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
      endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
    }

    const columns = [
      { title: '类型', dataIndex: 'category', key: 'category' },
      { title: '名称', dataIndex: 'nameC', key: 'nameC' },
      { title: '开始时间', dataIndex: 'startTime', key: 'startTime' },
      { title: '结束时间', dataIndex: 'endTime', key: 'endTime' },
      { title: '描述', dataIndex: 'description', key: 'description' },
      { title: '操作', key: 'action', width: 150 }
    ]

    const loading = ref(false)
    const modalVisible = ref(false)
    const modalLoading = ref(false)
    const modalTitle = ref('新增时间区间')
    const selectedRowKeys = ref([])
    const tableData = ref([])
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showTotal: total => `共 ${total} 条`
    })

    // 获取列表数据
    const fetchData = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm
        }
        const res = await getTimeIntervals(params)
        if (res.code === 200) {
          tableData.value = res.data.list
          pagination.total = res.data.total
        }
      } catch (error) {
        console.error('获取列表失败:', error)
        message.error('获取列表失败')
      } finally {
        loading.value = false
      }
    }

    // 表格选择
    const onSelectChange = (keys) => {
      selectedRowKeys.value = keys
    }

    // 查询
    const handleSearch = () => {
      pagination.current = 1
      fetchData()
    }

    // 重置
    const handleReset = () => {
      searchForm.category = ''
      searchForm.nameC = ''
      handleSearch()
    }

    // 表格变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current
      pagination.pageSize = pag.pageSize
      fetchData()
    }

    // 新增
    const handleAdd = () => {
      // 重置表单数据
      formData.id = ''
      formData.category = ''
      formData.nameC = ''
      formData.startTime = null
      formData.endTime = null
      formData.description = ''
      
      modalTitle.value = '新增时间区间'
      modalVisible.value = true
      
      // 等待DOM更新后再重置表单验证状态
      nextTick(() => {
        if (formRef.value) {
          formRef.value.resetFields()
        }
      })
    }

    // 编辑
    const handleEdit = (record) => {
      // 使用深拷贝避免直接修改表格数据
      const recordCopy = JSON.parse(JSON.stringify(record))
      
      // 重置表单数据
      formData.id = recordCopy.id
      formData.category = recordCopy.category
      formData.nameC = recordCopy.nameC
      
      // 处理日期格式 - 将字符串转换为日期对象
      try {
        // 检查日期是否为字符串，如果是则转换为日期对象
        if (typeof recordCopy.startTime === 'string') {
          formData.startTime = dayjs(recordCopy.startTime)
        } else {
          formData.startTime = recordCopy.startTime
        }
        
        if (typeof recordCopy.endTime === 'string') {
          formData.endTime = dayjs(recordCopy.endTime)
        } else {
          formData.endTime = recordCopy.endTime
        }
      } catch (error) {
        console.error('日期转换失败:', error)
        formData.startTime = null
        formData.endTime = null
      }
      
      formData.description = recordCopy.description || ''
      
      modalTitle.value = '编辑时间区间'
      modalVisible.value = true
      
      // 等待DOM更新后再重置表单验证状态
      nextTick(() => {
        if (formRef.value) {
          formRef.value.resetFields()
        }
      })
    }

    // 删除
    const handleDelete = async (record) => {
      try {
        const res = await deleteTimeInterval(record.id)
        if (res.code === 200) {
          message.success('删除成功')
          fetchData()
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }

    // 批量删除
    const handleBatchDelete = async () => {
      try {
        const res = await batchDeleteTimeIntervals(selectedRowKeys.value)
        if (res.code === 200) {
          message.success('批量删除成功')
          selectedRowKeys.value = []
          fetchData()
        }
      } catch (error) {
        console.error('批量删除失败:', error)
        message.error('批量删除失败')
      }
    }

    // 弹窗确认
    const handleModalOk = () => {
      if (!formRef.value) {
        message.error('表单引用不存在，请刷新页面重试')
        return
      }
      
      formRef.value.validate().then(async () => {
        modalLoading.value = true
        try {
          // 准备提交的数据
          const submitData = {
            ...formData,
            // 确保日期格式正确
            startTime: formData.startTime ? formData.startTime.format(dateFormat) : null,
            endTime: formData.endTime ? formData.endTime.format(dateFormat) : null
          }
          
          const api = formData.id ? updateTimeInterval : createTimeInterval
          const res = await api(submitData)
          
          if (res.code === 200) {
            message.success(formData.id ? '更新成功' : '创建成功')
            modalVisible.value = false
            fetchData()
          }
        } catch (error) {
          console.error(formData.id ? '更新失败:' : '创建失败:', error)
          message.error(formData.id ? '更新失败' : '创建失败')
        } finally {
          modalLoading.value = false
        }
      }).catch(error => {
        console.error('表单验证失败:', error)
      })
    }

    // 弹窗取消
    const handleModalCancel = () => {
      modalVisible.value = false
    }

    onMounted(() => {
      fetchData()
    })

    return {
      formRef,
      searchForm,
      formData,
      rules,
      columns,
      loading,
      modalVisible,
      modalLoading,
      modalTitle,
      selectedRowKeys,
      tableData,
      pagination,
      dateFormat,
      onSelectChange,
      handleSearch,
      handleReset,
      handleTableChange,
      handleAdd,
      handleEdit,
      handleDelete,
      handleBatchDelete,
      handleModalOk,
      handleModalCancel
    }
  }
})
</script>

<style lang="less" scoped>
.time-interval-container {
  padding: 24px;
  background: #fff;

  .search-bar {
    margin-bottom: 16px;
  }

  .operation-bar {
    margin-bottom: 16px;
  }

  .danger-text {
    color: #ff4d4f;
  }
}
</style> 