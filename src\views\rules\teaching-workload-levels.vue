<template>
  <div class="workload-levels">
    <!-- 错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />

    <a-card title="教学工作量级别管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><PlusOutlined /></template>
            添加级别
          </a-button>
          <a-button @click="handleExport">
            <template #icon><DownloadOutlined /></template>
            导出级别
          </a-button>
        </a-space>
      </template>

      <a-alert
        message="级别说明"
        description="工作量级别用于对教学工作量进行归类和评分，不同级别的工作量对应不同的分数。维护人员可以添加、编辑和删除工作量级别，但删除时需要确保该级别下没有关联的工作量记录。"
        type="info"
        show-icon
        style="margin-bottom: 16px;"
      />

      <!-- 搜索表单 -->
      <a-card title="搜索筛选" :bordered="false" size="small" style="margin-bottom: 16px;">
        <a-form :model="searchForm" @finish="handleSearch" layout="inline">
          <a-form-item label="级别名称" name="categoryName">
            <a-input
              v-model:value="searchForm.categoryName"
              placeholder="请输入级别名称"
              style="width: 200px;"
              allow-clear
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit" :loading="isLoading">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>

      <a-table
        :columns="columns"
        :data-source="filteredDataSource"
        :pagination="pagination"
        :loading="isLoading"
        rowKey="id"
        @change="handleTableChange"
        :scroll="{ x: 800 }"
        :bordered="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'score'">
            <span style="font-weight: bold; color: #1890ff;">{{ record.score }}分</span>
          </template>
          <template v-else-if="column.key === 'workloadCount'">
            <a @click="showWorkloadsInLevel(record)" v-if="record.workloadCount > 0">
              <a-tag color="blue">{{ record.workloadCount }}</a-tag>
            </a>
            <a-tag v-else color="default">0</a-tag>
          </template>
          <template v-else-if="column.key === 'createdAt'">
            {{ formatDate(record.createdAt) }}
          </template>
          <template v-else-if="column.key === 'updatedAt'">
            {{ formatDate(record.updatedAt) }}
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="showEditModal(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这个级别吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
                :disabled="record.workloadCount > 0"
              >
                <a :class="{ disabled: record.workloadCount > 0 }">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 新增/编辑模态框 -->
      <a-modal
        :title="modalTitle"
        :visible="modalVisible"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="confirmLoading"
        width="600px"
      >
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="类别名称" name="categoryName">
            <a-input
              v-model:value="formState.categoryName"
              :maxLength="100"
              placeholder="请输入类别名称"
            />
          </a-form-item>
          <a-form-item label="基础分数" name="score">
            <a-input-number
              v-model:value="formState.score"
              :min="0"
              :precision="2"
              :step="0.01"
              style="width: 100%"
              placeholder="请输入基础分数"
            />
          </a-form-item>
          <a-form-item label="状态" name="status">
            <a-radio-group v-model:value="formState.status">
              <a-radio :value="1">启用</a-radio>
              <a-radio :value="0">禁用</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="描述" name="description">
            <a-textarea
              v-model:value="formState.description"
              :rows="4"
              :maxLength="500"
              placeholder="请输入级别描述"
              show-count
            />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 工作量列表模态框 -->
      <a-modal
        :title="`${selectedLevel?.categoryName || ''} - 关联的工作量列表`"
        :visible="workloadsModalVisible"
        @cancel="closeWorkloadsModal"
        width="1000px"
        :footer="null"
      >
        <div v-if="selectedLevel">
          <h3>{{ selectedLevel.categoryName }} ({{ selectedLevel.score }}分)</h3>
          <a-table
            :columns="workloadColumns"
            :data-source="levelWorkloads"
            :pagination="{ pageSize: 10 }"
            :loading="workloadsLoading"
            rowKey="id"
            :bordered="true"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'semester'">
                {{ record.semester }}
              </template>
              <template v-else-if="column.key === 'participants'">
                {{ formatParticipants(record.participants) }}
              </template>
              <template v-else-if="column.key === 'reviewStatus'">
                <a-tag :color="record.reviewStatus === 'reviewed' ? 'green' : 'orange'">
                  {{ record.reviewStatus === 'reviewed' ? '已审核' : '待审核' }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onBeforeUnmount } from 'vue'
import { PlusOutlined, SearchOutlined, DownloadOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

// 导入API
import {
  getWorkloadLevelsList,
  getWorkloadLevelDetail,
  createWorkloadLevel,
  updateWorkloadLevel,
  deleteWorkloadLevel,
  batchDeleteWorkloadLevels
} from '@/api/modules/api.teachingWorkloads'

// 错误信息
const errorMessage = ref('')

// 表格列定义
const columns = [
  {
    title: '类别名称',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 200
  },
  {
    title: '基础分数',
    dataIndex: 'score',
    key: 'score',
    width: 120,
    sorter: true,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '关联工作量',
    dataIndex: 'workloadCount',
    key: 'workloadCount',
    width: 120,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
    key: 'updatedAt',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    align: 'center'
  }
]

// 工作量列表的列定义
const workloadColumns = [
  {
    title: '课程名称',
    dataIndex: 'courseName',
    key: 'courseName',
    width: 200
  },
  {
    title: '学期',
    dataIndex: 'semester',
    key: 'semester',
    width: 120
  },
  {
    title: '课程类型',
    dataIndex: 'courseType',
    key: 'courseType',
    width: 120
  },
  {
    title: '参与者',
    key: 'participants',
    width: 200
  },
  {
    title: '审核状态',
    key: 'reviewStatus',
    width: 100,
    align: 'center'
  }
]

// 数据源
const dataSource = ref([])
const isLoading = ref(false)

// 搜索表单
const searchForm = reactive({
  categoryName: ''
})

// 计算过滤后的数据源
const filteredDataSource = computed(() => {
  if (!searchForm.categoryName) {
    return dataSource.value
  }
  return dataSource.value.filter(item =>
    item.categoryName && item.categoryName.toLowerCase().includes(searchForm.categoryName.toLowerCase())
  )
})

// 重置搜索条件
const resetSearch = () => {
  searchForm.categoryName = ''
  handleSearch()
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)
const modalTitle = computed(() => isEdit.value ? '编辑级别' : '添加级别')

// 工作量列表模态框
const workloadsModalVisible = ref(false)
const selectedLevel = ref(null)
const levelWorkloads = ref([])
const workloadsLoading = ref(false)

// 表单引用
const formRef = ref(null)

// 表单数据
const formState = reactive({
  categoryName: '',
  score: 0,
  status: 1,
  description: ''
})

// 校验规则
const rules = {
  categoryName: [{ required: true, message: '请输入类别名称', trigger: 'blur' }],
  score: [{ required: true, message: '请输入基础分数', trigger: 'change' }]
}

// 获取列表数据
const fetchData = async () => {
  isLoading.value = true
  errorMessage.value = ''

  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    if (searchForm.categoryName) {
      params.categoryName = searchForm.categoryName
    }

    const response = await getWorkloadLevelsList(params)

    if (response && response.code === 200) {
      dataSource.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = '获取工作量级别列表失败：' + (response?.message || '未知错误')
    }
  } catch (error) {
    console.error('获取工作量级别列表失败:', error)
    message.error('获取工作量级别列表失败: ' + (error.message || '未知错误'))
    errorMessage.value = '获取工作量级别列表失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 处理表格变化
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize

  // 处理排序
  if (sorter && sorter.field) {
    // 这里可以添加排序逻辑
  }

  fetchData()
}

// 显示添加级别弹窗
const showAddModal = () => {
  isEdit.value = false
  currentRecord.value = null

  // 重置表单
  formState.categoryName = ''
  formState.score = 0
  formState.status = 1
  formState.description = ''

  // 显示弹窗
  modalVisible.value = true

  // 如果表单ref存在，重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 显示编辑级别弹窗
const showEditModal = async (record) => {
  isEdit.value = true
  currentRecord.value = record

  try {
    const response = await getWorkloadLevelDetail(record.id)
    if (response && response.code === 200) {
      const data = response.data
      formState.categoryName = data.categoryName
      formState.score = data.score
      formState.status = data.status
      formState.description = data.description || ''

      modalVisible.value = true
    } else {
      message.error(response?.message || '获取级别详情失败')
    }
  } catch (error) {
    console.error('获取级别详情失败:', error)
    message.error('获取级别详情失败: ' + (error.message || '未知错误'))
  }
}

// 处理模态框确定
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    confirmLoading.value = true

    const formData = {
      categoryName: formState.categoryName,
      score: formState.score,
      status: formState.status,
      description: formState.description
    }

    let response
    if (isEdit.value) {
      response = await updateWorkloadLevel({
        id: currentRecord.value.id,
        ...formData
      })
    } else {
      response = await createWorkloadLevel(formData)
    }

    if (response && response.code === 200) {
      message.success(isEdit.value ? '更新成功' : '创建成功')
      modalVisible.value = false
      fetchData()
    } else {
      message.error(response?.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败: ' + (error.message || '未知错误'))
  } finally {
    confirmLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

// 处理删除
const handleDelete = async (record) => {
  try {
    const response = await deleteWorkloadLevel(record.id)
    if (response && response.code === 200) {
      message.success('删除成功')
      fetchData()
    } else {
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败: ' + (error.message || '未知错误'))
  }
}

// 处理导出
const handleExport = () => {
  message.info('导出功能开发中')
}

// 查看级别下的工作量
const showWorkloadsInLevel = async (record) => {
  if (!record || record.workloadCount <= 0) return

  selectedLevel.value = record
  workloadsModalVisible.value = true
  workloadsLoading.value = true
  levelWorkloads.value = []

  try {
    // 这里需要实现获取级别下工作量的API
    // const response = await getWorkloadsByLevel(record.id)
    // 暂时使用空数据
    levelWorkloads.value = []
    message.info('获取级别工作量功能开发中')
  } catch (error) {
    console.error('获取级别工作量列表失败:', error)
    message.error('获取级别工作量列表失败: ' + (error.message || '未知错误'))
  } finally {
    workloadsLoading.value = false
  }
}

// 关闭工作量列表模态框
const closeWorkloadsModal = () => {
  workloadsModalVisible.value = false
  selectedLevel.value = null
  levelWorkloads.value = []
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 格式化参与者
const formatParticipants = (participants) => {
  if (!participants || participants.length === 0) return '-'
  return participants.map(p => p.nickname || p.displayName).join(', ')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})

</script>

<style lang="scss" scoped>
.workload-levels {
  padding: 24px;
}

.disabled {
  color: #ccc;
  cursor: not-allowed;
  pointer-events: none;
}

:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background: #fafafa;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}
</style>
