const express = require('express');
const router = express.Router();
const patentCategoryController = require('../../../controllers/v1/patents/patentCategoriesController');

/**
 * 获取专利分类列表
 * @route GET /v1/sys/patent-categories/categories
 * @group 专利分类管理 - 专利分类相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/categories', patentCategoryController.getPatentCategories);

/**
 * 获取所有分类及其专利数量
 * @route GET /v1/sys/patent-categories/categories-with-count
 * @group 专利分类管理 - 专利分类相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id, categoryName, score, remark, patentCount},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/categories-with-count', patentCategoryController.getCategoriesWithCount);

/**
 * 获取专利分类详情
 * @route GET /v1/sys/patent-categories/category/:id
 * @group 专利分类管理 - 专利分类相关接口
 * @param {string} id.path.required - 分类ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/category/:id', patentCategoryController.getCategoryDetail);

/**
 * 创建专利分类
 * @route POST /v1/sys/patent-categories/category/create
 * @group 专利分类管理 - 专利分类相关接口
 * @param {string} categoryName.body.required - 分类名称
 * @param {number} score.body.required - 分类分数
 * @param {string} remark.body - 备注
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/category/create', patentCategoryController.createCategory);

/**
 * 更新专利分类
 * @route POST /v1/sys/patent-categories/category/update
 * @group 专利分类管理 - 专利分类相关接口
 * @param {string} id.body.required - 分类ID
 * @param {string} categoryName.body - 分类名称
 * @param {number} score.body - 分类分数
 * @param {string} remark.body - 备注
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/category/update', async (req, res) => {
  const { id, ...updateData } = req.body;
  req.params = { id };
  req.body = updateData;
  await patentCategoryController.updateCategory(req, res);
});

/**
 * 删除专利分类
 * @route POST /v1/sys/patent-categories/category/delete
 * @group 专利分类管理 - 专利分类相关接口
 * @param {string} id.body.required - 分类ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/category/delete', async (req, res) => {
  const { id } = req.body;
  req.params = { id };
  await patentCategoryController.deleteCategory(req, res);
});

/**
 * 获取专利分类分布数据
 * @route POST /v1/sys/patent-categories/statistics/distribution
 * @group 专利分类统计 - 专利分类统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的专利
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{name: "分类名称", value: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/distribution', patentCategoryController.getCategoryDistribution);

module.exports = router; 