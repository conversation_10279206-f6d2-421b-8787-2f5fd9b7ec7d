const express = require('express');
const router = express.Router();
const textbookCategoriesController = require('../../../controllers/v1/textbooks/textbookCategoriesController');

/**
 * 获取教材与著作类别列表
 * @route GET /v1/sys/textbook-categories/categories
 * @group 教材与著作类别管理 - 教材与著作类别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/categories', textbookCategoriesController.getTextbookCategories);

/**
 * 获取所有类别及其教材与著作数量
 * @route GET /v1/sys/textbook-categories/categories-with-count
 * @group 教材与著作类别管理 - 教材与著作类别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{id, categoryAndPosition, score, textbooksCount},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/categories-with-count', textbookCategoriesController.getCategoriesWithCount);

/**
 * 获取教材与著作类别详情
 * @route GET /v1/sys/textbook-categories/category/:id
 * @group 教材与著作类别管理 - 教材与著作类别相关接口
 * @param {string} id.path.required - 类别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/category/:id', textbookCategoriesController.getTextbookCategoryDetail);

/**
 * 创建教材与著作类别
 * @route POST /v1/sys/textbook-categories/category/create
 * @group 教材与著作类别管理 - 教材与著作类别相关接口
 * @param {string} categoryAndPosition.body.required - 类别及任职名称
 * @param {string} score.body.required - 分数
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/category/create', textbookCategoriesController.createTextbookCategory);

/**
 * 更新教材与著作类别
 * @route POST /v1/sys/textbook-categories/category/update
 * @group 教材与著作类别管理 - 教材与著作类别相关接口
 * @param {string} id.body.required - 类别ID
 * @param {string} categoryAndPosition.body - 类别及任职名称
 * @param {string} score.body - 分数
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/category/update', async (req, res) => {
  const { id, ...updateData } = req.body;
  req.params = { id };
  req.body = updateData;
  await textbookCategoriesController.updateTextbookCategory(req, res);
});

/**
 * 删除教材与著作类别
 * @route POST /v1/sys/textbook-categories/category/delete
 * @group 教材与著作类别管理 - 教材与著作类别相关接口
 * @param {string} id.body.required - 类别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/category/delete', async (req, res) => {
  const { id } = req.body;
  req.params = { id };
  await textbookCategoriesController.deleteTextbookCategory(req, res);
});

module.exports = router; 