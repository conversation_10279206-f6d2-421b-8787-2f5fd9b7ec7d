// controllers/userController.js
const userModel = require('../../../models/v1/mapping/userModel');
const userLevelsModel = require('../../../models/v1/mapping/userLevelsModel');
const departmentsModel = require('../../../models/v1/mapping/departmentsModel');
const userLevelRecordsModel = require('../../../models/v1/mapping/userLevelRecordsModel');
const {body, param, validationResult} = require('express-validator');
const apiResponse = require('../../../utils/apiResponse')
const {actionRecords} = require("../../../middleware/actionLogMiddleware");
const {authMiddleware, checkApiPermission, checkUserRole} = require("../../../middleware/authMiddleware");
const {Op} = require('sequelize');
const { USER_CONFIG } = require('../../../config/index');
const { updateUserCurrentLevel } = require('../sys/userLevelRecordsController');
/**
 * 获取所有用户
 * @route POST /v1/sys/user/list
 * @group 用户管理 - 用户相关操作接口
 * @param {object} query.body - 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'createdAt', order: 'ascend'}}
 * @returns {object} 200 - {status: "success", message: "Success.", data: {result: [], current: 1, pageSize: 15, total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getAllUsers = [
    authMiddleware,
    checkApiPermission('sys:user:list'),
    async (req, res, next) => {
        try {
            // 从请求中获取分页、排序和查询参数
            let query = req.body;
            let params = query.params || {};
            let current = Number(query.pagination?.current || 1) || 1;
            let pageSize = Number(query.pagination?.pageSize || 15) || 15;
            let sortColumn = query.sort?.columnKey || 'createdAt';
            let sortOrder = query.sort?.order === 'ascend' ? 'ASC' : 'DESC';

            // 构建查询条件
            let whereConditions = {};
            for (let key in params) {
                if (params.hasOwnProperty(key)) {
                    whereConditions[key] = {[Op.like]: `%${params[key]}%`};
                }
            }

            // 查询数据库获取整张表的总记录数
            const totalCount = await userModel.count();
            // 查询数据库
            const users = await userModel.findAll({
                where: whereConditions,
                order: [[sortColumn, sortOrder]],
                offset: (current - 1) * pageSize,
                limit: pageSize,
            });
            return apiResponse.successResponseWithData(res, "Success.", {
                result: users,
                current,
                pageSize,
                total: totalCount
            })
        } catch (err) {
            next(err);
        }
    }
];


/**
 * 创建用户
 * @route POST /v1/sys/user/create
 * @group 用户管理 - 用户相关操作接口
 * @param {string} username.body.required - 用户名
 * @param {string} password.body.required - 密码
 * @param {number} roleId.body.required - 角色ID
 * @returns {object} 200 - {status: "success", message: "用户创建成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.createUser = [
    authMiddleware,
    checkApiPermission('sys:user:create'),
    body("username").notEmpty().withMessage('用户名不能为空.'),
    body("password").notEmpty().withMessage('密码不能为空.'),
    body("studentNumber").notEmpty().withMessage('学号/工号不能为空.'),
    body("roleId").notEmpty().withMessage('角色不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            // 检查用户名是否已存在
            const existingUser = await userModel.findOne({
                where: {
                    username: req.body.username
                }
            });

            if (existingUser) {
                return apiResponse.validationErrorWithData(res, "用户名已被占用，请选择其他用户名.");
            }

            // 检查学号/工号是否已存在
            const existingStudentNumber = await userModel.findOne({
                where: {
                    studentNumber: req.body.studentNumber
                }
            });

            if (existingStudentNumber) {
                return apiResponse.validationErrorWithData(res, "学号/工号已被占用，请选择其他学号/工号.");
            }

            // 如果没有提供头像，使用默认头像
            if (!req.body.avatar) {
                req.body.avatar = USER_CONFIG.defaultAvatar;
            }

            // 生成UUID作为用户ID
            const { v4: uuidv4 } = require('uuid');
            const userData = {
                id: uuidv4(),
                ...req.body
            };

            // 创建新用户
            const newUser = await userModel.create(userData);
            return apiResponse.successResponseWithData(res, "用户创建成功.", newUser.id);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取单个用户
 * @route POST /v1/sys/user/findOne
 * @group 用户管理 - 用户相关操作接口
 * @param {number} id.body.required - 用户ID
 * @returns {object} 200 - {status: "success", message: "Success.", data: {用户信息}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */

exports.findOneUser = [
    authMiddleware,
    body("id").notEmpty().withMessage('用户ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }
            const userId = req.body.id;
            // 在 attributes 中指定要返回的字段，排除不需要返回的字段
            const user = await userModel.findByPk(userId, {
                attributes: {
                    exclude: ['password']
                }
            });
            if (!user) {
                return apiResponse.notFoundResponse(res, "用户不存在.");
            }

            return apiResponse.successResponseWithData(res, "Success.", user);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 更新用户
 * @route POST /v1/sys/user/update
 * @group 用户管理 - 用户相关操作接口
 * @param {number} id.body.required - 用户ID
 * @param {string} username.body - 用户名
 * @param {string} password.body - 密码
 * @param {number} roleId.body - 角色ID
 * @returns {object} 200 - {status: "success", message: "用户更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.updateUser = [
    authMiddleware,
    checkApiPermission('sys:user:update'),
    body("id").notEmpty().withMessage('用户ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const userId = req.body.id;

            const user = await userModel.findByPk(userId);
            if (!user) {
                return apiResponse.notFoundResponse(res, "用户不存在.");
            }

            // 如果更新了学号/工号，检查是否已存在
            if (req.body.studentNumber && req.body.studentNumber !== user.studentNumber) {
                const existingStudentNumber = await userModel.findOne({
                    where: {
                        studentNumber: req.body.studentNumber,
                        id: { [Op.ne]: userId } // 排除当前用户
                    }
                });

                if (existingStudentNumber) {
                    return apiResponse.validationErrorWithData(res, "学号/工号已被占用，请选择其他学号/工号.");
                }
            }

            await user.update({...req.body});

            return apiResponse.successResponse(res, "用户更新成功.");
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 删除用户
 * @route POST /v1/sys/user/delete
 * @group 用户管理 - 用户相关操作接口
 * @param {number} id.body.required - 用户ID
 * @returns {object} 200 - {status: "success", message: "用户删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.deleteUser = [
    authMiddleware,
    checkApiPermission('sys:user:delete'),
    body("id").notEmpty().withMessage('用户ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const userId = req.body.id;

            const user = await userModel.findByPk(userId);
            if (!user) {
                return apiResponse.notFoundResponse(res, "用户不存在.");
            }

            await user.destroy();

            return apiResponse.successResponse(res, "用户删除成功.");
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 搜索用户
 * @route POST /v1/sys/user/search
 * @group 用户管理 - 用户相关操作接口
 * @param {string} keyword.body.required - 搜索关键词(用户名或学号/工号)
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [用户列表]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.searchUsers = [
    authMiddleware,
    async (req, res, next) => {
        try {
            const { keyword, pageSize = 20 } = req.body;

            // 如果没有关键词，返回前20个用户
            let whereCondition = {};
            if (keyword && keyword.trim()) {
                whereCondition = {
                    [Op.or]: [
                        { nickname: { [Op.like]: `%${keyword.trim()}%` } },
                        { studentNumber: { [Op.like]: `%${keyword.trim()}%` } },
                        { id: { [Op.like]: `%${keyword.trim()}%` } }
                    ]
                };
            }

            // 查询数据库
            const users = await userModel.findAll({
                where: whereCondition,
                attributes: {
                    exclude: ['password'] // 排除密码字段
                },
                limit: Math.min(pageSize, 50), // 限制返回数量，最多50个
                order: [['createdAt', 'DESC']] // 按创建时间倒序
            });

            return res.status(200).json({
                code: 200,
                message: "获取成功",
                data: users
            });
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取用户级别列表（用于下拉选择）
 * @route GET /v1/user/user-levels
 * @group 用户管理 - 用户相关操作接口
 * @returns {object} 200 - {status: "success", message: "获取成功", data: [用户级别列表]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getUserLevels = [
    authMiddleware,
    async (req, res, next) => {
        try {
            const userLevels = await userLevelsModel.findAll({
                where: { status: 1 },
                attributes: ['id', 'levelName', 'description', 'sort'],
                order: [['sort', 'ASC'], ['levelName', 'ASC']]
            });

            return apiResponse.successResponseWithData(res, "获取用户级别列表成功", userLevels);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取部门列表（用于下拉选择）
 * @route GET /v1/user/departments
 * @group 用户管理 - 用户相关操作接口
 * @returns {object} 200 - {status: "success", message: "获取成功", data: [部门列表]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getDepartments = [
    authMiddleware,
    async (req, res, next) => {
        try {
            const departments = await departmentsModel.findAll({
                where: { status: 1 },
                attributes: ['id', 'departmentName', 'departmentCode', 'description'],
                order: [['sort', 'ASC'], ['departmentName', 'ASC']]
            });

            return apiResponse.successResponseWithData(res, "获取部门列表成功", departments);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 获取用户职称记录
 * @route POST /v1/user/level-records
 * @group 用户管理 - 用户相关操作接口
 * @param {string} userId.body.required - 用户ID
 * @returns {object} 200 - {status: "success", message: "获取成功", data: [职称记录列表]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.getUserLevelRecords = [
    authMiddleware,
    body("userId").notEmpty().withMessage('用户ID不能为空.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const { userId } = req.body;

            const records = await userLevelRecordsModel.findAll({
                where: { userId },
                include: [
                    {
                        model: userLevelsModel,
                        as: 'level',
                        attributes: ['id', 'levelName', 'description', 'sort']
                    }
                ],
                order: [['obtainedAt', 'DESC'], ['createdAt', 'DESC']]
            });

            return apiResponse.successResponseWithData(res, "获取用户职称记录成功", records);
        } catch (err) {
            next(err);
        }
    }
];

/**
 * 保存用户职称记录
 * @route POST /v1/user/save-level-records
 * @group 用户管理 - 用户相关操作接口
 * @param {string} userId.body.required - 用户ID
 * @param {array} records.body.required - 职称记录数组
 * @returns {object} 200 - {status: "success", message: "保存成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
exports.saveUserLevelRecords = [
    authMiddleware,
    checkApiPermission('sys:user:update'),
    body("userId").notEmpty().withMessage('用户ID不能为空.'),
    body("records").isArray().withMessage('职称记录必须是数组.'),
    async (req, res, next) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return apiResponse.validationErrorWithData(res, "参数错误.", errors.array()[0].msg);
            }

            const { userId, records } = req.body;
            const createdBy = req.user.id;

            // 验证用户是否存在
            const user = await userModel.findByPk(userId);
            if (!user) {
                return apiResponse.notFoundResponse(res, "用户不存在.");
            }

            // 删除用户现有的职称记录
            await userLevelRecordsModel.destroy({
                where: { userId }
            });

            // 创建新的职称记录
            if (records && records.length > 0) {
                const { v4: uuidv4 } = require('uuid');

                for (const record of records) {
                    // 验证职称级别是否存在
                    const level = await userLevelsModel.findByPk(record.levelId);
                    if (!level) {
                        return apiResponse.validationErrorWithData(res, `职称级别不存在: ${record.levelId}`);
                    }

                    await userLevelRecordsModel.create({
                        id: uuidv4(),
                        userId,
                        levelId: record.levelId,
                        obtainedAt: new Date(record.obtainedAt),
                        remarks: record.remarks || '',
                        createdBy
                    });
                }

                // 更新用户的当前职称为最高级别（sort值最小）
                await updateUserCurrentLevel(userId);
            } else {
                // 如果没有职称记录，清空用户的当前职称
                await user.update({ userLevelId: null });
            }

            return apiResponse.successResponse(res, "保存用户职称记录成功.");
        } catch (err) {
            next(err);
        }
    }
];

