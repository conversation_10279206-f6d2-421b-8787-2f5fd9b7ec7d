/**
 * 教学工作量路由
 * 遵循项目RESTful API设计规范和权限控制规范
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');

// 导入控制器
const teachingWorkloadsController = require('../../../controllers/v1/teachingWorkloads/teachingWorkloadsController');

// 导入中间件
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建教学工作量权限中间件函数
const workloadsPermission = (action) => createModulePermission('teachingWorkloads', action);

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/teaching_workloads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'teaching-workload-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB限制
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只允许上传图片、PDF、Word和Excel文件'));
    }
  }
});

/**
 * 获取教学工作量列表
 * @route POST /v1/sys/teaching-workloads/workloads
 * @group 教学工作量管理 - 教学工作量相关接口
 * @param {string} courseName.body - 课程名称（模糊搜索）
 * @param {string} semester.body - 学期
 * @param {string} studentLevel.body - 学生类别
 * @param {string} courseType.body - 课程类型
 * @param {string} teacherId.body - 教师ID
 * @param {string} startDate.body - 开始日期
 * @param {string} endDate.body - 结束日期
 * @param {string} userId.body - 用户ID
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @param {string} range.body - 统计范围筛选
 * @param {string} reviewStatus.body - 审核状态筛选
 * @param {boolean} isExport.body - 是否导出
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list',
  authMiddleware,
  workloadsPermission('list'),
  teachingWorkloadsController.getWorkloads
);

/**
 * 获取教学工作量详情
 * @route POST /v1/teaching-workloads/detail
 * @group 教学工作量管理 - 教学工作量相关接口
 * @param {string} id.body.required - 工作量ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/detail',
  authMiddleware,
  workloadsPermission('detail'),
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await teachingWorkloadsController.getWorkloadDetail(req, res);
  }
);

/**
 * 创建教学工作量
 * @route POST /v1/teaching-workloads/create
 * @group 教学工作量管理 - 教学工作量相关接口
 * @param {string} semester.body.required - 学期
 * @param {string} courseName.body.required - 课程名称
 * @param {string} studentLevel.body.required - 学生类别
 * @param {string} courseType.body.required - 课程类型
 * @param {number} classHours.body.required - 课时数
 * @param {number} studentCount.body.required - 学生人数
 * @param {string} workloadCategoryId.body - 工作量类别ID
 * @param {number} calculatedWorkload.body - 计算工作量
 * @param {string} teacherId.body - 教师ID
 * @param {array} participants.body - 参与者列表
 * @param {string} remark.body - 备注
 * @param {array} files.body - 附件文件
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create',
  authMiddleware,
  workloadsPermission('create'),
  upload.array('files', 5),
  teachingWorkloadsController.createWorkload
);

/**
 * 更新教学工作量
 * @route POST /v1/teaching-workloads/update/:id
 * @group 教学工作量管理 - 教学工作量相关接口
 * @param {string} id.path.required - 工作量ID
 * @param {string} semester.body - 学期
 * @param {string} courseName.body - 课程名称
 * @param {string} studentLevel.body - 学生类别
 * @param {string} courseType.body - 课程类型
 * @param {number} classHours.body - 课时数
 * @param {number} studentCount.body - 学生人数
 * @param {string} workloadCategoryId.body - 工作量类别ID
 * @param {number} calculatedWorkload.body - 计算工作量
 * @param {string} teacherId.body - 教师ID
 * @param {array} participants.body - 参与者列表
 * @param {string} remark.body - 备注
 * @param {array} files.body - 附件文件
 * @param {array} deletedFileIds.body - 要删除的文件ID数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update/:id',
  authMiddleware,
  workloadsPermission('update'),
  upload.array('files', 5),
  teachingWorkloadsController.updateWorkload
);

/**
 * 删除教学工作量
 * @route DELETE /v1/teaching-workloads/delete/:id
 * @group 教学工作量管理 - 教学工作量相关接口
 * @param {string} id.path.required - 工作量ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete/:id',
  authMiddleware,
  workloadsPermission('delete'),
  teachingWorkloadsController.deleteWorkload
);

/**
 * 审核教学工作量
 * @route POST /v1/teaching-workloads/review
 * @group 教学工作量管理 - 教学工作量相关接口
 * @param {string} id.body.required - 工作量ID
 * @param {number} ifReviewer.body.required - 审核结果（1-通过，0-拒绝）
 * @param {string} reviewComment.body - 审核意见
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/review',
  authMiddleware,
  workloadsPermission('review'),
  teachingWorkloadsController.reviewWorkload
);

/**
 * 重新提交审核
 * @route POST /v1/teaching-workloads/reapply
 * @group 教学工作量管理 - 教学工作量相关接口
 * @param {string} id.body.required - 工作量ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/reapply',
  authMiddleware,
  workloadsPermission('reapply'),
  teachingWorkloadsController.reapplyWorkload
);

/**
 * 获取用户教学工作量详情
 * @route POST /v1/teaching-workloads/user-workload-details
 * @group 教学工作量管理 - 教学工作量相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围筛选，默认'all'
 * @param {string} reviewStatus.body - 审核状态筛选，默认'all'
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}, totalScore: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user-workload-details',
  authMiddleware,
  workloadsPermission('list'),
  teachingWorkloadsController.getUserWorkloadDetails
);

// ==================== 导出功能路由 ====================

// ==================== 导出功能路由 ====================
// TODO: 需要实现导出和导入功能
/*
router.post('/export',
  authMiddleware,
  workloadsPermission('export'),
  teachingWorkloadsController.exportWorkloads
);

router.post('/import',
  authMiddleware,
  workloadsPermission('import'),
  upload.single('file'),
  teachingWorkloadsController.importWorkloads
);
*/

// ==================== 统计分析路由 ====================

/**
 * 获取审核状态统计
 * @route POST /v1/sys/teaching-workloads/stats/review-status
 * @group 教学工作量管理 - 统计分析接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/review-status',
  authMiddleware,
  workloadsPermission('statistics'),
  teachingWorkloadsController.getReviewStatusStats
);

/**
 * 获取课程类型统计
 * @route POST /v1/sys/teaching-workloads/stats/course-type
 * @group 教学工作量管理 - 统计分析接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/course-type',
  authMiddleware,
  workloadsPermission('statistics'),
  teachingWorkloadsController.getCourseTypeStats
);

/**
 * 获取学期统计
 * @route POST /v1/sys/teaching-workloads/stats/semester
 * @group 教学工作量管理 - 统计分析接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/semester',
  authMiddleware,
  workloadsPermission('statistics'),
  teachingWorkloadsController.getSemesterStats
);

/**
 * 获取得分统计
 * @route POST /v1/sys/teaching-workloads/stats/score
 * @group 教学工作量管理 - 统计分析接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/score',
  authMiddleware,
  workloadsPermission('statistics'),
  teachingWorkloadsController.getScoreStats
);

/**
 * 获取教师教学工作量排名
 * @route POST /v1/sys/teaching-workloads/teacher-ranking
 * @group 教学工作量管理 - 排名接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @param {boolean} isExport.body - 是否导出，默认false
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/teacher-ranking',
  authMiddleware,
  workloadsPermission('statistics'),
  teachingWorkloadsController.getTeacherWorkloadRanking
);

/**
 * 获取教学工作量总分统计（按级别和总体）
 * @route POST /v1/sys/teaching-workloads/stats/total-score
 * @group 教学工作量管理 - 统计分析接口
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {levelStats: [], totalStats: {}, timeInterval: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/stats/total-score',
  authMiddleware,
  workloadsPermission('statistics'),
  teachingWorkloadsController.getTeachingWorkloadsTotalScore
);

/**
 * 获取用户教学工作量详情列表及得分
 * @route POST /v1/sys/teaching-workloads/user-workloads-detail
 * @group 教学工作量管理 - 用户详情接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 统计范围（in/out/all）
 * @param {string} reviewStatus.body - 审核状态（reviewed/rejected/pending/all）
 * @param {number} pageSize.body - 每页记录数，默认10
 * @param {number} pageNum.body - 当前页码，默认1
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalCount: 0, pageSize: 10, pageNum: 1, workloads: [], stats: {}, timeInterval: {}, user: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user-workloads-detail',
  authMiddleware,
  workloadsPermission('statistics'),
  teachingWorkloadsController.getUserWorkloadsDetail
);

// TODO: 需要实现其他统计分析功能
/*
router.post('/statistics',
  authMiddleware,
  workloadsPermission('statistics'),
  teachingWorkloadsController.getWorkloadsStatistics
);

router.post('/user/summary',
  authMiddleware,
  workloadsPermission('userSummary'),
  async (req, res) => {
    const { userId, ...params } = req.body;
    req.params = { userId };
    req.body = params;
    await teachingWorkloadsController.getUserWorkloadsSummary(req, res);
  }
);

router.post('/department/ranking',
  authMiddleware,
  workloadsPermission('departmentRanking'),
  teachingWorkloadsController.getDepartmentWorkloadsRanking
);
*/

module.exports = router;
