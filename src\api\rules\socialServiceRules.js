import request from '../server'

// API 路径配置
const api = {
  list: '/socialServicesRules/list',
  detail: '/socialServicesRules/detail',
  create: '/socialServicesRules/create',
  update: '/socialServicesRules/update',
  delete: '/socialServicesRules/delete',
  batchDelete: '/socialServicesRules/batch-delete',
  import: '/socialServicesRules/import',
  export: '/socialServicesRules/export'
}

/**
 * 获取社会服务规则列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getSocialServiceRules(params) {
  const { page = 1, pageSize = 10, contributionName } = params || {};
  
  // 构造参数对象
  const queryParams = {
    page,
    pageSize,
    contributionName
  };
  
  return request.get(api.list, queryParams);
}

/**
 * 获取社会服务规则详情
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function getSocialServiceRuleDetail(id) {
  return request.get(api.detail, { id });
}

/**
 * 创建社会服务规则
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function addSocialServiceRule(data) {
  return request.post(api.create, data);
}

/**
 * 更新社会服务规则
 * @param {string} id - 规则ID
 * @param {Object} data - 规则数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateSocialServiceRule(id, data) {
  return request.put(api.update, { id, ...data });
}

/**
 * 删除社会服务规则
 * @param {string} id - 规则ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteSocialServiceRule(id) {
  return request.delete(api.delete, { id });
}

/**
 * 批量删除社会服务规则
 * @param {Array} ids - 规则ID数组
 * @returns {Promise} - 返回Promise对象
 */
export function batchDeleteSocialServiceRules(ids) {
  return request.delete(api.batchDelete, { ids });
}

/**
 * 导入社会服务规则数据
 * @param {File} file - Excel文件
 * @returns {Promise} - 返回Promise对象
 */
export function importSocialServiceRules(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request.post(api.import, formData, null, 'multipart/form-data');
}

/**
 * 导出社会服务规则数据
 * @param {Object} params - 过滤参数
 * @returns {Promise} - 返回Promise对象
 */
export function exportSocialServiceRules(params) {
  return request.get(api.export, params, { responseType: 'blob' });
} 