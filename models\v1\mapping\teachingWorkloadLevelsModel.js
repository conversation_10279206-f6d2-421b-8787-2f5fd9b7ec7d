const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const teachingWorkloadLevelsModel = sequelize.define(
  'teaching_workload_level',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '类别ID'
    },
    categoryName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '类别名称'
    },
    score: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: '基础分数'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '类别描述'
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: 1,
      comment: '状态（1-启用，0-禁用）'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '更新时间'
    }
  },
  {
    sequelize,
    modelName: 'teaching_workload_level',
    tableName: 'teaching_workload_level',
    timestamps: true
  }
);

module.exports = teachingWorkloadLevelsModel;
