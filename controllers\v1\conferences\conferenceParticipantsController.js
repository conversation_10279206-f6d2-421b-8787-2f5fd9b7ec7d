const { Op } = require('sequelize');
const { getTimeIntervalByName } = require('../../../utils/others');
const conferenceModel = require('../../../models/v1/mapping/conferencesModel');
const conferenceLevelModel = require('../../../models/v1/mapping/conferencesLevelsModel');
const conferenceParticipantModel = require('../../../models/v1/mapping/conferenceParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');

/**
 * 获取用户会议参与统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserStatistics = async (req, res) => {
  try {
    const { 
      userId, 
      range = 'all',
      page = 1, 
      pageSize = 10 
    } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '请提供用户ID',
        data: null
      });
    }
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    let startDate, endDate;
    
    if (req.body.timeRange && req.body.timeRange.startDate && req.body.timeRange.endDate) {
      // 使用前端传入的自定义时间范围
      startDate = req.body.timeRange.startDate;
      endDate = req.body.timeRange.endDate;
    } else {
      // 使用系统默认的时间区间
      const timeInterval = await getTimeIntervalByName("conferences");
      startDate = timeInterval ? timeInterval.startTime : null;
      endDate = timeInterval ? timeInterval.endTime : null;
    }
    
    // 查询用户参与的会议
    const participations = await conferenceParticipantModel.findAll({
      where: { participantId: userId },
      include: [
        {
          model: conferenceModel,
          as: 'conference',
          include: [
            {
              model: conferenceLevelModel,
              as: 'level',
              attributes: ['id', 'levelName'],
              required: true,
            }
          ],
          required: true,
        }
      ]
    });
    
    // 统计会议详情
    const conferenceDetails = [];
    
    participations.forEach(participation => {
      const conference = participation.conference;
      const level = conference.level;
      
      // 检查会议是否在时间范围内
      const isInRange = startDate && endDate && conference.holdTime ? 
        isDateInTimeRange(conference.holdTime, startDate, endDate) : 
        false;
      
      // 根据查询范围筛选会议
      if ((range === 'in' && isInRange) || (range === 'out' && !isInRange) || range === 'all') {
        conferenceDetails.push({
          id: conference.id,
          conferenceName: conference.conferenceName,
          levelName: level.levelName,
          holdTime: conference.holdTime,
          role: participation.isLeader === 1 ? 'leader' : 'member',
          allocationRatio: participation.allocationRatio
        });
      }
    });
    
    // 按时间降序排序
    conferenceDetails.sort((a, b) => new Date(b.holdTime) - new Date(a.holdTime));
    
    // 计算总数
    const totalConferences = conferenceDetails.length;
    
    // 应用分页
    const pagedConferences = conferenceDetails.slice(offset, offset + limit);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        userId: userId,
        totalCount: totalConferences,
        list: pagedConferences,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: totalConferences,
          totalPages: Math.ceil(totalConferences / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取用户会议参与统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户会议参与统计失败',
      error: error.message
    });
  }
};

/**
 * 获取所有用户会议参与统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAllUsersStatistics = async (req, res) => {
  try {
    const { 
      range = 'all',
      page = 1, 
      pageSize = 10,
      sortField = 'conferenceCount',
      sortOrder = 'desc',
      nickname = ''
    } = req.body;
    
    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;
    
    // 获取时间区间
    let startDate, endDate;
    
    if (req.body.timeRange && req.body.timeRange.startDate && req.body.timeRange.endDate) {
      // 使用前端传入的自定义时间范围
      startDate = req.body.timeRange.startDate;
      endDate = req.body.timeRange.endDate;
    } else {
      // 使用系统默认的时间区间
      const timeInterval = await getTimeIntervalByName("conferences");
      startDate = timeInterval ? timeInterval.startTime : null;
      endDate = timeInterval ? timeInterval.endTime : null;
    }
    
    // 构建用户查询条件
    const userWhere = {
      status: 1 // 只获取状态正常的用户
    };
    
    // 添加昵称模糊搜索
    if (nickname) {
      userWhere.nickname = { [Op.like]: `%${nickname}%` };
    }
    
    // 查询所有用户
    const users = await userModel.findAll({
      attributes: ['id', 'username', 'nickname', 'studentNumber'],
      where: userWhere
    });
    
    // 查询所有会议参与记录
    const allParticipations = await conferenceParticipantModel.findAll({
      include: [
        {
          model: conferenceModel,
          as: 'conference',
          include: [
            {
              model: conferenceLevelModel,
              as: 'level',
              attributes: ['id', 'levelName'],
              required: true,
            }
          ],
          required: true,
        }
      ]
    });
    
    // 用户会议统计
    const userConferences = [];
    
    // 计算每个用户的会议统计
    for (const user of users) {
      const userId = user.id;
      let conferenceCount = 0;
      let leaderConferenceCount = 0;
      
      // 过滤出该用户的参与记录
      const userParticipations = allParticipations.filter(p => p.participantId === userId);
      
      // 统计每个用户的会议数
      userParticipations.forEach(participation => {
        const conference = participation.conference;
        
        // 检查会议是否在时间范围内
        const isInRange = startDate && endDate && conference.holdTime ? 
          isDateInTimeRange(conference.holdTime, startDate, endDate) : 
          false;
        
        // 根据查询范围筛选会议
        if ((range === 'in' && isInRange) || (range === 'out' && !isInRange) || range === 'all') {
          conferenceCount++;
          
          if (participation.isLeader === 1) {
            leaderConferenceCount++;
          }
        }
      });
      
      // 只添加有参与会议的用户
      if (conferenceCount > 0) {
        userConferences.push({
          userId: userId,
          userName: user.nickname || user.username,
          studentNumber: user.studentNumber || '',
          conferenceCount: conferenceCount,
          leaderConferenceCount: leaderConferenceCount
        });
      }
    }
    
    // 根据排序字段和顺序排序
    userConferences.sort((a, b) => {
      if (sortField === 'conferenceCount') {
        return sortOrder === 'desc' ? b.conferenceCount - a.conferenceCount : a.conferenceCount - b.conferenceCount;
      } else if (sortField === 'leaderConferenceCount') {
        return sortOrder === 'desc' ? b.leaderConferenceCount - a.leaderConferenceCount : a.leaderConferenceCount - b.leaderConferenceCount;
      }
      return 0;
    });
    
    // 计算总用户数
    const totalUsers = userConferences.length;
    
    // 应用分页
    const pagedUsers = userConferences.slice(offset, offset + limit);
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: pagedUsers,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: totalUsers,
          totalPages: Math.ceil(totalUsers / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取所有用户会议参与统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取所有用户会议参与统计失败',
      error: error.message
    });
  }
};

/**
 * 判断日期是否在时间范围内
 * @param {string} dateStr - 日期字符串
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isDateInTimeRange(dateStr, startDate, endDate) {
  if (!dateStr || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const dateObj = new Date(dateStr);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 日期必须在开始日期和结束日期之间
  return dateObj >= startDateObj && dateObj <= endDateObj;
} 