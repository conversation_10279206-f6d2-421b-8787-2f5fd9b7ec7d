const socialServiceModel = require('../../../models/v1/mapping/socialServicesModel');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

/**
 * 获取社会服务列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getServices = async (req, res) => {
  try {
    console.log('🔍 获取社会服务列表 - 请求参数:', req.query);
    
    // 获取查询参数
    const { name, type, target, usernameList, startDate, endDate, status, page, pageSize, userId } = req.query;
    // 确保 page 和 pageSize 是有效的数字，否则使用默认值
    const pageNum = page ? parseInt(page) : 1;
    const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

    // 如果 page 或 pageSize 是无效数字，返回默认值
    const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
    const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;
    
    console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

    // 构建查询条件
    const where = {};
    if (name) where.name = { [Op.like]: `%${name}%` };
    if (type) where.type = type;
    if (target) where.target = { [Op.like]: `%${target}%` };
    if (usernameList) where.usernameList = { [Op.like]: `%${usernameList}%` };
    if (status !== undefined && status !== '') where.status = status;
    
    // 按用户ID筛选
    if (userId) {
      console.log('👤 按用户ID筛选:', userId);
      // userIdList字段是逗号分隔的字符串，需要匹配userId是否在其中
      // 可能的格式有：'id1,id2,id3' 或 'id1' 或 'id1,'等
      const likePatterns = [
        `${userId}`,       // 精确匹配
        `${userId},`,      // 在开头
        `,${userId},`,     // 在中间
        `,${userId}`       // 在结尾
      ];
      
      // 使用OR条件组合多个匹配模式
      where[Op.or] = likePatterns.map(pattern => ({
        userIdList: { [Op.like]: `%${pattern}%` }
      }));
      
      console.log('🔍 用户ID查询条件:', JSON.stringify(where[Op.or]));
    }
    
    // 日期范围查询
    if (startDate && endDate) {
      where.startDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
      console.log('📅 日期范围查询:', { startDate, endDate });
    } else if (startDate) {
      where.startDate = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.endDate = {
        [Op.lte]: new Date(endDate)
      };
    }
    
    console.log('🔍 最终查询条件:', JSON.stringify(where));

    // 分页查询
    const offset = (validPage - 1) * validPageSize;
    try {
      console.log('📚 执行数据库查询...');
      const { count, rows } = await socialServiceModel.findAndCountAll({
        where,
        offset,
        limit: validPageSize,
        order: [['createdAt', 'DESC']] // 默认按时间倒序
      });
      
      console.log(`✅ 查询成功: 共${count}条记录`);
      
      const totalPages = Math.ceil(count / validPageSize);
      
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          total: count,
          page: validPage,
          pageSize: validPageSize,
          totalPages,
          list: rows
        }
      });
    } catch (dbError) {
      console.error('❌ 数据库查询失败:', dbError);
      return res.status(500).json({
        code: 500,
        message: `数据库查询失败: ${dbError.message}`,
        data: null
      });
    }
  } catch (error) {
    console.error('❌ 获取社会服务列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取社会服务列表失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取社会服务详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getServiceById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const service = await socialServiceModel.findByPk(id);
    if (!service) {
      return res.status(404).json({
        code: 404,
        message: '社会服务记录不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: service
    });
  } catch (error) {
    console.error('获取社会服务详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取社会服务详情失败',
      data: null
    });
  }
};

/**
 * 创建社会服务记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createService = async (req, res) => {
  try {
    const { name, type, target, userIdList, usernameList, startDate, endDate, content, result, score, status } = req.body;
    
    // 校验必填参数
    if (!name || !type || !target || !startDate || !endDate) {
      return res.status(400).json({
        code: 400,
        message: '服务名称、类型、服务对象、开始时间和结束时间不能为空',
        data: null
      });
    }
    
    // 创建社会服务记录
    const service = await socialServiceModel.create({
      id: uuidv4(),
      name,
      type,
      target,
      userIdList: userIdList || '',
      usernameList: usernameList || '',
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      content: content || '',
      result: result || '',
      score: score || 0,
      status: status !== undefined ? status : 1
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: service
    });
  } catch (error) {
    console.error('创建社会服务记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建社会服务记录失败',
      data: null
    });
  }
};

/**
 * 更新社会服务记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateService = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type, target, userIdList, usernameList, startDate, endDate, content, result, score, status } = req.body;
    
    // 校验必填参数
    if (!name || !type || !target || !startDate || !endDate) {
      return res.status(400).json({
        code: 400,
        message: '服务名称、类型、服务对象、开始时间和结束时间不能为空',
        data: null
      });
    }
    
    // 查找社会服务记录
    const service = await socialServiceModel.findByPk(id);
    if (!service) {
      return res.status(404).json({
        code: 404,
        message: '社会服务记录不存在',
        data: null
      });
    }
    
    // 更新社会服务记录
    await service.update({
      name,
      type,
      target,
      userIdList: userIdList || service.userIdList,
      usernameList: usernameList || service.usernameList,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      content: content || service.content,
      result: result || service.result,
      score: score || service.score,
      status: status !== undefined ? status : service.status
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: service
    });
  } catch (error) {
    console.error('更新社会服务记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新社会服务记录失败',
      data: null
    });
  }
};

/**
 * 删除社会服务记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteService = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找社会服务记录
    const service = await socialServiceModel.findByPk(id);
    if (!service) {
      return res.status(404).json({
        code: 404,
        message: '社会服务记录不存在',
        data: null
      });
    }
    
    // 删除社会服务记录
    await service.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除社会服务记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除社会服务记录失败',
      data: null
    });
  }
};

/**
 * 导入社会服务数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importServices = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请上传文件',
        data: null
      });
    }
    
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = xlsx.utils.sheet_to_json(worksheet);
    
    if (data.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '文件内容为空',
        data: null
      });
    }
    
    // 验证并处理数据
    const services = [];
    const errors = [];
    const requiredFields = ['服务名称', '服务类型', '服务对象', '参与用户', '开始时间', '结束时间'];
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNum = i + 2; // Excel 行号从1开始，且有表头
      
      // 检查必填字段
      const missingFields = requiredFields.filter(field => !row[field] && row[field] !== 0);
      if (missingFields.length > 0) {
        errors.push(`第${rowNum}行: ${missingFields.join(', ')}不能为空`);
        continue;
      }
      
      services.push({
        id: uuidv4(),
        name: row['服务名称'],
        type: row['服务类型'],
        target: row['服务对象'],
        userIdList: row['参与用户ID'] || '', 
        usernameList: row['参与用户'] || '',
        startDate: new Date(row['开始时间']),
        endDate: new Date(row['结束时间']),
        content: row['服务内容'] || '',
        result: row['服务成果'] || '',
        score: row['得分'] || 0,
        status: row['状态'] !== undefined ? row['状态'] : 1
      });
    }
    
    // 批量创建社会服务记录
    if (services.length > 0) {
      await socialServiceModel.bulkCreate(services);
    }
    
    // 删除临时文件
    fs.unlinkSync(filePath);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: {
        total: data.length,
        success: services.length,
        failed: errors.length,
        errors
      }
    });
  } catch (error) {
    console.error('导入社会服务记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导入社会服务记录失败',
      data: null
    });
  }
};

/**
 * 导出社会服务数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportServices = async (req, res) => {
  try {
    const { name, type, target, usernameList, startDate, endDate, status } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) where.name = { [Op.like]: `%${name}%` };
    if (type) where.type = type;
    if (target) where.target = { [Op.like]: `%${target}%` };
    if (usernameList) where.usernameList = { [Op.like]: `%${usernameList}%` };
    if (status !== undefined) where.status = status;
    
    // 日期范围查询
    if (startDate && endDate) {
      where.startDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      where.startDate = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.endDate = {
        [Op.lte]: new Date(endDate)
      };
    }
    
    // 查询数据
    const services = await socialServiceModel.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });
    
    // 转换为Excel格式
    const excelData = services.map(service => ({
      '服务名称': service.name,
      '服务类型': service.type,
      '服务对象': service.target,
      '参与用户': service.usernameList,
      '开始时间': service.startDate.toLocaleDateString(),
      '结束时间': service.endDate.toLocaleDateString(),
      '服务内容': service.content,
      '服务成果': service.result,
      '得分': service.score,
      '状态': service.status === 1 ? '正常' : '已删除',
      '创建时间': service.createdAt.toLocaleString(),
      '更新时间': service.updatedAt.toLocaleString()
    }));
    
    // 创建工作簿和工作表
    const worksheet = xlsx.utils.json_to_sheet(excelData);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, '社会服务数据');
    
    // 生成Excel文件
    const fileName = `social_services_${Date.now()}.xlsx`;
    const filePath = path.join(__dirname, '..', '..', 'uploads', fileName);
    
    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 写入文件
    xlsx.writeFile(workbook, filePath);
    
    // 发送文件
    return res.download(filePath, fileName, (err) => {
      if (err) {
        console.error('文件下载失败:', err);
        return res.status(500).json({
          code: 500,
          message: '文件下载失败',
          data: null
        });
      }
      
      // 下载完成后删除临时文件
      fs.unlinkSync(filePath);
    });
  } catch (error) {
    console.error('导出社会服务记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出社会服务记录失败',
      data: null
    });
  }
};

/**
 * 获取服务类型分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getServiceTypeDistribution = async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    console.log('🔍 获取服务类型分布 - 请求参数:', req.query);

    // 构建查询条件
    const where = { status: 1 };
    
    // 按用户筛选
    if (userId) {
      console.log('👤 按用户ID筛选:', userId);
      // userIdList字段是逗号分隔的字符串，需要匹配userId是否在其中
      // 可能的格式有：'id1,id2,id3' 或 'id1' 或 'id1,'等
      const likePatterns = [
        `${userId}`,       // 精确匹配
        `${userId},`,      // 在开头
        `,${userId},`,     // 在中间
        `,${userId}`       // 在结尾
      ];
      
      // 使用OR条件组合多个匹配模式
      where[Op.or] = likePatterns.map(pattern => ({
        userIdList: { [Op.like]: `%${pattern}%` }
      }));
      
      console.log('🔍 用户ID查询条件:', JSON.stringify(where[Op.or]));
    }
    
    // 日期范围查询
    if (startDate && endDate) {
      where.startDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    console.log('🔍 最终查询条件:', JSON.stringify(where));
    
    // 查询数据
    const services = await socialServiceModel.findAll({ where });
    console.log(`✅ 查询成功: 共${services.length}条记录`);
    
    // 计算各类型数量
    const typeDistribution = services.reduce((result, service) => {
      const type = service.type;
      if (!result.find(item => item.type === type)) {
        result.push({ type, count: 1 });
      } else {
        const index = result.findIndex(item => item.type === type);
        result[index].count += 1;
      }
      return result;
    }, []);

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: typeDistribution
    });
  } catch (error) {
    console.error('❌ 获取服务类型分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取服务类型分布失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取学术会议参与分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getConferenceDistribution = async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    console.log('🔍 获取学术会议参与分布 - 请求参数:', req.query);

    // 构建查询条件
    const where = { 
      status: 1,
      type: 'conference'
    };
    
    // 按用户筛选
    if (userId) {
      console.log('👤 按用户ID筛选:', userId);
      // userIdList字段是逗号分隔的字符串，需要匹配userId是否在其中
      // 可能的格式有：'id1,id2,id3' 或 'id1' 或 'id1,'等
      const likePatterns = [
        `${userId}`,       // 精确匹配
        `${userId},`,      // 在开头
        `,${userId},`,     // 在中间
        `,${userId}`       // 在结尾
      ];
      
      // 使用OR条件组合多个匹配模式
      where[Op.or] = likePatterns.map(pattern => ({
        userIdList: { [Op.like]: `%${pattern}%` }
      }));
      
      console.log('🔍 用户ID查询条件:', JSON.stringify(where[Op.or]));
    }
    
    // 日期范围查询
    if (startDate && endDate) {
      where.startDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    console.log('🔍 最终查询条件:', JSON.stringify(where));
    
    // 查询数据
    const conferences = await socialServiceModel.findAll({ where });
    console.log(`✅ 查询成功: 共${conferences.length}条记录`);
    
    // 统计不同级别和报告类型的数量
    const distribution = {
      internationalOral: 0,
      internationalPoster: 0,
      nationalOral: 0,
      nationalPoster: 0,
      provincialOral: 0,
      provincialPoster: 0
    };

    conferences.forEach(conference => {
      if (conference.level === 'international') {
        if (conference.reportType === 'oral') {
          distribution.internationalOral += 1;
        } else if (conference.reportType === 'poster') {
          distribution.internationalPoster += 1;
        }
      } else if (conference.level === 'national') {
        if (conference.reportType === 'oral') {
          distribution.nationalOral += 1;
        } else if (conference.reportType === 'poster') {
          distribution.nationalPoster += 1;
        }
      } else if (conference.level === 'provincial') {
        if (conference.reportType === 'oral') {
          distribution.provincialOral += 1;
        } else if (conference.reportType === 'poster') {
          distribution.provincialPoster += 1;
        }
      }
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: distribution
    });
  } catch (error) {
    console.error('❌ 获取学术会议参与分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取学术会议参与分布失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取学生竞赛指导分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCompetitionDistribution = async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    console.log('🔍 获取学生竞赛指导分布 - 请求参数:', req.query);

    // 构建查询条件
    const where = { 
      status: 1,
      type: 'competition'
    };
    
    // 按用户筛选
    if (userId) {
      console.log('👤 按用户ID筛选:', userId);
      // userIdList字段是逗号分隔的字符串，需要匹配userId是否在其中
      // 可能的格式有：'id1,id2,id3' 或 'id1' 或 'id1,'等
      const likePatterns = [
        `${userId}`,       // 精确匹配
        `${userId},`,      // 在开头
        `,${userId},`,     // 在中间
        `,${userId}`       // 在结尾
      ];
      
      // 使用OR条件组合多个匹配模式
      where[Op.or] = likePatterns.map(pattern => ({
        userIdList: { [Op.like]: `%${pattern}%` }
      }));
      
      console.log('🔍 用户ID查询条件:', JSON.stringify(where[Op.or]));
    }
    
    // 日期范围查询
    if (startDate && endDate) {
      where.startDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    console.log('🔍 最终查询条件:', JSON.stringify(where));
    
    // 查询数据
    const competitions = await socialServiceModel.findAll({ where });
    console.log(`✅ 查询成功: 共${competitions.length}条记录`);
    
    // 统计不同级别的竞赛数量
    const levels = ['national', 'provincial', 'school'];
    const distribution = levels.map(level => {
      return {
        level: level === 'national' ? '国家级' : level === 'provincial' ? '省级' : '校院级',
        count: competitions.filter(comp => comp.competitionLevel === level).length
      };
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: distribution
    });
  } catch (error) {
    console.error('❌ 获取学生竞赛指导分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取学生竞赛指导分布失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取教师得分排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherRanking = async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    console.log('🔍 获取教师得分排名 - 请求参数:', req.query);

    // 构建查询条件
    const where = { status: 1 };
    
    // 日期范围查询
    if (startDate && endDate) {
      where.startDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }

    // 如果是个人模式，返回历年得分趋势，否则返回教师排名
    if (userId) {
      console.log('👤 按用户ID筛选:', userId);
      // userIdList字段是逗号分隔的字符串，需要匹配userId是否在其中
      // 可能的格式有：'id1,id2,id3' 或 'id1' 或 'id1,'等
      const likePatterns = [
        `${userId}`,       // 精确匹配
        `${userId},`,      // 在开头
        `,${userId},`,     // 在中间
        `,${userId}`       // 在结尾
      ];
      
      // 使用OR条件组合多个匹配模式
      where[Op.or] = likePatterns.map(pattern => ({
        userIdList: { [Op.like]: `%${pattern}%` }
      }));
      
      console.log('🔍 用户ID查询条件:', JSON.stringify(where[Op.or]));
      
      // 查询5年内的数据
      const currentYear = new Date().getFullYear();
      const fiveYearsAgo = currentYear - 4;
      
      // 添加日期范围
      const dateWhere = {
        startDate: {
          [Op.gte]: new Date(`${fiveYearsAgo}-01-01`)
        }
      };
      
      // 合并查询条件
      Object.assign(where, dateWhere);
      
      console.log('🔍 最终查询条件:', JSON.stringify(where));
      
      // 查询数据
      const services = await socialServiceModel.findAll({ where });
      console.log(`✅ 查询成功: 共${services.length}条记录`);
      
      // 按年度分组并计算总分
      const yearScores = {};
      
      // 初始化5年数据
      for (let year = fiveYearsAgo; year <= currentYear; year++) {
        yearScores[year] = 0;
      }
      
      // 累计每年得分
      services.forEach(service => {
        const year = new Date(service.startDate).getFullYear();
        if (yearScores[year] !== undefined) {
          yearScores[year] += parseFloat(service.score) || 0;
        }
      });
      
      // 转换为数组格式
      const yearlyData = Object.keys(yearScores).map(year => ({
        year,
        score: parseFloat(yearScores[year].toFixed(2))
      }));
      
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: yearlyData
      });
    } else {
      // 全部教师排名模式
      console.log('🔍 全部教师排名模式查询条件:', JSON.stringify(where));
      
      // 查询数据
      const services = await socialServiceModel.findAll({ where });
      console.log(`✅ 查询成功: 共${services.length}条记录`);
      
      // 提取用户名列表
      const teacherNames = new Set();
      services.forEach(service => {
        if (service.usernameList) {
          // 解析逗号分隔的用户名列表
          const names = service.usernameList.split(',');
          names.forEach(name => {
            if (name && name.trim()) {
              teacherNames.add(name.trim());
            }
          });
        }
      });
      
      // 按教师分组并计算总分
      const teacherScores = {};
      
      // 初始化教师得分
      teacherNames.forEach(teacher => {
        teacherScores[teacher] = 0;
      });
      
      // 计算每个教师的总分
      services.forEach(service => {
        if (service.usernameList && service.score) {
          const names = service.usernameList.split(',');
          const score = parseFloat(service.score) || 0;
          // 如果有多个教师，平分得分
          const sharedScore = score / names.length;
          
          names.forEach(name => {
            if (name && name.trim() && teacherScores[name.trim()] !== undefined) {
              teacherScores[name.trim()] += sharedScore;
            }
          });
        }
      });
      
      // 转换为排序数组
      const rankingData = Object.keys(teacherScores).map(teacher => ({
        teacherName: teacher,
        score: parseFloat(teacherScores[teacher].toFixed(2))
      }));
      
      // 按分数降序排序
      rankingData.sort((a, b) => b.score - a.score);
      
      // 只返回前10名
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: rankingData.slice(0, 10)
      });
    }
  } catch (error) {
    console.error('❌ 获取教师得分排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教师得分排名失败: ${error.message}`,
      data: null
    });
  }
}; 