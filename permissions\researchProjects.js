/**
 * 科研项目模块权限配置
 * 简化版 - 角色验证 + 教师数据自动填充
 */
module.exports = {
  // 基本权限配置
  list: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  create: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1']
    // 创建时不限制教师，允许自由创建
  },
  
  update: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  delete: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  review: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以审核，无需教师访问控制
  },
  
  import: {
    roles: ['ADMIN-LV2', 'SUPER']
    // 只有管理员可以导入，无需教师访问控制
  },
  
  export: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 详情相关权限
  detail: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 统计相关权限
  levelDistribution: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  typeDistribution: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  timeDistribution: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  scoreDistribution: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  leaderRanking: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  userTotalScore: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  allUsersTotalScore: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  userProjectDetails: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  projectsTotalScore: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  userProjectsDetail: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },
  
  // 重新提交审核权限
  reapply: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  },

  reviewStatusOverview: {
    roles: ['ADMIN-LV2', 'SUPER', 'TEACHER-LV1'],
    teacherAccess: {
      enforce: true,
      params: ['userId']
    }
  }
};
