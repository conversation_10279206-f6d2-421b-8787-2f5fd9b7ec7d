<template>
  <div class="app-container">
    <!-- 错误信息展示 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
    
    <a-card title="高水平论文规则管理">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal">
            <template #icon><plus-outlined /></template>
            新增规则
          </a-button>
        </a-space>
      </template>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="showEditModal(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这条规则吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
          <template v-else-if="column.key === 'description'">
            <a-tooltip v-if="record.description && record.description.length > 20">
              <template #title>{{ record.description }}</template>
              <span>{{ record.description.substring(0, 20) }}...</span>
            </a-tooltip>
            <span v-else>{{ record.description || '--' }}</span>
          </template>
          <template v-else-if="column.key === 'createdAt'">
            <span>{{ formatDate(record.createdAt) }}</span>
          </template>
        </template>
      </a-table>

      <!-- 新增/编辑模态框 -->
      <a-modal
        :title="modalTitle"
        :visible="modalVisible"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="confirmLoading"
        width="800px"
      >
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="论文级别" name="paperLevel">
                <a-input 
                  v-model:value="formState.paperLevel" 
                  :maxLength="255"
                  placeholder="请输入论文级别" 
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="基础核算分数" name="baseScore">
                <a-input-number
                  v-model:value="formState.baseScore"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%"
                  placeholder="请输入基础核算分数"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="详细描述" name="description">
            <a-textarea
              v-model:value="formState.description"
              :rows="4"
              placeholder="请输入论文级别的详细描述"
            />
          </a-form-item>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="非本院研究生第一作者系数" name="nonDepartmentAuthorCoefficient">
                <a-input-number
                  v-model:value="formState.nonDepartmentAuthorCoefficient"
                  :min="0"
                  :max="1"
                  :precision="2"
                  :step="0.1"
                  style="width: 100%"
                  placeholder="请输入非本院研究生第一作者系数"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="共同第一作者排名系数" name="coFirstAuthorRankCoefficient">
                <a-input 
                  v-model:value="formState.coFirstAuthorRankCoefficient" 
                  :maxLength="255"
                  placeholder="请输入共同第一作者排名系数（如 100%, 1/2, 1/3）" 
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="导师最多填写论文数" name="maxPapersPerMentor">
                <a-input-number
                  v-model:value="formState.maxPapersPerMentor"
                  :min="1"
                  :precision="0"
                  style="width: 100%"
                  placeholder="请输入每个导师最多填写的代表性论文数量"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="非本院研究生第一作者限制" name="nonDepartmentAuthorLimit">
                <a-input-number
                  v-model:value="formState.nonDepartmentAuthorLimit"
                  :min="1"
                  :precision="0"
                  style="width: 100%"
                  placeholder="请输入非本院研究生第一作者的文章数量限制"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { getHighLevelPapersRules, getHighLevelPapersRuleDetail, addHighLevelPapersRule, updateHighLevelPapersRule, deleteHighLevelPapersRule } from "@/api/rules/highLevelPapersRules";

// 错误信息
const errorMessage = ref('');

// 表格列定义
const columns = [
  {
    title: '论文级别',
    dataIndex: 'paperLevel',
    key: 'paperLevel',
    width: 150,
  },
  {
    title: '基础核算分数',
    dataIndex: 'baseScore',
    key: 'baseScore',
    width: 120,
  },
  {
    title: '详细描述',
    key: 'description',
    width: 200,
  },
  {
    title: '非本院研究生第一作者系数',
    dataIndex: 'nonDepartmentAuthorCoefficient',
    key: 'nonDepartmentAuthorCoefficient',
    width: 180,
  },
  {
    title: '共同第一作者排名系数',
    dataIndex: 'coFirstAuthorRankCoefficient',
    key: 'coFirstAuthorRankCoefficient',
    width: 180,
  },
  {
    title: '导师最多填写论文数',
    dataIndex: 'maxPapersPerMentor',
    key: 'maxPapersPerMentor',
    width: 150,
  },
  {
    title: '非本院研究生第一作者限制',
    dataIndex: 'nonDepartmentAuthorLimit',
    key: 'nonDepartmentAuthorLimit',
    width: 180,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
];

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 搜索表单
const searchForm = reactive({
  paperLevel: '',
});

// 表单状态
const formState = reactive({
  id: '',
  paperLevel: '',
  baseScore: undefined,
  description: '',
  nonDepartmentAuthorCoefficient: 0.9,
  coFirstAuthorRankCoefficient: '',
  maxPapersPerMentor: 5,
  nonDepartmentAuthorLimit: 1,
});

// 表单验证规则
const rules = {
  paperLevel: [{ required: true, message: '请输入论文级别', trigger: 'blur' }],
  baseScore: [{ required: true, message: '请输入基础核算分数', trigger: 'blur' }],
  description: [{ required: true, message: '请输入详细描述', trigger: 'blur' }],
  nonDepartmentAuthorCoefficient: [{ required: true, message: '请输入非本院研究生第一作者系数', trigger: 'blur' }],
  coFirstAuthorRankCoefficient: [{ required: true, message: '请输入共同第一作者排名系数', trigger: 'blur' }],
  maxPapersPerMentor: [{ required: true, message: '请输入导师最多填写论文数', trigger: 'blur' }],
  nonDepartmentAuthorLimit: [{ required: true, message: '请输入非本院研究生第一作者限制', trigger: 'blur' }],
};

// 模态框状态
const modalVisible = ref(false);
const modalTitle = ref('新增规则');
const confirmLoading = ref(false);
const formRef = ref(null);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  errorMessage.value = '';
  
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      paperLevel: searchForm.paperLevel,
    };
    
    const response = await getHighLevelPapersRules(params);
    
    if (response && response.code === 200) {
      dataSource.value = response.data.list || [];
      pagination.total = response.data.total || 0;
    } else {
      message.error(response?.message || '获取数据失败');
      errorMessage.value = response?.message || '获取数据失败，请稍后重试';
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败：' + (error.message || '未知错误'));
    errorMessage.value = '获取数据失败：' + (error.message || '未知错误');
  } finally {
    loading.value = false;
  }
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

// 显示新增模态框
const showAddModal = () => {
  modalTitle.value = '新增规则';
  Object.keys(formState).forEach(key => {
    formState[key] = key === 'id' ? '' : (key === 'nonDepartmentAuthorCoefficient' ? 0.9 : (key === 'maxPapersPerMentor' ? 5 : (key === 'nonDepartmentAuthorLimit' ? 1 : '')));
  });
  modalVisible.value = true;
};

// 显示编辑模态框
const showEditModal = async (record) => {
  modalTitle.value = '编辑规则';
  try {
    const response = await getHighLevelPapersRuleDetail(record.id);
    if (response.code === 200) {
      formState.id = record.id;
      formState.paperLevel = response.data.paperLevel;
      formState.baseScore = response.data.baseScore;
      formState.description = response.data.description || '';
      formState.nonDepartmentAuthorCoefficient = response.data.nonDepartmentAuthorCoefficient || 0.9;
      formState.coFirstAuthorRankCoefficient = response.data.coFirstAuthorRankCoefficient || '';
      formState.maxPapersPerMentor = response.data.maxPapersPerMentor || 5;
      formState.nonDepartmentAuthorLimit = response.data.nonDepartmentAuthorLimit || 1;
      modalVisible.value = true;
    } else {
      message.error(response.message || '获取规则详情失败');
    }
  } catch (error) {
    console.error('获取规则详情失败:', error);
    message.error('获取规则详情失败');
  }
};

// 模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate();
    confirmLoading.value = true;
    
    const formData = {
      paperLevel: formState.paperLevel,
      baseScore: formState.baseScore,
      description: formState.description,
      nonDepartmentAuthorCoefficient: formState.nonDepartmentAuthorCoefficient,
      coFirstAuthorRankCoefficient: formState.coFirstAuthorRankCoefficient,
      maxPapersPerMentor: formState.maxPapersPerMentor,
      nonDepartmentAuthorLimit: formState.nonDepartmentAuthorLimit,
    };
    
    let response;
    if (formState.id) {
      response = await updateHighLevelPapersRule(formState.id, formData);
    } else {
      response = await addHighLevelPapersRule(formData);
    }
    
    if (response.code === 200) {
      message.success('保存成功');
      modalVisible.value = false;
      fetchData();
    } else {
      message.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    confirmLoading.value = false;
  }
};

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 删除记录
const handleDelete = async (record) => {
  try {
    const response = await deleteHighLevelPapersRule(record.id);
    if (response.code === 200) {
      message.success('删除成功');
      fetchData();
    } else {
      message.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '--';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 页面加载时初始化
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.app-container {
  padding: 24px;
}
.search-form-wrapper {
  background-color: #f8f8f8;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
}
</style> 