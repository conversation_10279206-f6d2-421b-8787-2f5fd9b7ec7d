import service from '../server'

export const rolesList = (data) => {
    return service.post('/role/list', data)
}
export const rolesCreate = (data) => {
    return service.post('/role/create', data)
}
export const rolesUpdate = (data) => {
    return service.post('/role/update', data)
}
export const rolesDelete = (data) => {
    return service.post('/role/delete', data)
}
export const rolesFindOne = (data) => {
    return service.post('/role/findOne', data)
}

