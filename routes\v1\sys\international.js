const express = require('express');
const router = express.Router();
const exchangeController = require('../../../controllers/v1/sys/internationalExchangeController');
const {
  validateCreateExchange,
  validateUpdateExchange,
  validateDeleteExchange,
  validatePersonalStats
} = require('../../../middleware/internationalExchangeValidatorMiddleware');

// 添加路由中间件，记录路由匹配情况
const logRoute = (req, res, next) => {
  console.log(`[路由] 匹配到路由: ${req.method} ${req.baseUrl}${req.path}`);
  console.log(`[路由] 请求参数:`, req.params);
  console.log(`[路由] 查询参数:`, req.query);
  next();
};

/**
 * 获取个人国际交流统计数据
 * @route GET /v1/sys/international/personal-stats
 * @group 国际交流管理 - 系统国际交流相关接口
 * @param {string} userId.query - 用户ID（可选，默认为当前登录用户）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {exchangeCount, totalScore, totalDays, typeDistribution, countryDistribution, timeDistribution, scoreTrend, recentExchanges}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/personal-stats', logRoute, validatePersonalStats, exchangeController.getPersonalExchangeStats);

/**
 * 导出国际交流数据
 * @route GET /v1/sys/international/export
 * @group 国际交流管理 - 系统国际交流相关接口
 * @param {string} name.query - 交流项目名称（模糊搜索）
 * @param {string} type.query - 交流类型
 * @param {string} country.query - 国家/地区
 * @param {string} startDate.query - 开始日期
 * @param {string} endDate.query - 结束日期
 * @returns {file} 200 - Excel文件
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/export', logRoute, exchangeController.exportExchanges);

/**
 * 导入国际交流数据
 * @route POST /v1/sys/international/import
 * @group 国际交流管理 - 系统国际交流相关接口
 * @param {file} file.body.required - Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/import', logRoute, exchangeController.importExchanges);

/**
 * 获取国际交流详情
 * @route GET /v1/sys/international/:id
 * @group 国际交流管理 - 系统国际交流相关接口
 * @param {string} id.path.required - 交流项目ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {交流项目详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/:id', logRoute, exchangeController.getExchangeById);

/**
 * 更新国际交流
 * @route PUT /v1/sys/international/:id
 * @group 国际交流管理 - 系统国际交流相关接口
 * @param {string} id.path.required - 交流项目ID
 * @param {array} usernameList.body.required - 参与用户名称列表
 * @param {array} userIdList.body - 用户ID列表
 * @param {string} name.body - 交流项目名称（如不提供将自动生成）
 * @param {string} type.body.required - 交流类型
 * @param {string} country.body.required - 国家/地区
 * @param {string} institution.body.required - 交流机构
 * @param {string} startDate.body.required - 开始日期
 * @param {string} endDate.body.required - 结束日期
 * @param {string} content.body.required - 交流内容
 * @param {string} result.body - 交流成果
 * @param {number} score.body.required - 评分
 * @param {string} remark.body - 备注
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/:id', logRoute, validateUpdateExchange, exchangeController.updateExchange);

/**
 * 删除国际交流
 * @route DELETE /v1/sys/international/:id
 * @group 国际交流管理 - 系统国际交流相关接口
 * @param {string} id.path.required - 交流项目ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/:id', logRoute, validateDeleteExchange, exchangeController.deleteExchange);

/**
 * 创建国际交流
 * @route POST /v1/sys/international
 * @group 国际交流管理 - 系统国际交流相关接口
 * @param {array} usernameList.body.required - 参与用户名称列表
 * @param {array} userIdList.body - 用户ID列表
 * @param {string} name.body - 交流项目名称（如不提供将自动生成）
 * @param {string} type.body.required - 交流类型
 * @param {string} country.body.required - 国家/地区
 * @param {string} institution.body.required - 交流机构
 * @param {string} startDate.body.required - 开始日期
 * @param {string} endDate.body.required - 结束日期
 * @param {string} content.body.required - 交流内容
 * @param {string} result.body - 交流成果
 * @param {number} score.body.required - 评分
 * @param {string} remark.body - 备注
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "交流项目ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/', logRoute, validateCreateExchange, exchangeController.createExchange);

/**
 * 获取国际交流列表
 * @route GET /v1/sys/international
 * @group 国际交流管理 - 系统国际交流相关接口
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @param {string} usernameList.query - 参与用户名称（模糊搜索）
 * @param {string} type.query - 交流类型
 * @param {string} country.query - 国家/地区
 * @param {string} institution.query - 交流机构
 * @param {string} startDate.query - 开始日期
 * @param {string} endDate.query - 结束日期
 * @param {boolean} userOnly.query - 是否只查询当前用户的记录
 * @param {string} userId.query - 用户ID（当userOnly为true时使用）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {page: 1, pageSize: 10, total: 0, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/', logRoute, exchangeController.getExchanges);

/**
 * 测试查询API - 仅用于开发环境
 * @route GET /v1/sys/international/test-query
 * @group 国际交流管理 - 系统国际交流相关接口
 * @returns {object} 200 - 返回查询结果
 * @security JWT
 */
router.get('/test-query', logRoute, exchangeController.testQuery);

module.exports = router; 