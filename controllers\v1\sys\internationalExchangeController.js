const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');
const Exchange = require('@models/v1/mapping/internationalExchangesModel');
const sequelize = require('@config/dbConfig');

/**
 * 获取国际交流列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getExchanges = async (req, res) => {
  try {
    const { 
      usernameList, 
      type, 
      country, 
      institution, 
      startDate, 
      endDate,
      page = 1, 
      pageSize = 10, 
      userOnly = false, 
      userId 
    } = req.query; // 修改为query，因为前端get请求会将参数放在query中
    
    console.log('----------- 获取国际交流列表开始 -----------');
    console.log(`请求参数:`, req.query);
    console.log(`分页: page=${page}, pageSize=${pageSize}`);
    console.log(`过滤: userOnly=${userOnly}, userId=${userId}`);
    
    // 构建查询条件
    const where = {};
    
    if (usernameList) {
      where.usernameList = { [Op.like]: `%${usernameList}%` };
      console.log(`添加用户名称条件: ${usernameList}`);
    }
    
    if (type) {
      where.type = type;
      console.log(`添加交流类型条件: ${type}`);
    }
    
    if (country) {
      where.country = { [Op.like]: `%${country}%` };
      console.log(`添加国家/地区条件: ${country}`);
    }
    
    if (institution) {
      where.institution = { [Op.like]: `%${institution}%` };
      console.log(`添加机构条件: ${institution}`);
    }
    
    // 处理日期范围查询
    if (startDate && endDate) {
      where.startDate = { 
        [Op.gte]: new Date(startDate)
      };
      where.endDate = {
        [Op.lte]: new Date(endDate)
      };
      console.log(`添加日期范围条件: ${startDate} 至 ${endDate}`);
    } else if (startDate) {
      where.startDate = { 
        [Op.gte]: new Date(startDate)
      };
      console.log(`添加开始日期条件: ${startDate}`);
    } else if (endDate) {
      where.endDate = {
        [Op.lte]: new Date(endDate)
      };
      console.log(`添加结束日期条件: ${endDate}`);
    }
    
    // 如果是查询个人数据，则添加用户ID条件
    if (userOnly && userId) {
      // 使用改进的查询方法，包括LIKE匹配和FIND_IN_SET函数
      where[Op.or] = [
        { userIdList: userId }, // 精确匹配单个ID
        { userIdList: { [Op.like]: `${userId},%` } }, // ID在开头，后面跟着逗号
        { userIdList: { [Op.like]: `%,${userId},%` } }, // ID在中间，两边都有逗号
        { userIdList: { [Op.like]: `%,${userId}` } }, // ID在结尾，前面有逗号
        sequelize.literal(`FIND_IN_SET('${userId}', REPLACE(userIdList, ' ', '')) > 0`) // 使用MySQL的FIND_IN_SET函数
      ];
      console.log(`添加用户ID过滤条件: ${userId}`);
      console.log(`使用改进的用户ID匹配条件`);
    }
    
    console.log('最终查询条件:', JSON.stringify(where));
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    console.log(`执行分页查询: offset=${offset}, limit=${Number(pageSize)}`);
    
    // 执行查询
    console.log('开始查询数据库...');
    const startTime = Date.now();
    const { count, rows } = await Exchange.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['createdAt', 'DESC']]
    });
    const queryTime = Date.now() - startTime;
    console.log(`查询完成，耗时: ${queryTime}ms, 找到记录数: ${count}`);
    
    // 构建分页信息
    const totalPages = Math.ceil(count / pageSize);
    console.log(`分页结果: 当前页=${page}, 总页数=${totalPages}, 总记录数=${count}`);
    
    if (rows.length === 0) {
      console.log('未找到记录');
    } else {
      console.log(`返回记录数: ${rows.length}`);
      console.log(`第一条记录ID: ${rows[0].id}, 名称: ${rows[0].name}`);
    }
    
    const response = {
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: totalPages
        }
      }
    };
    
    console.log('----------- 获取国际交流列表结束 -----------');
    return res.status(200).json(response);
  } catch (error) {
    console.error('获取国际交流列表失败:', error);
    console.log('----------- 获取国际交流列表异常结束 -----------');
    return res.status(500).json({
      code: 500,
      message: '获取国际交流列表失败',
      data: null
    });
  }
};

/**
 * 获取国际交流详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getExchangeById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const exchange = await Exchange.findByPk(id);
    if (!exchange) {
      return res.status(404).json({
        code: 404,
        message: '国际交流记录不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: exchange
    });
  } catch (error) {
    console.error('获取国际交流详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取国际交流详情失败',
      data: null
    });
  }
};

/**
 * 创建国际交流记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createExchange = async (req, res) => {
  try {
    const { 
      usernameList, 
      userIdList,
      name,
      type, 
      country, 
      institution, 
      startDate,
      endDate,
      content,
      result,
      score,
      remark
    } = req.body;
    
    // 处理用户ID和用户名
    let userIds = Array.isArray(userIdList) ? userIdList.join(',') : userIdList;
    let usernames = Array.isArray(usernameList) ? usernameList.join(',') : usernameList;
    
    // 生成名称（如果前端未提供）
    const exchangeName = name || `${usernames}的${type}交流`;
    
    // 创建国际交流记录
    const exchange = await Exchange.create({
      id: uuidv4(),
      name: exchangeName,
      type,
      country,
      institution: institution || '',
      userIdList: userIds,
      usernameList: usernames,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      content: content || '',
      result: result || '',
      score: score || 0,
      remark: remark || '',
      status: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: exchange
    });
  } catch (error) {
    console.error('创建国际交流记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建国际交流记录失败',
      data: null
    });
  }
};

/**
 * 更新国际交流记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateExchange = async (req, res) => {
  try {
    const { id } = req.params; // ID在URL参数中
    
    const { 
      usernameList, 
      userIdList,
      name,
      type, 
      country, 
      institution, 
      startDate,
      endDate,
      content,
      result,
      score,
      remark
    } = req.body;
    
    // 查找国际交流记录
    const exchange = await Exchange.findByPk(id);
    if (!exchange) {
      return res.status(404).json({
        code: 404,
        message: '国际交流记录不存在',
        data: null
      });
    }
    
    // 处理用户ID和用户名
    let userIds = Array.isArray(userIdList) ? userIdList.join(',') : userIdList;
    let usernames = Array.isArray(usernameList) ? usernameList.join(',') : usernameList;
    
    // 生成名称（如果前端未提供）
    const exchangeName = name || `${usernames}的${type}交流`;
    
    // 更新国际交流记录
    await exchange.update({
      name: exchangeName,
      type,
      country,
      institution: institution || exchange.institution,
      userIdList: userIds,
      usernameList: usernames,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      content: content || exchange.content,
      result: result || exchange.result,
      score: score || exchange.score,
      remark: remark || exchange.remark,
      updatedAt: new Date()
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: exchange
    });
  } catch (error) {
    console.error('更新国际交流记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新国际交流记录失败',
      data: null
    });
  }
};

/**
 * 删除国际交流记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteExchange = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找国际交流记录
    const exchange = await Exchange.findByPk(id);
    if (!exchange) {
      return res.status(404).json({
        code: 404,
        message: '国际交流记录不存在',
        data: null
      });
    }
    
    // 删除国际交流记录
    await exchange.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除国际交流记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除国际交流记录失败',
      data: null
    });
  }
};

/**
 * 导入国际交流数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importExchanges = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请上传文件',
        data: null
      });
    }
    
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = xlsx.utils.sheet_to_json(worksheet);
    
    if (data.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '文件内容为空',
        data: null
      });
    }
    
    // 验证并处理数据
    const exchanges = [];
    const errors = [];
    const requiredFields = ['参与用户', '交流类型', '交流国家/地区', '交流机构', '开始时间', '结束时间'];
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNum = i + 2; // Excel 行号从1开始，且有表头
      
      // 检查必填字段
      const missingFields = requiredFields.filter(field => !row[field] && row[field] !== 0);
      if (missingFields.length > 0) {
        errors.push(`第${rowNum}行: ${missingFields.join(', ')}不能为空`);
        continue;
      }
      
      // 处理开始和结束日期
      let startDate, endDate;
      
      try {
        startDate = new Date(row['开始时间']);
        endDate = new Date(row['结束时间']);
        
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          errors.push(`第${rowNum}行: 日期格式无效`);
          continue;
        }
        
        if (startDate > endDate) {
          errors.push(`第${rowNum}行: 开始时间不能晚于结束时间`);
          continue;
        }
      } catch (error) {
        errors.push(`第${rowNum}行: 日期格式转换错误 - ${error.message}`);
        continue;
      }
      
      // 处理用户名称
      const usernameList = row['参与用户'] || '';
      
      // 生成名称（如果Excel中没有提供）
      const name = row['交流名称'] || `${usernameList}的${row['交流类型']}交流`;
      
      exchanges.push({
        id: uuidv4(),
        name,
        type: row['交流类型'],
        country: row['交流国家/地区'],
        institution: row['交流机构'] || '',
        userIdList: row['用户ID列表'] || '',
        usernameList,
        startDate,
        endDate,
        content: row['交流内容'] || '',
        result: row['交流成果'] || '',
        score: row['评分'] || 0,
        remark: row['备注'] || '',
        status: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    
    // 批量创建国际交流记录
    if (exchanges.length > 0) {
      await Exchange.bulkCreate(exchanges);
    }
    
    // 删除临时文件
    fs.unlinkSync(filePath);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: {
        total: data.length,
        success: exchanges.length,
        failed: errors.length,
        errors
      }
    });
  } catch (error) {
    console.error('导入国际交流记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导入国际交流记录失败',
      data: null
    });
  }
};

/**
 * 导出国际交流数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportExchanges = async (req, res) => {
  try {
    const { usernameList, type, country, institution, startDate, endDate } = req.query;
    
    // 构建查询条件
    const where = {};
    if (usernameList) where.usernameList = { [Op.like]: `%${usernameList}%` };
    if (type) where.type = type;
    if (country) where.country = { [Op.like]: `%${country}%` };
    if (institution) where.institution = { [Op.like]: `%${institution}%` };
    
    // 处理日期范围
    if (startDate && endDate) {
      where.startDate = { [Op.gte]: new Date(startDate) };
      where.endDate = { [Op.lte]: new Date(endDate) };
    } else if (startDate) {
      where.startDate = { [Op.gte]: new Date(startDate) };
    } else if (endDate) {
      where.endDate = { [Op.lte]: new Date(endDate) };
    }
    
    // 查询数据
    const exchanges = await Exchange.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });
    
    // 转换为Excel格式
    const excelData = exchanges.map(exchange => {
      // 计算交流天数
      const startDate = new Date(exchange.startDate);
      const endDate = new Date(exchange.endDate);
      const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      return {
        '参与用户': exchange.usernameList || '',
        '交流名称': exchange.name,
        '交流类型': exchange.type,
        '交流国家/地区': exchange.country,
        '交流机构': exchange.institution || '',
        '开始时间': exchange.startDate.toLocaleDateString(),
        '结束时间': exchange.endDate.toLocaleDateString(),
        '交流天数': days,
        '交流内容': exchange.content || '',
        '交流成果': exchange.result || '',
        '评分': exchange.score,
        '备注': exchange.remark || '',
        '状态': exchange.status === 1 ? '正常' : '已删除',
        '创建时间': exchange.createdAt.toLocaleString(),
        '更新时间': exchange.updatedAt.toLocaleString()
      };
    });
    
    // 创建工作簿和工作表
    const worksheet = xlsx.utils.json_to_sheet(excelData);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, '国际交流数据');
    
    // 生成Excel文件
    const fileName = `international_exchanges_${Date.now()}.xlsx`;
    const filePath = path.join(__dirname, '..', '..', '..', 'uploads', fileName);
    
    // 确保目录存在
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 写入文件
    xlsx.writeFile(workbook, filePath);
    
    // 发送文件
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
    fileStream.on('close', () => {
      // 删除临时文件
      fs.unlinkSync(filePath);
    });
  } catch (error) {
    console.error('导出国际交流记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出国际交流记录失败',
      data: null
    });
  }
};

/**
 * 获取个人国际交流统计数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPersonalExchangeStats = async (req, res) => {
  try {
    let { userId } = req.query;
    
    console.log('----------- 个人国际交流统计开始 -----------');
    console.log(`请求参数: userId = ${userId}`);
    console.log(`请求头: ${JSON.stringify(req.headers)}`);
    
    // 如果没有提供userId
    if (!userId) {
      console.log('未提供用户ID，返回400错误');
      return res.status(400).json({
        code: 400,
        message: '未提供用户ID且无法获取当前用户信息',
        data: null
      });
    }
    
    console.log('构建查询条件...');
    
    // 尝试更多的匹配方式
    // 由于TEXT字段在MySQL中使用LIKE操作符，我们尝试多种模式匹配
    // 对于Sequelize，[Op.like]会被翻译为SQL中的LIKE
    const where = {
      [Op.or]: [
        { userIdList: userId }, // 精确匹配单个ID
        { userIdList: { [Op.like]: `${userId},%` } }, // ID在开头，后面跟着逗号
        { userIdList: { [Op.like]: `%,${userId},%` } }, // ID在中间，两边都有逗号
        { userIdList: { [Op.like]: `%,${userId}` } }, // ID在结尾，前面有逗号
        sequelize.literal(`FIND_IN_SET('${userId}', REPLACE(userIdList, ' ', '')) > 0`) // 使用MySQL的FIND_IN_SET函数
      ]
    };
    
    console.log('查询条件:', JSON.stringify(where));
    console.log('含SQL literal的条件无法完全序列化，请参考代码中的实际查询条件');
    
    // 查询用户所有国际交流记录
    console.log('开始查询用户记录...');
    const exchanges = await Exchange.findAll({
      where,
      order: [['startDate', 'DESC']]
    });
    
    console.log(`查询结果: 找到 ${exchanges.length} 条记录`);
    
    // 如果有记录，在控制台打印第一条记录的ID和userIdList字段，帮助排查
    if (exchanges.length > 0) {
      console.log(`第一条记录ID: ${exchanges[0].id}`);
      console.log(`第一条记录userIdList: ${exchanges[0].userIdList}`);
      console.log(`第一条记录usernameList: ${exchanges[0].usernameList}`);
    } else {
      // 尝试用两种查询方式分别查询，看看哪种能返回结果
      console.log('尝试更简单的查询方式...');
      
      // 尝试方式1: 只使用LIKE
      const likeResult = await Exchange.findAll({
        where: {
          [Op.or]: [
            { userIdList: { [Op.like]: `%${userId}%` } }
          ]
        },
        limit: 2
      });
      
      console.log(`简单LIKE查询结果数: ${likeResult.length}`);
      
      if (likeResult.length > 0) {
        console.log('简单LIKE查询找到记录，示例:');
        console.log(`- userIdList: ${likeResult[0].userIdList}`);
      }
      
      // 尝试方式2: 全表扫描然后在内存中过滤
      console.log('尝试全表扫描...');
      const allRecords = await Exchange.findAll({ limit: 20 });
      console.log(`全表扫描获取记录数: ${allRecords.length}`);
      
      if (allRecords.length > 0) {
        // 检查前几条记录的userIdList字段格式
        allRecords.slice(0, 5).forEach((record, i) => {
          console.log(`记录${i+1} userIdList: ${record.userIdList}, 类型: ${typeof record.userIdList}`);
        });
        
        // 在JS中尝试匹配
        const matchedRecords = allRecords.filter(record => {
          if (!record.userIdList) return false;
          
          // 按逗号分割
          const idList = String(record.userIdList).split(',').map(id => id.trim());
          return idList.includes(userId);
        });
        
        console.log(`JS内存匹配找到记录数: ${matchedRecords.length}`);
        if (matchedRecords.length > 0) {
          console.log('JS匹配找到的记录示例:');
          console.log(`- userIdList: ${matchedRecords[0].userIdList}`);
        }
      }
    }
    
    // 如果没有找到记录，返回空的统计数据
    if (exchanges.length === 0) {
      console.log('未找到记录，返回空统计数据');
      const emptyResponse = {
        code: 200,
        message: '获取成功，但没有找到交流记录',
        data: {
          exchangeCount: 0,
          totalScore: 0,
          totalDays: 0,
          typeDistribution: {},
          countryDistribution: {},
          timeDistribution: {},
          scoreTrend: {},
          recentExchanges: []
        }
      };
      console.log('响应数据:', JSON.stringify(emptyResponse));
      console.log('----------- 个人国际交流统计结束 -----------');
      return res.status(200).json(emptyResponse);
    }
    
    console.log('处理统计数据...');
    
    // 计算总分和总天数
    const totalScore = exchanges.reduce((sum, exchange) => sum + parseFloat(exchange.score || 0), 0);
    const totalDays = exchanges.reduce((sum, exchange) => {
      const startDate = new Date(exchange.startDate);
      const endDate = new Date(exchange.endDate);
      const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      return sum + days;
    }, 0);
    
    console.log(`计算结果: 总分=${totalScore}, 总天数=${totalDays}`);
    
    // 交流类型分布
    const typeDistribution = {};
    exchanges.forEach(exchange => {
      if (!typeDistribution[exchange.type]) {
        typeDistribution[exchange.type] = 0;
      }
      typeDistribution[exchange.type]++;
    });
    
    // 交流国家/地区分布
    const countryDistribution = {};
    exchanges.forEach(exchange => {
      if (!countryDistribution[exchange.country]) {
        countryDistribution[exchange.country] = 0;
      }
      countryDistribution[exchange.country]++;
    });
    
    // 交流时间分布
    const timeDistribution = {};
    const currentYear = new Date().getFullYear();
    // 初始化最近5年的数据
    for (let i = 0; i < 5; i++) {
      timeDistribution[currentYear - i] = 0;
    }
    
    exchanges.forEach(exchange => {
      if (exchange.startDate) {
        const year = new Date(exchange.startDate).getFullYear();
        if (timeDistribution[year] !== undefined) {
          timeDistribution[year]++;
        }
      }
    });
    
    // 计算年度分数趋势
    const yearlyScores = {};
    // 初始化最近5年的数据
    for (let i = 0; i < 5; i++) {
      yearlyScores[currentYear - i] = {
        total: 0,
        count: 0
      };
    }
    
    exchanges.forEach(exchange => {
      if (exchange.startDate && exchange.score) {
        const year = new Date(exchange.startDate).getFullYear();
        if (yearlyScores[year]) {
          yearlyScores[year].total += parseFloat(exchange.score || 0);
          yearlyScores[year].count++;
        }
      }
    });
    
    // 转换为平均分
    const scoreTrend = {};
    Object.keys(yearlyScores).forEach(year => {
      scoreTrend[year] = yearlyScores[year].count > 0
        ? (yearlyScores[year].total / yearlyScores[year].count).toFixed(2)
        : 0;
    });
    
    // 获取参与者用户名
    const getParticipants = (exchange) => {
      try {
        if (exchange.usernameList) {
          // 直接使用逗号分隔的字符串
          return exchange.usernameList;
        }
        return exchange.usernameList || '';
      } catch (e) {
        console.error('获取参与者名称出错:', e);
        return exchange.usernameList || '';
      }
    };
    
    const recentExchanges = exchanges.slice(0, 5).map(exchange => {
      const startDate = new Date(exchange.startDate);
      const endDate = new Date(exchange.endDate);
      const exchangeDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      return {
        id: exchange.id,
        name: exchange.name,
        teacherName: getParticipants(exchange),
        type: exchange.type,
        country: exchange.country,
        institution: exchange.institution || '',
        startDate: exchange.startDate,
        endDate: exchange.endDate,
        content: exchange.content,
        result: exchange.result,
        score: exchange.score,
        remark: exchange.remark || ''
      };
    });
    
    console.log(`处理最近交流记录: ${recentExchanges.length}条`);
    
    const responseData = {
      code: 200,
      message: '获取成功',
      data: {
        exchangeCount: exchanges.length,
        totalScore,
        totalDays,
        typeDistribution,
        countryDistribution,
        timeDistribution,
        scoreTrend,
        recentExchanges
      }
    };
    
    console.log('响应数据摘要:');
    console.log(`- 交流总数: ${exchanges.length}`);
    console.log(`- 总分: ${totalScore}`);
    console.log(`- 总天数: ${totalDays}`);
    console.log(`- 交流类型: ${Object.keys(typeDistribution).length}种`);
    console.log(`- 交流国家: ${Object.keys(countryDistribution).length}个`);
    console.log('----------- 个人国际交流统计结束 -----------');
    
    return res.status(200).json(responseData);
  } catch (error) {
    console.error('获取个人国际交流统计数据失败:', error);
    console.log('----------- 个人国际交流统计异常结束 -----------');
    return res.status(500).json({
      code: 500,
      message: '获取个人国际交流统计数据失败',
      data: null
    });
  }
};

/**
 * 测试查询 - 仅用于排查问题
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.testQuery = async (req, res) => {
  try {
    console.log('----------- 测试查询开始 -----------');
    
    // 直接查询所有记录，不加任何条件
    const allRecords = await Exchange.findAll({
      limit: 10,
      order: [['createdAt', 'DESC']]
    });
    
    console.log(`总共找到记录数: ${allRecords.length}`);
    
    // 检查每条记录的userIdList格式
    allRecords.forEach((record, index) => {
      console.log(`记录 #${index+1}:`);
      console.log(`- ID: ${record.id}`);
      console.log(`- 名称: ${record.name}`);
      console.log(`- userIdList: ${record.userIdList}`);
      console.log(`- userIdList类型: ${typeof record.userIdList}`);
      console.log(`- usernameList: ${record.usernameList}`);
    });
    
    // 返回部分数据用于检查
    return res.status(200).json({
      code: 200,
      message: '测试查询成功',
      data: {
        count: allRecords.length,
        records: allRecords.map(r => ({
          id: r.id,
          name: r.name,
          userIdList: r.userIdList,
          userIdListType: typeof r.userIdList,
          usernameList: r.usernameList
        }))
      }
    });
  } catch (error) {
    console.error('测试查询失败:', error);
    return res.status(500).json({
      code: 500,
      message: '测试查询失败',
      error: error.message
    });
  }
}; 