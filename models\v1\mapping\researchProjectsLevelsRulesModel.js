const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const researchProjectsLevelsRulesModel = sequelize.define(
  'research_projects_levels_rules',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '主键，使用 UUID 唯一标识每条记录'
    },
    levelName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '项目级别名称（如国家级、省部级等）'
    },
    score: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '基础核算分数'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '级别描述'
    },
    nonDepartmentLeaderCoefficient: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.90,
      comment: '非本院负责人的系数'
    },
    maxProjectsPerPerson: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 5,
      comment: '每人最多可填写的代表性项目数量'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '记录创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '记录最后修改时间'
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '创建者 ID（userId）'
    }
  },
  {
    sequelize,
    modelName: 'research_projects_levels_rules',
    tableName: 'research_projects_levels_rules',
    timestamps: true
  }
);

module.exports = researchProjectsLevelsRulesModel; 