<template>
  <div class="research-funds">
    <!-- 添加错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
    
    <a-card title="B科研经费评分" :bordered="false">
      <template #extra>
        <a-space>
          <a-upload
            :action="uploadUrl"
            :before-upload="beforeUpload"
            :show-upload-list="false"
            @change="handleImportChange"
            v-permission="'score:B:admin:update'"
          >
            <a-button type="primary">
              <template #icon><UploadOutlined /></template>
              导入数据
            </a-button>
          </a-upload>
          <a-button type="primary" @click="handleExport" v-permission="'score:B:admin:list'">
            <template #icon><DownloadOutlined /></template>
            导出数据
          </a-button>
          <a-button type="primary" @click="showAddModal" v-permission="showPersonalFunds ? 'score:B:self:create' : 'score:B:admin:create'">
            <template #icon><PlusOutlined /></template>
            添加经费
          </a-button>
          <a-button :type="showPersonalFunds ? 'default' : 'primary'" @click="togglePersonalFunds" v-permission="'score:B:admin:list'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalFunds ? '查看全部经费' : '查看我的经费' }}
          </a-button>
        </a-space>
      </template>

      <a-alert
        message="评分说明"
        description="统计时间：上年度7月1日-本年度6月30日。计算公式：B={1⌊20经费⌋ 经费≤20万 经费>20万}。横向经费：单项≥100万视为省级课题。"
        type="success"
        show-icon
        style="margin-bottom: 16px; background-color: #f0f9f4; border-color: #ccebd7; color: #52937b;"
      />

      <!-- 统计图表区域 -->
      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="12">
          <a-card title="经费类型分布" :bordered="false">
            <div ref="typeChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="经费金额分布" :bordered="false">
            <div ref="amountChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="12">
          <a-card title="经费时间分布" :bordered="false">
            <div ref="timeChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="经费得分分布" :bordered="false">
            <div ref="scoreChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="24">
          <a-card title="项目负责人得分排名" :bordered="false">
            <div ref="leaderRankChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-bottom: 24px;">
        <a-col :span="24">
          <a-card title="经费金额与得分关系" :bordered="false">
            <div ref="amountScoreChartRef" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="loading"
        rowKey="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'amount'">
            <span>{{ record.amount }}万元</span>
          </template>
          <template v-if="column.key === 'score'">
            <span>{{ record.score }}分</span>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)" v-permission="showPersonalFunds ? 'score:B:self:update' : 'score:B:admin:update'">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这条记录吗？"
                @confirm="handleDelete(record)"
              >
                <a class="text-danger" v-permission="showPersonalFunds ? 'score:B:self:delete' : 'score:B:admin:delete'">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <div class="table-footer">
        <div class="total-score">
          <span>总分：{{ totalScore }}分</span>
        </div>
      </div>
    </a-card>

    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑科研经费' : '添加科研经费'"
      width="700px"
      :confirmLoading="confirmLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :destroyOnClose="true"
      :maskClosable="false"
      centered
    >
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="项目名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入项目名称" />
        </a-form-item>

        <a-form-item label="经费类型" name="type">
          <a-select
            v-model:value="formState.type"
            placeholder="请选择经费类型"
          >
            <a-select-option value="vertical">纵向经费</a-select-option>
            <a-select-option value="horizontal">横向经费</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="经费金额" name="amount">
          <a-space direction="vertical" style="width: 100%">
            <a-input-group compact>
              <a-input-number
                v-model:value="formState.amount"
                :min="0"
                :max="amountUnit === '元' ? 999999999 : 99999999.99"
                :step="amountUnit === '元' ? 10000 : 1"
                style="width: 70%"
                :placeholder="`请输入经费金额(${amountUnit})`"
                @change="calculateScore"
              />
              <a-button style="width: 30%" @click="handleUnitChange">
                {{ amountUnit }}
              </a-button>
            </a-input-group>
            
            <a-button 
              type="link" 
              @click="showUnitSlider = !showUnitSlider" 
              style="padding-left: 0"
            >
              {{ showUnitSlider ? '隐藏' : '显示' }}拖动条
            </a-button>
            
            <a-slider 
              v-if="showUnitSlider"
              v-model:value="formState.amount" 
              :min="0" 
              :max="amountUnit === '元' ? 10000000 : 1000" 
              :step="amountUnit === '元' ? 10000 : 1"
              :tooltip-visible="true"
              :tooltip-open="true"
              :formatter="value => `${value}${amountUnit}`"
            />
            
            <a-alert
              v-if="amountUnit === '元'"
              message="将自动转换为万元单位存储"
              type="info"
              banner
              closable
            />
          </a-space>
        </a-form-item>

        <a-form-item label="开始时间" name="startDate">
          <a-date-picker
            v-model:value="formState.startDate"
            style="width: 100%"
            :disabled-date="disabledStartDate"
          />
        </a-form-item>

        <a-form-item label="结束时间" name="endDate">
          <a-date-picker
            v-model:value="formState.endDate"
            style="width: 100%"
            :disabled-date="disabledEndDate"
          />
        </a-form-item>

        <a-form-item label="项目负责人" name="leader">
          <a-select
            v-model:value="formState.leader"
            placeholder="请选择项目负责人"
            :options="memberOptions"
            show-search
            :filter-option="(input, option) => 
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            :dropdown-style="{ padding: '8px', width: '100%' }"
            :open="leaderSelectOpen"
            @dropdown-visible-change="handleLeaderDropdownVisibleChange"
            :disabled="showPersonalFunds.value && !isEdit.value"
          >
            <template #dropdownRender="{ menuNode }">
              <div style="padding: 8px;">
                <a-input
                  v-model:value="leaderNewTeacherName"
                  placeholder="输入姓名"
                  style="width: 100%; margin-bottom: 8px"
                  @mousedown.stop
                  @click.stop
                  @focus.stop
                  @input.stop
                  :disabled="showPersonalFunds.value && !isEdit.value"
                />
                <a-button 
                  type="primary" 
                  block 
                  :disabled="!leaderNewTeacherName || (showPersonalFunds.value && !isEdit.value)" 
                  @click="addLeaderCustomTeacher"
                  @mousedown.stop
                  @mouseup.stop
                >
                  <template #icon><PlusOutlined /></template>
                  添加教师
                </a-button>
                <a-divider style="margin: 8px 0" />
                <div v-if="menuNode" :innerHTML="menuNode.innerHTML || ''"></div>
                
                <a-divider style="margin: 8px 0" />
                <div v-html="menuNodeHandler(menuNode)"></div>

                <a-divider style="margin: 8px 0" />
                <a-select-option-container>
                  <component :is="menuNode"></component>
                </a-select-option-container>
              </div>
            </template>
          </a-select>
          <div v-if="showPersonalFunds.value && !isEdit.value" style="color: #999; font-size: 12px; margin-top: 4px;">
            个人视图下，项目负责人默认为当前用户
          </div>
        </a-form-item>

        <a-form-item label="项目成员" name="members">
          <a-select
            v-model:value="formState.members"
            mode="multiple"
            placeholder="请选择项目成员"
            :options="memberOptions"
            show-search
            :filter-option="(input, option) => 
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            :dropdown-style="{ padding: '8px', width: '100%' }"
            :open="membersSelectOpen"
            @dropdown-visible-change="handleMembersDropdownVisibleChange"
          >
            <template #dropdownRender="{ menuNode }">
              <div style="padding: 8px;">
                <a-input
                  v-model:value="membersNewTeacherName"
                  placeholder="输入姓名"
                  style="width: 100%; margin-bottom: 8px"
                  @mousedown.stop
                  @click.stop
                  @focus.stop
                  @input.stop
                />
                <a-button 
                  type="primary" 
                  block 
                  :disabled="!membersNewTeacherName" 
                  @click="addMembersCustomTeacher"
                  @mousedown.stop
                  @mouseup.stop
                >
                  <template #icon><PlusOutlined /></template>
                  添加
                </a-button>
                <a-divider style="margin: 8px 0" />
                <div v-if="menuNode" :innerHTML="menuNode.innerHTML || ''"></div>
              </div>
            </template>
          </a-select>
        </a-form-item>

        <a-form-item label="项目描述" name="description">
          <a-textarea
            v-model:value="formState.description"
            :rows="4"
            placeholder="请输入项目描述"
          />
        </a-form-item>

        <a-form-item
          label="导师类型"
          name="tutorType"
          required
        >
          <div class="checkbox-container">
            <div class="checkbox-item" style="display: flex; align-items: center; margin-bottom: 8px;">
              <a-checkbox :checked="formState.tutorType && formState.tutorType.includes('博导')" @change="e => handleTutorTypeChange('博导', e.target.checked)">博导</a-checkbox>
            </div>
            <div class="checkbox-item" style="display: flex; align-items: center; margin-bottom: 8px;">
              <a-checkbox :checked="formState.tutorType && formState.tutorType.includes('硕导')" @change="e => handleTutorTypeChange('硕导', e.target.checked)">硕导</a-checkbox>
            </div>
            <div class="checkbox-item" style="display: flex; align-items: center; margin-bottom: 8px;">
              <a-checkbox :checked="formState.tutorType && formState.tutorType.includes('学术学位')" @change="e => handleTutorTypeChange('学术学位', e.target.checked)">学术学位</a-checkbox>
            </div>
            <div class="checkbox-item" style="display: flex; align-items: center; margin-bottom: 8px;">
              <a-checkbox :checked="formState.tutorType && formState.tutorType.includes('专业学位')" @change="e => handleTutorTypeChange('专业学位', e.target.checked)">专业学位</a-checkbox>
            </div>
          </div>
          <a-alert
            message="注意：其中【学术学位】和【专业学位】至少选择一项，【博导】和【硕导】至少选择一项，某些选项对应成果的分值会有所不同。"
            type="info"
            show-icon
          />
        </a-form-item>

        <a-form-item label="研究级别" name="researchLevel">
          <a-select v-model:value="formState.researchLevel" placeholder="请选择研究级别">
            <a-select-option value="national">国家级</a-select-option>
            <a-select-option value="provincial">省级</a-select-option>
            <a-select-option value="municipal">市级</a-select-option>
            <a-select-option value="school">校级</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="附件" name="attachments">
          <a-upload
            v-model:file-list="fileList"
            :action="uploadUrl"
            :before-upload="beforeUpload"
            v-permission="'score:B:admin:update'"
          >
            <a-button>
              <template #icon><UploadOutlined /></template>
              选择文件
            </a-button>
          </a-upload>
        </a-form-item>

        <a-form-item label="得分" name="score">
          <a-input-number
            v-model:value="formState.score"
            :min="0"
            :max="100"
            style="width: 100%"
            disabled
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { UploadOutlined, DownloadOutlined, PlusOutlined, UserOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { getResearchFunds, getUserResearchFunds, getAllResearchFunds, getResearchFundDetail, 
addResearchFund, updateResearchFund, deleteResearchFund, importResearchFunds, exportResearchFunds } from '@/api/modules/api.research_funds'
import useUserId from '@/composables/useUserId'
import { 
  teachersList as getTeacherList
} from '@/api/modules/api.teachers'

// 使用useUserId composable
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()

// 图表引用
const typeChartRef = ref(null)
const amountChartRef = ref(null)
const timeChartRef = ref(null)
const scoreChartRef = ref(null)
const leaderRankChartRef = ref(null)
const amountScoreChartRef = ref(null)

// 图表实例
let typeChart = null
let amountChart = null
let timeChart = null
let scoreChart = null
let leaderRankChart = null
let amountScoreChart = null

// 表格列定义
const columns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '经费类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '经费金额',
    dataIndex: 'amount',
    key: 'amount',
    sorter: true,
  },
  {
    title: '开始时间',
    dataIndex: 'startDate',
    key: 'startDate',
  },
  {
    title: '结束时间',
    dataIndex: 'endDate',
    key: 'endDate',
  },
  {
    title: '项目负责人',
    dataIndex: 'leader',
    key: 'leader',
  },
  {
    title: '得分',
    dataIndex: 'score',
    key: 'score',
    sorter: true,
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 经费类型名称映射
const typeNameMap = {
  'vertical': '纵向经费',
  'horizontal': '横向经费'
}

// 数据源
const dataSource = ref([]) // 使用空数组作为默认值，不再使用mockData
const loading = ref(false)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
})

// 总分计算
const totalScore = computed(() => {
  return dataSource.value.reduce((sum, item) => sum + item.score, 0)
})

// 模态框相关
const modalVisible = ref(false)
const confirmLoading = ref(false)
const isEdit = ref(false)
const currentRecord = ref(null)
const formRef = ref(null)

// 表单数据
const formState = reactive({
  name: '',
  type: '',
  amount: 0,
  startDate: null,
  endDate: null,
  leader: '',
  members: [],
  description: '',
  score: 0,
  tutorType: [],
  researchLevel: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '项目名称长度在2-100个字符之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择经费类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入经费金额', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  leader: [
    { required: true, message: '请输入项目负责人', trigger: 'blur' }
  ]
}

// 文件上传相关
const fileList = ref([])
const uploadUrl = '/api/upload' // 替换为实际的上传接口

const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  return true
}

// 错误信息展示
const errorMessage = ref('')

// 个人/全部经费相关
const showPersonalFunds = ref(false)

// 切换个人/全部经费视图
const togglePersonalFunds = () => {
  console.log('切换个人/全部经费视图，当前状态:', showPersonalFunds.value)
  showPersonalFunds.value = !showPersonalFunds.value
  console.log('切换后状态:', showPersonalFunds.value)
  fetchData()
}

// 用户ID到用户名的映射
const userIdToNameMap = ref({})

// 根据ID获取用户名
const getUserNameById = (id) => {
  if (!id) return '未知用户'
  return userIdToNameMap.value[id] || id
}

// 处理menuNode，避免Object Object显示
const menuNodeHandler = (node) => {
  if (!node) return '';
  // 如果node有innerHTML属性，使用它
  if (node.innerHTML) return node.innerHTML;
  // 如果是字符串，直接返回
  if (typeof node === 'string') return node;
  // 默认返回空字符串
  return '';
}

// 更新用户ID到用户名的映射
const updateUserIdToNameMap = () => {
  memberOptions.value.forEach(option => {
    userIdToNameMap.value[option.value] = option.label
  })
}

// 处理表格数据，转换用户ID为姓名
const processTableData = (data) => {
  return data.map(item => ({
    ...item,
    _originalLeader: item.leader,
    _originalMembers: item.members,
    leader: getUserNameById(item.leader),
    members: item.members ? 
      (typeof item.members === 'string' 
        ? item.members.split(',').map(id => getUserNameById(id)).join(', ')
        : Array.isArray(item.members) 
          ? item.members.map(id => getUserNameById(id)).join(', ')
          : item.members)
      : ''
  }))
}

// 获取数据
const fetchData = async () => {
  console.log('开始获取数据')
  loading.value = true
  errorMessage.value = ''
  
  try {
    if (showPersonalFunds.value) {
      console.log('获取个人经费数据')
      // 获取当前用户信息
      try {
        // 使用统一的getUserId方法获取用户ID
        const currentUserId = await getUserId(true) // 允许使用默认ID
        
        if (!currentUserId) {
          throw new Error('未能获取到用户ID')
        }
        
        try {
          console.log('调用getUserResearchFunds API，参数:', {
            userId: currentUserId,
            page: pagination.current,
            pageSize: pagination.pageSize
          })
          
          const response = await getUserResearchFunds({
            userId: currentUserId,
            page: pagination.current,
            pageSize: pagination.pageSize
          })
          
          console.log('getUserResearchFunds API响应:', response)
          
          if (response && response.code === 200) {
            // 处理数据，转换用户ID为名称
            dataSource.value = processTableData(response.data.list || [])
            pagination.total = response.data.pagination?.total || 0
            
            // 更新图表
            updateCharts()
          } else {
            errorMessage.value = response?.message || '获取数据失败'
          }
        } catch (error) {
          console.error('获取个人科研经费列表失败:', error)
          errorMessage.value = error.message || '获取数据失败'
        }
      } catch (error) {
        console.error('获取用户ID失败:', error)
        errorMessage.value = '无法获取用户信息'
      }
    } else {
      console.log('获取全部经费数据')
      // 获取所有科研经费
      try {
        const response = await getAllResearchFunds({
          page: pagination.current,
          pageSize: pagination.pageSize
        })
        
        console.log('getAllResearchFunds API响应:', response)
        
        if (response && response.code === 200) {
          // 处理数据，转换用户ID为名称
          dataSource.value = processTableData(response.data.list || [])
          pagination.total = response.data.pagination?.total || 0
          
          // 更新图表
          updateCharts()
        } else {
          errorMessage.value = response?.message || '获取数据失败'
        }
      } catch (error) {
        console.error('获取所有科研经费列表失败:', error)
        errorMessage.value = error.message || '获取数据失败'
      }
    }
  } catch (error) {
    console.error('获取科研经费数据失败:', error)
    errorMessage.value = error.message || '获取数据失败'
  } finally {
    loading.value = false
  }
}

// 修改处理表格变化
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  
  // 处理排序
  if (sorter.field && sorter.order) {
    // 实际项目中可以调用API进行服务器端排序
    console.log('排序字段:', sorter.field)
    console.log('排序方式:', sorter.order)
  }
  
  fetchData()
}

// 计算得分
const calculateScore = (amount) => {
  // 如果金额为空或者0，得分为0
  if (!amount) {
    formState.score = 0;
    return;
  }
  
  // 如果当前单位是元，需要先转换为万元再计算得分
  let amountInWan = amount;
  if (amountUnit.value === '元') {
    amountInWan = convertAmount(amount, '元', '万元');
  }
  
  let warningMessages = [];
  
  // 1. 基本得分规则：B = {⌊经费⌋ (经费≤20万时); 20 (经费>20万时)}
  // 注意这里取整，如5.8万取整为5分
  if (amountInWan <= 20) {
    formState.score = Math.floor(amountInWan);
  } else {
    formState.score = 20;
  }
  
  // 2. 横向经费特殊规则：单项≥100万视为省级课题
  let isProvincialLevel = formState.researchLevel === 'provincial' || formState.researchLevel === 'national';
  let autoProvincialLevel = false;
  
  if (formState.type === 'horizontal' && amountInWan >= 100) {
    // 横向课题单项到账经费大于等于100万视为省级课题
    isProvincialLevel = true;
    autoProvincialLevel = true;
    
    // 添加提示信息
    warningMessages.push('横向经费≥100万，视为省级课题');
  }
  
  // 3. 检查导师类型要求
  if (formState.tutorType?.includes('博导')) {
    if (formState.researchLevel !== 'national' && !warningMessages.includes('博导需要主持在研国家级项目')) {
      warningMessages.push('博导需要主持在研国家级项目');
    }
  }
  
  if (formState.tutorType?.includes('硕导')) {
    if (!isProvincialLevel && !warningMessages.includes('硕导需要主持在研省级及以上课题')) {
      warningMessages.push('硕导需要主持在研省级及以上课题');
    }
  }
  
  // 4. 更新描述字段，添加警告信息
  if (warningMessages.length > 0) {
    const warningText = warningMessages.join('\n');
    
    if (!formState.description) {
      formState.description = warningText;
    } else if (!formState.description.includes(warningText)) {
      // 避免重复添加相同的警告信息
      formState.description = `${formState.description}\n\n${warningText}`;
    }
  }
  
  // 记录计算过程到控制台
  console.log('计算得分：', {
    原始金额: amount,
    万元金额: amountInWan,
    单位: amountUnit.value,
    经费类型: formState.type,
    研究级别: formState.researchLevel,
    自动判定为省级: autoProvincialLevel,
    导师类型: formState.tutorType,
    警告信息: warningMessages,
    计算公式说明: amountInWan <= 20 
      ? `经费${amountInWan}万元，取整为${Math.floor(amountInWan)}分` 
      : '经费>20万元，得分为20分',
    最终得分: formState.score
  });
};

// 添加对类型、研究级别变化的监听，确保变化时重新计算得分
watch([() => formState.type, () => formState.researchLevel, () => formState.tutorType], () => {
  calculateScore(formState.amount);
});

// 处理编辑
const handleEdit = (record) => {
  console.log('编辑记录:', record);
  
  // 设置编辑模式
  isEdit.value = true;
  currentRecord.value = record;
  
  // 重置表单为空白状态
  resetForm();
  
  // 使用 loading 状态记录加载中
  confirmLoading.value = true;
  
  // 获取经费详情
  getResearchFundDetail(record.id)
    .then(res => {
      if (res && res.code === 200) {
        console.log('获取经费详情成功:', res.data);
        
        // 填充表单数据
        const fundData = res.data;
        formState.name = fundData.name;
        formState.type = fundData.type;
        
        // 默认以万元为单位显示
        formState.amount = fundData.amount;
        amountUnit.value = '万元';
        
        formState.startDate = dayjs(fundData.startDate);
        formState.endDate = dayjs(fundData.endDate);
        // 负责人使用ID
        formState.leader = fundData.leader;
        
        // 成员字段处理
        let memberIds = [];
        if (fundData.members) {
          // 如果是字符串，尝试解析为ID数组
          if (typeof fundData.members === 'string') {
            memberIds = fundData.members.split(',');
          } else if (Array.isArray(fundData.members)) {
            memberIds = fundData.members;
          }
        }
        formState.members = memberIds;
        
        formState.description = fundData.description;
        formState.score = fundData.score;
        formState.tutorType = fundData.tutorType || [];
        formState.researchLevel = fundData.researchLevel || '';
        
        // 打开模态框
        modalVisible.value = true;
      } else {
        // 无法获取详情，使用表格中的数据
        console.warn('获取经费详情失败，使用表格数据:', record);
        
        formState.name = record.name;
        formState.type = record.type;
        
        // 默认以万元为单位显示
        formState.amount = record.amount;
        amountUnit.value = '万元';
        
        formState.startDate = dayjs(record.startDate);
        formState.endDate = dayjs(record.endDate);
        // 负责人使用ID
        formState.leader = record._originalLeader || record.leader;
        
        // 成员字段处理
        let memberIds = [];
        if (record._originalMembers) {
          // 如果是字符串，尝试解析为ID数组
          if (typeof record._originalMembers === 'string') {
            memberIds = record._originalMembers.split(',');
          } else if (Array.isArray(record._originalMembers)) {
            memberIds = record._originalMembers;
          }
        }
        formState.members = memberIds;
        
        formState.description = record.description || '';
        formState.score = record.score;
        formState.tutorType = record.tutorType || [];
        formState.researchLevel = record.researchLevel || '';
        
        // 打开模态框
        modalVisible.value = true;
      }
    })
    .catch(error => {
      console.error('获取经费详情失败:', error);
      message.error('获取经费详情失败');
      
      // 数据加载失败也要关闭loading状态
      confirmLoading.value = false;
    })
    .finally(() => {
      // 无论成功失败，都要关闭loading状态
      confirmLoading.value = false;
    });
}

// 处理删除
const handleDelete = (record) => {
  deleteResearchFund(record.id)
    .then(res => {
      if (res && res.code === 200) {
        message.success('删除成功')
        fetchData() // 重新加载数据
      } else {
        message.error(res?.message || '删除失败')
      }
    })
    .catch(error => {
      console.error('删除经费失败:', error)
      message.error('删除失败')
    })
}

// 添加单位转换相关状态
const amountUnit = ref('万元'); // 默认单位是万元
const showUnitSlider = ref(false); // 控制拖动条显示

// 单位转换处理函数
const convertAmount = (value, fromUnit, toUnit) => {
  if (!value) return 0;
  
  const numValue = parseFloat(value);
  if (isNaN(numValue)) return 0;
  
  if (fromUnit === toUnit) return numValue;
  
  // 元转万元
  if (fromUnit === '元' && toUnit === '万元') {
    return parseFloat((numValue / 10000).toFixed(2));
  }
  
  // 万元转元
  if (fromUnit === '万元' && toUnit === '元') {
    return parseFloat((numValue * 10000).toFixed(2));
  }
  
  return numValue;
};

// 处理单位切换
const handleUnitChange = () => {
  // 切换单位时，转换金额
  const currentAmount = formState.amount;
  const currentUnit = amountUnit.value;
  const newUnit = currentUnit === '元' ? '万元' : '元';
  
  formState.amount = convertAmount(currentAmount, currentUnit, newUnit);
  amountUnit.value = newUnit;
};

// 在提交表单前转换金额为万元
const convertToWanYuanBeforeSubmit = () => {
  if (amountUnit.value === '元') {
    formState.amount = convertAmount(formState.amount, '元', '万元');
  }
};

// 模态框确认
const handleModalOk = () => {
  // 显示正在处理
  console.log('点击保存按钮');
  confirmLoading.value = true;
  
  // 使用表单校验
  formRef.value.validate().then(async () => {
    console.log('表单验证通过');
    
    // 转换金额为万元单位（如果当前是元）
    convertToWanYuanBeforeSubmit();
    
    // 如果处于个人视图且是添加模式，确保leader是当前用户
    if (showPersonalFunds.value && !isEdit.value) {
      const currentUserId = await getUserId(true);
      if (currentUserId) {
        console.log('处于个人视图，强制使用当前用户ID:', currentUserId);
        formState.leader = currentUserId;
      }
    }
    
    // 构建经费对象
    const fund = {
      name: formState.name,
      type: formState.type,
      amount: formState.amount,
      startDate: formState.startDate.format('YYYY-MM-DD'),
      endDate: formState.endDate.format('YYYY-MM-DD'),
      leader: formState.leader,
      members: Array.isArray(formState.members) ? formState.members.join(',') : formState.members,
      description: formState.description,
      score: formState.score,
      tutorType: formState.tutorType,
      researchLevel: formState.researchLevel
    };
    
    console.log('提交数据:', fund);
    
    if (isEdit.value) {
      // 编辑模式
      updateResearchFund(currentRecord.value.id, fund)
        .then(res => {
          if (res && res.code === 200) {
            message.success('更新成功')
            modalVisible.value = false
            fetchData() // 重新加载数据
          } else {
            message.error(res?.message || '更新失败')
          }
        })
        .catch(error => {
          console.error('更新经费失败:', error)
          message.error('更新失败')
        })
        .finally(() => {
          confirmLoading.value = false
        })
    } else {
      // 添加模式
      addResearchFund(fund)
        .then(res => {
          if (res && res.code === 200) {
            message.success('添加成功')
            modalVisible.value = false
            fetchData() // 重新加载数据
          } else {
            message.error(res?.message || '添加失败')
          }
        })
        .catch(error => {
          console.error('添加经费失败:', error)
          message.error('添加失败')
        })
        .finally(() => {
          confirmLoading.value = false
        })
    }
  }).catch(errors => {
    console.log('表单验证失败:', errors);
    confirmLoading.value = false;
    
    // 显示每个字段的错误信息
    const errorFields = errors.errorFields || [];
    if (errorFields.length > 0) {
      errorFields.forEach(field => {
        message.error(`${field.name.join('.')}: ${field.errors.join(', ')}`);
      });
    }
  });
};

// 处理导入
const handleImportChange = (info) => {
  if (info.file.status === 'done') {
    importResearchFunds(info.file.originFileObj)
      .then(res => {
        if (res && res.code === 200) {
          message.success(`${info.file.name} 文件上传成功`)
          fetchData() // 重新加载数据
        } else {
          message.error(res?.message || `${info.file.name} 文件上传失败`)
        }
      })
      .catch(error => {
        console.error('导入经费数据失败:', error)
        message.error(`${info.file.name} 文件上传失败`)
      })
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} 文件上传失败`)
  }
}

// 处理导出
const handleExport = () => {
  exportResearchFunds()
    .then(res => {
      if (res) {
        // 创建下载链接
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = '科研经费数据.xlsx'
        link.click()
        message.success('数据导出成功')
      } else {
        message.error('导出失败')
      }
    })
    .catch(error => {
      console.error('导出经费数据失败:', error)
      message.error('导出失败')
    })
}

// 显示添加模态框
const showAddModal = () => {
  console.log('显示添加经费弹窗');
  // 设置为添加模式
  isEdit.value = false;
  currentRecord.value = null;
  
  // 重置表单
  resetForm();
  
  // 重置金额单位为默认值
  amountUnit.value = '万元';
  showUnitSlider.value = false;
  
  // 在个人视图时，自动设置项目负责人为当前用户
  if (showPersonalFunds.value) {
    // 获取当前用户信息并设置为项目负责人
    getUserId(true).then(currentUserId => {
      if (currentUserId) {
        // 查找对应的名称
        const currentUser = memberOptions.value.find(option => option.value === currentUserId);
        
        if (currentUser) {
          formState.leader = currentUser.value;
          console.log('自动设置项目负责人为当前用户:', currentUser.label);
        } else {
          // 如果在成员选项中找不到当前用户，直接使用ID
          formState.leader = currentUserId;
          console.log('无法在成员选项中找到当前用户，使用ID:', currentUserId);
        }
      }
    });
  }
  
  // 打开模态框
  modalVisible.value = true;
  console.log('modalVisible设置为:', modalVisible.value);
}

// 重置表单
const resetForm = () => {
  // 设置所有表单字段为初始值
  formState.name = ''
  formState.type = 'vertical' // 设置默认值，避免校验错误
  formState.amount = 0
  
  // 设置日期为今天和一年后，避免日期校验错误
  const today = dayjs()
  formState.startDate = today
  formState.endDate = today.add(1, 'year')
  
  formState.leader = ''
  formState.members = []
  formState.description = ''
  formState.score = 0
  formState.tutorType = []
  formState.researchLevel = ''
  
  // 清空文件列表
  fileList.value = []
  
  // 重新计算得分
  calculateScore(0)
}

// 禁用开始日期
const disabledStartDate = (current) => {
  if (formState.endDate) {
    return current && current > formState.endDate
  }
  return false
}

// 禁用结束日期
const disabledEndDate = (current) => {
  if (formState.startDate) {
    return current && current < formState.startDate
  }
  return false
}

// 模拟成员选项
const memberOptions = ref([])

// 获取教师列表作为成员选项
const fetchTeachers = async () => {
  try {
    // 导入和使用teachersList API
    const { teachersList } = await getTeacherList({})
    console.log('获取教师列表响应:', teachersList)
    if (teachersList && teachersList.code === 200) {
      memberOptions.value = (teachersList.data.result || []).map(teacher => ({
        label: teacher.nickname || teacher.username,
        value: teacher.id.toString()
      }))
      
      // 更新用户ID到名称的映射
      updateUserIdToNameMap()
      
      // 如果已经加载了数据，重新处理显示名称
      if (dataSource.value.length > 0) {
        dataSource.value = processTableData(dataSource.value.map(item => ({
          ...item,
          leader: item._originalLeader || item.leader,
          members: item._originalMembers || item.members
        })))
      }
    } else {
      console.error('获取教师列表失败:', teachersList)
      // 如果API调用失败，使用空数组
      memberOptions.value = []
    }
  } catch (error) {
    console.error('获取教师列表失败:', error)
    // 如果出现异常，使用空数组
    memberOptions.value = []
  }
}

// 模态框取消
const handleModalCancel = () => {
  console.log('取消操作');
  
  // 关闭模态框
  modalVisible.value = false;
  
  // 重置表单
  resetForm();
}

// 初始化图表
const initCharts = () => {
  // 经费类型分布图
  typeChart = echarts.init(typeChartRef.value)
  const typeData = {}
  dataSource.value.forEach(item => {
    const typeName = typeNameMap[item.type] || item.type
    typeData[typeName] = (typeData[typeName] || 0) + 1
  })
  
  typeChart.setOption({
    title: {
      text: '经费类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: Object.keys(typeData)
    },
    series: [
      {
        name: '经费类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: Object.entries(typeData).map(([name, value]) => ({ name, value }))
      }
    ]
  })
  
  // 经费金额分布图
  amountChart = echarts.init(amountChartRef.value)
  const amountRanges = [
    { min: 0, max: 10, name: '0-10万' },
    { min: 10, max: 20, name: '10-20万' },
    { min: 20, max: 50, name: '20-50万' },
    { min: 50, max: 100, name: '50-100万' },
    { min: 100, max: 1000, name: '100万以上' }
  ]
  
  const amountData = amountRanges.map(range => {
    const count = dataSource.value.filter(item => 
      item.amount > range.min && item.amount <= range.max
    ).length
    return { name: range.name, value: count }
  })
  
  amountChart.setOption({
    title: {
      text: '经费金额分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: amountRanges.map(range => range.name)
    },
    series: [
      {
        name: '金额范围',
        type: 'pie',
        radius: '55%',
        center: ['50%', '60%'],
        data: amountData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
  
  // 经费时间分布图
  timeChart = echarts.init(timeChartRef.value)
  const timeData = {}
  dataSource.value.forEach(item => {
    const month = item.startDate.substring(0, 7) // 格式：YYYY-MM
    timeData[month] = (timeData[month] || 0) + 1
  })
  
  timeChart.setOption({
    title: {
      text: '经费时间分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: Object.keys(timeData).sort(),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '项目数量',
        type: 'bar',
        data: Object.keys(timeData).sort().map(key => timeData[key]),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      }
    ]
  })
  
  // 经费得分分布图
  scoreChart = echarts.init(scoreChartRef.value)
  const scoreRanges = [
    { min: 0, max: 10, name: '0-10分' },
    { min: 10, max: 20, name: '10-20分' },
    { min: 20, max: 50, name: '20-50分' },
    { min: 50, max: 100, name: '50-100分' }
  ]
  
  const scoreData = scoreRanges.map(range => {
    const count = dataSource.value.filter(item => 
      item.score > range.min && item.score <= range.max
    ).length
    return { name: range.name, value: count }
  })
  
  scoreChart.setOption({
    title: {
      text: '经费得分分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: scoreRanges.map(range => range.name)
    },
    series: [
      {
        name: '得分范围',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: scoreData
      }
    ]
  })
  
  // 项目负责人得分排名图
  leaderRankChart = echarts.init(leaderRankChartRef.value)
  const leaderData = {}
  dataSource.value.forEach(item => {
    leaderData[item.leader] = (leaderData[item.leader] || 0) + item.score
  })
  
  const leaderRankData = Object.entries(leaderData)
    .map(([name, score]) => ({ name, score }))
    .sort((a, b) => b.score - a.score)
  
  leaderRankChart.setOption({
    title: {
      text: '项目负责人得分排名',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: leaderRankData.map(item => item.name)
    },
    series: [
      {
        name: '得分',
        type: 'bar',
        data: leaderRankData.map(item => item.score),
        itemStyle: {
          color: function(params) {
            // 为前三名设置不同颜色
            const colorList = ['#f5222d', '#fa8c16', '#faad14'];
            if (params.dataIndex < 3) {
              return colorList[params.dataIndex];
            }
            return '#1890ff';
          }
        }
      }
    ]
  })
  
  // 经费金额与得分关系图
  amountScoreChart = echarts.init(amountScoreChartRef.value)
  const amountScoreData = dataSource.value.map(item => [item.amount, item.score])
  
  amountScoreChart.setOption({
    title: {
      text: '经费金额与得分关系',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `项目: ${params.data[2]}<br/>金额: ${params.data[0]}万元<br/>得分: ${params.data[1]}分`;
      }
    },
    xAxis: {
      type: 'value',
      name: '经费金额(万元)',
      nameLocation: 'middle',
      nameGap: 30
    },
    yAxis: {
      type: 'value',
      name: '得分',
      nameLocation: 'middle',
      nameGap: 30
    },
    series: [
      {
        name: '金额-得分',
        type: 'scatter',
        data: dataSource.value.map(item => [item.amount, item.score, item.name]),
        symbolSize: 15,
        itemStyle: {
          color: function(params) {
            // 根据得分设置不同颜色
            const score = params.data[1];
            if (score >= 80) return '#f5222d';
            if (score >= 50) return '#fa8c16';
            if (score >= 20) return '#faad14';
            return '#1890ff';
          }
        },
        emphasis: {
          itemStyle: {
            borderColor: '#333',
            borderWidth: 2
          }
        }
      }
    ]
  })
}

// 更新图表
const updateCharts = () => {
  if (typeChart) typeChart.dispose()
  if (amountChart) amountChart.dispose()
  if (timeChart) timeChart.dispose()
  if (scoreChart) scoreChart.dispose()
  if (leaderRankChart) leaderRankChart.dispose()
  if (amountScoreChart) amountScoreChart.dispose()
  
  initCharts()
}

// 组件挂载后
onMounted(() => {
  fetchData()
  
  // 获取教师列表
  fetchTeachers()
  
  // 延迟一点时间初始化图表，确保DOM已渲染
  setTimeout(() => {
    initCharts()
  }, 500)
})

// 处理导师类型变更
const handleTutorTypeChange = (value, checked) => {
  if (!formState.tutorType) {
    formState.tutorType = [];
  }
  
  if (checked) {
    // 添加值
    if (!formState.tutorType.includes(value)) {
      formState.tutorType.push(value);
    }
  } else {
    // 移除值
    formState.tutorType = formState.tutorType.filter(item => item !== value);
  }
  
  // 重新计算得分
  calculateScore(formState.amount);
}

// 新增负责人选择逻辑
const leaderSelectOpen = ref(false);
const leaderNewTeacherName = ref('');

const handleLeaderDropdownVisibleChange = (visible) => {
  // 如果是要关闭下拉框，检查是否因为输入框操作
  if (!visible && document.activeElement) {
    const activeElement = document.activeElement;
    // 如果当前焦点在输入框或按钮上，则阻止关闭下拉菜单
    if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'BUTTON') {
      // 延迟确保点击事件处理完成后再重新打开
      setTimeout(() => {
        leaderSelectOpen.value = true;
      }, 0);
      return;
    }
  }
  
  // 打开负责人下拉框时，关闭成员下拉框
  if (visible) {
    membersSelectOpen.value = false;
  }
  
  leaderSelectOpen.value = visible;
};

const addLeaderCustomTeacher = () => {
  if (leaderNewTeacherName.value.trim() !== '') {
    // 添加到选项中
    memberOptions.value.push({
      label: leaderNewTeacherName.value,
      value: leaderNewTeacherName.value
    });
    
    // 选择新添加的教师
    formState.leader = leaderNewTeacherName.value;
    
    // 更新用户ID到名称的映射
    userIdToNameMap.value[leaderNewTeacherName.value] = leaderNewTeacherName.value;
    
    // 关闭下拉框并清空输入
    leaderSelectOpen.value = false;
    leaderNewTeacherName.value = '';
    
    // 显示成功提示
    message.success(`已添加: ${leaderNewTeacherName.value}`);
  }
};

// 新增成员选择逻辑
const membersSelectOpen = ref(false);
const membersNewTeacherName = ref('');

const handleMembersDropdownVisibleChange = (visible) => {
  // 如果是要关闭下拉框，检查是否因为输入框操作
  if (!visible && document.activeElement) {
    const activeElement = document.activeElement;
    // 如果当前焦点在输入框或按钮上，则阻止关闭下拉菜单
    if (activeElement.tagName === 'INPUT' || activeElement.tagName === 'BUTTON') {
      // 延迟确保点击事件处理完成后再重新打开
      setTimeout(() => {
        membersSelectOpen.value = true;
      }, 0);
      return;
    }
  }
  
  // 打开成员下拉框时，关闭负责人下拉框
  if (visible) {
    leaderSelectOpen.value = false;
  }
  
  membersSelectOpen.value = visible;
};

const addMembersCustomTeacher = () => {
  if (membersNewTeacherName.value.trim() !== '') {
    memberOptions.value.push({
      label: membersNewTeacherName.value,
      value: membersNewTeacherName.value
    });
    
    // 保留现有选择并添加新成员
    if (!formState.members) {
      formState.members = [];
    }
    formState.members = [...formState.members, membersNewTeacherName.value];
    
    // 更新用户ID到名称的映射
    userIdToNameMap.value[membersNewTeacherName.value] = membersNewTeacherName.value;
    
    // 显示成功提示
    message.success(`已添加: ${membersNewTeacherName.value}`);
    
    // 清空输入并保持下拉框打开
    membersNewTeacherName.value = '';
  }
};
</script>

<style scoped>
.research-funds {
  border: 1px solid #f0f0f0;
  margin: 24px;
}

.table-footer {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.total-score {
  font-size: 16px;
  font-weight: bold;
}

.text-danger {
  color: #ff4d4f;
}
</style> 