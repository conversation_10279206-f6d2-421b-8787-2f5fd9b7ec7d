const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义学术任职级别模型
const AssociationLevel = sequelize.define('associationLevels', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    levelName: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '级别名称（唯一）'
    },
    score: {
        type: DataTypes.STRING(36),
        allowNull: false,
        comment: '对应分数'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'associationLevels',
    timestamps: true,
    indexes: [
        {
            name: 'uk_level',
            fields: ['levelName'],
            unique: true
        }
    ]
});

module.exports = AssociationLevel; 