<template>
  <div class="data-collection">
    <a-card title="数据收集" :bordered="false">
      <a-alert
        message="数据收集说明"
        description="本页面用于收集和管理教师提交的各类数据，包括科研项目、教学获奖、国际交流、社会服务等。您可以查看数据收集进度、导出数据、设置数据收集规则等。"
        type="info"
        show-icon
        class="mb-4"
      />

      <a-row :gutter="16" class="mb-4">
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <TeamOutlined />
                教师总数
              </span>
            </template>
            <div class="card-content">
              <span class="number">128</span>
              <span class="unit">人</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <FileDoneOutlined />
                已提交
              </span>
            </template>
            <div class="card-content">
              <span class="number">98</span>
              <span class="unit">人</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <ClockCircleOutlined />
                待提交
              </span>
            </template>
            <div class="card-content">
              <span class="number">30</span>
              <span class="unit">人</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <template #title>
              <span class="card-title">
                <CheckCircleOutlined />
                完成率
              </span>
            </template>
            <div class="card-content">
              <span class="number">76.5</span>
              <span class="unit">%</span>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="progress" tab="收集进度">
          <a-table :columns="progressColumns" :data-source="progressData" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'progress'">
                <a-progress
                  :percent="record.progress"
                  :status="record.progress === 100 ? 'success' : 'active'"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a @click="viewDetail(record)">查看详情</a>
                  <a-divider type="vertical" />
                  <a @click="sendReminder(record)">发送提醒</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="rules" tab="收集规则">
          <div class="rules-header">
            <a-button type="primary" @click="showRuleModal">
              <template #icon><PlusOutlined /></template>
              添加规则
            </a-button>
          </div>
          <a-table :columns="ruleColumns" :data-source="ruleData" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-switch
                  v-model:checked="record.status"
                  :checkedChildren="'启用'"
                  :unCheckedChildren="'禁用'"
                  @change="(checked) => toggleRuleStatus(record, checked)"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a @click="editRule(record)">编辑</a>
                  <a-divider type="vertical" />
                  <a-popconfirm
                    title="确定要删除该规则吗？"
                    @confirm="deleteRule(record)"
                  >
                    <a class="text-danger">删除</a>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="export" tab="数据导出">
          <a-form :model="exportForm" layout="vertical">
            <a-form-item label="选择数据类型">
              <a-checkbox-group v-model:value="exportForm.dataTypes">
                <a-checkbox value="research">科研项目</a-checkbox>
                <a-checkbox value="teaching">教学获奖</a-checkbox>
                <a-checkbox value="international">国际交流</a-checkbox>
                <a-checkbox value="social">社会服务</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            <a-form-item label="时间范围">
              <a-range-picker v-model:value="exportForm.dateRange" />
            </a-form-item>
            <a-form-item label="导出格式">
              <a-radio-group v-model:value="exportForm.format">
                <a-radio value="excel">Excel</a-radio>
                <a-radio value="csv">CSV</a-radio>
                <a-radio value="pdf">PDF</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="handleExport">
                <template #icon><DownloadOutlined /></template>
                导出数据
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <a-modal
      v-model:visible="ruleModalVisible"
      :title="isEdit ? '编辑规则' : '添加规则'"
      @ok="handleRuleModalOk"
      @cancel="handleRuleModalCancel"
      width="600px"
    >
      <a-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" layout="vertical">
        <a-form-item label="规则名称" name="name">
          <a-input v-model:value="ruleForm.name" placeholder="请输入规则名称" />
        </a-form-item>
        <a-form-item label="数据类型" name="dataType">
          <a-select v-model:value="ruleForm.dataType" placeholder="请选择数据类型">
            <a-select-option value="research">科研项目</a-select-option>
            <a-select-option value="teaching">教学获奖</a-select-option>
            <a-select-option value="international">国际交流</a-select-option>
            <a-select-option value="social">社会服务</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="收集周期" name="cycle">
          <a-select v-model:value="ruleForm.cycle" placeholder="请选择收集周期">
            <a-select-option value="monthly">每月</a-select-option>
            <a-select-option value="quarterly">每季度</a-select-option>
            <a-select-option value="yearly">每年</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="截止日期" name="deadline">
          <a-date-picker v-model:value="ruleForm.deadline" style="width: 100%" />
        </a-form-item>
        <a-form-item label="规则描述" name="description">
          <a-textarea v-model:value="ruleForm.description" :rows="4" placeholder="请输入规则描述" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import {
  TeamOutlined,
  FileDoneOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 当前激活的标签页
const activeTab = ref('progress')

// 进度表格列定义
const progressColumns = [
  {
    title: '数据类型',
    dataIndex: 'dataType',
    key: 'dataType',
  },
  {
    title: '应提交人数',
    dataIndex: 'total',
    key: 'total',
  },
  {
    title: '已提交人数',
    dataIndex: 'submitted',
    key: 'submitted',
  },
  {
    title: '完成进度',
    dataIndex: 'progress',
    key: 'progress',
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 进度数据
const progressData = ref([
  {
    key: '1',
    dataType: '科研项目',
    total: 128,
    submitted: 98,
    progress: 76.5,
  },
  {
    key: '2',
    dataType: '教学获奖',
    total: 128,
    submitted: 95,
    progress: 74.2,
  },
  {
    key: '3',
    dataType: '国际交流',
    total: 128,
    submitted: 85,
    progress: 66.4,
  },
  {
    key: '4',
    dataType: '社会服务',
    total: 128,
    submitted: 90,
    progress: 70.3,
  },
])

// 规则表格列定义
const ruleColumns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    key: 'dataType',
  },
  {
    title: '收集周期',
    dataIndex: 'cycle',
    key: 'cycle',
  },
  {
    title: '截止日期',
    dataIndex: 'deadline',
    key: 'deadline',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 规则数据
const ruleData = ref([
  {
    key: '1',
    name: '科研项目月度收集',
    dataType: '科研项目',
    cycle: '每月',
    deadline: '2023-12-31',
    status: true,
  },
  {
    key: '2',
    name: '教学获奖季度收集',
    dataType: '教学获奖',
    cycle: '每季度',
    deadline: '2023-12-31',
    status: true,
  },
])

// 导出表单
const exportForm = reactive({
  dataTypes: [],
  dateRange: [],
  format: 'excel',
})

// 规则表单
const ruleModalVisible = ref(false)
const isEdit = ref(false)
const ruleFormRef = ref(null)
const ruleForm = reactive({
  name: '',
  dataType: undefined,
  cycle: undefined,
  deadline: null,
  description: '',
})

// 规则表单验证规则
const ruleRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在2-50个字符之间', trigger: 'blur' }
  ],
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  cycle: [
    { required: true, message: '请选择收集周期', trigger: 'change' }
  ],
  deadline: [
    { required: true, message: '请选择截止日期', trigger: 'change' }
  ],
}

// 查看详情
const viewDetail = (record) => {
  console.log('View detail:', record)
  // 这里添加查看详情的逻辑
}

// 发送提醒
const sendReminder = (record) => {
  message.success(`已向未提交的教师发送提醒`)
}

// 显示规则模态框
const showRuleModal = () => {
  isEdit.value = false
  ruleForm.name = ''
  ruleForm.dataType = undefined
  ruleForm.cycle = undefined
  ruleForm.deadline = null
  ruleForm.description = ''
  ruleModalVisible.value = true
}

// 编辑规则
const editRule = (record) => {
  isEdit.value = true
  Object.assign(ruleForm, record)
  ruleModalVisible.value = true
}

// 切换规则状态
const toggleRuleStatus = (record, checked) => {
  record.status = checked
  message.success(`规则已${checked ? '启用' : '禁用'}`)
}

// 删除规则
const deleteRule = (record) => {
  ruleData.value = ruleData.value.filter(item => item.key !== record.key)
  message.success('规则已删除')
}

// 处理规则模态框确认
const handleRuleModalOk = () => {
  ruleFormRef.value.validate().then(() => {
    // 这里添加保存规则的逻辑
    message.success(`${isEdit.value ? '编辑' : '创建'}成功！`)
    ruleModalVisible.value = false
  }).catch(error => {
    console.log('Validation failed:', error)
  })
}

// 处理规则模态框取消
const handleRuleModalCancel = () => {
  ruleModalVisible.value = false
}

// 处理导出
const handleExport = () => {
  if (exportForm.dataTypes.length === 0) {
    message.warning('请至少选择一种数据类型')
    return
  }
  if (!exportForm.dateRange || exportForm.dateRange.length === 0) {
    message.warning('请选择时间范围')
    return
  }
  message.success('数据导出成功')
}
</script>

<style scoped>
.data-collection {
  padding: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-content {
  text-align: center;
}

.card-content .number {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.card-content .unit {
  margin-left: 4px;
  color: #666;
}

.rules-header {
  margin-bottom: 16px;
}

.text-danger {
  color: #ff4d4f;
}
</style> 