const Deduction = require('../../../models/v1/mapping/deductionModel');
const { Teacher } = require('../../../models');
const { v4: uuidv4 } = require('uuid');
const { Op, Sequelize } = require('sequelize');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');
const { User } = require('../../../models');

/**
 * 获取扣分列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeductions = async (req, res) => {
  try {
    const { username, deductionType, startDate, endDate, page = 1, pageSize = 10, userId } = req.query;
    
    // 构建查询条件
    const where = { status: 1 };
    if (username) where.username = { [Op.like]: `%${username}%` };
    if (deductionType) where.deductionType = deductionType;
    if (userId) where.userId = userId;
    
    // 日期范围查询
    if (startDate && endDate) {
      where.deductionDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      where.deductionDate = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.deductionDate = {
        [Op.lte]: new Date(endDate)
      };
    }
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await Deduction.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['deductionDate', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        total: count,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(count / pageSize)
      }
    });
  } catch (error) {
    console.error('获取扣分列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取扣分列表失败',
      data: null
    });
  }
};

/**
 * 获取扣分详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeductionById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const deduction = await Deduction.findByPk(id);
    if (!deduction) {
      return res.status(404).json({
        code: 404,
        message: '扣分记录不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: deduction
    });
  } catch (error) {
    console.error('获取扣分详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取扣分详情失败',
      data: null
    });
  }
};

/**
 * 创建扣分记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createDeduction = async (req, res) => {
  try {
    // 打印请求体内容，便于调试
    console.log('创建扣分记录 - 请求体:', req.body);
    console.log('创建扣分记录 - 查询参数:', req.query);
    
    const { 
      username, 
      deductionType, 
      deductionDate, 
      deductionScore, 
      deductionReason, 
      handleResult,
      remark
    } = req.body;
    
    // 从请求查询参数或当前用户获取userId
    let userId = req.query.userId;
    if (!userId && req.user) {
      userId = req.user.id;
    }
    
    console.log('使用的userId:', userId);
    
    // 创建扣分记录
    const deduction = await Deduction.create({
      id: uuidv4(),
      username,
      deductionType,
      deductionDate: new Date(deductionDate),
      deductionScore,
      deductionReason,
      handleResult: handleResult || '',
      userId,
      status: 1,
      remark: remark || ''
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: deduction
    });
  } catch (error) {
    console.error('创建扣分记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建扣分记录失败',
      data: null
    });
  }
};

/**
 * 更新扣分记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateDeduction = async (req, res) => {
  try {
    // 打印请求体内容，便于调试
    console.log('更新扣分记录 - 请求体:', req.body);
    console.log('更新扣分记录 - 查询参数:', req.query);
    console.log('更新扣分记录 - ID:', req.params.id);
    
    const { 
      username, 
      deductionType, 
      deductionDate, 
      deductionScore, 
      deductionReason, 
      handleResult,
      remark
    } = req.body;
    
    // 从请求查询参数或当前用户获取userId
    let userId = req.query.userId;
    if (!userId && req.user) {
      userId = req.user.id;
    }
    
    console.log('使用的userId:', userId);
    
    // 使用中间件提供的扣分记录
    const deduction = req.deduction;
    
    // 更新扣分记录
    await deduction.update({
      username,
      deductionType,
      deductionDate: new Date(deductionDate),
      deductionScore,
      deductionReason,
      handleResult: handleResult || deduction.handleResult,
      userId,
      remark: remark !== undefined ? remark : (deduction.remark || '')
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: deduction
    });
  } catch (error) {
    console.error('更新扣分记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新扣分记录失败',
      data: null
    });
  }
};

/**
 * 删除扣分记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteDeduction = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.query; // 获取查询参数中的userId
    
    console.log(`删除扣分记录 - ID: ${id}, 用户ID: ${userId}`);
    
    // 使用中间件提供的扣分记录
    const deduction = req.deduction;
    
    // 记录用户操作（如果有userId）
    if (userId) {
      console.log(`用户ID: ${userId} 删除了扣分记录ID: ${id}`);
      // 这里可以添加用户操作日志记录
    }
    
    // 软删除扣分记录
    await deduction.update({ status: 0 });
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除扣分记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除扣分记录失败',
      data: null
    });
  }
};

/**
 * 导入扣分数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importDeductions = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请上传文件',
        data: null
      });
    }
    
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = xlsx.utils.sheet_to_json(worksheet);
    
    if (data.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '文件内容为空',
        data: null
      });
    }
    
    // 处理数据
    const results = {
      total: data.length,
      success: 0,
      failed: 0,
      errors: []
    };
    
    for (const item of data) {
      try {
        const username = item['教师姓名'];
        const deductionType = item['扣分类型'];
        const deductionDate = item['扣分时间'];
        const deductionScore = parseFloat(item['扣分分值'] || 0);
        const deductionReason = item['扣分原因'];
        const handleResult = item['处理结果'] || '';
        
        if (!username || !deductionType || !deductionDate || !deductionReason) {
          results.failed++;
          results.errors.push(`行 ${results.success + results.failed}: 用户名、扣分类型、扣分时间和扣分原因不能为空`);
          continue;
        }
        
        // 获取用户信息而不是教师信息
        const user = await User.findOne({ where: { username: username } });
        if (!user) {
          results.failed++;
          results.errors.push(`行 ${results.success + results.failed}: 用户 ${username} 不存在`);
          continue;
        }
        
        // 创建扣分记录时使用userId
        await Deduction.create({
          id: uuidv4(),
          username,
          deductionType,
          deductionDate: new Date(deductionDate),
          deductionScore,
          deductionReason,
          handleResult,
          userId: user.id,
          status: 1
        });
        
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`行 ${results.success + results.failed}: ${error.message}`);
      }
    }
    
    // 删除临时文件
    fs.unlinkSync(filePath);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: results
    });
  } catch (error) {
    console.error('导入扣分数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导入扣分数据失败',
      data: null
    });
  }
};

/**
 * 导出扣分数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportDeductions = async (req, res) => {
  try {
    const { username, deductionType, startDate, endDate } = req.query;
    
    // 构建查询条件
    const where = { status: 1 };
    if (username) where.username = { [Op.like]: `%${username}%` };
    if (deductionType) where.deductionType = deductionType;
    
    // 日期范围查询
    if (startDate && endDate) {
      where.deductionDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      where.deductionDate = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.deductionDate = {
        [Op.lte]: new Date(endDate)
      };
    }
    
    // 查询数据
    const deductions = await Deduction.findAll({
      where,
      order: [['deductionDate', 'DESC']]
    });
    
    // 创建工作簿和工作表
    const data = deductions.map(item => ({
      '教师姓名': item.username,
      '扣分类型': item.deductionType,
      '扣分时间': new Date(item.deductionDate).toISOString().split('T')[0],
      '扣分分值': item.deductionScore,
      '扣分原因': item.deductionReason,
      '处理结果': item.handleResult || ''
    }));
    
    const worksheet = xlsx.utils.json_to_sheet(data);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, '扣分数据');
    
    // 生成临时文件
    const filePath = path.join(__dirname, '../../../temp', `deduction_data_${Date.now()}.xlsx`);
    
    // 确保目录存在
    const dirPath = path.dirname(filePath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // 写入文件
    xlsx.writeFile(workbook, filePath);
    
    // 发送文件
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=deduction_data_${Date.now()}.xlsx`);
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
    // 删除临时文件
    fileStream.on('end', () => {
      fs.unlinkSync(filePath);
    });
  } catch (error) {
    console.error('导出扣分数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出扣分数据失败',
      data: null
    });
  }
};

/**
 * 获取扣分类型分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeductionTypeDistribution = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // 构建查询条件
    const where = { status: 1 };
    
    // 日期范围查询
    if (startDate && endDate) {
      where.deductionDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }
    
    const results = await Deduction.findAll({
      attributes: [
        'deductionType',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
        [Sequelize.fn('SUM', Sequelize.col('deductionScore')), 'totalScore']
      ],
      where,
      group: ['deductionType'],
      order: [[Sequelize.literal('count'), 'DESC']]
    });
    
    // 格式化返回数据
    const data = results.map(item => ({
      type: item.deductionType,
      count: parseInt(item.getDataValue('count')),
      totalScore: parseFloat(item.getDataValue('totalScore') || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data
    });
  } catch (error) {
    console.error('获取扣分类型分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取扣分类型分布失败',
      data: null
    });
  }
};

/**
 * 获取扣分时间分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeductionTimeDistribution = async (req, res) => {
  try {
    // 获取最近12个月的数据
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 11);
    
    const where = { 
      status: 1,
      deductionDate: {
        [Op.between]: [startDate, endDate]
      }
    };
    
    const results = await Deduction.findAll({
      attributes: [
        [Sequelize.fn('DATE_FORMAT', Sequelize.col('deductionDate'), '%Y-%m'), 'month'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
        [Sequelize.fn('SUM', Sequelize.col('deductionScore')), 'totalScore']
      ],
      where,
      group: [Sequelize.fn('DATE_FORMAT', Sequelize.col('deductionDate'), '%Y-%m')],
      order: [[Sequelize.literal('month'), 'ASC']]
    });
    
    // 格式化返回数据
    const data = [];
    
    // 填充所有月份的数据
    for (let i = 0; i < 12; i++) {
      const date = new Date(startDate);
      date.setMonth(date.getMonth() + i);
      const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      const foundItem = results.find(item => item.getDataValue('month') === monthStr);
      data.push({
        month: monthStr,
        count: foundItem ? parseInt(foundItem.getDataValue('count')) : 0,
        totalScore: foundItem ? parseFloat(foundItem.getDataValue('totalScore') || 0).toFixed(2) : '0.00'
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data
    });
  } catch (error) {
    console.error('获取扣分时间分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取扣分时间分布失败',
      data: null
    });
  }
};

/**
 * 获取教师扣分排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherDeductionRanking = async (req, res) => {
  try {
    const { startDate, endDate, limit = 10 } = req.query;
    
    // 构建查询条件
    const where = { status: 1 };
    
    // 日期范围查询
    if (startDate && endDate) {
      where.deductionDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }
    
    const results = await Deduction.findAll({
      attributes: [
        'username',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
        [Sequelize.fn('SUM', Sequelize.col('deductionScore')), 'totalScore']
      ],
      where,
      group: ['username'],
      order: [[Sequelize.literal('totalScore'), 'DESC']],
      limit: parseInt(limit)
    });
    
    // 格式化返回数据
    const data = results.map(item => ({
      teacherName: item.username,
      count: parseInt(item.getDataValue('count')),
      totalScore: parseFloat(item.getDataValue('totalScore') || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data
    });
  } catch (error) {
    console.error('获取教师扣分排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教师扣分排名失败',
      data: null
    });
  }
};

/**
 * 获取扣分原因分析
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getDeductionReasonAnalysis = async (req, res) => {
  try {
    const { startDate, endDate, limit = 10 } = req.query;
    
    // 构建查询条件
    const where = { status: 1 };
    
    // 日期范围查询
    if (startDate && endDate) {
      where.deductionDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    }
    
    // 查询最常见的扣分原因
    const results = await Deduction.findAll({
      attributes: [
        'deductionReason',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
        [Sequelize.fn('SUM', Sequelize.col('deductionScore')), 'totalScore']
      ],
      where,
      group: ['deductionReason'],
      order: [[Sequelize.literal('count'), 'DESC']],
      limit: parseInt(limit)
    });
    
    // 格式化返回数据
    const data = results.map(item => ({
      reason: item.deductionReason,
      count: parseInt(item.getDataValue('count')),
      totalScore: parseFloat(item.getDataValue('totalScore') || 0).toFixed(2)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data
    });
  } catch (error) {
    console.error('获取扣分原因分析失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取扣分原因分析失败',
      data: null
    });
  }
};

/**
 * 获取个人扣分列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPersonalDeductions = async (req, res) => {
  try {
    const { deductionType, startDate, endDate, page = 1, pageSize = 10 } = req.query;
    
    // 从查询参数中获取 userId
    const userId = req.query.userId;
    console.log(`获取个人扣分列表 - userId: ${userId}`);
    
    // 确保有用户信息
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID参数，请提供userId',
        data: null
      });
    }
    
    // 构建查询条件
    const where = { 
      status: 1,
      userId: userId // 使用查询参数中的 userId
    };
    
    if (deductionType) where.deductionType = deductionType;
    
    // 日期范围查询
    if (startDate && endDate) {
      where.deductionDate = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      };
    } else if (startDate) {
      where.deductionDate = {
        [Op.gte]: new Date(startDate)
      };
    } else if (endDate) {
      where.deductionDate = {
        [Op.lte]: new Date(endDate)
      };
    }
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await Deduction.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['deductionDate', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        total: count,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(count / pageSize)
      }
    });
  } catch (error) {
    console.error('获取个人扣分列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取个人扣分列表失败',
      data: null
    });
  }
};

/**
 * 获取个人扣分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPersonalDeductionStats = async (req, res) => {
  try {
    // 从查询参数中获取 userId
    const userId = req.query.userId;
    console.log(`获取个人扣分统计 - userId: ${userId}`);
    
    // 确保有用户信息
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID参数，请提供userId',
        data: null
      });
    }
    
    // 查询用户的所有扣分记录
    const deductions = await Deduction.findAll({
      where: {
        userId,
        status: 1
      },
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalCount'],
        [Sequelize.fn('SUM', Sequelize.col('deductionScore')), 'totalScore']
      ],
      raw: true
    });
    
    // 获取扣分类型分布
    const typeDistribution = await Deduction.findAll({
      where: {
        userId,
        status: 1
      },
      attributes: [
        'deductionType',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count'],
        [Sequelize.fn('SUM', Sequelize.col('deductionScore')), 'score']
      ],
      group: ['deductionType'],
      raw: true
    });
    
    // 获取扣分时间分布（按月）
    const timeDistribution = await Deduction.findAll({
      where: {
        userId,
        status: 1
      },
      attributes: [
        [Sequelize.fn('MONTH', Sequelize.col('deductionDate')), 'month'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      group: [Sequelize.fn('MONTH', Sequelize.col('deductionDate'))],
      raw: true
    });
    
    // 获取最近5条扣分记录
    const recentDeductions = await Deduction.findAll({
      where: {
        userId,
        status: 1
      },
      order: [['deductionDate', 'DESC']],
      limit: 5
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalCount: deductions[0]?.totalCount || 0,
        totalScore: deductions[0]?.totalScore || 0,
        typeDistribution,
        timeDistribution,
        recentDeductions
      }
    });
  } catch (error) {
    console.error('获取个人扣分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取个人扣分统计失败',
      data: null
    });
  }
}; 