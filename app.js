require('./alias'); // 引入路径别名配置文件

const isDev = process.env.NODE_ENV === 'development';
// 访问不同的 .env 文件
require('dotenv').config({path: isDev ? './.env.development' : './.env.production'});
// app.js
const express = require('express');
const bodyParser = require('body-parser');
const logger = require('morgan');
const chalk = require('chalk');
const cors = require('cors');
const path = require('path');
const session = require('express-session');

const errorHandler = require('./utils/errorHandler');
const apiResponse = require('./utils/apiResponse');

// Session配置
const sessionConfig = {
    secret: process.env.SIGN_KEY || 'your-secret-key',
    resave: true,
    saveUninitialized: true,
    cookie: {
        secure: false,             // ✅ 强制 HTTP 下也能用
        maxAge: 24 * 60 * 60 * 1000,
        httpOnly: true,
        sameSite: 'lax'
    },
    name: 'jnu-session-id'
};

const app = express();

// 中间件
app.use(cors({
    origin: true,
    credentials: true // 允许跨域携带cookie
}));

// 添加会话调试中间件
app.use((req, res, next) => {
    // 捕获每个请求的会话状态
    const originalEnd = res.end;
    
    // 记录请求开始
    console.log(`\n[${new Date().toISOString()}] 请求开始: ${req.method} ${req.url}`);
    console.log(`会话ID: ${req.sessionID || '无'}`);
    //console.log(`Cookie: ${req.headers.cookie || '无'}`);
    
    // 监听响应结束
    res.end = function(...args) {
        console.log(`\n[${new Date().toISOString()}] 请求结束: ${req.method} ${req.url}`);
        console.log(`会话ID: ${req.sessionID || '无'}`);
        console.log(`响应状态: ${res.statusCode}`);
        
        if (req.url.includes('/captcha')) {
            console.log(`验证码路由: 会话中验证码=${req.session.code || '无'}`);
        }
        
        // 调用原始的end方法
        return originalEnd.apply(this, args);
    };
    
    next();
});

// 设置响应头部，确保跨域请求正确处理cookie
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin);
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    next();
});

app.use(session(sessionConfig));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 设置响应头部，禁用缓存
app.use((req, res, next) => {
  res.set({
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Surrogate-Control': 'no-store'
  });
  console.log(`设置缓存控制头: ${req.method} ${req.url}`);
  next();
});

// 使用swagger API文档
const options = require('@config/swaggerConfig');
const expressSwagger = require('express-swagger-generator')(app);
expressSwagger(options);

// 开发环境启动请求日志
isDev && app.use(logger('dev'));

const db = require("./models/v1");
// db.sequelize.sync();

// 初始化定时任务系统
const { initScheduler } = require('./utils/scheduler');
try {
    initScheduler();
    console.log('定时任务系统初始化成功');
} catch (error) {
    console.error('定时任务系统初始化失败:', error);
}

// 静态文件
app.use(express.static(path.join(__dirname, 'public')));

// 导入路由
const routes = require('./routes');
app.use(routes);

// 404处理
app.all('*', function (req, res) {
    return apiResponse.notFoundResponse(res, '404 - 接口不存在');
});

// 错误处理
app.use(errorHandler);

// 端口配置
const PORT = process.env.PORT || 3000;

// 启动服务器
const server = app.listen(PORT, () => {
    console.log(chalk.hex('#031dc9').bold(`****************************************************`));
    console.log(chalk.hex('#6dff00').bold(`【接口地址】: ${process.env.URL}:${PORT}/v1`));
    console.log(chalk.hex('#6dff00').bold(`【文档地址】: ${process.env.URL}:${PORT}/swagger`));
    console.log(chalk.hex('#8e44ad').bold(`【启动环境】：${isDev ? '开发环境' : '生产环境'}`));
    console.log(chalk.hex('#10d7a0').bold(`【项目名称】：暨南大学绩效评定系统`));
    console.log(chalk.hex('#031dc9').bold(`****************************************************`));
});

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
});

process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，关闭HTTP服务器');
    server.close(() => {
        console.log('HTTP服务器已关闭');
        process.exit(0);
    });
});

module.exports = app;
