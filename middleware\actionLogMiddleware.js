const userOptLogModel = require('../models/v1/mapping/userOptLogModel');

/**
 * 操作日志记录中间件
 * @param {Object} options 日志选项
 * @returns {Function} 中间件函数
 */
const actionRecords = (options = {}) => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // 保存原始的res.json方法
    const originalJson = res.json;
    let responseBody;
    
    // 重写res.json方法，以捕获响应内容
    res.json = function(body) {
      responseBody = body;
      return originalJson.call(this, body);
    };
    
    // 当请求结束时记录日志
    res.on('finish', async () => {
      try {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // 获取请求信息
        const { method, originalUrl, body, ip } = req;
        const userId = req.user?.id || null;
        const username = req.user?.username || '未登录用户';
        
        // 获取响应状态
        const status = res.statusCode;
        const success = status >= 200 && status < 400;
        
        // 确定操作类型
        let operation = options.module || method;
        if (originalUrl.includes('login')) {
          operation = '登录';
        } else if (originalUrl.includes('logout')) {
          operation = '登出';
        } else if (method === 'POST' && originalUrl.includes('create')) {
          operation = '创建';
        } else if (method === 'POST' && originalUrl.includes('update')) {
          operation = '更新';
        } else if (method === 'POST' && originalUrl.includes('delete')) {
          operation = '删除';
        } else if (method === 'POST' && originalUrl.includes('import')) {
          operation = '导入';
        } else if (method === 'POST' && originalUrl.includes('export')) {
          operation = '导出';
        }
        
        // 处理请求参数 - 移除敏感信息
        const sanitizedBody = { ...body };
        if (sanitizedBody.password) sanitizedBody.password = '******';
        if (sanitizedBody.code) sanitizedBody.code = '******';
        
        // 处理响应内容 - 截断token
        let responseStr = '';
        if (responseBody) {
          try {
            const responseJson = JSON.stringify(responseBody);
            // 如果响应包含token，进行截断处理
            if (responseJson.includes('token')) {
              const parsedResponse = JSON.parse(responseJson);
              if (parsedResponse.data && parsedResponse.data.token) {
                // 截断token只显示前20个字符
                const token = parsedResponse.data.token;
                parsedResponse.data.token = token.substring(0, 20) + '...';
              }
              responseStr = JSON.stringify(parsedResponse);
            } else {
              responseStr = responseJson;
            }
            // 限制响应内容长度
            responseStr = responseStr.substring(0, 300);
          } catch (e) {
            responseStr = '响应内容解析错误';
            console.error('响应内容解析错误:', e);
          }
        }
        
        // 创建日志记录
        await userOptLogModel.create({
          operatorId: userId,
          operator: username,
          module: operation,
          platform: req.headers['user-agent'] ? req.headers['user-agent'].substring(0, 100) : '未知',
          operatorIP: ip || req.headers['x-forwarded-for'] || '127.0.0.1',
          content: JSON.stringify({
            method,
            url: originalUrl,
            requestParams: sanitizedBody,
            responseStatus: status,
            success,
            duration,
            responsePreview: responseStr
          })
        });
      } catch (error) {
        console.error('日志记录失败:', error);
        // 日志记录失败不影响主流程
      }
    });
    
    next();
  };
};

module.exports = {
  actionRecords
}; 