// routes/userRoutes.js
const express = require('express');
const roleController = require('../../../controllers/v1/system/roleController');

const router = express.Router();

/**
 * 获取所有角色
 * @route POST /v1/sys/role/list
 * @group 角色管理 - 系统角色相关接口
 * @param {object} query.body - 查询参数 {params: {}, pagination: {current: 1, pageSize: 15}, sort: {columnKey: 'createdAt', order: 'ascend'}}
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {current: 1, pageSize: 15, total: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', roleController.getAll);

/**
 * 创建角色
 * @route POST /v1/sys/role/create
 * @group 角色管理 - 系统角色相关接口
 * @param {string} roleName.body.required - 角色名称
 * @param {string} roleAuth.body.required - 角色标识
 * @param {string} description.body - 角色描述
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "角色ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', roleController.create);

/**
 * 获取指定角色
 * @route POST /v1/sys/role/findOne
 * @group 角色管理 - 系统角色相关接口
 * @param {number} id.body.required - 角色ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {角色详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/findOne', roleController.findOne);

/**
 * 更新角色
 * @route POST /v1/sys/role/update
 * @group 角色管理 - 系统角色相关接口
 * @param {number} id.body.required - 角色ID
 * @param {string} roleName.body - 角色名称
 * @param {string} roleAuth.body - 角色标识
 * @param {string} description.body - 角色描述
 * @param {array} perms.body - 权限标识列表
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update', roleController.update);

/**
 * 删除角色
 * @route POST /v1/sys/role/delete
 * @group 角色管理 - 系统角色相关接口
 * @param {number} id.body.required - 角色ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', roleController.delete);

/**
 * 角色分配权限
 * @route POST /v1/sys/role/assignAuthority
 * @group 角色管理 - 系统角色相关接口
 * @param {number} id.body.required - 角色ID
 * @param {array} permIds.body.required - 权限ID数组
 * @returns {object} 200 - {code: 200, message: "分配成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/assignAuthority', roleController.assignAuthority);

module.exports = router;
