const { body, param, validationResult } = require('express-validator');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const userLevelsModel = require('@models/v1/mapping/userLevelsModel');
const { getUserInfoFromRequest } = require('@utils/others');

/**
 * 获取用户级别列表
 * @route GET /v1/sys/user-levels
 * @group 用户级别管理
 * @param {string} levelName.query - 级别名称（模糊搜索）
 * @param {number} status.query - 状态筛选
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @returns {object} 200 - 成功返回用户级别列表
 */
exports.getUserLevels = async (req, res) => {
    try {
        const { levelName, status, page = 1, pageSize = 10 } = req.query;
        const where = {};
        
        if (levelName) {
            where.levelName = { [Op.like]: `%${levelName}%` };
        }
        
        if (status !== undefined) {
            where.status = status;
        }

        const offset = (page - 1) * pageSize;
        const limit = parseInt(pageSize);

        const { count, rows } = await userLevelsModel.findAndCountAll({
            where,
            order: [['sort', 'ASC'], ['createdAt', 'ASC']],
            offset,
            limit
        });

        return res.status(200).json({
            code: 200,
            message: '获取用户级别列表成功',
            data: {
                list: rows,
                pagination: {
                    total: count,
                    page: parseInt(page),
                    pageSize: limit,
                    totalPages: Math.ceil(count / limit)
                }
            }
        });
    } catch (error) {
        console.error('获取用户级别列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取用户级别列表失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 获取所有用户级别（不分页）
 * @route GET /v1/sys/user-levels/all
 * @group 用户级别管理
 * @returns {object} 200 - 成功返回所有用户级别
 */
exports.getAllUserLevels = async (_req, res) => {
    try {
        const levels = await userLevelsModel.findAll({
            where: { status: 1 },
            order: [['sort', 'ASC'], ['createdAt', 'ASC']],
            attributes: ['id', 'levelName', 'description', 'sort', 'weight']
        });

        return res.status(200).json({
            code: 200,
            message: '获取所有用户级别成功',
            data: levels
        });
    } catch (error) {
        console.error('获取所有用户级别失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取所有用户级别失败: ' + error.message,
            data: null
        });
    }
};

/**
 * 获取用户级别详情
 * @route GET /v1/sys/user-levels/:id
 * @group 用户级别管理
 * @param {string} id.path - 用户级别ID
 * @returns {object} 200 - 成功返回用户级别详情
 */
exports.getUserLevelDetail = [
    param('id').notEmpty().withMessage('用户级别ID不能为空'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const level = await userLevelsModel.findByPk(req.params.id);
            if (!level) {
                return res.status(404).json({
                    code: 404,
                    message: '用户级别不存在',
                    data: null
                });
            }

            return res.status(200).json({
                code: 200,
                message: '获取用户级别详情成功',
                data: level
            });
        } catch (error) {
            console.error('获取用户级别详情失败:', error);
            return res.status(500).json({
                code: 500,
                message: '获取用户级别详情失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 创建用户级别
 * @route POST /v1/sys/user-levels
 * @group 用户级别管理
 * @param {string} levelName.body - 级别名称
 * @param {string} description.body - 级别描述
 * @param {number} sort.body - 排序
 * @returns {object} 200 - 成功创建用户级别
 */
exports.createUserLevel = [
    body('levelName').notEmpty().withMessage('级别名称不能为空'),
    body('sort').optional().isInt({ min: 0 }).withMessage('排序必须是非负整数'),
    body('weight').optional().isFloat({ min: 0.01, max: 99.99 }).withMessage('权重必须是0.01-99.99之间的数值'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const { levelName, description, sort = 0, weight = 1.00 } = req.body;

            // 检查级别名称是否已存在
            const existingLevel = await userLevelsModel.findOne({
                where: { levelName }
            });

            if (existingLevel) {
                return res.status(400).json({
                    code: 400,
                    message: '该级别名称已存在',
                    data: null
                });
            }

            // 获取当前用户信息
            const userInfo = await getUserInfoFromRequest(req);

            const level = await userLevelsModel.create({
                id: uuidv4(),
                levelName,
                description,
                sort,
                weight,
                status: 1,
                createdBy: userInfo.id
            });

            return res.status(200).json({
                code: 200,
                message: '创建用户级别成功',
                data: level
            });
        } catch (error) {
            console.error('创建用户级别失败:', error);
            return res.status(500).json({
                code: 500,
                message: '创建用户级别失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 更新用户级别
 * @route PUT /v1/sys/user-levels/:id
 * @group 用户级别管理
 * @param {string} id.path - 用户级别ID
 * @param {string} levelName.body - 级别名称
 * @param {string} description.body - 级别描述
 * @param {number} sort.body - 排序
 * @param {number} status.body - 状态
 * @returns {object} 200 - 成功更新用户级别
 */
exports.updateUserLevel = [
    param('id').notEmpty().withMessage('用户级别ID不能为空'),
    body('levelName').optional().notEmpty().withMessage('级别名称不能为空'),
    body('sort').optional().isInt({ min: 0 }).withMessage('排序必须是非负整数'),
    body('weight').optional().isFloat({ min: 0.01, max: 99.99 }).withMessage('权重必须是0.01-99.99之间的数值'),
    body('status').optional().isIn([0, 1]).withMessage('状态值无效'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const level = await userLevelsModel.findByPk(req.params.id);
            if (!level) {
                return res.status(404).json({
                    code: 404,
                    message: '用户级别不存在',
                    data: null
                });
            }

            const { levelName, description, sort, weight, status } = req.body;

            // 如果修改级别名称，检查是否与其他级别重复
            if (levelName && levelName !== level.levelName) {
                const existingLevel = await userLevelsModel.findOne({
                    where: {
                        levelName,
                        id: { [Op.ne]: req.params.id }
                    }
                });

                if (existingLevel) {
                    return res.status(400).json({
                        code: 400,
                        message: '该级别名称已存在',
                        data: null
                    });
                }
            }

            const updateData = {};
            if (levelName !== undefined) updateData.levelName = levelName;
            if (description !== undefined) updateData.description = description;
            if (sort !== undefined) updateData.sort = sort;
            if (weight !== undefined) updateData.weight = weight;
            if (status !== undefined) updateData.status = status;

            await level.update(updateData);

            return res.status(200).json({
                code: 200,
                message: '更新用户级别成功',
                data: level
            });
        } catch (error) {
            console.error('更新用户级别失败:', error);
            return res.status(500).json({
                code: 500,
                message: '更新用户级别失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 删除用户级别
 * @route DELETE /v1/sys/user-levels/:id
 * @group 用户级别管理
 * @param {string} id.path - 用户级别ID
 * @returns {object} 200 - 成功删除用户级别
 */
exports.deleteUserLevel = [
    param('id').notEmpty().withMessage('用户级别ID不能为空'),

    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    code: 400,
                    message: errors.array()[0].msg,
                    data: null
                });
            }

            const level = await userLevelsModel.findByPk(req.params.id);
            if (!level) {
                return res.status(404).json({
                    code: 404,
                    message: '用户级别不存在',
                    data: null
                });
            }

            // 检查是否有用户使用此级别
            const userModel = require('@models/v1/mapping/userModel');
            const userCount = await userModel.count({
                where: { userLevelId: req.params.id }
            });

            if (userCount > 0) {
                return res.status(400).json({
                    code: 400,
                    message: `该级别下还有${userCount}个用户，无法删除`,
                    data: null
                });
            }

            await level.destroy();

            return res.status(200).json({
                code: 200,
                message: '删除用户级别成功',
                data: null
            });
        } catch (error) {
            console.error('删除用户级别失败:', error);
            return res.status(500).json({
                code: 500,
                message: '删除用户级别失败: ' + error.message,
                data: null
            });
        }
    }
];

/**
 * 获取用户级别统计信息
 * @route GET /v1/sys/user-levels/statistics
 * @group 用户级别管理
 * @returns {object} 200 - 成功返回统计信息
 */
exports.getUserLevelStatistics = async (_req, res) => {
    try {
        const levels = await userLevelsModel.findAll({
            where: { status: 1 },
            order: [['sort', 'ASC'], ['createdAt', 'ASC']]
        });

        const userModel = require('@models/v1/mapping/userModel');
        const statistics = await Promise.all(levels.map(async (level) => {
            const userCount = await userModel.count({
                where: { userLevelId: level.id }
            });

            return {
                id: level.id,
                levelName: level.levelName,
                description: level.description,
                sort: level.sort,
                weight: level.weight,
                userCount
            };
        }));

        return res.status(200).json({
            code: 200,
            message: '获取用户级别统计成功',
            data: statistics
        });
    } catch (error) {
        console.error('获取用户级别统计失败:', error);
        return res.status(500).json({
            code: 500,
            message: '获取用户级别统计失败: ' + error.message,
            data: null
        });
    }
};
