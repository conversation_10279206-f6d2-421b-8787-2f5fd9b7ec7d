# 暨南大学基础医学与公共卫生学院教师绩效评定与管理平台

## 项目概述

暨南大学基础医学与公共卫生学院教师绩效评定与管理平台用于管理教师绩效评分，包括科研项目、科研经费、高水平论文、教学与科研获奖、国际交流、社会服务、就业质量等维度的评分管理，以及扣分管理。

## 系统功能

- 教师信息管理
- 科研项目管理
- 科研经费管理
- 高水平论文管理
- 教学与科研获奖管理
- 国际交流管理
- 社会服务管理
- 就业质量管理
- 扣分管理
- 评分统计与分析

## 接口文档

### 1. 接口规范

#### 1.1 基础信息

- 基础URL: `/v1`
- 请求方式: RESTful API (GET, POST, PUT, DELETE)
- 数据格式: JSON
- 认证方式: JWT Token (在请求头中添加 `Authorization: Bearer {token}`)

#### 1.2 通用响应格式

```json
{
  "code": 200,       // 状态码，200表示成功，其他表示失败
  "message": "操作成功", // 响应消息
  "data": {}         // 响应数据
}
```

#### 1.3 分页参数

对于支持分页的接口，使用以下参数：

- `page`: 当前页码，从1开始
- `pageSize`: 每页记录数
- `total`: 总记录数
- `totalPages`: 总页数

### 2. 教师管理接口

#### 2.1 获取教师列表

- **URL**: `/teachers`
- **方法**: GET
- **参数**:
  - `name`: 教师姓名（可选）
  - `title`: 职称（可选）
  - `department`: 所属院系（可选）
  - `status`: 状态（可选，1:在职 0:离职）
  - `page`: 页码
  - `pageSize`: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": "uuid",
        "name": "张三",
        "title": "教授",
        "department": "计算机科学系",
        "status": 1,
        "remark": "备注信息",
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 2.2 获取教师详情

- **URL**: `/teachers/{id}`
- **方法**: GET
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "uuid",
    "name": "张三",
    "title": "教授",
    "department": "计算机科学系",
    "status": 1,
    "remark": "备注信息",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 2.3 创建教师

- **URL**: `/teachers`
- **方法**: POST
- **参数**:
```json
{
  "name": "张三",
  "title": "教授",
  "department": "计算机科学系",
  "status": 1,
  "remark": "备注信息"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "uuid",
    "name": "张三",
    "title": "教授",
    "department": "计算机科学系",
    "status": 1,
    "remark": "备注信息",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 2.4 更新教师

- **URL**: `/teachers/{id}`
- **方法**: PUT
- **参数**:
```json
{
  "name": "张三",
  "title": "教授",
  "department": "计算机科学系",
  "status": 1,
  "remark": "备注信息"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": "uuid",
    "name": "张三",
    "title": "教授",
    "department": "计算机科学系",
    "status": 1,
    "remark": "备注信息",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 2.5 删除教师

- **URL**: `/teachers/{id}`
- **方法**: DELETE
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 3. 科研项目评分接口

#### 3.1 获取科研项目列表

- **URL**: `/research-projects`
- **方法**: GET
- **参数**:
  - `name`: 项目名称（可选）
  - `level`: 项目级别（可选）
  - `type`: 项目类型（可选）
  - `leader`: 项目负责人（可选）
  - `startDate`: 开始时间（可选）
  - `endDate`: 结束时间（可选）
  - `page`: 页码
  - `pageSize`: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": "uuid",
        "name": "项目名称",
        "level": "national_major",
        "type": "national_fund",
        "startDate": "2023-01-01",
        "endDate": "2023-12-31",
        "leader": "张三",
        "members": "李四,王五",
        "description": "项目描述",
        "score": 250.00,
        "status": 1,
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 3.2 获取科研项目详情

- **URL**: `/research-projects/{id}`
- **方法**: GET
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "uuid",
    "name": "项目名称",
    "level": "national_major",
    "type": "national_fund",
    "startDate": "2023-01-01",
    "endDate": "2023-12-31",
    "leader": "张三",
    "members": "李四,王五",
    "description": "项目描述",
    "score": 250.00,
    "status": 1,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 3.3 创建科研项目

- **URL**: `/research-projects`
- **方法**: POST
- **参数**:
```json
{
  "name": "项目名称",
  "level": "national_major",
  "type": "national_fund",
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "leader": "张三",
  "members": ["李四", "王五"],
  "description": "项目描述",
  "score": 250.00,
  "status": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "uuid",
    "name": "项目名称",
    "level": "national_major",
    "type": "national_fund",
    "startDate": "2023-01-01",
    "endDate": "2023-12-31",
    "leader": "张三",
    "members": "李四,王五",
    "description": "项目描述",
    "score": 250.00,
    "status": 1,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 3.4 更新科研项目

- **URL**: `/research-projects/{id}`
- **方法**: PUT
- **参数**:
```json
{
  "name": "项目名称",
  "level": "national_major",
  "type": "national_fund",
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "leader": "张三",
  "members": ["李四", "王五"],
  "description": "项目描述",
  "score": 250.00,
  "status": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": "uuid",
    "name": "项目名称",
    "level": "national_major",
    "type": "national_fund",
    "startDate": "2023-01-01",
    "endDate": "2023-12-31",
    "leader": "张三",
    "members": "李四,王五",
    "description": "项目描述",
    "score": 250.00,
    "status": 1,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 3.5 删除科研项目

- **URL**: `/research-projects/{id}`
- **方法**: DELETE
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 3.6 导入科研项目数据

- **URL**: `/research-projects/import`
- **方法**: POST
- **参数**: 文件上传，支持Excel格式
- **响应**:
```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "total": 100,
    "success": 98,
    "failed": 2,
    "errors": [
      {
        "row": 3,
        "message": "项目名称不能为空"
      }
    ]
  }
}
```

#### 3.7 导出科研项目数据

- **URL**: `/research-projects/export`
- **方法**: GET
- **参数**:
  - `name`: 项目名称（可选）
  - `level`: 项目级别（可选）
  - `type`: 项目类型（可选）
  - `leader`: 项目负责人（可选）
  - `startDate`: 开始时间（可选）
  - `endDate`: 结束时间（可选）
- **响应**: Excel文件下载

### 4. 科研经费评分接口

#### 4.1 获取科研经费列表

- **URL**: `/research-funds`
- **方法**: GET
- **参数**:
  - `name`: 经费名称（可选）
  - `type`: 经费类型（可选）
  - `amount`: 经费金额（可选）
  - `leader`: 负责人（可选）
  - `startDate`: 开始时间（可选）
  - `endDate`: 结束时间（可选）
  - `page`: 页码
  - `pageSize`: 每页记录数
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": "uuid",
        "name": "经费名称",
        "type": "national",
        "amount": 100000.00,
        "leader": "张三",
        "members": "李四,王五",
        "startDate": "2023-01-01",
        "endDate": "2023-12-31",
        "description": "经费描述",
        "score": 100.00,
        "status": 1,
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 4.2 获取科研经费详情

- **URL**: `/research-funds/{id}`
- **方法**: GET
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "uuid",
    "name": "经费名称",
    "type": "national",
    "amount": 100000.00,
    "leader": "张三",
    "members": "李四,王五",
    "startDate": "2023-01-01",
    "endDate": "2023-12-31",
    "description": "经费描述",
    "score": 100.00,
    "status": 1,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 4.3 创建科研经费

- **URL**: `/research-funds`
- **方法**: POST
- **参数**:
```json
{
  "name": "经费名称",
  "type": "national",
  "amount": 100000.00,
  "leader": "张三",
  "members": ["李四", "王五"],
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "description": "经费描述",
  "score": 100.00,
  "status": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "uuid",
    "name": "经费名称",
    "type": "national",
    "amount": 100000.00,
    "leader": "张三",
    "members": "李四,王五",
    "startDate": "2023-01-01",
    "endDate": "2023-12-31",
    "description": "经费描述",
    "score": 100.00,
    "status": 1,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 4.4 更新科研经费

- **URL**: `/research-funds/{id}`
- **方法**: PUT
- **参数**:
```json
{
  "name": "经费名称",
  "type": "national",
  "amount": 100000.00,
  "leader": "张三",
  "members": ["李四", "王五"],
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "description": "经费描述",
  "score": 100.00,
  "status": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": "uuid",
    "name": "经费名称",
    "type": "national",
    "amount": 100000.00,
    "leader": "张三",
    "members": "李四,王五",
    "startDate": "2023-01-01",
    "endDate": "2023-12-31",
    "description": "经费描述",
    "score": 100.00,
    "status": 1,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### 4.5 删除科研经费

- **URL**: `/research-funds/{id}`
- **方法**: DELETE
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 4.6 导入科研经费数据

- **URL**: `/research-funds/import`
- **方法**: POST
- **参数**: 文件上传，支持Excel格式
- **响应**:
```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "total": 100,
    "success": 98,
    "failed": 2,
    "errors": [
      {
        "row": 3,
        "message": "经费名称不能为空"
      }
    ]
  }
}
```

#### 4.7 导出科研经费数据

- **URL**: `/research-funds/export`
- **方法**: GET
- **参数**:
  - `name`: 经费名称（可选）
  - `type`: 经费类型（可选）
  - `amount`: 经费金额（可选）
  - `leader`: 负责人（可选）
  - `startDate`: 开始时间（可选）
  - `endDate`: 结束时间（可选）
- **响应**: Excel文件下载

### 5. 高水平论文评分接口

#### 5.1 获取高水平论文列表

- **URL**: `/sys/papers`
- **方法**: GET
- **参数**:
  - `title`: 论文题目（可选，模糊搜索）
  - `type`: 论文类型（可选）
  - `journal`: 期刊名称（可选，模糊搜索）
  - `author`: 作者（可选，模糊搜索）
  - `publishStartDate`: 发表开始时间（可选）
  - `publishEndDate`: 发表结束时间（可选）
  - `page`: 页码（默认1）
  - `pageSize`: 每页数量（默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": "uuid",
        "title": "论文题目",
        "type": "SCI",
        "journal": "Journal of XX",
        "impactFactor": 3.5,
        "publishDate": "2023-05-15",
        "authors": "张三,李四",
        "correspondingAuthor": "张三",
        "citations": 25,
        "score": 80.5,
        "status": 1,
        "createdAt": "2023-05-16T10:30:00Z",
        "updatedAt": "2023-05-16T10:30:00Z"
      }
    ]
  }
}
```

#### 5.2 获取高水平论文详情

- **URL**: `/sys/papers/{id}`
- **方法**: GET
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "uuid",
    "title": "论文题目",
    "type": "SCI",
    "journal": "Journal of XX",
    "impactFactor": 3.5,
    "publishDate": "2023-05-15",
    "authors": "张三,李四",
    "correspondingAuthor": "张三",
    "citations": 25,
    "score": 80.5,
    "status": 1,
    "createdAt": "2023-05-16T10:30:00Z",
    "updatedAt": "2023-05-16T10:30:00Z"
  }
}
```

#### 5.3 创建高水平论文

- **URL**: `/sys/papers`
- **方法**: POST
- **参数**:
```json
{
  "title": "论文题目",
  "type": "SCI",
  "journal": "Journal of XX",
  "impactFactor": 3.5,
  "publishDate": "2023-05-15",
  "authors": ["张三", "李四"],
  "correspondingAuthor": "张三",
  "citations": 25,
  "score": 80.5,
  "status": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "uuid",
    "title": "论文题目",
    "type": "SCI",
    "journal": "Journal of XX",
    "impactFactor": 3.5,
    "publishDate": "2023-05-15",
    "authors": "张三,李四",
    "correspondingAuthor": "张三",
    "citations": 25,
    "score": 80.5,
    "status": 1,
    "createdAt": "2023-05-16T10:30:00Z",
    "updatedAt": "2023-05-16T10:30:00Z"
  }
}
```

#### 5.4 更新高水平论文

- **URL**: `/sys/papers/{id}`
- **方法**: PUT
- **参数**:
```json
{
  "title": "论文题目(更新)",
  "type": "SCI",
  "journal": "Journal of XX",
  "impactFactor": 3.8,
  "publishDate": "2023-05-15",
  "authors": ["张三", "李四", "王五"],
  "correspondingAuthor": "张三",
  "citations": 30,
  "score": 85.0,
  "status": 1
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": "uuid",
    "title": "论文题目(更新)",
    "type": "SCI",
    "journal": "Journal of XX",
    "impactFactor": 3.8,
    "publishDate": "2023-05-15",
    "authors": "张三,李四,王五",
    "correspondingAuthor": "张三",
    "citations": 30,
    "score": 85.0,
    "status": 1,
    "createdAt": "2023-05-16T10:30:00Z",
    "updatedAt": "2023-05-16T11:15:00Z"
  }
}
```

#### 5.5 删除高水平论文

- **URL**: `/sys/papers/{id}`
- **方法**: DELETE
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 5.6 导入高水平论文数据

- **URL**: `/sys/papers/import`
- **方法**: POST
- **参数**: 文件上传，支持Excel格式
- **响应**:
```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "total": 100,
    "success": 98,
    "failed": 2,
    "errors": [
      {
        "row": 3,
        "message": "论文题目不能为空"
      }
    ]
  }
}
```

#### 5.7 导出高水平论文数据

- **URL**: `/sys/papers/export`
- **方法**: GET
- **参数**:
  - `title`: 论文题目（可选，模糊搜索）
  - `type`: 论文类型（可选）
  - `journal`: 期刊名称（可选，模糊搜索）
  - `author`: 作者（可选，模糊搜索）
  - `publishStartDate`: 发表开始时间（可选）
  - `publishEndDate`: 发表结束时间（可选）
- **响应**: Excel文件下载

### 6. 数据管理接口

#### 6.1 获取数据导入任务列表
- **接口**: GET /data/import-tasks
- **参数**:
  - module: 模块（可选）
  - status: 状态（可选）
  - start_time: 开始时间（可选）
  - end_time: 结束时间（可选）
  - page: 页码（可选，默认1）
  - page_size: 每页数量（可选，默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": 1,
        "module": "teacher",
        "file_name": "teachers.xlsx",
        "status": "completed",
        "total": 100,
        "success": 98,
        "failed": 2,
        "creator": "admin",
        "created_at": "2023-01-01T00:00:00Z",
        "completed_at": "2023-01-01T00:10:00Z"
      }
    ]
  }
}
```

#### 6.2 创建数据导入任务
- **接口**: POST /data/import-tasks
- **参数**: 
  - module: 模块（必填）
  - file: 文件（必填）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "module": "teacher",
    "file_name": "teachers.xlsx",
    "status": "processing",
    "creator": "admin",
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

#### 6.3 获取数据导出任务列表
- **接口**: GET /data/export-tasks
- **参数**:
  - module: 模块（可选）
  - status: 状态（可选）
  - start_time: 开始时间（可选）
  - end_time: 结束时间（可选）
  - page: 页码（可选，默认1）
  - page_size: 每页数量（可选，默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": 1,
        "module": "teacher",
        "file_name": "teachers.xlsx",
        "status": "completed",
        "creator": "admin",
        "created_at": "2023-01-01T00:00:00Z",
        "completed_at": "2023-01-01T00:10:00Z",
        "download_url": "http://example.com/download/teachers.xlsx"
      }
    ]
  }
}
```

#### 6.4 创建数据导出任务
- **接口**: POST /data/export-tasks
- **参数**:
```json
{
  "module": "teacher",
  "filters": {
    "department": "计算机科学系",
    "status": 1
  },
  "fields": ["id", "name", "title", "department"]
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "module": "teacher",
    "status": "processing",
    "creator": "admin",
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

### 7. 表单管理接口

#### 7.1 获取表单列表
- **接口**: GET /forms
- **参数**:
  - type: 表单类型（可选）
  - status: 状态（可选）
  - page: 页码（可选，默认1）
  - page_size: 每页数量（可选，默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": 1,
        "code": "teacher_evaluation",
        "name": "教师评定表",
        "type": "evaluation",
        "description": "用于教师绩效评定的表单",
        "version": "1.0.0",
        "status": "published",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 7.2 获取表单详情
- **接口**: GET /forms/{id}
- **参数**: id - 表单ID
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "code": "teacher_evaluation",
    "name": "教师评定表",
    "type": "evaluation",
    "description": "用于教师绩效评定的表单",
    "version": "1.0.0",
    "status": "published",
    "schema": {
      "fields": [
        {
          "name": "basic_info",
          "label": "基本信息",
          "type": "section",
          "fields": [
            {
              "name": "name",
              "label": "姓名",
              "type": "text",
              "required": true
            },
            {
              "name": "department",
              "label": "所属院系",
              "type": "select",
              "options": [
                {"label": "计算机科学系", "value": "计算机科学系"},
                {"label": "软件工程系", "value": "软件工程系"}
              ],
              "required": true
            }
          ]
        },
        {
          "name": "research_projects",
          "label": "科研项目",
          "type": "table",
          "columns": [
            {"name": "name", "label": "项目名称", "type": "text", "required": true},
            {"name": "level", "label": "项目级别", "type": "select", "options": [], "required": true},
            {"name": "score", "label": "评分", "type": "number", "required": false}
          ]
        }
      ]
    },
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z"
  }
}
```

### 8. 通知管理接口

#### 8.1 获取通知列表
- **接口**: GET /notifications
- **参数**:
  - type: 通知类型（可选）
  - status: 状态（可选）
  - start_time: 开始时间（可选）
  - end_time: 结束时间（可选）
  - page: 页码（可选，默认1）
  - page_size: 每页数量（可选，默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": 1,
        "type": "system",
        "title": "系统维护通知",
        "content": "系统将于今晚22:00进行维护升级",
        "level": "high",
        "status": "unread",
        "sender": "admin",
        "receivers": ["user1", "user2"],
        "created_at": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 8.2 获取通知详情
- **接口**: GET /notifications/{id}
- **参数**: id - 通知ID
- **响应**: 同获取通知列表的单个通知

#### 8.3 发送通知
- **接口**: POST /notifications
- **参数**:
```json
{
  "type": "system",
  "title": "系统维护通知",
  "content": "系统将于今晚22:00进行维护升级",
  "level": "high",
  "receivers": ["user1", "user2"]
}
```
- **响应**: 同获取通知列表的单个通知

#### 8.4 更新通知
- **接口**: PUT /notifications/{id}
- **参数**: 同发送通知
- **响应**: 同发送通知

#### 8.5 删除通知
- **接口**: DELETE /notifications/{id}
- **参数**: id - 通知ID
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 9. 绩效评分管理接口

#### 9.1 获取绩效列表
- **接口**: GET /performance
- **参数**:
  - teacher: 教师姓名（可选）
  - department: 院系（可选）
  - year: 年份（可选）
  - status: 状态（可选）
  - page: 页码（可选，默认1）
  - page_size: 每页数量（可选，默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": 1,
        "teacher_id": 1,
        "teacher_name": "张三",
        "department": "计算机科学系",
        "year": 2023,
        "total_score": 850,
        "research_project_score": 250,
        "research_fund_score": 100,
        "paper_score": 150,
        "award_score": 100,
        "international_score": 50,
        "social_service_score": 20,
        "employment_score": 90,
        "deduction_score": -10,
        "status": "已评定",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 9.2 获取绩效详情
- **接口**: GET /performance/{id}
- **参数**: id - 绩效ID
- **响应**: 同获取绩效列表的单个绩效详情

#### 9.3 计算绩效
- **接口**: POST /performance/calculate
- **参数**:
```json
{
  "teacher_id": 1,
  "year": 2023
}
```
- **响应**: 同获取绩效列表的单个绩效详情

### 10. 首页接口

#### 10.1 获取首页数据概览
- **接口**: GET /home/<USER>
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "teacher_count": 500,
    "research_project_count": 1000,
    "research_fund_count": 800,
    "paper_count": 1500,
    "award_count": 600,
    "top_performers": [
      {
        "id": 1,
        "name": "张三",
        "department": "计算机科学系",
        "score": 950
      },
      {
        "id": 2,
        "name": "李四",
        "department": "软件工程系",
        "score": 920
      }
    ],
    "recent_activities": [
      {
        "id": 1,
        "type": "research_project",
        "title": "新增科研项目",
        "content": "张三添加了国家重点项目",
        "created_at": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 10.2 获取首页图表数据
- **接口**: GET /home/<USER>
- **参数**: 无
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "score_trend": {
      "x_axis": ["2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06"],
      "series": [
        {
          "name": "平均分",
          "data": [800, 820, 830, 840, 850, 870]
        }
      ]
    },
    "dimension_distribution": {
      "dimensions": ["科研项目", "科研经费", "论文", "获奖", "国际交流", "社会服务", "就业质量"],
      "data": [250, 100, 150, 100, 50, 20, 90]
    }
  }
}
```

#### 10.3 获取用户综合数据（含个人评分、趋势和建议）
- **接口**: GET /home/<USER>
- **参数**: 无（需要登录态获取当前用户信息）
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "basic_info": {
      "total_score": 890,
      "school_rank": 5,
      "department_rank": 2,
      "previous_score": 865
    },
    "dimension_scores": [
      {
        "name": "科研项目",
        "score": 250,
        "max": 300,
        "color": "#1890ff"
      },
      {
        "name": "科研经费",
        "score": 18,
        "max": 20,
        "color": "#52c41a"
      },
      {
        "name": "论文发表",
        "score": 150,
        "max": 200,
        "color": "#722ed1"
      },
      {
        "name": "教学获奖",
        "score": 100,
        "max": 150,
        "color": "#faad14"
      },
      {
        "name": "国际交流",
        "score": 50,
        "max": 100,
        "color": "#13c2c2"
      },
      {
        "name": "社会服务",
        "score": 20,
        "max": 50,
        "color": "#fa8c16"
      }
    ],
    "score_trend": {
      "x_axis": ["2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06"],
      "personal_score": [850, 865, 875, 880, 885, 890],
      "department_average": [820, 830, 835, 840, 850, 855]
    },
    "improvement_suggestions": [
      {
        "dimension": "科研项目",
        "suggestion": "建议申报国家级研究项目，提升此项得分",
        "color": "#1890ff"
      },
      {
        "dimension": "论文发表",
        "suggestion": "建议在SCI一区期刊发表论文，提高论文质量",
        "color": "#722ed1"
      },
      {
        "dimension": "国际交流",
        "suggestion": "参与国际学术会议或访问学者项目",
        "color": "#13c2c2"
      },
      {
        "dimension": "社会服务",
        "suggestion": "积极参与社会服务项目，提高影响力",
        "color": "#fa8c16"
      }
    ],
    "department_ranking": [
      {
        "rank": 1,
        "department": "计算机科学系",
        "avgScore": "925.50",
        "count": 45
      },
      {
        "rank": 2,
        "department": "软件工程系",
        "avgScore": "898.75",
        "count": 38
      }
    ]
  }
}
```

#### 10.4 获取数据分析指标
- **接口**: GET /analysis/performance-trend
- **参数**:
```json
{
  "year": 2023,
  "department": "计算机科学系" // 可选
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "years": [2019, 2020, 2021, 2022, 2023],
    "scoreTrend": [
      { "year": 2019, "score": 750 },
      { "year": 2020, "score": 790 },
      { "year": 2021, "score": 820 },
      { "year": 2022, "score": 860 },
      { "year": 2023, "score": 890 }
    ],
    "projectTrend": [
      { "year": 2019, "count": 85 },
      { "year": 2020, "count": 92 },
      { "year": 2021, "count": 98 },
      { "year": 2022, "count": 105 },
      { "year": 2023, "count": 110 }
    ],
    "fundTrend": [
      { "year": 2019, "amount": 980 },
      { "year": 2020, "amount": 1050 },
      { "year": 2021, "amount": 1120 },
      { "year": 2022, "amount": 1200 },
      { "year": 2023, "amount": 1350 }
    ]
  }
}
```

#### 10.5 获取教师职称分布
- **接口**: GET /analysis/teacher-titles
- **参数**:
```json
{
  "department": "计算机科学系" // 可选
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    { "name": "教授", "value": 45 },
    { "name": "副教授", "value": 78 },
    { "name": "讲师", "value": 120 },
    { "name": "助教", "value": 25 }
  ]
}
```

#### 10.6 获取项目级别分布
- **接口**: GET /analysis/project-levels
- **参数**:
```json
{
  "year": 2023,
  "department": "计算机科学系" // 可选
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    { "name": "国家级重大项目", "value": 8 },
    { "name": "国家级重点项目", "value": 15 },
    { "name": "省部级重大项目", "value": 22 },
    { "name": "国家级项目", "value": 35 },
    { "name": "省部级项目", "value": 48 }
  ]
}
```

#### 10.7 获取院系教师分布
- **接口**: GET /analysis/department-teachers
- **参数**:
```json
{
  "status": 1 // 可选，1表示在职
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    { "name": "计算机科学系", "value": 45 },
    { "name": "软件工程系", "value": 38 },
    { "name": "网络工程系", "value": 30 },
    { "name": "数据科学系", "value": 35 }
  ]
}
```

#### 10.8 获取项目年度分布
- **接口**: GET /analysis/project-years
- **参数**:
```json
{
  "start_year": 2019,
  "end_year": 2023,
  "department": "计算机科学系" // 可选
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    { "year": 2019, "count": 85 },
    { "year": 2020, "count": 92 },
    { "year": 2021, "count": 98 },
    { "year": 2022, "count": 105 },
    { "year": 2023, "count": 110 }
  ]
}
```

#### 10.9 获取高级排行榜数据
- **接口**: GET /analysis/ranking
- **参数**:
```json
{
  "year": 2023,
  "type": "score", // score: 总分排名, project: 项目数量排名, fund: 经费排名, paper: 论文排名
  "department": "计算机科学系", // 可选
  "limit": 10 // 可选，默认为10
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "rank": 1,
      "teacher_id": 101,
      "name": "张三",
      "department": "计算机科学系",
      "value": 950, // 根据type不同，可能是分数、项目数量等
      "title": "教授" // 职称
    },
    {
      "rank": 2,
      "teacher_id": 102,
      "name": "李四",
      "department": "软件工程系",
      "value": 920,
      "title": "教授"
    }
  ]
}
```

### 11. 社会服务管理接口

#### 11.1 获取社会服务列表
- **接口**: GET /v1/sys/social-service/list
- **参数**:
  - `name`: 服务名称（可选，模糊搜索）
  - `type`: 服务类型（可选）
  - `target`: 服务对象（可选，模糊搜索）
  - `teacher`: 参与教师（可选，模糊搜索）
  - `startDate`: 开始时间（可选）
  - `endDate`: 结束时间（可选）
  - `status`: 状态（可选，1:正常, 0:已删除）
  - `page`: 页码（默认1）
  - `pageSize`: 每页数量（默认10）
- **响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10,
    "items": [
      {
        "id": "uuid",
        "name": "服务名称",
        "type": "consulting",
        "target": "服务对象",
        "teacher": "参与教师",
        "startDate": "2023-01-01",
        "endDate": "2023-12-31",
        "content": "服务内容",
        "result": "服务成果",
        "score": 20.00,
        "status": 1,
        "createdAt": "2023-01-01T00:00:00Z",
        "updatedAt": "2023-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 11.2 获取社会服务详情
- **接口**: GET /v1/sys/social-service/detail/:id
- **参数**: id - 服务ID
- **响应**: 同获取社会服务列表的单个服务详情

#### 11.3 创建社会服务
- **接口**: POST /v1/sys/social-service/create
- **参数**:
```json
{
  "name": "服务名称",
  "type": "consulting",
  "target": "服务对象",
  "teacher": "参与教师",
  "startDate": "2023-01-01",
  "endDate": "2023-12-31",
  "content": "服务内容",
  "result": "服务成果",
  "score": 20.00,
  "status": 1
}
```
- **响应**: 同获取社会服务列表的单个服务详情

#### 11.4 更新社会服务
- **接口**: PUT /v1/sys/social-service/update/:id
- **参数**: 同创建社会服务
- **响应**: 同创建社会服务

#### 11.5 删除社会服务
- **接口**: DELETE /v1/sys/social-service/delete/:id
- **参数**: id - 服务ID
- **响应**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 11.6 导入社会服务数据
- **接口**: POST /v1/sys/social-service/import
- **参数**: 文件上传，支持Excel格式
- **响应**:
```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "total": 100,
    "success": 98,
    "failed": 2,
    "errors": [
      {
        "row": 3,
        "message": "服务名称不能为空"
      }
    ]
  }
}
```

#### 11.7 导出社会服务数据
- **接口**: GET /v1/sys/social-service/export
- **参数**:
  - `name`: 服务名称（可选，模糊搜索）
  - `type`: 服务类型（可选）
  - `target`: 服务对象（可选，模糊搜索）
  - `teacher`: 参与教师（可选，模糊搜索）
  - `startDate`: 开始时间（可选）
  - `endDate`: 结束时间（可选）
  - `status`: 状态（可选，1:正常, 0:已删除）
- **响应**: Excel文件下载 