## 项目配置

本项目使用环境变量和配置文件统一管理后端API地址和其他配置参数。

### 环境变量文件

项目中包含以下环境变量文件：

- `.env`: 默认环境变量
- `.env.development`: 开发环境变量
- `.env.test`: 测试环境变量
- `.env.production`: 生产环境变量

### 修改后端API地址

如果需要修改后端API地址，可以通过以下方式：

1. 直接修改对应环境的环境变量文件中的 `VITE_API_BASE_URL` 值：

```
# 示例：修改开发环境API地址
VITE_API_BASE_URL=http://new-api-server:8080/v1
```

2. 在启动应用时临时覆盖环境变量：

```bash
# Windows
set VITE_API_BASE_URL=http://new-api-server:8080/v1 && npm run dev

# Linux/Mac
VITE_API_BASE_URL=http://new-api-server:8080/v1 npm run dev
```

### 配置文件

项目配置集中在 `src/config/index.js` 文件中，该文件从环境变量中读取配置并提供默认值。

引用配置示例：

```javascript
import config from '@/config';

// 使用配置
console.log('API地址:', config.apiBaseUrl);
console.log('超时设置:', config.requestTimeout);
``` 