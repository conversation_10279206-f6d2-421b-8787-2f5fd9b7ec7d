const express = require('express');
const router = express.Router();
const textbookPublishingRulesController = require('../../../controllers/v1/textbooks/textbookPublishingRulesController');

/**
 * 获取教材出版规则列表
 * @route GET /v1/sys/textbook-publishing-rules/list
 * @group 教材出版规则管理 - 教材出版规则相关接口
 * @param {string} publisher_level.query - 出版社级别（模糊搜索）
 * @param {number} page.query - 页码，从1开始，默认1
 * @param {number} pageSize.query - 每页数量，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 0, page: 1, pageSize: 10, list: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/list',  textbookPublishingRulesController.getTextbookPublishingRules);

/**
 * 获取教材出版规则详情
 * @route GET /v1/sys/textbook-publishing-rules/detail
 * @group 教材出版规则管理 - 教材出版规则相关接口
 * @param {string} id.query - 规则ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {规则详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail',  textbookPublishingRulesController.getTextbookPublishingRuleDetail);

/**
 * 创建教材出版规则
 * @route POST /v1/sys/textbook-publishing-rules/create
 * @group 教材出版规则管理 - 教材出版规则相关接口
 * @param {string} publisher_level.body.required - 出版社级别
 * @param {number} base_score.body.required - 基础分数
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {创建的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create',  textbookPublishingRulesController.createTextbookPublishingRule);

/**
 * 更新教材出版规则
 * @route PUT /v1/sys/textbook-publishing-rules/update
 * @group 教材出版规则管理 - 教材出版规则相关接口
 * @param {string} id.body.required - 规则ID
 * @param {string} publisher_level.body - 出版社级别
 * @param {number} base_score.body - 基础分数
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {更新后的规则}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/update',  textbookPublishingRulesController.updateTextbookPublishingRule);

/**
 * 删除教材出版规则
 * @route DELETE /v1/sys/textbook-publishing-rules/delete
 * @group 教材出版规则管理 - 教材出版规则相关接口
 * @param {string} id.body.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/delete',  textbookPublishingRulesController.deleteTextbookPublishingRule);

/**
 * 批量删除教材出版规则
 * @route DELETE /v1/sys/textbook-publishing-rules/batch-delete
 * @group 教材出版规则管理 - 教材出版规则相关接口
 * @param {Array} ids.body.required - 规则ID列表
 * @returns {object} 200 - {code: 200, message: "批量删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/batch-delete',  textbookPublishingRulesController.batchDeleteTextbookPublishingRules);

module.exports = router; 