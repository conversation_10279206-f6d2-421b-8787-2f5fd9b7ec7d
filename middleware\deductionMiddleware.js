const User = require('../models/v1/mapping/userModel');
const Teacher = require('../models/v1/mapping/teacherModel');
const { errorResponse } = require('../utils/apiResponse');
const Deduction = require('../models/v1/mapping/deductionModel');

/**
 * 数据验证中间件
 * 简单验证token是否存在及参数合法性
 * 或者验证是否通过query参数传递了userId
 */
exports.checkDeductionPermission = async (req, res, next) => {
  try {
    // 检查请求中是否包含userId参数
    const queryUserId = req.query.userId;
    
    // 如果请求包含userId参数，则允许请求继续
    if (queryUserId) {
      console.log(`请求通过query参数userId: ${queryUserId} 进行验证`);
      return next();
    }
    
    // 否则检查是否已经通过token验证
    if (!req.user || !req.user.id) {
      return errorResponse(res, '未授权访问，请提供有效的token或userId参数', 401);
    }

    // 验证通过，继续执行
    next();
  } catch (error) {
    console.error('权限验证失败:', error);
    return errorResponse(res, '权限验证失败', 500);
  }
};

/**
 * 添加teacher数据中间件
 * 为请求添加当前登录用户的teacher信息
 */
exports.attachTeacherInfo = async (req, res, next) => {
  try {
    const userId = req.user ? req.user.id : null;
    
    if (!userId) {
      return next();
    }

    // 查询用户关联的教师信息
    const teacher = await Teacher.findOne({ where: { userId: userId } });
    
    if (teacher) {
      // 将教师信息添加到请求中，以便控制器使用
      req.teacher = teacher;
    }
    
    next();
  } catch (error) {
    console.error('添加教师信息失败:', error);
    return errorResponse(res, '添加教师信息失败', 500);
  }
};

/**
 * 验证创建扣分记录的必要字段
 */
exports.validateCreateDeduction = async (req, res, next) => {
  try {
    console.log('验证创建扣分数据 - 请求体:', req.body);
    
    // 提取字段，兼容可能的不同字段名
    const username = req.body.username;
    const deductionType = req.body.deductionType;
    const deductionDate = req.body.deductionDate;
    const deductionScore = req.body.deductionScore;
    const deductionReason = req.body.deductionReason;
    
    // 检查userId是否存在
    const userId = req.query.userId || (req.user ? req.user.id : null);
    console.log('验证userId:', userId);
    
    // 校验必填参数
    if (!username) {
      return errorResponse(res, '用户名不能为空', 400);
    }
    
    if (!deductionType) {
      return errorResponse(res, '扣分类型不能为空', 400);
    }
    
    if (!deductionDate) {
      return errorResponse(res, '扣分时间不能为空', 400);
    }
    
    if (deductionScore === undefined || deductionScore === null) {
      return errorResponse(res, '扣分分值不能为空', 400);
    }
    
    if (!deductionReason) {
      return errorResponse(res, '扣分原因不能为空', 400);
    }
    
    if (!userId) {
      return errorResponse(res, '用户ID不能为空，请提供userId参数或使用有效的token', 400);
    }
    
    // 验证通过，继续执行
    next();
  } catch (error) {
    console.error('验证创建扣分记录失败:', error);
    return errorResponse(res, '验证失败', 500);
  }
};

/**
 * 验证更新扣分记录的必要字段
 */
exports.validateUpdateDeduction = async (req, res, next) => {
  try {
    console.log('验证更新扣分数据 - 请求体:', req.body);
    console.log('验证更新扣分数据 - 参数ID:', req.params.id);
    
    // 检查要更新的ID是否存在
    const { id } = req.params;
    if (!id) {
      return errorResponse(res, '缺少记录ID', 400);
    }
    
    // 提取字段，兼容可能的不同字段名
    const username = req.body.username;
    const deductionType = req.body.deductionType;
    const deductionDate = req.body.deductionDate;
    const deductionScore = req.body.deductionScore;
    const deductionReason = req.body.deductionReason;
    
    // 检查userId是否存在
    const userId = req.query.userId || (req.user ? req.user.id : null);
    console.log('验证userId:', userId);
    
    // 校验必填参数
    if (!username) {
      return errorResponse(res, '用户名不能为空', 400);
    }
    
    if (!deductionType) {
      return errorResponse(res, '扣分类型不能为空', 400);
    }
    
    if (!deductionDate) {
      return errorResponse(res, '扣分时间不能为空', 400);
    }
    
    if (deductionScore === undefined || deductionScore === null) {
      return errorResponse(res, '扣分分值不能为空', 400);
    }
    
    if (!deductionReason) {
      return errorResponse(res, '扣分原因不能为空', 400);
    }
    
    if (!userId) {
      return errorResponse(res, '用户ID不能为空，请提供userId参数或使用有效的token', 400);
    }
    
    // 验证通过，继续执行
    next();
  } catch (error) {
    console.error('验证更新扣分记录失败:', error);
    return errorResponse(res, '验证失败', 500);
  }
};

/**
 * 检查记录ID是否存在
 */
exports.checkRecordExists = async (req, res, next) => {
  try {
    const { id } = req.params;
    if (!id) {
      return errorResponse(res, '缺少记录ID', 400);
    }
    
    // 查找扣分记录
    const deduction = await Deduction.findByPk(id);
    if (!deduction) {
      return errorResponse(res, '扣分记录不存在', 404);
    }
    
    // 将找到的记录附加到请求对象中，方便后续处理
    req.deduction = deduction;
    
    // 验证通过，继续执行
    next();
  } catch (error) {
    console.error('检查记录存在失败:', error);
    return errorResponse(res, '验证失败', 500);
  }
};
