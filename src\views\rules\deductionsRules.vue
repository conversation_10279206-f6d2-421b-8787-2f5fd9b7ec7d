<template>
  <div class="app-container">
    <!-- 错误信息展示 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />
    
    <!-- 未按时获得学位扣减规则 -->
    <a-card title="未按时获得学位扣减规则" class="rule-card">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal('degree')">
            <template #icon><plus-outlined /></template>
            新增规则
          </a-button>
        </a-space>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form-wrapper">
        <a-form layout="inline">
          <a-row :gutter="16" style="width: 100%">
            <a-col :span="8">
              <a-form-item label="扣分类型">
                <a-input v-model:value="searchForm.deductionType" placeholder="请输入扣分类型" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item>
                <a-button type="primary" @click="handleSearch">搜索</a-button>
                <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="degreeColumns"
        :data-source="degreeDataSource"
        :loading="loading"
        :pagination="degreePagination"
        @change="handleDegreeTableChange"
        rowKey="id"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="showEditModal(record, 'degree')">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这条规则吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
          <template v-else-if="column.key === 'createdAt'">
            <span>{{ formatDate(record.createdAt) }}</span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 未就业扣减规则 -->
    <a-card title="未就业扣减规则" class="rule-card">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showAddModal('employment')">
            <template #icon><plus-outlined /></template>
            新增规则
          </a-button>
        </a-space>
      </template>

      <!-- 数据表格 -->
      <a-table
        :columns="employmentColumns"
        :data-source="employmentDataSource"
        :loading="loading"
        :pagination="employmentPagination"
        @change="handleEmploymentTableChange"
        rowKey="id"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="showEditModal(record, 'employment')">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这条规则吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
          <template v-else-if="column.key === 'unemploymentDate'">
            <span>{{ formatDate(record.unemploymentDate) }}</span>
          </template>
          <template v-else-if="column.key === 'createdAt'">
            <span>{{ formatDate(record.createdAt) }}</span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑模态框 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="confirmLoading"
      width="800px"
    >
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="扣分类型" name="deductionType">
              <a-input 
                v-model:value="formState.deductionType" 
                :maxLength="255"
                placeholder="请输入扣分类型" 
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="扣分分数" name="deductionScore">
              <a-input-number
                v-model:value="formState.deductionScore"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 100%"
                placeholder="请输入扣分分数"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="详细描述" name="description">
          <a-textarea
            v-model:value="formState.description"
            :rows="4"
            placeholder="请输入扣分类型的详细描述"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { 
  PlusOutlined, 
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { 
  getDeductionsRules, 
  getDeductionRuleDetail, 
  addDeductionRule, 
  updateDeductionRule, 
  deleteDeductionRule 
} from "@/api/rules/deductionsRules";

// 错误信息
const errorMessage = ref('');

// 当前操作类型
const currentType = ref('degree');

// 未按时获得学位表格列定义
const degreeColumns = [
  {
    title: '未按时获得学位的研究生数',
    dataIndex: 'unTimelyDegreeCount',
    key: 'unTimelyDegreeCount',
    width: 200,
  },
  {
    title: '未按时获得学位的扣减分数',
    dataIndex: 'unTimelyDegreeDeduction',
    key: 'unTimelyDegreeDeduction',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
];

// 未就业表格列定义
const employmentColumns = [
  {
    title: '未就业的截止日期',
    key: 'unemploymentDate',
    width: 200,
  },
  {
    title: '未就业的扣减分数',
    dataIndex: 'unemploymentDeduction',
    key: 'unemploymentDeduction',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
];

// 表格数据
const degreeDataSource = ref([]);
const employmentDataSource = ref([]);
const loading = ref(false);

// 分页配置
const degreePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

const employmentPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 搜索表单
const searchForm = reactive({
  deductionType: '',
});

// 表单状态
const formState = reactive({
  id: '',
  unTimelyDegreeCount: undefined,
  unTimelyDegreeDeduction: undefined,
  unemploymentDate: undefined,
  unemploymentDeduction: undefined,
  deductionType: '',
  deductionScore: undefined,
  description: '',
});

// 表单验证规则
const rules = {
  unTimelyDegreeCount: [{ required: true, message: '请输入未按时获得学位的研究生数', trigger: 'blur' }],
  unTimelyDegreeDeduction: [{ required: true, message: '请输入未按时获得学位的扣减分数', trigger: 'blur' }],
  unemploymentDate: [{ required: true, message: '请选择未就业的截止日期', trigger: 'change' }],
  unemploymentDeduction: [{ required: true, message: '请输入未就业的扣减分数', trigger: 'blur' }],
  deductionType: [{ required: true, message: '请输入扣分类型', trigger: 'blur' }],
  deductionScore: [{ required: true, message: '请输入扣分分数', trigger: 'blur' }],
};

// 模态框状态
const modalVisible = ref(false);
const modalTitle = ref('新增规则');
const confirmLoading = ref(false);
const formRef = ref(null);

// 获取数据
const fetchData = async () => {
  loading.value = true;
  errorMessage.value = '';
  
  try {
    const params = {
      page: degreePagination.current,
      pageSize: degreePagination.pageSize,
    };
    
    const response = await getDeductionsRules(params);
    
    if (response && response.code === 200) {
      // 设置未按时获得学位规则数据
      degreeDataSource.value = response.data.degreeRules.list || [];
      degreePagination.total = response.data.degreeRules.total || 0;
      
      // 设置未就业规则数据
      employmentDataSource.value = response.data.employmentRules.list || [];
      employmentPagination.total = response.data.employmentRules.total || 0;
    } else {
      message.error(response?.message || '获取数据失败');
      errorMessage.value = response?.message || '获取数据失败，请稍后重试';
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败：' + (error.message || '未知错误'));
    errorMessage.value = '获取数据失败：' + (error.message || '未知错误');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  degreePagination.current = 1;
  employmentPagination.current = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.deductionType = '';
  handleSearch();
};

// 未按时获得学位表格变化
const handleDegreeTableChange = (pag) => {
  degreePagination.current = pag.current;
  degreePagination.pageSize = pag.pageSize;
  fetchData();
};

// 未就业表格变化
const handleEmploymentTableChange = (pag) => {
  employmentPagination.current = pag.current;
  employmentPagination.pageSize = pag.pageSize;
  fetchData();
};

// 显示新增模态框
const showAddModal = (type) => {
  currentType.value = type;
  modalTitle.value = type === 'degree' ? '新增未按时获得学位扣减规则' : '新增未就业扣减规则';
  Object.keys(formState).forEach(key => {
    formState[key] = key === 'id' ? '' : undefined;
  });
  modalVisible.value = true;
};

// 显示编辑模态框
const showEditModal = async (record, type) => {
  currentType.value = type;
  modalTitle.value = type === 'degree' ? '编辑未按时获得学位扣减规则' : '编辑未就业扣减规则';
  try {
    const response = await getDeductionRuleDetail(record.id);
    if (response.code === 200) {
      formState.id = record.id;
      if (type === 'degree') {
        formState.unTimelyDegreeCount = response.data.unTimelyDegreeCount;
        formState.unTimelyDegreeDeduction = response.data.unTimelyDegreeDeduction;
      } else {
        formState.unemploymentDate = response.data.unemploymentDate ? dayjs(response.data.unemploymentDate) : undefined;
        formState.unemploymentDeduction = response.data.unemploymentDeduction;
      }
      modalVisible.value = true;
    } else {
      message.error(response.message || '获取规则详情失败');
    }
  } catch (error) {
    console.error('获取规则详情失败:', error);
    message.error('获取规则详情失败');
  }
};

// 模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate();
    confirmLoading.value = true;
    
    const formData = currentType.value === 'degree' ? {
      unTimelyDegreeCount: formState.unTimelyDegreeCount,
      unTimelyDegreeDeduction: formState.unTimelyDegreeDeduction,
    } : {
      unemploymentDate: formState.unemploymentDate ? formState.unemploymentDate.format('YYYY-MM-DD') : undefined,
      unemploymentDeduction: formState.unemploymentDeduction,
    };
    
    let response;
    if (formState.id) {
      response = await updateDeductionRule(formState.id, formData);
    } else {
      response = await addDeductionRule(formData);
    }
    
    if (response.code === 200) {
      message.success('保存成功');
      modalVisible.value = false;
      fetchData();
    } else {
      message.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    confirmLoading.value = false;
  }
};

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 删除记录
const handleDelete = async (record) => {
  try {
    const response = await deleteDeductionRule(record.id);
    if (response.code === 200) {
      message.success('删除成功');
      fetchData();
    } else {
      message.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '--';
  return dayjs(date).format('YYYY-MM-DD');
};

// 页面加载时初始化
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.app-container {
  padding: 24px;
}
.rule-card {
  margin-bottom: 24px;
}
.search-form-wrapper {
  background-color: #f8f8f8;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
}
</style> 