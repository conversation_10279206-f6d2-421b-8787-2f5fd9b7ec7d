const { Op } = require('sequelize');
const teachingResearchAwardParticipantsModel = require('../../../models/v1/mapping/teachingResearchAwardParticipantsModel');
const teachingResearchAwardsModel = require('../../../models/v1/mapping/teachingResearchAwardsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const { getUserInfoFromRequest } = require('../../../utils/others');

/**
 * 获取教学科研奖励参与者列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardParticipants = async (req, res) => {
  try {
    console.log('🔍 获取教学科研奖励参与者列表 - 请求参数:', req.body);

    const {
      awardId,
      userId,
      isLeader,
      page = 1,
      pageSize = 10
    } = req.body;

    // 构建查询条件
    const where = {};

    if (awardId) {
      where.awardId = awardId;
    }

    if (userId) {
      where.participantId = userId;
    }

    if (isLeader !== undefined) {
      where.isLeader = isLeader;
    }

    // 确保 page 和 pageSize 是有效的数字
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const offset = (pageNum - 1) * pageSizeNum;
    const limit = pageSizeNum;

    console.log('查询条件:', where);

    // 查询数据
    const { count, rows } = await teachingResearchAwardParticipantsModel.findAndCountAll({
      where,
      offset,
      limit,
      order: [
        ['isLeader', 'DESC'], // 负责人排在前面
        ['createdAt', 'DESC']
      ],
      include: [
        {
          model: userModel,
          as: 'participant',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: teachingResearchAwardsModel,
          as: 'award',
          attributes: ['id', 'awardName', 'awardTime'],
          required: false
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total: count,
          totalPages: Math.ceil(count / pageSizeNum)
        }
      }
    });
  } catch (error) {
    console.error('获取教学科研奖励参与者列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教学科研奖励参与者列表失败',
      error: error.message
    });
  }
};

/**
 * 获取奖励参与者详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAwardParticipantDetail = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少参与者ID',
        data: null
      });
    }

    // 查询参与者详情
    const participant = await teachingResearchAwardParticipantsModel.findByPk(id, {
      include: [
        {
          model: userModel,
          as: 'participant',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: teachingResearchAwardsModel,
          as: 'award',
          attributes: ['id', 'awardName', 'awardTime', 'awardLevelId', 'department'],
          required: false
        }
      ]
    });

    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '未找到参与者',
        data: null
      });
    }

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: participant
    });
  } catch (error) {
    console.error('获取奖励参与者详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取奖励参与者详情失败',
      error: error.message
    });
  }
};

/**
 * 创建奖励参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAwardParticipant = async (req, res) => {
  try {
    const {
      awardId,
      participantId,
      employeeNumber,
      allocationRatio,
      isLeader = false
    } = req.body;

    // 验证必要字段
    if (!awardId || !participantId || allocationRatio === undefined) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要字段',
        data: null
      });
    }

    // 验证分配比例是否在有效范围内 (0-1)
    if (allocationRatio < 0 || allocationRatio > 1) {
      return res.status(400).json({
        code: 400,
        message: '分配比例必须在0到1之间',
        data: null
      });
    }

    // 检查奖励是否存在
    const award = await teachingResearchAwardsModel.findByPk(awardId);
    if (!award) {
      return res.status(404).json({
        code: 404,
        message: '奖励不存在',
        data: null
      });
    }

    // 检查用户是否存在
    const user = await userModel.findByPk(participantId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }

    // 检查是否已存在相同的参与记录
    const existingParticipant = await teachingResearchAwardParticipantsModel.findOne({
      where: {
        awardId,
        participantId
      }
    });

    if (existingParticipant) {
      return res.status(400).json({
        code: 400,
        message: '该用户已是奖励参与者',
        data: null
      });
    }

    // 如果是负责人，检查奖励是否已有负责人
    if (isLeader) {
      const existingLeader = await teachingResearchAwardParticipantsModel.findOne({
        where: {
          awardId,
          isLeader: true
        }
      });

      if (existingLeader) {
        return res.status(400).json({
          code: 400,
          message: '该奖励已有负责人',
          data: null
        });
      }
    }

    // 创建参与者记录
    const participant = await teachingResearchAwardParticipantsModel.create({
      awardId,
      participantId,
      employeeNumber,
      allocationRatio,
      isLeader
    });

    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: participant
    });
  } catch (error) {
    console.error('创建奖励参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建奖励参与者失败',
      error: error.message
    });
  }
};

/**
 * 更新奖励参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateAwardParticipant = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      employeeNumber,
      allocationRatio,
      isLeader
    } = req.body;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少参与者ID',
        data: null
      });
    }

    // 查询参与者是否存在
    const participant = await teachingResearchAwardParticipantsModel.findByPk(id);

    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '未找到参与者',
        data: null
      });
    }

    // 如果更新为负责人，需要检查奖励是否已有其他负责人
    if (isLeader && !participant.isLeader) {
      const existingLeader = await teachingResearchAwardParticipantsModel.findOne({
        where: {
          awardId: participant.awardId,
          isLeader: true,
          id: { [Op.ne]: id }
        }
      });

      if (existingLeader) {
        return res.status(400).json({
          code: 400,
          message: '该奖励已有负责人',
          data: null
        });
      }
    }

    // 验证分配比例是否在有效范围内 (0-1)
    if (allocationRatio !== undefined && (allocationRatio < 0 || allocationRatio > 1)) {
      return res.status(400).json({
        code: 400,
        message: '分配比例必须在0到1之间',
        data: null
      });
    }

    // 更新参与者
    const updateData = {};
    if (employeeNumber !== undefined) updateData.employeeNumber = employeeNumber;
    if (allocationRatio !== undefined) updateData.allocationRatio = allocationRatio;
    if (isLeader !== undefined) updateData.isLeader = isLeader;

    await participant.update(updateData);

    // 重新加载数据以返回最新信息
    const updatedParticipant = await teachingResearchAwardParticipantsModel.findByPk(id, {
      include: [
        {
          model: userModel,
          as: 'participant',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: teachingResearchAwardsModel,
          as: 'award',
          attributes: ['id', 'awardName', 'awardTime'],
          required: false
        }
      ]
    });

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: updatedParticipant
    });
  } catch (error) {
    console.error('更新奖励参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新奖励参与者失败',
      error: error.message
    });
  }
};

/**
 * 删除奖励参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAwardParticipant = async (req, res) => {
  try {
    const { id } = req.params;
    const userInfo = await getUserInfoFromRequest(req);

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少参与者ID',
        data: null
      });
    }

    // 查询参与者是否存在
    const participant = await teachingResearchAwardParticipantsModel.findByPk(id);

    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '未找到参与者',
        data: null
      });
    }

    // 检查权限：只有管理员或奖励负责人可以删除参与者
    if (!['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      // 查找当前用户是否是奖励负责人
      const isAwardLeader = await teachingResearchAwardParticipantsModel.findOne({
        where: {
          awardId: participant.awardId,
          participantId: userInfo.id,
          isLeader: true
        }
      });

      if (!isAwardLeader) {
        return res.status(403).json({
          code: 403,
          message: '您没有权限删除该参与者',
          data: null
        });
      }
    }

    // 删除参与者
    await participant.destroy();

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除奖励参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除奖励参与者失败',
      error: error.message
    });
  }
};
