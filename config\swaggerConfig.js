/**
 *<AUTHOR>
 *@date 2023/11/14 21:00
 *@Description: 配置swagger
 */

module.exports = {
    swaggerDefinition: {
        info: {
            title: '暨南大学绩效评定系统API文档',
            version: '1.0.0',
            description: '使用Node.js + Express + MySQL开发的绩效评定系统API',
        },
        host: `${process.env.SWA_HOST}:${process.env.SWA_PORT}`,
        basePath: '/',
        produces: [
            "application/json",
            "application/xml"
        ],
        schemes: ['http', 'https'],
        securityDefinitions: {
            JWT: {
                type: 'apiKey',
                in: 'header',
                name: 'Authorization',
                description: "Bearer token",
            }
        }
    },
    route: {
        url: '/swagger',//打开swagger文档页面地址
        docs: '/swagger.json' //swagger文件 api
    },
    basedir: __dirname, //app absolute path
    files: ['../routes/**/*.js']
};
