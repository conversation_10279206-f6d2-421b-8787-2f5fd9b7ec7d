import { ref } from 'vue';

/**
 * 用于获取当前登录用户ID的钩子
 * @returns {Object} { userId, getUserId }
 */
export function useUserId() {
  const userId = ref('');
  const loading = ref(false);
  const error = ref(null);

  /**
   * 从多种存储中获取用户ID
   * @param {boolean} useDefaultIfNotFound - 如果未找到用户ID，是否使用默认ID进行测试
   * @returns {Promise<string|null>} 用户ID或null
   */
  const getUserId = async (useDefaultIfNotFound = false) => {
    loading.value = true;
    error.value = null;
    
    try {
      let id = null;
      
      // 方式1: 优先从单独存储的userId获取
      try {
        id = localStorage.getItem('zyadmin-1.0.0-userId');
        console.log('从localStorage单独存储获取的用户ID:', id);
        
        if (!id) {
          id = sessionStorage.getItem('zyadmin-1.0.0-userId');
          console.log('从sessionStorage单独存储获取的用户ID:', id);
        }
      } catch (e) {
        console.error('从单独存储获取用户ID失败:', e);
      }
      
      // 方式2: 从localStorage中的userInfo获取
      if (!id) {
        try {
          const userInfoStr = localStorage.getItem('zyadmin-1.0.0-userInfo');
          console.log('从localStorage获取的用户信息字符串:', userInfoStr);
          if (userInfoStr) {
            const userInfo = JSON.parse(userInfoStr);
            id = userInfo.id;
            console.log('从localStorage解析的用户ID:', id);
          }
        } catch (e) {
          console.error('从localStorage解析用户信息失败:', e);
        }
      }
      
      // 方式3: 从sessionStorage获取
      if (!id) {
        try {
          const sessionUserInfo = JSON.parse(sessionStorage.getItem('zyadmin-1.0.0-userInfo') || '{}');
          id = sessionUserInfo.id;
          console.log('从sessionStorage获取的用户ID:', id);
        } catch (e) {
          console.error('从sessionStorage获取用户信息失败:', e);
        }
      }
      
      // 方式4: 从cookie中获取(如果有的话)
      if (!id) {
        try {
          // 尝试从cookie中获取
          const cookies = document.cookie.split(';');
          for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('userId=')) {
              id = cookie.substring('userId='.length);
              console.log('从cookie获取的用户ID:', id);
              break;
            }
          }
        } catch (e) {
          console.error('从cookie获取用户信息失败:', e);
        }
      }
      
      // 方式5: 如果需要且仍然没有获取到，使用临时硬编码ID进行测试
      if (!id && useDefaultIfNotFound) {
        // 临时硬编码用户ID用于测试
        id = '1'; // 假设ID为1
        console.log('使用临时硬编码的用户ID:', id);
      }
      
      if (!id) {
        throw new Error('未能获取到用户ID');
      }
      
      console.log('最终使用的用户ID:', id);
      userId.value = id;
      return id;
    } catch (err) {
      console.error('获取用户ID失败:', err);
      error.value = err.message || '获取用户ID失败';
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 初始调用一次
  getUserId();

  return {
    userId,
    loading,
    error,
    getUserId
  };
}

export default useUserId; 