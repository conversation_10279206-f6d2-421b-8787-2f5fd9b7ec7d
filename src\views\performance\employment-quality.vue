<template>
  <div class="employment-quality-container">
    <a-card title="G就业质量评分管理" :bordered="false">
      <template #extra>
        <a-space>
          <a-upload
            :customRequest="handleImport"
            :show-upload-list="false"
            :before-upload="beforeUpload"
          >
            <a-button type="primary" v-permission="'score:G:admin:update'">
              <template #icon><upload-outlined /></template>
              导入数据
            </a-button>
          </a-upload>
          <a-button type="primary" @click="handleExport" v-permission="'score:G:admin:list'">
            <template #icon><download-outlined /></template>
            导出数据
          </a-button>
          <a-button type="primary" @click="showAddModal" v-permission="viewMode === 'personal' ? 'score:G:self:create' : 'score:G:admin:create'">
            <template #icon><plus-outlined /></template>
            添加记录
          </a-button>
          <a-button :type="viewMode === 'personal' ? 'default' : 'primary'" @click="toggleViewMode">
            <template #icon><user-outlined /></template>
            {{ viewMode === 'personal' ? '查看全部就业质量' : '查看我的就业质量' }}
          </a-button>
        </a-space>
      </template>

      <a-alert
        message="评分说明"
        description="统计时间：上年度7月1日-本年度6月30日。根据就业率、就业行业、就业地区、薪资水平、专业对口率、就业满意度等评分标准。"
        type="success"
        show-icon
        style="margin-bottom: 16px; background-color: #f0f9f4; border-color: #ccebd7; color: #52937b;"
      />

      <!-- 个人数据统计卡片，仅在个人视图下显示 -->
      <a-row :gutter="16" style="margin-bottom: 24px" v-if="viewMode === 'personal'">
        <a-col :span="24">
          <a-card :bordered="false">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic 
                  title="我的平均就业率" 
                  :value="personalStats.avgEmploymentRate || 0" 
                  :precision="2"
                  :value-style="{ fontSize: '24px', color: '#3f8600' }"
                  suffix="%"
                >
                  <template #prefix>
                    <rise-outlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="8">
                <a-statistic 
                  title="我的平均薪资" 
                  :value="personalStats.avgSalary || 0" 
                  :precision="0"
                  :value-style="{ fontSize: '24px', color: '#1890ff' }"
                  suffix="元"
                >
                  <template #prefix>
                    <dollar-outlined />
                  </template>
                </a-statistic>
              </a-col>
              <a-col :span="8">
                <a-statistic 
                  title="我的总得分" 
                  :value="personalStats.totalScore || 0" 
                  :precision="2"
                  :value-style="{ fontSize: '24px', color: '#722ed1' }"
                >
                  <template #prefix>
                    <trophy-outlined />
                  </template>
                </a-statistic>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <!-- 搜索区域 -->
      <a-form layout="inline" :model="searchForm" @finish="handleSearch" style="margin-bottom: 16px;">
        <a-form-item name="major" label="专业名称">
          <a-input v-model:value="searchForm.major" placeholder="请输入专业名称" style="width: 200px;" />
        </a-form-item>
        <a-form-item name="year" label="年份">
          <a-input v-model:value="searchForm.year" placeholder="请输入年份" style="width: 200px;" />
        </a-form-item>
        <a-form-item name="employmentIndustry" label="就业行业">
          <a-select v-model:value="searchForm.employmentIndustry" placeholder="请选择就业行业" style="width: 200px;" allowClear>
            <a-select-option value="IT">IT</a-select-option>
            <a-select-option value="金融">金融</a-select-option>
            <a-select-option value="教育">教育</a-select-option>
            <a-select-option value="制造业">制造业</a-select-option>
            <a-select-option value="服务业">服务业</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="employmentRegion" label="就业地区">
          <a-select v-model:value="searchForm.employmentRegion" placeholder="请选择就业地区" style="width: 200px;" allowClear>
            <a-select-option value="一线城市">一线城市</a-select-option>
            <a-select-option value="二线城市">二线城市</a-select-option>
            <a-select-option value="三线城市">三线城市</a-select-option>
            <a-select-option value="其他地区">其他地区</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">
            <template #icon><search-outlined /></template>
            搜索
          </a-button>
          <a-button style="margin-left: 8px" @click="resetSearch">
            <template #icon><reload-outlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>

      <!-- 图表部分 -->
      <a-row :gutter="16" style="margin-bottom: 24px" v-if="viewMode === 'all'">
        <a-col :span="12">
          <a-card title="就业率趋势" :bordered="false">
            <div ref="employmentRateChartRef" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="就业行业分布" :bordered="false">
            <div ref="employmentIndustryChartRef" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="16" style="margin-bottom: 24px" v-if="viewMode === 'all'">
        <a-col :span="12">
          <a-card title="就业地区分布" :bordered="false">
            <div ref="employmentRegionChartRef" style="height: 300px"></div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="专业评分排名" :bordered="false">
            <div ref="majorScoreChartRef" style="height: 300px"></div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 个人视图图表 -->
      <template v-if="viewMode === 'personal'">
        <a-row :gutter="16" style="margin-bottom: 24px;">
          <a-col :span="12">
            <a-card title="我的就业率趋势" :bordered="false">
              <div ref="personalEmploymentRateChartRef" style="height: 300px;"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="我的就业行业分布" :bordered="false">
              <div ref="personalEmploymentIndustryChartRef" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>
        
        <a-row :gutter="16" style="margin-bottom: 24px;">
          <a-col :span="12">
            <a-card title="我的就业地区分布" :bordered="false">
              <div ref="personalEmploymentRegionChartRef" style="height: 300px;"></div>
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card title="我的薪资趋势" :bordered="false">
              <div ref="personalSalaryChartRef" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>
      </template>

      <!-- 数据表格 -->
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="viewMode === 'personal' ? personalDataSource : dataSource"
          :loading="viewMode === 'personal' ? personalLoading : loading"
          :pagination="viewMode === 'personal' ? personalPagination : pagination"
          @change="handleTableChange"
          row-key="id"
          size="middle"
          :scroll="{ x: 1500 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-space>
                <a @click="showEditForm(record)" v-permission="viewMode === 'personal' ? 'score:G:self:update' : 'score:G:admin:update'">编辑</a>
                <a-divider type="vertical" />
                <a @click="handleConfirmDelete(record.id)" v-permission="viewMode === 'personal' ? 'score:G:self:delete' : 'score:G:admin:delete'">删除</a>
              </a-space>
            </template>
          </template>
          
          <!-- 添加空状态展示 -->
          <template #emptyText>
            <a-empty 
              :description="viewMode === 'personal' ? '暂无个人就业质量数据' : '暂无就业质量数据'" 
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
            >
              <a-button type="primary" @click="showAddModal">添加数据</a-button>
            </a-empty>
          </template>
        </a-table>
      </a-card>

      <!-- 添加/编辑模态框 -->
      <a-modal
        :title="modalTitle"
        :visible="modalVisible"
        @ok="handleSubmit"
        @cancel="handleModalCancel"
        :confirmLoading="submitLoading"
      >
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="专业名称" name="major">
            <a-input v-model:value="formState.major" placeholder="请输入专业名称" />
          </a-form-item>
          <a-form-item label="年份" name="year">
            <a-input v-model:value="formState.year" placeholder="请输入年份" />
          </a-form-item>
          <a-form-item label="关联用户" name="userId">
            <a-select 
              v-model:value="formState.userId"
              placeholder="请选择关联用户"
              style="width: 100%"
              show-search
              :options="usersOptions"
              :filter-option="(input, option) => 
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            >
            </a-select>
          </a-form-item>
          <a-form-item label="就业率" name="employmentRate">
            <a-input-number
              v-model:value="formState.employmentRate"
              :min="0"
              :max="100"
              :step="0.1"
              style="width: 100%"
              placeholder="请输入就业率（%）"
            />
          </a-form-item>
          <a-form-item label="就业行业" name="employmentIndustry">
            <a-select v-model:value="formState.employmentIndustry" placeholder="请选择就业行业">
              <a-select-option value="IT">IT</a-select-option>
              <a-select-option value="金融">金融</a-select-option>
              <a-select-option value="教育">教育</a-select-option>
              <a-select-option value="制造业">制造业</a-select-option>
              <a-select-option value="服务业">服务业</a-select-option>
              <a-select-option value="其他">其他</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="就业地区" name="employmentRegion">
            <a-select v-model:value="formState.employmentRegion" placeholder="请选择就业地区">
              <a-select-option value="一线城市">一线城市</a-select-option>
              <a-select-option value="二线城市">二线城市</a-select-option>
              <a-select-option value="三线城市">三线城市</a-select-option>
              <a-select-option value="其他地区">其他地区</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="平均薪资" name="averageSalary">
            <a-input-number
              v-model:value="formState.averageSalary"
              :min="0"
              :step="1000"
              style="width: 100%"
              placeholder="请输入平均薪资（元/月）"
            />
          </a-form-item>
          <a-form-item label="专业对口率" name="majorMatchRate">
            <a-input-number
              v-model:value="formState.majorMatchRate"
              :min="0"
              :max="100"
              :step="0.1"
              style="width: 100%"
              placeholder="请输入专业对口率（%）"
            />
          </a-form-item>
          <a-form-item label="就业满意度" name="employmentSatisfaction">
            <a-input-number
              v-model:value="formState.employmentSatisfaction"
              :min="0"
              :max="100"
              :step="0.1"
              style="width: 100%"
              placeholder="请输入就业满意度（%）"
            />
          </a-form-item>
          <a-form-item label="评分" name="score">
            <a-input-number
              v-model:value="formState.score"
              :min="0"
              :max="100"
              style="width: 100%"
              placeholder="请输入评分"
            />
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea
              v-model:value="formState.remark"
              :rows="4"
              placeholder="请输入备注"
            />
          </a-form-item>
        </a-form>
      </a-modal>
      
      <!-- 导入文件模态框 -->
      <a-modal
        title="导入就业质量数据"
        :visible="uploadModalVisible"
        @ok="handleUploadOk"
        @cancel="handleUploadCancel"
        :confirmLoading="uploadConfirmLoading"
      >
        <a-upload
          name="file"
          :multiple="false"
          :beforeUpload="beforeUpload"
          :fileList="fileList"
          @change="handleUploadChange"
        >
          <a-button>
            <template #icon><upload-outlined /></template>
            选择文件
          </a-button>
        </a-upload>
        <a-alert
          style="margin-top: 16px"
          message="提示"
          description="请上传Excel格式的文件，文件大小不超过10MB。模板中必须包含专业名称、年份、就业率等字段。"
          type="info"
          show-icon
        />
      </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick, onUnmounted, h } from 'vue';
import { 
  UploadOutlined, 
  DownloadOutlined, 
  PlusOutlined, 
  UserOutlined, 
  SearchOutlined, 
  ReloadOutlined, 
  TrophyOutlined,
  RiseOutlined,
  DollarOutlined
} from '@ant-design/icons-vue';
import { message, Empty, Modal } from 'ant-design-vue';
import * as echarts from 'echarts';
import { 
  getEmploymentQualities, 
  getEmploymentRateTrend, 
  getEmploymentIndustryDistribution, 
  getEmploymentRegionDistribution, 
  getMajorScoreRanking,
  getPersonalEmployments,
  getPersonalEmploymentStats,
  addEmploymentQuality,
  updateEmploymentQuality,
  deleteEmploymentQuality,
  importEmploymentQualities,
  exportEmploymentQualities,
  getEmploymentQualityDetail
} from '@/api/modules/api.employment_quality';
import { usersList } from '@/api/modules/api.users';
import { useUserId } from '@/composables/useUserId';

// 获取用户ID
const { userId, getUserId } = useUserId();

// 表格列定义
const columns = [
  {
    title: '专业名称',
    dataIndex: 'major',
    key: 'major',
    width: 160,
  },
  {
    title: '年份',
    dataIndex: 'year',
    key: 'year',
    width: 80,
    sorter: true,
  },
  {
    title: '用户',
    dataIndex: 'username',
    key: 'username',
    width: 100,
    customRender: ({ record }) => record.username || '未指定',
  },
  {
    title: '就业率(%)',
    dataIndex: 'employmentRate',
    key: 'employmentRate',
    width: 100,
    sorter: true,
    customRender: ({ text }) => `${text}%`
  },
  {
    title: '就业行业',
    dataIndex: 'employmentIndustry',
    key: 'employmentIndustry',
    width: 120,
  },
  {
    title: '就业地区',
    dataIndex: 'employmentRegion',
    key: 'employmentRegion',
    width: 120,
  },
  {
    title: '平均薪资',
    dataIndex: 'averageSalary',
    key: 'averageSalary',
    width: 120,
    sorter: true,
    customRender: ({ text }) => `${text}元`
  },
  {
    title: '专业对口率',
    dataIndex: 'majorMatchRate',
    key: 'majorMatchRate',
    width: 110,
    sorter: true,
    customRender: ({ text }) => `${text}%`
  },
  {
    title: '就业满意度',
    dataIndex: 'employmentSatisfaction',
    key: 'employmentSatisfaction',
    width: 110,
    sorter: true,
    customRender: ({ text }) => `${text}%`
  },
  {
    title: '评分',
    dataIndex: 'score',
    key: 'score',
    width: 80,
    sorter: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 180,
    ellipsis: true, // 超出宽度显示省略号
    customRender: ({ text }) => {
      if (!text) return '-';
      // 如果备注超过20个字符，显示部分内容并添加tooltip
      if (text.length > 20) {
        return h(
          'a-tooltip',
          { title: text },
          { default: () => text.substring(0, 20) + '...' }
        );
      }
      return text;
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
    align: 'center'
  }
];

// 个人统计数据
const personalStats = reactive({
  avgEmploymentRate: 0,
  avgSalary: 0,
  totalScore: 0
});

// 视图模式
const viewMode = ref('all'); // 'all' 或 'personal'

// 切换视图模式
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'all' ? 'personal' : 'all';
  
  // 重置分页和搜索条件
  if (viewMode.value === 'all') {
    pagination.current = 1;
  } else {
    personalPagination.current = 1;
  }
  
  // 获取相应视图的数据
  if (viewMode.value === 'personal') {
    // 获取个人数据
    fetchPersonalData();
    updatePersonalStats();
  } else {
    // 获取全部数据
    fetchData();
  }
  
  message.success(`已切换至${viewMode.value === 'personal' ? '我的' : '全部'}就业质量数据`);
};

// 搜索表单
const searchForm = reactive({
  major: '',
  year: undefined,
  employmentIndustry: undefined,
  employmentRegion: undefined,
});

// 表单状态
const formState = reactive({
  id: '',
  major: '',
  year: new Date().getFullYear(),
  employmentRate: undefined,
  employmentIndustry: undefined,
  employmentRegion: undefined,
  averageSalary: undefined,
  majorMatchRate: undefined,
  employmentSatisfaction: undefined,
  score: undefined,
  userId: '',
  username: '',
  remark: '',
});

// 表单验证规则
const rules = {
  major: [{ required: true, message: '请输入专业名称', trigger: 'blur' }],
  year: [{ required: true, message: '请选择年份', trigger: 'change' }],
  userId: [{ required: true, message: '请选择关联用户', trigger: 'change' }],
  employmentRate: [{ required: true, message: '请输入就业率', trigger: 'blur' }],
  employmentIndustry: [{ required: true, message: '请选择就业行业', trigger: 'change' }],
  employmentRegion: [{ required: true, message: '请选择就业地区', trigger: 'change' }],
  averageSalary: [{ required: true, message: '请输入平均薪资', trigger: 'blur' }],
  majorMatchRate: [{ required: true, message: '请输入专业对口率', trigger: 'blur' }],
  employmentSatisfaction: [{ required: true, message: '请输入就业满意度', trigger: 'blur' }],
  score: [{ required: true, message: '请输入评分', trigger: 'blur' }],
};

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 编辑状态
const isEdit = ref(false);
const recordId = ref('');
const showModal = ref(false);

// 模态框状态
const modalVisible = ref(false);
const modalTitle = ref('新增记录');
const submitLoading = ref(false);
const formRef = ref(null);

// 图表引用
const employmentRateChartRef = ref(null);
const employmentIndustryChartRef = ref(null);
const employmentRegionChartRef = ref(null);
const majorScoreChartRef = ref(null);
const deductionReasonChartRef = ref(null);

// 个人图表引用
const personalEmploymentRateChartRef = ref(null);
const personalEmploymentIndustryChartRef = ref(null);
const personalEmploymentRegionChartRef = ref(null);
const personalSalaryChartRef = ref(null);

// 图表实例
let employmentRateChart = null;
let employmentIndustryChart = null;
let employmentRegionChart = null;
let majorScoreChart = null;
let deductionReasonChart = null;

// 个人图表实例
let personalEmploymentRateChart = null;
let personalEmploymentIndustryChart = null;
let personalEmploymentRegionChart = null;
let personalSalaryChart = null;

// 个人表格数据
const personalDataSource = ref([]);
const personalLoading = ref(false);
const personalPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 上传模态框相关
const uploadModalVisible = ref(false);
const uploadConfirmLoading = ref(false);
const fileList = ref([]);

// 用户列表数据
const usersOptions = ref([]);

// 获取用户列表
const fetchUsersList = async () => {
  try {
    const res = await usersList({
      pagination: { current: 1, pageSize: 100 },
      params: {}
    });
    
    console.log('用户列表API响应数据:', res);
    
    // 处理不同的API返回格式
    let userData = [];
    
    // 情况1: {status: 1, message: "Success.", data: {result: [...]}}
    if (res && res.status === 1 && res.data && Array.isArray(res.data.result)) {
      userData = res.data.result;
    }
    // 情况2: {data: {status: 1, message: "Success.", data: {result: [...]}}}
    else if (res && res.data && res.data.status === 1 && res.data.data && Array.isArray(res.data.data.result)) {
      userData = res.data.data.result;
    }
    // 情况3: {code: 200, message: "获取成功", data: {list: [...]}}
    else if (res && res.code === 200 && res.data && Array.isArray(res.data.list)) {
      userData = res.data.list;
    }
    // 情况4: {data: {code: 200, message: "获取成功", data: {list: [...]}}}
    else if (res && res.data && res.data.code === 200 && res.data.data && Array.isArray(res.data.data.list)) {
      userData = res.data.data.list;
    }
    
    if (userData.length > 0) {
      usersOptions.value = userData.map(user => ({
        label: user.nickname || user.username,
        value: user.id
      }));
      console.log('获取用户列表成功，数量:', usersOptions.value.length);
    } else {
      console.error('获取用户列表失败，未找到有效数据:', res);
      message.error('获取用户列表失败，请刷新页面重试');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  }
};

// 显示上传模态框
const showUploadModal = () => {
  uploadModalVisible.value = true;
  fileList.value = [];
};

// 上传前检查
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.ms-excel' || 
                  file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  if (!isExcel) {
    message.error('只能上传Excel文件!');
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
  }
  return false;
};

// 处理上传变更
const handleUploadChange = (info) => {
  fileList.value = [...info.fileList].slice(-1);
};

// 处理上传确认
const handleUploadOk = () => {
  if (fileList.value.length === 0) {
    message.warning('请选择要上传的文件');
    return;
  }
  
  uploadConfirmLoading.value = true;
  
  // 构建表单数据
  const formData = new FormData();
  formData.append('file', fileList.value[0].originFileObj);
  
  // 调用API上传文件
  importEmploymentQualities(formData)
    .then(res => {
      if (res.code === 200) {
        message.success(`导入成功，共导入${res.data.success}条记录`);
        uploadModalVisible.value = false;
        fileList.value = [];
        fetchData();
        if (viewMode.value === 'personal') {
          fetchPersonalData();
          updatePersonalStats();
        }
      } else {
        message.error(`导入失败: ${res.message}`);
      }
    })
    .catch(error => {
      message.error(`导入失败: ${error.message}`);
    })
    .finally(() => {
      uploadConfirmLoading.value = false;
    });
};

// 处理导入
const handleImport = async () => {
  if (!fileList.value || fileList.value.length === 0) {
    message.warning('请先选择文件');
    return;
  }
  
  const formData = new FormData();
  formData.append('file', fileList.value[0]);
  
  importLoading.value = true;
  try {
    const res = await importEmploymentQualities(formData);
    const data = res.data || res;
    if (data && (data.code === 200 || res.status === 200)) {
      message.success('导入成功');
      fetchData();
      importVisible.value = false;
      fileList.value = [];
    } else {
      message.error(data.message || '导入失败');
    }
  } catch (error) {
    console.error('导入失败:', error);
    message.error('导入失败');
  } finally {
    importLoading.value = false;
  }
};

// 处理上传取消
const handleUploadCancel = () => {
  uploadModalVisible.value = false;
  fileList.value = [];
};

// 初始化图表
const initCharts = () => {
  console.log('正在初始化图表...');
  
  // 就业率趋势图
  if (employmentRateChartRef.value) {
    console.log('初始化就业率趋势图');
    // 确保先销毁旧实例
    if (employmentRateChart) {
      employmentRateChart.dispose();
    }
    employmentRateChart = echarts.init(employmentRateChartRef.value);
    const employmentRateOption = {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: []
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '就业率',
          type: 'line',
          smooth: true,
          data: [],
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ]
          },
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#1890ff'
          }
        }
      ]
    };
    employmentRateChart.setOption(employmentRateOption);
    console.log('就业率趋势图初始化完成');
  } else {
    console.warn('就业率趋势图DOM元素不存在');
  }

  // 就业行业分布图
  if (employmentIndustryChartRef.value) {
    console.log('初始化就业行业分布图');
    // 确保先销毁旧实例
    if (employmentIndustryChart) {
      employmentIndustryChart.dispose();
    }
    employmentIndustryChart = echarts.init(employmentIndustryChartRef.value);
    const employmentIndustryOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        bottom: 10,
        data: []
      },
      series: [
        {
          name: '就业行业',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    };
    employmentIndustryChart.setOption(employmentIndustryOption);
    console.log('就业行业分布图初始化完成');
  } else {
    console.warn('就业行业分布图DOM元素不存在');
  }

  // 就业地区分布图
  if (employmentRegionChartRef.value) {
    console.log('初始化就业地区分布图');
    // 确保先销毁旧实例
    if (employmentRegionChart) {
      employmentRegionChart.dispose();
    }
    employmentRegionChart = echarts.init(employmentRegionChartRef.value);
    const employmentRegionOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        bottom: 10,
        data: []
      },
      series: [
        {
          name: '就业地区',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    };
    employmentRegionChart.setOption(employmentRegionOption);
    console.log('就业地区分布图初始化完成');
  } else {
    console.warn('就业地区分布图DOM元素不存在');
  }

  // 专业评分排名图
  if (majorScoreChartRef.value) {
    console.log('初始化专业评分排名图');
    // 确保先销毁旧实例
    if (majorScoreChart) {
      majorScoreChart.dispose();
    }
    majorScoreChart = echarts.init(majorScoreChartRef.value);
    const majorScoreOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01]
      },
      yAxis: {
        type: 'category',
        data: []
      },
      series: [
        {
          name: '评分',
          type: 'bar',
          data: [],
          itemStyle: {
            color: function(params) {
              // 根据评分设置不同颜色
              const score = params.value;
              if (score >= 90) return '#52c41a'; // 优秀
              if (score >= 80) return '#1890ff'; // 良好
              if (score >= 70) return '#faad14'; // 中等
              return '#f5222d'; // 较差
            }
          }
        }
      ]
    };
    majorScoreChart.setOption(majorScoreOption);
    console.log('专业评分排名图初始化完成');
  } else {
    console.warn('专业评分排名图DOM元素不存在');
  }
};

// 初始化个人图表
const initPersonalCharts = () => {
  // 个人就业率趋势图
  if (personalEmploymentRateChartRef.value && !personalEmploymentRateChart) {
    personalEmploymentRateChart = echarts.init(personalEmploymentRateChartRef.value);
    const personalEmploymentRateOption = {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: []
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '就业率',
          type: 'line',
          smooth: true,
          data: [],
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ]
          },
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#1890ff'
          }
        }
      ]
    };
    personalEmploymentRateChart.setOption(personalEmploymentRateOption);
  }

  // 个人就业行业分布图
  if (personalEmploymentIndustryChartRef.value && !personalEmploymentIndustryChart) {
    personalEmploymentIndustryChart = echarts.init(personalEmploymentIndustryChartRef.value);
    const personalEmploymentIndustryOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        bottom: 10,
        data: []
      },
      series: [
        {
          name: '就业行业',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    };
    personalEmploymentIndustryChart.setOption(personalEmploymentIndustryOption);
  }

  // 个人就业地区分布图
  if (personalEmploymentRegionChartRef.value && !personalEmploymentRegionChart) {
    personalEmploymentRegionChart = echarts.init(personalEmploymentRegionChartRef.value);
    const personalEmploymentRegionOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 10,
        bottom: 10,
        data: []
      },
      series: [
        {
          name: '就业地区',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    };
    personalEmploymentRegionChart.setOption(personalEmploymentRegionOption);
  }

  // 个人薪资趋势图
  if (personalSalaryChartRef.value && !personalSalaryChart) {
    personalSalaryChart = echarts.init(personalSalaryChartRef.value);
    const personalSalaryOption = {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}年: {c}元'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: []
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}元'
        }
      },
      series: [
        {
          name: '薪资',
          type: 'line',
          smooth: true,
          data: [],
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ]
          },
          itemStyle: {
            color: '#52c41a'
          }
        }
      ]
    };
    personalSalaryChart.setOption(personalSalaryOption);
  }
};

// 更新个人图表数据
const updatePersonalCharts = (data) => {
  if (!data) return;
  
  // 确保图表实例已创建
  if (!personalEmploymentRateChart && personalEmploymentRateChartRef.value) {
    initPersonalCharts();
  }
  
  // 更新就业率趋势
  if (personalEmploymentRateChart) {
    personalEmploymentRateChart.setOption({
      xAxis: {
        data: data.years || []
      },
      series: [{
        data: data.employmentRates || []
      }]
    });
  }
  
  // 更新就业行业分布
  if (personalEmploymentIndustryChart) {
    personalEmploymentIndustryChart.setOption({
      legend: {
        data: data.industries.map(item => item.name) || []
      },
      series: [{
        data: data.industries || []
      }]
    });
  }
  
  // 更新就业地区分布
  if (personalEmploymentRegionChart) {
    personalEmploymentRegionChart.setOption({
      legend: {
        data: data.regions.map(item => item.name) || []
      },
      series: [{
        data: data.regions || []
      }]
    });
  }
  
 // 更新薪资趋势
  if (personalSalaryChart) {
    personalSalaryChart.setOption({
      xAxis: {
        data: data.salaries.map(item => item.year) || []
      },
      series: [{
        data: data.salaries.map(item => item.value) || []
      }]
    });
  }
};

// 更新个人统计数据
const updatePersonalStats = async () => {
  try {
    // 先确保获取到用户ID
    const currentUserId = await getUserId(true);
    if (!currentUserId) {
      console.error('无法获取用户ID，无法获取个人统计数据');
      message.warning('无法获取用户ID，请先登录');
      return;
    }
    
    console.log('正在获取个人就业质量统计数据，userId:', currentUserId);
    
    // 调用接口时传递userId参数
    const res = await getPersonalEmploymentStats({ userId: currentUserId });
    
    console.log('个人就业质量统计API响应:', res);
    
    // 处理返回数据
    if (res && res.code === 200 && res.data) {
      // 判断是否返回单个对象
      if (res.data.id) {
        // 单个对象情况，直接提取数据
        personalStats.avgEmploymentRate = parseFloat(res.data.employmentRate) || 0;
        personalStats.avgSalary = parseFloat(res.data.averageSalary) || 0;
        personalStats.totalScore = parseFloat(res.data.score) || 0;
        
        // 构造图表数据
        const chartData = {
          years: [res.data.year.toString()],
          employmentRates: [parseFloat(res.data.employmentRate)],
          industries: [{
            name: res.data.employmentIndustry || '未知',
            value: 1
          }],
          regions: [{
            name: res.data.employmentRegion || '未知',
            value: 1
          }],
          salaries: [{
            year: res.data.year.toString(),
            value: parseFloat(res.data.averageSalary)
          }],
          scores: [{
            year: res.data.year.toString(),
            value: parseFloat(res.data.score)
          }]
        };
        
        // 更新个人图表
        nextTick(() => {
          initPersonalCharts();
          updatePersonalCharts(chartData);
        });
      } 
      // 标准格式的返回
      else if (res.data.years || res.data.employmentRates) {
        // 计算平均就业率
        const rates = res.data.employmentRates || [];
        personalStats.avgEmploymentRate = rates.length > 0 
          ? rates.reduce((sum, rate) => sum + rate, 0) / rates.length 
          : 0;
          
        // 计算平均薪资
        const salaries = res.data.salaries || [];
        personalStats.avgSalary = salaries.length > 0 
          ? salaries.reduce((sum, item) => sum + item.value, 0) / salaries.length 
          : 0;
          
        // 计算总评分 (取最新年份的评分)
        const scores = res.data.scores || [];
        personalStats.totalScore = scores.length > 0 
          ? scores[scores.length - 1].value
          : 0;
        
        // 更新个人图表
        nextTick(() => {
          initPersonalCharts();
          updatePersonalCharts(res.data);
        });
      } else {
        console.warn('个人就业质量统计数据返回格式有误或为空:', res.data);
      }
    } else {
      console.warn('个人就业质量统计数据返回格式有误或为空:', res);
    }
  } catch (error) {
    console.error('获取个人统计数据失败:', error);
    // 使用默认值
    personalStats.avgEmploymentRate = 0;
    personalStats.avgSalary = 0;
    personalStats.totalScore = 0;
    message.error('获取个人统计数据失败：' + (error.message || String(error)));
  }
};

// 获取个人数据
const fetchPersonalData = async () => {
  personalLoading.value = true;
  
  try {
    // 先确保获取到用户ID
    const currentUserId = await getUserId(true);
    if (!currentUserId) {
      console.error('无法获取用户ID，无法获取个人数据');
      message.warning('无法获取用户ID，请先登录');
      personalLoading.value = false;
      return;
    }
    
    console.log('正在获取个人就业质量数据，userId:', currentUserId);
    
    // 调用接口时传递userId参数
    const res = await getPersonalEmployments({
      page: personalPagination.current,
      pageSize: personalPagination.pageSize,
      major: searchForm.major,
      year: searchForm.year,
      employmentIndustry: searchForm.employmentIndustry,
      employmentRegion: searchForm.employmentRegion,
      userId: currentUserId
    });
    
    console.log('个人就业质量数据API响应:', res);
    
    // 处理响应数据
    if (res && (res.code === 200 || res.status === 200)) {
      let listData, pageInfo;
      
      // 检查是否返回单个对象还是列表
      if (res.data && !Array.isArray(res.data) && res.data.id) {
        // 单个对象情况，转换为数组
        listData = [res.data];
        pageInfo = {
          total: 1,
          page: personalPagination.current,
          pageSize: personalPagination.pageSize
        };
      }
      // 情况1: 直接返回 {list: [...], total: 数量}
      else if (Array.isArray(res.list)) {
        listData = res.list;
        pageInfo = {
          total: res.total || listData.length,
          page: res.page || personalPagination.current,
          pageSize: res.pageSize || personalPagination.pageSize
        };
      } 
      // 情况2: 返回 {data: {list: [...], total: 数量}}
      else if (res.data && Array.isArray(res.data.list)) {
        listData = res.data.list;
        pageInfo = {
          total: res.data.total || listData.length,
          page: res.data.page || personalPagination.current,
          pageSize: res.data.pageSize || personalPagination.pageSize
        };
      }
      // 情况3: 没有找到有效数据，但接口返回成功
      else {
        listData = [];
        pageInfo = {
          total: 0,
          page: personalPagination.current,
          pageSize: personalPagination.pageSize
        };
        console.warn('接口返回成功但未找到个人数据列表:', res);
      }
      
      // 更新数据和分页信息
      personalDataSource.value = listData;
      personalPagination.total = pageInfo.total;
      personalPagination.current = Number(pageInfo.page);
      personalPagination.pageSize = Number(pageInfo.pageSize);
    } else {
      // 只有当接口返回失败时才清空列表并提示错误
      personalDataSource.value = [];
      personalPagination.total = 0;
      // 显示接口返回的错误信息
      const errorMsg = res?.message || '获取个人就业质量数据失败';
      console.error('接口返回错误状态:', res);
      message.warning(`${errorMsg}`);
    }
  } catch (error) {
    console.error('获取个人就业质量列表失败:', error);
    personalDataSource.value = [];
    personalPagination.total = 0;
    // 显示详细的错误信息
    message.error(`获取个人就业质量列表失败: ${error.message || error}`);
  } finally {
    personalLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.major = '';
  searchForm.year = undefined;
  searchForm.employmentIndustry = undefined;
  searchForm.employmentRegion = undefined;
  handleSearch();
};

// 处理表格分页、筛选、排序变化
const handleTableChange = (pag, filters, sorter) => {
  console.log('表格变更:', { pag, filters, sorter });
  
  // 根据当前视图模式更新相应的分页数据
  if (viewMode.value === 'personal') {
    personalPagination.current = pag.current;
    personalPagination.pageSize = pag.pageSize;
    fetchPersonalData();
  } else {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    fetchData();
  }
};

// 显示新增模态框
const showAddModal = () => {
  modalTitle.value = '新增记录';
  formState.id = '';
  formState.major = '';
  formState.year = new Date().getFullYear();
  formState.employmentRate = undefined;
  formState.employmentIndustry = undefined;
  formState.employmentRegion = undefined;
  formState.averageSalary = undefined;
  formState.majorMatchRate = undefined;
  formState.employmentSatisfaction = undefined;
  formState.score = undefined;
  formState.userId = '';
  formState.username = '';
  modalVisible.value = true;
};

// 打开编辑表单
const showEditForm = async (record) => {
  console.log('编辑记录:', record);
  isEdit.value = true;
  recordId.value = record.id;
  formState.major = record.major;
  formState.year = record.year;
  formState.employmentRate = parseFloat(record.employmentRate);
  formState.employmentIndustry = record.employmentIndustry;
  formState.employmentRegion = record.employmentRegion;
  formState.averageSalary = parseFloat(record.averageSalary);
  formState.majorMatchRate = parseFloat(record.majorMatchRate);
  formState.employmentSatisfaction = parseFloat(record.employmentSatisfaction);
  formState.score = parseFloat(record.score);
  formState.userId = record.userId;
  formState.username = record.username;
  formState.remark = record.remark || ''; // 确保备注字段被正确加载
  
  // 尝试获取详细信息，包括备注
  try {
    const detailRes = await getEmploymentQualityDetail(record.id);
    if (detailRes && (detailRes.code === 200 || detailRes.status === 200) && detailRes.data) {
      console.log('获取详细信息成功:', detailRes.data);
      // 更新表单状态，特别是备注字段
      formState.remark = detailRes.data.remark || '';
    }
  } catch (error) {
    console.error('获取详细信息失败:', error);
  }
  
  // 打开模态框
  modalVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    formRef.value.validate().then(async () => {
      submitLoading.value = true;
      console.log('表单数据：', formState);
      
      try {
        let response;
        
        // 根据是否是编辑模式选择不同的API调用
        if (isEdit.value && recordId.value) {
          // 编辑模式
          response = await updateEmploymentQuality(recordId.value, formState);
          console.log('更新响应:', response);
        } else {
          // 新增模式
          response = await addEmploymentQuality(formState);
          console.log('新增响应:', response);
        }
        
        if (response && (response.code === 200 || response.status === 200)) {
          message.success(isEdit.value ? '更新成功' : '添加成功');
          modalVisible.value = false;
          // 重置表单和状态
          resetForm();
          isEdit.value = false;
          recordId.value = '';
          // 刷新数据
          fetchData();
        } else {
          message.error(response?.message || '操作失败');
        }
      } catch (error) {
        console.error('提交失败:', error);
        message.error(`提交失败: ${error.message || '未知错误'}`);
      } finally {
        submitLoading.value = false;
      }
    });
  } catch (error) {
    console.error('表单验证失败:', error);
    message.error('表单验证失败，请检查填写内容');
  }
};

// 重置表单
const resetForm = () => {
  formState.major = '';
  formState.year = new Date().getFullYear();
  formState.employmentRate = 0;
  formState.employmentIndustry = '';
  formState.employmentRegion = '';
  formState.averageSalary = 0;
  formState.majorMatchRate = 0;
  formState.employmentSatisfaction = 0;
  formState.score = 0;
  formState.userId = '';
  formState.username = '';
  formState.remark = '';
  
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
  resetForm();
  isEdit.value = false;
  recordId.value = '';
};

// 确认删除
const handleConfirmDelete = (id) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => confirmDelete(id)
  })
}

const confirmDelete = async (id) => {
  try {
    console.log('删除记录:', id);
    const res = await deleteEmploymentQuality(id);
    if (res.code === 200 || res.status === 200) {
      message.success('删除成功');
      // 根据当前视图模式刷新数据
      if (viewMode.value === 'personal') {
        fetchPersonalData();
        updatePersonalStats();
      } else {
        fetchData();
      }
    } else {
      message.error(res.message || '删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    message.error(`删除失败: ${error.message || '未知错误'}`);
  }
};

// 导出数据
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const res = await exportEmploymentQualities({
      major: searchForm.major,
      year: searchForm.year,
      employmentIndustry: searchForm.employmentIndustry,
      employmentRegion: searchForm.employmentRegion
    });
    
    // 处理响应数据
    if (res.status === 200) {
      const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
      const downloadLink = document.createElement('a');
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = `就业质量数据_${new Date().getTime()}.xlsx`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      message.success('导出成功');
    } else {
      message.error('导出失败');
    }
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  } finally {
    exportLoading.value = false;
  }
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  
  try {
    const res = await getEmploymentQualities({
      page: pagination.current,
      pageSize: pagination.pageSize,
      major: searchForm.major,
      year: searchForm.year,
      employmentIndustry: searchForm.employmentIndustry,
      employmentRegion: searchForm.employmentRegion
    });
    
    console.log('就业质量数据API响应:', res);
    
    // 处理响应数据 - 灵活处理多种可能的数据结构
    if (res && (res.code === 200 || res.status === 200)) {
      let listData, pageInfo;
      
      // 情况1: 直接返回 {list: [...], total: 数量}
      if (Array.isArray(res.list)) {
        listData = res.list;
        pageInfo = {
          total: res.total || listData.length,
          page: res.page || pagination.current,
          pageSize: res.pageSize || pagination.pageSize
        };
      } 
      // 情况2: 返回 {data: {list: [...], total: 数量}}
      else if (res.data && Array.isArray(res.data.list)) {
        listData = res.data.list;
        pageInfo = {
          total: res.data.total || listData.length,
          page: res.data.page || pagination.current,
          pageSize: res.data.pageSize || pagination.pageSize
        };
      }
      // 情况3: 没有找到有效数据，但接口返回成功
      else {
        listData = [];
        pageInfo = {
          total: 0,
          page: pagination.current,
          pageSize: pagination.pageSize
        };
        console.warn('接口返回成功但未找到有效数据列表:', res);
      }
      
      // 更新数据和分页信息
      dataSource.value = listData;
      pagination.total = pageInfo.total;
      pagination.current = Number(pageInfo.page);
      pagination.pageSize = Number(pageInfo.pageSize);
    } else {
      // 只有当接口返回失败时才清空列表并提示错误
      dataSource.value = [];
      pagination.total = 0;
      // 显示接口返回的错误信息
      const errorMsg = res?.message || '获取就业质量数据失败';
      console.error('接口返回错误状态:', res);
      message.warning(`${errorMsg}`);
    }
  } catch (error) {
    console.error('获取就业质量列表失败:', error);
    dataSource.value = [];
    pagination.total = 0;
    // 显示详细的错误信息
    message.error(`获取就业质量列表失败: ${error.message || error}`);
  } finally {
    loading.value = false;
  }
  
  // 更新图表数据
  if (viewMode.value === 'all') {
    nextTick(() => {
      initCharts();
      updateCharts();
    });
  }
};

// 更新图表数据
const updateCharts = async () => {
  try {
    console.log('开始获取图表数据...');
    
    // 获取就业率趋势
    const trendRes = await getEmploymentRateTrend();
    console.log('就业率趋势原始响应:', trendRes);
    
    // 处理多层嵌套的响应结构
    let trendData;
    if (trendRes.data && trendRes.data.code === 200) {
      // 情况1: axios返回 {data: {code: 200, data: {...}}}
      trendData = trendRes.data;
    } else if (trendRes.code === 200) {
      // 情况2: 直接返回 {code: 200, data: {...}}
      trendData = trendRes;
    } else {
      console.error('无法识别的就业率趋势返回格式:', trendRes);
      trendData = null;
    }
    
    if (employmentRateChart && trendData && trendData.code === 200 && trendData.data) {
      console.log('解析就业率趋势数据:', trendData.data);
      
      // 处理新的数据格式 {trends: [...], startYear, endYear}
      const trends = trendData.data.trends || [];
      console.log('提取的趋势数据:', trends);
      
      if (trends.length > 0) {
        const years = trends.map(item => item.year);
        const rates = trends.map(item => parseFloat(item.employmentRate));
        
        console.log('就业率趋势图表数据 - 年份:', years);
        console.log('就业率趋势图表数据 - 就业率:', rates);
        
        // 仅更新数据，不改变图表的类型和样式配置
        employmentRateChart.setOption({
          xAxis: {
            data: years
          },
          series: [{
            // 保持原有的图表类型和配置
            name: '就业率',
            type: 'line',  // 明确指定类型，避免覆盖原始配置
            data: rates
          }]
        }, false);  // 使用false参数，只更新指定的配置项
        console.log('就业率趋势图表已更新');
      } else {
        console.warn('就业率趋势数据为空');
      }
    } else {
      console.error('就业率趋势数据获取失败或格式有误:', trendData);
    }

    // 获取就业行业分布
    const industryRes = await getEmploymentIndustryDistribution({ year: searchForm.year });
    console.log('就业行业分布原始响应:', industryRes);
    
    // 处理多层嵌套的响应结构
    let industryData;
    if (industryRes.data && industryRes.data.code === 200) {
      industryData = industryRes.data;
    } else if (industryRes.code === 200) {
      industryData = industryRes;
    } else {
      console.error('无法识别的就业行业分布返回格式:', industryRes);
      industryData = null;
    }
    
    if (employmentIndustryChart && industryData && industryData.code === 200 && industryData.data) {
      console.log('解析就业行业分布数据:', industryData.data);
      
      // 处理新的数据格式 {list: [...], year, total}
      const industries = industryData.data.list || [];
      console.log('提取的行业数据:', industries);
      
      if (industries.length > 0) {
        const industryNames = industries.map(item => item.industry);
        const industryChartData = industries.map(item => ({
          name: item.industry,
          value: item.count
        }));
        
        console.log('就业行业分布图表数据 - 行业名称:', industryNames);
        console.log('就业行业分布图表数据 - 完整数据:', industryChartData);
        
        // 仅更新数据，不改变图表的类型和样式配置
        employmentIndustryChart.setOption({
          legend: {
            data: industryNames
          },
          series: [{
            // 保持原有的图表类型和配置
            name: '就业行业',
            type: 'pie',  // 明确指定类型，避免覆盖原始配置
            data: industryChartData
          }]
        }, false);  // 使用false参数，只更新指定的配置项
        console.log('就业行业分布图表已更新');
      } else {
        console.warn('就业行业分布数据为空');
      }
    } else {
      console.error('就业行业分布数据获取失败或格式有误:', industryData);
    }

    // 获取就业地区分布
    const regionRes = await getEmploymentRegionDistribution({ year: searchForm.year });
    console.log('就业地区分布原始响应:', regionRes);
    
    // 处理多层嵌套的响应结构
    let regionData;
    if (regionRes.data && regionRes.data.code === 200) {
      regionData = regionRes.data;
    } else if (regionRes.code === 200) {
      regionData = regionRes;
    } else {
      console.error('无法识别的就业地区分布返回格式:', regionRes);
      regionData = null;
    }
    
    if (employmentRegionChart && regionData && regionData.code === 200 && regionData.data) {
      console.log('解析就业地区分布数据:', regionData.data);
      
      // 处理新的数据格式 {list: [...], year, total}
      const regions = regionData.data.list || [];
      console.log('提取的地区数据:', regions);
      
      if (regions.length > 0) {
        const regionNames = regions.map(item => item.region);
        const regionChartData = regions.map(item => ({
          name: item.region,
          value: item.count
        }));
        
        console.log('就业地区分布图表数据 - 地区名称:', regionNames);
        console.log('就业地区分布图表数据 - 完整数据:', regionChartData);
        
        // 仅更新数据，不改变图表的类型和样式配置
        employmentRegionChart.setOption({
          legend: {
            data: regionNames
          },
          series: [{
            // 保持原有的图表类型和配置
            name: '就业地区',
            type: 'pie',  // 明确指定类型，避免覆盖原始配置
            data: regionChartData
          }]
        }, false);  // 使用false参数，只更新指定的配置项
        console.log('就业地区分布图表已更新');
      } else {
        console.warn('就业地区分布数据为空');
      }
    } else {
      console.error('就业地区分布数据获取失败或格式有误:', regionData);
    }
    
    // 获取专业评分排名
    const rankingRes = await getMajorScoreRanking({ year: searchForm.year, limit: 10 });
    console.log('专业评分排名原始响应:', rankingRes);
    
    // 处理多层嵌套的响应结构
    let rankingData;
    if (rankingRes.data && rankingRes.data.code === 200) {
      rankingData = rankingRes.data;
    } else if (rankingRes.code === 200) {
      rankingData = rankingRes;
    } else {
      console.error('无法识别的专业评分排名返回格式:', rankingRes);
      rankingData = null;
    }
    
    if (majorScoreChart && rankingData && rankingData.code === 200 && rankingData.data) {
      console.log('解析专业评分排名数据:', rankingData.data);
      
      // 处理新的数据格式 {list: [...], year, limit}
      const rankings = rankingData.data.list || [];
      console.log('提取的排名数据:', rankings);
      
      if (rankings.length > 0) {
        const majorNames = rankings.map(item => item.major);
        const scores = rankings.map(item => item.score);
        
        console.log('专业评分排名图表数据 - 专业名称:', majorNames);
        console.log('专业评分排名图表数据 - 评分:', scores);
        
        // 仅更新数据，不改变图表的类型和样式配置
        majorScoreChart.setOption({
          yAxis: {
            data: majorNames
          },
          series: [{
            // 保持原有的图表类型和配置
            name: '评分',
            type: 'bar',  // 明确指定类型，避免覆盖原始配置
            data: scores
          }]
        }, false);  // 使用false参数，只更新指定的配置项
        console.log('专业评分排名图表已更新');
      } else {
        console.warn('专业评分排名数据为空');
      }
    } else {
      console.error('专业评分排名数据获取失败或格式有误:', rankingData);
    }
  } catch (error) {
    console.error('获取图表数据失败:', error);
    message.error('获取图表数据失败');
  }
};

// 初始化数据
onMounted(() => {
  console.log('组件已挂载');
  // 初始化时获取数据
  fetchData();
  // 获取用户列表
  fetchUsersList();
  
  // 等待DOM更新后初始化图表
  nextTick(() => {
    console.log('DOM已更新，准备初始化图表');
    console.log('视图模式:', viewMode.value);
    console.log('图表DOM引用:',
      '就业率趋势图:', !!employmentRateChartRef.value,
      '就业行业分布图:', !!employmentIndustryChartRef.value,
      '就业地区分布图:', !!employmentRegionChartRef.value,
      '专业评分排名图:', !!majorScoreChartRef.value
    );
    
    if (viewMode.value === 'all') {
      initCharts();
      setTimeout(() => {
        console.log('延迟更新图表数据');
        updateCharts();
      }, 300);
    } else {
      initPersonalCharts();
      updatePersonalStats();
    }
  });
  
  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', handleResize);
});

// 处理窗口大小变化
const handleResize = () => {
  if (viewMode.value === 'all') {
    employmentRateChart && employmentRateChart.resize();
    employmentIndustryChart && employmentIndustryChart.resize();
    employmentRegionChart && employmentRegionChart.resize();
    majorScoreChart && majorScoreChart.resize();
    deductionReasonChart && deductionReasonChart.resize();
  } else {
    personalEmploymentRateChart && personalEmploymentRateChart.resize();
    personalEmploymentIndustryChart && personalEmploymentIndustryChart.resize();
    personalEmploymentRegionChart && personalEmploymentRegionChart.resize();
    personalSalaryChart && personalSalaryChart.resize();
  }
};

// 在组件卸载前移除事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  
  // 销毁所有图表实例
  employmentRateChart?.dispose();
  employmentIndustryChart?.dispose();
  employmentRegionChart?.dispose();
  majorScoreChart?.dispose();
  deductionReasonChart?.dispose();
  personalEmploymentRateChart?.dispose();
  personalEmploymentIndustryChart?.dispose();
  personalEmploymentRegionChart?.dispose();
  personalSalaryChart?.dispose();
});
</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';
.employment-quality-container {
  border: 1px solid #f0f0f0; 
  margin: 24px;
}
</style> 