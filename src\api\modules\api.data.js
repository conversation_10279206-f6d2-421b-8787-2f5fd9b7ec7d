import service from '../server'

// 获取数据导入任务列表
export const getImportTasks = (data) => {
  return service.post('/api/v1/data/import-tasks', data)
}

// 创建数据导入任务
export const createImportTask = (data) => {
  const formData = new FormData()
  formData.append('module', data.module)
  formData.append('file', data.file)
  return service.post('/api/v1/data/import-tasks/create', formData, null, 'multipart/form-data')
}

// 获取数据导出任务列表
export const getExportTasks = (data) => {
  return service.post('/api/v1/data/export-tasks', data)
}

// 创建数据导出任务
export const createExportTask = (data) => {
  return service.post('/api/v1/data/export-tasks/create', data)
} 