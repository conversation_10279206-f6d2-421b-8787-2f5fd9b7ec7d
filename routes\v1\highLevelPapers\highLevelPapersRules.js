const express = require('express');
const router = express.Router();
const highLevelPapersRulesController = require('../../../controllers/v1/highLevelPapers/highLevelPapersRulesController');

/**
 * 获取高水平论文规则列表
 * @route GET /v1/sys/highLevelPapersRules
 * @group 高水平论文规则管理 - 高水平论文规则相关接口
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @param {string} paperLevel.query - 论文级别（模糊搜索）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {total: 0, page: 1, pageSize: 10, totalPages: 0, list: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/', highLevelPapersRulesController.getHighLevelPapersRules);

/**
 * 获取高水平论文规则详情
 * @route GET /v1/sys/highLevelPapersRules/detail
 * @group 高水平论文规则管理 - 高水平论文规则相关接口
 * @param {string} id.query.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {规则详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/detail', highLevelPapersRulesController.getHighLevelPapersRuleDetail);

/**
 * 创建高水平论文规则
 * @route POST /v1/sys/highLevelPapersRules
 * @group 高水平论文规则管理 - 高水平论文规则相关接口
 * @param {string} paperLevel.body.required - 论文级别（如 A1 Ⅰ、A1 Ⅱ 等）
 * @param {number} score.body.required - 基础核算分数
 * @param {string} description.body - 论文级别的详细描述
 * @param {number} nonDepartmentAuthorCoefficient.body - 非本院研究生第一作者的系数，默认0.9
 * @param {string} coFirstAuthorRankCoefficient.body - 共同第一作者的排名系数
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {规则详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/', highLevelPapersRulesController.createHighLevelPapersRule);

/**
 * 更新高水平论文规则
 * @route PUT /v1/sys/highLevelPapersRules
 * @group 高水平论文规则管理 - 高水平论文规则相关接口
 * @param {string} id.body.required - 规则ID
 * @param {string} paperLevel.body - 论文级别（如 A1 Ⅰ、A1 Ⅱ 等）
 * @param {number} score.body - 基础核算分数
 * @param {string} description.body - 论文级别的详细描述
 * @param {number} nonDepartmentAuthorCoefficient.body - 非本院研究生第一作者的系数
 * @param {string} coFirstAuthorRankCoefficient.body - 共同第一作者的排名系数
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {规则详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/', highLevelPapersRulesController.updateHighLevelPapersRule);

/**
 * 删除高水平论文规则
 * @route DELETE /v1/sys/highLevelPapersRules
 * @group 高水平论文规则管理 - 高水平论文规则相关接口
 * @param {string} id.query.required - 规则ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/', highLevelPapersRulesController.deleteHighLevelPapersRule);

/**
 * 批量删除高水平论文规则
 * @route DELETE /v1/sys/highLevelPapersRules/batch
 * @group 高水平论文规则管理 - 高水平论文规则相关接口
 * @param {array} ids.body.required - 规则ID数组
 * @returns {object} 200 - {code: 200, message: "批量删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/batch', highLevelPapersRulesController.batchDeleteHighLevelPapersRules);

module.exports = router; 