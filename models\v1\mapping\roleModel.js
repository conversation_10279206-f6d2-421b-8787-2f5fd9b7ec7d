// models/userModel.js
const {DataTypes} = require('sequelize');
const sequelize = require('@config/dbConfig');
const Permissions = require('./permissionsModel');
const role_permissions = require('./role_permissions');
const UserOptLog = require('./userOptLogModel');

/**
 * 角色模型
 * 表名使用下划线命名法: role
 * 字段名使用小驼峰命名法
 */
const Role = sequelize.define('role',
    {
        id: {
            type: DataTypes.CHAR(36),
            primaryKey: true,
            allowNull: false,
            defaultValue: DataTypes.UUIDV4,
            comment: '角色ID，使用UUID'
        },
        roleName: {
            type: DataTypes.STRING(255),
            notNull: true,
            comment: '角色名称'
        },
        roleAuth: {
            type: DataTypes.STRING(255),
            notNull: true,
            comment: '角色标识'
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: true,
            comment: '角色备注'
        },
        status: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
            comment: '状态: 1-启用, 0-禁用'
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '创建时间'
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '更新时间'
        }
    },
    {
        // 明确指定表名
        tableName: 'role',
        // 不使用自动复数形式
        freezeTableName: true,
        // 添加表注释
        comment: '角色表'
    });

module.exports = Role;

