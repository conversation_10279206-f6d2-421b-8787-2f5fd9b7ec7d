import request from '@/utils/request'
import axios from 'axios'
import config from '@/config'

// 就业质量相关接口
const api = {
  list: '/sys/employment/list',
  detail: '/sys/employment',
  create: '/sys/employment',
  update: '/sys/employment',
  delete: '/sys/employment',
  import: '/sys/employment/import',
  export: '/sys/employment/export',
  rateTrend: '/sys/employment/stats/rate-trend',
  industryDistribution: '/sys/employment/stats/industry-distribution',
  regionDistribution: '/sys/employment/stats/region-distribution',
  majorRanking: '/sys/employment/stats/major-ranking',
  // 个人就业质量相关接口
  personalList: '/sys/employment/personal',
  personalStats: '/sys/employment/personal-stats'
}

// 获取就业质量列表
export function getEmploymentQualities(params) {
  return request.get(api.list, params)
}

// 获取就业质量详情
export function getEmploymentQualityDetail(id) {
  return request.get(`${api.detail}/${id}`)
}

// 添加就业质量
export function addEmploymentQuality(data) {
  return request.post(api.create, data)
}

// 更新就业质量
export function updateEmploymentQuality(id, data) {
  return request.put(`${api.update}/${id}`, data)
}

// 删除就业质量
export function deleteEmploymentQuality(id) {
  return request.delete(`${api.delete}/${id}`)
}

// 导入就业质量数据
export function importEmploymentQualities(file) {
  const formData = new FormData()
  if (file instanceof File) {
    formData.append('file', file)
  } else {
    formData.append('file', file.file)
  }
  
  // 使用request对象正确处理文件上传
  const url = `${config.baseUrl}${api.import}`
  return axios.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出就业质量数据
export function exportEmploymentQualities(params) {
  const url = `${config.baseUrl}${api.export}`
  return axios.get(url, { 
    params,
    responseType: 'blob'
  })
}

// 获取就业率趋势
export function getEmploymentRateTrend(params) {
  return request.get(api.rateTrend, params)
}

// 获取就业行业分布
export function getEmploymentIndustryDistribution(params) {
  return request.get(api.industryDistribution, params)
}

// 获取就业地区分布
export function getEmploymentRegionDistribution(params) {
  return request.get(api.regionDistribution, params)
}

// 获取专业评分排名
export function getMajorScoreRanking(params) {
  return request.get(api.majorRanking, params)
}

// 获取个人就业质量列表
export function getPersonalEmployments(params) {
  return request.get(api.personalList, params)
}

// 获取个人就业质量统计
export function getPersonalEmploymentStats(params) {
  return request.get(api.personalStats, params)
} 