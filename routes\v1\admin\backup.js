/**
 * 数据库备份管理路由
 */

const express = require('express');
const router = express.Router();
const backupController = require('../../../controllers/v1/admin/backupController');

// 获取备份文件列表
router.get('/list', backupController.getBackupList);

// 手动执行备份
router.post('/create', backupController.createBackup);

// 获取定时任务状态
router.get('/scheduler/status', backupController.getSchedulerStatus);

// 启动定时备份任务
router.post('/scheduler/start', backupController.startBackupTask);

// 停止定时备份任务
router.post('/scheduler/stop', backupController.stopBackupTask);

// 下载备份文件
router.get('/download/:fileName', backupController.downloadBackup);

// 删除备份文件
router.delete('/:fileName', backupController.deleteBackup);

// 获取备份配置信息
router.get('/config', backupController.getBackupConfig);

module.exports = router;
