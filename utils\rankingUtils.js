const userRankingReviewedAllModel = require('../models/v1/mapping/userRankingReviewedAllModel');
const userRankingReviewedOutModel = require('../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedInModel = require('../models/v1/mapping/userRankingReviewedInModel');
const userModel = require('../models/v1/mapping/userModel');
const logger = require('./logger');

// 添加映射常量，将类型名映射到对应的计数字段和分数字段
const RANKING_TYPE_MAPPINGS = {
  'academicAppointments': { countField: 'appointmentCount', scoreField: 'appointmentScore' },
  'studentAwardGuidance': { countField: 'awardCount', scoreField: 'awardScore' },
  'conferences': { countField: 'conferenceCount', scoreField: 'conferenceScore' },
  'highLevelPapers': { countField: 'paperCount', scoreField: 'paperScore' },
  'patents': { countField: 'patentCount', scoreField: 'patentScore' },
  'researchProjects': { countField: 'researchCount', scoreField: 'researchScore' },
  'studentProjectGuidance': { countField: 'studentProjectCount', scoreField: 'studentProjectScore' },
  'teachingReformProjects': { countField: 'teachingProjectCount', scoreField: 'teachingProjectScore' },
  'teachingWorkloads': { countField: 'teachingWorkloadCount', scoreField: 'teachingWorkloadScore' },
  'teachingResearchAwards': { countField: 'teachingResearchAwardCount', scoreField: 'teachingResearchAwardScore' },
  'textbooks': { countField: 'textbookCount', scoreField: 'textbookScore' }
};

/**
 * 更新指定用户的排名数据
 * 用于在审核、删除、修改完毕后更新排名表
 * @param {Array<string>} userIds - 需要更新的用户ID数组
 * @param {string} tableName - 要更新的表名 ('user_ranking_reviewed_in', 'user_ranking_reviewed_out', 'user_ranking_reviewed_all')
 * @param {string} typeName - 类型名称 ('academicAppointments', 'studentAwardGuidance', 'conferences', 'highLevelPapers', 'patents', 'researchProjects', 'studentProject', 'teachingProject', 'textbook')
 * @param {number|Array<number>} totalDelta - 计数变化量，可以是单个数字或数组(与userIds长度相同)
 * @param {number|Array<number>} scoreDelta - 分数变化量，可以是单个数字或数组(与userIds长度相同)
 * @param {Object} [transaction] - 可选的事务对象，用于确保数据一致性
 * @param {string} [operation="add"] - 操作类型："add"(加分)或"subtract"(减分)
 * @returns {Promise<Object>} - 包含更新结果的对象
 */
const updateUserRankings = async (userIds, tableName, typeName, totalDelta, scoreDelta, transaction = null, operation = "add") => {
  try {
    // 参数校验
    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new Error('参数错误：用户ID数组不能为空');
    }

    // 验证表名是否有效
    const validTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
    if (!validTables.includes(tableName)) {
      throw new Error(`参数错误：无效的表名 ${tableName}`);
    }

    // 验证typeName是否有效
    const validTypes = Object.keys(RANKING_TYPE_MAPPINGS);
    if (typeName && !validTypes.includes(typeName)) {
      throw new Error(`参数错误：无效的类型名称 ${typeName}`);
    }

    // 验证operation是否有效
    if (operation !== "add" && operation !== "subtract") {
      throw new Error('参数错误：operation必须是"add"或"subtract"');
    }

    // 根据tableName选择对应的模型
    let RankingModel;
    switch (tableName) {
      case 'user_ranking_reviewed_in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'user_ranking_reviewed_out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'user_ranking_reviewed_all':
        RankingModel = userRankingReviewedAllModel;
        break;
    }

    // 处理totalDelta和scoreDelta，确保它们是数组
    const totalDeltaArray = Array.isArray(totalDelta) ? totalDelta : Array(userIds.length).fill(totalDelta);
    const scoreDeltaArray = Array.isArray(scoreDelta) ? scoreDelta : Array(userIds.length).fill(scoreDelta);

    // 确保数组长度一致
    if (totalDeltaArray.length !== userIds.length || scoreDeltaArray.length !== userIds.length) {
      throw new Error('参数错误：totalDelta和scoreDelta数组长度必须与userIds数组长度一致');
    }

    // 更新用户数据
    for (let i = 0; i < userIds.length; i++) {
      const userId = userIds[i];
      const currentTotalDelta = totalDeltaArray[i];
      const currentScoreDelta = scoreDeltaArray[i];
      console.log("userId===", userId);
      // 查询当前用户的排名记录
      let userRanking = await RankingModel.findOne({
        where: { userId },
        transaction // 使用传入的事务
      });
      console.log("userRanking", userRanking);

      // 如果不存在排名记录，则创建一个新记录
      if (!userRanking) {
        const userInfo = await userModel.findByPk(userId, { transaction });
        userRanking = await RankingModel.create(
          { userId, nickName: userInfo.nickname, studentNumber: userInfo.studentNumber},
          { transaction } // 使用传入的事务
        );
      }

      // 如果有指定类型，更新对应类型的计数和分数
      if (typeName) {
        // 根据typeName获取对应的字段名
        const { countField, scoreField } = RANKING_TYPE_MAPPINGS[typeName];

        // 更新计数（如果有提供totalDelta）
        if (currentTotalDelta !== undefined) {
          const updateData = {};
          const deltaValue = operation === "add" ? currentTotalDelta : -currentTotalDelta;
          // 确保转换为数字后再进行加减操作
          const currentCount = parseFloat(userRanking[countField] || 0) || 0;
          updateData[countField] = currentCount + deltaValue;
          // 确保计数不为负
          if (updateData[countField] < 0) updateData[countField] = 0;
          console.log("updateData更新计数", updateData);
          await userRanking.update(updateData, { transaction }); // 使用传入的事务
        }

        // 更新分数（如果有提供scoreDelta）
        if (currentScoreDelta !== undefined) {
          const updateData = {};
          // 确保deltaValue是数字类型
          const deltaValue = parseFloat(operation === "add" ? currentScoreDelta : -currentScoreDelta);

          if (isNaN(deltaValue)) {
            console.error(`分数变化量无效: ${currentScoreDelta}`);
            throw new Error(`分数变化量无效: ${currentScoreDelta}`);
          }

          // 确保转换为数字后再进行加减操作
          const currentScore = parseFloat(userRanking[scoreField] || 0) || 0;

          // 计算新分数并确保结果是数字
          const newScore = currentScore + deltaValue;
          // 使用Number()确保是数字类型后再调用toFixed
          updateData[scoreField] = Number(newScore).toFixed(2);

          // 确保分数不为负
          if (parseFloat(updateData[scoreField]) < 0) updateData[scoreField] = "0.00";

          console.log("updateData更新分数", updateData);
          await userRanking.update(updateData, { transaction }); // 使用传入的事务
        }
      }

      // 重新计算总计数和总分数
      // 获取最新的用户排名数据
      userRanking = await RankingModel.findOne({
        where: { userId },
        transaction // 使用传入的事务
      });

      // 计算总项目数 - 使用所有countField
      const totalItemCount = Object.values(RANKING_TYPE_MAPPINGS).reduce((sum, { countField }) => {
        return sum + (parseFloat(userRanking[countField] || 0) || 0);
      }, 0);

      // 计算总分 - 使用所有scoreField
      const totalScore = Object.values(RANKING_TYPE_MAPPINGS).reduce((sum, { scoreField }) => {
        const fieldValue = parseFloat(userRanking[scoreField] || 0) || 0;
        return sum + fieldValue;
      }, 0);

      // 更新总计数和总分
      await userRanking.update({
        totalItemCount: totalItemCount,
        totalScore: Number(totalScore).toFixed(2) // 确保是数字类型后再格式化为两位小数的字符串
      }, { transaction }); // 使用传入的事务
    }

    // 重新计算排名
    // 获取所有记录并按总分降序排序
    const allRankings = await RankingModel.findAll({
      order: [['totalScore', 'DESC']],
      transaction // 使用传入的事务
    });

    // 更新排名
    for (let i = 0; i < allRankings.length; i++) {
      await allRankings[i].update({
        rank: i + 1
      }, { transaction }); // 使用传入的事务
    }

    logger.info(`成功更新 ${userIds.length} 个用户的排名数据，操作：${operation}`);

    return {
      success: true,
      tableName,
      operation,
      updatedUsers: userIds.length,
      updateTime: new Date()
    };
  } catch (error) {
    logger.error('更新用户排名数据失败:', error);
    throw new Error(`更新用户排名数据失败: ${error.message}`);
  }
};

// 导出常量和函数
module.exports = {
  RANKING_TYPE_MAPPINGS,
  updateUserRankings
};