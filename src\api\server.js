import axios from 'axios';
import dbUtils from "../libs/util.strotage";
import {ZyNotification} from "../libs/util.toast";
// 不能使用useRouter ,useRoute，他们需要在setup中调用执行后才能用
import router from '@/router'
// 导入配置
import config from '../config';

// 创建 Axios 实例
const instance = axios.create({
    // 使用从配置中读取的API基础URL
    baseURL: config.apiBaseUrl,
    timeout: config.requestTimeout, // 使用配置文件中的超时设置
    withCredentials: false, // 禁用跨域请求携带cookie，避免CORS问题
    headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
    }
});

// 请求拦截器
instance.interceptors.request.use(
    (config) => {
        // 在请求发送之前可以进行一些处理，例如设置 token、添加请求头等
        console.log('========== API请求开始 ==========');
        console.log(`请求方法: ${config.method.toUpperCase()}`);
        console.log(`请求URL: ${config.baseURL}${config.url}`);
        console.log(`请求参数: ${JSON.stringify(config.params || {})}`);
        if (config.data) {
            console.log(`请求数据: ${JSON.stringify(config.data)}`);
        }
        console.log(`请求头: ${JSON.stringify(config.headers)}`);
        console.log(`环境变量API基础URL: ${import.meta.env.VITE_API_BASE_URL}`);

        // 从多个可能的位置获取token
        let token = dbUtils.get('token')

        // 调试token信息
        if (token) {

            // 检查token格式是否正确
            if (!token.startsWith('Bearer ')) {
                console.log('Token格式不正确，添加Bearer前缀')
                token = 'Bearer ' + token
            }

            config.headers.authorization = token;

            // 对需要权限验证的API请求添加时间戳和签名
            if (!config.url.includes('/auth/login') && !config.url.includes('/auth/captcha')) {
                // 添加时间戳，可用于服务器判断请求是否过期
                const timestamp = Date.now()
                config.headers['X-Timestamp'] = timestamp

                // 可以在这里添加其他安全相关的头信息
                config.headers['X-Client'] = 'zyadmin-web'
            }
        } else {
            console.log('API请求无token')
            // 如果是登录请求，不需要token
            if (config.url.includes('/auth/login') || config.url.includes('/auth/captcha')) {
                console.log('登录或验证码请求，无需token')
            } else {
                console.log('警告: 需要授权的请求无token!')
            }
        }

        // 设置禁用缓存的头部
        config.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
        config.headers['Pragma'] = 'no-cache';
        config.headers['Expires'] = '0';

        // 添加时间戳参数防止缓存，但排除验证码请求
        if (config.method === 'get' && !config.url.includes('/auth/captcha')) {
            config.params = config.params || {};
            config.params._t = new Date().getTime();
        } else if (config.url.includes('/auth/captcha')) {
        }

        return config;
    },
    (error) => {
        console.error('API请求配置错误:', error)
        return Promise.reject(error);
    }
);


// 响应拦截器
instance.interceptors.response.use(
    (response) => {
        // 在接收到响应数据之前可以进行一些处理，例如解析响应数据、错误处理等
        console.log('========== API响应开始 ==========');
        console.log(`响应URL: ${response.config.url}`);
        console.log(`响应状态: ${response.status}`);
        console.log(`响应头: ${JSON.stringify(response.headers)}`);

        const contentType = response.headers['content-type'];
        console.log(`响应内容类型: ${contentType}`);

        if (contentType === 'application/octet-stream' ||
            contentType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            console.log('检测到文件下载请求');
            // 注意：Blob类型文件下载需要请求头参数添加 responseType:'blob'  下载 导出等功能需要
            downloadFile(response)
        } else {
            // 响应数据是有效的 JSON 格式，继续处理
            if (response.data) {
                console.log(`响应数据: ${JSON.stringify(response.data)}`);

                // 检查响应数据中的code字段
                if (response.data.code) {
                    console.log(`响应状态码: ${response.data.code}`);
                    if (response.data.code !== 200) {
                        console.warn(`API请求返回非正常状态码: ${response.data.code}, 消息: ${response.data.message}`);
                    }
                }
            }
            console.log('========== API响应结束 ==========');
            return Promise.resolve(response.data);
        }
    },
    (error) => {
        // 统一处理错误
        console.error('========== API响应错误 ==========');
        if (error.config) {
            console.error(`请求URL: ${error.config.url}`);
            console.error(`请求方法: ${error.config.method}`);
            console.error(`请求参数: ${JSON.stringify(error.config.params || {})}`);
            if (error.config.data) {
                console.error(`请求数据: ${error.config.data}`);
            }
        }
        if (error.response) {
            console.error(`响应状态: ${error.response.status}`);
            console.error(`响应数据: ${JSON.stringify(error.response.data || {})}`);
        } else {
            console.error(`错误信息: ${error.message}`);
        }
        console.error('========== API响应错误结束 ==========');
        return handleRequestError(error)
    }
);

// 下载blob二进制文件
const downloadFile = (response) => {
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const filename = response.headers['x-filename'];

    axios.get(url, {responseType: 'blob'}).then((res) => {
        const blob = new Blob([res.data]);
        if (window.navigator.msSaveBlob) {
            // 兼容 IE，使用 msSaveBlob 方法进行下载
            window.navigator.msSaveBlob(blob, decodeURIComponent(filename));
        } else {
            // 创建一个 <a> 元素
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.setAttribute('download', decodeURIComponent(filename));
            // 模拟点击下载
            link.click();
            // 清理 URL 和 <a> 元素
            link.remove();
            window.URL.revokeObjectURL(url);
        }
    });
}

// 统一处理错误
const handleRequestError = (error) => {
    // 进行错误处理
    if (error.response) {
        // 服务器响应错误
        let status = error.response.status
        console.error('API错误：', status, error.response.data)

        // 在这里可以进行错误处理逻辑，例如弹出错误提示、记录错误日志等
        switch (status) {
            case 400:
                console.error('参数校验失败:', error.response.data.message);
                ZyNotification.error(error.response.data.message || '参数校验失败')
                return Promise.reject(error.response.data.message ?? '参数json解析失败');
            case 401:
                console.error('未授权:', error.response.data.message);

                // 检查是否是登录过程中的授权错误
                const currentPath = window.location.hash;
                const isLoginPage = currentPath.includes('/login')
                const isTokenError = error.response.data.message === 'Token不存在或已过期' ||
                                    error.response.data.message.includes('无效的认证Token')

                console.log('授权错误详情：', {
                    当前页面: currentPath,
                    是否登录页: isLoginPage,
                    是否Token错误: isTokenError,
                    消息: error.response.data.message
                })

                if (isLoginPage) {
                    console.log('登录过程中的授权错误，可能是token刚刚生成，仅记录错误')
                    return Promise.reject({
                        error: '401',
                        message: error.response.data.message,
                        inLoginProcess: true
                    });
                }

                if (isTokenError) {
                    console.log('Token已过期或无效，清除存储并重定向到登录页')
                    // 清除所有token存储
                    localStorage.removeItem(`zyadmin-1.0.0-token`)
                    dbUtils.remove('token')
                    dbUtils.remove('userInfo')
                    dbUtils.remove('perms')

                    router.replace('/login')
                    ZyNotification.error('会话已过期，请重新登录')
                    return Promise.reject({
                        error: 'Unauthorized',
                        message: '会话已过期，请重新登录'
                    });
                } else {
                    ZyNotification.error(error.response.data.message || '账号已过期,请重新登录')
                    return Promise.reject({
                        error: '401',
                        message: error.response.data.message
                    });
                }

            case 404:
                console.error('404:', error.response.data.message);
                ZyNotification.error(error.response.data.message || '资源不存在')
                return Promise.reject({error: '接口不存在', message: error.response.data.message});
            case 500:
                console.error('服务器内部错误:', error.response.data.message);
                ZyNotification.error(error.response.data.message || '服务器内部错误')
                return Promise.reject({error: '服务器内部错误', message: error.response.data.message});
            default:
                ZyNotification.error('服务器响应错误')
                console.error('服务器响应错误:', error.response.data);
                return Promise.reject({error: `HTTP ${status}`, message: '服务器响应错误'});
        }

    } else if (error.request) {
        // 请求未收到响应
        console.error('请求未收到响应:', error.request);
        ZyNotification.error('请求未收到响应，请检查网络连接')
        return Promise.reject({error: 'NetworkError', message: '请求未收到响应，请检查网络连接'});
    } else {
        // 请求配置出错
        console.error('请求配置出错:', error.message);
        ZyNotification.error('请求配置出错: ' + error.message)
        return Promise.reject({error: 'RequestError', message: error.message});
    }
};

// 封装请求方法
class AxiosService {
    constructor() {
        if (AxiosService.instance) {
            return AxiosService.instance;
        }
        AxiosService.instance = this;
    }

    // GET 请求
    get(url, params = null) {
        return instance.request({
            method: 'get',
            url,
            params,
        });
    }

    // POST 请求
    post(url, data = null, params = null, responseType) {
        return instance.request({
            method: 'post',
            url,
            data,
            params,
            responseType
        });
    }

    // PUT 请求
    put(url, data = null, params = null) {
        return instance.request({
            method: 'put',
            url,
            data,
            params,
        });
    }

    // DELETE 请求
    delete(url, params = null) {
        return instance.request({
            method: 'delete',
            url,
            params,
        });
    }

}

// 创建 AxiosService 实例
const axiosService = new AxiosService();

// 导出实例化后的 AxiosService 对象
export default axiosService;
