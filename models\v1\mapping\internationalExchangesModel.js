const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义国际交流模型
const Exchange = sequelize.define('international_exchanges', {
  id: {
    type: DataTypes.CHAR(36),
    primaryKey: true,
    allowNull: false,
    comment: 'ID'
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '交流名称'
  },
  type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '交流类型'
  },
  country: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '交流国家/地区'
  },
  institution: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '交流机构'
  },
  startDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '开始时间'
  },
  endDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '结束时间'
  },
  userIdList: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '参与用户ID列表'
  },
  usernameList: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '参与用户列表'
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '交流内容'
  },
  result: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '交流成果'
  },
  score: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '得分'
  },
  ifReviewer: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    comment: '审核状态（0，拒审核 1，审核，null未审核）'
  },
  attachmentUrl: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '附件URL'
  },
  reviewComment: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核意见'
  },
  reviewerId: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: '审核人 ID'
  },
  status: {
    type: DataTypes.TINYINT(1),
    allowNull: true,
    defaultValue: 1,
    comment: '状态'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'international_exchanges',
  timestamps: true,
  indexes: [
    {
      name: 'idx_exchange_type',
      fields: ['type']
    },
    {
      name: 'idx_exchange_country',
      fields: ['country']
    },
    {
      name: 'idx_exchange_institution',
      fields: ['institution']
    },
    {
      name: 'idx_exchange_dates',
      fields: ['startDate', 'endDate']
    },
    {
      name: 'idx_exchange_status',
      fields: ['status']
    },
    {
      name: 'idx_exchange_user_ids',
      fields: ['userIdList'],
      type: 'FULLTEXT'
    }
  ]
});

module.exports = Exchange; 