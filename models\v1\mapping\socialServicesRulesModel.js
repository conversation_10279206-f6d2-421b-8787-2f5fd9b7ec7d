const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义社会服务核算规则模型
module.exports = sequelize.define('social_services_rules', // 数据库表名为social_services_rules
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: '唯一标识符，随机生成的 UUID',
        },
        contributionName: {
            type: DataTypes.STRING(255),
            notNull: true,
            allowNull: false,
            comment: '贡献的主类型（如"创办及参与重要学术期刊"）',
        },
        secondaryOption: {
            type: DataTypes.STRING(255),
            notNull: true,
            allowNull: false,
            comment: '贡献的二级选择（如"主编"、"副主编"、"编委"等）',
        },
        score: {
            type: DataTypes.INTEGER,
            notNull: true,
            allowNull: false,
            comment: '该二级选择对应的得分',
        },
        createdBy: {
            type: DataTypes.UUID,
            notNull: true,
            allowNull: false,
            comment: '创建者 ID（与 id 一致的 UUID）',
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录创建时间',
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: DataTypes.NOW,
            comment: '记录最后修改时间',
        },
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
        indexes: [
            {
                unique: true,
                fields: ['contribution_name', 'secondary_option'],
                name: 'uk_contribution_secondary'
            }
        ]
    }); 