<template>
  <div @click="clickLogo">
    <div class="side-logo">
      <img :src="setting.websiteInfo.logo || url || defaultLogo">
    </div>
  </div>
</template>

<script setup>
import {ref} from 'vue'
// 使用在线默认logo替代本地不存在的图片
import setting from '@/setting.js';

const defaultLogo = 'https://jnumed.jnu.edu.cn/_upload/tpl/02/6a/618/template618/images/logo.png';

const props = defineProps(
    {
      showLogo: {
        type: Boolean,
        default: true,
      },
      isSidebarOpen: {
        type: Boolean,
        default: true,
      },
      url: {
        type: String,
        default: ''
      },
      title: {
        type: String,
        default: '暨南大学基础医学与公共卫生学院教师绩效评定与管理平台'
      },
    }
)
// 定义可触发的事件
const emit = defineEmits(['logo-click',])
const clickLogo = () => {
  emit('logo-click')
}
</script>

<style lang="scss" scoped>
.side-logo {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  padding: 0 10px;
  cursor: pointer;
  
  img {
    width: 100%;
    max-width: 150px;
    height: auto;
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.08));
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
}

.logo-noname {
  justify-content: flex-start;
  padding-left: 16px;
}
</style>
