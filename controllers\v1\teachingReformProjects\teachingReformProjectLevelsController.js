const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const { getTimeIntervalByName } = require('../../../utils/others');
const projectModel = require('../../../models/v1/mapping/teachingReformProjectsModel');
const projectLevelModel = require('../../../models/v1/mapping/teachingReformProjectLevelsModel');
const participantModel = require('../../../models/v1/mapping/teachingReformParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');

/**
 * 获取项目级别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectLevels = async (req, res) => {
  try {
    const levels = await projectLevelModel.findAll({
      order: [['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levels
    });
  } catch (error) {
    console.error('获取项目级别列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目级别列表失败',
      error: error.message
    });
  }
};

/**
 * 获取项目级别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }
    
    // 查询级别详情
    const level = await projectLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到项目级别',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: level
    });
  } catch (error) {
    console.error('获取项目级别详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目级别详情失败',
      error: error.message
    });
  }
};

/**
 * 创建项目级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createLevel = async (req, res) => {
  try {
    const { levelName, score, remark } = req.body;
    
    // 验证必要字段
    if (!levelName || score === undefined) {
      return res.status(400).json({
        code: 400,
        message: '级别名称和分数不能为空',
        data: null
      });
    }
    
    // 检查级别名称是否已存在
    const existingLevel = await projectLevelModel.findOne({
      where: {
        levelName: levelName
      }
    });
    
    if (existingLevel) {
      return res.status(400).json({
        code: 400,
        message: '级别名称已存在',
        data: null
      });
    }
    
    // 创建项目级别
    const level = await projectLevelModel.create({
      id: uuidv4(),
      levelName,
      score,
      remark
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: level
    });
  } catch (error) {
    console.error('创建项目级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建项目级别失败',
      error: error.message
    });
  }
};

/**
 * 更新项目级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateLevel = async (req, res) => {
  try {
    const { id } = req.params;
    const { levelName, score, remark } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }
    
    // 查询级别是否存在
    const level = await projectLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到项目级别',
        data: null
      });
    }
    
    // 如果要更新级别名称，检查新名称是否已存在
    if (levelName && levelName !== level.levelName) {
      const existingLevel = await projectLevelModel.findOne({
        where: {
          levelName: levelName,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingLevel) {
        return res.status(400).json({
          code: 400,
          message: '级别名称已存在',
          data: null
        });
      }
    }
    
    // 更新级别
    const updateData = {};
    if (levelName !== undefined) updateData.levelName = levelName;
    if (score !== undefined) updateData.score = score;
    if (remark !== undefined) updateData.remark = remark;
    
    await level.update(updateData);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: await projectLevelModel.findByPk(id)
    });
  } catch (error) {
    console.error('更新项目级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新项目级别失败',
      error: error.message
    });
  }
};

/**
 * 删除项目级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteLevel = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }
    
    // 查询级别是否存在
    const level = await projectLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到项目级别',
        data: null
      });
    }
    
    // 检查是否有项目使用此级别
    const projectsCount = await projectModel.count({
      where: { levelId: id }
    });
    
    if (projectsCount > 0) {
      return res.status(400).json({
        code: 400,
        message: `该级别已被${projectsCount}个项目使用，无法删除`,
        data: null
      });
    }
    
    // 删除级别
    await level.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除项目级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除项目级别失败',
      error: error.message
    });
  }
};

/**
 * 获取所有级别及其项目数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelsWithCount = async (req, res) => {
  try {
    // 获取所有级别
    const levels = await projectLevelModel.findAll({
      order: [['createdAt', 'ASC']]
    });
    
    // 获取每个级别的项目数量
    const result = await Promise.all(levels.map(async (level) => {
      const count = await projectModel.count({
        where: { levelId: level.id }
      });
      
      return {
        ...level.toJSON(),
        projectCount: count
      };
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取级别及项目数量失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取级别及项目数量失败',
      error: error.message
    });
  }
};

/**
 * 获取项目级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
  try {
    const { range = 'all', userId } = req.body; // range: 'in', 'out', 'all'
    
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingReformProjects");
    
    // 构建查询条件
    let projectIds = [];
    
    // 如果提供了userId，只统计该用户参与的项目
    if (userId) {
      const participations = await participantModel.findAll({
        where: { userId: userId },
        attributes: ['projectId']
      });
      
      projectIds = participations.map(p => p.projectId);
      
      if (projectIds.length === 0) {
        // 该用户没有参与项目
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
    }
    
    // 查询所有项目
    const projects = await projectModel.findAll({
      where: projectIds.length > 0 ? { id: { [Op.in]: projectIds } } : {},
      include: [
        {
          model: projectLevelModel,
          as: 'level',
          attributes: ['id', 'levelName'],
          required: true,
        },
      ]
    });
    
    // 初始化级别数据
    const levelData = {};
    
    // 统计项目级别分布
    projects.forEach(project => {
      const projectJson = project.toJSON();
      
      // 检查项目是否在时间范围内
      const isInRange = timeInterval && projectJson.approvalDate ? 
        isDateInTimeRange(projectJson.approvalDate, timeInterval.startTime, timeInterval.endTime) : 
        false;
      
      // 根据查询范围筛选项目
      if ((range === 'in' && isInRange) || 
          (range === 'out' && !isInRange) || 
          range === 'all') {
        
        const levelName = projectJson.level.levelName;
        levelData[levelName] = (levelData[levelName] || 0) + 1;
      }
    });
    
    // 转换为前端期望的格式：[{name, value}]
    const result = Object.entries(levelData).map(([name, value]) => ({ name, value }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取项目级别分布数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取项目级别分布数据失败',
      error: error.message
    });
  }
};

/**
 * 判断日期是否在时间范围内
 * @param {string} dateStr - 日期字符串
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isDateInTimeRange(dateStr, startDate, endDate) {
  if (!dateStr || !startDate || !endDate) return false;
  
  // 将日期字符串转换为日期对象
  const dateObj = new Date(dateStr);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  
  // 日期必须在开始日期和结束日期之间
  return dateObj >= startDateObj && dateObj <= endDateObj;
}

/**
 * 获取级别下的项目列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getProjectsByLevel = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }
    
    // 查询级别是否存在
    const level = await projectLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到项目级别',
        data: null
      });
    }
    
    // 查询该级别下的所有项目
    const projects = await projectModel.findAll({
      where: { levelId: id },
      include: [
        {
          model: participantModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'user',
              attributes: ['id', 'nickname', 'username', 'studentNumber']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: projects
    });
  } catch (error) {
    console.error('获取级别项目列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取级别项目列表失败',
      error: error.message
    });
  }
}; 