const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const textbookCategoriesModel = sequelize.define('textbook_categories', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
    comment: '类别 ID'
  },
  categoryAndPosition: {
    type: DataTypes.STRING(150),
    allowNull: false,
    unique: true,
    comment: '类别及任职（唯一）'
  },
  score: {
    type: DataTypes.STRING(36),
    allowNull: false,
    comment: '对应分数'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false
  }
}, {
  tableName: 'textbook_categories',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['categoryAndPosition'],
      name: 'uk_category'
    }
  ]
});

module.exports = textbookCategoriesModel; 