export default {
    // 网站信息
    websiteInfo: {
        name: '暨南大学绩效评定管理系统',
        version: '1.0.0',
        desc: '暨南大学基础医学与公共卫生学院教师绩效评定与管理平台',
        // logo仅支持在线地址
        logo: 'https://jnumed.jnu.edu.cn/_upload/tpl/02/6a/618/template618/images/logo.png',
    },


    // 快捷键
    // 支持快捷键 例如 ctrl+shift+s
    hotkey: {
        search: {
            open: 'ctrl+s',
            close: 'esc'
        }
    },
    // 页脚备案信息
    reference: {
        show: true, //是否展示页脚
        number: '粤ICP备XXXXXXXX号',//备案号
        authorization: '暨南大学', // 站点所属机构
        authorizationUrl: 'https://jnumed.jnu.edu.cn/main.htm', // 站点所属机构链接
    },
    // 注册的主题  fixed:true, 默认主题（必填一个）
    theme: {
        list: [
            {
                name: '暨南淡绿',
                fixed: true,
                value: {
                    primaryColor: '#7fc7a0',  // 淡绿色主色调
                    errorColor: '#e07f7f',
                    warningColor: '#e6bf7c',
                    successColor: '#6dae84',
                    infoColor: '#8fbaad',
                }
            },
            {
                name: '暨南蓝金',
                fixed: false,
                value: {
                    primaryColor: '#003e7e',
                    errorColor: '#c54e4e',
                    warningColor: '#daa520',
                    successColor: '#4e8550',
                    infoColor: '#6486a4',
                }
            },
            {
                name: '金色沙滩',
                fixed: false,
                value: {
                    primaryColor: '#d2b48c',
                    errorColor: '#c98a7d',
                    warningColor: '#e2b14c',
                    successColor: '#80b178',
                    infoColor: '#d8c49a',
                }
            },
            {
                name: '薄荷清新',
                value: {
                    primaryColor: '#88c7b1',
                    errorColor: '#e88f78',
                    warningColor: '#f0c175',
                    successColor: '#85b17e',
                    infoColor: '#c6d4a2',
                }

            },
            {
                name: '海岸微风',
                value: {
                    primaryColor: '#8db6d2',
                    errorColor: '#d98f83',
                    warningColor: '#e7c687',
                    successColor: '#a3cc9c',
                    infoColor: '#d8d6a7',
                }

            },
            {
                name: '宁静花园',
                value: {
                    primaryColor: '#b7d1a4',
                    errorColor: '#e79585',
                    warningColor: '#f2ce6e',
                    successColor: '#8eb392',
                    infoColor: '#d7d3ae',
                }

            },
            {
                name: '宁静水域',
                value: {
                    primaryColor: '#a4cfd9',
                    errorColor: '#e89f8a',
                    warningColor: '#f2cf7d',
                    successColor: '#9dc4a1',
                    infoColor: '#dcd9be',
                }

            },
            {
                "name": "蓝天白云",
                "value": {
                    "primaryColor": "#4fa1b1",
                    "errorColor": "#8b4172",
                    "warningColor": "#d7a643",
                    "successColor": "#539e61",
                    "infoColor": "#ac7e5f"
                }
            },
            {
                "name": "晨曦绿野",
                "value": {
                    "primaryColor": "#9c567f",
                    "errorColor": "#76a95f",
                    "warningColor": "#b04a3e",
                    "successColor": "#deba50",
                    "infoColor": "#4f97a3"
                }
            },
            {
                "name": "秋叶金黄",
                "value": {
                    "primaryColor": "#698ab0",
                    "errorColor": "#c7a446",
                    "warningColor": "#947a48",
                    "successColor": "#4b9d7d",
                    "infoColor": "#87477d"
                }
            },
            {
                "name": "玫瑰红粉",
                "value": {
                    "primaryColor": "#b4555e",
                    "errorColor": "#5a9a73",
                    "warningColor": "#dc9256",
                    "successColor": "#4c7e95",
                    "infoColor": "#976d87"
                }
            },
            {
                "name": "柠檬青绿",
                "value": {
                    "primaryColor": "#9b674a",
                    "errorColor": "#438486",
                    "warningColor": "#b95945",
                    "successColor": "#6e9a53",
                    "infoColor": "#4e7394"
                }
            },
            {
                "name": "阳光明媚",
                "value": {
                    "primaryColor": "#a98258",
                    "errorColor": "#3c8a66",
                    "warningColor": "#d27c4a",
                    "successColor": "#5280a9",
                    "infoColor": "#b75791"
                }
            },
            {
                "name": "清晨露珠",
                "value": {
                    "primaryColor": "#558c92",
                    "errorColor": "#98634e",
                    "warningColor": "#c5b54c",
                    "successColor": "#6a8754",
                    "infoColor": "#4d6b94"
                }
            },
            {
                "name": "紫罗兰梦境",
                "value": {
                    "primaryColor": "#7d608d",
                    "errorColor": "#7b9965",
                    "warningColor": "#be4b6a",
                    "successColor": "#487b98",
                    "infoColor": "#9a7057"
                }
            },
            {
                "name": "金秋收获",
                "value": {
                    "primaryColor": "#8a7d4d",
                    "errorColor": "#499b85",
                    "warningColor": "#b85b72",
                    "successColor": "#4e7c9e",
                    "infoColor": "#6d8a57"
                }
            },
            {
                "name": "梅花雪霜",
                "value": {
                    "primaryColor": "#9f5d85",
                    "errorColor": "#4f986c",
                    "warningColor": "#c16a40",
                    "successColor": "#5d7d9a",
                    "infoColor": "#826d54"
                }
            }
        ]
    },
}
