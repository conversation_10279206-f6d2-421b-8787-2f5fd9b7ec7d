const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义教师模型
module.exports = sequelize.define('teacher', // 数据库表名为teacher
    {
        id: {
            type: DataTypes.UUID,
            notNull: true,
            primaryKey: true,
            defaultValue: DataTypes.UUIDV4,
            comment: 'ID',
        },
        name: {
            type: DataTypes.STRING,
            notEmpty: true,
            notNull: true,
            allowNull: false,
            comment: '教师姓名',
        },
        title: {
            type: DataTypes.STRING,
            allowNull: true,
            comment: '职称',
        },
        department: {
            type: DataTypes.STRING,
            allowNull: true,
            comment: '所属院系',
        },
        status: {
            type: DataTypes.BOOLEAN,
            defaultValue: 1, // 1表示在职，0表示离职
            comment: '状态(1:在职 0:离职)',
        },
        remark: {
            type: DataTypes.STRING,
            allowNull: true,
            comment: '备注',
        }
    },
    {
        freezeTableName: true, // 禁止表名自动复数化
    }); 