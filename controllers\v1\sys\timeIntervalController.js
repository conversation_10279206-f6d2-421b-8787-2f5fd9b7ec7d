const { Op } = require('sequelize');
const timeIntervalModel = require('../../../models/v1/mapping/timeIntervalModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取时间区间列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTimeIntervals = async (req, res) => {
    try {
        console.log('🔍 获取时间区间列表 - 请求参数:', req.query);
        
        // 获取查询参数
        const { page = 1, pageSize = 10, category, nameC } = req.query;
        
        // 确保 page 和 pageSize 是有效的数字，否则使用默认值
        const pageNum = page ? parseInt(page) : 1;
        const pageSizeNum = pageSize ? parseInt(pageSize) : 10;

        // 如果 page 或 pageSize 是无效数字，返回默认值
        const validPage = isNaN(pageNum) || pageNum < 1 ? 1 : pageNum;
        const validPageSize = isNaN(pageSizeNum) || pageSizeNum < 1 ? 10 : pageSizeNum;
        
        console.log('📊 分页参数:', { page: validPage, pageSize: validPageSize });

        // 构建查询条件
        const where = {};
        if (category) {
            where.category = { [Op.like]: `%${category}%` };
        }
        if (nameC) {
            where.nameC = { [Op.like]: `%${nameC}%` };
        }
        
        console.log('🔍 最终查询条件:', JSON.stringify(where));

        // 分页查询
        const offset = (validPage - 1) * validPageSize;
        try {
            console.log('📚 执行数据库查询...');
            const { count, rows } = await timeIntervalModel.findAndCountAll({
                where,
                offset,
                limit: validPageSize,
                order: [['startTime', 'DESC']] // 默认按开始时间倒序
            });
            
            console.log(`✅ 查询成功: 共${count}条记录`);
            
            const totalPages = Math.ceil(count / validPageSize);
            
            return res.status(200).json({
                code: 200,
                message: '获取成功',
                data: {
                    total: count,
                    page: validPage,
                    pageSize: validPageSize,
                    totalPages,
                    list: rows
                }
            });
        } catch (dbError) {
            console.error('❌ 数据库查询失败:', dbError);
            return res.status(500).json({
                code: 500,
                message: `数据库查询失败: ${dbError.message}`,
                data: null
            });
        }
    } catch (error) {
        console.error('❌ 获取时间区间列表失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取时间区间列表失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 获取时间区间详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTimeIntervalDetail = async (req, res) => {
    try {
        const { id } = req.query;
        console.log('🔍 获取时间区间详情 - ID:', id);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        const timeInterval = await timeIntervalModel.findByPk(id);
        if (!timeInterval) {
            return res.status(404).json({
                code: 404,
                message: '未找到该记录',
                data: null
            });
        }
        
        console.log(`✅ 查询成功: 时间区间ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '获取成功',
            data: timeInterval
        });
    } catch (error) {
        console.error('❌ 获取时间区间详情失败:', error);
        return res.status(500).json({
            code: 500,
            message: `获取时间区间详情失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 创建时间区间
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createTimeInterval = async (req, res) => {
    try {
        const { category, nameC, startTime, endTime, description } = req.body;
        console.log('📝 创建时间区间 - 请求数据:', { category, nameC, startTime, endTime });
        
        // 校验必填参数
        if (!category || !nameC || !startTime || !endTime) {
            return res.status(400).json({
                code: 400,
                message: '类型、名称、开始时间和结束时间不能为空',
                data: null
            });
        }
        
        // 检查时间区间是否有效
        if (new Date(startTime) >= new Date(endTime)) {
            return res.status(400).json({
                code: 400,
                message: '开始时间必须早于结束时间',
                data: null
            });
        }
        
        // 创建时间区间
        const timeInterval = await timeIntervalModel.create({
            id: uuidv4(),
            category,
            nameC,
            startTime,
            endTime,
            description,
            createBy: req.user.id
        });
        
        console.log(`✅ 创建成功: 时间区间ID ${timeInterval.id}`);
        
        return res.status(201).json({
            code: 200,
            message: '创建成功',
            data: timeInterval
        });
    } catch (error) {
        console.error('❌ 创建时间区间失败:', error);
        return res.status(500).json({
            code: 500,
            message: `创建时间区间失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 更新时间区间
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateTimeInterval = async (req, res) => {
    try {
        const { id, category, nameC, startTime, endTime, description } = req.body;
        console.log('📝 更新时间区间 - 请求数据:', { id, category, nameC, startTime, endTime });

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        // 查找时间区间
        const timeInterval = await timeIntervalModel.findByPk(id);
        if (!timeInterval) {
            return res.status(404).json({
                code: 404,
                message: '未找到该记录',
                data: null
            });
        }

        // 检查时间区间是否有效
        if (startTime && endTime && new Date(startTime) >= new Date(endTime)) {
            return res.status(400).json({
                code: 400,
                message: '开始时间必须早于结束时间',
                data: null
            });
        }

        // 更新时间区间
        await timeInterval.update({
            category: category || timeInterval.category,
            nameC: nameC || timeInterval.nameC,
            startTime: startTime || timeInterval.startTime,
            endTime: endTime || timeInterval.endTime,
            description: description || timeInterval.description
        });
        
        console.log(`✅ 更新成功: 时间区间ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '更新成功',
            data: timeInterval
        });
    } catch (error) {
        console.error('❌ 更新时间区间失败:', error);
        return res.status(500).json({
            code: 500,
            message: `更新时间区间失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 删除时间区间
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteTimeInterval = async (req, res) => {
    try {
        const { id } = req.query;
        console.log('🗑️ 删除时间区间 - ID:', id);

        if (!id) {
            return res.status(400).json({
                code: 400,
                message: 'ID不能为空',
                data: null
            });
        }

        const timeInterval = await timeIntervalModel.findByPk(id);
        if (!timeInterval) {
            return res.status(404).json({
                code: 404,
                message: '未找到该记录',
                data: null
            });
        }

        await timeInterval.destroy();
        
        console.log(`✅ 删除成功: 时间区间ID ${id}`);
        
        return res.status(200).json({
            code: 200,
            message: '删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 删除时间区间失败:', error);
        return res.status(500).json({
            code: 500,
            message: `删除时间区间失败: ${error.message}`,
            data: null
        });
    }
};

/**
 * 批量删除时间区间
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteTimeIntervals = async (req, res) => {
    try {
        const { ids } = req.body;
        console.log('🗑️ 批量删除时间区间 - IDs:', ids);

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                code: 400,
                message: 'ID列表不能为空',
                data: null
            });
        }

        await timeIntervalModel.destroy({
            where: {
                id: {
                    [Op.in]: ids
                }
            }
        });
        
        console.log(`✅ 批量删除成功: 共${ids.length}条记录`);
        
        return res.status(200).json({
            code: 200,
            message: '批量删除成功',
            data: null
        });
    } catch (error) {
        console.error('❌ 批量删除时间区间失败:', error);
        return res.status(500).json({
            code: 500,
            message: `批量删除时间区间失败: ${error.message}`,
            data: null
        });
    }
}; 