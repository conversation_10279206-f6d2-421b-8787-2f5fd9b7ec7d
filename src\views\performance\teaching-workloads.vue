<template>
  <div class="performance-container teaching-workloads-container">
    <!-- 添加错误信息展示区域 -->
    <a-alert v-if="errorMessage" message="加载错误" :description="errorMessage" type="error" show-icon closable />

    <a-card title="教学工作量管理" :bordered="false" class="performance-card">
      <template #extra>
        <a-space>
          <!-- Excel数据导入按钮 -->
          <a-upload
            :customRequest="handleExcelToJsonConvert"
            :show-upload-list="false"
            :before-upload="beforeExcelUpload"
          >
            <a-button type="primary" v-permission="'score:teachingWorkloads:admin:update'">
              <template #icon><FileExcelOutlined /></template>
              Excel数据导入
            </a-button>
          </a-upload>
          <a-button type="primary" @click="showAddModal" v-permission="'score:teachingWorkloads:self:create'">
            <template #icon><PlusOutlined /></template>
            添加工作量
          </a-button>
          <a-button :type="showPersonalWorkloads ? 'default' : 'primary'" @click="togglePersonalWorkloads" v-permission="'score:teachingWorkloads:admin'">
            <template #icon><UserOutlined /></template>
            {{ showPersonalWorkloads ? '查看全部工作量' : '查看我的工作量' }}
          </a-button>
        </a-space>
      </template>
        <!-- 教师上课学时填写说明区域 -->
        <a-card title="教师上课学时填写说明" :bordered="false" class="performance-card" style="margin-bottom: 20px">
          <a-alert
            class="mb-16"
            message="教学工作量统计时间范围"
            :description="`统计时间：${timeRangeText || '加载中...'}`"
            type="info"
            show-icon
          />
          <div class="rule-content">
            <p><strong>填写说明：</strong></p>
            <ol class="detail-list">
              <li>教学工作量统计范围为每学年度内完成的教学任务</li>
              <li>系统将自动从教务系统导入教学工作量数据</li>
              <li>包括理论课、实验课、实践课等各类教学活动</li>
              <li>教学工作量按照学校最新标准折算成标准学时</li>
              <li>教师可在系统中查看自己的教学工作量明细</li>
              <li>如有疑问请与教务处确认</li>
              <li>分配比例已按照实际授课情况自动计算</li>
              <li>最终数据以教务处审核为准</li>
            </ol>
          </div>
        </a-card>

        <!-- 图表区域 -->
        <a-row :gutter="16" style="margin-bottom: 24px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="审核状态分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="reviewStatusChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('reviewStatus', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="reviewStatusChartRef" id="reviewStatusChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="课程类型分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="courseTypeChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('courseType', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="courseTypeChartRef" id="courseTypeChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-bottom: 10px">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="学期分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="semesterChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('semester', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="semesterChartRef" id="semesterChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-card :bordered="false" title="得分分布" size="small" class="performance-card chart-container">
              <template #extra>
                <a-select
                  v-model:value="scoreChartRange"
                  size="small"
                  style="width: 100px;"
                  @change="(value) => changeChartRange('score', value)"
                >
                  <a-select-option value="in">范围内</a-select-option>
                  <a-select-option value="out">范围外</a-select-option>
                  <a-select-option value="all">全部</a-select-option>
                </a-select>
              </template>
              <div ref="scoreChartRef" id="scoreChartContainer" class="chart-wrapper"></div>
            </a-card>
          </a-col>
        </a-row>

      <!-- 用户教学工作量得分统计表格 -->
      <a-card title="用户教学工作量得分统计" :bordered="false" style="margin-top: 24px;" >
        <template #extra>
          <a-space>
            <a-input-search
              v-model:value="userScoreSearchParams.nickname"
              v-permission="'score:teachingWorkloads:admin:list'"
              placeholder="用户昵称"
              style="width: 150px;"
              @search="fetchAllUsersTotalScore"
              @pressEnter="fetchAllUsersTotalScore"
            />
            <a-select
              v-model:value="userScoreChartRange"
              style="width: 150px;"
              @change="handleUserScoreRangeChange"
            >
              <a-select-option value="in">统计范围内</a-select-option>
              <a-select-option value="out">统计范围外</a-select-option>
              <a-select-option value="all">全部</a-select-option>
            </a-select>
            <a-button type="primary" @click="exportUserScoreData" :loading="exporting" v-permission="'score:teachingWorkloads:admin:list'">
              <template #icon><DownloadOutlined /></template>
              导出
            </a-button>
          </a-space>
        </template>

        <a-table
          :columns="userScoreColumns"
          :data-source="userScoreData"
          :pagination="currentRole?.roleAuth === 'SUPER' || currentRole?.roleAuth === 'ADMIN-LV2' ? userScorePagination : false"
          :loading="userScoreLoading"
          rowKey="userId"
          @change="handleUserScoreTableChange"
          :scroll="{ x: 800 }"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'rank'">
              <a-tag :color="getRankColor(record.rank)">{{ record.rank }}</a-tag>
            </template>
            <template v-else-if="column.key === 'totalScore'">
              <span style="font-weight: bold; color: #1890ff;">{{ record.totalScore ? parseFloat(record.totalScore).toFixed(2) : '0.00' }}分</span>
            </template>
            <template v-else-if="column.key === 'details'">
              <!-- 根据用户角色控制查看详情按钮的显示 -->
              <a-button
                v-if="currentRole?.roleAuth === 'SUPER' || currentRole?.roleAuth === 'ADMIN-LV2' || record.userId === currentUserId"
                type="link"
                @click="showUserWorkloadDetails(record)"
              >
                查看详情
              </a-button>
            </template>
          </template>
        </a-table>
      </a-card>

        <!-- 搜索表单 -->
        <a-card title="搜索筛选" :bordered="false" size="small" class="performance-card search-form" style="margin-bottom: 16px;">
          <a-form :model="searchParams" @finish="handleSearch" layout="vertical" class="performance-form">
            <a-row :gutter="[12, 8]">
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="课程名称" name="courseName">
                  <a-input
                    v-model:value="searchParams.courseName"
                    placeholder="请输入课程名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="学期" name="semester">
                  <a-input
                    v-model:value="searchParams.semester"
                    placeholder="请输入学期"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="课程类型" name="courseType">
                  <a-input
                    v-model:value="searchParams.courseType"
                    placeholder="请输入课程类型"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="学生类别" name="studentLevel">
                  <a-input
                    v-model:value="searchParams.studentLevel"
                    placeholder="请输入学生类别"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4" v-permission="'score:teachingWorkloads:admin:list'">
                <a-form-item label="主讲教师" name="teacher">
                  <a-input
                    v-model:value="searchParams.teacher"
                    placeholder="请输入主讲教师"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="统计范围" name="range">
                  <a-select
                    v-model:value="searchParams.range"
                    placeholder="请选择统计范围"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="in">范围内</a-select-option>
                    <a-select-option value="out">范围外</a-select-option>
                    <a-select-option value="all">全部</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4">
                <a-form-item label="审核状态" name="reviewStatus">
                  <a-select
                    v-model:value="searchParams.reviewStatus"
                    placeholder="请选择审核状态"
                    style="width: 100%"
                    allow-clear
                  >
                    <a-select-option value="all">全部</a-select-option>
                    <a-select-option value="reviewed">已审核</a-select-option>
                    <a-select-option value="rejected">已拒绝</a-select-option>
                    <a-select-option value="pending">待审核</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6" :lg="8" :xl="8">
                <a-form-item label=" " style="margin-bottom: 0;">
                  <div class="search-actions-inline">
                    <a-button type="primary" html-type="submit" size="default">
                      <template #icon><SearchOutlined /></template>
                      搜索
                    </a-button>
                    <a-button @click="resetSearch" size="default">
                      <template #icon><ReloadOutlined /></template>
                      重置
                    </a-button>
                    <a-button type="default" @click="exportCurrentWorkloads" :loading="exporting" size="default" v-permission="'score:teachingWorkloads:admin:list'">
                      <template #icon><DownloadOutlined /></template>
                      导出
                    </a-button>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 数据表格 -->
        <div class="performance-table">
          <a-table
            :columns="columns"
            :data-source="dataSource"
            :loading="isLoading"
            :pagination="pagination"
            @change="handleTableChange"
            rowKey="id"
            :scroll="{ x: 1200 }"
            :bordered="true"
          >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'score'">
            <span v-if="isWorkloadInScoreRange(record)" style="font-weight: bold; color: #1890ff;">{{ record.score ? parseFloat(record.score).toFixed(2) : '0.00' }}分</span>
            <span v-else style="color: #999999;">不计分</span>
          </template>
          <template v-else-if="column.key === 'participants'">
            <span style="word-break: break-all; white-space: pre-wrap;">{{ formatParticipantsWithAllocation(record.participants, record) }}</span>
          </template>
          <template v-else-if="column.key === 'courseName'">
            <span style="word-break: break-all;">{{ record.courseName }}</span>
          </template>
          <template v-else-if="column.key === 'courseType'">
            {{ getCourseTypeText(record.courseType) }}
          </template>
          <template v-else-if="column.key === 'studentLevel'">
            {{ getStudentLevelText(record.studentLevel) }}
          </template>
          <template v-else-if="column.key === 'ifReviewer'">
            <a-tag :color="record.ifReviewer === true || record.ifReviewer === 1 ? 'success' : (record.ifReviewer === false || record.ifReviewer === 0 ? 'error' : 'warning')">
              {{ record.ifReviewer === true || record.ifReviewer === 1 ? '已审核' : (record.ifReviewer === false || record.ifReviewer === 0 ? '已拒绝' : '待审核') }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-dropdown placement="bottomRight" :trigger="['click']">
              <template #overlay>
                <a-menu class="action-dropdown-menu">
                  <!-- 查看详情 -->
                  <a-menu-item
                    v-if="hasPerms('score:teachingWorkloads:self:detail')"
                    key="view"
                    @click="handleDetail(record)"
                  >
                    <a class="action-menu-item">
                      <EyeOutlined />
                      <span>查看详情</span>
                    </a>
                  </a-menu-item>

                  <!-- 编辑 -->
                  <a-menu-item
                    v-if="hasPerms('score:teachingWorkloads:self:update') && record.ifReviewer !== 'reviewed' && record.ifReviewer !== 1"
                    key="edit"
                    @click="handleEdit(record)"
                  >
                    <a class="action-menu-item">
                      <EditOutlined />
                      <span>编辑</span>
                    </a>
                  </a-menu-item>

                  <!-- 审核 -->
                  <a-menu-item
                    v-if="hasPerms('score:teachingWorkloads:admin:review') && (record.ifReviewer === 'pending' || record.ifReviewer === null || record.ifReviewer === undefined)"
                    key="review"
                    @click="handleReview(record)"
                  >
                    <a class="action-menu-item">
                      <CheckCircleOutlined />
                      <span>审核</span>
                    </a>
                  </a-menu-item>

                  <!-- 重新提交 -->
                  <a-menu-item
                    v-if="hasPerms('score:teachingWorkloads:self:reapply') && (record.ifReviewer === false || record.ifReviewer == 0)"
                    key="resubmit"
                    @click="handleResubmit(record)"
                  >
                    <a class="action-menu-item">
                      <RedoOutlined />
                      <span>重新提交</span>
                    </a>
                  </a-menu-item>

                  <!-- 删除 -->
                  <a-menu-item
                    v-if="hasPerms('score:teachingWorkloads:self:delete')"
                    key="delete"
                    @click="handleDelete(record)"
                  >
                    <a class="action-menu-item text-danger">
                      <DeleteOutlined />
                      <span>删除</span>
                    </a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small" class="action-trigger-btn">
                操作
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
          </a-table>
        </div>

        <div class="table-footer">
          <div class="total-score">
            <span>总分：{{ typeof totalScore === 'number' ? totalScore.toFixed(2) : '0.00' }}分</span>
          </div>
        </div>
    </a-card>

    <!-- 添加/编辑教学工作量模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      width="800px"
      :maskClosable="false"
      @ok="handleSubmit"
      :confirmLoading="isLoading"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :rules="rules"
      >
        <!-- 学期 -->
        <a-form-item
          name="semester"
          label="学期"
          :rules="[{ required: true, message: '请输入学期' }]"
        >
          <a-input v-model:value="formState.semester" placeholder="请输入学期，如：2023-2024-1" />
        </a-form-item>

        <!-- 课程名称 -->
        <a-form-item
          name="courseName"
          label="课程名称"
          :rules="[{ required: true, message: '请输入课程名称' }]"
        >
          <a-input v-model:value="formState.courseName" placeholder="请输入课程名称" />
        </a-form-item>

        <!-- 学生类别 -->
        <a-form-item
          name="studentLevel"
          label="学生类别"
          :rules="[{ required: true, message: '请选择学生类别' }]"
        >
          <a-select v-model:value="formState.studentLevel" placeholder="请选择学生类别">
            <a-select-option value="undergraduate">本科生</a-select-option>
            <a-select-option value="graduate">研究生</a-select-option>
            <a-select-option value="doctoral">博士生</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 课程类型 -->
        <a-form-item
          name="courseType"
          label="课程类型"
          :rules="[{ required: true, message: '请选择课程类型' }]"
        >
          <a-select v-model:value="formState.courseType" placeholder="请选择课程类型">
            <a-select-option value="theory">理论课</a-select-option>
            <a-select-option value="experiment">实验课</a-select-option>
            <a-select-option value="practice">实践课</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 课程性质 -->
        <a-form-item
          name="courseNature"
          label="课程性质"
          :rules="[{ required: true, message: '请选择课程性质' }]"
        >
          <a-select v-model:value="formState.courseNature" placeholder="请选择课程性质">
            <a-select-option value="required">必修课</a-select-option>
            <a-select-option value="elective">选修课</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 是否新开课 -->
        <a-form-item
          name="isNewCourse"
          label="是否新开课"
        >
          <a-switch v-model:checked="formState.isNewCourse" checked-children="是" un-checked-children="否" />
        </a-form-item>

        <!-- 教师授课时数 -->
        <a-form-item
          name="teachingHours"
          label="教师授课时数"
          :rules="[{ required: true, message: '请输入教师授课时数' }]"
        >
          <a-input-number
            v-model:value="formState.teachingHours"
            :min="0"
            :precision="1"
            style="width: 100%"
            placeholder="请输入教师授课时数"
            addon-after="学时"
          />
        </a-form-item>

        <!-- 授课人数 -->
        <a-form-item
          name="studentCount"
          label="授课人数"
          :rules="[{ required: true, message: '请输入授课人数' }]"
        >
          <a-input-number
            v-model:value="formState.studentCount"
            :min="1"
            :precision="0"
            style="width: 100%"
            placeholder="请输入授课人数"
            addon-after="人"
          />
        </a-form-item>

        <!-- 系/教研室 -->
        <a-form-item
          name="department"
          label="系/教研室"
        >
          <a-input v-model:value="formState.department" placeholder="请输入系/教研室" />
        </a-form-item>

        <!-- 工作量类别 -->
        <a-form-item
          name="categoryId"
          label="工作量类别"
        >
          <a-select v-model:value="formState.categoryId" placeholder="请选择工作量类别" allow-clear>
            <a-select-option v-for="level in levelOptions" :key="level.id" :value="level.id">
              {{ level.categoryName }} ({{ level.baseScore }}分)
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 工作量参与人员 -->
        <a-form-item name="participants" label="参与人员">
          <!-- 参与者选择和分配比例表单 -->
          <div class="member-selection">
            <a-row :gutter="8">
              <a-col :span="9">
                <a-form-item-rest>
                  <a-select
                    v-model:value="currentMember.userId"
                    placeholder="请选择参与人员"
                    :filter-option="false"
                    show-search
                    allow-clear
                    :loading="userSearchLoading"
                    @search="handleTeacherSearch"
                    :not-found-content="userSearchLoading ? undefined : '未找到匹配结果'"
                    @change="handleCurrentMemberChange"
                    style="width: 100%;"
                  >
                    <a-select-option v-for="(option, index) in teacherOptions" :key="option.value || index" :value="option.value" :data="option">
                      {{ option.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item-rest>
              </a-col>
              <a-col :span="9">
                <a-form-item-rest>
                  <!-- 分配比例输入框 -->
                  <a-input-number
                    v-model:value="currentMember.allocationRatio"
                    :min="0.1"
                    :max="100"
                    :step="0.1"
                    :precision="1"
                    placeholder="分配比例"
                    style="width: 100%;"
                    addon-after="%"
                  />
                </a-form-item-rest>
              </a-col>
              <a-col :span="6">
                <a-form-item-rest>
                  <a-button type="primary" @click="handleAddMember">添加参与人</a-button>
                </a-form-item-rest>
              </a-col>
            </a-row>

            <!-- 参与者列表 -->
            <div class="member-list" style="margin-top: 16px;">
              <a-alert v-if="formState.participants.length > 0 && !validateTotalAllocation()" message="警告：所有参与者的分配比例总和应为1（100%）" type="warning" show-icon style="margin-bottom: 8px;" />

              <div v-if="formState.participants.length > 0" class="members-container">
                <a-divider style="margin: 8px 0">已添加参与人员</a-divider>
                <p style="color: #666; font-size: 12px; margin-bottom: 8px;">已添加 {{ formState.participants.length }} 位参与人员</p>
                <a-list
                  :data-source="formState.participants"
                  size="small"
                  bordered
                >
                  <template #renderItem="{ item, index }">
                    <a-list-item>
                      <a-row style="width: 100%">
                        <a-col :span="5">
                          {{ item.displayName }}
                        </a-col>
                        <a-col :span="4">
                          <a-form-item-rest>
                            <a-switch
                              :checked="item.isLeader"
                              @change="(checked) => toggleLeader(index, checked)"
                              checkedChildren="负责人"
                              unCheckedChildren="参与者"
                            />
                          </a-form-item-rest>
                        </a-col>
                        <a-col :span="5">
                          <a-form-item-rest>
                            <a-input-number
                              v-model:value="item.allocationRatio"
                              :min="0.1"
                              :max="100"
                              :step="0.1"
                              :precision="1"
                              style="width: 90%"
                              addon-after="%"
                              @change="validateTotalAllocation"
                            />
                          </a-form-item-rest>
                        </a-col>
                        <a-col :span="5" style="text-align: right">
                          <a-button type="link" danger @click="() => handleRemoveMember(index)">删除</a-button>
                        </a-col>
                      </a-row>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
              <div v-else style="color: #999; text-align: center; padding: 10px; border: 1px dashed #ddd; border-radius: 4px;">
                还没有添加参与人员，请先选择参与人员并点击"添加"按钮
              </div>
            </div>
          </div>
        </a-form-item>

        <!-- 备注 -->
        <a-form-item
          name="remark"
          label="备注"
        >
          <a-textarea
            v-model:value="formState.remark"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情模态框 -->
    <a-modal
      v-model:visible="viewModalVisible"
      title="教学工作量详情"
      width="800px"
      :footer="null"
      :maskClosable="false"
    >
      <div v-if="viewRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="学期">{{ viewRecord.semester }}</a-descriptions-item>
          <a-descriptions-item label="课程名称">{{ viewRecord.courseName }}</a-descriptions-item>
          <a-descriptions-item label="学生类别">{{ getStudentLevelText(viewRecord.studentLevel) }}</a-descriptions-item>
          <a-descriptions-item label="课程类型">{{ getCourseTypeText(viewRecord.courseType) }}</a-descriptions-item>
          <a-descriptions-item label="课程性质">{{ viewRecord.courseNature === 'required' ? '必修课' : (viewRecord.courseNature === 'elective' ? '选修课' : viewRecord.courseNature) }}</a-descriptions-item>
          <a-descriptions-item label="是否新开课">{{ viewRecord.isNewCourse ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item label="教师授课时数">{{ viewRecord.teachingHours }}学时</a-descriptions-item>
          <a-descriptions-item label="授课人数">{{ viewRecord.studentCount }}人</a-descriptions-item>
          <a-descriptions-item label="系/教研室">{{ viewRecord.department || '未填写' }}</a-descriptions-item>
          <a-descriptions-item label="工作量类别">{{ viewRecord.category?.categoryName || '未选择' }}</a-descriptions-item>
          <a-descriptions-item label="得分">
            <span style="font-weight: bold; color: #1890ff;">{{ viewRecord.score ? parseFloat(viewRecord.score).toFixed(2) : '0.00' }}分</span>
          </a-descriptions-item>
          <a-descriptions-item label="审核状态">
            <a-tag :color="viewRecord.ifReviewer === true || viewRecord.ifReviewer === 1 ? 'success' : (viewRecord.ifReviewer === false || viewRecord.ifReviewer === 0 ? 'error' : 'warning')">
              {{ viewRecord.ifReviewer === true || viewRecord.ifReviewer === 1 ? '已审核' : (viewRecord.ifReviewer === false || viewRecord.ifReviewer === 0 ? '已拒绝' : '待审核') }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="参与人员" :span="2">
            <div v-if="viewRecord.participantsList && viewRecord.participantsList.length > 0">
              <div v-for="(participant, index) in viewRecord.participantsList" :key="index" style="margin-bottom: 8px;">
                <a-tag :color="participant.isLeader ? 'blue' : 'default'">
                  {{ participant.isLeader ? '负责人' : '参与者' }}
                </a-tag>
                {{ participant.name }} ({{ participant.studentNumber }}) - {{ participant.allocationRatio }}%
              </div>
            </div>
            <span v-else style="color: #999;">无参与人员</span>
          </a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">{{ viewRecord.remark || '无' }}</a-descriptions-item>
          <a-descriptions-item label="审核意见" :span="2" v-if="viewRecord.reviewComment">{{ viewRecord.reviewComment }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 审核模态框 -->
    <a-modal
      v-model:visible="reviewModalVisible"
      title="审核教学工作量"
      width="600px"
      :maskClosable="false"
      @ok="handleReviewSubmit"
      :confirmLoading="reviewLoading"
      @cancel="handleReviewModalCancel"
    >
      <a-form
        ref="reviewFormRef"
        :model="reviewForm"
        :rules="reviewFormRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="审核结果" name="ifReviewer">
          <a-radio-group v-model:value="reviewForm.ifReviewer">
            <a-radio :value="1">通过</a-radio>
            <a-radio :value="0">拒绝</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="审核意见" name="reviewComment">
          <a-textarea
            v-model:value="reviewForm.reviewComment"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 用户工作量详情模态框 -->
    <a-modal
      v-model:visible="userDetailsVisible"
      title="教师工作量详情"
      width="1300px"
      :footer="null"
      :maskClosable="false"
    >
      <div v-if="userWorkloadDetails.length > 0">
        <a-table
          :columns="userDetailsColumns"
          :data-source="userWorkloadDetails"
          :pagination="userDetailsPagination"
          :loading="userDetailsLoading"
          rowKey="id"
          @change="handleUserDetailsPaginationChange"
          :bordered="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'score'">
              <span style="font-weight: bold; color: #1890ff;">{{ record.score ? parseFloat(record.score).toFixed(2) : '0.00' }}分</span>
            </template>
            <template v-else-if="column.key === 'allocationRatio'">
              <span style="font-weight: bold; color: #1890ff;">{{ parseFloat(record.userAllocationRatio).toFixed(1) }}%</span>
            </template>
            <template v-else-if="column.key === 'totalScore'">
              <span style="font-weight: bold; color: #1890ff;">{{ record.totalScore ? parseFloat(record.totalScore).toFixed(2) : '0.00' }}分</span>
            </template>
            <template v-else-if="column.key === 'userScore'">
              <span style="font-weight: bold; color: #1890ff;">{{ record.userScore ? parseFloat(record.userScore).toFixed(2) : '0.00' }}分</span>
            </template>
            <template v-else-if="column.key === 'ifReviewer'">
              <a-tag :color="record.ifReviewer === true || record.ifReviewer === 1 ? 'success' : (record.ifReviewer === false || record.ifReviewer === 0 ? 'error' : 'warning')">
                {{ record.ifReviewer === true || record.ifReviewer === 1 ? '已审核' : (record.ifReviewer === false || record.ifReviewer === 0 ? '已拒绝' : '待审核') }}
              </a-tag>
            </template>
          </template>
        </a-table>
        
        <div style="margin-top: 16px; text-align: right; font-weight: bold;">
          <span>总分: {{ userDetailsTotalScore ? parseFloat(userDetailsTotalScore).toFixed(2) : '0.00' }} 分</span>
        </div>
      </div>
      <div v-else style="color: #999; text-align: center; padding: 10px; border: 1px dashed #ddd; border-radius: 4px;">
        还没有用户工作量详情，请先选择用户并点击"查看详情"按钮
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import * as echarts from 'echarts'
import debounce from 'lodash/debounce'
import useUserId from '@/composables/useUserId'
const { userId, loading: loadingUserId, error: userIdError, getUserId } = useUserId()
import {
  getTeachingWorkloadsList,
  getTeachingWorkloadDetail,
  createTeachingWorkload,
  updateTeachingWorkload,
  deleteTeachingWorkload,
  reviewTeachingWorkload,
  reapplyTeachingWorkload,
  getReviewStatusStats,
  getCourseTypeStats,
  getSemesterStats,
  getScoreStats,
  getTeacherWorkloadRanking,
  getUserWorkloadDetails
} from '@/api/modules/api.teachingWorkloads'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  UserOutlined,
  DeleteOutlined,
  EyeOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  EditOutlined,
  DownOutlined,
  CheckCircleOutlined,
  RedoOutlined
} from '@ant-design/icons-vue'
import { getWorkloadLevels } from '@/api/modules/api.teachingWorkloads'
import { usersSearch, usersFindOne } from '@/api/modules/api.users'
import { uploadFiles, deleteFile } from '@/api/modules/api.file'
import * as XLSX from 'xlsx';
import { useUserRole } from '../../../composables/useUserRole';
const { getUserRole, getRoleFromStorage } = useUserRole()
import { hasPerms } from '@/libs/util.common';
import { getScoreTimeRange } from '@/api/modules/api.home';

// 当前用户信息
const currentUserId = ref(null)
const currentRole = ref(null)

// 个人/全部工作量切换
const showPersonalWorkloads = ref(false)

// 图表相关
const reviewStatusChartRef = ref(null)
const courseTypeChartRef = ref(null)
const semesterChartRef = ref(null)
const scoreChartRef = ref(null)

let reviewStatusChart = null
let courseTypeChart = null
let semesterChart = null
let scoreChart = null

// 图表范围选择
const reviewStatusChartRange = ref('in')
const courseTypeChartRange = ref('in')
const semesterChartRange = ref('in')
const scoreChartRange = ref('in')

// 图表审核状态过滤
const reviewStatusChartReviewStatus = ref('reviewed')
const courseTypeChartReviewStatus = ref('reviewed')
const semesterChartReviewStatus = ref('reviewed')
const scoreChartReviewStatus = ref('reviewed')

// 添加时间范围文本
const timeRangeText = ref('');

// 添加错误状态
const errorMessage = ref('')

// 搜索参数
const searchParams = reactive({
  courseName: '',
  semester: '',
  courseType: '',
  studentLevel: '',
  teacher: '',
  range: 'in',
  reviewStatus: 'all'
});

// 查询参数对象
const queryParams = reactive({
  courseName: '',
  semester: '',
  courseType: '',
  studentLevel: '',
  teacher: '',
  range: 'all',
  reviewStatus: 'all'
});

// 用户得分统计相关
const userScoreData = ref([])
const userScoreLoading = ref(false)
const userScoreSearchParams = reactive({
  courseName: '',
  nickname: ''
})
const userScoreChartRange = ref('in')
const userScorePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: false,
  showTotal: (total) => `共 ${total} 条记录`
})

// 用户得分统计表格列
const userScoreColumns = [
  {
    title: '排名',
    dataIndex: 'rank',
    key: 'rank',
    align: 'center'
  },
  {
    title: '用户名',
    dataIndex: 'nickname',
    key: 'nickname'
  },
  {
    title: '工作量数量',
    dataIndex: 'workloadCount',
    key: 'workloadCount',
    align: 'center'
  },
  {
    title: '总得分',
    key: 'totalScore',
    align: 'center'
  },
  {
    title: '操作',
    key: 'details',
    align: 'center'
  }
]

// 表格相关
const dataSource = ref([])
const isLoading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: false,
  showTotal: (total) => `共 ${total} 条记录`
})

// 表格列定义
const columns = [
  {
    title: '课程名称',
    dataIndex: 'courseName',
    key: 'courseName',
    width: 200,
    ellipsis: false
  },
  {
    title: '学期',
    dataIndex: 'semester',
    key: 'semester',
    width: 100
  },
  {
    title: '课程类型',
    dataIndex: 'courseType',
    key: 'courseType',
    width: 100
  },
  {
    title: '学生类别',
    dataIndex: 'studentLevel',
    key: 'studentLevel',
    width: 100
  },
  {
    title: '课时数',
    dataIndex: 'teachingHours',
    key: 'teachingHours',
    width: 80,
    align: 'center'
  },
  {
    title: '学生人数',
    dataIndex: 'studentCount',
    key: 'studentCount',
    width: 80,
    align: 'center'
  },
  {
    title: '参与者',
    key: 'participants',
    width: 200,
    ellipsis: false
  },
  {
    title: '得分',
    key: 'score',
    width: 80,
    align: 'center'
  },
  {
    title: '审核状态',
    key: 'ifReviewer',
    width: 100,
    align: 'center',
    customRender: ({ record }) => {
      let color = 'warning', text = '待审核';
      if (record.ifReviewer === 1 || record.ifReviewer === true) {
        color = 'success'; text = '已审核';
      } else if (record.ifReviewer === 0 || record.ifReviewer === false) {
        color = 'error'; text = '已拒绝';
      }
      return h('a-tag', { color }, text);
    }
  },
  {
    title: '审核建议',
    dataIndex: 'reviewComment',
    key: 'reviewComment',
    width: 150,
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right',
    align: 'center'
  }
]

// 工作量表单状态
const modalVisible = ref(false);
const isEdit = ref(false);
const modalTitle = ref('添加教学工作量');
const editMode = ref(false);
const confirmLoading = ref(false);
const formRef = ref(null);
const formState = reactive({
  id: '',
  semester: '',
  courseName: '',
  studentLevel: '',
  courseType: '',
  courseNature: 'required',
  isNewCourse: false,
  teachingHours: null,
  studentCount: null,
  categoryId: '',
  department: '',
  teacherId: null,
  participants: [],
  remark: '',
  status: 1,
  score: 0,
  submitterId: null,

  // 文件相关字段
  attachmentUrl: [],
  fileIds: [],
  deletedFileIds: []
});

// 表单验证规则
const rules = {
  semester: [{ required: true, message: '请输入学期', trigger: 'blur' }],
  courseName: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
  studentLevel: [{ required: true, message: '请输入学生类别', trigger: 'blur' }],
  courseType: [{ required: true, message: '请输入课程类型', trigger: 'blur' }],
  teachingHours: [{ required: true, message: '请输入课时数', trigger: 'blur' }],
  studentCount: [{ required: true, message: '请输入学生人数', trigger: 'blur' }]
}

// 级别选项
const levelOptions = ref([])
const levelLoading = ref(false)

// 文件上传相关
const fileList = ref([])
const uploadLoading = ref(false)

// 参与者管理
const currentMember = reactive({
  userId: null,
  displayName: '',
  username: '',
  studentNumber: '',
  nickname: '',
  allocationRatio: 10,
  isLeader: false,
  participantRank: 1
})

// 用户搜索相关
const userSearchLoading = ref(false)
const userSearchResults = ref([])
const userSearchValue = ref('')
const teacherOptions = ref([])
const userSearchTimeout = ref(null)

// 查看详情相关
const viewModalVisible = ref(false)
const viewRecord = ref(null)

// 审核相关
const reviewModalVisible = ref(false)
const reviewFormRef = ref(null)
const reviewLoading = ref(false)
const reviewForm = reactive({
  id: '',
  ifReviewer: 1,
  reviewComment: ''
})

// 审核表单验证规则
const reviewFormRules = {
  ifReviewer: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ]
}

// 图表初始化状态
const chartsInitialized = ref(false)

// 导出相关
const exporting = ref(false)

// 用户工作量详情相关
const userDetailsVisible = ref(false)
const userDetailsLoading = ref(false)
const userWorkloadDetails = ref([])
const userDetailsTotalScore = ref(0)
const selectedUserDetailName = ref('')
const selectedUserId = ref(null)
const userDetailsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: total => `共 ${total} 条`
})

// 用户工作量详情表格列
const userDetailsColumns = [
  {
    title: '课程名称',
    dataIndex: 'courseName',
    key: 'courseName',
    ellipsis: false,
    width: 180
  },
  {
    title: '学期',
    dataIndex: 'semester',
    key: 'semester',
    width: 100
  },
  {
    title: '课程类型',
    dataIndex: 'courseType',
    key: 'courseType',
    width: 100,
    customRender: ({ text }) => getCourseTypeText(text)
  },
  {
    title: '学生类别',
    dataIndex: 'studentLevel',
    key: 'studentLevel',
    width: 100,
    customRender: ({ text }) => getStudentLevelText(text)
  },
  {
    title: '课时数',
    dataIndex: 'teachingHours',
    key: 'teachingHours',
    width: 80,
    align: 'center'
  },
  {
    title: '分配比例',
    dataIndex: 'userAllocationRatio',
    key: 'allocationRatio',
    width: 80,
    align: 'center',
    customRender: ({ text }) => `${parseFloat(text).toFixed(1)}%`
  },
  {
    title: '得分',
    dataIndex: 'userScore',
    key: 'userScore',
    width: 80,
    align: 'center',
    customRender: ({ text }) => parseFloat(text || 0).toFixed(2)
  }
]

// 总分计算
const totalScore = computed(() => {
  return dataSource.value.reduce((sum, item) => {
    if (isWorkloadInScoreRange(item)) {
      return sum + (parseFloat(item.score) || 0)
    }
    return sum
  }, 0)
})

// 获取数据
const fetchData = async () => {
  isLoading.value = true
  errorMessage.value = ''

  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...queryParams
    }
    // 审核状态筛选转换
    if (queryParams.reviewStatus && queryParams.reviewStatus !== 'all') {
      // 转换为后端需要的ifReviewer参数
      if (queryParams.reviewStatus === 'reviewed') params.ifReviewer = 1
      else if (queryParams.reviewStatus === 'rejected') params.ifReviewer = 0
      else if (queryParams.reviewStatus === 'pending') params.ifReviewer = null
    }
    // 如果是个人视图，添加用户ID过滤
    if (showPersonalWorkloads.value && currentUserId.value) {
      params.userId = currentUserId.value
    }
    const response = await getTeachingWorkloadsList(params)
    if (response && response.code === 200) {
      dataSource.value = response.data.list || []
      pagination.total = response.data.total || 0
      if (!chartsInitialized.value) {
        await updateCharts()
      }
    } else {
      message.error(response?.message || '获取数据失败')
      errorMessage.value = response?.message || '获取数据失败，请稍后重试'
    }
  } catch (error) {
    console.error('获取教学工作量数据失败:', error)
    message.error('获取教学工作量数据失败：' + (error.message || '未知错误'))
    errorMessage.value = '获取教学工作量数据失败：' + (error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 获取级别选项
const fetchLevelOptions = async () => {
  levelLoading.value = true
  try {
    const response = await getWorkloadLevels()
    if (response && response.code === 200) {
      levelOptions.value = response.data || []
    }
  } catch (error) {
    console.error('获取级别选项失败:', error)
  } finally {
    levelLoading.value = false
  }
}

// 获取用户得分统计
const fetchAllUsersTotalScore = async () => {
  userScoreLoading.value = true
  try {
    const params = {
      page: userScorePagination.current,
      pageSize: userScorePagination.pageSize,
      range: userScoreChartRange.value,
      reviewStatus: 'reviewed', // 只显示已审核的数据
      ...userScoreSearchParams
    }

    const response = await getTeacherWorkloadRanking(params)

    if (response && response.code === 200) {
      // 转换数据格式以匹配表格列
      userScoreData.value = response.data.list.map(item => ({
        key: item.userId,
        rank: item.rank,
        nickname: item.userName,
        workloadCount: item.totalWorkloads,
        totalScore: item.totalScore,
        userId: item.userId
      }))
      userScorePagination.total = response.data.pagination.total
    } else {
      message.error(response?.message || '获取用户得分统计失败')
      userScoreData.value = []
      userScorePagination.total = 0
    }
  } catch (error) {
    console.error('获取用户得分统计失败:', error)
    message.error('获取用户得分统计失败')
    userScoreData.value = []
    userScorePagination.total = 0
  } finally {
    userScoreLoading.value = false
  }
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchData()
}

// 用户得分表格变化处理
const handleUserScoreTableChange = (pag, filters, sorter) => {
  userScorePagination.current = pag.current
  userScorePagination.pageSize = pag.pageSize
  fetchAllUsersTotalScore()
}

// 搜索处理
const handleSearch = () => {
  // 将搜索参数复制到查询参数
  Object.assign(queryParams, searchParams)
  pagination.current = 1
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    courseName: '',
    semester: '',
    courseType: '',
    studentLevel: '',
    teacher: '',
    range: 'in',
    reviewStatus: 'all'
  })
  handleSearch()
}

// 显示添加模态框
const showAddModal = () => {
  modalTitle.value = '添加教学工作量'
  isEdit.value = false
  editMode.value = false
  resetForm()
  modalVisible.value = true
}

// 编辑
const handleEdit = async (record) => {
  try {
    // 设置编辑模式
    isEdit.value = true
    modalTitle.value = '编辑教学工作量'
    editMode.value = true

    // 显示loading状态
    isLoading.value = true

    // 通过API获取工作量详情
    console.log('正在获取教学工作量详情:', record.id)
    const response = await getTeachingWorkloadDetail({ id: record.id })

    if (!response || response.code !== 200 || !response.data) {
      message.error('获取工作量详情失败')
      isLoading.value = false
      return
    }

    const data = response.data
    console.log('获取工作量详情成功:', data)

    // 处理参与者数据，将后端返回的 participantsList 转换为前端需要的格式
    let participants = []
    if (data.participantsList && data.participantsList.length > 0) {
      participants = data.participantsList.map(p => ({
        userId: p.id,
        displayName: `${p.name} (${p.studentNumber})`,
        username: p.username || '',
        studentNumber: p.studentNumber || '',
        nickname: p.name || '',
        allocationRatio: p.allocationRatio || 0,
        isLeader: p.isLeader || false
      }))
    }

    Object.assign(formState, {
      id: data.id,
      semester: data.semester,
      courseName: data.courseName,
      studentLevel: data.studentLevel,
      courseType: data.courseType,
      courseNature: data.courseNature,
      isNewCourse: data.isNewCourse || false,
      teachingHours: data.teachingHours,
      studentCount: data.studentCount,
      categoryId: data.categoryId,
      department: data.department,
      teacherId: data.teacherId,
      participants: participants,
      remark: data.remark,
      status: data.status,
      score: data.score,
      attachmentUrl: data.attachmentUrl || [],
      fileIds: data.fileIds || [],
      deletedFileIds: []
    })

    // 显示表单模态框
    modalVisible.value = true

  } catch (error) {
    console.error('获取工作量详情失败:', error)
    message.error('获取工作量详情失败')
  } finally {
    isLoading.value = false
  }
}



// 查看详情
const handleDetail = async (record) => {
  try {
    const response = await getTeachingWorkloadDetail({ id: record.id })
    if (response && response.code === 200) {
      viewRecord.value = response.data
      viewModalVisible.value = true
    }
  } catch (error) {
    console.error('获取工作量详情失败:', error)
    message.error('获取工作量详情失败')
  }
}

// 审核
const handleReview = async (record) => {
  console.log('准备审核工作量:', record.id, record.courseName)
  
  try {
    // 获取工作量详情以检查是否有必要的字段
    console.log('正在获取工作量详情...')
    const response = await getTeachingWorkloadDetail({ id: record.id })
    
    if (response && response.code === 200) {
      const detailData = response.data
      console.log('工作量详情:', JSON.stringify(detailData, null, 2))
      
      // 检查关键字段
      console.log('工作量级别ID:', detailData.categoryId)
      console.log('课程类型:', detailData.courseType)
      console.log('学生层次:', detailData.studentLevel)
      
      // 检查是否有工作量级别ID
      if (!detailData.categoryId) {
        message.warning('此工作量缺少级别信息，请先编辑并设置级别信息后再进行审核')
        return
      }
      
      // 继续审核流程
      reviewForm.id = record.id
      reviewForm.ifReviewer = 1
      reviewForm.reviewComment = ''
      reviewModalVisible.value = true
    } else {
      console.error('获取工作量详情失败:', response)
      message.error(response?.message || '获取工作量详情失败')
    }
  } catch (error) {
    console.error('准备审核失败:', error)
    message.error('准备审核失败: ' + (error.message || '未知错误'))
  }
}

// 处理删除
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条教学工作量记录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const response = await deleteTeachingWorkload(record.id)
        if (response && response.code === 200) {
          message.success('删除成功')
          fetchData()
        } else {
          message.error(response?.message || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败：' + (error.message || '未知错误'))
      }
    }
  })
}

// 重新提交审核
const handleResubmit = (record) => {
  Modal.confirm({
    title: '确认重新提交审核',
    content: `确定要重新提交"${record.courseName}"的审核申请吗？`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        console.log('重新提交审核，工作量ID:', record.id)

        // 调用重新提交审核的API
        const response = await reapplyTeachingWorkload(record.id)
        console.log('重新提交审核API响应:', response)

        if (response && response.code === 200) {
          message.success('重新提交审核成功')
          fetchData() // 刷新数据
        } else {
          message.error(response?.message || '重新提交审核失败')
        }
      } catch (error) {
        console.error('重新提交审核失败:', error)
        message.error('重新提交审核失败: ' + (error.message || '未知错误'))
      }
    }
  })
}



// 清空表单数据
const resetForm = () => {
  Object.assign(formState, {
    id: null,
    semester: '',
    courseName: '',
    studentLevel: '',
    courseType: '',
    courseNature: 'required',
    isNewCourse: false,
    teachingHours: null,
    studentCount: null,
    categoryId: '',  // 修改为categoryId
    calculatedWorkload: null,
    teacherId: null,
    participants: [],
    remark: '',
    status: 1,
    score: 0,
    submitterId: null,
    attachmentUrl: [],
    fileIds: [],
    deletedFileIds: []
  })

  Object.assign(currentMember, {
    userId: null,
    displayName: '',
    username: '',
    studentNumber: '',
    nickname: '',
    allocationRatio: 10,
    isLeader: false,
    participantRank: 1
  })

  fileList.value = []
}

// 判断工作量是否在计分范围内
const isWorkloadInScoreRange = (record) => {
  // 这里需要根据实际业务逻辑判断
  return record.range === 'in' || record.inScoreRange === true
}

// 格式化参与者信息
const formatParticipantsWithAllocation = (participants, record) => {
  // 首先检查participantsList是否存在，这是后端返回的参与者列表
  if (record.participantsList && record.participantsList.length > 0) {
    return record.participantsList.map(p => {
      const leaderMark = p.isLeader ? '(负责人)' : '';
      const ratio = p.allocationRatio ? `${p.allocationRatio}%` : '';
      return `${p.name || '未知'} (${p.studentNumber || '无工号'})${leaderMark} ${ratio}`;
    }).join('\n');
  }
  
  // 如果没有participantsList，则尝试使用participants
  if (participants && participants.length > 0) {
    return participants.map(p => {
      const leaderMark = p.isLeader ? '(负责人)' : '';
      const ratio = p.allocationRatio ? `${p.allocationRatio}%` : '';
      return `${p.nickname || p.displayName || '未知'} (${p.studentNumber || '无工号'})${leaderMark} ${ratio}`;
    }).join('\n');
  }
  
  // 如果都没有，则显示教师名称或无参与者
  return record.teacher?.nickName || record.teacherName || '无参与者';
}



// 切换个人/全部工作量视图
const togglePersonalWorkloads = async () => {
  showPersonalWorkloads.value = !showPersonalWorkloads.value
  console.log(`切换到${showPersonalWorkloads.value ? '个人' : '全部'}工作量视图`)

  // 销毁现有图表
  if (reviewStatusChart) {
    reviewStatusChart.dispose()
    reviewStatusChart = null
  }
  if (courseTypeChart) {
    courseTypeChart.dispose()
    courseTypeChart = null
  }
  if (semesterChart) {
    semesterChart.dispose()
    semesterChart = null
  }
  if (scoreChart) {
    scoreChart.dispose()
    scoreChart = null
  }

  chartsInitialized.value = false

  try {
    await fetchData()

    setTimeout(() => {
      initReviewStatusChart(reviewStatusChartRange.value, reviewStatusChartReviewStatus.value)
      initCourseTypeChart(courseTypeChartRange.value, courseTypeChartReviewStatus.value)
      initSemesterChart(semesterChartRange.value, semesterChartReviewStatus.value)
      initScoreChart(scoreChartRange.value, scoreChartReviewStatus.value)
    }, 100)

    if (showPersonalWorkloads.value) {
      await fetchAllUsersTotalScore()
    }
  } catch (error) {
    console.error('切换视图失败:', error)
    message.error('切换视图失败，请稍后重试')
  }
}

// 用户得分范围变化处理
const handleUserScoreRangeChange = (value) => {
  userScoreChartRange.value = value
  fetchAllUsersTotalScore()
}

// 图表范围变化处理
const changeChartRange = (chartType, value) => {
  switch (chartType) {
    case 'reviewStatus':
      reviewStatusChartRange.value = value
      initReviewStatusChart(value, reviewStatusChartReviewStatus.value)
      break
    case 'courseType':
      courseTypeChartRange.value = value
      initCourseTypeChart(value, courseTypeChartReviewStatus.value)
      break
    case 'semester':
      semesterChartRange.value = value
      initSemesterChart(value, semesterChartReviewStatus.value)
      break
    case 'score':
      scoreChartRange.value = value
      initScoreChart(value, scoreChartReviewStatus.value)
      break
  }
}

// 初始化审核状态图表
const initReviewStatusChart = async (range = 'in', reviewStatus = 'reviewed') => {
  if (!reviewStatusChartRef.value) return

  try {
    if (reviewStatusChart) {
      reviewStatusChart.dispose()
    }

    reviewStatusChart = echarts.init(reviewStatusChartRef.value)

    // 从API获取真实数据
    let data = []
    try {
      const response = await getReviewStatusStats({ range, reviewStatus })
      if (response && response.data) {
        data = response.data.map(item => ({
          name: item.status === 1 ? '已审核' : (item.status === 0 ? '已拒绝' : '待审核'),
          value: item.count
        }))
      }
    } catch (error) {
      console.error('获取审核状态统计数据失败:', error)
      // 使用默认数据
      data = [
        { name: '已审核', value: 0 },
        { name: '待审核', value: 0 },
        { name: '已拒绝', value: 0 }
      ]
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '审核状态',
          type: 'pie',
          radius: '50%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    reviewStatusChart.setOption(option)
  } catch (error) {
    console.error('初始化审核状态图表失败:', error)
  }
}

// 初始化课程类型图表
const initCourseTypeChart = async (range = 'in', reviewStatus = 'reviewed') => {
  if (!courseTypeChartRef.value) return

  try {
    if (courseTypeChart) {
      courseTypeChart.dispose()
    }

    courseTypeChart = echarts.init(courseTypeChartRef.value)

    // 从API获取真实数据
    let data = []
    try {
      const response = await getCourseTypeStats({ range, reviewStatus })
      if (response && response.data) {
        data = response.data.map(item => ({
          name: item.type || item.name,
          value: item.count
        }))
      }
    } catch (error) {
      console.error('获取课程类型统计数据失败:', error)
      // 使用默认数据
      data = [
        { name: '理论课', value: 0 },
        { name: '实验课', value: 0 },
        { name: '实践课', value: 0 },
        { name: '其他', value: 0 }
      ]
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '课程类型',
          type: 'pie',
          radius: '50%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    courseTypeChart.setOption(option)
  } catch (error) {
    console.error('初始化课程类型图表失败:', error)
  }
}

// 初始化学期图表
const initSemesterChart = async (range = 'in', reviewStatus = 'reviewed') => {
  if (!semesterChartRef.value) return

  try {
    if (semesterChart) {
      semesterChart.dispose()
    }

    semesterChart = echarts.init(semesterChartRef.value)

    // 从API获取真实数据
    let data = []
    try {
      const response = await getSemesterStats({ range, reviewStatus })
      if (response && response.data) {
        data = response.data.map(item => ({
          name: item.semester || item.name,
          value: item.count
        }))
      }
    } catch (error) {
      console.error('获取学期统计数据失败:', error)
      // 使用默认数据
      data = [
        { name: '2023-2024-1', value: 0 },
        { name: '2023-2024-2', value: 0 },
        { name: '2024-2025-1', value: 0 }
      ]
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '学期分布',
          type: 'pie',
          radius: '50%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    semesterChart.setOption(option)
  } catch (error) {
    console.error('初始化学期图表失败:', error)
  }
}

// 初始化得分图表
const initScoreChart = async (range = 'in', reviewStatus = 'reviewed') => {
  if (!scoreChartRef.value) return

  try {
    if (scoreChart) {
      scoreChart.dispose()
    }

    scoreChart = echarts.init(scoreChartRef.value)

    // 从API获取真实数据
    let data = []
    try {
      const response = await getScoreStats({ range, reviewStatus })
      if (response && response.data) {
        data = response.data.map(item => ({
          name: item.range || item.name,
          value: item.count
        }))
      }
    } catch (error) {
      console.error('获取得分统计数据失败:', error)
      // 使用默认数据
      data = [
        { name: '0-20分', value: 0 },
        { name: '20-40分', value: 0 },
        { name: '40-60分', value: 0 },
        { name: '60-80分', value: 0 },
        { name: '80分以上', value: 0 }
      ]
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '得分分布',
          type: 'pie',
          radius: '50%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    scoreChart.setOption(option)
  } catch (error) {
    console.error('初始化得分图表失败:', error)
  }
}

// 更新所有图表
const updateCharts = async () => {
  try {
    if (!chartsInitialized.value) {
      chartsInitialized.value = true

      await Promise.all([
        initReviewStatusChart(reviewStatusChartRange.value, reviewStatusChartReviewStatus.value),
        initCourseTypeChart(courseTypeChartRange.value, courseTypeChartReviewStatus.value),
        initSemesterChart(semesterChartRange.value, semesterChartReviewStatus.value),
        initScoreChart(scoreChartRange.value, scoreChartReviewStatus.value)
      ])
    }
  } catch (error) {
    console.error('更新图表出错:', error)
    message.error('加载图表数据失败')
    chartsInitialized.value = false
  }
}

// Excel导入相关
const handleExcelToJsonConvert = async ({ file }) => {
  try {
    const formData = new FormData()
    formData.append('file', file)

    // 这里需要实现Excel导入功能
    message.success('Excel导入功能开发中')
  } catch (error) {
    console.error('Excel导入失败:', error)
    message.error('Excel导入失败：' + (error.message || '未知错误'))
  }
}

const beforeExcelUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    message.error('只能上传Excel文件!')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  return false // 阻止自动上传
}

// 显示用户工作量详情
const showUserWorkloadDetails = async (record) => {
  try {
    userDetailsLoading.value = true;
    selectedUserId.value = record.userId;
    selectedUserDetailName.value = record.nickname || '未知用户';
    
    // 重置分页
    userDetailsPagination.current = 1;
    
    const response = await getUserWorkloadDetails({
      userId: record.userId,
      range: userScoreChartRange.value,
      reviewStatus: userScoreSearchParams.reviewStatus,
      page: userDetailsPagination.current,
      pageSize: userDetailsPagination.pageSize
    });
    
    if (response && response.code === 200) {
      // 处理返回的数据
      userWorkloadDetails.value = response.data.list.map(item => ({
        ...item,
        userAllocationRatio: parseFloat(item.userAllocationRatio || 0).toFixed(1),
        userScore: parseFloat(item.userScore || 0).toFixed(2),
        totalScore: parseFloat(item.totalScore || 0).toFixed(2)
      }));
      
      userDetailsPagination.total = response.data.pagination?.total || 0;
      userDetailsTotalScore.value = parseFloat(response.data.totalScore || 0).toFixed(2);
      
      // 显示模态框
      userDetailsVisible.value = true;
    } else {
      message.error(response?.message || '获取用户工作量详情失败');
    }
  } catch (error) {
    console.error('获取用户工作量详情失败:', error);
    message.error('获取用户工作量详情失败: ' + (error.message || '未知错误'));
  } finally {
    userDetailsLoading.value = false;
  }
};

// 用户工作量详情分页变化处理
const handleUserDetailsPaginationChange = async (pagination) => {
  userDetailsLoading.value = true;
  userDetailsPagination.current = pagination.current;
  userDetailsPagination.pageSize = pagination.pageSize;
  
  try {
    const response = await getUserWorkloadDetails({
      userId: selectedUserId.value,
      range: userScoreChartRange.value,
      reviewStatus: userScoreSearchParams.reviewStatus,
      page: userDetailsPagination.current,
      pageSize: userDetailsPagination.pageSize
    });
    
    if (response && response.code === 200) {
      userWorkloadDetails.value = response.data.list.map(item => ({
        ...item,
        userAllocationRatio: parseFloat(item.userAllocationRatio || 0).toFixed(1),
        userScore: parseFloat(item.userScore || 0).toFixed(2),
        totalScore: parseFloat(item.totalScore || 0).toFixed(2)
      }));
      userDetailsPagination.total = response.data.pagination?.total || 0;
      userDetailsTotalScore.value = parseFloat(response.data.totalScore || 0).toFixed(2);
    } else {
      message.error(response?.message || '获取用户工作量详情失败');
    }
  } catch (error) {
    console.error('获取用户工作量详情分页数据失败:', error);
    message.error('获取用户工作量详情分页数据失败');
  } finally {
    userDetailsLoading.value = false;
  }
};

// ==================== 参与者管理相关函数 ====================

// 处理教师搜索
const handleTeacherSearch = (value) => {
  // 清除之前的延时搜索
  if (userSearchTimeout.value) {
    clearTimeout(userSearchTimeout.value);
  }

  // 如果搜索词为空，则清空选项列表并返回
  if (!value || value.trim() === '') {
    teacherOptions.value = [];
    return;
  }

  // 设置搜索中状态
  userSearchLoading.value = true;

  // 延时搜索，避免频繁请求
  userSearchTimeout.value = setTimeout(() => {
    // 调用用户搜索API
    usersSearch({ keyword: value.trim() })
      .then((res) => {
        // 处理API返回结果
        if (res && res.code === 200) {
          // 将返回的用户数据转换为下拉选项格式
          if (Array.isArray(res.data)) {
            teacherOptions.value = res.data.map(item => ({
              value: item.id, // 用户ID作为选项值
              label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`, // 显示名称和用户名
              ...item // 保留原始用户数据，方便后续使用
            }));
          } else if (res.data && Array.isArray(res.data.list)) {
            // 如果返回的是包含list属性的对象
            teacherOptions.value = res.data.list.map(item => ({
              value: item.id,
              label: `${item.nickname || item.username || '未知'} (${item.studentNumber || '无工号'})`,
              ...item
            }));
          } else {
            teacherOptions.value = [];
            console.warn('用户搜索返回数据格式不正确:', res);
          }
        } else {
          teacherOptions.value = [];
          console.warn('用户搜索返回数据格式不正确:', res);
        }
      })
      .catch((error) => {
        console.error('搜索用户失败:', error);
        message.error('搜索用户失败');
        teacherOptions.value = [];
      })
      .finally(() => {
        userSearchLoading.value = false;
      });
  }, 500); // 500ms的防抖延迟
};

// 处理当前选中成员变更
const handleCurrentMemberChange = (value) => {
  // 查找选中的用户信息
  const selectedOption = teacherOptions.value.find(item => item.value === value);

  if (selectedOption) {
    // 更新当前选中成员的信息
    currentMember.userId = selectedOption.value;
    currentMember.displayName = selectedOption.label;
    currentMember.username = selectedOption.username || '';
    currentMember.studentNumber = selectedOption.studentNumber || '';
    currentMember.nickname = selectedOption.nickname || '';

    // 如果是第一个成员，默认分配比例为100%
    if (formState.participants.length === 0) {
      currentMember.allocationRatio = 100;
    } else {
      // 否则设置默认分配比例为10%
      currentMember.allocationRatio = 10;
    }
  } else {
    // 重置当前选中成员
    currentMember.userId = null;
    currentMember.displayName = '';
    currentMember.username = '';
    currentMember.studentNumber = '';
    currentMember.nickname = '';
    currentMember.allocationRatio = 10;
  }
};

// 添加成员
const handleAddMember = () => {
  // 验证当前成员信息是否完整
  if (!currentMember.userId) {
    message.warning('请先选择参与人');
    return;
  }

  if (!currentMember.allocationRatio || currentMember.allocationRatio <= 0 || currentMember.allocationRatio > 100) {
    message.warning('分配比例必须大于0且不超过100');
    return;
  }

  // 检查是否已经存在相同的成员
  const existingMember = formState.participants.find(
    (item) => item.userId === currentMember.userId
  );

  if (existingMember) {
    message.warning(`${currentMember.displayName}已在参与人列表中`);
    return;
  }

  // 计算当前总分配比例
  const currentTotalRatio = formState.participants.reduce(
    (sum, item) => sum + Number(item.allocationRatio),
    0
  );

  // 检查添加新成员后是否会超过100%
  if (currentTotalRatio + Number(currentMember.allocationRatio) > 100) {
    message.warning('所有成员的分配比例总和不能超过100%');
    return;
  }

  // 添加新成员到列表
  formState.participants.push({
    userId: currentMember.userId,
    displayName: currentMember.displayName,
    username: currentMember.username,
    studentNumber: currentMember.studentNumber,
    nickname: currentMember.nickname,
    allocationRatio: Number(currentMember.allocationRatio),
    isLeader: formState.participants.length === 0 // 第一个成员默认设为负责人
  });

  // 重置当前成员数据
  currentMember.userId = null;
  currentMember.displayName = '';
  currentMember.username = '';
  currentMember.studentNumber = '';
  currentMember.nickname = '';
  currentMember.allocationRatio = 10;

  // 检查分配比例总和
  validateTotalAllocation();

  message.success('参与人员添加成功');
};

// 设置/取消负责人
const toggleLeader = (index, isLeader) => {
  // 如果设置为负责人，先将其他所有人设为非负责人
  if (isLeader) {
    formState.participants.forEach((p, i) => {
      if (i !== index) {
        p.isLeader = false;
      }
    });
  }

  // 修改指定参与人的负责人状态
  formState.participants[index].isLeader = isLeader;
};

// 移除成员
const handleRemoveMember = (index) => {
  const member = formState.participants[index];

  // 如果要移除的是负责人，需要确保有其他成员可以设为负责人
  if (member.isLeader && formState.participants.length > 1) {
    // 设置另一个成员为负责人
    const newLeaderIndex = index === 0 ? 1 : 0;
    formState.participants[newLeaderIndex].isLeader = true;
  }

  // 移除成员
  formState.participants.splice(index, 1);

  // 检查分配比例总和
  if (!validateTotalAllocation() && formState.participants.length > 0) {
    message.warning('所有成员的分配比例总和应为100%');
  }

  message.success('参与人员移除成功');
};

// 校验分配比例总和
const validateTotalAllocation = () => {
  if (!formState.participants || formState.participants.length === 0) {
    return true; // 如果没有参与者，则视为有效
  }

  const total = formState.participants.reduce(
    (sum, item) => sum + Number(item.allocationRatio || 0),
    0
  );

  // 允许0.1%的误差
  return Math.abs(total - 100) < 0.1;
};

// 重置用户得分搜索
const resetUserScoreSearch = () => {
  userScoreSearchParams.courseName = ''
  userScoreSearchParams.nickname = ''
  userScoreChartRange.value = 'in'
  fetchAllUsersTotalScore()
}

// 导出用户得分数据
const exportUserScoreData = async () => {
  exporting.value = true
  try {
    // 这里需要实现导出功能
    message.success('导出功能开发中')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败：' + (error.message || '未知错误'))
  } finally {
    exporting.value = false
  }
}

// 导出当前工作量数据
const exportCurrentWorkloads = async () => {
  exporting.value = true
  try {
    // 这里需要实现导出功能
    message.success('导出功能开发中')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败：' + (error.message || '未知错误'))
  } finally {
    exporting.value = false
  }
}







// 获取排名颜色
const getRankColor = (rank) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  if (rank <= 10) return 'blue'
  return 'default'
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    isLoading.value = true

    if (isEdit.value) {
      await updateTeachingWorkload(formState)
      message.success('更新成功')
    } else {
      await createTeachingWorkload(formState)
      message.success('添加成功')
    }

    modalVisible.value = false
    fetchData()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败：' + (error.message || '未知错误'))
  } finally {
    isLoading.value = false
  }
}

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 处理审核提交
const handleReviewSubmit = async () => {
  try {
    reviewLoading.value = true
    reviewFormRef.value.validate().then(async () => {
      const { id, ifReviewer, reviewComment } = reviewForm
      await reviewTeachingWorkload({ id, ifReviewer, reviewComment })
      message.success('审核成功')
      handleReviewModalCancel()
      await fetchData()
    })
  } catch (error) {
    console.error(error)
    message.error('审核失败：' + (error.message || '未知错误'))
  } finally {
    reviewLoading.value = false
  }
}

// 处理审核模态框取消
const handleReviewModalCancel = () => {
  reviewModalVisible.value = false
  reviewForm.id = ''
  reviewForm.ifReviewer = 1
  reviewForm.reviewComment = ''
}





// 初始化搜索参数
const initSearchParams = () => {
  if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
    searchParams.range = 'in'
    searchParams.reviewStatus = 'all'
  }
}

// 组件加载时的处理
onMounted(async () => {
  try {
    // 获取当前用户角色
    currentRole.value = await getUserRole()
    console.log("当前用户角色:", currentRole.value)

    // 获取当前用户ID
    try {
      currentUserId.value = await getUserId(true)
      console.log("当前用户ID:", currentUserId.value)
    } catch (error) {
      console.error("获取用户ID失败:", error)
    }

    // 如果是教师角色，默认显示个人工作量
    if (currentRole.value && currentRole.value.roleAuth === 'TEACHER-LV1') {
      showPersonalWorkloads.value = true
      console.log('用户为教师角色，默认显示个人工作量')
    }

    // 初始化搜索参数
    initSearchParams()

    // 获取级别选项
    try {
      await fetchLevelOptions()
    } catch (error) {
      console.error('获取级别选项失败:', error)
      message.error('获取级别选项失败')
      levelOptions.value = []
    }

    // 获取工作量数据
    try {
      await fetchData()
    } catch (error) {
      console.error('获取工作量数据失败:', error)
      message.error('获取工作量数据失败')
    }

    // 获取用户得分统计数据
    try {
      await fetchAllUsersTotalScore()
    } catch (error) {
      console.error('获取用户得分统计数据失败:', error)
      message.error('获取用户得分统计数据失败')
    }

    // 监听窗口大小变化，以调整图表尺寸
    window.addEventListener('resize', debounce(() => {
      try {
        reviewStatusChart && reviewStatusChart.resize()
        courseTypeChart && courseTypeChart.resize()
        semesterChart && semesterChart.resize()
        scoreChart && scoreChart.resize()
      } catch (error) {
        console.error('调整图表大小出错:', error)
      }
    }, 300))

    // 获取时间范围
    const getTimeRange = () => {
      // 调用API获取时间范围
      getScoreTimeRange('teachingWorkloads').then(res => {
        if (res.code === 200 && res.data) {
          timeRangeText.value = res.data.timeRange || '';
        } else {
          timeRangeText.value = '暂无时间范围数据';
        }
      }).catch(error => {
        console.error('获取时间范围失败:', error);
        timeRangeText.value = '获取时间范围失败';
      });
    };

    // 在onMounted中调用
    getTimeRange();
  } catch (error) {
    console.error('组件挂载时出错:', error)
    message.error('初始化界面失败')
  }
})

// 学生类别转换函数
const getStudentLevelText = (level) => {
  switch (level) {
    case 'undergraduate':
      return '本科生';
    case 'graduate':
      return '研究生';
    case 'doctoral':
      return '博士生';
    default:
      return level;
  }
}

// 课程类型转换函数
const getCourseTypeText = (type) => {
  switch (type) {
    case 'theory':
      return '理论课';
    case 'experiment':
      return '实验课';
    case 'practice':
      return '实践课';
    case 'other':
      return '其他';
    default:
      return type;
  }
}

</script>

<style lang="scss" scoped>
@import '@/styles/performance-common.scss';

// 页面特定样式
.teaching-workloads-container {
  margin: 24px;
  border: 1px solid #f0f0f0;
}

.text-danger {
  color: #ff4d4f;
}

.participants-container {
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;

  .ant-list {
    max-height: 200px;
    overflow-y: auto;
  }
}

.participant-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.filter-container {
  margin-bottom: 16px;
}

/* 导入预览表格中错误行的样式 */
:deep(.error-row) {
  background-color: #fff2f0;
}

.chart-wrapper {
  height: 300px;
  width: 100%;
}

.chart-container {
  .chart-wrapper {
    min-height: 300px;
  }
}

.search-actions-inline {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.table-footer {
  margin-top: 16px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;

  .total-score {
    text-align: right;
    font-size: 16px;
    font-weight: bold;
    color: #1890ff;
  }
}
</style>
