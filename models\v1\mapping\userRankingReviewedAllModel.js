const {DataTypes} = require('sequelize');
const sequelize = require('@config/dbConfig');

/**
 * 用户总体排名模型（已审核）
 * 表名使用下划线命名法: user_ranking_reviewed_all
 * 字段名使用小驼峰命名法
 */
const UserRankingReviewedAll = sequelize.define('user_ranking_reviewed_all',
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            notNull: true,
            comment: '主键ID'
        },
        updateTime: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            comment: '更新时间'
        },
        rank: {
            type: DataTypes.INTEGER,
            allowNull: true,
            comment: '排名'
        },
        userId: {
            type: DataTypes.STRING(50),
            allowNull: true,
            comment: '用户ID'
        },
        nickName: {
            type: DataTypes.STRING(255),
            allowNull: true,
            comment: '用户昵称'
        },
        studentNumber: {
            type: DataTypes.STRING(50),
            allowNull: true,
            comment: '学号'
        },
        totalItemCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '总项目数'
        },
        totalScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '总分数'
        },
        appointmentCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '学术任职数量'
        },
        appointmentScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '学术任职分数'
        },
        awardCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '奖项数量'
        },
        awardScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '奖项分数'
        },
        conferenceCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '会议数量'
        },
        conferenceScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '会议分数'
        },
        paperCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '论文数量'
        },
        paperScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '论文分数'
        },
        patentCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '专利数量'
        },
        patentScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '专利分数'
        },
        researchCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '科研项目数量'
        },
        researchScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '科研项目分数'
        },
        studentProjectCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '学生项目数量'
        },
        studentProjectScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '学生项目分数'
        },
        teachingProjectCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '教学项目数量'
        },
        teachingProjectScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '教学项目分数'
        },
        textbookCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '教材数量'
        },
        textbookScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '教材分数'
        },
        teachingWorkloadCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '教学工作量数量'
        },
        teachingWorkloadScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '教学工作量分数'
        },
        teachingResearchAwardCount: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            comment: '教学科技奖励数量'
        },
        teachingResearchAwardScore: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            comment: '教学科技奖励分数'
        }
    },
    {
        // 明确指定表名
        tableName: 'user_ranking_reviewed_all',
        // 不使用自动复数形式
        freezeTableName: true,
        // 禁用时间戳
        timestamps: false,
        // 添加表注释
        comment: '用户总体排名表（已审核）'
    });

module.exports = UserRankingReviewedAll;