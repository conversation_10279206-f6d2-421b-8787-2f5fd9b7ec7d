const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const patentCategoryModel = require('./patentCategoriesModel');
const userModel = require('./userModel');

// 定义专利模型
const Patent = sequelize.define('patents', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    firstResponsibleID: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '第一负责人ID'
    },
    patentName: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '专利名称'
    },
    categoryId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '专利分类ID'
    },
    authorizationDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '授权日期'
    },
    conversionDate: {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: '转化日期'
    },
    remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注'
    },
    reviewerId: {
        type: DataTypes.UUID,
        allowNull: true,
        comment: '审核人ID'
      },
    attachmentUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '附件URL'
      },
      reviewComment: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '审核意见'
      },
    ifReviewer: {
        type: DataTypes.TINYINT,
        allowNull: true,
        comment: '审核状态（0，拒审核 1，审核，null未审核）'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'patents',
    timestamps: true,
    indexes: [
        {
            name: 'idx_patent_category',
            fields: ['categoryId']
        },
        {
            name: 'idx_patent_authorization_date',
            fields: ['authorizationDate']
        }
    ]
});

// 建立与专利分类的关联关系
Patent.belongsTo(patentCategoryModel, {
    foreignKey: 'categoryId',
    as: 'category'
});

// 建立与第一负责人的关联关系
Patent.belongsTo(userModel, {
    foreignKey: 'firstResponsibleID',
    as: 'firstResponsible'
});

module.exports = Patent;

// 使用 setTimeout 延迟建立关联关系
setTimeout(() => {
  const patentParticipantModel = require('./patentParticipantsModel');
  
  // 建立与专利参与者的关联关系
  Patent.hasMany(patentParticipantModel, {
    foreignKey: 'patentId',
    as: 'participants',
    onDelete: 'CASCADE'
  });
}, 0); 