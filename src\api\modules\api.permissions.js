import service from '../server'

export const permissionsTree = (data) => {
    return service.post('/permissions/tree', data)
}
export const permissionsList = (data) => {
    return service.post('/permissions/list', data)
}
export const permissionsCreate = (data) => {
    return service.post('/permissions/create', data)
}
export const permissionsUpdate = (data) => {
    return service.post('/permissions/update', data)
}
export const permissionsDelete = (data) => {
    return service.post('/permissions/delete', data)
}
export const permissionsStop = (data) => {
    return service.post('/permissions/stop', data)
}






