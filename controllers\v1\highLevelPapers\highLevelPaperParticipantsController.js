const { Op } = require('sequelize');
const highLevelPaperParticipantsModel = require('../../../models/v1/mapping/highLevelPaperParticipantsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const highLevelPapersModel = require('../../../models/v1/mapping/highLevelPapersModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取论文参与者列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getParticipants = async (req, res) => {
  try {
    const { paperId, userId } = req.query;
    
    // 构建查询条件
    const where = {};
    
    if (paperId) {
      where.paperId = paperId;
    }
    
    if (userId) {
      where.userId = userId;
    }
    
    // 查询参与者数据
    const participants = await highLevelPaperParticipantsModel.findAll({
      where,
      include: [
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: highLevelPapersModel,
          as: 'paper',
          attributes: ['id', 'title', 'journal', 'publishDate'],
          required: false
        }
      ],
      order: [
        ['authorRank', 'ASC'],
        ['createdAt', 'ASC']
      ]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: participants
    });
  } catch (error) {
    console.error('获取论文参与者列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取论文参与者列表失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 获取论文参与者详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getParticipantById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const participant = await highLevelPaperParticipantsModel.findByPk(id, {
      include: [
        {
          model: userModel,
          as: 'user',
          attributes: ['id', 'nickname', 'username', 'studentNumber'],
          required: false
        },
        {
          model: highLevelPapersModel,
          as: 'paper',
          attributes: ['id', 'title', 'journal', 'publishDate'],
          required: false
        }
      ]
    });
    
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '参与者不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: participant
    });
  } catch (error) {
    console.error('获取论文参与者详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取论文参与者详情失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 添加论文参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.addParticipant = async (req, res) => {
  try {
    const { paperId, userId, allocationRatio, authorRank, isFirstAuthor, isCorrespondingAuthor } = req.body;
    
    // 校验必填参数
    if (!paperId) {
      return res.status(400).json({
        code: 400,
        message: '论文ID不能为空',
        data: null
      });
    }
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '用户ID不能为空',
        data: null
      });
    }
    
    // 检查论文是否存在
    const paper = await highLevelPapersModel.findByPk(paperId);
    if (!paper) {
      return res.status(404).json({
        code: 404,
        message: '论文不存在',
        data: null
      });
    }
    
    // 检查用户是否存在
    const user = await userModel.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        data: null
      });
    }
    
    // 检查是否已存在同一论文同一用户的记录
    const existingParticipant = await highLevelPaperParticipantsModel.findOne({
      where: {
        paperId,
        userId
      }
    });
    
    if (existingParticipant) {
      return res.status(400).json({
        code: 400,
        message: '该用户已是论文参与者',
        data: null
      });
    }
    
    // 如果设置为第一作者，需要检查是否已有第一作者
    if (isFirstAuthor) {
      const existingFirstAuthor = await highLevelPaperParticipantsModel.findOne({
        where: {
          paperId,
          isFirstAuthor: true
        }
      });
      
      if (existingFirstAuthor) {
        return res.status(400).json({
          code: 400,
          message: '该论文已有第一作者',
          data: null
        });
      }
    }
    
    // 创建参与者记录
    const participant = await highLevelPaperParticipantsModel.create({
      id: uuidv4(),
      paperId,
      userId,
      allocationRatio: parseFloat(allocationRatio) || 0,
      authorRank: parseInt(authorRank) || null,
      isFirstAuthor: Boolean(isFirstAuthor) || false,
      isCorrespondingAuthor: Boolean(isCorrespondingAuthor) || false
    });
    
    return res.status(201).json({
      code: 200,
      message: '添加成功',
      data: participant
    });
  } catch (error) {
    console.error('添加论文参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '添加论文参与者失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 更新论文参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateParticipant = async (req, res) => {
  try {
    const { id } = req.params;
    const { allocationRatio, authorRank, isFirstAuthor, isCorrespondingAuthor } = req.body;
    
    // 查找参与者
    const participant = await highLevelPaperParticipantsModel.findByPk(id);
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '参与者不存在',
        data: null
      });
    }
    
    // 如果设置为第一作者，需要检查是否已有第一作者（且不是当前参与者）
    if (isFirstAuthor && !participant.isFirstAuthor) {
      const existingFirstAuthor = await highLevelPaperParticipantsModel.findOne({
        where: {
          paperId: participant.paperId,
          isFirstAuthor: true,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingFirstAuthor) {
        return res.status(400).json({
          code: 400,
          message: '该论文已有第一作者',
          data: null
        });
      }
    }
    
    // 更新参与者信息
    await participant.update({
      allocationRatio: allocationRatio !== undefined ? parseFloat(allocationRatio) : participant.allocationRatio,
      authorRank: authorRank !== undefined ? parseInt(authorRank) : participant.authorRank,
      isFirstAuthor: isFirstAuthor !== undefined ? Boolean(isFirstAuthor) : participant.isFirstAuthor,
      isCorrespondingAuthor: isCorrespondingAuthor !== undefined ? Boolean(isCorrespondingAuthor) : participant.isCorrespondingAuthor
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: participant
    });
  } catch (error) {
    console.error('更新论文参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新论文参与者失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 删除论文参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteParticipant = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找参与者
    const participant = await highLevelPaperParticipantsModel.findByPk(id);
    if (!participant) {
      return res.status(404).json({
        code: 404,
        message: '参与者不存在',
        data: null
      });
    }
    
    // 删除参与者
    await participant.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除论文参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除论文参与者失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 批量添加论文参与者
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchAddParticipants = async (req, res) => {
  try {
    const { paperId, participants } = req.body;
    
    // 校验必填参数
    if (!paperId) {
      return res.status(400).json({
        code: 400,
        message: '论文ID不能为空',
        data: null
      });
    }
    
    if (!participants || !Array.isArray(participants) || participants.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '参与者数据不能为空',
        data: null
      });
    }
    
    // 检查论文是否存在
    const paper = await highLevelPapersModel.findByPk(paperId);
    if (!paper) {
      return res.status(404).json({
        code: 404,
        message: '论文不存在',
        data: null
      });
    }
    
    // 删除现有参与者
    await highLevelPaperParticipantsModel.destroy({
      where: { paperId }
    });
    
    // 创建新的参与者记录
    const newParticipants = [];
    for (const participant of participants) {
      if (!participant.userId) {
        continue;
      }
      
      try {
        const newParticipant = await highLevelPaperParticipantsModel.create({
          id: uuidv4(),
          paperId,
          userId: participant.userId,
          allocationRatio: parseFloat(participant.allocationRatio) || 0,
          authorRank: parseInt(participant.authorRank) || null,
          isFirstAuthor: Boolean(participant.isFirstAuthor) || false,
          isCorrespondingAuthor: Boolean(participant.isCorrespondingAuthor) || false
        });
        
        newParticipants.push(newParticipant);
      } catch (error) {
        console.error(`添加参与者 ${participant.userId} 失败:`, error);
      }
    }
    
    return res.status(201).json({
      code: 200,
      message: '批量添加成功',
      data: {
        total: participants.length,
        success: newParticipants.length,
        participants: newParticipants
      }
    });
  } catch (error) {
    console.error('批量添加论文参与者失败:', error);
    return res.status(500).json({
      code: 500,
      message: '批量添加论文参与者失败: ' + error.message,
      data: null
    });
  }
}; 