#!/usr/bin/env bash
set -euo pipefail

FRONTEND_DIR="/root/jxpd/jn-jxpd-admin"
SRC_DIR="$FRONTEND_DIR/dist"
TARGET_DIR="/var/www/jxpd-admin"
DO_NGINX_RELOAD=${DO_NGINX_RELOAD:-1}
DO_GIT_PULL=${DO_GIT_PULL:-0}
FALLBACK_RSYNC=${FALLBACK_RSYNC:-1}   # bind mount 失败时回退到 rsync（默认开启）

echo "==> 前端目录: $FRONTEND_DIR"
cd "$FRONTEND_DIR"

# 0) 可选更新代码
if [[ "$DO_GIT_PULL" == "1" ]]; then
  echo "==> git pull"
  git pull --rebase || true
fi

# 1) 安装依赖
if [[ -f package-lock.json ]]; then
  echo "==> npm ci"
  npm ci
else
  echo "==> npm install"
  npm install
fi

# 2) 确保目标目录存在再判断挂载状态
sudo mkdir -p "$TARGET_DIR"

# 如已挂载（或存在 /deleted 脏挂载），卸载后再继续
if findmnt -no SOURCE "$TARGET_DIR" 2>/dev/null | grep -q '/deleted'; then
  echo "==> 发现脏挂载（/deleted），清理中..."
  sudo umount -l "$TARGET_DIR" || true
elif mountpoint -q "$TARGET_DIR"; then
  echo "==> 卸载 bind mount: $TARGET_DIR"
  sudo umount -l "$TARGET_DIR" || true
fi

# 3) 清理旧 dist 并构建
echo "==> 清理旧 dist"
rm -rf "$SRC_DIR"
mkdir -p "$SRC_DIR"

echo "==> npm run build"
npm run build

# 4) 重新绑定挂载（失败则回退到 rsync）
echo "==> 绑定/同步发布：$SRC_DIR -> $TARGET_DIR"
if sudo mount --bind "$SRC_DIR" "$TARGET_DIR" 2>/dev/null; then
  echo "   - bind mount 成功"
  grep -q "$SRC_DIR $TARGET_DIR none bind 0 0" /etc/fstab || \
    echo "$SRC_DIR  $TARGET_DIR  none  bind  0  0" | sudo tee -a /etc/fstab >/dev/null
else
  echo "❗ bind mount 失败"
  if [[ "$FALLBACK_RSYNC" == "1" ]]; then
    echo "==> 使用 rsync 回退方案"
    sudo rsync -a --delete "$SRC_DIR/" "$TARGET_DIR/"
  else
    echo "请检查目录是否存在：$SRC_DIR 或 $TARGET_DIR"
    exit 1
  fi
fi

# 5) 权限（nginx 可读）
echo "==> 设置权限"
sudo find "$SRC_DIR" -type d -exec chmod 755 {} \;
sudo find "$SRC_DIR" -type f -exec chmod 644 {} \;

# 6) （可选）重载 Nginx
if [[ "$DO_NGINX_RELOAD" == "1" ]]; then
  echo "==> 检查并重载 Nginx"
  sudo nginx -t
  sudo systemctl reload nginx
fi

# 7) 版本标记
date -u +"%F %T UTC" > "$SRC_DIR/.deploy_time"

echo "✅ 前端已更新完成：$SRC_DIR → $TARGET_DIR"
echo "   立即构建部署并拉代码：DO_GIT_PULL=1 ./update_frontend.sh"
echo "   仅构建不 reload nginx：DO_NGINX_RELOAD=0 ./update_frontend.sh"
echo "   关闭 rsync 回退：FALLBACK_RSYNC=0 ./update_frontend.sh"
