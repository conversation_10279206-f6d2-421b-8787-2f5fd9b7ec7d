const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const researchProjectModel = sequelize.define(
  'research_projects',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '项目ID'
    },
    submitterId: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: '提交人ID'
    },
    projectId: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '项目编号'
    },
    projectIssuingDepartment: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '项目下达部门'
    },
    type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '项目类型'
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '项目名称'
    },
    approvalDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: '获批时间'
    },
    levelId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '项目级别ID'
    },
    fundingAmount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      comment: '经费金额'
    },
    isUniversityFirstUnit: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: 1,
      comment: '文章第一单位是否为我们大学'
    },
    isCollegeFirstUnit: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: 1,
      comment: '如果文章第一单位为大学，是否我们学院为第一单位'
    },
    startDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: '开始日期'
    },
    endDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: '结束日期'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '项目描述'
    },
    attachmentUrl: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '附件URL'
    },
    reviewerId: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: '审核人ID'
    },
    reviewComment: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '审核意见'
    },
    ifReviewer: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      comment: '审核状态（默认为空，0拒绝未审核 1，同意审核）'
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注'
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: 1,
      comment: '状态(1:进行中,0:已结项)'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '更新时间'
    }
  },
  {
    sequelize,
    modelName: 'research_projects',
    tableName: 'research_projects',
    timestamps: true
  }
);

// 在文件加载后添加关联，避免循环依赖问题
const setupAssociations = () => {
  const userModel = require('./userModel');
  const researchProjectLevelsRulesModel = require('./researchProjectsLevelsRulesModel');

  // 与用户表关联
  researchProjectModel.belongsTo(userModel, {
    foreignKey: 'submitterId',
    as: 'submitter'
  });

  // 与项目表关联
  researchProjectModel.belongsTo(researchProjectLevelsRulesModel, {
    foreignKey: 'levelId',
    as: 'level'
  });

  // 与审核人的关联关系
  researchProjectModel.belongsTo(userModel, {
    foreignKey: 'reviewerId',
    as: 'reviewer'
  });
};

// 确保关联设置被调用
setTimeout(setupAssociations, 0);

module.exports = researchProjectModel; 