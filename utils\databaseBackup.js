/**
 * 数据库备份工具
 * 用于定期备份MySQL数据库
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const os = require('os');

// 加载环境变量
const isDev = process.env.NODE_ENV === 'development';
require('dotenv').config({path: isDev ? './.env.development' : './.env.production'});

// 检查是否为Windows系统
const isWindows = os.platform() === 'win32';

// 数据库配置
const DB_CONFIG = {
    host: process.env.DB_HOST || '************',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USERNAME || 'zsh',
    password: process.env.DB_PASSWORD || '213yuUINI@#zbxy78',
    database: process.env.DB_NAME || 'jxpd'
};

console.log('数据库配置:', {
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.user,
    password: DB_CONFIG.password ? '***已设置***' : '未设置',
    database: DB_CONFIG.database
});

console.log('环境变量检查:', {
    NODE_ENV: process.env.NODE_ENV,
    DB_HOST: process.env.DB_HOST,
    DB_USERNAME: process.env.DB_USERNAME,
    DB_PASSWORD: process.env.DB_PASSWORD ? '***已设置***' : '未设置',
    DB_NAME: process.env.DB_NAME
});

// 强制设置NODE_ENV为development来测试
process.env.NODE_ENV = 'development';
require('dotenv').config({path: './.env.development'});

console.log('重新加载开发环境变量后:', {
    NODE_ENV: process.env.NODE_ENV,
    DB_HOST: process.env.DB_HOST,
    DB_USERNAME: process.env.DB_USERNAME,
    DB_PASSWORD: process.env.DB_PASSWORD ? '***已设置***' : '未设置',
    DB_NAME: process.env.DB_NAME
});

// 重新设置数据库配置 - 直接使用正确的配置
DB_CONFIG.host = '************';
DB_CONFIG.user = 'zsh';
DB_CONFIG.password = '213yuUINI@#zbxy78';
DB_CONFIG.database = 'jxpd';

console.log('最终数据库配置:', {
    host: DB_CONFIG.host,
    port: DB_CONFIG.port,
    user: DB_CONFIG.user,
    password: DB_CONFIG.password ? '***已设置***' : '未设置',
    database: DB_CONFIG.database
});

// 备份配置
const BACKUP_CONFIG = {
    // 备份文件存储目录
    backupDir: path.join(__dirname, '../backups'),
    // 备份文件保留天数
    retentionDays: 30,
    // 备份文件名前缀
    filePrefix: 'jxpd_backup',
    // 是否压缩备份文件
    compress: true
};

/**
 * 检查mysqldump命令是否可用
 */
const checkMysqldumpAvailable = async () => {
    try {
        const command = isWindows ? 'where mysqldump' : 'which mysqldump';
        await execAsync(command);
        return true;
    } catch (error) {
        return false;
    }
};

/**
 * 确保备份目录存在
 */
const ensureBackupDirectory = () => {
    if (!fs.existsSync(BACKUP_CONFIG.backupDir)) {
        fs.mkdirSync(BACKUP_CONFIG.backupDir, { recursive: true });
        console.log(`创建备份目录: ${BACKUP_CONFIG.backupDir}`);
    }
};

/**
 * 生成备份文件名
 * @returns {string} 备份文件名
 */
const generateBackupFileName = () => {
    const now = new Date();
    const timestamp = now.toISOString()
        .replace(/[:.]/g, '-')
        .replace('T', '_')
        .split('.')[0];
    
    const extension = BACKUP_CONFIG.compress ? '.sql.gz' : '.sql';
    return `${BACKUP_CONFIG.filePrefix}_${timestamp}${extension}`;
};

/**
 * 使用Node.js直接备份数据库（替代方案）
 */
const performNodeBackup = async () => {
    const mysql = require('mysql2/promise');

    try {
        console.log('使用Node.js直接备份数据库...');

        // 创建数据库连接
        const connection = await mysql.createConnection({
            host: DB_CONFIG.host,
            port: DB_CONFIG.port,
            user: DB_CONFIG.user,
            password: DB_CONFIG.password,
            database: DB_CONFIG.database
        });

        // 确保备份目录存在
        ensureBackupDirectory();

        // 生成备份文件名
        const backupFileName = generateBackupFileName().replace('.gz', ''); // Node.js备份不压缩
        const backupFilePath = path.join(BACKUP_CONFIG.backupDir, backupFileName);

        console.log(`备份文件: ${backupFilePath}`);

        // 获取所有表名
        const [tables] = await connection.execute('SHOW TABLES');
        const tableNames = tables.map(row => Object.values(row)[0]);

        console.log(`发现 ${tableNames.length} 个表:`, tableNames);

        let sqlContent = `-- 数据库备份文件\n-- 生成时间: ${new Date().toLocaleString('zh-CN')}\n-- 数据库: ${DB_CONFIG.database}\n\n`;
        sqlContent += `SET FOREIGN_KEY_CHECKS=0;\n\n`;

        // 备份每个表
        for (const tableName of tableNames) {
            console.log(`备份表: ${tableName}`);

            // 获取表结构
            const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
            sqlContent += `-- 表结构: ${tableName}\n`;
            sqlContent += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
            sqlContent += `${createTable[0]['Create Table']};\n\n`;

            // 获取表数据
            const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);

            if (rows.length > 0) {
                sqlContent += `-- 表数据: ${tableName}\n`;
                sqlContent += `INSERT INTO \`${tableName}\` VALUES\n`;

                const values = rows.map(row => {
                    const rowValues = Object.values(row).map(value => {
                        if (value === null) return 'NULL';
                        if (typeof value === 'string') return `'${value.replace(/'/g, "\\'")}'`;
                        if (value instanceof Date) return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                        return value;
                    });
                    return `(${rowValues.join(', ')})`;
                });

                sqlContent += values.join(',\n') + ';\n\n';
            }
        }

        sqlContent += `SET FOREIGN_KEY_CHECKS=1;\n`;

        // 写入备份文件
        fs.writeFileSync(backupFilePath, sqlContent, 'utf8');

        // 关闭数据库连接
        await connection.end();

        // 获取备份文件大小
        const stats = fs.statSync(backupFilePath);
        const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);

        console.log(`Node.js备份完成! 文件大小: ${fileSizeMB}MB`);
        console.log(`备份文件路径: ${backupFilePath}`);

        return backupFilePath;

    } catch (error) {
        console.error('Node.js备份失败:', error);
        throw error;
    }
};

/**
 * 执行数据库备份
 * @returns {Promise<string>} 备份文件路径
 */
const performBackup = async () => {
    try {
        console.log('开始执行数据库备份...');

        // 检查mysqldump是否可用
        const mysqldumpAvailable = await checkMysqldumpAvailable();
        console.log(`mysqldump可用性: ${mysqldumpAvailable}`);

        if (!mysqldumpAvailable) {
            console.log('mysqldump不可用，使用Node.js直接备份方案');
            return await performNodeBackup();
        }

        // 确保备份目录存在
        ensureBackupDirectory();

        // 生成备份文件名
        const backupFileName = generateBackupFileName();
        const backupFilePath = path.join(BACKUP_CONFIG.backupDir, backupFileName);

        // 构建mysqldump命令
        let mysqldumpCmd = `mysqldump -h${DB_CONFIG.host} -P${DB_CONFIG.port} -u${DB_CONFIG.user}`;

        if (DB_CONFIG.password) {
            mysqldumpCmd += ` -p${DB_CONFIG.password}`;
        }

        // 添加备份选项
        mysqldumpCmd += ` --single-transaction --routines --triggers --events`;
        mysqldumpCmd += ` --add-drop-database --databases ${DB_CONFIG.database}`;

        // 根据是否压缩决定输出方式
        if (BACKUP_CONFIG.compress) {
            mysqldumpCmd += ` | gzip > "${backupFilePath}"`;
        } else {
            mysqldumpCmd += ` > "${backupFilePath}"`;
        }

        console.log('执行mysqldump备份命令...');
        console.log(`备份文件: ${backupFilePath}`);

        // 执行备份命令
        const { stdout, stderr } = await execAsync(mysqldumpCmd);

        if (stderr && !stderr.includes('Warning')) {
            throw new Error(`备份执行错误: ${stderr}`);
        }

        // 检查备份文件是否创建成功
        if (!fs.existsSync(backupFilePath)) {
            throw new Error('备份文件创建失败');
        }

        // 获取备份文件大小
        const stats = fs.statSync(backupFilePath);
        const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);

        console.log(`mysqldump备份完成! 文件大小: ${fileSizeMB}MB`);
        console.log(`备份文件路径: ${backupFilePath}`);

        return backupFilePath;

    } catch (error) {
        console.error('数据库备份失败:', error);
        throw error;
    }
};

/**
 * 清理过期的备份文件
 */
const cleanupOldBackups = () => {
    try {
        console.log('开始清理过期备份文件...');
        
        if (!fs.existsSync(BACKUP_CONFIG.backupDir)) {
            console.log('备份目录不存在，跳过清理');
            return;
        }
        
        const files = fs.readdirSync(BACKUP_CONFIG.backupDir);
        const now = new Date();
        const cutoffTime = now.getTime() - (BACKUP_CONFIG.retentionDays * 24 * 60 * 60 * 1000);
        
        let deletedCount = 0;
        
        files.forEach(file => {
            if (file.startsWith(BACKUP_CONFIG.filePrefix)) {
                const filePath = path.join(BACKUP_CONFIG.backupDir, file);
                const stats = fs.statSync(filePath);
                
                if (stats.mtime.getTime() < cutoffTime) {
                    fs.unlinkSync(filePath);
                    deletedCount++;
                    console.log(`删除过期备份文件: ${file}`);
                }
            }
        });
        
        console.log(`清理完成，删除了 ${deletedCount} 个过期备份文件`);
        
    } catch (error) {
        console.error('清理备份文件失败:', error);
    }
};

/**
 * 获取备份文件列表
 * @returns {Array} 备份文件信息列表
 */
const getBackupList = () => {
    try {
        if (!fs.existsSync(BACKUP_CONFIG.backupDir)) {
            return [];
        }
        
        const files = fs.readdirSync(BACKUP_CONFIG.backupDir);
        const backupFiles = [];
        
        files.forEach(file => {
            if (file.startsWith(BACKUP_CONFIG.filePrefix)) {
                const filePath = path.join(BACKUP_CONFIG.backupDir, file);
                const stats = fs.statSync(filePath);
                
                backupFiles.push({
                    fileName: file,
                    filePath: filePath,
                    size: stats.size,
                    sizeFormatted: (stats.size / (1024 * 1024)).toFixed(2) + 'MB',
                    createdAt: stats.mtime,
                    createdAtFormatted: stats.mtime.toLocaleString('zh-CN')
                });
            }
        });
        
        // 按创建时间倒序排列
        backupFiles.sort((a, b) => b.createdAt - a.createdAt);
        
        return backupFiles;
        
    } catch (error) {
        console.error('获取备份文件列表失败:', error);
        return [];
    }
};

/**
 * 检查是否需要执行备份（基于15天频率控制）
 * @returns {boolean} 是否需要备份
 */
const shouldPerformBackup = () => {
    try {
        if (!fs.existsSync(BACKUP_CONFIG.backupDir)) {
            console.log('备份目录不存在，需要执行备份');
            return true;
        }

        const files = fs.readdirSync(BACKUP_CONFIG.backupDir);
        const backupFiles = files.filter(file =>
            file.startsWith(BACKUP_CONFIG.filePrefix) && file.endsWith('.sql')
        );

        if (backupFiles.length === 0) {
            console.log('没有找到历史备份文件，需要执行备份');
            return true;
        }

        // 检查最近15天内是否有备份文件
        const fifteenDaysAgo = new Date();
        fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15);

        for (const file of backupFiles) {
            const filePath = path.join(BACKUP_CONFIG.backupDir, file);
            const stats = fs.statSync(filePath);
            const fileCreationTime = stats.birthtime || stats.mtime;

            if (fileCreationTime > fifteenDaysAgo) {
                console.log(`发现15天内的备份文件: ${file}`);
                console.log(`文件创建时间: ${fileCreationTime.toLocaleString('zh-CN')}`);
                console.log('根据备份频率控制，跳过本次备份操作');
                return false;
            }
        }

        console.log('没有发现15天内的备份文件，需要执行备份');
        return true;

    } catch (error) {
        console.error('检查备份频率时发生错误:', error);
        // 发生错误时默认执行备份
        return true;
    }
};

/**
 * 执行完整的备份流程
 * 包括备份和清理过期文件
 * @param {boolean} forceBackup - 是否强制备份（跳过频率检查）
 */
const runBackupProcess = async (forceBackup = false) => {
    try {
        console.log('=== 开始数据库备份流程 ===');
        console.log(`时间: ${new Date().toLocaleString('zh-CN')}`);
        console.log(`数据库: ${DB_CONFIG.database}`);
        console.log(`强制备份模式: ${forceBackup ? '是' : '否'}`);

        // 检查是否需要备份（除非强制备份）
        if (!forceBackup && !shouldPerformBackup()) {
            const backupList = getBackupList();
            console.log('=== 备份流程结束（已跳过） ===');

            return {
                success: true,
                skipped: true,
                message: '根据备份频率控制，跳过本次备份操作（15天内已有备份文件）',
                backupList: backupList,
                totalBackups: backupList.length
            };
        }

        console.log('开始执行备份操作...');

        // 执行备份
        const backupFilePath = await performBackup();
        
        // 清理过期备份
        cleanupOldBackups();
        
        // 获取当前备份文件列表
        const backupList = getBackupList();
        console.log(`当前共有 ${backupList.length} 个备份文件`);
        
        console.log('=== 备份流程完成 ===');
        
        return {
            success: true,
            backupFilePath,
            backupList,
            message: '数据库备份成功'
        };
        
    } catch (error) {
        console.error('=== 备份流程失败 ===');
        console.error(error);
        
        return {
            success: false,
            error: error.message,
            message: '数据库备份失败'
        };
    }
};

module.exports = {
    performBackup,
    performNodeBackup,
    checkMysqldumpAvailable,
    cleanupOldBackups,
    getBackupList,
    runBackupProcess,
    shouldPerformBackup,
    BACKUP_CONFIG,
    DB_CONFIG
};
