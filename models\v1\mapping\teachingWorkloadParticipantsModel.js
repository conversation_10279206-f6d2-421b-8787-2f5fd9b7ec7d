const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const teachingWorkloadParticipantsModel = sequelize.define(
  'teaching_workload_participants',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '参与者记录ID'
    },
    workloadId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '工作量ID（外键）'
    },
    participantId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '参与者ID（外键，关联user.id）'
    },
    employeeNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '人事编号'
    },
    allocationRatio: {
      type: DataTypes.DECIMAL(5, 4),
      allowNull: false,
      comment: '分配比例（0~1）'
    },
    isLeader: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否主讲教师（1-是）'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '更新时间'
    }
  },
  {
    sequelize,
    modelName: 'teaching_workload_participants',
    tableName: 'teaching_workload_participants',
    timestamps: true
  }
);

// 在文件加载后添加关联，避免循环依赖问题
const setupAssociations = () => {
  const userModel = require('./userModel');
  const teachingWorkloadsModel = require('./teachingWorkloadsModel');

  // 与用户表关联
  teachingWorkloadParticipantsModel.belongsTo(userModel, {
    foreignKey: 'participantId',
    as: 'participant'
  });

  // 与工作量表关联
  teachingWorkloadParticipantsModel.belongsTo(teachingWorkloadsModel, {
    foreignKey: 'workloadId',
    as: 'workload'
  });
};

// 导出模型时调用关联设置
setTimeout(setupAssociations, 0);

module.exports = teachingWorkloadParticipantsModel;
