#!/usr/bin/env bash
set -euo pipefail

# ========= 可按需修改 =========
APP_DIR="/root/jxpd/jn-jxpd-server"   # 后端代码目录
PM2_NAME="jxpd-server"                # PM2 进程名
BRANCH="${BRANCH:-master}"            # 要发布的分支（可用环境变量覆盖：BRANCH=xxx）
ENTRY="${ENTRY:-app.js}"              # 入口文件（没 ecosystem.config.js 时用）
NODE_ENV="${NODE_ENV:-production}"    # 运行环境
PORT="${PORT:-3089}"                  # 服务端口（按你的 Nginx 配置）
# =================================

echo "==> 切换到项目目录: $APP_DIR"
cd "$APP_DIR"

echo "==> 拉取最新代码（$BRANCH）"
git fetch --all --prune
git checkout -B "$BRANCH" "origin/$BRANCH"
git pull --ff-only

# --- 安装 Node 依赖 ---
if [[ -f package.json ]]; then
  echo "==> 检测到 Node 项目，安装依赖"
  if [[ -f package-lock.json ]]; then
    npm ci
  else
    npm install --production
  fi

  # 若有构建脚本或 TypeScript，则构建一下（不存在就跳过）
  if npm run | grep -qE ' build'; then
    echo "==> 运行构建脚本：npm run build"
    npm run build || true
  fi
fi

# --- 安装 Python 依赖（如存在）---
if [[ -f requirements.txt ]]; then
  echo "==> 检测到 Python 依赖清单，安装依赖"
  # 选用系统 pip3；你有虚拟环境的话在这里激活
  python3 -m pip install --upgrade pip
  pip3 install -r requirements.txt
fi

# --- 重启/启动 PM2 ---
echo "==> 重启 PM2 进程：$PM2_NAME"
export NODE_ENV="$NODE_ENV"
export PORT="$PORT"

if pm2 list | grep -q "$PM2_NAME"; then
  pm2 restart "$PM2_NAME" --update-env
else
  # 若你使用 ecosystem.config.js，优先用它；否则用入口文件启动
  if [[ -f ecosystem.config.js ]]; then
    pm2 start ecosystem.config.js --only "$PM2_NAME"
  else
    pm2 start "$ENTRY" --name "$PM2_NAME" --cwd "$APP_DIR" --update-env
  fi
fi

pm2 save

echo "==> 部署完成"
echo "    分支: $BRANCH"
echo "    环境: NODE_ENV=$NODE_ENV  PORT=$PORT"
echo "    进程: $(pm2 info "$PM2_NAME" >/dev/null 2>&1 && echo OK || echo FAILED)"
echo "    提示: 查看日志 -> pm2 logs $PM2_NAME --lines 100"
