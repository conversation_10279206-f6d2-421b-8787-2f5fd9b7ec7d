const express = require('express');
const router = express.Router();
const teachingWorkloadLevelsController = require('../../../controllers/v1/teachingWorkloads/teachingWorkloadLevelsController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建教学工作量级别模块的权限中间件
const levelsPermission = (action) => createModulePermission('teachingWorkloads', action);;

/**
 * 获取教学工作量级别列表（分页）
 * @route GET /v1/sys/teaching-workloads/levels
 * @group 教学工作量级别管理 - 教学工作量级别相关接口
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @param {string} categoryName.query - 类别名称（模糊搜索）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels',
  authMiddleware,
  levelsPermission('list'),
  teachingWorkloadLevelsController.getWorkloadLevels
);

/**
 * 获取所有教学工作量级别（不分页）
 * @route GET /v1/sys/teaching-workloads/levels/all
 * @group 教学工作量级别管理 - 教学工作量级别相关接口
 * @returns {object} 200 - {code: 200, message: "获取成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/levels/all',
  authMiddleware,
  levelsPermission('list'),
  teachingWorkloadLevelsController.getAllWorkloadLevels
);

/**
 * 创建教学工作量级别
 * @route POST /v1/sys/teaching-workloads/level/create
 * @group 教学工作量级别管理 - 教学工作量级别相关接口
 * @param {string} categoryName.body.required - 类别名称
 * @param {number} score.body - 基础分数
 * @param {string} description.body - 类别描述
 * @param {number} status.body - 状态（1-启用，0-禁用）
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/level/create',
  authMiddleware,
  levelsPermission('create'),
  teachingWorkloadLevelsController.createWorkloadLevel
);

/**
 * 获取教学工作量级别详情
 * @route GET /v1/sys/teaching-workloads/level/detail/:id
 * @group 教学工作量级别管理 - 教学工作量级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/level/detail/:id',
  authMiddleware,
  levelsPermission('detail'),
  teachingWorkloadLevelsController.getWorkloadLevelDetail
);

/**
 * 更新教学工作量级别
 * @route PUT /v1/sys/teaching-workloads/level/update/:id
 * @group 教学工作量级别管理 - 教学工作量级别相关接口
 * @param {string} id.path.required - 级别ID
 * @param {string} categoryName.body - 类别名称
 * @param {number} score.body - 基础分数
 * @param {string} description.body - 类别描述
 * @param {number} status.body - 状态（1-启用，0-禁用）
 * @returns {object} 200 - {code: 200, message: "更新成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/level/update/:id',
  authMiddleware,
  levelsPermission('update'),
  teachingWorkloadLevelsController.updateWorkloadLevel
);

/**
 * 删除教学工作量级别
 * @route DELETE /v1/sys/teaching-workloads/level/delete/:id
 * @group 教学工作量级别管理 - 教学工作量级别相关接口
 * @param {string} id.path.required - 级别ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/level/delete/:id',
  authMiddleware,
  levelsPermission('delete'),
  teachingWorkloadLevelsController.deleteWorkloadLevel
);

/**
 * 批量删除教学工作量级别
 * @route POST /v1/sys/teaching-workloads/levels/batch-delete
 * @group 教学工作量级别管理 - 教学工作量级别相关接口
 * @param {array} ids.body.required - 级别ID数组
 * @returns {object} 200 - {code: 200, message: "批量删除成功", data: {deletedCount: number}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/levels/batch-delete',
  authMiddleware,
  levelsPermission('delete'),
  teachingWorkloadLevelsController.batchDeleteWorkloadLevels
);

module.exports = router;
