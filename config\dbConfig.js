const { Sequelize } = require('sequelize');
const chalk = require('chalk');

// 自定义的日志函数，过滤掉查询语句的输出
function myCustomLoggerFunction(log) {
    if (log.includes('Executing (default):')) {
        return;
    }
    console.log(log);
}

const config = {
    // 连接池配置
    pool: {
        max: 10,            // 最大连接数
        min: 0,             // 最小连接数
        acquire: 30000,     // 获取连接的超时时间（毫秒）
        idle: 10000         // 连接在池中保持空闲的最长时间（毫秒）
    },

    // 数据库连接配置
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    database: process.env.DB_NAME || 'jxpd',
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '123456',
    dialect: 'mysql',
    logging: process.env.NODE_ENV === 'development' ? myCustomLoggerFunction : false,
    timezone: '+08:00', // 设置时区为东八区
    define: {
        timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
        underscored: false, // 禁用下划线命名法
        freezeTableName: true // 禁止表名自动复数化
    }
};

// 创建 Sequelize 实例
const sequelize = new Sequelize(
    config.database,
    config.username,
    config.password,
    {
        host: config.host,
        port: config.port,
        dialect: config.dialect,
        pool: config.pool,
        logging: config.logging,
        timezone: config.timezone,
        define: config.define
    }
);

/**
 *<AUTHOR>
 *@date 2023/11/17
 *@Description:数据库连接失败请检查数据库是否建立账号密码是否正确 位置：.env.*文件
 */
// 测试数据库连接
sequelize.authenticate()
    .then(() => {
        console.log(chalk.green('数据库连接成功'));
        console.log(chalk.hex('#bf00d0').bold(`******************数据库连接成功********************`));
        console.log(chalk.hex('#bf00d0').bold(`【数据库】：数据库连接已成功建立.`));
        console.log(chalk.hex('#bf00d0').bold(`【数据库主机】：${process.env.DB_HOST}`));
        console.log(chalk.hex('#bf00d0').bold(`【数据库名称】：${process.env.DB_NAME}`));
        // 同步模型与数据库
        sequelize
            .sync({logging: false}) // 关闭数据库同步日志
            .then(() => {
                console.log(chalk.hex('#bf00d0').bold(`【数据库状态】：数据库和表已同步`));
                console.log(chalk.hex('#bf00d0').bold(`******************数据库连接成功********************`));
            })
            .catch(err => {
                console.error('同步数据库错误:', err);
            });
    })
    .catch(err => {
        console.error(chalk.red('数据库连接失败:', err));
        console.log(chalk.red.bold(`******************数据库连接失败*********************`));
        console.log(chalk.red.bold(`【数据库主机】：${process.env.DB_HOST}`));
        console.log(chalk.red.bold(`【数据库名称】：${process.env.DB_NAME}`));
        console.log(chalk.red.bold(`【数据库用户名】：${process.env.DB_USERNAME}`));
        console.log(chalk.red.bold(`【数据库密码】：${process.env.DB_PASSWORD}`));
        console.log(chalk.red.bold(`******************数据库连接失败*********************`));
    });

module.exports = sequelize;
