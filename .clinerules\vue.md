# Vue 前端项目规范（vue.md）

## 1. 技术栈约定
- Vue 版本：3.2+
- 使用 Composition API 和 `<script setup>`
- 状态管理：Pinia（每个模块对应一个 store）
- UI 框架：Ant Design Vue（统一风格）

## 2. 目录结构建议
- `/api`：接口模块，按业务模块划分
- `/components`：通用组件放 common，业务组件单独文件夹
- `/views`：页面组件，建议与 router 配置同步命名
- `/stores`：Pinia 模块，组织清晰
- `/assets/styles/`：采用 SCSS 架构，支持设计令牌（design tokens）

## 3. 命名规则
- 所有组件统一使用 `Zy` 前缀，如 `ZyTable`
- Props 类型需完整定义
- 接口响应需定义统一接口类型（如 `ApiResponse<T>`）

## 4. 测试与文档
- 单元测试框架为 Vitest，配合 @vue/test-utils
- 建议集成 Storybook 做组件文档化
- 所有公共函数和组件需有 JSDoc 注释说明

## 5. 性能与优化
- 所有页面组件采用懒加载（Vue Router）
- 建议启用 Vite 的动态 import 以实现代码分割
- 引入组件按需加载，移除未使用依赖

## 6. 错误处理与安全
- 全局错误捕获（使用 `app.config.errorHandler`）
- API 错误处理模块标准化封装
- 所有用户输入前需验证合法性（使用 VeeValidate / 自定义）

## 7. 样式系统
- 使用 CSS Modules / SCSS 模块化
- 支持主题切换（通过 CSS 变量或 less 改色）
- 样式目录结构参考 BEM 命名，使用 tokens 控制风格一致性
