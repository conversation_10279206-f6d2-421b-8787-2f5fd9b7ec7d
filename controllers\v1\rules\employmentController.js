const EmploymentQuality = require('../../../models/v1/mapping/employmentQualityModel');
const { v4: uuidv4 } = require('uuid');
const { Op, Sequelize } = require('sequelize');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

/**
 * 就业质量控制器
 * 
 * 更新说明：
 * 1. 图表数据查询已从Sequelize ORM方式修改为原生SQL查询
 * 2. 增加了模型存在性检查，提高代码健壮性
 * 3. 从employment_quality表中直接计算统计数据，不依赖特殊表结构
 * 4. 优化了数据格式化逻辑，确保返回标准格式的数据
 */

/**
 * 获取就业质量列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getEmploymentList = async (req, res) => {
  try {
    const { major, year, employmentIndustry, employmentRegion, page = 1, pageSize = 10, userId } = req.query;
    
    // 构建查询条件
    const where = { status: 1 };
    if (major) where.major = { [Op.like]: `%${major}%` };
    if (year) where.year = year;
    if (employmentIndustry) where.employmentIndustry = employmentIndustry;
    if (employmentRegion) where.employmentRegion = employmentRegion;
    if (userId) where.userId = userId;
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    
    const { count, rows } = await EmploymentQuality.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['year', 'DESC'], ['major', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        total: count,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(count / pageSize)
      }
    });
  } catch (error) {
    console.error('获取就业质量列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取就业质量列表失败',
      data: null
    });
  }
};

/**
 * 获取就业质量详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getEmploymentById = async (req, res) => {
  try {
    // 支持两种方式获取ID：1. 路径参数id 2. 查询参数userId
    const id = req.params.id;
    const { userId } = req.query;
    
    let employment;
    
    // 如果有路径参数ID，优先使用
    if (id) {
      employment = await EmploymentQuality.findByPk(id);
    } 
    // 否则，尝试使用查询参数userId
    else if (userId) {
      employment = await EmploymentQuality.findOne({
        where: { userId, status: 1 }
      });
    } else {
      return res.status(400).json({
        code: 400,
        message: '缺少必要的ID参数',
        data: null
      });
    }
    
    // 如果记录不存在，返回成功状态码但数据为空
    if (!employment) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: employment
    });
  } catch (error) {
    console.error('获取就业质量详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取就业质量详情失败',
      data: null
    });
  }
};

/**
 * 创建就业质量记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createEmployment = async (req, res) => {
  try {
    const { 
      major, 
      year, 
      employmentRate, 
      employmentIndustry, 
      employmentRegion, 
      averageSalary, 
      majorMatchRate, 
      employmentSatisfaction, 
      score,
      userId,
      username
    } = req.body;
    
    // 查询是否已存在相同专业和年份的记录
    const whereCondition = {
      major,
      year,
      userId
    };
    
    const existingRecord = await EmploymentQuality.findOne({
      where: whereCondition
    });
    
    if (existingRecord) {
      return res.status(400).json({
        code: 400,
        message: `${year}年${major}的就业质量记录已存在`,
        data: null
      });
    }
    
    // 创建就业质量记录
    const employment = await EmploymentQuality.create({
      id: uuidv4(),
      userId,
      username: username || '',
      major,
      year,
      employmentRate,
      employmentIndustry: employmentIndustry || '',
      employmentRegion: employmentRegion || '',
      averageSalary: averageSalary || 0,
      majorMatchRate: majorMatchRate || 0,
      employmentSatisfaction: employmentSatisfaction || 0,
      score: score || 0,
      status: 1
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: employment
    });
  } catch (error) {
    console.error('创建就业质量记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建就业质量记录失败',
      data: null
    });
  }
};

/**
 * 更新就业质量记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateEmployment = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      major, 
      year, 
      employmentRate, 
      employmentIndustry, 
      employmentRegion, 
      averageSalary, 
      majorMatchRate, 
      employmentSatisfaction, 
      score,
      userId,
      username,
      remark
    } = req.body;
    
    // 查找就业质量记录
    const employment = await EmploymentQuality.findByPk(id);
    if (!employment) {
      return res.status(200).json({
        code: 400,
        message: '未找到要更新的记录',
        data: null
      });
    }
    
    // 如果专业或年份或用户ID变更，检查是否与其他记录冲突
    if (major !== employment.major || year !== employment.year || userId !== employment.userId) {
      const existingRecord = await EmploymentQuality.findOne({
        where: {
          major,
          year,
          userId,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingRecord) {
        return res.status(400).json({
          code: 400,
          message: `${year}年${major}的就业质量记录已存在`,
          data: null
        });
      }
    }
    
    // 更新就业质量记录
    await employment.update({
      major,
      year,
      employmentRate,
      userId,
      username: username || '',
      employmentIndustry: employmentIndustry || employment.employmentIndustry,
      employmentRegion: employmentRegion || employment.employmentRegion,
      averageSalary: averageSalary !== undefined ? averageSalary : employment.averageSalary,
      majorMatchRate: majorMatchRate !== undefined ? majorMatchRate : employment.majorMatchRate,
      employmentSatisfaction: employmentSatisfaction !== undefined ? employmentSatisfaction : employment.employmentSatisfaction,
      score: score !== undefined ? score : employment.score,
      remark: remark !== undefined ? remark : employment.remark,
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: employment
    });
  } catch (error) {
    console.error('更新就业质量记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新就业质量记录失败',
      data: null
    });
  }
};

/**
 * 删除就业质量记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteEmployment = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找就业质量记录
    const employment = await EmploymentQuality.findByPk(id);
    if (!employment) {
      return res.status(200).json({
        code: 400,
        message: '未找到要删除的记录',
        data: null
      });
    }
    
    // 软删除就业质量记录
    await employment.update({ status: 0 });
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除就业质量记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除就业质量记录失败',
      data: null
    });
  }
};

/**
 * 导入就业质量数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.importEmployments = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '请上传文件',
        data: null
      });
    }
    
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = xlsx.utils.sheet_to_json(worksheet);
    
    if (data.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '文件内容为空',
        data: null
      });
    }
    
    // 处理数据
    const results = {
      total: data.length,
      success: 0,
      failed: 0,
      errors: []
    };
    
    for (const item of data) {
      try {
        const major = item['专业名称'];
        const year = item['年份'];
        const employmentRate = parseFloat(item['就业率'] || 0);
        const employmentIndustry = item['就业行业'] || '';
        const employmentRegion = item['就业地区'] || '';
        const averageSalary = parseFloat(item['平均薪资'] || 0);
        const majorMatchRate = parseFloat(item['专业对口率'] || 0);
        const employmentSatisfaction = parseFloat(item['就业满意度'] || 0);
        const score = parseFloat(item['评分'] || 0);
        
        if (!major || !year) {
          results.failed++;
          results.errors.push(`行 ${results.success + results.failed}: 专业名称和年份不能为空`);
          continue;
        }
        
        // 查找是否存在相同记录
        const existingRecord = await EmploymentQuality.findOne({
          where: {
            major,
            year
          }
        });
        
        if (existingRecord) {
          // 更新已存在记录
          await existingRecord.update({
            employmentRate,
            employmentIndustry,
            employmentRegion,
            averageSalary,
            majorMatchRate,
            employmentSatisfaction,
            score
          });
        } else {
          // 创建新记录
          await EmploymentQuality.create({
            id: uuidv4(),
            major,
            year,
            employmentRate,
            employmentIndustry,
            employmentRegion,
            averageSalary,
            majorMatchRate,
            employmentSatisfaction,
            score,
            status: 1
          });
        }
        
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`行 ${results.success + results.failed}: ${error.message}`);
      }
    }
    
    // 删除临时文件
    fs.unlinkSync(filePath);
    
    return res.status(200).json({
      code: 200,
      message: '导入成功',
      data: results
    });
  } catch (error) {
    console.error('导入就业质量数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导入就业质量数据失败',
      data: null
    });
  }
};

/**
 * 导出就业质量数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.exportEmployments = async (req, res) => {
  try {
    const { major, year, employmentIndustry, employmentRegion } = req.query;
    
    // 构建查询条件
    const where = { status: 1 };
    if (major) where.major = { [Op.like]: `%${major}%` };
    if (year) where.year = year;
    if (employmentIndustry) where.employmentIndustry = employmentIndustry;
    if (employmentRegion) where.employmentRegion = employmentRegion;
    
    // 查询数据
    const employments = await EmploymentQuality.findAll({
      where,
      order: [['year', 'DESC'], ['major', 'ASC']]
    });
    
    // 创建工作簿和工作表
    const data = employments.map(item => ({
      '专业名称': item.major,
      '年份': item.year,
      '就业率': item.employmentRate,
      '就业行业': item.employmentIndustry,
      '就业地区': item.employmentRegion,
      '平均薪资': item.averageSalary,
      '专业对口率': item.majorMatchRate,
      '就业满意度': item.employmentSatisfaction,
      '评分': item.score
    }));
    
    const worksheet = xlsx.utils.json_to_sheet(data);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, '就业质量数据');
    
    // 生成临时文件
    const filePath = path.join(__dirname, '../../../temp', `employment_data_${Date.now()}.xlsx`);
    
    // 确保目录存在
    const dirPath = path.dirname(filePath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // 写入文件
    xlsx.writeFile(workbook, filePath);
    
    // 发送文件
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=employment_data_${Date.now()}.xlsx`);
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
    // 删除临时文件
    fileStream.on('end', () => {
      fs.unlinkSync(filePath);
    });
  } catch (error) {
    console.error('导出就业质量数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '导出就业质量数据失败',
      data: null
    });
  }
};

/**
 * 获取就业率趋势
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getEmploymentRateTrend = async (req, res) => {
  try {
    // 获取最近5年的数据
    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 4;
    
    // 检查模型是否存在
    if (!EmploymentQuality) {
      throw new Error('EmploymentQuality 模型未定义');
    }
    
    // 直接查询 employment_quality 表 - 不依赖特定的关联表
    const query = `
      SELECT year, ROUND(AVG(employmentRate), 2) as avgEmploymentRate 
      FROM employment_quality 
      WHERE year >= ${startYear} AND status = 1 
      GROUP BY year 
      ORDER BY year ASC
    `;
    
    const [results] = await EmploymentQuality.sequelize.query(query);
    
    // 格式化返回数据
    const trendData = [];
    
    // 确保每一年都有数据
    for (let year = startYear; year <= currentYear; year++) {
      const foundItem = results.find(item => parseInt(item.year) === year);
      trendData.push({
        year: year.toString(),
        employmentRate: foundItem ? parseFloat(foundItem.avgEmploymentRate).toFixed(2) : '0.00'
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        trends: trendData,
        startYear,
        endYear: currentYear
      }
    });
  } catch (error) {
    console.error('获取就业率趋势失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取就业率趋势失败',
      data: null
    });
  }
};

/**
 * 获取就业行业分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getEmploymentIndustryDistribution = async (req, res) => {
  try {
    const { year } = req.query;
    
    // 检查模型是否存在
    if (!EmploymentQuality) {
      throw new Error('EmploymentQuality 模型未定义');
    }
    
    // 构建查询条件
    let whereClause = 'WHERE status = 1';
    if (year) {
      whereClause += ` AND year = '${year}'`;
    }
    
    // 使用原生SQL查询
    const query = `
      SELECT employmentIndustry, COUNT(id) as count
      FROM employment_quality
      ${whereClause}
      GROUP BY employmentIndustry
      ORDER BY count DESC
    `;
    
    const [results] = await EmploymentQuality.sequelize.query(query);
    
    // 格式化返回数据
    const industryData = results.map(item => ({
      industry: item.employmentIndustry || '未知',
      count: parseInt(item.count)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: industryData,
        year: year || '全部',
        total: industryData.reduce((sum, item) => sum + item.count, 0)
      }
    });
  } catch (error) {
    console.error('获取就业行业分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取就业行业分布失败',
      data: null
    });
  }
};

/**
 * 获取就业地区分布
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getEmploymentRegionDistribution = async (req, res) => {
  try {
    const { year } = req.query;
    
    // 检查模型是否存在
    if (!EmploymentQuality) {
      throw new Error('EmploymentQuality 模型未定义');
    }
    
    // 构建查询条件
    let whereClause = 'WHERE status = 1';
    if (year) {
      whereClause += ` AND year = '${year}'`;
    }
    
    // 使用原生SQL查询
    const query = `
      SELECT employmentRegion, COUNT(id) as count
      FROM employment_quality
      ${whereClause}
      GROUP BY employmentRegion
      ORDER BY count DESC
    `;
    
    const [results] = await EmploymentQuality.sequelize.query(query);
    
    // 格式化返回数据
    const regionData = results.map(item => ({
      region: item.employmentRegion || '未知',
      count: parseInt(item.count)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: regionData,
        year: year || '全部',
        total: regionData.reduce((sum, item) => sum + item.count, 0)
      }
    });
  } catch (error) {
    console.error('获取就业地区分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取就业地区分布失败',
      data: null
    });
  }
};

/**
 * 获取专业评分排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getMajorScoreRanking = async (req, res) => {
  try {
    const { year, limit = 10 } = req.query;
    
    // 检查模型是否存在
    if (!EmploymentQuality) {
      throw new Error('EmploymentQuality 模型未定义');
    }
    
    // 构建查询条件
    let whereClause = 'WHERE status = 1';
    if (year) {
      whereClause += ` AND year = '${year}'`;
    }
    
    // 使用原生SQL查询
    const query = `
      SELECT major, score
      FROM employment_quality
      ${whereClause}
      ORDER BY score DESC
      LIMIT ${parseInt(limit)}
    `;
    
    const [results] = await EmploymentQuality.sequelize.query(query);
    
    // 格式化返回数据
    const rankingData = results.map(item => ({
      major: item.major,
      score: parseFloat(item.score)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rankingData,
        year: year || '全部',
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取专业评分排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取专业评分排名失败',
      data: null
    });
  }
};

/**
 * 获取就业质量年度分析
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getEmploymentAnalysis = async (req, res) => {
  try {
    const { year } = req.query;
    
    // 查询指定年份的就业质量数据
    const employments = await EmploymentQuality.findAll({
      where: { year },
      order: [['employmentRate', 'DESC']]
    });
    
    if (employments.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          year,
          totalGraduates: 0,
          totalEmployed: 0,
          avgEmploymentRate: 0,
          avgSalary: 0,
          employmentRateRanking: [],
          salaryRanking: [],
          satisfactionRanking: [],
          departmentData: []
        }
      });
    }
    
    // 计算平均就业率
    const avgEmploymentRate = employments.reduce((sum, item) => sum + parseFloat(item.employmentRate), 0) / employments.length;
    
    // 计算平均薪资
    const avgSalary = employments.reduce((sum, item) => sum + parseFloat(item.averageSalary), 0) / employments.length;
    
    // 就业率排名
    const employmentRateRanking = employments.map(item => ({
      department: item.department,
      employmentRate: item.employmentRate
    }));
    
    // 薪资排名
    const salaryRanking = [...employments].sort((a, b) => b.averageSalary - a.averageSalary).map(item => ({
      department: item.department,
      averageSalary: item.averageSalary
    }));
    
    // 满意度排名
    const satisfactionRanking = [...employments].sort((a, b) => b.satisfactionRate - a.satisfactionRate).map(item => ({
      department: item.department,
      satisfactionRate: item.satisfactionRate
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        year,
        totalGraduates: employments.reduce((sum, item) => sum + item.graduateCount, 0),
        totalEmployed: employments.reduce((sum, item) => sum + item.employedCount, 0),
        avgEmploymentRate,
        avgSalary,
        employmentRateRanking,
        salaryRanking,
        satisfactionRanking,
        departmentData: employments
      }
    });
  } catch (error) {
    console.error('获取就业质量年度分析失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取就业质量年度分析失败',
      data: null
    });
  }
};

/**
 * 获取就业质量记录（路由别名）
 */
exports.getEmploymentRecords = exports.getEmploymentList;

/**
 * 获取个人就业质量记录
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPersonalEmployments = async (req, res) => {
  try {
    const { userId, major, year, employmentIndustry, employmentRegion, page = 1, pageSize = 10 } = req.query;
    
    console.log('=== 个人就业质量记录查询 ===');
    console.log('请求参数:', req.query);
    console.log('userId:', userId);
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: 'userId不能为空',
        data: null
      });
    }
    
    // 构建查询条件
    const where = {
      userId,
      status: 1
    };
    
    // 添加可选的过滤条件
    if (major) where.major = { [Op.like]: `%${major}%` };
    if (year) where.year = year;
    if (employmentIndustry) where.employmentIndustry = employmentIndustry;
    if (employmentRegion) where.employmentRegion = employmentRegion;
    
    console.log('查询条件:', JSON.stringify(where));
    
    // 查询个人就业质量记录
    const offset = (page - 1) * pageSize;
    const { count, rows } = await EmploymentQuality.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['year', 'DESC']]
    });
    
    console.log('查询结果数量:', count);
    console.log('=== 查询结束 ===');
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        total: count,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(count / pageSize)
      }
    });
  } catch (error) {
    console.error('获取个人就业质量记录失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取个人就业质量记录失败',
      data: null
    });
  }
};

/**
 * 获取个人就业质量统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getPersonalEmploymentStats = async (req, res) => {
  try {
    const { userId } = req.query;
    
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: 'userId不能为空',
        data: null
      });
    }
    
    // 查询个人就业质量记录
    const employments = await EmploymentQuality.findAll({
      where: {
        userId,
        status: 1
      },
      order: [['year', 'ASC']]
    });
    
    if (employments.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '暂无个人就业质量数据',
        data: {
          years: [],
          employmentRates: [],
          industries: [],
          regions: [],
          salaries: [],
          scores: []
        }
      });
    }
    
    // 处理年份和就业率
    const years = employments.map(item => item.year);
    const employmentRates = employments.map(item => parseFloat(item.employmentRate));
    
    // 处理就业行业
    const industryMap = {};
    employments.forEach(item => {
      if (item.employmentIndustry) {
        if (!industryMap[item.employmentIndustry]) {
          industryMap[item.employmentIndustry] = 0;
        }
        industryMap[item.employmentIndustry]++;
      }
    });
    
    const industries = Object.keys(industryMap).map(key => ({
      name: key,
      value: industryMap[key]
    }));
    
    // 处理就业地区
    const regionMap = {};
    employments.forEach(item => {
      if (item.employmentRegion) {
        if (!regionMap[item.employmentRegion]) {
          regionMap[item.employmentRegion] = 0;
        }
        regionMap[item.employmentRegion]++;
      }
    });
    
    const regions = Object.keys(regionMap).map(key => ({
      name: key,
      value: regionMap[key]
    }));
    
    // 处理薪资
    const salaries = employments.map(item => ({
      year: item.year,
      value: parseFloat(item.averageSalary)
    }));
    
    // 处理评分
    const scores = employments.map(item => ({
      year: item.year,
      value: parseFloat(item.score)
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        years,
        employmentRates,
        industries,
        regions,
        salaries,
        scores
      }
    });
  } catch (error) {
    console.error('获取个人就业质量统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取个人就业质量统计失败',
      data: null
    });
  }
}; 