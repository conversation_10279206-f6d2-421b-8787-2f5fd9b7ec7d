// 绩效分析模块专用样式
// 用于首页绩效分析功能的样式定义

// 绩效分析主容器
.performance-analysis {
  margin-bottom: 32px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  
  // 分析控制面板样式
  .analysis-controls {
    margin-bottom: 24px;
    
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #f0f0f0;
      background: #fff;

      .ant-card-body {
        padding: 20px;
      }
    }
    
    // 表单项样式
    .ant-form-item {
      margin-bottom: 0;
      
      .ant-form-item-label {
        padding-bottom: 4px;
        
        > label {
          font-weight: 500;
          color: #262626;
          font-size: 14px;
        }
      }
      
      .ant-select {
        border-radius: 6px;
        
        &:hover {
          border-color: #40a9ff;
        }
        
        &.ant-select-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
    
    // 操作按钮区域
    .analysis-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      
      .ant-space {
        flex-wrap: wrap;
        gap: 8px;
      }
      
      .ant-btn {
        border-radius: 6px;
        font-weight: 500;
        height: 32px;
        padding: 0 16px;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        
        .anticon {
          margin-right: 6px;
        }
        
        // 主要按钮样式
        &.ant-btn-primary {
          background: #1890ff;
          border: 1px solid #1890ff;

          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
          }

          &:active {
            background: #096dd9;
            border-color: #096dd9;
          }
        }
        
        // 普通按钮样式
        &:not(.ant-btn-primary) {
          border-color: #d9d9d9;
          background: #fff;
          
          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }

          &:active {
            border-color: #096dd9;
            color: #096dd9;
          }
        }
        
        // 加载状态
        &.ant-btn-loading {
          pointer-events: none;
          opacity: 0.7;
        }
      }
    }
  }
  
  // 图表网格布局
  .ant-row {
    margin-left: -12px;
    margin-right: -12px;
    
    .ant-col {
      padding-left: 12px;
      padding-right: 12px;
      margin-bottom: 24px;
    }
  }
  
  // 绩效卡片通用样式
  .performance-card {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    transition: all 0.3s ease;
    overflow: hidden;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
    
    // 卡片头部
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;

      .ant-card-head-title {
        font-weight: 600;
        font-size: 16px;
        color: #262626;
        padding: 16px 0;
      }
      
      .ant-card-extra {
        padding: 16px 0;
        
        .anticon {
          font-size: 16px;
          color: #999;
          cursor: help;
          transition: color 0.3s ease;
          
          &:hover {
            color: #1890ff;
          }
        }
      }
    }
    
    // 卡片内容
    .ant-card-body {
      padding: 20px;
      height: calc(100% - 64px);
      display: flex;
      flex-direction: column;
    }
  }
}

// 图表容器样式
.chart-container {
  .chart-wrapper {
    height: 400px;
    width: 100%;
    position: relative;
    flex: 1;
    
    &.echarts-container {
      canvas {
        border-radius: 4px;
      }
    }
  }
  
  // 图表加载状态
  .chart-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #999;
    
    .ant-spin {
      margin-bottom: 16px;
      
      .ant-spin-dot {
        font-size: 24px;
      }
    }
    
    > div {
      font-size: 14px;
      color: #666;
      text-align: center;
    }
  }
  
  // 图表空状态
  .chart-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #999;
    
    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
      color: #d9d9d9;
    }
    
    .empty-message {
      font-size: 14px;
      color: #999;
      text-align: center;
    }
  }
  
  // 图表底部信息
  .chart-info {
    margin-top: 16px;
    text-align: center;
    color: #666;
    font-size: 12px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #fafafa, #f5f5f5);
    border-radius: 4px;
    border-top: 1px solid #f0f0f0;
    line-height: 1.4;
  }
}

// 分析报告样式
.analysis-report {
  height: 100%;
  overflow-y: auto;
  
  .report-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #262626;
      border-left: 4px solid #1890ff;
      padding: 8px 12px;
      background: #f6ffed;
      border-radius: 4px;
      margin-left: -4px;
    }
    
    // 描述列表样式
    .ant-descriptions {
      .ant-descriptions-item {
        padding-bottom: 12px;
        
        .ant-descriptions-item-label {
          font-weight: 500;
          color: #666;
          width: 80px;
        }
        
        .ant-descriptions-item-content {
          font-weight: 500;
          color: #262626;
        }
      }
    }
    
    // 列表样式
    .ant-list {
      .ant-list-item {
        padding: 8px 0;
        border-bottom: 1px solid #f5f5f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .ant-list-item-meta {
          .ant-list-item-meta-avatar {
            margin-right: 12px;
          }
          
          .ant-list-item-meta-title {
            margin-bottom: 4px;
            font-weight: 500;
            color: #262626;
            line-height: 1.4;
          }
          
          .ant-list-item-meta-description {
            color: #666;
            font-size: 13px;
            line-height: 1.4;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .performance-analysis {
    .performance-card {
      .chart-wrapper {
        height: 320px;
      }

      .chart-loading,
      .chart-empty {
        height: 320px;
      }
    }

    .analysis-controls {
      .analysis-actions {
        .ant-btn {
          font-size: 13px;
          padding: 0 12px;
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .performance-analysis {
    .ant-row {
      .ant-col {
        margin-bottom: 20px;
      }
    }

    .analysis-controls {
      .ant-row {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .performance-analysis {
    margin-bottom: 24px;
    padding: 0 16px;

    .analysis-controls {
      .ant-card {
        .ant-card-body {
          padding: 16px;
        }
      }

      .ant-row {
        flex-direction: column;
        gap: 16px;

        .ant-col {
          width: 100% !important;
          flex: none !important;
          margin-bottom: 12px;
        }
      }

      .analysis-actions {
        justify-content: center;

        .ant-space {
          flex-wrap: wrap;
          justify-content: center;
          gap: 8px;

          .ant-btn {
            min-width: 80px;
            font-size: 12px;
            height: 28px;
            padding: 0 12px;
          }
        }
      }
    }

    .ant-row {
      margin-left: -8px;
      margin-right: -8px;

      .ant-col {
        padding-left: 8px;
        padding-right: 8px;
        margin-bottom: 16px;
      }
    }

    .performance-card {
      margin-bottom: 16px;

      .ant-card-head {
        .ant-card-head-title {
          font-size: 14px;
          padding: 12px 0;
        }

        .ant-card-extra {
          padding: 12px 0;
        }
      }

      .ant-card-body {
        padding: 16px;
        height: calc(100% - 48px);
      }

      .chart-wrapper {
        height: 280px;
      }

      .chart-loading,
      .chart-empty {
        height: 280px;

        .ant-spin {
          .ant-spin-dot {
            font-size: 20px;
          }
        }

        .empty-icon {
          font-size: 36px;
        }

        .empty-message,
        > div {
          font-size: 12px;
        }
      }

      .chart-info {
        font-size: 11px;
        padding: 6px 8px;
        margin-top: 12px;
      }
    }

    .analysis-report {
      .report-section {
        margin-bottom: 16px;

        .section-title {
          font-size: 14px;
          padding: 6px 10px;
          margin-bottom: 8px;
        }

        .ant-descriptions {
          .ant-descriptions-item {
            padding-bottom: 8px;

            .ant-descriptions-item-label {
              width: 70px;
              font-size: 12px;
            }

            .ant-descriptions-item-content {
              font-size: 12px;
            }
          }
        }

        .ant-list {
          .ant-list-item {
            padding: 6px 0;

            .ant-list-item-meta {
              .ant-list-item-meta-avatar {
                margin-right: 8px;
              }

              .ant-list-item-meta-title {
                font-size: 13px;
                margin-bottom: 2px;
              }

              .ant-list-item-meta-description {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .performance-analysis {
    padding: 0 8px;

    .analysis-controls {
      .ant-card {
        .ant-card-body {
          padding: 12px;
        }
      }

      .analysis-actions {
        .ant-space {
          .ant-btn {
            min-width: 70px;
            font-size: 11px;
            height: 26px;
            padding: 0 8px;

            .anticon {
              margin-right: 4px;
              font-size: 12px;
            }
          }
        }
      }
    }

    .performance-card {
      .ant-card-head {
        .ant-card-head-title {
          font-size: 13px;
        }
      }

      .ant-card-body {
        padding: 12px;
      }

      .chart-wrapper {
        height: 240px;
      }

      .chart-loading,
      .chart-empty {
        height: 240px;

        .empty-icon {
          font-size: 32px;
        }
      }
    }

    .analysis-report {
      .report-section {
        .section-title {
          font-size: 13px;
          padding: 4px 8px;
        }

        .ant-descriptions {
          .ant-descriptions-item {
            .ant-descriptions-item-label {
              width: 60px;
              font-size: 11px;
            }

            .ant-descriptions-item-content {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

// 工具类样式
.performance-level-excellent {
  color: #52c41a;
  font-weight: 600;
}

.performance-level-good {
  color: #1890ff;
  font-weight: 600;
}

.performance-level-average {
  color: #faad14;
  font-weight: 600;
}

.performance-level-poor {
  color: #ff4d4f;
  font-weight: 600;
}

// 移除动画效果，保持原始样式

// 图表特殊样式
.radar-chart-container {
  .chart-wrapper {
    background: #fff;
  }
}

.distribution-chart-container {
  .chart-wrapper {
    background: #fff;
  }
}

.trend-chart-container {
  .chart-wrapper {
    background: #fff;
  }
}
