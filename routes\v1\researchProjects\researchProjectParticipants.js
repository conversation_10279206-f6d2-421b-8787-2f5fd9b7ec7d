const express = require('express');
const router = express.Router();
const researchProjectParticipantsController = require('../../../controllers/v1/researchProjects/researchProjectParticipantsController');

/**
 * 获取科研项目参与者列表
 * @route POST /v1/sys/research-project-participants/list
 * @group 科研项目管理 - 科研项目参与者相关接口
 * @param {string} projectId.body - 项目ID
 * @param {string} userId.body - 用户ID
 * @param {boolean} isLeader.body - 是否是负责人
 * @param {number} page.body - 页码，默认1
 * @param {number} pageSize.body - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, page: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', researchProjectParticipantsController.getProjectParticipants);

/**
 * 获取项目参与者详情
 * @route GET /v1/sys/research-project-participants/:id
 * @group 科研项目管理 - 科研项目参与者相关接口
 * @param {string} id.path.required - 参与者ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/:id', researchProjectParticipantsController.getProjectParticipantDetail);

/**
 * 创建项目参与者
 * @route POST /v1/sys/research-project-participants
 * @group 科研项目管理 - 科研项目参与者相关接口
 * @param {string} projectId.body.required - 项目ID
 * @param {string} userId.body.required - 用户ID
 * @param {number} allocationRatio.body.required - 分配比例（0-1之间）
 * @param {number} participantRank.body - 参与者排名
 * @param {boolean} isLeader.body - 是否是负责人，默认false
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/', researchProjectParticipantsController.createProjectParticipant);

/**
 * 更新项目参与者
 * @route PUT /v1/sys/research-project-participants/:id
 * @group 科研项目管理 - 科研项目参与者相关接口
 * @param {string} id.path.required - 参与者ID
 * @param {number} allocationRatio.body - 分配比例（0-1之间）
 * @param {number} participantRank.body - 参与者排名
 * @param {boolean} isLeader.body - 是否是负责人
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/:id', researchProjectParticipantsController.updateProjectParticipant);

/**
 * 删除项目参与者
 * @route DELETE /v1/sys/research-project-participants/:id
 * @group 科研项目管理 - 科研项目参与者相关接口
 * @param {string} id.path.required - 参与者ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/:id', researchProjectParticipantsController.deleteProjectParticipant);

/**
 * 批量添加项目参与者
 * @route POST /v1/sys/research-project-participants/batch
 * @group 科研项目管理 - 科研项目参与者相关接口
 * @param {string} projectId.body.required - 项目ID
 * @param {Array} participants.body.required - 参与者数组，每个元素包含userId, allocationRatio, participantRank, isLeader
 * @returns {object} 200 - {code: 200, message: "批量添加完成", data: {success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/batch', researchProjectParticipantsController.batchAddProjectParticipants);

module.exports = router; 