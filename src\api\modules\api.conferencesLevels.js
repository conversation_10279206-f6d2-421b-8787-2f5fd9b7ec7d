import request from '../server'

/**
 * 获取会议级别列表
 * @returns {Promise} 响应结果
 */
export function getConferenceLevels() {
  return request.get('/conferencesLevels/levels')
}

/**
 * 获取会议级别及会议数量
 * @returns {Promise} 响应结果
 */
export function getLevelsWithCount() {
  return request.get('/conferencesLevels/levels-with-count')
}

/**
 * 获取会议级别详情
 * @param {String} id - 级别ID
 * @returns {Promise} 响应结果
 */
export function getConferenceLevelDetail(id) {
  return request.get(`/conferencesLevels/level/${id}`)
}

/**
 * 创建会议级别
 * @param {Object} data - 级别数据
 * @returns {Promise} 响应结果
 */
export function createConferenceLevel(data) {
  return request.post('/conferencesLevels/level/create', data || {})
}

/**
 * 更新会议级别
 * @param {String} id - 级别ID
 * @param {Object} data - 级别数据
 * @returns {Promise} 响应结果
 */
export function updateConferenceLevel(id, data) {
  return request.post('/conferencesLevels/level/update', { id, ...data })
}

/**
 * 删除会议级别
 * @param {String} id - 级别ID
 * @returns {Promise} 响应结果
 */
export function deleteConferenceLevel(id) {
  return request.post('/conferencesLevels/level/delete', { id })
}

/**
 * 获取会议级别分布数据
 * @param {Object} data - 请求参数
 * @param {String} data.range - 数据范围: 'in', 'out', 'all'
 * @param {String} data.userId - 用户ID，可选，用于过滤特定用户的数据
 * @returns {Promise} 响应结果
 */
export function getLevelDistribution(data) {
  return request.post('/conferencesLevels/statistics/distribution', data || {})
}