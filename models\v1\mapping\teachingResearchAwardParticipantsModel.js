const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const teachingResearchAwardParticipantsModel = sequelize.define(
  'teaching_research_award_participants',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '参与者记录ID'
    },
    awardId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '奖励ID（外键）'
    },
    participantId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '参与者ID（外键，关联user.id）'
    },
    employeeNumber: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '人事编号'
    },
    allocationRatio: {
      type: DataTypes.DECIMAL(4, 2),
      allowNull: false,
      comment: '分配比例（0.00-1.00）'
    },
    isLeader: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否第一负责人'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '更新时间'
    }
  },
  {
    sequelize,
    modelName: 'teaching_research_award_participants',
    tableName: 'teaching_research_award_participants',
    timestamps: true
  }
);

// 在文件加载后添加关联，避免循环依赖问题
const setupAssociations = () => {
  const userModel = require('./userModel');
  const teachingResearchAwardsModel = require('./teachingResearchAwardsModel');

  // 与用户表关联
  teachingResearchAwardParticipantsModel.belongsTo(userModel, {
    foreignKey: 'participantId',
    as: 'participant'
  });

  // 与奖励表关联
  teachingResearchAwardParticipantsModel.belongsTo(teachingResearchAwardsModel, {
    foreignKey: 'awardId',
    as: 'award'
  });
};

// 导出模型时调用关联设置
setTimeout(setupAssociations, 0);

module.exports = teachingResearchAwardParticipantsModel;
