const { verifyToken } = require('./authMiddleware');
const { errorHandler } = require('./errorMiddleware');
const { logger, requestLogger } = require('./loggerMiddleware');
const { logMiddleware } = require('./logMiddleware');
const { validateRequest } = require('./validatorMiddleware');
const { checkPermissions, checkRoles, checkAccess, checkAccessAndData } = require('./permissionMiddleware');
const { upload } = require('./uploadMiddleware');
const { createCache } = require('./cacheMiddleware');
const { createSession } = require('./sessionMiddleware');
const { createSecurity } = require('./securityMiddleware');
const { createCompression } = require('./compressionMiddleware');
const { createBodyParser } = require('./bodyParserMiddleware');
const { corsMiddleware } = require('./corsMiddleware');
const { formatResponse } = require('./responseMiddleware');
const { createRateLimiter } = require('./rateLimitMiddleware');
const { actionRecords } = require('./actionLogMiddleware');
const {
  validateCreateExchange,
  validateUpdateExchange,
  validateDeleteExchange,
  validatePersonalStats
} = require('./internationalExchangeValidatorMiddleware');
const createModulePermission = require('./modulePermission');

console.log('modulePermission loaded:', !!createModulePermission);

/**
 * 创建科研项目模块的权限中间件
 * @param {string} action - 操作名称 (list, create, update, delete 等)
 * @returns {Function} Express中间件函数
 */
const researchPermission = (action) => {
  console.log(`正在创建科研项目模块的权限中间件，操作: ${action}`);
  const middleware = createModulePermission('researchProjects', action);
  return (req, res, next) => {
    console.log(`正在执行科研项目模块权限检查，操作: ${action}`);
    return middleware(req, res, next);
  };
};

/**
 * 创建高水平论文模块的权限中间件
 * 仅示例，需要在permissions目录下添加相应配置文件
 */
const papersPermission = (action) => {
  console.log(`正在创建高水平论文模块的权限中间件，操作: ${action}`);
  const middleware = createModulePermission('highLevelPapers', action);
  return (req, res, next) => {
    console.log(`正在执行高水平论文模块权限检查，操作: ${action}`);
    return middleware(req, res, next);
  };
};

/**
 * 通用模块权限中间件生成器
 * @param {string} module - 模块名称 
 * @param {string} action - 操作名称
 * @returns {Function} Express中间件函数
 */
const modulePermission = (module, action) => {
  console.log(`正在创建${module}模块的权限中间件，操作: ${action}`);
  const middleware = createModulePermission(module, action);
  return (req, res, next) => {
    console.log(`正在执行${module}模块权限检查，操作: ${action}`);
    return middleware(req, res, next);
  };
};

module.exports = {
  // 身份验证
  verifyToken,
  // 错误处理
  errorHandler,
  // 系统日志
  logger,
  requestLogger,
  // 操作日志
  logMiddleware,
  actionRecords,
  // 请求验证
  validateRequest,
  // 权限检查
  checkPermissions,
  checkRoles,
  checkAccess,
  checkAccessAndData,
  // 文件上传
  upload,
  // 缓存
  createCache,
  // 会话
  createSession,
  // 安全
  createSecurity,
  // 压缩
  createCompression,
  // 请求体解析
  createBodyParser,
  // 跨域
  corsMiddleware,
  // 响应格式化
  formatResponse,
  // 限流
  createRateLimiter,
  // 国际交流验证
  validateCreateExchange,
  validateUpdateExchange,
  validateDeleteExchange,
  validatePersonalStats,
  // 模块权限中间件
  researchPermission,
  papersPermission,
  modulePermission
}; 