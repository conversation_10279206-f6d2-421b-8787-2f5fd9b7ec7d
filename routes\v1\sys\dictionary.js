const express = require('express');
const dictionaryController = require('../../../controllers/v1/sys/dictionaryController');

const router = express.Router();

/**
 * 获取字典类型列表
 * @route POST /v1/sys/dictionary/type/list
 * @group 字典管理 - 系统字典相关接口
 * @param {number} page.body - 页码，默认1
 * @param {number} limit.body - 每页数量，默认10
 * @param {string} name.body - 类型名称（模糊搜索）
 * @param {string} code.body - 类型编码（模糊搜索）
 * @param {number} status.body - 状态（0-停用，1-启用）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/type/list', dictionaryController.getDictionaryTypes);

/**
 * 获取指定字典类型
 * @route POST /v1/sys/dictionary/type/detail
 * @group 字典管理 - 系统字典相关接口
 * @param {string} id.body.required - 类型ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {类型详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/type/detail', dictionaryController.getDictionaryTypeById);

/**
 * 创建字典类型
 * @route POST /v1/sys/dictionary/type/create
 * @group 字典管理 - 系统字典相关接口
 * @param {string} name.body.required - 类型名称
 * @param {string} code.body.required - 类型编码
 * @param {string} description.body - 类型描述
 * @param {number} status.body - 状态（0-停用，1-启用），默认1
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "类型ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/type/create', dictionaryController.createDictionaryType);

/**
 * 更新字典类型
 * @route POST /v1/sys/dictionary/type/update
 * @group 字典管理 - 系统字典相关接口
 * @param {string} id.body.required - 类型ID
 * @param {string} name.body - 类型名称
 * @param {string} code.body - 类型编码
 * @param {string} description.body - 类型描述
 * @param {number} status.body - 状态（0-停用，1-启用）
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/type/update', dictionaryController.updateDictionaryType);

/**
 * 删除字典类型
 * @route POST /v1/sys/dictionary/type/delete
 * @group 字典管理 - 系统字典相关接口
 * @param {string} id.body.required - 类型ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/type/delete', dictionaryController.deleteDictionaryType);

/**
 * 获取字典数据列表
 * @route POST /v1/sys/dictionary/data/list
 * @group 字典管理 - 系统字典相关接口
 * @param {string} typeId.body.required - 字典类型ID
 * @param {number} page.body - 页码，默认1
 * @param {number} limit.body - 每页数量，默认10
 * @param {string} label.body - 标签（模糊搜索）
 * @param {string} value.body - 值（模糊搜索）
 * @param {number} status.body - 状态（0-停用，1-启用）
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/data/list', dictionaryController.getDictionaryData);

/**
 * 获取指定字典数据
 * @route POST /v1/sys/dictionary/data/detail
 * @group 字典管理 - 系统字典相关接口
 * @param {string} id.body.required - 数据ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {数据详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/data/detail', dictionaryController.getDictionaryDataById);

/**
 * 创建字典数据
 * @route POST /v1/sys/dictionary/data/create
 * @group 字典管理 - 系统字典相关接口
 * @param {string} typeId.body.required - 字典类型ID
 * @param {string} label.body.required - 标签
 * @param {string} value.body.required - 值
 * @param {string} description.body - 描述
 * @param {number} sort.body - 排序
 * @param {number} status.body - 状态（0-停用，1-启用），默认1
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "数据ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/data/create', dictionaryController.createDictionaryData);

/**
 * 更新字典数据
 * @route POST /v1/sys/dictionary/data/update
 * @group 字典管理 - 系统字典相关接口
 * @param {string} id.body.required - 数据ID
 * @param {string} label.body - 标签
 * @param {string} value.body - 值
 * @param {string} description.body - 描述
 * @param {number} sort.body - 排序
 * @param {number} status.body - 状态（0-停用，1-启用）
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/data/update', dictionaryController.updateDictionaryData);

/**
 * 删除字典数据
 * @route POST /v1/sys/dictionary/data/delete
 * @group 字典管理 - 系统字典相关接口
 * @param {string} id.body.required - 数据ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/data/delete', dictionaryController.deleteDictionaryData);

module.exports = router; 