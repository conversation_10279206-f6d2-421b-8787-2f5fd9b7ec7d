const express = require('express');
const router = express.Router();
const teachingWorkloadParticipantsController = require('../../../controllers/v1/teachingWorkloads/teachingWorkloadParticipantsController');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建教学工作量参与者模块的权限中间件
const participantsPermission = (action) => createModulePermission('teachingWorkloads', action);

/**
 * 获取教学工作量参与者列表
 * @route GET /v1/sys/teaching-workloads/participants
 * @group 教学工作量参与者管理 - 教学工作量参与者相关接口
 * @param {string} workloadId.query - 工作量ID
 * @param {string} participantName.query - 参与者姓名（模糊搜索）
 * @param {number} page.query - 页码，默认1
 * @param {number} pageSize.query - 每页条数，默认10
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/participants',
  authMiddleware,
  participantsPermission('list'),
  teachingWorkloadParticipantsController.getParticipants
);

/**
 * 创建教学工作量参与者
 * @route POST /v1/sys/teaching-workloads/participant/create
 * @group 教学工作量参与者管理 - 教学工作量参与者相关接口
 * @param {string} workloadId.body.required - 工作量ID
 * @param {string} participantId.body.required - 参与者ID
 * @param {string} employeeNumber.body - 人事编号
 * @param {number} allocationRatio.body.required - 分配比例
 * @param {boolean} isLeader.body - 是否主讲教师
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/participant/create',
  authMiddleware,
  participantsPermission('create'),
  teachingWorkloadParticipantsController.createParticipant
);

/**
 * 获取教学工作量参与者详情
 * @route GET /v1/sys/teaching-workloads/participant/detail/:id
 * @group 教学工作量参与者管理 - 教学工作量参与者相关接口
 * @param {string} id.path.required - 参与者ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.get('/participant/detail/:id',
  authMiddleware,
  participantsPermission('detail'),
  teachingWorkloadParticipantsController.getParticipantDetail
);

/**
 * 更新教学工作量参与者
 * @route PUT /v1/sys/teaching-workloads/participant/update/:id
 * @group 教学工作量参与者管理 - 教学工作量参与者相关接口
 * @param {string} id.path.required - 参与者ID
 * @param {string} employeeNumber.body - 人事编号
 * @param {number} allocationRatio.body - 分配比例
 * @param {boolean} isLeader.body - 是否主讲教师
 * @returns {object} 200 - {code: 200, message: "更新成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.put('/participant/update/:id',
  authMiddleware,
  participantsPermission('update'),
  teachingWorkloadParticipantsController.updateParticipant
);

/**
 * 删除教学工作量参与者
 * @route DELETE /v1/sys/teaching-workloads/participant/delete/:id
 * @group 教学工作量参与者管理 - 教学工作量参与者相关接口
 * @param {string} id.path.required - 参与者ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/participant/delete/:id',
  authMiddleware,
  participantsPermission('delete'),
  teachingWorkloadParticipantsController.deleteParticipant
);

module.exports = router;
