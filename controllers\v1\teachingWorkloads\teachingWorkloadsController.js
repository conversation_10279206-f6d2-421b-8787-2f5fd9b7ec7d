const { v4: uuidv4 } = require('uuid');
const teachingWorkloadsModel = require('../../../models/v1/mapping/teachingWorkloadsModel');
const teachingWorkloadParticipantsModel = require('../../../models/v1/mapping/teachingWorkloadParticipantsModel');
const teachingWorkloadLevelsModel = require('../../../models/v1/mapping/teachingWorkloadLevelsModel');
const userModel = require('../../../models/v1/mapping/userModel');
const multer = require('multer');
const { Op } = require('sequelize');
const fileController = require('../common/fileController');
const { getTimeIntervalByName, getUserInfoFromRequest, getSequelizeInstance } = require('../../../utils/others');
const upload = multer({ dest: 'uploads/' });
const { updateUserRankings, RANKING_TYPE_MAPPINGS } = require('../../../utils/rankingUtils');
const userRankingReviewedInModel = require('../../../models/v1/mapping/userRankingReviewedInModel');
const userRankingReviewedOutModel = require('../../../models/v1/mapping/userRankingReviewedOutModel');
const userRankingReviewedAllModel = require('../../../models/v1/mapping/userRankingReviewedAllModel');

/**
 * 获取教学工作量列表（统一接口）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getWorkloads = async (req, res) => {
  try {
    const {
      courseName,
      semester,
      studentLevel,
      courseType,
      teacherId,
      startDate,
      endDate,
      userId,
      page = 1,
      pageSize = 10,
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'all'(全部), 'reviewed'(已审核), 'rejected'(拒绝), 'pending'(待审核)
      isExport = false // 是否是导出操作
    } = req.body;
    console.log("req.body===", req.body);

    // 构建查询条件
    const where = {};

    if (courseName) {
      where.courseName = { [Op.like]: `%${courseName}%` };
    }

    if (semester) {
      where.semester = semester;
    }

    if (studentLevel) {
      where.studentLevel = studentLevel;
    }

    if (courseType) {
      where.courseType = courseType;
    }

    if (teacherId) {
      where.teacherId = teacherId;
    }

    // 在这里添加时间区间检查逻辑
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingWorkloads");

    // 根据统计范围筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        where.createdAt = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        where[Op.or] = [
          { createdAt: { [Op.lt]: intervalStartTime } },
          { createdAt: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    // 审核状态筛选
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      where.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null;
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if (userInfo.role && (userInfo.role.roleAuth === 'TEACHER-LV1')) {
      // 通过参与者表查询该用户参与的工作量
      const participantWorkloadIds = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userInfo.id },
        attributes: ['workloadId']
      });

      const workloadIds = participantWorkloadIds.map(p => p.workloadId);
      console.log("workloadIds123===", workloadIds);
      if (workloadIds.length > 0) {
        where.id = { [Op.in]: workloadIds };
      } else {
        // 如果用户没有参与任何工作量，返回空结果
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: {
            list: [],
            pagination: {
              total: 0,
              page: parseInt(page),
              pageSize: parseInt(pageSize),
              totalPages: 0
            }
          }
        });
      }
    }

    // 如果指定了userId参数，则通过参与者表查询该用户参与的工作量
    if (userId) {
      const participantWorkloadIds = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userId },
        attributes: ['workloadId']
      });

      const workloadIds = participantWorkloadIds.map(p => p.workloadId);
      console.log("workloadIds===", workloadIds);
      console.log("where===", where);
      console.log("userId===", userId);
      if (workloadIds.length > 0) {
        where.id = { [Op.in]: workloadIds };
      } else {
        // 如果指定用户没有参与任何工作量，返回空结果
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: {
            list: [],
            pagination: {
              total: 0,
              page: parseInt(page),
              pageSize: parseInt(pageSize),
              totalPages: 0
            }
          }
        });
      }
    }

    // 分页参数
    const offset = (page - 1) * pageSize;
    const limit = isExport ? null : parseInt(pageSize);

    // 查询工作量列表
    const { count, rows } = await teachingWorkloadsModel.findAndCountAll({
      where,
      include: [
        {
          model: userModel,
          as: 'teacher',
          attributes: ['id', 'nickName', 'studentNumber'],
          required: false
        },
        {
          model: teachingWorkloadLevelsModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false
        },
        {
          model: teachingWorkloadParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'participant',
              attributes: ['id', 'nickName', 'studentNumber'],
              required: false
            }
          ],
          required: false
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickName'],
          required: false
        }
      ],
      offset: isExport ? 0 : offset,
      limit,
      order: [['createdAt', 'DESC']]
    });
    console.log("rows===", rows);

    // 处理返回数据
    const workloadList = rows.map(workload => {
      const workloadJson = workload.toJSON();

      // 处理参与者信息
      if (workloadJson.participants && workloadJson.participants.length > 0) {
        workloadJson.participantsList = workloadJson.participants.map(p => ({
          id: p.participant?.id,
          name: p.participant?.nickName,
          studentNumber: p.participant?.studentNumber,
          allocationRatio: p.allocationRatio * 100, // 将小数转换为百分比
          isLeader: p.isLeader
        }));
      } else {
        workloadJson.participantsList = [];
      }

      // 删除原始participants数组
      delete workloadJson.participants;

      return workloadJson;
    });

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: workloadList,
        pagination: {
          total: count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });

  } catch (error) {
    console.error('获取教学工作量列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教学工作量列表失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 创建教学工作量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createWorkload = async (req, res) => {
  const transaction = await teachingWorkloadsModel.sequelize.transaction();

  try {
    const {
      semester,
      courseName,
      studentLevel,
      courseType,
      courseNature,
      isNewCourse,
      studentCount,
      teachingHours,
      categoryId,
      department,
      teacherId,
      participants,
      remark,
      status,
      userId,
      fileIds, // 接收前端传递的文件ID数组
      attachmentUrl // 接收前端传递的文件路径
    } = req.body;
    console.log('创建教学工作量请求参数:', req.query);
    console.log('创建教学工作量请求参数body:', req.body);

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 验证必填字段
    if (!semester || !courseName || !studentLevel || !courseType || !studentCount) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '请填写所有必填字段',
        data: null
      });
    }

    // 转换中文值为数据库对应的英文值
    const studentLevelMap = {
      '本科生': 'undergraduate',
      '研究生': 'graduate'
    };

    const courseTypeMap = {
      '理论课': 'theory',
      '实验课': 'experiment',
      '实践课': 'practice',
      '课程设计': 'design',
      '毕业论文': 'thesis'
    };

    const courseNatureMap = {
      '必修课': 'required',
      '选修课': 'elective'
    };

    // 转换字段值
    const mappedStudentLevel = studentLevelMap[studentLevel] || studentLevel;
    const mappedCourseType = courseTypeMap[courseType] || courseType;
    const mappedCourseNature = courseNatureMap[courseNature] || courseNature;

    // 创建工作量记录
    const workload = await teachingWorkloadsModel.create({
      id: uuidv4(),
      semester,
      courseName,
      studentLevel: mappedStudentLevel,
      courseType: mappedCourseType,
      courseNature: mappedCourseNature || 'required',
      isNewCourse: isNewCourse || false,
      studentCount,
      teachingHours,
      categoryId: categoryId || null, // 空字符串转换为null
      department,
      teacherId: teacherId || userInfo.id,
      remark,
      status: status !== undefined ? status : 1,
      attachmentUrl: Array.isArray(attachmentUrl) ? attachmentUrl.join(',') : attachmentUrl
    }, { transaction });

    // 处理参与者信息
    if (participants && participants.length > 0) {
      const participantData = participants.map(participant => ({
        id: uuidv4(),
        workloadId: workload.id,
        participantId: participant.userId,
        employeeNumber: participant.employeeNumber,
        allocationRatio: participant.allocationRatio / 100, // 将百分比转换为小数
        isLeader: participant.isLeader || false
      }));

      await teachingWorkloadParticipantsModel.bulkCreate(participantData, { transaction });
    }

    // 处理文件关联
    if (fileIds && fileIds.length > 0) {
      await fileController.associateFilesWithRecord(fileIds, workload.id, 'teaching_workloads', transaction);
    }

    await transaction.commit();

    console.log(`✅ 创建成功: 工作量ID ${workload.id}`);

    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: workload
    });

  } catch (error) {
    await transaction.rollback();
    console.error('❌ 创建教学工作量失败:', error);
    return res.status(500).json({
      code: 500,
      message: `创建教学工作量失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取教学工作量详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getWorkloadDetail = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少工作量ID',
        data: null
      });
    }

    // 查询工作量详情
    const workload = await teachingWorkloadsModel.findByPk(id, {
      include: [
        {
          model: userModel,
          as: 'teacher',
          attributes: ['id', 'nickName', 'studentNumber'],
          required: false
        },
        {
          model: teachingWorkloadLevelsModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false
        },
        {
          model: teachingWorkloadParticipantsModel,
          as: 'participants',
          include: [
            {
              model: userModel,
              as: 'participant',
              attributes: ['id', 'nickName', 'studentNumber'],
              required: false
            }
          ],
          required: false
        },
        {
          model: userModel,
          as: 'reviewer',
          attributes: ['id', 'nickName'],
          required: false
        }
      ]
    });

    if (!workload) {
      return res.status(404).json({
        code: 404,
        message: '工作量不存在',
        data: null
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 权限控制：TEACHER-LV1角色只能查看自己参与的工作量
    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {
      const isParticipant = workload.participants?.some(p => p.participantId === userInfo.id);
      if (!isParticipant) {
        return res.status(403).json({
          code: 403,
          message: '权限不足，您只能查看自己参与的工作量',
          data: null
        });
      }
    }

    const workloadJson = workload.toJSON();

    // 处理参与者信息
    if (workloadJson.participants && workloadJson.participants.length > 0) {
      workloadJson.participantsList = workloadJson.participants.map(p => ({
        id: p.participant?.id,
        name: p.participant?.nickName,
        studentNumber: p.participant?.studentNumber,
        allocationRatio: p.allocationRatio * 100, // 将小数转换为百分比
        isLeader: p.isLeader
      }));
    } else {
      workloadJson.participantsList = [];
    }

    // 删除原始participants数组
    delete workloadJson.participants;

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: workloadJson
    });

  } catch (error) {
    console.error('获取教学工作量详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教学工作量详情失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 更新教学工作量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateWorkload = async (req, res) => {
  const transaction = await teachingWorkloadsModel.sequelize.transaction();

  try {
    const { id } = req.params;

    const {
      workloadId,
      semester,
      courseName,
      studentLevel,
      courseType,
      courseNature,
      isNewCourse,
      studentCount,
      teachingHours,
      categoryId,
      department,
      teacherId,
      participants,
      remark,
      attachmentUrl,
      status,
      fileIds // 添加文件ID参数
    } = req.body;
    console.log('更新教学工作量请求参数:', req.body);
    console.log('更新教学工作量请求参数:', req.query);

    // 转换中文值为数据库对应的英文值
    const studentLevelMap = {
      '本科生': 'undergraduate',
      '研究生': 'graduate'
    };

    const courseTypeMap = {
      '理论课': 'theory',
      '实验课': 'experiment',
      '实践课': 'practice',
      '课程设计': 'design',
      '毕业论文': 'thesis'
    };

    const courseNatureMap = {
      '必修课': 'required',
      '选修课': 'elective'
    };

    // 转换字段值
    const mappedStudentLevel = studentLevelMap[studentLevel] || studentLevel;
    const mappedCourseType = courseTypeMap[courseType] || courseType;
    const mappedCourseNature = courseNatureMap[courseNature] || courseNature;

    // 查询工作量是否存在
    const workload = await teachingWorkloadsModel.findByPk(id, {
      include: [
        {
          model: teachingWorkloadParticipantsModel,
          as: 'participants',
          required: false
        },
        {
          model: teachingWorkloadLevelsModel,
          as: 'category',
          required: false
        }
      ],
      transaction
    });

    if (!workload) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '工作量不存在',
        data: null
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 权限控制：TEACHER-LV1角色只能修改自己参与的工作量
    if ((userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') || !['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      const isParticipant = workload.participants?.some(p => p.participantId === userInfo.id);
      if (!isParticipant) {
        await transaction.rollback();
        return res.status(403).json({
          code: 403,
          message: '权限不足，您只能修改自己参与的工作量',
          data: null
        });
      }
    }

    // 更新工作量基本信息
    await workload.update({
      workloadId,
      semester,
      courseName,
      studentLevel: mappedStudentLevel,
      courseType: mappedCourseType,
      courseNature: mappedCourseNature,
      isNewCourse,
      studentCount,
      teachingHours,
      categoryId: categoryId || null, // 空字符串转换为null
      department,
      teacherId,
      remark,
      status,
      attachmentUrl: Array.isArray(attachmentUrl) ? attachmentUrl.join(',') : attachmentUrl
    }, { transaction });

    // 更新参与者信息
    if (participants) {
      // 删除原有参与者
      await teachingWorkloadParticipantsModel.destroy({
        where: { workloadId: id },
        transaction
      });

      // 添加新的参与者
      if (participants.length > 0) {
        const participantData = participants.map(participant => ({
          id: uuidv4(),
          workloadId: id,
          participantId: participant.userId,
          employeeNumber: participant.employeeNumber,
          allocationRatio: participant.allocationRatio / 100, // 将百分比转换为小数
          isLeader: participant.isLeader || false
        }));

        await teachingWorkloadParticipantsModel.bulkCreate(participantData, { transaction });
      }
    }

    // 处理文件关联
    if (fileIds && fileIds.length > 0) {
      await fileController.associateFilesWithRecord(fileIds, id, 'teaching_workloads', transaction);
    }

    // 如果工作量已审核，需要更新用户排名
    if (workload.ifReviewer === 1) {
      try {
        console.log('开始更新用户排名数据，工作量ID:', id);

        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("teachingWorkloads");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));

        // 判断工作量变更前后是否在时间区间内（使用创建时间判断）
        const oldCreatedAt = workload.createdAt;
        const wasInTimeRange = timeInterval ?
          isProjectInTimeRange(oldCreatedAt, timeInterval.startTime, timeInterval.endTime) :
          false;

        const isInTimeRange = timeInterval ?
          isProjectInTimeRange(workload.createdAt, timeInterval.startTime, timeInterval.endTime) :
          false;

        console.log('工作量时间范围状态 - 原始:', wasInTimeRange ? '在范围内' : '不在范围内',
          '变更后:', isInTimeRange ? '在范围内' : '不在范围内');

        // 确定需要更新的排名表（针对减分操作）
        let oldRankingTables = [];
        if (wasInTimeRange) {
          oldRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          oldRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }

        // 确定需要更新的排名表（针对加分操作）
        let newRankingTables = [];
        if (isInTimeRange) {
          newRankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          newRankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }

        console.log('排名表 - 原始:', oldRankingTables, '新:', newRankingTables);

        // 获取旧的级别分数
        const oldBaseScore = workload.category ? workload.category.score : 0;
        console.log('原始级别分数:', oldBaseScore);

        // 获取新的级别分数
        let newBaseScore = oldBaseScore;
        if (categoryId && categoryId !== workload.categoryId) {
          const newCategory = await teachingWorkloadLevelsModel.findByPk(categoryId, { transaction });
          if (newCategory) {
            newBaseScore = newCategory.score || 0;
          }
        }
        console.log('新级别分数:', newBaseScore);

        // 获取旧的参与者名单
        const oldParticipants = workload.participants || [];
        console.log('原始参与者数量:', oldParticipants.length);

        // 准备新的参与者列表
        let newParticipants = [];

        // 如果提供了新的参与者信息
        if (participants && Array.isArray(participants)) {
          newParticipants = participants.map(p => ({
            userId: p.userId,
            allocationRatio: parseFloat(p.allocationRatio) / 100, // 将百分比转换为小数
            isLeader: p.isLeader || false
          }));
        }
        console.log('新参与者数量:', newParticipants.length);

        // 从原始参与者中找出要删除的参与者 - 他们在旧列表中但不在新列表中
        const oldUserIds = oldParticipants.map(p => p.participantId);
        const newUserIds = newParticipants.map(p => p.userId);

        // 找出要删除的参与者
        const deletedUserIds = oldUserIds.filter(id => !newUserIds.includes(id));
        console.log('要删除的参与者:', deletedUserIds);

        // 找出保留的参与者
        const keptUserIds = oldUserIds.filter(id => newUserIds.includes(id));
        console.log('保留的参与者:', keptUserIds);

        // 找出新增的参与者
        const addedUserIds = newUserIds.filter(id => !oldUserIds.includes(id));
        console.log('新增的参与者:', addedUserIds);

        // 1. 处理要删除的参与者 - 减少工作量数量和分数
        if (deletedUserIds.length > 0) {
          const deletedParticipants = oldParticipants.filter(p => deletedUserIds.includes(p.participantId));
          console.log('被删除的参与者完整数据:', JSON.stringify(deletedParticipants));

          const deletedIds = [];
          const deletedRatios = [];
          const deletedScores = [];
          const countDeltas = []; // 固定值为1的数组表示每人减少1个工作量

          for (const participant of deletedParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;

            deletedIds.push(userId);
            deletedRatios.push(ratio);
            deletedScores.push(participantScore);
            countDeltas.push(1); // 每个被删除的参与者工作量数-1
          }

          console.log('被删除参与者的排名更新数据:', {
            userIds: deletedIds,
            countDeltas: countDeltas,
            scores: deletedScores
          });

          // 对每个排名表执行减分操作
          for (const table of oldRankingTables) {
            console.log(`为被删除的参与者更新排名表 ${table}`);
            await updateUserRankings(
              deletedIds,
              table,
              'teachingWorkloads',
              countDeltas, // 使用固定值1表示工作量计数-1
              deletedScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去被删除参与者的分数和工作量数');
        }

        // 2. 处理保留但分配比例变化的参与者 - 先减去原有分数，后面再加上新分数
        if (keptUserIds.length > 0) {
          const keptOldParticipants = oldParticipants.filter(p => keptUserIds.includes(p.participantId));
          console.log('保留的参与者原始数据:', JSON.stringify(keptOldParticipants));

          const keptIds = [];
          const keptRatios = [];
          const keptScores = [];

          for (const participant of keptOldParticipants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = oldBaseScore * ratio;

            keptIds.push(userId);
            keptRatios.push(ratio);
            keptScores.push(participantScore);
          }

          console.log('保留参与者的减分数据:', {
            userIds: keptIds,
            scores: keptScores
          });

          // 减去原有分数（但不减少工作量计数）
          for (const table of oldRankingTables) {
            console.log(`为保留的参与者减去原有分数：${table}`);
            // 传递0表示不改变工作量计数，只改变分数
            const zeroCounts = Array(keptIds.length).fill(0);
            await updateUserRankings(
              keptIds,
              table,
              'teachingWorkloads',
              zeroCounts, // 工作量计数不变
              keptScores,
              transaction,
              "subtract" // 减分操作
            );
          }
          console.log('成功从排名表中减去保留参与者的原有分数');
        }

        // 3. 处理所有新参与者名单（包括保留的和新增的）- 增加分数，对新增的也增加工作量数
        if (newParticipants.length > 0) {
          console.log('新参与者完整数据:', JSON.stringify(newParticipants));

          const allNewIds = [];
          const allNewRatios = [];
          const allNewScores = [];
          const allCountDeltas = []; // 对新增参与者设为1，对保留的参与者设为0

          for (const participant of newParticipants) {
            const userId = participant.userId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = newBaseScore * ratio;

            allNewIds.push(userId);
            allNewRatios.push(ratio);
            allNewScores.push(participantScore);

            // 对新增参与者工作量数+1，对保留的参与者工作量数不变
            if (addedUserIds.includes(userId)) {
              allCountDeltas.push(1);
            } else {
              allCountDeltas.push(0);
            }
          }

          console.log('所有新参与者的加分数据:', {
            userIds: allNewIds,
            countDeltas: allCountDeltas,
            scores: allNewScores
          });

          // 为所有参与者添加分数，但只为新增参与者增加工作量计数
          for (const table of newRankingTables) {
            console.log(`为所有参与者更新排名表：${table}`);
            await updateUserRankings(
              allNewIds,
              table,
              'teachingWorkloads',
              allCountDeltas, // 使用差异化的计数更新：新增的+1，保留的不变
              allNewScores,
              transaction,
              "add" // 加分操作
            );
          }
          console.log('成功更新所有参与者的分数和新增参与者的工作量数');
        }

        console.log('成功完成教学工作量参与者的排名数据更新');
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }

    await transaction.commit();

    console.log(`✅ 更新成功: 工作量ID ${id}`);

    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: null
    });

  } catch (error) {
    await transaction.rollback();
    console.error('❌ 更新教学工作量失败:', error);
    return res.status(500).json({
      code: 500,
      message: `更新教学工作量失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 删除教学工作量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteWorkload = async (req, res) => {
  const transaction = await teachingWorkloadsModel.sequelize.transaction();

  try {
    const { id } = req.params;

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少工作量ID',
        data: null
      });
    }

    // 查询工作量是否存在
    const workload = await teachingWorkloadsModel.findByPk(id, {
      include: [
        {
          model: teachingWorkloadLevelsModel,
          as: 'category',
          required: false
        }
      ],
      transaction
    });

    if (!workload) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '工作量不存在',
        data: null
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 获取参与者信息（用于权限检查和排名更新）
    const participants = await teachingWorkloadParticipantsModel.findAll({
      where: { workloadId: id },
      transaction
    });

    // 权限控制：TEACHER-LV1角色只能删除自己参与的工作量
    if ((userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') || ['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      const isParticipant = participants.some(p => p.participantId === userInfo.id);
      if (!isParticipant) {
        await transaction.rollback();
        return res.status(403).json({
          code: 403,
          message: '权限不足，您只能删除自己参与的工作量',
          data: null
        });
      }
    }

    // 如果工作量已审核通过，需要更新用户排名数据（减分）
    if (workload.ifReviewer === 1) {
      try {
        // 获取时间区间
        const timeInterval = await getTimeIntervalByName("teachingWorkloads");
        console.log('获取到时间区间:', JSON.stringify(timeInterval));

        // 判断工作量是否在时间区间内（使用创建时间判断）
        const isInTimeRange = timeInterval ?
          isProjectInTimeRange(workload.createdAt, timeInterval.startTime, timeInterval.endTime) :
          false;

        console.log('工作量时间范围状态:', isInTimeRange ? '在范围内' : '不在范围内');

        // 确定需要更新的排名表
        let rankingTables = [];
        if (isInTimeRange) {
          rankingTables = ['user_ranking_reviewed_in', 'user_ranking_reviewed_all'];
        } else {
          rankingTables = ['user_ranking_reviewed_out', 'user_ranking_reviewed_all'];
        }

        console.log('需要更新的排名表:', rankingTables);

        // 获取工作量级别对应的分数
        const workloadLevel = await teachingWorkloadLevelsModel.findByPk(workload.categoryId, { transaction });
        if (!workloadLevel) {
          console.error(`未找到工作量级别信息，levelId: ${workload.categoryId}`);
          throw new Error('未找到工作量级别信息');
        }

        const baseScore = workloadLevel.score || 0;

        // 如果有参与者，批量处理减分操作
        if (participants.length > 0) {
          // 准备批量更新的数据
          const userIds = [];
          const scores = [];

          // 收集所有参与者数据
          for (const participant of participants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = baseScore * ratio;

            userIds.push(userId);
            scores.push(participantScore);
          }

          console.log('删除工作量的参与者数据:', {
            userIds: userIds,
            scores: scores
          });

          // 创建固定值为1的计数数组，表示每个用户工作量数-1
          const countDeltas = Array(userIds.length).fill(1);

          // 对每个排名表执行减分操作（一次性批量处理所有参与者）
          for (const table of rankingTables) {
            console.log(`为被删除工作量的所有参与者更新排名表 ${table}`);
            await updateUserRankings(
              userIds,
              table,
              'teachingWorkloads',
              countDeltas, // 使用固定值1表示工作量计数-1
              scores, // 使用参与者分数数组
              transaction,
              "subtract" // 减分操作
            );
          }

          console.log('成功更新被删除工作量的所有参与者排名数据');
        }
      } catch (rankingError) {
        console.error('更新用户排名数据失败:', rankingError);
        await transaction.rollback();
        throw new Error(`更新排名失败: ${rankingError.message}`);
      }
    }

    // 删除参与者记录
    await teachingWorkloadParticipantsModel.destroy({
      where: { workloadId: id },
      transaction
    });

    // 删除工作量记录
    await workload.destroy({ transaction });

    await transaction.commit();

    console.log(`✅ 删除成功: 工作量ID ${id}`);

    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });

  } catch (error) {
    await transaction.rollback();
    console.error('❌ 删除教学工作量失败:', error);
    return res.status(500).json({
      code: 500,
      message: `删除教学工作量失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 审核教学工作量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reviewWorkload = async (req, res) => {
  const transaction = await teachingWorkloadsModel.sequelize.transaction();

  try {
    const { id, ifReviewer, reviewComment } = req.body;

    if (!id || ifReviewer === undefined) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数',
        data: null
      });
    }

    // 查询工作量是否存在
    const workload = await teachingWorkloadsModel.findByPk(id, {
      include: [
        {
          model: teachingWorkloadParticipantsModel,
          as: 'participants',
          required: false
        },
        {
          model: teachingWorkloadLevelsModel,
          as: 'category',
          required: false
        }
      ],
      transaction
    });

    if (!workload) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '工作量不存在',
        data: null
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 权限检查 - 只有管理员可以审核
    const hasPermission = userInfo.role.roleAuth === 'ADMIN-LV2' || userInfo.role.roleAuth === 'SUPER';

    if (!hasPermission) {
      await transaction.rollback();
      return res.status(403).json({
        code: 403,
        message: '您没有权限审核工作量',
        data: null
      });
    }

    // 更新审核状态
    const updateData = {
      ifReviewer: ifReviewer,
      reviewComment: reviewComment || null,
      reviewerId: userInfo.id
    };

    await workload.update(updateData, { transaction });

    // 工作量审核通过后，更新用户排名数据
    if (ifReviewer === 1) {
      try {
        // 获取工作量级别对应的分数
        const workloadLevel = await teachingWorkloadLevelsModel.findByPk(workload.categoryId, { transaction });
        if (!workloadLevel) {
          console.error(`未找到工作量级别信息，levelId: ${workload.categoryId}`);
          throw new Error('未找到工作量级别信息');
        }

        const baseScore = workloadLevel.score || 0;

        // 获取工作量所有参与者及其分配比例
        const participants = await teachingWorkloadParticipantsModel.findAll({
          where: { workloadId: id },
          transaction
        });

        if (participants && participants.length > 0) {
          console.log(`工作量ID ${id} 有 ${participants.length} 个参与者，开始更新排名数据`);

          // 准备批量更新的数据
          const participantUserIds = [];
          const participantScores = [];

          // 收集所有参与者数据
          for (const participant of participants) {
            const userId = participant.participantId;
            const ratio = parseFloat(participant.allocationRatio) || 0;
            const participantScore = baseScore * ratio;

            participantUserIds.push(userId);
            participantScores.push(participantScore);
          }

          console.log('审核通过的工作量参与者数据:', {
            userIds: participantUserIds,
            scores: participantScores
          });

          // 获取时间区间
          const timeInterval = await getTimeIntervalByName("teachingWorkloads");

          // 判断工作量是否在时间区间内
          const isInTimeRange = timeInterval && workload.createdAt ?
            isProjectInTimeRange(workload.createdAt, timeInterval.startTime, timeInterval.endTime) :
            false;

          // 根据工作量是否在时间区间内，更新不同的排名表
          let rankingTables = [];

          if (isInTimeRange) {
            // 在区间内：更新范围内表和全部表
            rankingTables = [
              'user_ranking_reviewed_in',
              'user_ranking_reviewed_all'
            ];
          } else {
            // 不在区间内：更新范围外表和全部表
            rankingTables = [
              'user_ranking_reviewed_out',
              'user_ranking_reviewed_all'
            ];
          }

          try {
            for (const table of rankingTables) {
              // 更新所有参与者的排名数据：每人计数+1，分数增加各自的得分
              await updateUserRankings(
                participantUserIds,          // 所有参与者的用户ID数组
                table,                       // 表名
                'teachingWorkloads',         // 类型名
                Array(participantUserIds.length).fill(1), // 每个参与者计数+1
                participantScores,           // 每个参与者的得分数组
                transaction,                 // 传递事务对象
                "add"                        // 操作类型：加分
              );
            }
          } catch (rankingError) {
            console.error('更新排名表失败:', rankingError);
            // 对于数据完整性错误，应当回滚事务
            if (rankingError.name === 'SequelizeDatabaseError') {
              await transaction.rollback();
              throw new Error(`更新排名失败: ${rankingError.message}`);
            } else {
              // 对于其他非致命错误，记录但不中断流程
              console.warn(`排名更新出现非致命错误: ${rankingError.message}`);
            }
          }
        } else {
          console.log(`工作量ID ${id} 没有参与者，无需更新排名`);
        }
      } catch (error) {
        console.error('更新用户排名数据失败:', error);
        // 检查是否已经回滚，如果没有则回滚事务
        if (!error.message || !error.message.includes('更新排名失败')) {
          await transaction.rollback();
          throw error; // 将错误向上传播
        }
      }
    }

    // 提交事务
    await transaction.commit();

    console.log(`✅ 审核成功: 工作量ID ${id}, 审核状态: ${ifReviewer}`);

    return res.status(200).json({
      code: 200,
      message: '审核成功',
      data: null
    });

  } catch (error) {
    // 检查事务状态，只有在未完成状态才回滚
    if (transaction && !transaction.finished) {
      await transaction.rollback();
    }
    console.error('❌ 审核教学工作量失败:', error);
    return res.status(500).json({
      code: 500,
      message: `审核教学工作量失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 重新提交教学工作量审核
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.reapplyWorkload = async (req, res) => {
  const transaction = await teachingWorkloadsModel.sequelize.transaction();

  try {
    const { id } = req.body;

    if (!id) {
      await transaction.rollback();
      return res.status(400).json({
        code: 400,
        message: '缺少工作量ID',
        data: null
      });
    }

    // 查询工作量是否存在
    const workload = await teachingWorkloadsModel.findByPk(id, { transaction });

    if (!workload) {
      await transaction.rollback();
      return res.status(404).json({
        code: 404,
        message: '工作量不存在',
        data: null
      });
    }

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 权限控制：TEACHER-LV1角色只能重新提交自己参与的工作量
    if ((userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') || ['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      const participants = await teachingWorkloadParticipantsModel.findAll({
        where: { workloadId: id },
        transaction
      });

      const isParticipant = participants.some(p => p.participantId === userInfo.id);
      if (!isParticipant) {
        await transaction.rollback();
        return res.status(403).json({
          code: 403,
          message: '权限不足，您只能重新提交自己参与的工作量',
          data: null
        });
      }
    }

    // 重置审核状态
    await workload.update({
      ifReviewer: null,
      reviewComment: null,
      reviewerId: null
    }, { transaction });

    await transaction.commit();

    console.log(`✅ 重新提交审核成功: 工作量ID ${id}`);

    return res.status(200).json({
      code: 200,
      message: '重新提交审核成功',
      data: null
    });

  } catch (error) {
    // 检查事务状态，只有在未完成状态才回滚
    if (transaction && !transaction.finished) {
      await transaction.rollback();
    }
    console.error('❌ 重新提交教学工作量审核失败:', error);
    return res.status(500).json({
      code: 500,
      message: `重新提交教学工作量审核失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取教学工作量类型分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTypeDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 条件构建
    const whereCondition = {};

    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }

    // 在这里添加时间区间检查逻辑
    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingWorkloads");

    // 根据统计范围筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        whereCondition.createdAt = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { createdAt: { [Op.lt]: intervalStartTime } },
          { createdAt: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if ((userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') || ['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      // 通过参与者表查询该用户参与的工作量
      const participantWorkloadIds = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userInfo.id },
        attributes: ['workloadId']
      });

      const workloadIds = participantWorkloadIds.map(p => p.workloadId);

      if (workloadIds.length > 0) {
        whereCondition.id = { [Op.in]: workloadIds };
      } else {
        // 如果用户没有参与任何工作量，返回空结果
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
    }

    // 如果传入了特定的userId参数，则通过关联表查询该用户参与的工作量
    if (userId) {
      // 使用关联查询，通过参与者表查找相关工作量
      const participantWorkloadIds = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userId },
        attributes: ['workloadId']
      });

      const workloadIds = participantWorkloadIds.map(p => p.workloadId);

      if (workloadIds.length > 0) {
        whereCondition.id = { [Op.in]: workloadIds };
      } else {
        // 如果指定用户没有参与任何工作量，返回空结果
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
    }

    // 查询所有工作量
    const workloads = await teachingWorkloadsModel.findAll({
      where: whereCondition,
      include: [
        {
          model: teachingWorkloadParticipantsModel,
          as: 'participants',
          where: userId ? { participantId: userId } : undefined,
          required: userId ? true : false
        }
      ],
      attributes: {
        include: ['courseType', 'createdAt'] // 确保包含必要字段
      }
    });

    // 初始化类型数据
    const typeData = {};

    // 统计工作量类型分布
    workloads.forEach(workload => {
      const workloadJson = workload.toJSON();
      const typeName = workloadJson.courseType;
      typeData[typeName] = (typeData[typeName] || 0) + 1;
    });

    // 转换为数组格式
    const result = Object.entries(typeData).map(([type, count]) => ({
      type,
      count,
      name: type
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });

  } catch (error) {
    console.error('获取教学工作量类型分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教学工作量类型分布失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取教学工作量级别分布数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelDistribution = async (req, res) => {
  try {
    const { range = 'all', userId, reviewStatus = 'all' } = req.body;

    // 获取当前用户信息
    const userInfo = await getUserInfoFromRequest(req);

    // 条件构建
    const whereCondition = {};

    // 根据审核状态过滤
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }

    // 权限控制：TEACHER-LV1角色只能查看自己的数据
    if ((userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') || ['ADMIN-LV2', 'SUPER'].includes(userInfo.role.roleAuth)) {
      // 通过参与者表查询该用户参与的工作量
      const participantWorkloadIds = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userInfo.id },
        attributes: ['workloadId']
      });

      const workloadIds = participantWorkloadIds.map(p => p.workloadId);

      if (workloadIds.length > 0) {
        whereCondition.id = { [Op.in]: workloadIds };
      } else {
        // 如果用户没有参与任何工作量，返回空结果
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
    }

    // 如果传入了特定的userId参数，则通过关联表查询该用户参与的工作量
    if (userId) {
      const participantWorkloadIds = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userId },
        attributes: ['workloadId']
      });

      const workloadIds = participantWorkloadIds.map(p => p.workloadId);

      if (workloadIds.length > 0) {
        whereCondition.id = { [Op.in]: workloadIds };
      } else {
        return res.status(200).json({
          code: 200,
          message: '获取成功',
          data: []
        });
      }
    }

    // 查询所有工作量
    const workloads = await teachingWorkloadsModel.findAll({
      where: whereCondition,
      include: [
        {
          model: teachingWorkloadLevelsModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false
        },
        {
          model: teachingWorkloadParticipantsModel,
          as: 'participants',
          where: userId ? { participantId: userId } : undefined,
          required: userId ? true : false
        }
      ]
    });

    // 初始化级别数据
    const levelData = {};

    // 统计工作量级别分布
    workloads.forEach(workload => {
      const workloadJson = workload.toJSON();
      const levelName = workloadJson.category?.categoryName || '未分类';
      levelData[levelName] = (levelData[levelName] || 0) + 1;
    });

    // 转换为数组格式
    const result = Object.entries(levelData).map(([level, count]) => ({
      level,
      count,
      name: level
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });

  } catch (error) {
    console.error('获取教学工作量级别分布失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教学工作量级别分布失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取审核状态统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getReviewStatusStats = async (req, res) => {
  try {
    const { range = 'all'} = req.body;
    const userInfo = await getUserInfoFromRequest(req);
    // 构建查询条件
    const whereCondition = {};

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingWorkloads");

    // 根据统计范围筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        whereCondition.createdAt = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { createdAt: { [Op.lt]: intervalStartTime } },
          { createdAt: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {

      // 首先查询用户参与的项目ID列表
      const participantWorkloads = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userInfo.id },
        attributes: ['workloadId'],
        raw: true
      });
      const workloadIds = participantWorkloads.map(p => p.workloadId);

      // 添加项目ID筛选条件，确保只查询用户参与的项目
      whereCondition.id = { [Op.in]: workloadIds };
    }



    // 查询所有工作量
    const workloads = await teachingWorkloadsModel.findAll({
      where: whereCondition,
      attributes: ['ifReviewer', 'createdAt']
    });

    // 统计审核状态分布
    const statusData = {
      'pending': 0,    // 待审核
      'reviewed': 0,   // 已审核
      'rejected': 0    // 已拒绝
    };

    workloads.forEach(workload => {
      if (workload.ifReviewer === null) {
        statusData.pending++;
      } else if (workload.ifReviewer === true) {
        statusData.reviewed++;
      } else if (workload.ifReviewer === false) {
        statusData.rejected++;
      }
    });

    // 转换为数组格式
    const result = Object.entries(statusData).map(([status, count]) => ({
      status,
      count,
      name: status === 'pending' ? '待审核' : status === 'reviewed' ? '已审核' : '已拒绝'
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });

  } catch (error) {
    console.error('获取审核状态统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取审核状态统计失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取课程类型统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getCourseTypeStats = async (req, res) => {
  try {
    const { range = 'all', reviewStatus = 'all' } = req.body;
    const userInfo = await getUserInfoFromRequest(req);
    // 构建查询条件
    const whereCondition = {};

    // 根据审核状态筛选
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingWorkloads");

    // 根据统计范围筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        whereCondition.createdAt = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { createdAt: { [Op.lt]: intervalStartTime } },
          { createdAt: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {

      // 首先查询用户参与的项目ID列表
      const participantWorkloads = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userInfo.id },
        attributes: ['workloadId'],
        raw: true
      });
      const workloadIds = participantWorkloads.map(p => p.workloadId);

      // 添加项目ID筛选条件，确保只查询用户参与的项目
      whereCondition.id = { [Op.in]: workloadIds };
    }

    // 查询所有工作量
    const workloads = await teachingWorkloadsModel.findAll({
      where: whereCondition,
      attributes: ['courseType', 'createdAt']
    });

    // 统计课程类型分布
    const typeData = {};

    workloads.forEach(workload => {
      const courseType = workload.courseType;
      let typeName = '';

      switch (courseType) {
        case 'theory':
          typeName = '理论课';
          break;
        case 'experiment':
          typeName = '实验课';
          break;
        case 'practice':
          typeName = '实践课';
          break;
        case 'design':
          typeName = '课程设计';
          break;
        case 'thesis':
          typeName = '毕业论文';
          break;
        default:
          typeName = '其他';
      }

      typeData[typeName] = (typeData[typeName] || 0) + 1;
    });

    // 转换为数组格式
    const result = Object.entries(typeData).map(([type, count]) => ({
      type,
      count,
      name: type
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });

  } catch (error) {
    console.error('获取课程类型统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取课程类型统计失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取学期统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getSemesterStats = async (req, res) => {
  try {
    const { range = 'all', reviewStatus = 'all' } = req.body;
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const whereCondition = {};

    // 根据审核状态筛选
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingWorkloads");

    // 根据统计范围筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        whereCondition.createdAt = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { createdAt: { [Op.lt]: intervalStartTime } },
          { createdAt: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    if (userInfo.role && userInfo.role.roleAuth === 'TEACHER-LV1') {
      // 首先查询用户参与的项目ID列表
      const participantWorkloads = await teachingWorkloadParticipantsModel.findAll({
        where: { participantId: userInfo.id },
        attributes: ['workloadId'],
        raw: true
      });
      const workloadIds = participantWorkloads.map(p => p.workloadId);

      // 添加项目ID筛选条件，确保只查询用户参与的项目
      whereCondition.id = { [Op.in]: workloadIds };
    }
    // 查询所有工作量
    const workloads = await teachingWorkloadsModel.findAll({
      where: whereCondition,
      attributes: ['semester', 'createdAt']
    });

    // 统计学期分布
    const semesterData = {};

    workloads.forEach(workload => {
      const semester = workload.semester || '未设置';
      semesterData[semester] = (semesterData[semester] || 0) + 1;
    });

    // 转换为数组格式
    const result = Object.entries(semesterData).map(([semester, count]) => ({
      semester,
      count,
      name: semester
    }));

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });

  } catch (error) {
    console.error('获取学期统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取学期统计失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取得分统计
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getScoreStats = async (req, res) => {
  try {
    const { range = 'all', reviewStatus = 'all', intervalCount = 5 } = req.body;
    const userInfo = await getUserInfoFromRequest(req);

    // 构建查询条件
    const whereCondition = {};

    // 根据审核状态筛选
    if (reviewStatus === 'reviewed') {
      whereCondition.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      whereCondition.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      whereCondition.ifReviewer = null;
    }

    // 获取时间区间
    const timeInterval = await getTimeIntervalByName("teachingWorkloads");

    // 根据统计范围筛选
    if (timeInterval && range !== 'all') {
      const intervalStartTime = new Date(timeInterval.startTime);
      const intervalEndTime = new Date(timeInterval.endTime);

      if (range === 'in') {
        // 在时间范围内
        whereCondition.createdAt = {
          [Op.gte]: intervalStartTime,
          [Op.lte]: intervalEndTime
        };
      } else if (range === 'out') {
        // 在时间范围外
        whereCondition[Op.or] = [
          { createdAt: { [Op.lt]: intervalStartTime } },
          { createdAt: { [Op.gt]: intervalEndTime } }
        ];
      }
    }

    // 查询所有工作量，包含级别信息用于计算得分
    const workloads = await teachingWorkloadsModel.findAll({
      where: whereCondition,
      include: [
        {
          model: teachingWorkloadLevelsModel,
          as: 'category',
          attributes: ['score'],
          required: false
        }
      ],
      attributes: {
        include: ['createdAt'] // 确保包含createdAt字段
      }
    });

    // 提取所有分数
    const scores = workloads.map(workload => parseFloat(workload.category?.score || 0));

    if (scores.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: []
      });
    }

    // 计算最小和最大分数
    const minScore = Math.min(...scores);
    const maxScore = Math.max(...scores);

    // 计算区间步长
    const step = (maxScore - minScore) / intervalCount;

    // 创建动态区间
    const scoreRanges = {};
    const scoreData = [];

    // 如果所有分数都相同，创建一个单独的区间
    if (minScore === maxScore) {
      const rangeName = `${minScore.toFixed(2)}分`;
      scoreRanges[rangeName] = scores.length;
      scoreData.push({
        range: rangeName,
        count: scores.length,
        name: rangeName,
        minScore: minScore,
        maxScore: minScore
      });
    } else {
      // 创建区间并统计
      for (let i = 0; i < intervalCount; i++) {
        const start = minScore + i * step;
        const end = i === intervalCount - 1 ? maxScore : minScore + (i + 1) * step;

        // 区间名称
        const rangeName = i === intervalCount - 1
          ? `${start.toFixed(2)}-${end.toFixed(2)}分`
          : `${start.toFixed(2)}-${end.toFixed(2)}分`;

        // 初始化区间计数
        scoreRanges[rangeName] = 0;

        // 统计落在该区间的分数
        scores.forEach(score => {
          if (i === intervalCount - 1) {
            // 最后一个区间包含上限
            if (score >= start && score <= end) {
              scoreRanges[rangeName]++;
            }
          } else {
            // 其他区间不包含上限
            if (score >= start && score < end) {
              scoreRanges[rangeName]++;
            }
          }
        });

        // 添加到结果数组
        if (scoreRanges[rangeName] > 0) {
          scoreData.push({
            range: rangeName,
            count: scoreRanges[rangeName],
            name: rangeName,
            minScore: start,
            maxScore: end
          });
        }
      }
    }

    // 按照分数范围排序
    scoreData.sort((a, b) => a.minScore - b.minScore);

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: scoreData
    });

  } catch (error) {
    console.error('获取得分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取得分统计失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取教师教学工作量排名
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeacherWorkloadRanking = async (req, res) => {
  try {
    console.log('获取教师教学工作量排名，参数:', req.body);
    const {
      range = 'all',
      reviewStatus = 'all',
      page = 1,
      pageSize,
      isExport = false
    } = req.body;
    const limit = pageSize != null ? pageSize : 10;

    // 分页参数
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(limit);
    const offset = (pageNum - 1) * pageSizeNum;

    // 根据range选择对应的排名表模型
    let RankingModel;
    switch (range) {
      case 'in':
        RankingModel = userRankingReviewedInModel;
        break;
      case 'out':
        RankingModel = userRankingReviewedOutModel;
        break;
      case 'all':
      default:
        RankingModel = userRankingReviewedAllModel;
        break;
    }

    // 查询条件：按教学工作量总分降序排序
    const queryOptions = {
      order: [['teachingWorkloadScore', 'DESC']],
      attributes: [
        'rank',
        'userId',
        'nickName',
        'studentNumber',
        'teachingWorkloadCount',
        'teachingWorkloadScore'
      ]
    };

    // 如果不是导出，添加分页限制
    if (!isExport) {
      queryOptions.limit = pageSizeNum;
      queryOptions.offset = offset;
    }

    // 执行查询
    const { count, rows } = await RankingModel.findAndCountAll(queryOptions);

    // 格式化返回数据
    const formattedResults = rows.map((item, index) => ({
      rank: index + 1 + offset,
      userId: item.userId,
      userName: item.nickName,
      studentNumber: item.studentNumber,
      totalWorkloads: item.teachingWorkloadCount || 0,
      totalScore: parseFloat(item.teachingWorkloadScore || 0).toFixed(2)
    }));

    // 构建分页信息
    const pagination = {
      total: count,
      page: pageNum,
      pageSize: pageSizeNum,
      totalPages: Math.ceil(count / pageSizeNum)
    };

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: formattedResults,
        pagination: pagination
      }
    });

  } catch (error) {
    console.error('获取教师教学工作量排名失败:', error);
    return res.status(500).json({
      code: 500,
      message: `获取教师教学工作量排名失败: ${error.message}`,
      data: null
    });
  }
};

/**
 * 获取用户教学工作量详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserWorkloadDetails = async (req, res) => {
  try {
    const {
      userId,
      range = 'all',  // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all', // 审核状态筛选: 'reviewed'(已审核), 'rejected'(拒绝), 'pending'(待审核), 'all'(全部)
      page = 1,
      pageSize = 10
    } = req.body;
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：userId',
        data: null
      });
    }

    // 查询该用户参与的所有工作量ID
    const participations = await teachingWorkloadParticipantsModel.findAll({
      where: { participantId: userId },
      attributes: ['workloadId', 'allocationRatio', 'isLeader']
    });

    if (!participations || participations.length === 0) {
      return res.status(200).json({
        code: 200,
        message: '获取成功',
        data: {
          list: [],
          pagination: {
            total: 0,
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            totalPages: 0
          },
          totalScore: 0
        }
      });
    }

    const workloadIds = participations.map(p => p.workloadId);

    // 构建查询条件
    const where = {
      id: { [Op.in]: workloadIds }
    };

    // 审核状态筛选
    if (reviewStatus === 'reviewed') {
      where.ifReviewer = 1;
    } else if (reviewStatus === 'rejected') {
      where.ifReviewer = 0;
    } else if (reviewStatus === 'pending') {
      where.ifReviewer = null;
    }

    // 分页参数
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    // 查询工作量列表
    const { count, rows } = await teachingWorkloadsModel.findAndCountAll({
      where,
      include: [
        {
          model: teachingWorkloadLevelsModel,
          as: 'category',
          attributes: ['id', 'categoryName', 'score'],
          required: false
        },
        {
          model: userModel,
          as: 'teacher',
          attributes: ['id', 'nickName', 'studentNumber'],
          required: false
        }
      ],
      offset,
      limit,
      order: [['createdAt', 'DESC']]
    });

    // 处理返回数据
    const workloadList = await Promise.all(rows.map(async workload => {
      const workloadJson = workload.toJSON();
      const participation = participations.find(p => p.workloadId === workloadJson.id);

      // 计算用户得分
      const userAllocationRatio = participation ? parseFloat(participation.allocationRatio) * 100 : 0;
      const baseScore = workloadJson.category ? parseFloat(workloadJson.category.score || 0) : 0;
      const totalScore = parseFloat(workloadJson.score || baseScore || 0);
      const userScore = totalScore * (userAllocationRatio / 100);

      // 获取工作量是否在时间区间内的信息
      let inScoreRange = false;
      if (workloadJson.createdAt) {
        const timeInterval = await getTimeIntervalByName("teachingWorkloads");
        if (timeInterval && timeInterval.startTime && timeInterval.endTime) {
          inScoreRange = isProjectInTimeRange(
            workloadJson.createdAt,
            timeInterval.startTime,
            timeInterval.endTime
          );
        }
      }

      return {
        ...workloadJson,
        userAllocationRatio: parseFloat(userAllocationRatio).toFixed(1), // 格式化为一位小数
        isLeader: participation ? participation.isLeader : false,
        userScore: parseFloat(userScore).toFixed(2), // 格式化为两位小数
        totalScore: parseFloat(totalScore).toFixed(2), // 格式化为两位小数
        inScoreRange
      };
    }));

    // 计算总分
    const totalUserScore = workloadList.reduce((sum, item) => {
      // 只计算已审核且在统计范围内的工作量
      if ((reviewStatus === 'all' || reviewStatus === 'reviewed') &&
        (range === 'all' || (range === 'in' && item.inScoreRange) || (range === 'out' && !item.inScoreRange)) &&
        (item.ifReviewer === 1)) {
        return sum + parseFloat(item.userScore || 0);
      }
      return sum;
    }, 0);

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: workloadList,
        pagination: {
          total: count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(count / pageSize)
        },
        totalScore: parseFloat(totalUserScore).toFixed(2) // 格式化为两位小数
      }
    });
  } catch (error) {
    console.error('获取用户教学工作量详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户教学工作量详情失败: ' + error.message,
      data: null
    });
  }
};

/**
 * 判断项目是否在时间范围内
 * @param {string} projectDate - 项目日期
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {boolean} - 是否在范围内
 */
function isProjectInTimeRange(projectDate, startDate, endDate) {
  if (!projectDate || !startDate || !endDate) return false;

  // 将日期字符串转换为日期对象
  const projectDateObj = new Date(projectDate);
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);

  // 项目日期必须在开始日期和结束日期之间
  return projectDateObj >= startDateObj && projectDateObj <= endDateObj;
}

/**
 * 获取教学工作量总分统计（按级别和总体）
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getTeachingWorkloadsTotalScore = async (req, res) => {
  try {
    // 获取参数
    const { range = 'in', reviewStatus = 'reviewed' } = req.body;

    // 获取数据库连接实例
    let sequelize;
    if (teachingWorkloadsModel.sequelize) {
      sequelize = teachingWorkloadsModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }

    console.log('调用存储过程参数:', {
      range,
      reviewStatus
    });

    let userId = null; 
    const userInfo = await getUserInfoFromRequest(req);
    if(userInfo.role.roleAuth == 'TEACHER-LV1'){
      userId = userInfo.id;
    }

    // 调用存储过程
    const results = await sequelize.query(
      'CALL get_teaching_workloads_total_score(?, ?, ?)',
      {
        replacements: [range, reviewStatus, userId],
        type: sequelize.QueryTypes.RAW,
        raw: true
      }
    );

    console.log('存储过程原始返回结果:', JSON.stringify(results));

    // 初始化返回值
    const responseData = {
      levelStats: [], // 按级别的统计
      overallStats: { totalWorkloads: 0, totalScore: 0 }, // 总体统计
      timeInterval: null // 时间区间信息
    };

    // 根据实际返回结果格式处理数据
    if (Array.isArray(results) && results.length > 0) {
      // 检查返回的是单层数组(直接就是结果数组)还是嵌套数组(多个结果集)
      const isNestedArray = Array.isArray(results[0]) && !results[0].hasOwnProperty('levelId') && !results[0].hasOwnProperty('levelName');

      if (isNestedArray) {
        // 处理嵌套数组格式 (传统格式)
        // 第一个结果集可能是按级别统计的数据
        if (results[0] && Array.isArray(results[0])) {
          responseData.levelStats = results[0].map(item => ({
            levelId: item.levelId,
            levelName: item.levelName || '未知级别',
            count: parseInt(item.count || 0, 10),
            totalScore: parseFloat(item.totalScore || 0)
          }));
        }

        // 第二个结果集可能是总体统计
        if (results.length > 1 && Array.isArray(results[1]) && results[1].length > 0) {
          responseData.overallStats = {
            totalWorkloads: parseInt(results[1][0].totalWorkloads || 0, 10),
            totalScore: parseFloat(results[1][0].totalScore || 0)
          };
        }

        // 第三个结果集可能是时间区间信息
        if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
          responseData.timeInterval = results[2][0];
        }
      } else {
        // 处理单层数组格式 (当前情况)
        // 将results直接作为levelStats使用
        responseData.levelStats = results.map(item => ({
          levelId: item.levelId,
          levelName: item.levelName || '未知级别',
          count: parseInt(item.count || 0, 10),
          totalScore: parseFloat(item.totalScore || 0)
        }));

        // 根据levelStats计算总体统计
        responseData.overallStats = {
          totalWorkloads: responseData.levelStats.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0),
          totalScore: parseFloat(
            responseData.levelStats.reduce((sum, item) => sum + (parseFloat(item.totalScore) || 0), 0).toFixed(2)
          )
        };
      }
    }

    // 如果没有获取到时间区间信息，则尝试使用工具函数获取
    if (!responseData.timeInterval) {
      try {
        const timeInterval = await getTimeIntervalByName('teachingWorkloads');
        if (timeInterval) {
          responseData.timeInterval = timeInterval;
        }
      } catch (timeError) {
        console.error('获取时间区间信息失败:', timeError);
      }
    }

    return res.status(200).json({
      code: 200,
      message: '获取教学工作量总分统计成功',
      data: responseData
    });
  } catch (error) {
    console.error('获取教学工作量总分统计失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取教学工作量总分统计失败',
      error: error.message
    });
  }
}

/**
 * 获取用户教学工作量详情列表及得分
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getUserWorkloadsDetail = async (req, res) => {
  try {
    const {
      userId,                // 用户ID - 必填
      range = 'all',         // 统计范围筛选: 'in'(范围内), 'out'(范围外), 'all'(全部)
      reviewStatus = 'all',  // 审核状态筛选: 'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)
      pageSize = 10,         // 每页记录数
      pageNum = 1            // 当前页码
    } = req.body;

    // 验证必填参数
    if (!userId) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数：userId',
        data: null
      });
    }

    // 获取数据库连接实例
    let sequelize;
    if (teachingWorkloadsModel.sequelize) {
      sequelize = teachingWorkloadsModel.sequelize;
    } else {
      throw new Error("无法获取数据库连接实例");
    }

    // 调用存储过程
    const results = await sequelize.query(
      'CALL get_user_teaching_workloads_detail(?, ?, ?, ?, ?)',
      {
        replacements: [
          userId,
          range,
          reviewStatus,
          pageSize,
          pageNum
        ],
        type: sequelize.QueryTypes.RAW,
        raw: true,
        nest: true
      }
    );

    console.log("用户教学工作量详情存储过程返回结果:", JSON.stringify(results));

    // 处理查询结果
    let totalCount = 0;
    let workloadsList = [];
    let statsData = {
      totalWorkloads: 0,
      leaderCount: 0,
      participantCount: 0,
      totalScore: 0
    };
    let timeInterval = null;

    // 分析和处理返回的结果结构
    if (Array.isArray(results) && results.length > 0) {
      // 第一个结果集: 总记录数
      if (results[0] && Array.isArray(results[0]) && results[0].length > 0) {
        totalCount = parseInt(results[0][0].totalCount || 0);
      }

      // 第二个结果集: 工作量列表
      if (results.length > 1 && Array.isArray(results[1])) {
        workloadsList = results[1].map(item => ({
          id: item.id,
          courseName: item.courseName,
          semester: item.semester,
          studentLevel: item.studentLevel,
          courseType: item.courseType,
          courseNature: item.courseNature,
          categoryName: item.categoryName,
          baseScore: parseFloat(item.baseScore || 0),
          userAllocationRatio: parseFloat(item.userAllocationRatio || 0),
          userScore: parseFloat(item.userScore || 0),
          isLeader: item.isLeader === 1,
          reviewStatus: item.reviewStatus,
          rejectReason: item.rejectReason,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }));
      }

      // 第三个结果集: 统计数据
      if (results.length > 2 && Array.isArray(results[2]) && results[2].length > 0) {
        const stats = results[2][0];
        statsData = {
          totalWorkloads: parseInt(stats.totalWorkloads || 0),
          leaderCount: parseInt(stats.leaderCount || 0),
          participantCount: parseInt(stats.participantCount || 0),
          totalScore: parseFloat(stats.totalScore || 0)
        };
      }

      // 第四个结果集: 时间区间
      if (results.length > 3 && Array.isArray(results[3]) && results[3].length > 0) {
        const timeData = results[3][0];
        timeInterval = {
          startTime: timeData.startTime,
          endTime: timeData.endTime,
          name: timeData.name
        };
      }
    }

    // 如果没有从存储过程获得时间区间，则从数据库直接获取
    if (!timeInterval) {
      try {
        const timeIntervalData = await getTimeIntervalByName("teachingWorkloads");
        if (timeIntervalData) {
          timeInterval = {
            startTime: timeIntervalData.startTime,
            endTime: timeIntervalData.endTime,
            name: timeIntervalData.name
          };
        }
      } catch (error) {
        console.error("获取时间区间失败:", error);
      }
    }

    // 查询用户信息
    const user = await userModel.findByPk(userId);
    const userData = user ? {
      id: user.id,
      name: user.nickname || user.username,
      employeeNumber: user.studentNumber
    } : { id: userId, name: '未知用户', employeeNumber: '' };

    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        totalCount,
        pageSize,
        pageNum,
        workloads: workloadsList,
        stats: statsData,
        timeInterval,
        user: userData
      }
    });
  } catch (error) {
    console.error('获取用户教学工作量详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取用户教学工作量详情失败: ' + error.message,
      data: null
    });
  }
};
