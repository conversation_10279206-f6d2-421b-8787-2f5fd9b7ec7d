const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const { getTimeIntervalByName } = require('../../../utils/others');
const academicAppointmentModel = require('../../../models/v1/mapping/academicAppointmentsModel');
const associationLevelModel = require('../../../models/v1/mapping/associationLevelsModel');

/**
 * 获取学术任职级别列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAssociationLevels = async (req, res) => {
  try {
    const levels = await associationLevelModel.findAll({
      order: [['createdAt', 'ASC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: levels
    });
  } catch (error) {
    console.error('获取学术任职级别列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取学术任职级别列表失败',
      error: error.message
    });
  }
};

/**
 * 获取学术任职级别详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAssociationLevelDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }
    
    // 查询级别详情
    const level = await associationLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到学术任职级别',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: level
    });
  } catch (error) {
    console.error('获取学术任职级别详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取学术任职级别详情失败',
      error: error.message
    });
  }
};

/**
 * 创建学术任职级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createAssociationLevel = async (req, res) => {
  try {
    const { levelName, score, description } = req.body;
    
    // 验证必要字段
    if (!levelName) {
      return res.status(400).json({
        code: 400,
        message: '级别名称不能为空',
        data: null
      });
    }
    
    if (!score) {
      return res.status(400).json({
        code: 400,
        message: '分数不能为空',
        data: null
      });
    }
    
    // 检查级别名称是否已存在
    const existingLevel = await associationLevelModel.findOne({
      where: {
        levelName: levelName
      }
    });
    
    if (existingLevel) {
      return res.status(400).json({
        code: 400,
        message: '级别名称已存在',
        data: null
      });
    }
    
    // 创建学术任职级别
    const level = await associationLevelModel.create({
      id: uuidv4(),
      levelName,
      score,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: level
    });
  } catch (error) {
    console.error('创建学术任职级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建学术任职级别失败',
      error: error.message
    });
  }
};

/**
 * 更新学术任职级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateAssociationLevel = async (req, res) => {
  try {
    const { id } = req.params;
    const { levelName, score } = req.body;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }
    
    // 查询级别是否存在
    const level = await associationLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到学术任职级别',
        data: null
      });
    }
    
    // 如果要更新级别名称，检查新名称是否已存在
    if (levelName && levelName !== level.levelName) {
      const existingLevel = await associationLevelModel.findOne({
        where: {
          levelName: levelName,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingLevel) {
        return res.status(400).json({
          code: 400,
          message: '级别名称已存在',
          data: null
        });
      }
    }
    
    // 更新级别
    const updateData = {};
    if (levelName !== undefined) updateData.levelName = levelName;
    if (score !== undefined) updateData.score = score;
    updateData.updatedAt = new Date();
    
    await level.update(updateData);
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: await associationLevelModel.findByPk(id)
    });
  } catch (error) {
    console.error('更新学术任职级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新学术任职级别失败',
      error: error.message
    });
  }
};

/**
 * 删除学术任职级别
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteAssociationLevel = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '缺少级别ID',
        data: null
      });
    }
    
    // 查询级别是否存在
    const level = await associationLevelModel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        code: 404,
        message: '未找到学术任职级别',
        data: null
      });
    }
    
    // 检查是否有学术任职使用此级别
    const appointmentsCount = await academicAppointmentModel.count({
      where: { levelId: id }
    });
    
    if (appointmentsCount > 0) {
      return res.status(400).json({
        code: 400,
        message: `该级别已被${appointmentsCount}个学术任职记录使用，无法删除`,
        data: null
      });
    }
    
    // 删除级别
    await level.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除学术任职级别失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除学术任职级别失败',
      error: error.message
    });
  }
};

/**
 * 获取所有级别及其学术任职数量
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getLevelsWithCount = async (req, res) => {
  try {
    // 获取所有级别
    const levels = await associationLevelModel.findAll({
      order: [['createdAt', 'ASC']]
    });
    
    // 获取每个级别的学术任职数量
    const result = await Promise.all(levels.map(async (level) => {
      const count = await academicAppointmentModel.count({
        where: { levelId: level.id }
      });
      
      return {
        ...level.toJSON(),
        appointmentCount: count
      };
    }));
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: result
    });
  } catch (error) {
    console.error('获取级别及学术任职数量失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取级别及学术任职数量失败',
      error: error.message
    });
  }
}; 