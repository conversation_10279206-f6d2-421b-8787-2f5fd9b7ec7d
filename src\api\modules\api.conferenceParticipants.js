import request from '../server'

/**
 * 获取用户会议参与统计
 * @param {Object} params - 请求参数
 * @param {string} params.userId - 用户ID
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {Object} [params.timeRange] - 可选，自定义时间范围
 * @param {string} [params.timeRange.startDate] - 开始日期，格式 YYYY-MM-DD
 * @param {string} [params.timeRange.endDate] - 结束日期，格式 YYYY-MM-DD
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @returns {Promise} - 请求Promise对象
 */
export function getUserConferenceStatistics(params) {
  return request.post('/conferencesParticipants/statistics/user-statistics', params)
}

/**
 * 获取所有用户会议参与统计
 * @param {Object} params - 请求参数
 * @param {string} [params.range='all'] - 可选，统计范围，可选值：'in'(在统计时间内), 'out'(不在统计时间内), 'all'(全部)
 * @param {Object} [params.timeRange] - 可选，自定义时间范围
 * @param {string} [params.timeRange.startDate] - 开始日期，格式 YYYY-MM-DD
 * @param {string} [params.timeRange.endDate] - 结束日期，格式 YYYY-MM-DD
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.pageSize=10] - 每页条数
 * @param {string} [params.sortField='participationCount'] - 排序字段
 * @param {string} [params.sortOrder='desc'] - 排序方向，'asc' 或 'desc'
 * @param {string} [params.nickname] - 可选，用户昵称，支持模糊搜索
 * @returns {Promise} - 请求Promise对象
 */
export function getAllUsersConferenceStatistics(params) {
  return request.post('/conferencesParticipants/statistics/all-users-statistics', params)
}