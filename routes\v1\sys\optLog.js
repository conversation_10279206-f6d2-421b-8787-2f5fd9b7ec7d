// routes/userRoutes.js
const express = require('express');
const userOptLogController = require('../../../controllers/v1/system/userOptLogController');

const router = express.Router();

/**
 * 获取所有操作日志
 * @route POST /v1/sys/optLog/list
 * @group 操作日志管理 - 系统操作日志相关接口
 * @param {number} page.body - 页码，默认1
 * @param {number} limit.body - 每页数量，默认10
 * @param {string} startTime.body - 开始时间
 * @param {string} endTime.body - 结束时间
 * @param {string} type.body - 操作类型
 * @param {string} operator.body - 操作人
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], total: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', userOptLogController.getAll);

/**
 * 创建操作日志
 * @route POST /v1/sys/optLog/create
 * @group 操作日志管理 - 系统操作日志相关接口
 * @param {string} type.body.required - 操作类型
 * @param {string} content.body.required - 操作内容
 * @param {string} operator.body.required - 操作人
 * @param {string} ip.body - IP地址
 * @param {string} userAgent.body - 用户代理
 * @returns {object} 200 - {code: 200, message: "创建成功", data: {id: "日志ID"}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/create', userOptLogController.create);

/**
 * 获取指定操作日志
 * @route POST /v1/sys/optLog/findOne
 * @group 操作日志管理 - 系统操作日志相关接口
 * @param {string} id.body.required - 日志ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {日志详情}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/findOne', userOptLogController.findOne);

/**
 * 更新操作日志
 * @route POST /v1/sys/optLog/update
 * @group 操作日志管理 - 系统操作日志相关接口
 * @param {string} id.body.required - 日志ID
 * @param {string} type.body - 操作类型
 * @param {string} content.body - 操作内容
 * @param {string} operator.body - 操作人
 * @param {string} ip.body - IP地址
 * @param {string} userAgent.body - 用户代理
 * @returns {object} 200 - {code: 200, message: "更新成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/update', userOptLogController.update);

/**
 * 删除操作日志
 * @route POST /v1/sys/optLog/delete
 * @group 操作日志管理 - 系统操作日志相关接口
 * @param {string} id.body.required - 日志ID
 * @returns {object} 200 - {code: 200, message: "删除成功"}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/delete', userOptLogController.delete);

module.exports = router;
