const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

// 定义评分权重系数表模型
const ScoreWeight = sequelize.define('score_weights', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: 'ID'
    },
    categoryName: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '类别名称'
    },
    categoryCode: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '类别代码'
    },
    weight: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 1.00,
        comment: '权重系数'
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '描述'
    },
    status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: '状态：0-禁用，1-启用'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'score_weights',
    timestamps: true,
    indexes: [
        {
            name: 'uk_category_code',
            fields: ['categoryCode'],
            unique: true
        },
        {
            name: 'idx_weight_status',
            fields: ['status']
        }
    ]
});

module.exports = ScoreWeight;