// 数据库配置
const DB_CONFIG = {
  development: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    database: process.env.DB_NAME || 'performance_system',
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'root',
    dialect: 'mysql',
    timezone: '+08:00', // 设置时区为北京时间
    logging: console.log, // 开发环境打印SQL
    define: {
      timestamps: true, // 默认启用时间戳
      underscored: false, // 默认不使用下划线命名法，由各模型自行定义
      freezeTableName: true // 表名不加s
    },
    dialectOptions: {
      dateStrings: true,
      typeCast: true
    },
    pool: {
      max: 5, // 最大连接数
      min: 0, // 最小连接数
      acquire: 30000, // 获取连接超时
      idle: 10000 // 空闲释放时间
    }
  },
  production: {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    dialect: 'mysql',
    timezone: '+08:00',
    logging: false, // 生产环境不打印SQL
    define: {
      timestamps: true,
      underscored: false, // 默认不使用下划线命名法，由各模型自行定义
      freezeTableName: true
    },
    dialectOptions: {
      dateStrings: true,
      typeCast: true
    },
    pool: {
      max: 10,
      min: 2,
      acquire: 30000,
      idle: 10000
    }
  }
};

// JWT配置
const JWT_SECRET = 'your-jwt-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h'; // token有效期

// 上传文件配置
const UPLOAD_CONFIG = {
  fileSize: 10 * 1024 * 1024, // 最大文件大小 10MB
  uploadDir: process.env.UPLOAD_DIR || 'uploads', // 上传文件保存目录
  allowedFileTypes: [ // 允许上传的文件类型
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png',
    'image/gif'
  ]
};

// 分页默认配置
const PAGINATION_CONFIG = {
  defaultPageSize: 10,
  maxPageSize: 100
};

// 服务器配置
const SERVER_CONFIG = {
  port: process.env.PORT || 3000,
  apiPrefix: '/v1'
};

// 用户相关配置
const USER_CONFIG = {
  defaultAvatar: '/assets/default-avatar.png'
};

// Redis配置
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || '',
  db: process.env.REDIS_DB || 0
};

module.exports = {
  DB_CONFIG: DB_CONFIG[process.env.NODE_ENV || 'development'],
  JWT_SECRET,
  JWT_EXPIRES_IN,
  UPLOAD_CONFIG,
  PAGINATION_CONFIG,
  SERVER_CONFIG,
  REDIS_CONFIG,
  USER_CONFIG
}; 