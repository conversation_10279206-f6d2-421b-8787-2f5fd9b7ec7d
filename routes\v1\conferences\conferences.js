const express = require('express');
const router = express.Router();
const conferencesController = require('../../../controllers/v1/conferences/conferencesController');
const multer = require('multer');
const path = require('path');
const { authMiddleware } = require('../../../middleware/authMiddleware');
const createModulePermission = require('../../../middleware/modulePermission');

// 创建会议权限中间件函数
const conferencesPermission = (action) => createModulePermission('conferences', action);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/conferences/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'conferences-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

/**
 * 获取会议列表
 * @route POST /v1/sys/conferences/list
 * @group 会议管理 - 会议相关接口
 * @param {string} conferenceName - 会议名称（模糊搜索）
 * @param {string} levelId - 会议级别ID
 * @param {string} holdTimeStart - 举办开始日期
 * @param {string} holdTimeEnd - 举办结束日期
 * @param {string} userId - 用户ID（可选，如果提供则获取特定用户参与的会议）
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 统计范围筛选，可选值：'in'(范围内), 'out'(范围外), 'all'(全部)，默认'all'
 * @param {string} reviewStatus - 审核状态筛选，可选值：'all'(全部), 'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核)，默认'all'
 * @param {string} sortField - 排序字段，默认holdTime
 * @param {string} sortOrder - 排序方式，默认desc
 * @param {string} query - 关键词搜索
 * @param {boolean} isExport - 是否导出，如果为true则不分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], pagination: {total: 0, page: 1, pageSize: 10, totalPages: 0}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/list', 
  authMiddleware, 
  conferencesPermission('list'), 
  conferencesController.getConferences
);

/**
 * 导入会议数据
 * @route POST /v1/sys/conferences/projects/import
 * @group 会议管理 - 会议相关接口
 * @param {file} file.formData - 上传的Excel文件
 * @returns {object} 200 - {code: 200, message: "导入成功", data: {total: 0, success: 0, failed: 0, errors: []}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects/import', 
  authMiddleware, 
  conferencesPermission('import'), 
  upload.single('file'), 
  conferencesController.importConferences
);

/**
 * 导出会议数据
 * @route POST /v1/sys/conferences/projects/export
 * @group 会议管理 - 会议相关接口
 * @param {string} conferenceName - 会议名称（模糊搜索）
 * @param {string} levelId - 会议级别ID
 * @param {string} holdTimeStart - 举办开始日期
 * @param {string} holdTimeEnd - 举办结束日期
 * @param {string} userId - 用户ID（可选）
 * @param {string} range - 范围筛选，可选值：all, in, out
 * @param {string} reviewStatus - 审核状态筛选
 * @param {string} fileName - 导出文件名
 * @returns {object} 200 - {code: 200, message: "导出成功", data: []}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/projects/export', 
  authMiddleware, 
  conferencesPermission('export'), 
  conferencesController.exportConferences
);

/**
 * 创建会议
 * @route POST /v1/sys/conferences/project/create
 * @group 会议管理 - 会议相关接口
 * @param {string} conferenceName.body.required - 会议名称
 * @param {string} levelId.body.required - 会议级别ID
 * @param {string} holdTime.body.required - 举办日期
 * @param {string} remark.body - 备注
 * @param {Array} participants.body.required - 参与者数组，包含participantId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @returns {object} 201 - {code: 200, message: "创建成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/create', 
  authMiddleware, 
  conferencesPermission('create'), 
  upload.array('files', 5), 
  conferencesController.createConference
);

/**
 * 更新会议
 * @route POST /v1/sys/conferences/project/update
 * @group 会议管理 - 会议相关接口
 * @param {string} id.body.required - 会议ID
 * @param {string} conferenceName.body - 会议名称
 * @param {string} levelId.body - 会议级别ID
 * @param {string} holdTime.body - 举办日期
 * @param {string} remark.body - 备注
 * @param {Array} participants.body - 参与者数组，包含participantId, allocationRatio, isLeader
 * @param {Array} fileIds.body - 文件ID数组
 * @param {Array} attachmentUrl.body - 文件路径数组
 * @returns {object} 200 - {code: 200, message: "更新成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/update', 
  authMiddleware, 
  conferencesPermission('update'), 
  upload.array('files', 5), 
  async (req, res) => {
    const { id, ...updateData } = req.body;
    req.params = { id };
    req.body = updateData;
    await conferencesController.updateConference(req, res);
  }
);

/**
 * 删除会议
 * @route POST /v1/sys/conferences/project/delete
 * @group 会议管理 - 会议相关接口
 * @param {string} id.body.required - 会议ID
 * @returns {object} 200 - {code: 200, message: "删除成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.delete('/project/delete/:id', 
  authMiddleware, 
  conferencesPermission('delete'), 
  conferencesController.deleteConference
);

/**
 * 获取会议详情
 * @route POST /v1/sys/conferences/project/detail
 * @group 会议管理 - 会议相关接口
 * @param {string} id.body.required - 会议ID
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/detail', 
  authMiddleware, 
  conferencesPermission('detail'), 
  async (req, res) => {
    const { id } = req.body;
    req.params = { id };
    await conferencesController.getConferenceDetail(req, res);
  }
);

/**
 * 获取会议时间分布数据
 * @route POST /v1/sys/conferences/statistics/time-distribution
 * @group 会议统计 - 会议统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} userId.body - 用户ID，如果提供则只统计该用户参与的会议
 * @param {string} reviewStatus.body - 审核状态: 'all', 'rejected', 'pending', 'reviewed'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {months: ["YYYY-MM",...], data: [数量,...]}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/time-distribution', 
  authMiddleware, 
  conferencesPermission('timeDistribution'), 
  conferencesController.getTimeDistribution
);

/**
 * 审核会议
 * @route POST /v1/sys/conferences/project/review
 * @group 会议管理 - 会议相关接口
 * @param {string} id.body.required - 会议ID
 * @param {boolean} reviewStatus.body.required - 审核状态
 * @param {string} reviewComment.body - 审核意见
 * @param {string} reviewer.body.required - 审核人ID
 * @returns {object} 200 - {code: 200, message: "审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/project/review', 
  authMiddleware, 
  conferencesPermission('review'), 
  conferencesController.reviewConference
);

/**
 * 获取会议级别分布数据
 * @route POST /v1/sys/conferences/statistics/level-distribution
 * @group 会议统计 - 会议级别分布统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} userId - 用户ID，可选
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: [{levelName: '级别名称', count: 数量},...]}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/level-distribution', 
  authMiddleware, 
  conferencesPermission('levelDistribution'), 
  conferencesController.getLevelDistribution
);

/**
 * 获取教师会议排名数据
 * @route POST /v1/sys/conferences/statistics/teacher-ranking
 * @group 会议统计 - 教师会议排名统计
 * @param {string} range - 数据范围: 'in', 'out', 'all'
 * @param {string} reviewStatus - 审核状态: 'all', 'reviewed', 'rejected', 'pending'
 * @param {number} page - 页码
 * @param {number} pageSize - 每页记录数
 * @param {boolean} isExport - 是否导出所有数据，导出时不应用分页
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [{userId, userName, studentNumber, totalConferences, totalScore}], pagination: {page, pageSize, total, totalPages}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-ranking', 
  authMiddleware, 
  conferencesPermission('teacherRanking'), 
  conferencesController.getTeacherConferenceRanking
);

/**
 * 获取教师会议详情
 * @route POST /v1/sys/conferences/statistics/teacher-project-details
 * @group 会议统计 - 教师会议详情
 * @param {string} userId - 用户ID
 * @param {number} page - 页码，默认1
 * @param {number} pageSize - 每页条数，默认10
 * @param {string} range - 数据范围
 * @param {string} reviewStatus - 审核状态，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {list: [], totalScore: 0}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/teacher-project-details', 
  authMiddleware, 
  conferencesPermission('teacherProjectDetails'), 
  conferencesController.getTeacherConferenceDetails
);

/**
 * 获取会议统计概览数据
 * @route POST /v1/sys/conferences/statistics/overview
 * @group 会议统计 - 会议统计概览
 * @param {string} userId - 用户ID，可选
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalConferences, activeConferences, averageScore, reviewedRate}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/overview', 
  authMiddleware, 
  conferencesPermission('overview'), 
  conferencesController.getConferenceStatistics
);

/**
 * 获取会议总分统计（按级别和总体）
 * @route POST /v1/sys/conferences/statistics/conferences-total-score
 * @group 会议统计 - 会议统计相关接口
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核),默认'all'
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {levelStats: [{levelId, levelName, count, totalScore},...], totalStats: {totalConferences, totalScore}, timeInterval: {startTime, endTime, name}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/statistics/conferences-total-score', 
  authMiddleware, 
  conferencesPermission('totalScore'), 
  conferencesController.getConferencesTotalScore
);

/**
 * 获取用户会议详情列表及得分
 * @route POST /v1/sys/conferences/user/details
 * @group 会议管理 - 会议相关接口
 * @param {string} userId.body.required - 用户ID
 * @param {string} range.body - 查询范围，可选值：'in'(统计范围内),'out'(统计范围外),'all'(全部)，默认'all'
 * @param {string} reviewStatus.body - 审核状态，可选值'all'(全部),'rejected'(已拒绝), 'pending'(待审核), 'reviewed'(已审核), 默认'all'
 * @param {number} pageSize.body - 每页记录数，默认10
 * @param {number} pageNum.body - 当前页码，默认1
 * @returns {object} 200 - {code: 200, message: "获取成功", data: {totalCount, pageSize, pageNum, conferences: [{conferenceId, conferenceName, holdTime, levelName, baseScore, allocationRatio, actualScore, reviewStatus}], stats: {totalConferences, leaderConferenceCount, totalScore}, timeInterval: {startTime, endTime, name}, user: {id, name, employeeNumber}}}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/user/details', 
  authMiddleware, 
  conferencesPermission('userDetails'), 
  conferencesController.getUserConferencesDetail
);

/**
 * 重新提交会议审核
 * @route POST /v1/sys/conferences/reapply
 * @group 会议管理 - 会议相关接口
 * @param {string} id.body.required - 会议ID
 * @returns {object} 200 - {code: 200, message: "重新提交审核成功", data: null}
 * @returns {Error} default - Unexpected error
 * @security JWT
 */
router.post('/reapply',
  authMiddleware,
  conferencesPermission('reapply'),
  conferencesController.reapply
);

/**
 * 获取审核状态概览
 * @route POST /v1/sys/conferences/statistics/review-status-overview
 * @group 会议统计 - 会议统计相关接口
 * @param {string} range.body - 查询范围：'in'|'out'|'all'，默认'all'
 * @param {string} userId.body - 用户ID，可选
 * @returns {object} 200 - 审核状态统计数据
 * @security JWT
 */
router.post('/statistics/review-status-overview',
  authMiddleware,
  conferencesPermission('reviewStatusOverview'),
  conferencesController.getReviewStatusOverview
);

module.exports = router;