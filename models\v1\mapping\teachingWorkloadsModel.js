const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');

const teachingWorkloadsModel = sequelize.define(
  'teaching_workloads',
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      comment: '工作量ID'
    },
    teacherId: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: '教师ID（外键，关联user.id）'
    },
    semester: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '授课学期（如：2023-2024-1）'
    },
    courseName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '课程名称'
    },
    studentLevel: {
      type: DataTypes.ENUM('undergraduate', 'graduate'),
      allowNull: false,
      comment: '学生类别（undergraduate-本科生，graduate-研究生）'
    },
    courseType: {
      type: DataTypes.ENUM('theory', 'experiment', 'practice', 'design', 'thesis'),
      allowNull: false,
      comment: '课程类型（theory-理论课，experiment-实验课，practice-实践课，design-课程设计，thesis-毕业论文）'
    },
    courseNature: {
      type: DataTypes.ENUM('required', 'elective'),
      allowNull: false,
      comment: '课程性质（required-必修课，elective-选修课）'
    },
    isNewCourse: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: '是否新开课（1-是，0-否）'
    },
    studentCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '授课人数'
    },
    teachingHours: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      comment: '教师授课时数'
    },
    categoryId: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: '工作量类别ID（外键）'
    },
    department: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '系/教研室'
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注'
    },
    attachmentUrl: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '附件URL'
    },
    reviewComment: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '审核意见（拒绝或同意）'
    },
    reviewerId: {
      type: DataTypes.UUID,
      allowNull: true,
      comment: '审核人ID（外键，关联user.id）'
    },
    ifReviewer: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      comment: '审核状态（默认为空，0拒绝未审核 1，同意审核）'
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: 1,
      comment: '状态（1-有效，0-删除）'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '创建时间'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '更新时间'
    }
  },
  {
    sequelize,
    modelName: 'teaching_workloads',
    tableName: 'teaching_workloads',
    timestamps: true
  }
);

// 在文件加载后添加关联，避免循环依赖问题
const setupAssociations = () => {
  const userModel = require('./userModel');
  const teachingWorkloadLevelsModel = require('./teachingWorkloadLevelsModel');
  const teachingWorkloadParticipantsModel = require('./teachingWorkloadParticipantsModel');

  // 与用户表关联（教师）
  teachingWorkloadsModel.belongsTo(userModel, {
    foreignKey: 'teacherId',
    as: 'teacher'
  });

  // 与级别表关联
  teachingWorkloadsModel.belongsTo(teachingWorkloadLevelsModel, {
    foreignKey: 'categoryId',
    as: 'category'
  });

  // 与审核人的关联关系
  teachingWorkloadsModel.belongsTo(userModel, {
    foreignKey: 'reviewerId',
    as: 'reviewer'
  });

  // 与参与者表的关联关系
  teachingWorkloadsModel.hasMany(teachingWorkloadParticipantsModel, {
    foreignKey: 'workloadId',
    as: 'participants'
  });
};

// 确保关联设置被调用
setTimeout(setupAssociations, 0);

module.exports = teachingWorkloadsModel;
