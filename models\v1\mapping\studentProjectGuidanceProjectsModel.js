const { DataTypes } = require('sequelize');
const sequelize = require('@config/dbConfig');
const studentProjectGuidanceProjectLevelModel = require('./studentProjectGuidanceProjectLevelsModel');
const userModel = require('./userModel');

// 定义指导学生立项主表模型
const StudentProjectGuidanceProject = sequelize.define('student_project_guidance_projects', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
        comment: '项目ID'
    },
    projectNumber: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '项目编号'
    },
    projectName: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '项目名称'
    },
    approvalDepartment: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '下达部门'
    },
    approvalDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: '获批日期'
    },
    approvalFund: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        comment: '批准经费(万元)'
    },
    levelId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        comment: '级别ID'
    },
    startYear: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '执行起始年'
    },
    endYear: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '执行结束年'
    },
    remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注'
    },
    reviewerId: {
        type: DataTypes.CHAR(36),
        allowNull: true,
        comment: '审核人ID'
    },
    ifReviewer: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        comment: '审核状态（0，拒审核 1，审核，null未审核）'
      },
      attachmentUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '附件URL'
      },
      reviewComment: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '审核意见'
      },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: 'student_project_guidance_projects',
    timestamps: true,
    indexes: [
        {
            name: 'idx_project_number',
            fields: ['projectNumber']
        },
        {
            name: 'idx_approval_date',
            fields: ['approvalDate']
        },
        {
            name: 'idx_level',
            fields: ['levelId']
        }
    ]
});

// 建立与项目级别的关联关系
StudentProjectGuidanceProject.belongsTo(studentProjectGuidanceProjectLevelModel, {
    foreignKey: 'levelId',
    as: 'level'
});

// 建立与审核人的关联关系
StudentProjectGuidanceProject.belongsTo(userModel, {
    foreignKey: 'reviewerId',
    as: 'reviewer'
});

module.exports = StudentProjectGuidanceProject; 

// 为避免循环依赖问题，使用延迟加载方式
setTimeout(() => {
    const StudentProjectGuidanceParticipant = require('./studentProjectGuidanceParticipantsModel');
    
    StudentProjectGuidanceProject.hasMany(StudentProjectGuidanceParticipant, {
        foreignKey: 'projectId',
        as: 'participants'
    });
    
    // 也在参与者模型中建立反向关联
    StudentProjectGuidanceParticipant.belongsTo(StudentProjectGuidanceProject, {
        foreignKey: 'projectId',
        as: 'project'
    });
}, 0);
