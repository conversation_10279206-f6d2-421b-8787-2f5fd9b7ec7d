const { FormTemplate, FormField, sequelize, FormData } = require('../../../models');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');

/**
 * 获取表单模板列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getFormTemplates = async (req, res) => {
  try {
    const { name, type, status, page = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (name) where.name = { [Op.like]: `%${name}%` };
    if (type) where.type = type;
    if (status !== undefined) where.status = status;
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await FormTemplate.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['updatedAt', 'DESC']]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取表单模板列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取表单模板列表失败',
      data: null
    });
  }
};

/**
 * 获取表单模板详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getFormTemplateById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const template = await FormTemplate.findByPk(id);
    if (!template) {
      return res.status(404).json({
        code: 404,
        message: '表单模板不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: template
    });
  } catch (error) {
    console.error('获取表单模板详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取表单模板详情失败',
      data: null
    });
  }
};

/**
 * 创建表单模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createFormTemplate = async (req, res) => {
  try {
    const { name, type, description, schema, config, status = 1 } = req.body;
    
    // 校验必填参数
    if (!name || !type || !schema) {
      return res.status(400).json({
        code: 400,
        message: '名称、类型和表单结构不能为空',
        data: null
      });
    }
    
    // 创建表单模板
    const template = await FormTemplate.create({
      id: uuidv4(),
      name,
      type,
      description: description || '',
      schema,
      config: config || {},
      createdBy: req.user.id,
      updatedBy: req.user.id,
      status
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: template
    });
  } catch (error) {
    console.error('创建表单模板失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建表单模板失败',
      data: null
    });
  }
};

/**
 * 更新表单模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateFormTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type, description, schema, config, status } = req.body;
    
    // 校验必填参数
    if (!name || !type || !schema) {
      return res.status(400).json({
        code: 400,
        message: '名称、类型和表单结构不能为空',
        data: null
      });
    }
    
    // 查找表单模板
    const template = await FormTemplate.findByPk(id);
    if (!template) {
      return res.status(404).json({
        code: 404,
        message: '表单模板不存在',
        data: null
      });
    }
    
    // 更新表单模板
    await template.update({
      name,
      type,
      description: description || template.description,
      schema,
      config: config || template.config,
      updatedBy: req.user.id,
      status: status !== undefined ? status : template.status
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: template
    });
  } catch (error) {
    console.error('更新表单模板失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新表单模板失败',
      data: null
    });
  }
};

/**
 * 删除表单模板
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteFormTemplate = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找表单模板
    const template = await FormTemplate.findByPk(id);
    if (!template) {
      return res.status(404).json({
        code: 404,
        message: '表单模板不存在',
        data: null
      });
    }
    
    // 检查是否有关联的表单数据
    const formDataCount = await FormData.count({
      where: {
        templateId: id
      }
    });
    
    if (formDataCount > 0) {
      return res.status(400).json({
        code: 400,
        message: '该表单模板已有关联的表单数据，无法删除',
        data: null
      });
    }
    
    // 删除表单模板
    await template.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除表单模板失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除表单模板失败',
      data: null
    });
  }
};

/**
 * 获取表单数据列表
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getFormDataList = async (req, res) => {
  try {
    const { templateId, title, status, createdBy, page = 1, pageSize = 10 } = req.query;
    
    // 构建查询条件
    const where = {};
    if (templateId) where.templateId = templateId;
    if (title) where.title = { [Op.like]: `%${title}%` };
    if (status !== undefined) where.status = status;
    if (createdBy) where.createdBy = createdBy;
    
    // 分页查询
    const offset = (page - 1) * pageSize;
    const { count, rows } = await FormData.findAndCountAll({
      where,
      offset,
      limit: Number(pageSize),
      order: [['updatedAt', 'DESC']],
      include: [
        {
          model: FormTemplate,
          as: 'template',
          attributes: ['id', 'name', 'type']
        }
      ]
    });
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: {
        list: rows,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total: count,
          totalPages: Math.ceil(count / pageSize)
        }
      }
    });
  } catch (error) {
    console.error('获取表单数据列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取表单数据列表失败',
      data: null
    });
  }
};

/**
 * 获取表单数据详情
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getFormDataById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const formData = await FormData.findByPk(id, {
      include: [
        {
          model: FormTemplate,
          as: 'template',
          attributes: ['id', 'name', 'type', 'schema', 'config']
        }
      ]
    });
    
    if (!formData) {
      return res.status(404).json({
        code: 404,
        message: '表单数据不存在',
        data: null
      });
    }
    
    return res.status(200).json({
      code: 200,
      message: '获取成功',
      data: formData
    });
  } catch (error) {
    console.error('获取表单数据详情失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取表单数据详情失败',
      data: null
    });
  }
};

/**
 * 创建表单数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.createFormData = async (req, res) => {
  try {
    const { templateId, title, data, status = 1 } = req.body;
    
    // 校验必填参数
    if (!templateId || !title || !data) {
      return res.status(400).json({
        code: 400,
        message: '模板ID、标题和表单数据不能为空',
        data: null
      });
    }
    
    // 检查模板是否存在
    const template = await FormTemplate.findByPk(templateId);
    if (!template) {
      return res.status(404).json({
        code: 404,
        message: '表单模板不存在',
        data: null
      });
    }
    
    // 创建表单数据
    const formData = await FormData.create({
      id: uuidv4(),
      templateId,
      title,
      data,
      createdBy: req.user.id,
      updatedBy: req.user.id,
      status
    });
    
    return res.status(201).json({
      code: 200,
      message: '创建成功',
      data: formData
    });
  } catch (error) {
    console.error('创建表单数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '创建表单数据失败',
      data: null
    });
  }
};

/**
 * 更新表单数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.updateFormData = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, data, status } = req.body;
    
    // 校验必填参数
    if (!title || !data) {
      return res.status(400).json({
        code: 400,
        message: '标题和表单数据不能为空',
        data: null
      });
    }
    
    // 查找表单数据
    const formData = await FormData.findByPk(id);
    if (!formData) {
      return res.status(404).json({
        code: 404,
        message: '表单数据不存在',
        data: null
      });
    }
    
    // 更新表单数据
    await formData.update({
      title,
      data,
      updatedBy: req.user.id,
      status: status !== undefined ? status : formData.status
    });
    
    return res.status(200).json({
      code: 200,
      message: '更新成功',
      data: formData
    });
  } catch (error) {
    console.error('更新表单数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '更新表单数据失败',
      data: null
    });
  }
};

/**
 * 删除表单数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.deleteFormData = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找表单数据
    const formData = await FormData.findByPk(id);
    if (!formData) {
      return res.status(404).json({
        code: 404,
        message: '表单数据不存在',
        data: null
      });
    }
    
    // 删除表单数据
    await formData.destroy();
    
    return res.status(200).json({
      code: 200,
      message: '删除成功',
      data: null
    });
  } catch (error) {
    console.error('删除表单数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '删除表单数据失败',
      data: null
    });
  }
};

/**
 * 批量删除表单数据
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.batchDeleteFormData = async (req, res) => {
  try {
    const { ids } = req.body;
    
    // 校验参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '表单数据ID不能为空',
        data: null
      });
    }
    
    // 批量删除表单数据
    const result = await FormData.destroy({
      where: {
        id: {
          [Op.in]: ids
        }
      }
    });
    
    return res.status(200).json({
      code: 200,
      message: '批量删除成功',
      data: {
        count: result
      }
    });
  } catch (error) {
    console.error('批量删除表单数据失败:', error);
    return res.status(500).json({
      code: 500,
      message: '批量删除表单数据失败',
      data: null
    });
  }
};

/**
 * 获取表单列表 (路由映射)
 */
exports.getForms = exports.getFormTemplates;

/**
 * 获取指定表单 (路由映射)
 */
exports.getFormById = exports.getFormTemplateById;

/**
 * 创建表单 (路由映射)
 */
exports.createForm = exports.createFormTemplate;

/**
 * 更新表单 (路由映射)
 */
exports.updateForm = exports.updateFormTemplate;

/**
 * 删除表单 (路由映射)
 */
exports.deleteForm = exports.deleteFormTemplate;

/**
 * 提交表单数据 (路由映射)
 */
exports.submitFormData = exports.createFormData;

/**
 * 获取表单数据 (路由映射)
 */
exports.getFormData = exports.getFormDataList; 